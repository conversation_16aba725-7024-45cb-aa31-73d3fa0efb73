<?php

namespace Database\Seeders;

use App\Models\Providers\MtMachine;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MachineSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        MtMachine::create([
            'name' => 'Machine 1',
            'description' => 'Machine 1',
            'store_id' => 1,
            'status' => true
        ]);

        MtMachine::create([
            'name' => 'Machine 2',
            'description' => 'Machine 2',
            'store_id' => 1,
            'status' => true
        ]);

        MtMachine::create([
            'name' => 'Machine 3',
            'description' => 'Machine 3',
            'store_id' => 1,
            'status' => true
        ]);
    }
}
