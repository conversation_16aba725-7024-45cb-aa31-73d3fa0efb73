<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('angpau_events', function (Blueprint $table) {
            $table->id()->startingValue(1000);
            $table->foreignId('promotion_id')->references('id')->on('promotions');
            $table->string('name');
            $table->dateTime('start_date');
            $table->dateTime('end_date');
            $table->decimal('pool_amount');
            $table->decimal('pool_amount_display');
            $table->decimal('current_pool_amount');
            $table->decimal('min_amount');
            $table->decimal('max_amount');
            $table->integer('min_ticket');
            $table->boolean('is_active');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('angpau_events');
    }
};
