<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_turnover_summaries', function (Blueprint $table) {
            $table->decimal('win_loss', 20, 2)->default(0)->after('turnover');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_turnover_summaries', function (Blueprint $table) {
            $table->dropColumn('win_loss');
        });
    }
};
