<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement('ALTER TABLE game_settings MODIFY game_id VARCHAR(50)');
        DB::statement('ALTER TABLE services MODIFY `name` VARCHAR(255)');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement('ALTER TABLE game_settings MODIFY game_id INT NULL INDEX');
        DB::statement('ALTER TABLE services MODIFY `name` VARCHAR(50)');
    }
};
