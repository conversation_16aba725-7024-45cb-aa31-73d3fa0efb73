<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_card_logs', function (Blueprint $table) {
            $table->foreignId('user_id')->nullable()->index()->after('store_id');
            // $table->foreignId('user_id')->nullable()->after('store_id')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_card_logs', function (Blueprint $table) {
            $table->dropForeign('user_card_logs_id_foreign');
            $table->dropColumn('user_id');
        });
    }
};
