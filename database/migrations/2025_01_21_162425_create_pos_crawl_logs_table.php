<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pos_crawl_logs', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('store_id')->reference('id')->on('store');
            $table->string('type')->nullable();
            $table->dateTime('start_at');
            $table->dateTime('end_at');
            $table->integer('count')->default(0);
            $table->boolean('status')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pos_crawl_logs');
    }
};
