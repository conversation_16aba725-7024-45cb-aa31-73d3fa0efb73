<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ex_transfer', function (Blueprint $table) {
            $table->foreignId('machine_id')->nullable()->after('reference')->references('id')->on('mt_machines');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ex_transfer', function (Blueprint $table) {
            $table->dropForeign('ex_transfer_machine_id_foreign');
            $table->dropColumn('machine_id');
        });
    }
};
