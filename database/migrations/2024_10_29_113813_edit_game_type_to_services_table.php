<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('services', function (Blueprint $table) {
            $table->dropColumn('is_top_game');
            $table->dropColumn('is_hot_game');
            $table->integer('filter_type')->default(0)->after('wallet_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('services', function (Blueprint $table) {
            $table->dropColumn('filter_type');
            $table->boolean('is_top_game')->default(0)->after('wallet_type');
            $table->boolean('is_hot_game')->default(0)->after('is_top_game');
        });
    }
};
