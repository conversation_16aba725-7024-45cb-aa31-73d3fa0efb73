<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_promotions', function (Blueprint $table) {
            $table->foreignId('machine_id')->nullable()->after('status')->references('id')->on('mt_machines');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_promotions', function (Blueprint $table) {
            $table->dropForeign(['machine_id']);
        });
    }
};
