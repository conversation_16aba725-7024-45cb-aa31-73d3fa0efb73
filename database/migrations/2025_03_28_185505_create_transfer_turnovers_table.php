<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('transfer_turnovers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->decimal('amount');
            $table->decimal('target_turnover');
            $table->decimal('achieved_turnover');
            $table->dateTime('achieved_at')->nullable();
            $table->boolean('status')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('transfer_turnovers');
    }
};
