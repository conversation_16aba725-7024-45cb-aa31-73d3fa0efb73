<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::unprepared("
            USE `fw`;
            INSERT INTO `logs_migrations` (`migration`) VALUES ('2024_09_10_102800_pos_report');

            SET @maxPrio = (SELECT MAX(`priority`) as priority FROM `permissions` WHERE `level` = 1);

            INSERT INTO `permissions` VALUES (NULL, 0, 'perm-pos-report', @maxPrio + 1, 1, '[]', 0, 0, NOW(), NULL, NULL);

            SET @parent = (SELECT `permissions`.`id` FROM `permissions` WHERE `permissions`.`name` = 'perm-pos-report');


            INSERT INTO `permissions` VALUES (NULL, @parent, 'perm-pos-report-record', 1, 2, '[\"report/pos-report-record\"]', 0, 0, now(), null, null);
            INSERT INTO `permissions` VALUES (NULL, @parent, 'perm-pos-report-reload', 2, 2, '[\"report/pos-report-reload\"]', 0, 0, now(), null, null);
            INSERT INTO `permissions` VALUES (NULL, @parent, 'perm-pos-report-withdraw', 2, 2, '[\"report/pos-report-withdraw\"]', 0, 0, now(), null, null);

            INSERT INTO `lang` (`slug`, `type`, `en`, `created_at`) VALUES ('perm-pos-report', 'Permissions', 'Pos Report', now());
            INSERT INTO `lang` (`slug`, `type`, `en`, `created_at`) VALUES ('perm-pos-report-record', 'Permissions', 'Pos Card Record', now());
            INSERT INTO `lang` (`slug`, `type`, `en`, `created_at`) VALUES ('perm-pos-report-reload', 'Permissions', 'Pos Card Reload', now());
            INSERT INTO `lang` (`slug`, `type`, `en`, `created_at`) VALUES ('perm-pos-report-withdraw', 'Permissions', 'Pos Card Withdrawal', now());
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::unprepared("
            USE `fw`;
            DELETE FROM `logs_migrations` WHERE migration = '2024_09_10_102800_pos_report';
        ");
    }
};
