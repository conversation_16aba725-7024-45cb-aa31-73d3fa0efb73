<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('store', function (Blueprint $table) {
            $table->boolean('is_allow_deposit')->default(1)->after('is_wallet_transferable');
            $table->boolean('is_allow_withdraw')->default(1)->after('is_wallet_transferable');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('store', function (Blueprint $table) {
            $table->dropColumn('is_allow_deposit');
            $table->dropColumn('is_allow_withdraw');
        });
    }
};
