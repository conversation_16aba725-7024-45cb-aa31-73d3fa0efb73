<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::unprepared("
            INSERT INTO `fw`.`logs_migrations` (`migration`) VALUES ('2024_09_06_174400_migrate');

            UPDATE `fw`.`permissions` SET `disabled` = 1, `master_disabled` = 1 WHERE `name` = 'perm-user-rank-detail';
            UPDATE `fw`.`permissions` SET `disabled` = 1, `master_disabled` = 1 WHERE `name` = 'perm-user-rank-list';

            DROP TABLE IF EXISTS `fw`.`bonus`;
            DROP TABLE IF EXISTS `fw`.`bonus_agent`;
            DROP TABLE IF EXISTS `fw`.`bonus_calculation_batch`;
            DROP TABLE IF EXISTS `fw`.`bonus_community`;
            DROP TABLE IF EXISTS `fw`.`bonus_goldmine`;
            DROP TABLE IF EXISTS `fw`.`bonus_log`;
            DROP TABLE IF EXISTS `fw`.`bonus_in`;
            DROP TABLE IF EXISTS `fw`.`bonus_payment_method`;
            DROP TABLE IF EXISTS `fw`.`bonus_report`;
            DROP TABLE IF EXISTS `fw`.`bonus_setting`;
            DROP TABLE IF EXISTS `fw`.`bonus_special`;
            DROP TABLE IF EXISTS `fw`.`bonus_vip`;
            DROP TABLE IF EXISTS `fw`.`user_rank`;
            DROP TABLE IF EXISTS `fw`.`user_referral_link`;
            DROP TABLE IF EXISTS `fw`.`user_game_logs`;

        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::unprepared("
            DELETE FROM `fw`.`logs_migrations` WHERE migration = '2024_09_06_174400_migrate';
        ");
    }
};
