<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_promotions', function (Blueprint $table) {
            $table->longText('meta_transaction')->nullable()->after('game_return_amount');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_promotions', function (Blueprint $table) {
            $table->dropColumn('meta_transaction');
        });
    }
};
