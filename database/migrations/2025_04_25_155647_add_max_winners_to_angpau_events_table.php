<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('angpau_events', function (Blueprint $table) {
            $table->integer('max_winners')->default(10)->after('min_ticket')->comment('Maximum number of winners that can be selected for this event');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('angpau_events', function (Blueprint $table) {
            $table->dropColumn('max_winners');
        });
    }
};
