<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_turnover_summaries', function (Blueprint $table) {
            $table->integer('bet_count')->default(0)->after('product_id');
            $table->decimal('return', 20, 2)->default(0)->after('turnover');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_turnover_summaries', function (Blueprint $table) {
            $table->dropColumn('bet_count');
            $table->dropColumn('return');
        });
    }
};
