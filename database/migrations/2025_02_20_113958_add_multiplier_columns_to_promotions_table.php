<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('promotions', function (Blueprint $table) {
            $table->decimal('bonus_multiplier')->after('is_bonus_flexible')->nullable();
            $table->boolean('is_bonus_multiplier')->after('bonus_amount')->nullable();
            $table->boolean('is_turnover_multiplier')->after('turnover_amount')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('promotions', function (Blueprint $table) {
            $table->dropColumn('bonus_multiplier');
            $table->dropColumn('is_bonus_multiplier');
            $table->dropColumn('is_turnover_multiplier');
        });
    }
};
