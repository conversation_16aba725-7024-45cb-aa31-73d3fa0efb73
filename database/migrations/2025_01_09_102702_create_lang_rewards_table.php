<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('lang_rewards', function (Blueprint $table) {
            $table->id();
            $table->string('slug')->unique();
            $table->string('type')->nullable();
            $table->text('en')->nullable();
            $table->text('vn')->nullable();
            $table->text('th')->nullable();
            $table->text('indo')->nullable();
            $table->text('cn')->nullable();
            $table->text('my')->nullable();
            $table->text('ben')->nullable();
            $table->text('npl')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('lang_rewards');
    }
};
