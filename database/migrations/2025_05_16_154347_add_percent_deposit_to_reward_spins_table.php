<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('reward_spins', function (Blueprint $table) {
            $table->decimal('percent_deposit', 5, 2)->default(0)->after('percent');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('reward_spins', function (Blueprint $table) {
            $table->dropColumn('percent_deposit');
        });
    }
};
