<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('reward_spins', function (Blueprint $table) {
            $table->id();
            $table->integer('type');
            $table->string('title')->nullable();
            $table->string('image')->nullable();
            $table->decimal('percent', 5, 2)->default(0);
            $table->integer('reward_type');
            $table->integer('min_points')->default(0);
            $table->integer('max_points')->default(0);
            $table->string('value')->nullable();
            $table->boolean('status')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('reward_spins');
    }
};
