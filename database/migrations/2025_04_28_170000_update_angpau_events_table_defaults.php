<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Make promotion_id nullable
        DB::statement('ALTER TABLE angpau_events MODIFY promotion_id BIGINT UNSIGNED NULL');
        
        // Add default values for pool_amount, pool_amount_display, and current_pool_amount
        DB::statement('ALTER TABLE angpau_events MODIFY pool_amount DECIMAL(8,2) NOT NULL DEFAULT 0');
        DB::statement('ALTER TABLE angpau_events MODIFY pool_amount_display DECIMAL(8,2) NOT NULL DEFAULT 0');
        DB::statement('ALTER TABLE angpau_events MODIFY current_pool_amount DECIMAL(8,2) NOT NULL DEFAULT 0');
        
        // Drop the foreign key constraint
        DB::statement('ALTER TABLE angpau_events DROP FOREIGN KEY angpau_events_promotion_id_foreign');
        
        // Add the foreign key constraint back with ON DELETE SET NULL
        DB::statement('ALTER TABLE angpau_events ADD CONSTRAINT angpau_events_promotion_id_foreign FOREIGN KEY (promotion_id) REFERENCES promotions(id) ON DELETE SET NULL');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Drop the foreign key constraint
        DB::statement('ALTER TABLE angpau_events DROP FOREIGN KEY angpau_events_promotion_id_foreign');
        
        // Make promotion_id required again
        DB::statement('ALTER TABLE angpau_events MODIFY promotion_id BIGINT UNSIGNED NOT NULL');
        
        // Remove default values
        DB::statement('ALTER TABLE angpau_events MODIFY pool_amount DECIMAL(8,2) NOT NULL');
        DB::statement('ALTER TABLE angpau_events MODIFY pool_amount_display DECIMAL(8,2) NOT NULL');
        DB::statement('ALTER TABLE angpau_events MODIFY current_pool_amount DECIMAL(8,2) NOT NULL');
        
        // Add the foreign key constraint back
        DB::statement('ALTER TABLE angpau_events ADD CONSTRAINT angpau_events_promotion_id_foreign FOREIGN KEY (promotion_id) REFERENCES promotions(id)');
    }
};
