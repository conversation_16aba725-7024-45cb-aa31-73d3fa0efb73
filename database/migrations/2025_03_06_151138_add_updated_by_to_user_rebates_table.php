<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_rebates', function (Blueprint $table) {
            $table->string('serial_number')->index()->nullable()->after('id');
            $table->text('remark')->nullable()->after('status');
            $table->integer('updated_by')->nullable();
            $table->dateTime('approved_at')->nullable();
            $table->integer('approved_by')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_rebates', function (Blueprint $table) {
            $table->dropColumn('serial_number');
            $table->dropIndex(['serial_number']);
            $table->dropColumn('remark');
            $table->dropColumn('updated_by');
            $table->dropColumn('approved_at');
            $table->dropColumn('approved_by');
        });
    }
};
