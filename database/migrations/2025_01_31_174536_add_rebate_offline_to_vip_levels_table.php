<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('vip_levels', function (Blueprint $table) {
            $table->decimal('rebate_offline', 5, 4)->after('rebate');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('vip_levels', function (Blueprint $table) {
            $table->dropColumn('rebate_offline');
        });
    }
};
