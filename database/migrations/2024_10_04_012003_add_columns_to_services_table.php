<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('services', function (Blueprint $table) {
            $table->boolean('is_top_game')->default(0)->after('wallet_type');
            $table->boolean('is_hot_game')->default(0)->after('is_top_game');
            $table->foreignId('game_provider_id')->nullable()->after('is_hot_game');
            $table->foreignId('game_category_id')->nullable()->after('game_provider_id');
            $table->foreignId('game_setting_id')->nullable()->after('game_category_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('services', function (Blueprint $table) {
            $table->dropForeign(['game_provider_id', 'game_category_id', 'game_setting_id']);
            $table->dropColumn(['is_top_game', 'is_hot_game', 'game_provider_id', 'game_category_id', 'game_setting_id']);
        });
    }
};
