<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('currency_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable();
            $table->foreignId('user_card_id')->nullable();
            $table->foreignId('store_id');
            $table->string('member_card_id')->index();
            $table->string('member_card_no')->index();
            $table->string('member_phone')->index();
            $table->string('terminal_serial')->index()->nullable();
            $table->string('machine_name')->index()->nullable();
            $table->string('machine_serial')->index()->nullable();
            $table->string('operation_type')->index()->nullable();
            $table->integer('operation_type_id')->index()->nullable();
            $table->decimal('operation_qty', 20, 2)->default(0.00);
            $table->decimal('member_balance', 20, 2)->default(0.00);
            $table->decimal('latest_member_balance', 20, 2)->default(0.00);
            $table->string('date_time')->nullable();
            $table->timestamp('transaction_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('currency_details');
    }
};
