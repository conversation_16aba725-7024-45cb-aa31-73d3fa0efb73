<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('reward_spin_logs', function (Blueprint $table) {
            $table->boolean('is_dummy')->default(false)->after('value');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('reward_spin_logs', function (Blueprint $table) {
            $table->dropColumn('is_dummy');
        });
    }
};
