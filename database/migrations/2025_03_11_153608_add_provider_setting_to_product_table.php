<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('product', function (Blueprint $table) {
            $table->text('aggregator')->nullable()->after('status');
            $table->text('operator_code')->nullable()->after('status');
            $table->text('provider_code')->nullable()->after('status');
            $table->text('key')->nullable()->after('status');
            $table->text('api_url')->nullable()->after('status');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('product', function (Blueprint $table) {
            $table->dropColumn('api_url');
            $table->dropColumn('provider_code');
            $table->dropColumn('operator_code');
            $table->dropColumn('key');
            $table->dropColumn('aggregator');
        });
        
    }
};
