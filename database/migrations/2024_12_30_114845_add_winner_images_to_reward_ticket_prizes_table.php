<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('reward_ticket_prizes', function (Blueprint $table) {
            $table->text('winner_image')->nullable()->after('drawing_time');
            $table->text('winner_list')->nullable()->after('winner_image');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('reward_ticket_prizes', function (Blueprint $table) {
            $table->dropColumn(['winner_image', 'winner_list']);
        });
    }
};
