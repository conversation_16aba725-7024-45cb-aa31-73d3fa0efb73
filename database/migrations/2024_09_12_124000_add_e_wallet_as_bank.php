<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::unprepared("
            USE `fw`;
            INSERT INTO `logs_migrations` (`migration`) VALUES ('2024_09_12_124000_add_e_wallet_as_bank');

            SET @countryID = (SELECT id FROM `country` WHERE `name` = 'malaysia');
            SET @maxPrio = (SELECT MAX(`priority`) as priority FROM `bank`);

            INSERT INTO `fw`.`bank` (`country_id`,`name`,`translation_code`,`status`,`transfer_status`,`priority`,`created_at`,`updater_id`,`updated_at`,`deleted_at`) VALUES (@countryID,'e-wallet','e-wallet',1,1,@maxPrio+1,'2024-09-12 12:45:00',0,'2024-09-12 12:45:00',NULL);

        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::unprepared("
            USE `fw`;
            DELETE FROM `logs_migrations` WHERE migration = '2024_09_12_124000_add_e_wallet_as_bank';
        ");
    }
};
