<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_angpaus', function (Blueprint $table) {
            $table->id();
            $table->foreignId('angpau_event_id')->references('id')->on('angpau_events');
            $table->foreignId('user_id')->nullable()->references('id')->on('users');
            $table->string('name');
            $table->decimal('amount');
            $table->boolean('is_dummy')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_angpaus');
    }
};
