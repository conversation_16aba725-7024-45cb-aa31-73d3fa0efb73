<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_angpaus', function (Blueprint $table) {
            $table->integer('ticket')->default(0)->after('name');
            $table->boolean('is_selected')->default(false)->after('is_dummy');
            $table->boolean('status')->default(false)->after('is_selected');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_angpaus', function (Blueprint $table) {
            $table->dropColumn('ticket');
            $table->dropColumn('is_selected');
            $table->dropColumn('status');
        });
    }
};
