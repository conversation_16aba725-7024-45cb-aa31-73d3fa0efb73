<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('currency_details', function (Blueprint $table) {
            $table->foreignId('pnl_ref_id')->nullable();
            $table->decimal('pnl_amount', 20, 2)->nullable();
            $table->foreignId('user_level_transaction_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('currency_details', function (Blueprint $table) {
            $table->dropForeign(['pnl_ref_id']);
            $table->dropColumn('pnl_ref_id');
            $table->dropColumn('pnl_amount');
            $table->dropForeign(['user_level_transaction_id']);
            $table->dropColumn('user_level_transaction_id');
        });
    }
};
