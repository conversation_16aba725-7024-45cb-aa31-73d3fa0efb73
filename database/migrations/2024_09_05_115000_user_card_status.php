<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::unprepared("
            INSERT INTO `fw`.`logs_migrations` (`migration`) VALUES ('2024_09_05_115000_user_card_status');

            ALTER TABLE `fw`.`user_card` ADD COLUMN `status` tinyint(1) DEFAULT '0' AFTER `branch_code`;
            ALTER TABLE `fw`.`user_card` MODIFY COLUMN `card_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;
            UPDATE `fw`.`user_card` SET `status` = 1;
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::unprepared("
            DELETE FROM `fw`.`logs_migrations` WHERE migration = '2024_09_05_115000_user_card_status';
        ");
    }
};
