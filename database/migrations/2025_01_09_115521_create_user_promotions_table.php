<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_promotions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable();
            $table->foreignId('promotion_id')->nullable();
            $table->decimal('target_turnover', 20, 4)->default(0);
            $table->decimal('achieved_turnover', 20, 4)->default(0);
            $table->decimal('bonus_amount', 20, 4)->default(0);
            $table->decimal('max_withdraw_amount', 20, 4)->nullable();
            $table->dateTime('achieved_at')->nullable();
            $table->tinyInteger('status');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_promotions');
    }
};
