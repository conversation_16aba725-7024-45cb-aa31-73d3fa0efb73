<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wwj_account_bet_transaction', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable();
            $table->string('account')->index()->nullable();
            $table->decimal('total_turn_over', 20, 4)->default(0);
            $table->decimal('total_bet', 20, 4)->default(0);
            $table->decimal('total_return', 20, 4)->default(0);
            $table->decimal('total_win_loss', 20, 4)->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wwj_account_bet_transaction');
    }
};
