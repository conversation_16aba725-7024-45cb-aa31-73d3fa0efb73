<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_card_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('store_id')->nullable()->index();
            $table->string('member_card_id')->index();
            $table->string('member_phone')->index();
            $table->string('date_time')->nullable();
            $table->timestamp('transaction_at')->nullable();
            $table->integer('operation_type')->nullable();
            $table->decimal('member_balance', 20, 2)->default(0.00);
            $table->string('member_card_no')->index();
            $table->decimal('operation_qty', 20, 2)->default(0.00);
            $table->decimal('latest_member_balance', 20, 2)->default(0.00);
            $table->string('terminal_address')->nullable();
            $table->string('terminal_serial')->nullable();
            $table->integer('value_type')->nullable();
            $table->string('remark')->nullable();
            $table->integer('status')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_card_logs');
    }
};
