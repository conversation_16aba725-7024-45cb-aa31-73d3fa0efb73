<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_ticket_rewards', function (Blueprint $table) {
            $table->boolean('is_read')->default(false)->after('is_dummy');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_ticket_rewards', function (Blueprint $table) {
            $table->dropColumn('is_read');
        });
    }
};
