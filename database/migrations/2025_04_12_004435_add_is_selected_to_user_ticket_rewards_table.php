<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_ticket_rewards', function (Blueprint $table) {
            $table->boolean('is_joined')->default(false)->after('is_dummy');
            $table->boolean('is_selected')->default(false)->after('is_joined');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_ticket_rewards', function (Blueprint $table) {
            $table->dropColumn('is_joined');
            $table->dropColumn('is_selected');
        });
    }
};
