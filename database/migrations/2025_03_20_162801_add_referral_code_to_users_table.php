<?php

use App\Models\User;
use App\Traits\GenerateNumberTrait;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('referral_code')->nullable()->after('member_id');
        });

        User::all()->each(function ($e) {
            $referralCode = GenerateNumberTrait::generateReferralCode(User::query(), 'referral_code');
            $e->update(['referral_code' => $referralCode]);
        });

        Schema::table('users', function (Blueprint $table) {
            $table->unique('referral_code');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('referral_code');
        });
    }
};
