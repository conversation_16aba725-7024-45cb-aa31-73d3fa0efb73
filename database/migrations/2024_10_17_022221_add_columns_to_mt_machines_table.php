<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('mt_machines', function (Blueprint $table) {
            $table->decimal('transfer_in', 20, 8)->default(0.00000000)->after('service_id');
            $table->decimal('transfer_out', 20, 8)->default(0.00000000)->after('transfer_in');
            $table->decimal('profit_loss', 20, 8)->default(0.00000000)->after('transfer_out');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('mt_machines', function (Blueprint $table) {
            $table->dropColumn('transfer_in');
            $table->dropColumn('transfer_out');
            $table->dropColumn('profit_loss');
        });
    }
};
