<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::unprepared("
            USE `fw`;
            INSERT INTO `logs_migrations` (`migration`) VALUES ('2024_09_09_145930_setup_i8_services');

            DROP TABLE IF EXISTS `product`;
            DROP TABLE IF EXISTS `product_setting`;
            DROP TABLE IF EXISTS `user_product`;
            DROP TABLE IF EXISTS `services`;

            CREATE TABLE `product` (
                `id` bigint NOT NULL AUTO_INCREMENT,
                `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `priority` int NOT NULL,
                `image_url` varchar(255) NULL DEFAULT NULL,
                `status` tinyint NOT NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                `deleted_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`),
                KEY `productName` (`name`),
                KEY `code` (`code`),
                KEY `status` (`status`),
                KEY `createdAt` (`created_at`)
            ) ENGINE=InnoDB AUTO_INCREMENT=2001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            
            CREATE TABLE `product_setting` (
                `id` bigint NOT NULL AUTO_INCREMENT,
                `product_id` bigint NOT NULL,
                `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                `reference` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
                `deleted_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`),
                KEY `idx_ps_id` (`product_id`,`name`),
                KEY `name` (`name`),
                KEY `type` (`type`),
                KEY `reference` (`reference`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

            CREATE TABLE `user_product` (
              `id` bigint NOT NULL AUTO_INCREMENT,
              `user_id` bigint NOT NULL,
              `product_id` bigint NOT NULL,
              `wallet_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
              `member_id` varchar(50) DEFAULT NULL,
              `balance` decimal(20,8) NOT NULL DEFAULT '0.00000000',
              `transfer_in` decimal(20,8) NOT NULL DEFAULT '0.00000000',
              `transfer_out` decimal(20,8) NOT NULL DEFAULT '0.00000000',
              `profit_loss` decimal(20,8) NOT NULL DEFAULT '0.00000000',
              `status` tinyint NOT NULL,
              `created_at` timestamp NULL DEFAULT NULL,
              `updated_at` timestamp NULL DEFAULT NULL,
              `deleted_at` timestamp NULL DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `user_id_idx` (`user_id`),
              KEY `member_id_idx` (`member_id`),
              KEY `product_id_idx` (`product_id`),
              KEY `status_idx` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

            CREATE TABLE `services` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT,
            `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
            `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `product_id` bigint NULL,
            `wallet_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
            `priority` int DEFAULT NULL,
            `status` tinyint DEFAULT 0,
            `created_at` timestamp NOT NULL,
            `updater_id` bigint NOT NULL DEFAULT '0',
            `updated_at` timestamp NULL DEFAULT NULL,
            `deleted_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

            INSERT INTO `product` VALUES (NULL, 'TK8', 'TK8', 'oc', '1', 'TK8.png', 1, NOW(), NOW(), NULL);
            SET @productID = (SELECT `id` FROM `product` WHERE `name` = 'TK8');
            INSERT INTO `product_setting` VALUES (NULL, @productID, 'display_top_up', 0, NULL, NULL, NULL, NULL);
            INSERT INTO `product_setting` VALUES (NULL, @productID, 'hasWalletList', 1, 'Main', NULL, NULL, NULL);

            INSERT INTO `system_setting` (`name`, `value`) VALUES ('ocTraitEnvironment', 'production');

            ALTER TABLE `ex_transfer` ADD COLUMN `product_id` bigint NULL DEFAULT NULL AFTER `credit_id`;
            ALTER TABLE `ex_transfer` MODIFY COLUMN `card_id` bigint NULL DEFAULT NULL;

            UPDATE `permissions` SET `disabled` = 0 WHERE `name` = 'perm-ex-transfer-myr-credit';
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::unprepared("
            USE `fw`;
            DELETE FROM `logs_migrations` WHERE migration = '2024_09_09_145930_setup_i8_services';

            DELETE FROM `system_setting` WHERE `name` = 'ocTraitEnvironment';
            ALTER TABLE `ex_transfer` DROP COLUMN `product_id`;

            UPDATE `permissions` SET `disabled` = 1 WHERE `name` = 'perm-ex-transfer-myr-credit';
        ");
    }
};
