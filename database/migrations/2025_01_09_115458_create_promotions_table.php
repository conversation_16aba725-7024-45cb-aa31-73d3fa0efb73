<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('promotions', function (Blueprint $table) {
            $table->id()->startingValue(1000);
            $table->string('name');
            $table->text('description')->nullable();
            $table->text('terms')->nullable();
            $table->foreignId('product_id')->nullable();
            $table->foreignId('service_id')->nullable();
            $table->decimal('max_withdraw_amount')->nullable();
            $table->boolean('is_bonus_flexible')->default(false);
            $table->decimal('bonus_amount')->default(0);
            $table->integer('turnover_multipler');
            $table->decimal('turnover_amount')->default(0);
            $table->boolean('status')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('promotions');
    }
};
