version: '3.9'

services:
  redis:
    image: redis:6-alpine
    expose:
      - "6379"
    volumes:
      - ./redis/data:/data

  php:
    container_name: fw_api
    image: 'webdevops/php-nginx:8.2-alpine'
    environment:
     WEB_DOCUMENT_ROOT: "/app/fw_backend/public"
    ports:
      - "8081:80"
    volumes:
      - ./repo/fw_backend:/app/fw_backend
      - ./.env:/app/fw_backend/.env
    working_dir: /app/fw_backend
    logging:
      driver: "json-file"
      options:
        max-size: "30m"
        max-file: "2"

  composer:
    container_name: fw-composer
    image: 'webdevops/php-nginx:8.2-alpine'
    volumes:
      - ./repo/fw_backend:/app/fw_backend
      - ./.env:/app/fw_backend/.env
    working_dir: /app/fw_backend
    command: sh -c "composer update && composer install"