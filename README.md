# fw

Set Up Guide
------------
<PERSON>vel: 9.0
PHP Version: 8.0

1. Composer install to install required dependencies
```console
composer install
```

2. Create an .env file. Please request from person in charge, run 'php artisan key:generate' if your .env APP_KEY is empty.

3. Run 'php artisan jwt:secret' if your .env JWT_SECRET is empty.

4. Add new entry into your httpd-vhosts.conf
```console
<VirtualHost *:80>
        ServerName local-api.fw.com
        ServerAlias local-api.fw.com

        DocumentRoot /.../fw_backend/public # Change to your project file path
        SetEnv APPLICATION_ENV "local"

        <Directory "/.../fw_backend/public"> # Change to your project file path
            DirectoryIndex index.php
            AllowOverride All
            Order allow,deny
            Allow from all
            Require all granted
        </Directory>

        <FilesMatch \.php$> # Mac<PERSON> can ignore this, just make sure your PHP version is 8.0
            # For Apache version 2.4.10 and above, use SetHandler to run PHP as a fastCGI process server
            Set<PERSON>andler "proxy:unix:/run/php/php8.0-fpm.sock|fcgi://localhost"
            #  SetHandler "proxy:unix:/tmp/hasantest/php8.0-fpm.sock|fcgi://localhost"
        </FilesMatch>

        ErrorLog /.../fw-backend.log # Change to your project file path
        CustomLog /.../fw-backend.log combined # Change to your project file path
</VirtualHost>
```

5. Add new entry into your hosts file
```console
127.0.0.1       local-api.fw.com
```

6. Restart apache to verify virtual host set up correctly.

7. Prepare your localhost database schema according to project_folder/database/init.sql, not 'php artisan migrate'.

8. Run 'php artisan setup:cleanProject' after database is imported.