name: Deploy Staging

on:
  workflow_dispatch:
  push:
    branches: ["stage"]

env:
  API_APP_TRIGGER_URL: ${{ secrets.API_APP_TRIGGER_URL }}
  API_ADMIN_TRIGGER_URL: ${{ secrets.API_ADMIN_TRIGGER_URL }}

jobs:
  deploy:
    name: Call deployment trigger url
    runs-on: ubuntu-latest
    steps:
      - name: Deploy
        run: |
          curl "$API_APP_TRIGGER_URL"
          curl "$API_ADMIN_TRIGGER_URL"
