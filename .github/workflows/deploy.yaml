name: Deploy Production

on:
  workflow_dispatch:

env:
  OVPN_FILE: ${{ secrets.OVPN_FILE }}
  OVPN_PASS: ${{ secrets.OVPN_PASSWORD }}
  PRIVATE_KEY: ${{ secrets.EC2_PEM }}
  USERNAME: ${{ secrets.EC2_USERNAME }}

permissions:
  contents: read
  statuses: write

jobs:
  deploy:
    name: Deploy with ansible playbook
    runs-on: ubuntu-latest
    steps:
      - name: Install OpenVPN and Ansible
        uses: awalsh128/cache-apt-pkgs-action@latest
        with:
          packages: openvpn openvpn-systemd-resolved ansible

      - name: Checkout repository
        uses: actions/checkout@v4

      # - name: Set commit status as pending
      #   uses: myrotvorets/set-commit-status-action@master
      #   with:
      #     token: ${{ secrets.GITHUB_TOKEN }}
      #     status: pending
      #     context: Deploy Production
      #     sha: ${{ github.sha }}

      - name: Set up VPN and SSH
        run: |
          echo "$OVPN_FILE" > fw.ovpn
          echo "$OVPN_PASS" > pass.txt
          echo "$PRIVATE_KEY" > private_key.pem
          chmod 400 pass.txt
          chmod 400 private_key.pem

      - name: Connect to VPN
        run: |
          sudo openvpn --config fw.ovpn --askpass pass.txt --daemon

      - name: Run Ansible Playbook
        env:
          ANSIBLE_CONFIG: ansible/ansible.cfg
          ANSIBLE_HOST_KEY_CHECKING: false
        run: |
          ansible-playbook ansible/deploy.yaml --private-key private_key.pem -u $USERNAME

      - name: Kill VPN
        if: always()
        run: |
          sudo killall openvpn || true

      # - name: Set final commit status
      #   uses: myrotvorets/set-commit-status-action@master
      #   if: always()
      #   with:
      #     token: ${{ secrets.GITHUB_TOKEN }}
      #     status: ${{ job.status }}
      #     context: Deploy Production
      #     sha: ${{ github.sha }}
