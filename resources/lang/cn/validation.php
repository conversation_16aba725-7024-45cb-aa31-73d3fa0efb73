<?php

use Illuminate\Support\Facades\DB;

$language = 'cn';

$res = DB::table('lang_validation')
        ->get()
        ->toArray();

$lang = [];
foreach ((array) $res as $item) {
    $item = (array) $item;
    $lang[$item['slug']] = $item[$language];
}

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    $lang['custom'] = [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
    ];

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email". This simply helps us make our message more expressive.
    |
    */

    $lang['attributes'] = [
        'email' => '电邮',
    ];

return $lang;
