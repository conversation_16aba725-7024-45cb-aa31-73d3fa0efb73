<?php

use Illuminate\Support\Facades\DB;
use App\Models\LangCustom;
use App\Traits\CacheTrait;

$language = 'cn';
$cacheName = 'lang-custom-'.$language;

$lang = CacheTrait::getCache($cacheName);

if(!empty($lang)){
    return (array)$lang;
}

$res = DB::table('lang_custom')
    ->get()
    ->toArray();

$lang = [];

foreach ((array) $res as $item) {
    $item = (array) $item;
    $lang[array_search($item['type'],LangCustom::$type).'-'.$item['slug']] = $item[$language];
}

CacheTrait::setCache($cacheName, (object)$lang, null);

return $lang;
