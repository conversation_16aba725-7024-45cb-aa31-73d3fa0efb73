<?php

use Illuminate\Support\Facades\DB;
use App\Traits\CacheTrait;

$language = 'indo';
$cacheName = 'lang-'.$language;
$lang = CacheTrait::getCache($cacheName);
if(!empty($lang)){
    return (array)$lang;
}

$res = DB::table('lang')
        ->get()
        ->toArray();

$lang = [];
foreach ((array) $res as $item) {
    $item = (array) $item;
    $lang[$item['slug']] = $item[$language];
}

CacheTrait::setCache($cacheName, (object)$lang, null);

return $lang;
