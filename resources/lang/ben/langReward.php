<?php

use App\Traits\CacheTrait;
use Illuminate\Support\Facades\DB;

$language = 'ben';
$cacheName = 'lang-rewards-'.$language;
$lang = CacheTrait::getCache($cacheName);

if (!empty($lang)) {
    return (array)$lang;
}

$res = DB::table('lang_rewards')
        ->get()
        ->toArray();

$lang = [];
foreach ((array) $res as $item) {
    $item = (array) $item;
    $lang[$item['slug']] = $item[$language];
}

CacheTrait::setCache($cacheName, (object)$lang, null);

return $lang;
