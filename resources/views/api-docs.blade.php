<!DOCTYPE html>
<html lang="en">
<head>

    <!-- Required meta tags -->
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Bootstrap CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.28.0/prism.min.js" integrity="sha512-RDQSW3KoqJMiX0L/UBgwBmH1EmRYp8LBOiLaA8rBHIy+7OGP/7Gxg8vbt8wG4ZYd29P0Fnoq6+LOytCqx3cyoQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <title>{{ config('app.name') }}</title>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <style>
        html {
          scroll-behavior: smooth;
        }

        pre {

        }
        .json-key {
                color: brown;
        }
        .json-value {
            color: navy;
        }
        .json-string {
            color: olive;
        }
        
        .leftbox {
            width: 288px;
        }
        .rightbox {
            width: calc(100% - 288px);
            margin-left: 288px;
            overflow: hidden;
        }
    </style>
</head>
<body style="font-family:nunito" class="bg-gray-100">
    <div class="w-full pb-6 px-6 flex bg-gray-100">
        <div class="leftbox fixed h-full overflow-y-auto bg-gray-100 left-0 pt-6 pb-4 px-4" x-data="list">
            <div class="px-4 py-2 hover:bg-blue-100 text-gray-500 hover:text-blue-800 transform duration-300 rounded-md mb-1" @click='setActive(0)' :class="active == 0 ? 'bg-blue-100 text-blue-800 font-bold' : ''">
                <a href="#api-info" @click='setActive(0)'>
                    API Summary
                </a>
            </div>
            @php
                $count = 0;
            @endphp
            @foreach($data['items'] as $key => $items)
                <div class="px-4 py-2 text-blue-400 rounded-md mb-1">{{ str_replace('Controller', '', $key) }}</div>
                @foreach($items as $key => $item)
                <div class="hover:bg-blue-100 text-gray-500 hover:text-blue-800 transform duration-300 rounded-md mb-1" @click='setActive({{ $count + 1 }})' :class="active == {{ $count + 1 }} ? 'bg-blue-100 text-blue-800 font-bold' : ''">
                    <a href="#{{ $item['method'] . str_replace('/', '_', $item['path']) }}" ><div class="w-full h-full px-4 py-2">{{ $item['name'] }}</div></a>
                </div>
                @php 
                        $count++;
                    @endphp
                @endforeach
            @endforeach
        </div>
        <div class="rightbox">
            <div class="">
                <div id="api-info" class="w-full h-6"></div>
                <div class="font-bold text-2xl">{{ $data['api_name'] }}</div>
                <div class="text-xs">Version: V{{ $data['api_version'] }}</div>
                <div class="text-sm">API Domain (Staging): <i>{{ $data['api_endpoint']['staging'] }}</i></div>
                <div class="text-sm">API Domain (Live): <i>{{ $data['api_endpoint']['live'] }}</i></div>
                <br>
                <div>Postman Collection : [<a class="text-blue-500 underline" href={{ $data['postman_download'] }}>Download</a>]</div>
                <div class="w-full h-px bg-gray-300 mt-4 mb-4"></div>

                @foreach($data['items'] as $key => $items)
                    @foreach($items as $key => $item)
                    <div id="{{ $item['method'] . str_replace('/', '_', $item['path']) }}" class="w-full h-5"></div>
                    <div class="text-xl text-gray-600 font-bold">{{ $item['name'] }}</div>
                    <div class="text-sm text-gray-500">{{ $item['description'] }}</div>
                    <div class="w-full mt-2 shadow-md mb-8">
                        <div class="card p-2 text-dark bg-white">
                            <div class="card-header text-primary flex">
                                @if( $item['method'] == 'POST')
                                    <div class="bg-yellow-300 py-1 px-2 rounded font-bold text-gray-600 flex item-center justify-center">
                                        {{ $item['method'] }}
                                    </div>
                                @endif
                                @if( $item['method'] == 'GET')
                                    <div class="bg-green-300 py-1 px-2 rounded font-bold text-gray-600 flex item-center justify-center">
                                        {{ $item['method'] }}
                                    </div>
                                @endif
                                @if( $item['method'] == 'PATCH')
                                    <div class="bg-blue-300 py-1 px-2 rounded font-bold text-gray-600 flex item-center justify-center">
                                        {{ $item['method'] }}
                                    </div>
                                @endif
                                @if( $item['method'] == 'DELETE')
                                    <div class="bg-red-300 py-1 px-2 rounded font-bold text-gray-600 flex item-center justify-center">
                                        {{ $item['method'] }}
                                    </div>
                                @endif
                                @if( $item['method'] == 'PUT')
                                    <div class="bg-blue-300 py-1 px-2 rounded font-bold text-gray-600 flex item-center justify-center">
                                        {{ $item['method'] }}
                                    </div>
                                @endif
                                <div class="ml-4 px-2 rounded text-sm text-gray-100 flex items-center bg-gray-800 w-96 pl-4">
                                    {{ $item['path'] }}
                                </div>
                            </div>
                        </div>
                        <div class="w-full bg-gray-100 p-4">
                            <div class="w-full text-gray-800 font-bold">Headers</div>
                            <table class="w-full text-sm border mb-3">
                                <thead class="bg-gray-200 text-gray-600">
                                    <tr class="">
                                        <th class="border px-2 border-gray-300 py-1">Key</th>
                                        <th class="border px-2 border-gray-300 py-1">Value</th>
                                        <th class="border px-2 border-gray-300 py-1">Type</th>
                                        <th class="border px-2 border-gray-300 py-1">Optional</th>
                                        <th class="border px-2 border-gray-300 py-1">Sample</th>
                                        <th class="border px-2 border-gray-300 py-1">Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($item['headers'] as $header)
                                        <tr class="border bg-white">
                                            <td class="border px-2 py-1">{{ $header['key'] }}</td>
                                            <td class="border px-2 py-1">{{ $header['value'] }}</td>
                                            <td class="border px-2 py-1">{{ $header['type'] }}</td>
                                            <td class="border px-2 py-1">{{ $header['optional'] }}</td>
                                            <td class="border px-2 py-1">{{ $header['sample'] }}</td>
                                            <td class="border px-2 py-1">{{ $header['description'] }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>

                            @if (isset($item['param']) && $item['param'] != null)
                                <strong>Parameter</strong>
                                <table class="w-full text-sm border mb-3">
                                    <thead class="bg-gray-200 text-gray-600">
                                    <tr class="">
                                        <th class="border px-2 border-gray-300 py-1">Key</th>
                                        <th class="border px-2 border-gray-300 py-1">Value</th>
                                        <th class="border px-2 border-gray-300 py-1">Type</th>
                                        <th class="border px-2 border-gray-300 py-1">Optional</th>
                                        <th class="border px-2 border-gray-300 py-1">Sample</th>
                                        <th class="border px-2 border-gray-300 py-1">Description</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($item['param'] as $body)
                                        <tr class="border bg-white">
                                            <td class="border px-2 py-1">
                                                @php
                                                    $text = $body['key'];

                                                    if (\Illuminate\Support\Str::contains($body['key'], '.*.')) {
                                                        $arr = explode('.', $body['key']);
                                                        $text = "{$arr[0]}[][{$arr[2]}]";
                                                    } elseif (\Illuminate\Support\Str::contains($body['key'], '.')) {
                                                        $arr = explode('.', $body['key']);

                                                        if (count($arr) == 2) {
                                                            $text = "{$arr[0]}[{$arr[1]}]";
                                                        } else {
                                                            $text = "{$arr[0]}[{$arr[1]}][{$arr[2]}]";
                                                        }
                                                    }
                                                @endphp
                                                {{ $text }}
                                            </td>
                                            <td class="border px-2 py-1">{{ $body['value'] }}</td>
                                            <td class="border px-2 py-1">{{ $body['type'] }}</td>
                                            <td class="border px-2 py-1">{{ $body['optional'] }}</td>
                                            <td class="border px-2 py-1">{{ $body['sample'] }}</td>
                                            <td class="border px-2 py-1">{{ $body['description'] }}</td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            @endif
                            @if (isset($item['body']) && $item['body'] != null)
                                <strong>Body</strong>
                                <table class="w-full text-sm border mb-3">
                                    <thead class="bg-gray-200 text-gray-600">
                                        <tr class="">
                                        <th class="border px-2 border-gray-300 py-1">Key</th>
                                        <th class="border px-2 border-gray-300 py-1">Value</th>
                                        <th class="border px-2 border-gray-300 py-1">Type</th>
                                        <th class="border px-2 border-gray-300 py-1">Optional</th>
                                        <th class="border px-2 border-gray-300 py-1">Sample</th>
                                        <th class="border px-2 border-gray-300 py-1">Description</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($item['body'] as $body)
                                        <tr class="border bg-white">
                                            <td class="border px-2 py-1">
                                                @php
                                                    $text = $body['key'];

                                                    if (\Illuminate\Support\Str::contains($body['key'], '.*.')) {
                                                        $arr = explode('.', $body['key']);
                                                        $text = "{$arr[0]}[][{$arr[2]}]";
                                                    } elseif (\Illuminate\Support\Str::contains($body['key'], '.')) {
                                                        $arr = explode('.', $body['key']);

                                                        if (count($arr) == 2) {
                                                            $text = "{$arr[0]}[{$arr[1]}]";
                                                        } else {
                                                            $text = "{$arr[0]}[{$arr[1]}][{$arr[2]}]";
                                                        }
                                                    }
                                                @endphp
                                                {{ $text }}
                                            </td>
                                            <td class="border px-2 py-1">{{ $body['value'] }}</td>
                                            <td class="border px-2 py-1">{{ $body['type'] }}</td>
                                            <td class="border px-2 py-1">{{ $body['optional'] }}</td>
                                            <td class="border px-2 py-1">{{ $body['sample'] }}</td>
                                            <td class="border px-2 py-1">{{ $body['description'] }}</td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            @endif
                            <strong>Sample Request</strong>

                            <div x-data="{
                                openTab: 1-{{ $key }},
                                activeClasses: 'inline-block p-4 text-blue-600 bg-gray-100 rounded-t-lg active light:bg-gray-800 light:text-blue-500',
                                inactiveClasses: 'inline-block p-4 rounded-t-lg hover:text-gray-600 hover:bg-gray-50 light:hover:bg-gray-500 light:hover:text-gray-300'
                                }">
                                <ul class="flex flex-wrap text-sm font-medium text-center text-gray-500 border-b border-gray-200 dark:border-gray-700 dark:text-gray-400">
                                    <li class="mr-2" @click="openTab = 1-{{ $key }}" :class="{ '-mb-px': openTab === 1-{{ $key }} }">
                                        <button :class="openTab === 1-{{ $key }} ? activeClasses : inactiveClasses">Node JS</button>
                                    </li>
                                    <li class="mr-2" @click="openTab = 2-{{ $key }}" :class="{ '-mb-px': openTab === 2-{{ $key }} }">
                                        <button :class="openTab === 2-{{ $key }} ? activeClasses : inactiveClasses">PHP</button>
                                    </li>
                                    <li class="mr-2" @click="openTab = 3-{{ $key }}" :class="{ '-mb-px': openTab === 3-{{ $key }} }">
                                        <button :class="openTab === 3-{{ $key }} ? activeClasses : inactiveClasses">CURL</button>
                                    </li>
                                </ul>
                                
                                <div class="bg-gray-800 rounded w-full p-4" style="overflow: auto;">
                                    @php
                                        $params = collect($item['body'])->mapWithKeys(function ($item) {
                                            return [$item['key'] => $item['value']];
                                        })->undot();

                                        $params = $params->transform(function ($item, $key) {
                                            if (is_array($item) && array_key_exists('*', $item)) {
                                                return [$item['*']];
                                            }

                                            return $item;
                                        });
                                    @endphp
                                    <div x-show="openTab === 1-{{ $key }}" class="tab-pane fade">
                                        <code class="text-xs font-light text-white" style="white-space: pre;">var request = require("request");

var requestBody = {{ json_encode(json_decode($params->toJson(), true, 512, JSON_BIGINT_AS_STRING), JSON_PRETTY_PRINT) }};

var options = {
    "method": "POST",
    "url": "{{ $data['api_endpoint']['development'] }}{{ $item['path'] }}",
    "headers": {
        "Content-Type":"application/json"{{ last($item['headers'])['key'] == 'Authorization' ? ',' : '' }}
@if(last($item['headers'])['key'] == 'Authorization')
        "Authorization": "Bearer &#60;access_token&#62;"
@endif
    },
    "body": JSON.stringify(requestBody)
};

request(options, function (error, response) {
    if (error) throw new Error(error);
    console.log(response.body);
});
                                        </code>
                                    </div>
                                    <div x-show="openTab === 2-{{ $key }}">
                                        <code class="text-xs font-light text-white" style="white-space: pre;">$curl = curl_init();

$body = '{{ json_encode(json_decode($params->toJson(), true, 512, JSON_BIGINT_AS_STRING), JSON_PRETTY_PRINT) }}';

curl_setopt_array($curl, array(
    CURLOPT_URL => "{{ $data['api_endpoint']['development'] }}{{ $item['path'] }}",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => "",
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 10,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => "POST",
    CURLOPT_POSTFIELDS => $body,
    CURLOPT_HTTPHEADER => array(
        "Content-Type" => "application/json"{{ last($item['headers'])['key'] == 'Authorization' ? ',' : '' }}
@if(last($item['headers'])['key'] == 'Authorization')
        "Authorization" => "Bearer &#60;access_token&#62;"
@endif
    )
));

$response = curl_exec($curl);
curl_close($curl);

echo $response;
                                        </code>
                                    </div>
                                    <div x-show="openTab === 3-{{ $key }}">
                                        <code class="text-xs font-light text-white" style="white-space: pre;">curl --location --request '{{ $data['api_endpoint']['development'] }}{{ $item['path'] }}' \
--header 'Content-Type:application/json' \
@if(last($item['headers'])['key'] == 'Authorization')
--header 'Authorization:Bearer &#60;access_token&#62;' \
@endif
--data-raw '{{ json_encode(json_decode($params->toJson(), true, 512, JSON_BIGINT_AS_STRING), JSON_PRETTY_PRINT) }}'
                                        </code>
                                    </div>
                                </div>
                            </div>

                            <strong>Sample Response</strong>
                            <div class="bg-gray-800 rounded w-full p-4" style="overflow: auto;">
                                <code class="text-xs font-light text-white">
                                    <div class="language-json" style="white-space: pre-wrap;">{{ json_encode(json_decode($item['response']), JSON_PRETTY_PRINT) }}</div>
                                </code>
                            </div>
                        </div>
                    </div>
                @endforeach
                @endforeach
            </div>
        </div>
    </div>
    <script>
        document.addEventListener('alpine:init', () => {
                Alpine.data('list', () => ({
                    active: 0,

                    setActive(i) {
                        this.active = i
                    }
                }))
            })
    </script>

</body>
</html>
