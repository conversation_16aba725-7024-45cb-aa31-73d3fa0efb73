#!/bin/bash

current_date_time="$(date +%Y_%m_%d_%H_%M_%S)"
log_file_name="logs/${current_date_time}.log"

set -e

mkdir -p logs

exec 1> >(tee $log_file_name) 2>&1

cd /opt/docker/repo/fw-backend

git pull origin main --rebase

docker exec fw_api composer install --no-dev --no-interaction --prefer-dist --optimize-autoloader --no-ansi
docker exec fw_api php artisan migrate --force
docker exec fw_api php artisan optimize:clear
docker exec fw_api php artisan queue:restart

docker stop fw_api

/usr/local/bin/docker-compose -f /opt/docker/docker-compose.yaml up -d php
