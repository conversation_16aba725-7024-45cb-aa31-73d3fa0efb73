## Setup New Game

1. Product
   Create new product. Example: MT

```sql
INSERT INTO `product` (`id`, `name`, `code`, `type`, `priority`, `image_url`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (NULL, 'MT', 'MT', 'oc', '1', 'MT.png', '1', '2024-09-13 12:25:23', '2024-09-13 12:25:23', NULL);
```

2. If member non-exits

1. getProductBalance

and create new record inside UserProduct

<!-- Add Product Setting -->

# Tranfer In the Amount

INSERT INTO `product_setting` (`id`, `product_id`, `name`, `value`, `type`, `reference`, `description`, `deleted_at`) VALUES (NULL, '2002', 'display_top_up', '0', NULL, NULL, NULL, NULL), (NULL, '2002', 'hasWalletList', '1', 'Main', NULL, NULL, NULL);

## Setup Credit Setting

UPDATE `credit_setting` SET `type` = '[2001,2002]' WHERE `credit_setting`.`id` = 19;

<!-- CQ9 -->

INSERT INTO `product` (`id`, `name`, `code`, `type`, `priority`, `image_url`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (NULL, 'CQ9', 'CQ9', 'oc', '1', 'CQ9.png', '1', '2024-09-23 04:25:23', '2024-09-23 04:25:23', NULL);

INSERT INTO `product_setting` (`id`, `product_id`, `name`, `value`, `type`, `reference`, `description`, `deleted_at`) VALUES (NULL, '2003', 'display_top_up', '0', NULL, NULL, NULL, NULL), (NULL, '2003', 'hasWalletList', '1', 'Main', NULL, NULL, NULL);

INSERT INTO `services` (`id`, `name`, `url`, `icon`, `product_id`, `wallet_type`, `is_top_game`, `is_hot_game`, `game_provider_id`, `game_category_id`, `game_setting_id`, `priority`, `status`, `created_at`, `updater_id`, `updated_at`, `deleted_at`) VALUES (NULL, 'MT v2', 'https://api.cqgame.games', 'production/2024/10/1727952810_5881', '2003', 'Main', '0', '0', NULL, NULL, NULL, '6', '1', NOW(), '1', '2024-09-15 03:17:36', NULL)
