APP_NAME="FW"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_DEBUG_KEY=
APP_TIMEZONE=Asia/Kuala_Lumpur
APP_LANG=en
API_KEY_ENCKEY=

APP_URL_API=local-api.fw.com
APP_URL_API_ADMIN=local-api-admin.fw.com
APP_URL_API_USER=local-api-user.fw.com
APP_URL_API_APP=local-api-app.fw.com
APP_URL_USER=

EXCLUDE_GLOBAL_MIDDLEWARE=api-docs,api-docs-admin,api-docs-user,telescope
EXCLUDE_GLOBAL_CALLBACK=

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_TIMEZONE=Asia/Kuala_Lumpur
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=fw
DB_USERNAME=root
DB_PASSWORD=

PAGINATION_ROWS=15

BROADCAST_DRIVER=log
# FILESYSTEM_DISK=local
QUEUE_CONNECTION=database
SESSION_DRIVER=file
SESSION_LIFETIME=120

JWT_SECRET=
JWT_TTL=526000
USER_JWT_TTL=526000

AWS_COLLECTION=
AWS_S3_ACCESS_KEY=
AWS_S3_SECRET_KEY=
AWS_REGION=

TRONUMX_API_KEY=
TRONUMX_API_URL=
TRONUMX_MAIN_ADDRESS=

FPAY_USERNAME=
FPAY_API_KEY=
FPAY_SECRET_KEY=
FPAY_CALLBACK_URL=
FPAY_API_URL=
FPAY_PHONE_NO=
FPAY_REDIRECT_URL=

CACHE_DRIVER=redis

REDIS_CLIENT=predis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_CACHE_DB=0
REDIS_PREFIX=
CACHE_PREFIX=
CACHE_EXPIRY_SECONDS=31556952

MAIL_MAILER=smtp
MAIL_HOST=
MAIL_PORT=
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=""
MAIL_FROM_NAME="${APP_NAME}"

SMS_YUNPIAN_URL=
SMS_YUNPIAN_API_KEY=
SMS_YUNPIAN_TPL_ID_EN=

SMS_GOSMS_URL=
SMS_GOSMS_COMPANY_LOGIN=
SMS_GOSMS_USER_ID=
SMS_GO_SMS_PASSWORD=

SMS_TWILIO_URL=
SMS_TWILIO_USERNAME=
SMS_TWILIO_PASSWORD=
SMS_TWILIO_MSG_SVC_ID=

SMS_SKYLINE_URL=
SMS_SKYLINE_USERNAME=
SMS_SKYLINE_PASSWORD=
SMS_SKYLINE_SIGNATURE=

SMS_NEXMO_URL=
