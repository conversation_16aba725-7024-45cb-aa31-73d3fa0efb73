{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "aws/aws-sdk-php": "^3.228", "defstudio/telegraph": "^1.41", "doctrine/dbal": "^2.13", "glushkovds/phpclickhouse-laravel": "^2.2", "guzzlehttp/guzzle": "^7.2", "kreait/laravel-firebase": "^5.8", "laravel-notification-channels/telegram": "^2.1", "laravel/framework": "9.52.5", "laravel/prompts": "^0.3.1", "laravel/sanctum": "^2.14.1", "laravel/telescope": "^4.9", "laravel/tinker": "^2.7", "maatwebsite/excel": "^3.1", "predis/predis": "^1.1", "psr/simple-cache": "^2.0", "pusher/pusher-php-server": "^7.2", "rap2hpoutre/fast-excel": "^5.2", "sentry/sentry-laravel": "^4.13", "smi2/phpclickhouse": "^1.6", "soundasleep/html2text": "^2.1", "spatie/laravel-activitylog": "^4.5", "stevebauman/location": "^6.6", "tymon/jwt-auth": "*"}, "require-dev": {"laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --no-ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}