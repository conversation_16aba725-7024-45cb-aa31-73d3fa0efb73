<?php

namespace App\Traits;

use App\Models;

trait TronUMXTrait
{

    public static function generateWalletAddress($label)
    {
        $endpoint = env('TRONUMX_API_URL').'/address/generate';

        $requestData = [
            'method' => 'POST',
            'endpoint' => $endpoint,
            'header' => [
                'Connection: close',
                'Api-Key: '.env("TRONUMX_API_KEY"),
            ],
            'body' => [
                "label" => $label,
                "owner_address" => env("TRONUMX_MAIN_ADDRESS"),
            ],
        ];

        $res = CurlTrait::doCurl($requestData);
        $result = json_decode($res,true);
        $walletAddres = $result['data']['address'] ?? NULL;

        return $walletAddres;
    }
}
