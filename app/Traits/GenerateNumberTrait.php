<?php

namespace App\Traits;

use App\Models\Country;
use App\Models\DeliveryOrder;
use App\Models\GroupId;
use App\Models\NewId;
use App\Models\Order;
use App\Models\TransactionLogs;
use Illuminate\Support\Str;

trait GenerateNumberTrait
{
    public static function GenerateMemberID($check_table)
    {
        $memberIDLength = config('users.memberIDLength') ?? 9;
        $min = 1;
        $max = 9;
        $memberIDLength -= 1;
        for ($i = 1; $i < $memberIDLength; $i++) {
            $max .= '9';
        }
        while (1) {
            $firstDigit = mt_rand(1, 9);
            $memberID = $firstDigit.sprintf('%0'.$memberIDLength.'s', mt_rand((int) $min, (int) $max));
            $checkTable = clone $check_table;
            $check = $checkTable->where('member_id', $memberID)->first();
            if (empty($check)) {
                break;
            }
        }

        return $memberID;
    }

    public static function GenerateNumber($check_table)
    {
        $newNumber = date('Ymd').date('His').str_pad(rand(0, 99), 2, 0, STR_PAD_LEFT);

        $checkTable = clone $check_table;
        $check = $checkTable->where('payment_number', $newNumber)->first();
        if ($check) {
            return self::GenerateNumber($check_table);
        }

        return $newNumber;
    }

    public static function GenerateRandomAlphanumeric($check_table, $check_column)
    {

        $input = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $input_length = strlen($input);
        $strength = config('numbers.depositUniqueCodeLength');
        $random_string = '';
        for ($i = 0; $i < $strength; $i++) {
            $random_character = $input[mt_rand(0, $input_length - 1)];
            $random_string .= $random_character;
        }

        $checkTable = clone $check_table;
        $check = $checkTable->where($check_column, $random_string)->first();
        if ($check) {
            return self::generateRandomAlphanumeric($check_table, $check_column);
        }

        return $random_string;
    }

    public static function GenerateRandomSmallCasenumeric($check_table, $check_column)
    {

        $input = '0123456789abcdefghijklmnopqrstuvwxyz';
        $input_length = strlen($input);
        $strength = 6;
        $random_string = '';
        for ($i = 0; $i < $strength; $i++) {
            $random_character = $input[mt_rand(0, $input_length - 1)];
            $random_string .= $random_character;
        }

        $checkTable = clone $check_table;
        $check = $checkTable->where($check_column, $random_string)->first();
        if ($check) {
            return self::GenerateRandomSmallCasenumeric($check_table, $check_column);
        }

        return $random_string;
    }

    public static function generateReferenceNo($check_table, $column, $newNumber = null, $category = null)
    {
        // switch ($category) {
        //     case 'tt-instant-transfer':
        //     case 'tt-normal-transfer':
        //         $newNumber = 'TT'.str_pad(rand(0, ********), 8, 0, STR_PAD_LEFT);
        //         break;
        //
        //     case 'bank-withdrawal':
        //         $newNumber = 'WB'.str_pad(rand(0, ********), 8, 0, STR_PAD_LEFT);
        //         break;
        //
        //     case 'TK8':
        //     case 'TK8TH':
        //         $newNumber = $category.'_'.str_pad(rand(0, ********), 8, 0, STR_PAD_LEFT);
        //         break;
        //
        //     default:
        //         $newNumber = 'FW'.str_pad(rand(0, ********), 8, 0, STR_PAD_LEFT);
        //         break;
        // }

        $prefix = 'UW';
        $checkTable = clone $check_table;
        $check = $checkTable->latest()->first();

        $latestNumber = null;
        $offsetLength = 2;

        do {
            if ($newNumber) {
                $latestNumber = substr($newNumber, $offsetLength);
            } elseif (str_starts_with($check?->$column ?? '', $prefix)) {
                $latestNumber = substr($check?->$column ?? '', $offsetLength++);
            } else {
                $latestNumber = $check?->id ?? 0;
            }
        } while (! $latestNumber || ! is_numeric($latestNumber));

        $newNumber = $prefix.str_pad(++$latestNumber, 8, 0, STR_PAD_LEFT);
        $check = $checkTable->where($column, $newNumber)->count();

        if ($check) {
            return self::generateReferenceNo($check_table, $column, $newNumber);
        }

        return $newNumber;
    }

    public static function GenerateNewId()
    {
        $newID = NewId::create()->id;

        return $newID;
    }

    public static function GenerateGroupId()
    {
        $newID = GroupId::create()->id;

        return $newID;
    }

    public static function GenerateOTPCode()
    {
        $otpCode = mt_rand(100000, 999999);

        return $otpCode;
    }

    public static function generateVerifyUserCode()
    {
        return rand(0, 9).rand(0, 9).rand(0, 9).rand(0, 9).rand(0, 9).rand(0, 9).rand(0, 9).rand(0, 9);
    }

    public static function generatePaymentNumber($type, $orderID = null)
    {
        switch ($type) {
            /*case 'bank':
                $tbl = new BankOrderLogs;
                $prefix = 'B';
                $tblColumn = 'payment_number';
                break;
                */
            case 'crypto':
                $tbl = new TransactionLogs;
                $prefix = 'T';
                $tblColumn = 'payment_number';
                $getLast = $tbl::selectRaw("MAX(CAST(SUBSTRING($tblColumn,".(strlen($prefix) + 1).') AS UNSIGNED)) as payment_number')->whereNotNull($tblColumn)->first();
                $paymentNumber = $prefix.sprintf('%06d', $getLast->payment_number + 1);
                break;

            case 'order':
                $tbl = new Order;
                $prefix = 'INV';
                $tblColumn = 'reference_number';
                $getLast = $tbl::selectRaw("MAX(CAST(SUBSTRING($tblColumn,".(strlen($prefix) + 1).') AS UNSIGNED)) as payment_number')->whereNotNull($tblColumn)->first();
                $paymentNumber = $prefix.sprintf('%06d', $getLast->payment_number + 1);
                break;

            case 'delivery_order':
                $tbl = new DeliveryOrder;
                $order_prefix = 'INV';
                $prefix = 'DO';
                $tblColumn = 'reference_number';

                if (! isset($orderID)) {
                    abort(400, json_encode('Invalid Order.'));
                } else {
                    $orders = $tbl::where('order_id', $orderID)->first() ?? null;

                    if (isset($orders)) {
                        $letter = preg_split('/(?<=[0-9])(?=[a-z]+)/i', $orders->reference_number);
                        $running = $letter[0];
                        $letter = $letter[1];
                        $letter++;
                        $paymentNumber = $running.$letter;
                    } else {
                        $order = Order::find($orderID);
                        $running = str_replace($order_prefix, $prefix, $order->reference_number);
                        $letter = 'A';
                        $paymentNumber = $running.$letter;
                    }
                }
                break;
        }

        return $paymentNumber;
    }

    public static function generateRandomPassword()
    {
        $length = config('numbers.randomPasswordLength') ?? 8;

        $pass = [];
        $alphabet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890';

        $alphaLength = strlen($alphabet) - 1;

        for ($i = 0; $i < $length; $i++) {
            $n = rand(0, $alphaLength);
            $pass[] = $alphabet[$n];
        }

        $pass = implode($pass);
        if (! preg_match('/[A-Za-z].*[0-9]|[0-9].*[A-Za-z]/', $pass)) {
            $pass = self::generateRandomPassword();
        }

        return $pass;
    }

    public static function generateMemberIDPrefix($tableQuery, $countryID)
    {
        $prefix = config('users.memberIDPrefix') ?? 'CQ';
        $length = config('users.memberIDLength') ?? 6;
        $countryID = $countryID ?? null;

        if (empty($countryID)) {
            return false;
        }

        $countryRes = Country::find($countryID);

        if (empty($countryRes)) {
            return false;
        }

        $isoCode = $countryRes->iso_code2 ?? null;

        if (empty($isoCode)) {
            return false;
        }

        while (1) {
            $random = '';

            for ($i = 0; $i < $length; $i++) {
                $number = rand(0, 9);
                $random .= $number;
            }

            $memberID = $prefix.$random.$isoCode;

            $checkTable = clone $tableQuery;

            $check = $checkTable->where('member_id', $memberID)->first();

            if (empty($check)) {
                break;
            }
        }

        return $memberID;
    }

    public static function generateUsername($tableQuery)
    {
        $maxLength = config('users.maxUsernameLength') ?? 8;

        while (1) {
            $random = '';

            for ($i = 0; $i < $maxLength; $i++) {
                $number = rand(0, 9);
                $random .= $number;
            }

            $username = $random;

            $checkTable = clone $tableQuery;

            $check = $checkTable->where('member_id', $username)->first();

            if (empty($check)) {
                break;
            }
        }

        return $username;
    }

    public static function GenerateRandomNumeric($check_table, $check_column)
    {

        $input = '0123456789';
        $input_length = strlen($input);
        $strength = config('numbers.stockTransfer');
        $random_string = '';
        for ($i = 0; $i < $strength; $i++) {
            $random_character = $input[mt_rand(0, $input_length - 1)];
            $random_string .= $random_character;
        }

        $checkTable = clone $check_table;
        $check = $checkTable->where($check_column, $random_string)->first();
        if ($check) {
            return self::generateRandomAlphanumeric($check_table, $check_column);
        }

        return $random_string;
    }

    public static function generateStorePhoneNumber($tableQuery, $countryCode, $prefix, $suffix, $length)
    {
        $pLen = Str::length($prefix);
        $sLen = Str::length($suffix);
        $remainLength = $length - $pLen - $sLen;

        while (1) {
            $random = '';

            for ($i = 0; $i < $remainLength; $i++) {
                $number = rand(0, 9);
                $random .= $number;
            }

            $username = $countryCode.$prefix.$random.$suffix;

            $checkTable = clone $tableQuery;

            $check = $checkTable->where('username', $username)->first();

            if (empty($check)) {
                break;
            }
        }

        return $countryCode.'-'.$prefix.$random.$suffix;
    }

    public static function generateReferralCode($check_table, $column)
    {
        $newNumber = bin2hex(random_bytes(4));

        $check = $check_table->where($column, $newNumber)->count();

        if ($check) {
            return self::generateReferralCode($check_table, $column);
        }

        return $newNumber;
    }
}
