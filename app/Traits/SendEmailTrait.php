<?php

namespace App\Traits;

use App\Models\EmailLog;
use Illuminate\Support\Facades\Mail;

trait SendEmailTrait
{
    public static function sendEmail($params, $mail)
    {
        $verifyEmailType = self::verifyEmailType($params['email_type']);

        if(!$verifyEmailType){
            throw new \Exception('Invalid email type.');
        }

        $checkLog = self::checkEmailLog([
            'email' => $params['email'],
            'email_type' => $params['email_type'],
        ]);

        if(gettype($checkLog) == 'integer'){
            // throw new \Exception("An email has been sent out before. Please try again in $checkLog minutes.");
        }

        if(env('APP_ENV') != 'local'){
            Mail::to($params['email'])->queue($mail);    
        }

        self::createEmailLog([
            'email' => $params['email'],
            'email_type' => $params['email_type'],
        ]);
    }

    protected static function verifyEmailType(int $emailType): bool
    {
        return in_array($emailType,EmailLog::$emailType);
    }

    protected static function checkEmailLog(array $params = []): bool|int
    {
        $checkHistory = EmailLog::nonExpiredEmailRequest([
            'email' => $params['email'],
            'email_type' => $params['email_type'],
        ])->first();

        if($checkHistory){
            $differenceInMinute = now()->diffInMinutes($checkHistory->expired_at);
            return $differenceInMinute;
        }

        return true;
    }

    protected static function createEmailLog(array $params = []): bool
    {
        $emailLog = EmailLog::create([
            'email' => $params['email'],
            'email_type' => $params['email_type'],
            'expired_at' => now()->addMinute(5), // Temporary
        ]);

        return $emailLog ? true : false;
    }
}
