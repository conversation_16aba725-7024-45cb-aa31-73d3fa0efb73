<?php

namespace App\Traits;

use Aws\Credentials\Credentials;
use Aws\S3\PostObjectV4;
use Aws\S3\S3Client;
use Aws\Sts\StsClient;

trait S3Trait
{
    protected static $s3;

    protected static function assignS3Property()
    {
        self::$s3 = config('filesystems.disks.s3');
    }

    public static function uploadToS3($fileName, $contentType, $isPublic = false, $minSize = 1024, $maxSize = 10485760)
    {
        self::assignS3Property();
        $s3Bucket = $isPublic ? self::$s3['bucket_pub'] : self::$s3['bucket_pvt'];
        $fileDestination = self::fileDestination($fileName);
        $acl = ['acl' => 'private'];
        $options = [
            ['acl' => 'private'],
            ['bucket' => $s3Bucket],
            ['eq', '$key', $fileDestination],
            ['content-length-range', $minSize, $maxSize],
            ['content-type' => $contentType],
        ];

        $s3Client = self::getS3Client();

        //generate post object
        // $post_object = new PostObjectV4($s3Client, $s3Bucket, $acl, $options, self::$s3['client_expiry'].' seconds');
        $post_object = new PostObjectV4($s3Client, $s3Bucket, $acl, $options);
        $form_attributes = $post_object->getFormAttributes();
        $form_inputs = $post_object->getFormInputs();
        $form_inputs['key'] = $fileDestination;

        $data = [
            'body' => $form_attributes,
            'field' => $form_inputs,
            'url' => $form_attributes['action']."/".$form_inputs['key'],
        ];

        return $data;
    }

    public static function uploadToS3Multi($param = [])
    {
        self::assignS3Property();
        $s3Client = self::getS3Client();

        foreach ($param['upload'] as $uploadData) {
            $fileName = $uploadData['filename'] ?? null;
            $contentType = $uploadData['content_type'] ?? null;
            $isPublic = $uploadData['is_public'] ?? null;
            $minSize = $uploadData['min_size'] ?? 1024;
            $maxSize = $uploadData['max_size'] ?? 10485760;

            $s3Bucket = $isPublic ? self::$s3['bucket_pub'] : self::$s3['bucket_pvt'];
            $fileDestination = self::fileDestination($fileName);
            $acl = ['acl' => 'private'];
            $options = [
                ['acl' => 'private'],
                ['bucket' => $s3Bucket],
                ['eq', '$key', $fileDestination],
                ['content-length-range', $minSize, $maxSize],
                ['content-type' => $contentType],
            ];

            
            //generate post object
            // $post_object = new PostObjectV4($s3Client, $s3Bucket, $acl, $options, self::$s3['client_expiry'].' seconds');
            $post_object = new PostObjectV4($s3Client, $s3Bucket, $acl, $options);
            $form_attributes = $post_object->getFormAttributes();
            $form_inputs = $post_object->getFormInputs();
            $form_inputs['key'] = $fileDestination;

            $res = [
                'body' => $form_attributes,
                'field' => $form_inputs,
                'url' => $form_attributes['action']."/".$form_inputs['key'],
            ];

            $data[] = $res;
        }

        return $data;
    }

    public static function getS3Client()
    {
        self::assignS3Property();

        // Get Cache
        $cacheName = md5('sts-access');
        $s3ClientParams = CacheTrait::getCache($cacheName);
        if (!empty($s3ClientParams)) {
            // Decryption
            $s3ClientParams->credentials = ApiKeyTrait::decryptApiKey(['api_key' => $s3ClientParams->credentials['api_key'], 'iv' => $s3ClientParams->credentials['iv']]);
            if ($s3ClientParams->credentials) {
                $s3ClientParams->credentials = new Credentials($s3ClientParams->credentials['AccessKeyId'], $s3ClientParams->credentials['SecretAccessKey'], $s3ClientParams->credentials['SessionToken']);
                $s3Client = new S3Client((array)$s3ClientParams);
                return $s3Client;
            }
        }

        $stsClient = new StsClient([
            'region' => self::$s3['region'],
            'version' => 'latest',
            'credentials' => new Credentials(self::$s3['key'], self::$s3['secret']),
        ]);

        $assumeRole = $stsClient->AssumeRole([
            'RoleArn' => self::$s3['role_arn'],
            'RoleSessionName' => 'AssumeSession',
            'DurationSeconds' => self::$s3['assume_expiry'],
        ]);

        $assume = $assumeRole['Credentials'];

        $s3ClientParams = [
            'version' => 'latest',
            'region' => self::$s3['region'],
            // 'credentials' => new Credentials($assume['AccessKeyId'], $assume['SecretAccessKey'], $assume['SessionToken'], self::$s3['client_expiry'].' seconds'),
            'credentials' => new Credentials($assume['AccessKeyId'], $assume['SecretAccessKey'], $assume['SessionToken']),
        ];

        $s3Client = new S3Client($s3ClientParams);

        // Encryption
        $s3ClientParams['credentials'] = ApiKeyTrait::generateEncryptedApiKey(['data' => ['AccessKeyId' => $assume['AccessKeyId'], 'SecretAccessKey' => $assume['SecretAccessKey'], 'SessionToken' => $assume['SessionToken']]]);

        // Set Cache
        $bufferTime = 300; // To prevent the access expired before redis expired
        $redisExpiryTime = (self::$s3['assume_expiry'] - $bufferTime);
        if($redisExpiryTime <= 0) $redisExpiryTime = 600; // Default 10 mins
        CacheTrait::setCache($cacheName, (object)$s3ClientParams, now()->add($redisExpiryTime . ' seconds'));

        return $s3Client;
    }

    protected static function fileDestination($fileName)
    {
        // Change filename to milisecond 2022-09-30
        $unixPrefix = time() . '_' . rand(0, 9) . rand(0, 9) . rand(0, 9) . rand(0, 9);

        return config('app.env') . '/' . date('Y') . '/' . date('m') . '/' . $unixPrefix;
    }

    public static function awsDownload($s3Client, $filename, $isPublic = false)
    {
        if (is_array($filename)) {
            $presignedUrl = [];
            foreach ($filename as $path) {
                $filePathArr = explode('/', $path);
                $file = $filePathArr[count($filePathArr) - 1];
                $cmd = $s3Client->getCommand('GetObject', [
                    'Bucket' => $isPublic ? self::$s3['bucket_pub'] : self::$s3['bucket_pvt'],
                    'Key' => $path,
                    'ResponseContentDisposition' => 'inline; filename="' . $file . '"',
                ]);

                $request = $s3Client->createPresignedRequest($cmd, '+' . self::$s3['assume_expiry'] . 'seconds');
                $presignedUrl[$path] = (string) $request->getUri();
            }
        } else {
            $filePathArr = explode('/', $filename);
            $file = $filePathArr[count($filePathArr) - 1];
            /*$checkExist = $s3Client->doesObjectExist($isPublic ? self::$s3['bucket_pub'] : self::$s3['bucket_pvt'], $filename);
            if (!$checkExist) {
                return null;
            }*/

            $cmd = $s3Client->getCommand('GetObject', [
                'Bucket' => $isPublic ? self::$s3['bucket_pub'] : self::$s3['bucket_pvt'],
                'Key' => $filename,
                'ResponseContentDisposition' => 'inline; filename="' . $file . '"',
                'IfNoneMatch' => 'Not match',
            ]);

            $request = $s3Client->createPresignedRequest($cmd, '+' . self::$s3['assume_expiry'] . 'seconds');
            $presignedUrl = (string) $request->getUri();
        }
        return $presignedUrl;
    }

    public static function getFileInfo($url)
    {
        $temp = tmpfile();
        try {
            fwrite($temp,file_get_contents($url));
        } catch (\Throwable $th) {
            fclose($temp);
            return null;
        }
        
        $fileURI = stream_get_meta_data($temp)['uri'];
        $fileData = getimagesize($fileURI);
        fclose($temp);

        return $fileData;
    }
}
