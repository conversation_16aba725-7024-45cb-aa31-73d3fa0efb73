<?php

namespace App\Traits;

use App\Models;
use DB;
use Illuminate\Support\Facades\Log;

trait FMTTrait
{
    private static function generateOcSecrect()
    {
        $millis = time(); // current UNIX timestamp;
        $secretKey = env('APP_FMT_SECRET_KEY') ?? null; 
        $secret = sha1($millis.$secretKey);
        return [ "millis" => $millis, "secret" => $secret];
    }

    protected static function ocTraitEnv()
    {
        // $systemSetting = Models\SystemSetting::where('name','ocTraitEnvironment')->first()['value'] ?? 'production';
        $object = Models\SystemSetting::where('name','ocTraitEnvironment')->first();
        $disabledProducts = json_decode($object->reference) ?? [];
        $systemSetting['env'] = $object->value ?? 'production';
        $systemSetting['enabled'] = (int)!in_array("FMT", $disabledProducts);
        return $systemSetting;
    }

    public static function postOC($params,$type){
        $ocSecrect = self::generateOcSecrect();
        $presetBody = [
            // 'millis' => (int) $ocSecrect['millis'] ?? null,
            // 'secret' => $ocSecrect['secret'] ?? null,
            'language' => $params['language'] ?? 'en',
            // 'company' => $params['company'] ?? env('APP_FMT_COMPANY') ?? null,
            'company' => $params['company'] ?? null,
        ];
        if(!isset($presetBody['company'])){
            unset($presetBody['company']);
        }

        unset($webhookParam);
        switch ($type) {
            case 'register': 
                $body = [
                    'ip' => $params['ip'] ?? null,
                    'account' => $params['account'] ?? null,
                    'password' => $params['password'] ?? null,
                    'dial_code' => $params['dial_code'] ?? null,
                ];

                if(isset($params['referral']) && !empty($params['referral'])) $body['referral'] = $params['referral'];
                break;

            // case 'auto_login':
            //     $body = [
            //         'account' => $params['account'] ?? null,
            //     ];
            //     break;
            case 'check_balance':
                $body = [
                    'account' => $params['account'] ?? null,
                ];
                break;

            // case 'deposit':
            //     $body = [
            //         'account' => $params['account'] ?? null,
            //         'amount' => $params['amount'] ?? null,
            //     ];

            //     // optional
            //     if(isset($params['user_bank_holder_name']) && !empty($params['user_bank_holder_name'])) $body['user_bank_holder_name'] = $params['user_bank_holder_name'];
            //     if(isset($params['remark']) && !empty($params['remark'])) $body['remark'] = $params['remark'];
            //     break;

            // case 'withdraw':
            //     $body = [
            //         'account' => $params['account'] ?? null,
            //     ];

            //     // optional
            //     if(isset($params['waive']) && !empty($params['waive'])) $body['waive'] = $params['waive'];
            //     if(isset($params['user_bank_holder_name']) && !empty($params['user_bank_holder_name'])) $body['user_bank_holder_name'] = $params['user_bank_holder_name'];
            //     if(isset($params['remark']) && !empty($params['remark'])) $body['remark'] = $params['remark'];
            //     break;

            // case 'get_player_gamelog':
            //     $body = [
            //         'date' => $params['date'] ?? date("Y-m-d", strtotime('-1 day')),
            //     ];
            //     break;
            case 'balance-promoid':
                $body = [
                    'order_id' => $params['order_id'] ?? null,
                    'amount' => $params['amount'] ?? null,
                    'account' => $params['account'] ?? null,
                ];
                break;
            
            default:
                $body = [];
                break;
        }

        // Prepare endpoint
        $endUrl = self::getEndUrl($type);
        $endpoint = env('APP_FMT_API_URL').$endUrl;

        $webhookParam['type'] = $type;
        $webhookParam['params'] = $params;
        $webhookParam['deposit_id'] = $params['deposit_id'] ?? null;
        $webhookParam['withdraw_id'] = $params['withdraw_id'] ?? null;
        $body = $presetBody + ($body ?? []);

        // Prepare curl
        $request = [
            'method' => 'POST',
            'endpoint' => $endpoint,
            'header' => [
                'X-API-KEY: '.env('APP_FMT_UTILITY_TOKEN'),
                'Content-Type: multipart/form-data',
            ],
            'body' => $body,
            'message' => $body,
        ];

        if(env('APP_ENV') == self::ocTraitEnv()['env'] && self::ocTraitEnv()['enabled']){
            try {
                $result = CurlTrait::doCurl($request,'postOC-fmt',$webhookParam);
            } catch( \Exception $e) {
                $returnData = ['status' => false, "refund" => 0, 'msg' => $e->getMessage() ?? "[$type] Something went wrong."];
                return $returnData;
            }
        } else{
            $result = null;
            switch ($type) {
                case 'register': 
                    $result = '{"status":true,"message":"Success","data":{"deeplink":"https://google.com"}}';
                    break;

                // case 'auto_login': 
                //     $result = '{"status":"SUCCESS","message":"Successfully Returned Login Link","result":{"login_link":"https:\/\/m.FMT.io?auth_code=7e861960"}}';
                //     break;

                case 'check_balance': 
                    $result = '{"status":true,"message":"success","data":{"status":true,"amount":10000,"loan_id":12345}}';
                    break;

                // case 'deposit': 
                //     $result = '{"status":"SUCCESS","message":"Deposit Success!","result": {"transaction_id": *********, "reference": "xxx099xx88", "deposit_amount": "10.00", "bonus_amount": "0.00", "before_balance": "254.00", "current_balance": "264.00"}}';
                //     // $result = '{"status": "ERROR","message": "The Amount field must contain a number greater than 0."}';
                //     break;

                // case 'withdraw': 
                //     $result = '{"status":"SUCCESS","message":"Withdraw Success!","result":{"transaction_id":*********,"reference":"00xxx0x00","witdraw_amount":"100.00","waive_amount":"0.00","before_balance":"200.00","current_balance":"0.00"}}';
                //     break;

                // case 'get_player_gamelog': 
                //     $result = '{"status":"SUCCESS","message":"Successfully returned result","result":[{"account":"**********","game_category":"SLOT","turnover_achieved":"126.75"},{"account":"***********","game_category":"SLOT","turnover_achieved":"122.00"},{"account":"**********","game_category":"SLOT","turnover_achieved":"162.31"},{"account":"**********","game_category":"SLOT","turnover_achieved":"53.15"},{"account":"**********","game_category":"SLOT","turnover_achieved":"158.65"},{"account":"**********","game_category":"LIVE","turnover_achieved":"1960.00"},{"account":"**********","game_category":"FISHING","turnover_achieved":"220.10"},{"account":"***********","game_category":"SLOT","turnover_achieved":"0.28"},{"account":"***********","game_category":"SLOT","turnover_achieved":"8.10"},{"account":"***********","game_category":"SLOT","turnover_achieved":"2.04"},{"account":"**********","game_category":"SLOT","turnover_achieved":"0.10"},{"account":"**********","game_category":"SLOT","turnover_achieved":"1.07"},{"account":"***********","game_category":"SLOT","turnover_achieved":"25.88"},{"account":"**********","game_category":"SLOT","turnover_achieved":"1.20"},{"account":"**********","game_category":"SLOT","turnover_achieved":"324.00"},{"account":"**********","game_category":"SLOT","turnover_achieved":"13.35"}]}';
                //     break;
                case 'balance-promoid':
                    $result = '{"status":true,"message":"success","data":{"status":true,"amount":10,"promo_id":1}}';
                    break;
                
                default:
                    break;
            }
        }

        if(!$result){
            return [
                "status" => false,
                "refund" => 0
            ];
        }

        $result = json_decode($result,true);
        if(!isset($result['status']) || empty($result['status'])){
            $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong.", "data" => null];
            return $returnData;
        }

        if(strtolower($result['status']) != true){
            $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong.", "data" => null];
            return $returnData;
        }

        switch ($type) { // different api give different style for of result
            // case 'auto_login':
            //     if(!isset($result['result']) || empty($result['result'])){
            //         $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong. Result Missing.", "data" => null];
            //         return $returnData;
            //     }

            //     if(!isset($result['result']['login_link']) || empty($result['result']['login_link'])){
            //         $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong. Log In Link Missing.", "data" => null];
            //         return $returnData;
            //     }

            //     $returnData = [
            //         'status' => true, 
            //         "data" => $result['result']['login_link'],
            //     ];
            //     break;

            case 'check_balance':
                if(!isset($result['data']) || empty($result['data'])){
                    $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong. Result Missing.", "data" => null];
                    return $returnData;
                }

                $returnData = [
                    'status' => true, 
                    "data" => $result['data'],
                ];
                break;
            // case 'deposit': 
            //     if(!isset($result['result']) || empty($result['result'])){
            //         $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong. Result Missing.", "data" => null];
            //         return $returnData;
            //     }

            //     $returnData = [
            //         'status' => true, 
            //         "data" => $result['result'],
            //     ];
            //     break;

            // case 'withdraw': 
            //     if(!isset($result['result']) || empty($result['result'])){
            //         $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong. Result Missing.", "data" => null];
            //         return $returnData;
            //     }

            //     if(!isset($result['result']['witdraw_amount']) || $result['result']['witdraw_amount'] <= 0){
            //         $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong. Invalid withdraw amount.", "data" => null];
            //         return $returnData;
            //     }

            //     $returnData = [
            //         'status' => true, 
            //         "data" => $result['result'],
            //     ];
            //     break;

            // case 'get_player_gamelog': 
            //     if(!isset($result['data']) || empty($result['data'])){
            //         $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong. Result Missing.", "data" => null];
            //         return $returnData;
            //     }

            //     $returnData = [
            //         'status' => true, 
            //         "data" => $result['data'],
            //     ];
            //     break;
            
            case 'register':
                if(isset($params['user_id'])) {
                    $user = Models\User::find($params['user_id']);

                    if (empty($user)) {
                        $returnData = [
                            'status' => true, 
                            "data" => $result['data'],
                        ];
                        return $returnData;
                    }
                    $user->update(['deeplink' => $result['data']['deeplink'] ?? null]);
                }

                $returnData = [
                    'status' => true, 
                    "data" => $result['data'],
                ];

            case 'balance-promoid':
                if(!isset($result['data']) || empty($result['data'])){
                    $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong. Result Missing.", "data" => null];
                    return $returnData;
                }

                $returnData = [
                    'status' => true, 
                    "data" => $result['data'],
                ];
                break;
            default:
                $returnData = ['status' => true, "data" => null];
                break;
        }

        return $returnData;
    }

    public static function getEndUrl($type = null){
        switch ($type) {
            case 'check_balance':
                return 'balance';
            case 'register';
                return 'account';
        }
        return $type;
    }
}
