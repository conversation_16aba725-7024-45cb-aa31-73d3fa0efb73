<?php

namespace App\Traits;

use AlibabaCloud\SDK\Dysmsapi\V20170525\Dysmsapi;
use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Dysmsapi\V20170525\Models\SendSmsRequest;
use App\Traits\GenerateNumberTrait;
use App\Models\SMSLog;
use App\Models\SystemSetting;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;

class Sms {
    protected $providers = [
        "malaysia" => [
            // '1' => 'gosms',
            '1' => 'yunpian',
            
        ],
        "default" => [
            '1' => 'yunpian',
            // '1' => 'gosms',
        ],
    ];

    public static $smsType = [
        'register-otp' => 1,
        'login-otp' => 2,
        'change-phone-otp' => 3,
        'email-verify-otp' => 4,
        'register-success' => 5,
        'forgot-password-otp' => 6,
        // 'reset-password-otp' =>73,
        'reset-transaction-password-otp' => 8,
        // 'forgot-username' => 9,
        'change-transaction-password-otp' => 10,
    ];

    protected static function smsEnv()
    {
        $systemSetting = SystemSetting::where('name','smsEnvironment')->first()['value'] ?? 'production';
        return $systemSetting;
    }

    protected static $name = 'FW';

    protected static function yunpianTemplate($type)
    {
        $smsTemplate = [
            "login-otp" => [
                "en" => [
                    "tpl_id" => 6017428,
                    "template" => "[".self::$name."] Your verification code is #code#. Do not share it with anyone."
                ],
                "cn" => [
                    "tpl_id" => 6017430,
                    "template" => "[".self::$name."] 您的验证码为 #code#。请勿将简讯转发或告知他人。"
                ]
            ],
            "register-otp" => [
                "en" => [
                    "tpl_id" => 6017428,
                    "template" => "[".self::$name."] Your verification code is #code#. Do not share it with anyone."
                ],
                "cn" => [
                    "tpl_id" => 6017430,
                    "template" => "[".self::$name."] 您的验证码为 #code#。请勿将简讯转发或告知他人。"
                ]
            ],
            "default" => [
                "en" => [
                    "tpl_id" => 6017428,
                    "template" => "[".self::$name."] Your verification code is #code#. Do not share it with anyone."
                ],
                "cn" => [
                    "tpl_id" => 6017430,
                    "template" => "[".self::$name."] 您的验证码为 #code#。请勿将简讯转发或告知他人。"
                ]
            ],
        ];
        return $smsTemplate[$type] ?? $smsTemplate['default'];
    }

    protected static function smsTemplate()
    {
        $smsTemplate = [
            'register-otp' => [
                'en' => "[".self::$name."] Your OTP code for account registration is #code#. This OTP is valid for #minute# minutes only."
            ],
            'forgot-password-otp' => [
                'en' => "[".self::$name."] Your verification code is #code#. Do not share it with anyone.",
                'cn' => "[".self::$name."] 您的验证码为 #code#。请勿将简讯转发或告知他人。"
            ],
            'reset-password-otp' => [
                'en' => "[".self::$name."] Your OTP code for password reset request for #username# is #code#. Do not share it with anyone."
            ],
            'reset-transaction-password-otp' => [
                'en' => "[".self::$name."] Your OTP code for #username# is #code#. This OTP is valid for #minute# minutes only.",
                'cn' => "[".self::$name."] 您的验证码为 #code#。请勿将简讯转发或告知他人。"
            ],
            'change-transaction-password-otp' => [
                'en' => "[".self::$name."] Your OTP code for #username# is #code#. This OTP is valid for #minute# minutes only."
            ],
            'forgot-username' => [
                'en' => "[".self::$name."] Your forgotten is #username#. Log in now: #link#"
            ],
            'change-phone-otp' => [
                'en' => "[".self::$name."] Your OTP code for #username# is #code#. This OTP is valid for #minute# minutes only."
            ],
            'first-login-otp' => [
                'en' => "[".self::$name."] Your OTP code for #username# is #code#. This OTP is valid for #minute# minutes only."
            ],
            'register-success' => [
                'en' => "[".self::$name."] Thank you for choosing us. Your account has been successfully registered. We're excited to have you onboard and look forward to providing you with an amazing experience."
            ],
            'login-otp' => [
                // 'en' => "[".self::$name."] This code will let you log in to your fw account, so don't share it with anyone, even a fw employee. Your code is: #code#."
                'en' => "[" . self::$name . "] Your verification code is #code#. Do not share it with anyone."
            ],
        ];

        return $smsTemplate;
    }

    protected function curlPost($params = []) {
        if (isset($params['method']) && $params['method'] == 'GET') {
            $params['url'] .= '?' . http_build_query($params['data']);
        }
        if (isset($params['paramsType']) && $params['paramsType'] == 'json') {
            $q = json_encode($params['data']);
        } else {
            $q = http_build_query($params['data']);
        }

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $params['url']);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, isset($params['method']) ? $params['method'] : 'POST');
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_MAXREDIRS, 10);
        curl_setopt($curl, CURLOPT_TIMEOUT, 10);
        curl_setopt($curl, CURLOPT_VERBOSE, false);
        if (!isset($params['method']) || $params['method'] != 'GET') {
            curl_setopt($curl, CURLOPT_POSTFIELDS, $q);
            curl_setopt($curl, CURLOPT_HTTPHEADER, $params['header'] ?? []);
        }
        if (isset($params['auth']) && !empty($params['auth'])) {
            curl_setopt($curl, CURLOPT_USERPWD, $params['auth']['username'] . ":" . $params['auth']['password']);
        }
        if (isset($params['useragent']) && !empty($params['useragent'])) {
            curl_setopt($curl, CURLOPT_USERAGENT, $params['useragent']);
        }
        $response = curl_exec($curl);

        curl_close($curl);

        if(empty($response)){
            return [
                "status" => false,
                "code" => -1,
                "message" => "Unable to reach " . $params['url']
            ];
        }

        return [
            'status' => true,
            'data' => $response
        ];
    }

    protected function is_valid_json($raw_json) {
        return (json_decode($raw_json, true) == NULL) ? false : true;
    }

    protected function checkPrevRequest($params = []) {
        // Check last request for same user
        $smsLog = SMSLog::select('id','provider_request')
                ->where('type',$params['type'])
                ->where('phone_no',$params['phone'])
                ->orderBy('id','DESC')
                ->limit(1)
                ->get()
                ->toArray();

        // Never request before
        if(empty($smsLog)){
            return [
                'code' => 1
            ];
        }

        // Previously failed / never confirmed
        /*
        # Not used in this project
        if(empty($smsLog[0]['confirmed_at'])){
            return [
                'code' => 2,
                'provider_request' => json_decode($smsLog[0]['provider_request'],true)
            ];
        }
        */

        // Previously success
        return [
            'code' => 3,
            'provider_request' => json_decode($smsLog[0]['provider_request'],true)
        ];
    }

    protected function sequenceArrangement($params = []) {
        $countryCode = $params['countryCode'];
        $code = $params['prevRequest']['code'];
        $list = $params['prevRequest']['list'];

        // Used to set provider for specific country code, currently no need set in this project
        switch($countryCode){
            case 60: // Malaysia
                $sequence = $this->providers['malaysia'];
                break;

            /*
            case 84: // Vietnam
                $sequence = $this->providers['vietnam'];
                break;

            case 62: // Indonesia
                $sequence = $this->providers['indonesia'];
                break;

            case 86: // China
                $sequence = ['aliyun'];
                break;

            case 62: // Indonesia
                $sequence = ['twilio'];
                break;

            case 66: // Thailand
                $sequence = ['twilio'];
                break;

            case 84: // Vietnam
                $sequence = ['twilio'];
                break;
            */

            default:
                $sequence = $this->providers['default'];
                
                if($countryCode != '60'){
                    unset($sequence[array_search('gosms',$sequence)]);
                }
                break;
        }

        if (isset($params['bypass_provider'])) {
            $sequence = array_filter($sequence, function ($q) use ($params) {
                return !in_array($q, $params['bypass_provider']);
            });
        }
        // Never request before
        if($code == 1 || empty($list)){ 
            $newSequence = $sequence;
        }else{
            // Get last request
            $lastProvider = $list[count($list) - 1];

            // Rearrange sequence
            $sequenceBefore = [];
            $sequenceAfter = [];
            
            if(in_array($lastProvider['provider'],$sequence)){
                $pvdIndex = array_search($lastProvider['provider'], $sequence) - 1;
                if($pvdIndex != (count($sequence) - 1)) {
                    $sequenceAfter = array_slice($sequence, $pvdIndex + 1);
                }
                if($pvdIndex != 0){
                    $sequenceBefore = array_slice($sequence, 0, $pvdIndex);
                }

                $newSequence = array_merge($sequenceAfter,$sequenceBefore,[$lastProvider['provider']]);
            }else{
                $newSequence = $sequence;
            }
        }

        return $newSequence;
    }

    public function sendSms($params = []) {
        $providers = $this->providers;
        ksort($providers);

        $user_id = $params['user_id'] ?? null;
        $phone = $params['phone'];
        $phoneDetail = explode('-',$phone);
        $countryCode = $phoneDetail[0];
        $phoneNo = $phoneDetail[1];
        $lang = $params['lang'] ?? 'en';
        $type = self::$smsType[$params['type']];
        $text = self::smsTemplate()[$params['type']][$lang] ?? self::smsTemplate()[$params['type']]['en'];
        $otpCode = $params['otp_code'] ?? null;
        $username = null;
        if(isset($params['username']) && !empty($params['username'])){
            $username = $params['username'];
        }

        switch($params['type']){
            case 'change-transaction-password-otp':
            case 'reset-transaction-password-otp':
            case 'reset-password-otp':
            case 'forgot-password-otp':
            case 'change-phone-otp':
            case 'first-login-otp':
                $otpCode = isset($otpCode) ? $otpCode : GenerateNumberTrait::GenerateOTPCode();
                $otpValidTime = SystemSetting::where('name','otpValidTime')->first()->value ?? '10 minutes';
                $otpTime = (int) filter_var($otpValidTime,FILTER_SANITIZE_NUMBER_INT);
                $text = str_replace(['#username#','#code#','#minute#'],[$params['username'],$otpCode,$otpTime],$text);
                break;

            case 'login-otp':
            case 'register-otp':
                $otpCode = isset($otpCode) ? $otpCode : GenerateNumberTrait::GenerateOTPCode();
                $otpValidTime = SystemSetting::where('name','otpValidTime')->first()->value ?? '10 minutes';
                $otpTime = (int) filter_var($otpValidTime,FILTER_SANITIZE_NUMBER_INT);
                $text = str_replace(['#code#','#minute#'],[$otpCode,$otpTime],$text);
                break;

            case 'forgot-username':
                $otpValidTime = SystemSetting::where('name','otpValidTime')->first()->value ?? '10 minutes';
                $text = str_replace(['#username#','#link#'], [$params['username'],env('APP_URL_USER')], $text);
                break;

            case 'register-success':
                break;
            
            default:
                break;
        }

        // Check previous request
        $prevRequest = $this->checkPrevRequest([
            'phone' => preg_replace("/[^0-9]/","",$phone),
            'type' => $type
        ]);

        // Change sequence based on previous record
        $sequence = $this->sequenceArrangement([
            'countryCode' => $countryCode,
            'bypass_provider' => $params['bypass_provider'],
            'prevRequest' => [
                'code' => $prevRequest['code'],
                'list' => ($prevRequest['provider_request'] ?? [])
            ]
        ]);

        $requests = [];
        $status = false;

        foreach($sequence as $provider){
            $requestData = [
                'countryCode' => $countryCode,
                'phoneNo' => $phoneNo,
                'text' => $text,
            ];

            switch($provider){
                case 'yunpian':
                    if (!in_array($params['type'], ['register-otp','login-otp','reset-transaction-password-otp','change-transaction-password-otp','forgot-password-otp'])) break;
                    $templates = $this->yunpianTemplate($params['type']);
                    $template = $templates[$lang] ?? $templates['en'];
                    switch($params['type']) {
                        case 'register-otp':
                            $template = str_replace(["#code#", "#minute#"], [$otpCode, $otpTime], $template);
                            break;
                        case 'login-otp':
                            $template = str_replace(["#code#"], [$otpCode], $template);
                            break;
                        default:
                            $template = str_replace(['#username#','#code#','#minute#'],[$params['username'],$otpCode,$otpTime], $template);
                            break;
                    }
                    $requestData['text'] = $template['template'];
                    $requestData['tpl_id'] = $template['tpl_id'];
                    break;

                case 'gosms':
                    $requestData['otpCode'] = $otpCode ?? null;
                    $requestData['lang'] = $lang;
                    $requestData['type'] = $params['lang'] == 'cn' ? 'CM' : 'TX';
                    break;
                case 'twilio':
                case 'skyline':
                case 'nxcloud':
                    break;

                case 'nexmo':
                    $requestData['type'] = $params['lang'] == 'cn' ? 'unicode' : 'text';
                    break;                
                
                case 'sureceive':
                    unset($requestData['text']);
                    $requestData['otpCode'] = $otpCode ?? null;
                    $requestData['lang'] = $lang;
                    break;

                default:
                    break;
            }

            if ($provider == 'sureceive' && !isset($requestData['otpCode']) || ($provider == 'yunpian' && (!in_array($params['type'], ['register-otp', 'login-otp', 'reset-transaction-password-otp', 'change-transaction-password-otp','forgot-password-otp'])))) continue;
            $responseData = $this->$provider($requestData);

            $requests[] = [
                'provider' => $provider,
                'request_at' => date('Y-m-d H:i:s'),
                'request' => $requestData,
                'response' => $responseData,
                'otp_code' => (int) $otpCode,
                'user_id' => $user_id ?? null,
            ];

            if($responseData['status']){
                $status = true;
                $providerName = $provider;
                break;
            }
        }

        $dateTime = date('Y-m-d H:i:s');
        $expiredAt = date('Y-m-d H:i:s', strtotime(($dateTime).' + '.$otpValidTime));

        $data = [
            'user_id' => $user_id,
            'username' => $username,
            'country_code' => $countryCode,
            'type' => $type,
            'otp_code' => $otpCode ?? NUll,
            'phone_no' => preg_replace("/[^0-9]/","",$phone),
            'provider' => $providerName ?? 'N/A',
            'provider_request' => json_encode($requests),
            'expired_at' => $expiredAt,
            'created_at' => $dateTime,
        ];

        SMSLog::create($data);

        return true;
    }

    public static function sendOTPCode($params = []) {
        $dateTime = date('Y-m-d H:i:s');
        $phone_no = $params['phone_no'] ?? null;
        $otp_type = $params['otp_type'] ?? null;
        $user_id = $params['user_id'] ?? null;
        $username = $params['username'] ?? null;
        $lang = $params['lang'] ?? 'en';
        $isDashboardV2 = $params['isDashboardV2'] ?? false;

        $otp = SMSLog::query()
            ->where('type',Sms::$smsType[$otp_type])
            ->where('phone_no',preg_replace('/[^0-9]/','',$phone_no))
            ->where('expired_at','>=',$dateTime)
            ->when(isset($user_id), function($q) use($user_id){
                return $q->whereJsonContains('provider_request',['user_id' => (int)$user_id]);
            })
            ->first();

        $otpSetting = SystemSetting::whereIn('name',['otpValidTime', 'resendOtpValidTime', 'otpSpamLimit'])->get()->pluck('value', 'name');

        if(!empty($otp)) {
            $resendTime = date('Y-m-d H:i:s', strtotime($otp->created_at . " + " . ($otpSetting['resendOtpValidTime'] ?? '1 minutes')));
            $expiredTime = date('Y-m-d H:i:s', strtotime($otp->expired_at));
            if($dateTime <= $resendTime){
                $interval = (strtotime($resendTime) - strtotime($dateTime));
                $minRemain = floor(DecimalTrait::setDecimal(($interval/60)));
                $secRemain = ($interval % 60);

                $validInterval = (strtotime($expiredTime) - strtotime($dateTime));
                $validMinRemain = floor(DecimalTrait::setDecimal(($validInterval / 60)));
                $validSecRemain = ($interval % 60);
    
                if($otp_type == 'forgot-username'){
                    abort(400, json_encode(["otp-code" => [Lang::get('lang.username-requested', ['minutes' => $minRemain, 'seconds' => $secRemain])], "resendOtpValidTime" => [($minRemain * 60 + $secRemain)], "otpValidTime" => [($validMinRemain * 60 + $validSecRemain)]]));
                }else if ($otp_type == 'login-otp' || $otp_type == 'register-otp') {
                    abort(200, json_encode(["data" => ["phone_no" =>  $phone_no, "resendOtpValidTime" => ($minRemain * 60 + $secRemain), "otpValidTime" => ($validMinRemain * 60 + $validSecRemain)]]));
                }else{
                    abort(400, json_encode (["otp-code" => [Lang::get('lang.otp-requested', ['minutes' => $minRemain,'seconds' => $secRemain])], "resendOtpValidTime" => [($minRemain * 60 + $secRemain)], "otpValidTime" => [($validMinRemain * 60 + $validSecRemain)]]));
                }
            }
        }

        if(env('APP_ENV') == self::smsEnv() && $isDashboardV2 == false){
            $smsParams = [
                'phone' => $phone_no,
                'lang' => $lang,
                'type' => $otp_type,
                'user_id' => $user_id,
                'username' => $username,
                'bypass_provider' => $params['bypass_provider'] ?? [],
                'otp_code' => $params['otp_code'] ?? null,
            ];
            $smsParams = json_encode($smsParams);
            $job = new \App\Jobs\SendSMS($smsParams);
            dispatch($job);
        }

        $otpValidTime = $otpSetting['otpValidTime'] ?? '10 minutes';
        $otpValidTime = (int) filter_var($otpValidTime,FILTER_SANITIZE_NUMBER_INT);
        $resendOtpValidTime = $otpSetting['resendOtpValidTime'] ?? '10 minutes';
        $resendOtpValidTime = (int) filter_var($resendOtpValidTime, FILTER_SANITIZE_NUMBER_INT);
        switch($otp_type) {
            case 'forgot-username':
                $msg = 'lang.username-successfully-send';
                break;
            default:
                $msg = 'lang.resend-otp-success';
                break;
        }
        $data = [
            'message' => Lang::get($msg),
            'data' => [
                'resendOtpValidTime' => ($resendOtpValidTime * 60 + 1),
                'otpValidTime' => ($otpValidTime * 60 + 1),
                'phone_no' => $phone_no,
            ],
        ];
        abort(200, json_encode($data));
    }

    public static function verifyOTPCode($params = []) {
        $dateTime = date('Y-m-d H:i:s');
        $type = self::$smsType[$params['type']] ?? Null;
        $phone = isset($params['phone']) ? preg_replace('/[^0-9]/','',$params['phone']) : null;
        $otpCode = $params['otp_code'] ?? null;
        $user_id = $params['user_id'] ?? null;

        if(!$type || !$otpCode){
            return false;
        }

        if(env('APP_ENV') != 'production' && $otpCode == 888888){
            return true;
        }

        $verifyOTPCode = SMSLog::query()
            ->where('type',$type)
            ->where('expired_at','>=',$dateTime)
            ->whereJsonContains('provider_request',['response' => ['status' => true],'otp_code' => (int)$otpCode])
            ->when(isset($phone), function ($q) use ($phone){
                return $q->where('phone_no',$phone);
            })
            ->when(isset($user_id), function($q) use($user_id){
                return $q->whereJsonContains('provider_request',['user_id' => (int)$user_id]);
            })
            ->first();

        if(!$verifyOTPCode){
            return false;
        }

        return $verifyOTPCode;
    }

    protected function nxcloud($params = [])
    {
        // Dummy response from sms provider for non production
        if (getenv('APP_ENV') != self::smsEnv()) {
        // if(getenv('APP_ENV') == 'local'){
            return [
                'status' => true,
                'data' => []
            ];
        }

        $res = $this->curlPost([
            'url' => getenv('SMS_NXCLOUD_URL'),
            'data' => [
                'content' => $params['text'],
                'phone' => $params['countryCode'] . $params['phoneNo'],
                'appkey' => getenv('SMS_NXCLOUD_USERNAME'),
                'secretkey' => getenv('SMS_NXCLOUD_PASSWORD'),
            ]
        ]);

        if (!$res['status']) {
            return $res;
        }

        if (!$this->is_valid_json($res['data'])) {
            return [
                'status' => false,
                'message' => 'Not valid json',
                'data' => json_encode($res['data'])
            ];
        }

        $resData = json_decode($res['data'], true);

        if (!isset($resData['code']) || $resData['code'] != 0) {
            return [
                'status' => false,
                'message' => 'Code not success',
                'data' => $res['data']
            ];
        }

        return $res;
    }

    // Need whitelist IP
    protected function yunpian($params = []) {
        // Dummy response from sms provider for non production
        if(getenv('APP_ENV') != self::smsEnv()){
            return [
                'status' => true,
                'data' => []
            ];
        }

        $res = $this->curlPost([
            'url' => getenv('SMS_YUNPIAN_URL'),
            'header' => ['Content-Type: application/x-www-form-urlencoded;charset=utf-8;', 'Accept: application/json;charset=utf-8;'],
            'data' => [
                'tpl_id' => $params['tpl_id'],
                'apikey' => getenv('SMS_YUNPIAN_API_KEY'),
                'mobile' => '+' . $params['countryCode'] . $params['phoneNo'],
                'text' => $params['text'],
            ]
        ]);

        if(!$res['status']){
            return $res;
        }

        if(!$this->is_valid_json($res['data'])){
            return [
                'status' => false,
                'message' => 'Not valid json',
                'data' => json_encode($res['data'])
            ];
        }

        $resData = json_decode($res['data'],true);

        $code = $resData['code'] ?? false;

        if($code !== 0){
            return [
                'status' => false,
                'message' => 'Code not success',
                'data' => $res['data']
            ];
        }

        return $res;
    }

    protected function gosms($params = []) {
        // Dummy response from sms provider for non production
        if(getenv('APP_ENV') != self::smsEnv()){
        // if(getenv('APP_ENV') == 'local'){
            return [
                'status' => true,
                'data' => []
            ];
        }

        $data = [
            'url' => getenv('SMS_GOSMS_URL'),
            // 'header' => ['Content-Type: application/x-www-form-urlencoded;charset=utf-8;', 'Accept: application/json;charset=utf-8;'],
            'useragent' => 'Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1)',
            'method' => 'GET',
            'data' => [
                'company' => getenv('SMS_GOSMS_COMPANY_LOGIN'),
                'user' => getenv('SMS_GOSMS_USER_ID'),
                'password' => base64_encode(getenv('SMS_GO_SMS_PASSWORD')),
                'gateway' => 'L',
                'mode' => 'BUK',
                'type' => $params['type'],
                'hp' => $params['countryCode'].$params['phoneNo'],
                'mesg' => str_replace(']',')',str_replace('[','(',$params['text'])),
                'charge' => 0,
                'convert' => ($params['type'] == 'CM' ? 1 : 0),
            ]
        ];
        $res = $this->curlPost($data);

        if(!$res['status']){
            return $res;
        }

        if(!$this->is_valid_json($res['data'])){
            return [
                'status' => false,
                'message' => 'Not valid json',
                'data' => json_encode($res['data'])
            ];
        }

        $resData = json_decode($res['data'],true);

        if(empty($resData) || $resData != 1){
            return [
                'status' => false,
                'message' => 'Code not success',
                'data' => $res['data']
            ];
        }

        return $res;
    }

    protected function twilio($params = []) {
        // Dummy response from sms provider for non production
        if(getenv('APP_ENV') != self::smsEnv()){
            return [
                'status' => true,
                'data' => []
            ];
        }

        $res = $this->curlPost([
            'url' => getenv('SMS_TWILIO_URL'),
            'header' => ['Content-Type: application/x-www-form-urlencoded;charset=utf-8;', 'Accept: application/json;charset=utf-8;'],
            'auth' => [
                'username' => getenv('SMS_TWILIO_USERNAME'),
                'password' => getenv('SMS_TWILIO_PASSWORD')
            ],
            'data' => [
                'Body' => $params['text'],
                'To' => '+' . $params['countryCode'] . $params['phoneNo'],
                'MessagingServiceSid' => getenv('SMS_TWILIO_MSG_SVC_ID')
            ]
        ]);

        if(!$res['status']){
            return $res;
        }

        if(!$this->is_valid_json($res['data'])){
            return [
                'status' => false,
                'message' => 'Not valid json',
                'data' => json_encode($res['data'])
            ];
        }

        $resData = json_decode($res['data'],true);

        if(!isset($resData['status']) || $resData['status'] != 'accepted'){
            return [
                'status' => false,
                'message' => 'Code not success',
                'data' => $res['data']
            ];
        }

        return $res;
    }

    protected function skyline($params = []) {
        // Dummy response from sms provider for non production
        if(getenv('APP_ENV') != self::smsEnv()){
        // if(getenv('APP_ENV') == 'local'){
            return [
                'status' => true,
                'data' => []
            ];
        }

        // Get server time
        // $serverTime = new \DateTime();

        // // Convert to UTC time
        // $utcTime = $serverTime->setTimezone(new \DateTimeZone('UTC'));

        // // Add 8 hours to UTC time
        // $MyTime = $utcTime->add(new \DateInterval('PT8H')); // Add 8 hours from UTC

        // // Convert to str format
        // $timeString = date_format($MyTime, 'YmdHis');
        // $timeString = strtotime($timeString);
        $timeString = time();

        // Generate sign
        $encodedSign = md5( getenv('SMS_SKYLINE_USERNAME') . getenv('SMS_SKYLINE_PASSWORD') . $timeString);
        $res = $this->curlPost([
            'url' => getenv('SMS_SKYLINE_URL'),
            'paramsType' => 'json',
            'header' => [
                        'Content-Type: application/json;charset=UTF-8',
                        'sign: '.$encodedSign,
                        'Timestamp: '.$timeString,
                        'Api-Key: '.getenv('SMS_SKYLINE_USERNAME')
                    ],
            'data' => [
                'content' => $params['text'],
                'numbers' => $params['countryCode'] . $params['phoneNo'],
                'appId' => getenv('SMS_SKYLINE_USERNAME'),

            ]
        ]);

        if(!$res['status']){
            return $res;
        }

        if(!$this->is_valid_json($res['data'])){
            return [
                'status' => false,
                'message' => 'Not valid json',
                'data' => json_encode($res['data'])
            ];
        }

        $resData = json_decode($res['data'],true);

        if(!isset($resData['success']) || $resData['success'] != 1){
            return [
                'status' => false,
                'message' => 'Code not success',
                'data' => $res['data']
            ];
        }

        $res['url'] = getenv('SMS_SKYLINE_URL') . '?account=' . getenv('SMS_SKYLINE_USERNAME') . '&sign=' . $encodedSign . '&datetime=' . $timeString;

        return $res;
    }

    protected function nexmo($params = []) {
        // Dummy response from sms provider for non production
        if(getenv('APP_ENV') != self::smsEnv()){
        // if(getenv('APP_ENV') == 'local'){
            return [
                'status' => true,
                'data' => []
            ];
        }

        $res = $this->curlPost([
            'url' => getenv('SMS_NEXMO_URL'),
            'header' => ['Content-Type: application/x-www-form-urlencoded;charset=utf-8;', 'Accept: application/json;charset=utf-8;'],
            'data' => [
                'text' => $params['text'],
                'from' => getenv('SMS_NEXMO_SIGNATURE'),
                'to' => '+' . $params['countryCode'] . $params['phoneNo'],
                'type' => $params['type']
            ]
        ]);

        if(!$res['status']){
            return $res;
        }

        if(!$this->is_valid_json($res['data'])){
            return [
                'status' => false,
                'message' => 'Not valid json',
                'data' => json_encode($res['data'])
            ];
        }

        $resData = json_decode($res['data'],true);

        if(!isset($resData['messages']) || !isset($resData['messages'][0]) || !isset($resData['messages'][0]['status']) || intval($resData['messages'][0]['status']) > 0){
            return [
                'status' => false,
                'message' => 'Code not success',
                'data' => $res['data']
            ];
        }

        return $res;
    }

    # Not required for this project

    // Aliyun otp sms
    protected function aliyun($params = []) {
        //dummy response from sms provider for non production
       if (getenv('APP_ENV') != self::smsEnv()) {
           return [
               'status' => true,
               'data' => []
           ];
       }

        $config = new Config([
            // 'type' => 'access_key',
            // 您的AccessKey ID
            "accessKeyId" => getenv('SMS_ALIYUN_ACCESS_KEY_ID'),
            // 您的AccessKey Secret
            "accessKeySecret" => getenv('SMS_ALIYUN_ACCESS_KEY_SECRET')
        ]);
        // 访问的域名
        $config->endpoint = "dysmsapi.aliyuncs.com";

        $client = new Dysmsapi($config);
        $sendSmsRequest = new SendSmsRequest([
            "phoneNumbers" => str_replace("-", "", $params['phoneno']),
            "signName" => getenv('SMS_ALIYUN_SIGN_NAME'),
            "templateCode" => getenv('SMS_ALIYUN_TEMPLATE_CODE'),
            "templateParam" => "{\"code\": \"" . $params['otp'] . "\"}"
        ]);
        // 复制代码运行请自行打印 API 的返回值
        $res = $client->sendSms($sendSmsRequest);
        $res = $res->toMap();

        if ($res['body']['Code'] == "OK") {
            return [
                'status' => true,
                'data' => $res['body']
            ];
        } else {
            return [
                'status' => false,
                'message' => 'Code not success',
                'data' => $res['body']
            ];
        }
    }

    protected function mailbit($params = []) {
        //dummy response from sms provider for non production
        if (getenv('APP_ENV') != self::smsEnv()) {
            return [
                'status' => true,
                'data' => []
            ];
        }

        $res = $this->curlPost([
            'url' => OTPCREDS['mailbit']['url'],
            'header' => ['Content-Type: application/x-www-form-urlencoded;charset=utf-8;', 'Accept: application/json;charset=utf-8;'],
            'data' => [
                'username' => OTPCREDS['mailbit']['username'],
                'password' => OTPCREDS['mailbit']['password'],
                'ani' => OTPCREDS['mailbit']['signature'],
                'dnis' => '+' . $params['countrycode'] . $params['phoneno'],
                'message' => $params['text'],
                'command' => 'submit',
                'silent' => 0,
                'longMessageMode' => 'payload',
            ]
        ]);

        if (!$res['status']) {
            return $res;
        }

        if (!$this->is_valid_json($res['data'])) {
            return [
                'status' => false,
                'message' => 'Not valid json',
                'data' => json_encode($res['data'])
            ];
        }

        $resData = json_decode($res['data'], true);

        if (!isset($resData['message_id']) || empty($resData['message_id'])) {
            return [
                'status' => false,
                'message' => 'Code not success',
                'data' => $res['data']
            ];
        }

        return $res;
    }

    protected function clickatell($params = []) {
        //dummy response from sms provider for non production
        if (getenv('APP_ENV') != self::smsEnv()) {
            return [
                'status' => true,
                'data' => []
            ];
        }

        $res = $this->curlPost([
            'url' => OTPCREDS['clickatell']['url'],
            'method' => 'GET',
            'data' => [
                'content' => $params['text'],
                'to' => $params['countrycode'] . $params['phoneno'],
                'apiKey' => OTPCREDS['clickatell']['apiKey']
            ]
        ]);

        if (!$res['status']) {
            return $res;
        }

        if (!$this->is_valid_json($res['data'])) {
            return [
                'status' => false,
                'message' => 'Not valid json',
                'data' => json_encode($res['data'])
            ];
        }

        $resData = json_decode($res['data'], true);

        if (!isset($resData['messages']) || empty($resData['messages']) || !$resData['messages'][0]['accepted']) {
            return [
                'status' => false,
                'message' => 'Code not success',
                'data' => $res['data']
            ];
        }

        return $res;
    }

    protected function elfo($params = []) {
        //dummy response from sms provider for non production
        if (getenv('APP_ENV') != self::smsEnv()) {
            return [
                'status' => true,
                'data' => []
            ];
        }

        $res = $this->curlPost([
            'url' => getenv('SMS_ELFO_URL'),
            'header' => [
                'Content-Type: application/x-www-form-urlencoded;charset=utf-8;',
                'Accept: application/json;charset=utf-8;',
                'Authorization: ' . getenv('SMS_ELFO_KEY')
            ],
            'paramsType' => 'json',
            'data' => [
                'message' => $params['text'],
                'msisdn' => '+' . $params['countrycode'] . $params['phoneno']
            ]
        ]);
        if (!$res['status']) {
            return $res;
        }

        if (!$this->is_valid_json($res['data'])) {
            return [
                'status' => false,
                'message' => 'Not valid json',
                'data' => json_encode($res['data'])
            ];
        }

        $resData = json_decode($res['data'], true);

        // Create yuanpian message record regardless of failed or success (Old wewe logic)
        YunpianMessages::insert([
            'contact_id' => $params['contact_id'],
            'phone_no' => '+' . $params['countrycode'] . $params['phoneno'],
            'body' => $params['text'],
            'code' => $resData["code"],
            'msg' => $resData["message"],
            'result' => $res
        ]);

        if (!isset($resData['status_code']) || $resData['status_code'] != '200') {
            return [
                'status' => false,
                'message' => 'Code not success',
                'data' => $res['data']
            ];
        }

        return $res;
    }

    protected function sureceive($params = [])
    {
        // Dummy response from sms provider for non production
        if (getenv('APP_ENV') != self::smsEnv()) {
            // if(getenv('APP_ENV') == 'local'){
            return [
                'status' => true,
                'data' => []
            ];
        }

        $data = [
            'url' => getenv('SMS_SURECEIVE_URL'),
            'header' => ['api-key:' . getenv('SMS_SURECEIVE_API_KEY')],
            'method' => 'POST',
            'data' => [
                'phone_no' => '+' . $params['countryCode'] . $params['phoneNo'],
                'otp' => $params['otpCode'],
                'lang' => $params['lang'] ?? 'en',
            ]
        ];
        $res = $this->curlPost($data);

        $error = json_decode($res['data'], true);
        if (!$res['status'] || isset($error['error'])) {
            return [
                'status' => false,
                'message' => 'Code not success',
                'data' => $res['data'],
            ];
        }

        return $res;
    }
}
