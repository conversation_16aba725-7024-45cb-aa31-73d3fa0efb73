<?php

namespace App\Traits;

use App\Models\CurlLog;
use App\Models\TpWebHook;

trait CurlTrait
{
    public static function doCurl($params = [], $curlType = 'normal', $webhookParam = null)
    {
        // Create a unique hash based on the URL and data
        if (isset($params['endpoint']) && $params['endpoint'] == 'https://api.luckydonutshop.com/api_ext/withdraw' && isset($params['body'])) {
            $requestHash = md5($params['endpoint'] . $params['body']['account'] . $params['body']['millis'] . $params['body']['secret']);
            $cacheKey = 'curl_request_' . $requestHash;

            // Check if this request has been recently processed
            if (isset($_SESSION[$cacheKey])) {
                // Recent duplicate found; you can return or handle as needed
                CurlLog::create([
                    'endpoint' => $params['endpoint'] ?? '',
                    'request' => isset($params['body']) ? (is_array($params['body']) ? json_encode($params['body']) : $params['body']) : '',
                    'response' => null,
                    'created_at' => now(),
                ]);

                throw new \Exception('Duplicate request detected');
            }
        }

        //step 1: init curl
        $curl = curl_init();

        //step 2: prepare curl option
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, isset($params['header']) ? $params['header'] : []);

        if (isset($params['username']) && isset($params['password'])) {
            curl_setopt($curl, CURLOPT_USERPWD, $params['username'] . ":" . $params['password']);
        }

        if (isset($params['method']) && $params['method'] == 'DELETE') {
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'DELETE');
        } elseif (isset($params['method']) && in_array($params['method'], ['PUT', 'PATCH'])) {
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'PUT');
            curl_setopt($curl, CURLOPT_POSTFIELDS, (isset($params['body']) && ! empty($params['body'])) ? $params['body'] : []);
        } elseif (isset($params['method']) && in_array($params['method'], ['GET', 'POST'])) {
            curl_setopt($curl, CURLOPT_TIMEOUT, 60);
            curl_setopt($curl, CURLOPT_MAXREDIRS, 3);
            curl_setopt($curl, CURLOPT_HEADER, false);
            curl_setopt($curl, CURLOPT_VERBOSE, false);

            curl_setopt($curl, CURLOPT_POST, $params['method'] == 'POST' ? true : false);

            if ($params['method'] == 'POST') {
                curl_setopt($curl, CURLOPT_POSTFIELDS, (isset($params['body']) && ! empty($params['body'])) ? $params['body'] : []);
            } else { //handle query for GET method
                $params['endpoint'] .= (isset($params['body']) && ! empty($params['body'])) ? '?' . http_build_query(json_decode($params['body'], true)) : '';
            }
        } else {
            throw new \Exception('Curl Method Not Recognized.');
        }
        curl_setopt($curl, CURLOPT_URL, $params['endpoint']);
        //step 3: curl
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        switch ($curlType) {
            case 'postOC-tk8':
            case 'postOC-tk8th':
            case 'postOC-fmt':
            case 'postOC-bo42':
            case 'postOC-711lot':
                // insert table
                CurlLog::create([
                    'endpoint' => $params['endpoint'] ?? '',
                    'request' => isset($params['body']) ? (is_array($params['body']) ? json_encode($params['body']) : $params['body']) : '',
                    'response' => $response ?? '',
                    'created_at' => now(),
                ]);

                if (isset($webhookParam['type']) && in_array(
                    $webhookParam['type'],
                    // array_keys(TpWebHook::$type))){
                    ['callback/deposit', 'callback/withdraw', 'balance-promoid']
                )) {

                    $curlTypeAry = explode('-', $curlType);

                    switch ($webhookParam['type']) {
                        case 'callback/deposit':
                        case 'balance-promoid':
                            $tableType = TpWebHook::$tableType['deposit'];
                            $tableId = $webhookParam['deposit_id'] ?? null;
                            break;
                        case 'callback/withdraw':
                            $tableType = TpWebHook::$tableType['withdraw'];
                            $tableId = $webhookParam['withdraw_id'] ?? null;
                            break;
                    }

                    TpWebHook::create([
                        'table_type' => $tableType,
                        'table_id' => $tableId,
                        'type' => TpWebHook::$type[($webhookParam['type'] ?? '')] ?? '',
                        'platform' => $curlTypeAry[1] ?? '',
                        'endpoint' => $params['endpoint'] ?? '',
                        'params' => json_encode(($webhookParam['params'] ?? '')),
                        'req_data' => isset($params['body']) ? (is_array($params['body']) ? json_encode($params['body']) : $params['body']) : '',
                        'res_data' => $response ?? '',
                        'created_at' => now(),
                    ]);
                }
                break;
            default:
                CurlLog::create([
                    'endpoint' => $params['endpoint'],
                    'request' => isset($params['body']) ? (is_array($params['body']) ? json_encode($params['body']) : $params['body']) : '',
                    'response' => $response,
                    'created_at' => now(),
                ]);
                break;
        }

        //checking
        if ($curlType == 'post-sonic') {
            if (empty($response)) {
                throw new \Exception('Unable to reach API server.');
            } else if ($httpCode != 200) {
                throw new \Exception($response);
            }
        } else {
            if ((empty($response) || ! self::is_valid_json($response)) && $httpCode != 200) {
                throw new \Exception('Unable to reach API server.');
            }
        }

        if ($params['endpoint'] == 'https://api.luckydonutshop.com/api_ext/withdraw') {
            // Store the hash for a limited time, e.g., 10 seconds
            $_SESSION[$cacheKey] = time();
            $_SESSION = array_filter($_SESSION, function ($time) {
                return time() - $time < 10; // 10-second cache duration
            });
        }

        //step 4: return
        return $response;
    }

    protected static function is_valid_json($raw_json): bool
    {
        return (json_decode($raw_json, true) == null) ? false : true;
    }
}
