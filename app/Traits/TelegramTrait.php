<?php

namespace App\Traits;

trait TelegramTrait
{
    protected static $decimal = 0;

    public static function sendTelegram($endpoint, $eventDescription)
    {
        $endpoint = isset($endpoint) ? $endpoint : env('TELEGRAM_BOT_URL') . "/telegram/send-message";
        $eventDescription = isset($eventDescription) ? $eventDescription : null;
        $retryTimes = 1;

        while ($retryTimes <= 5) {
            $res = CurlTrait::doCurl([
                'method' => 'POST',
                'endpoint' => $endpoint,
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'body' => [
                    'api_key' => $apiKey ?? env('TELEGRAM_BOT_API_KEY'),
                    'content' => $eventDescription,
                ],
            ]);

            $decodeRes = json_decode($res, true);
            $retryTimes++;
            if (isset($decodeRes['data']['message_id'])) break;
        }
    }

    public static function send($channelId, $eventDescription)
    {
        $botApiToken = '7114426385:AAEO5E2wekm6jE7ZGjkEyB_6n76C0EpTKiA';
        $query = http_build_query([
            'chat_id' => $channelId,
            'text' => $eventDescription
        ]);

        $url = "https://api.telegram.org/bot$botApiToken/sendMessage?$query";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $query);
        curl_exec($ch);
        curl_close($ch);
    }

    public static function setDecimal($amount)
    {
        if (!$amount) {
            $amount = 0;
        }

        $decimal = self::$decimal;

        $amount = explode('.', $amount);
        $amount = $amount[0];

        return number_format($amount, $decimal, '.', ',');
    }

    public static function setDecimalDisplay($amount, $decimal = null, $cutOff = null)
    {
        if (!isset($decimal)) {
            $decimal = config('decimal.calculation') ?? 8;
        }
        if (is_null($cutOff)) {
            $cutOff = config('decimal.cut_off') ?? false;
        }
        if (($decimal >= 8) || ($cutOff == true)) {
            $floor = pow(10, $decimal); // floor for extra decimal
            $convertedAmount = number_format((floor(strval($amount * $floor)) / $floor), $decimal, '.', '');
        } else {
            $convertedAmount = number_format($amount, $decimal, '.', '');
        }
        return number_format($convertedAmount, $decimal, '.', ',');
    }
}
