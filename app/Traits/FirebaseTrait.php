<?php

namespace App\Traits;

use App\Enums\Firebase\Channel;
use App\Models\NotificationDetail;
use App\Models\SystemSetting;
use App\Models\User;
use App\Models\UserNotification;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Lang;
use Kreait\Firebase\Messaging\AndroidConfig;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;
use Kreait\Laravel\Firebase\Facades\Firebase;
use Soundasleep\Html2Text;

trait FirebaseTrait
{
    public static function template($type, $lang = 'en', $notification = null)
    {
        App::setLocale($lang);

        $customNotification = null;
        if (isset($notification)) {
            $customNotification = self::getNotificationDetails($notification->id, $lang);
        }

        if ($type === 'custom' && ! isset($customNotification)) {
            abort(400, 'Notification not found.');
        }

        $template = [
            'sponsorBonus' => [
                'title' => Lang::has('lang.bonus-title') ? Lang::get('lang.bonus-title') : 'Yay! Incoming Money',
                'body' => Lang::has('lang.bonus-body') ? Lang::get('lang.bonus-body') : 'You have earned RM {{bonus_amount}}',
            ],
            'specialBonus' => [
                'title' => Lang::has('lang.bonus-title') ? Lang::get('lang.bonus-title') : 'Yay! Incoming Money',
                'body' => Lang::has('lang.bonus-body') ? Lang::get('lang.bonus-body') : 'You have earned RM {{bonus_amount}}',
            ],
            'vipBonus' => [
                'title' => Lang::has('lang.bonus-title') ? Lang::get('lang.bonus-title') : 'Yay! Incoming Money',
                'body' => Lang::has('lang.bonus-body') ? Lang::get('lang.bonus-body') : 'You have earned RM {{bonus_amount}}',
            ],
            'agentBonus' => [
                'title' => Lang::has('lang.bonus-title') ? Lang::get('lang.bonus-title') : 'Yay! Incoming Money',
                'body' => Lang::has('lang.bonus-body') ? Lang::get('lang.bonus-body') : 'You have earned RM {{bonus_amount}}',
            ],
            'ttransferProcessing' => [
                'title' => Lang::has('lang.ttransfer-processing-title') ? Lang::get('lang.ttransfer-processing-title') : 'On the way!',
                'body' => Lang::has('lang.ttransfer-processing-body') ? Lang::get('lang.ttransfer-processing-body') : 'Your order {{serial_no}} is processing.',
            ],
            'ttransferApproved' => [
                'title' => Lang::has('lang.ttransfer-approved-title') ? Lang::get('lang.ttransfer-approved-title') : 'Good news!',
                'body' => Lang::has('lang.ttransfer-approved-body') ? Lang::get('lang.ttransfer-approved-body') : 'Your order {{serial_no}} is approved & credited to the respective bank account. Contact support for help.',
            ],
            'ttransferRejected' => [
                'title' => Lang::has('lang.ttransfer-rejected-title') ? Lang::get('lang.ttransfer-rejected-title') : 'Oops!',
                'body' => Lang::has('lang.ttransfer-rejected-body') ? Lang::get('lang.ttransfer-rejected-body') : "Your Order {{serial_no}} couldn't be processed. Check your Send History for details.",
            ],
            'withdrawalProcessing' => [
                'title' => Lang::has('lang.withdrawal-processing-title') ? Lang::get('lang.withdrawal-processing-title') : 'On the way!!',
                'body' => Lang::has('lang.withdrawal-processing-body') ? Lang::get('lang.withdrawal-processing-body') : "Your withdrawal request ({{serial_no}}) is now being processed. We'll keep you updated on its status.",
            ],
            'withdrawalApproved' => [
                'title' => Lang::has('lang.withdrawal-approved-title') ? Lang::get('lang.withdrawal-approved-title') : 'Good news!',
                'body' => Lang::has('lang.withdrawal-approved-body') ? Lang::get('lang.withdrawal-approved-body') : 'Your withdrawal ({{serial_no}}) is approved & credited to your bank account. Contact support for help.',
            ],
            'withdrawalRejected' => [
                'title' => Lang::has('lang.withdrawal-rejected-title') ? Lang::get('lang.withdrawal-rejected-title') : 'Oh-no!',
                'body' => Lang::has('lang.withdrawal-rejected-body') ? Lang::get('lang.withdrawal-rejected-body') : 'We regret to inform you that your withdrawal request ({{serial_no}}) has been rejected. Please check the Withdrawal History for more details.',
            ],
            'rebateApproved' => [
                'title' => Lang::has('lang.rebate-approved-title') ? Lang::get('lang.rebate-approved-title') : 'Good news!',
                'body' => Lang::has('lang.rebate-approved-body') ? Lang::get('lang.rebate-approved-body') : 'Your rebate ({{serial_no}}) is approved & credited to your wallet. Contact support for help.',
            ],
            'rebateRejected' => [
                'title' => Lang::has('lang.rebate-rejected-title') ? Lang::get('lang.rebate-rejected-title') : 'Oh-no!',
                'body' => Lang::has('lang.rebate-rejected-body') ? Lang::get('lang.rebate-rejected-body') : 'We regret to inform you that your rebate request ({{serial_no}}) has been rejected. Please contact us for more details.',
            ],
            'onlineBankingDepositApproved' => [
                'title' => Lang::has('lang.online-banking-deposit-approved-title') ? Lang::get('lang.online-banking-deposit-approved-title') : 'Great news!',
                'body' => Lang::has('lang.online-banking-deposit-approved-body') ? Lang::get('lang.online-banking-deposit-approved-body') : 'Your online banking transaction of RM{{amount}} has been credited to your wallet.',
            ],
            'manualDepositApproved' => [
                'title' => Lang::has('lang.manual-deposit-approved-title') ? Lang::get('lang.manual-deposit-approved-title') : 'Great news!',
                'body' => Lang::has('lang.manual-deposit-approved-body') ? Lang::get('lang.manual-deposit-approved-body') : 'Your cash deposit of RM{{amount}} is approved and credited to your wallet.',
            ],
            'manualDepositRejected' => [
                'title' => Lang::has('lang.manual-deposit-rejected-title') ? Lang::get('lang.manual-deposit-rejected-title') : 'Oh-no!',
                'body' => Lang::has('lang.manual-deposit-rejected-body') ? Lang::get('lang.manual-deposit-rejected-body') : "We're sorry to inform you that your cash deposit of RM{{amount}} has been declined. Check your Top Up history for details.",
            ],
            'transferReceived' => [
                'title' => Lang::has('lang.transfer-received-title') ? Lang::get('lang.transfer-received-title') : 'Woo-hoo!',
                'body' => Lang::has('lang.transfer-received-body') ? Lang::get('lang.transfer-received-body') : 'You just got RM{{amount}} from {{from_user}}!',
            ],
            'transferSended' => [
                'title' => Lang::has('lang.transfer-sended-title') ? Lang::get('lang.transfer-sended-title') : 'Transfer Succesfully',
                'body' => Lang::has('lang.transfer-sended-body') ? Lang::get('lang.transfer-sended-body') : 'Your recent transfer of RM{{amount}} to {{to_user}} is Completed!',
            ],
            'currencyUpdate' => [
                'title' => Lang::has('lang.currency-update-title') ? Lang::get('lang.currency-update-title') : 'Currency Updated!',
                'body' => Lang::has('lang.currency-update-body') ? Lang::get('lang.currency-update-body') : 'Your favourite currency rate ({{currency}}) is updated to {{rate}}',
                'sound' => 'default',
            ],
            'vipRankUp' => [
                'title' => Lang::has('lang.vip-rank-up-title') ? Lang::get('lang.vip-rank-up-title') : 'VIP Level Up',
                'body' => Lang::has('lang.vip-rank-up-body') ? Lang::get('lang.vip-rank-up-body') : 'Congratulations! Your have upgraded to {{rank_name}}.',
            ],
            'cryptoDepositApproved' => [
                'title' => Lang::has('lang.crypto-deposit-approved-title') ? Lang::get('lang.crypto-deposit-approved-title') : 'Great news!',
                'body' => Lang::has('lang.crypto-deposit-approved-body') ? Lang::get('lang.crypto-deposit-approved-body') : 'Your crypto transaction of {{amount}}USDT has been credited to your wallet.',
            ],
            'ewalletDepositApproved' => [
                'title' => Lang::has('lang.ewallet-deposit-approved-title') ? Lang::get('lang.ewallet-deposit-approved-title') : 'Great news!',
                'body' => Lang::has('lang.ewallet-deposit-approved-body') ? Lang::get('lang.ewallet-deposit-approved-body') : 'Your E-wallet transaction of RM{{amount}} has been credited to your wallet.',
            ],
            'onlineBankingThaiDepositApproved' => [
                'title' => Lang::has('lang.online-banking-thai-deposit-approved-title') ? Lang::get('lang.online-banking-thai-deposit-approved-title') : 'Great news!',
                'body' => Lang::has('lang.online-banking-thai-deposit-approved-body') ? Lang::get('lang.online-banking-thai-deposit-approved-body') : 'Your online banking transaction of THB{{amount}} has been credited to your wallet.',
            ],
            'cryptoDepositRejected' => [
                'title' => Lang::has('lang.crypto-deposit-rejected-title') ? Lang::get('lang.crypto-deposit-rejected-title') : 'Oh-no!',
                'body' => Lang::has('lang.crypto-deposit-rejected-body') ? Lang::get('lang.crypto-deposit-rejected-body') : "We're sorry to inform you that your crypto deposit of {{amount}}USDT has been declined. Check your Top Up history for details.",
            ],
            'ewalletDepositRejected' => [
                'title' => Lang::has('lang.ewallet-deposit-rejected-title') ? Lang::get('lang.ewallet-deposit-rejected-title') : 'Oh-no!',
                'body' => Lang::has('lang.ewallet-deposit-rejected-body') ? Lang::get('lang.ewallet-deposit-rejected-body') : "We're sorry to inform you that your E-wallet deposit of RM{{amount}} has been declined. Check your Top Up history for details.",
            ],
            'onlineBankingThaiDepositRejected' => [
                'title' => Lang::has('lang.online-banking-thai-deposit-rejected-title') ? Lang::get('lang.online-banking-thai-deposit-rejected-title') : 'Oh-no!',
                'body' => Lang::has('lang.online-banking-thai-deposit-rejected-body') ? Lang::get('lang.online-banking-thai-deposit-rejected-body') : "We're sorry to inform you that your online banking deposit of THB{{amount}} has been declined. Check your Top Up history for details.",
            ],
            'onlineBankingDepositRejected' => [
                'title' => Lang::has('lang.online-banking-deposit-rejected-title') ? Lang::get('lang.online-banking-deposit-rejected-title') : 'Oh-no!',
                'body' => Lang::has('lang.online-banking-deposit-rejected-body') ? Lang::get('lang.online-banking-deposit-rejected-body') : "We're sorry to inform you that your online banking deposit of RM{{amount}} has been declined. Check your Top Up history for details.",
            ],
            'convertSuccess' => [
                'title' => Lang::has('lang.convert-success-title') ? Lang::get('lang.convert-success-title') : 'Woo-hoo!',
                'body' => Lang::has('lang.convert-success-body') ? Lang::get('lang.convert-success-body') : 'Your recent conversion of {{from_iso}}{{from_amount}} to {{to_iso}}{{to_amount}} is complete!',
            ],
            'thbManualDepositApproved' => [
                'title' => Lang::has('lang.thb-manual-deposit-approved-title') ? Lang::get('lang.thb-manual-deposit-approved-title') : 'Great news!',
                'body' => Lang::has('lang.thb-manual-deposit-approved-body') ? Lang::get('lang.thb-manual-deposit-approved-body') : 'Your deposit submission of {{amount}}THB is approved and credited to your wallet.',
            ],
            'thbManualDepositRejected' => [
                'title' => Lang::has('lang.thb-manual-deposit-rejected-title') ? Lang::get('lang.thb-manual-deposit-rejected-title') : 'Oh-no!',
                'body' => Lang::has('lang.thb-manual-deposit-rejected-body') ? Lang::get('lang.thb-manual-deposit-rejected-body') : "We're sorry to inform you that your deposit submission of {{amount}}THB has been declined. Check your Top Up history for details.",
            ],
            'custom' => [
                'title' => $customNotification->title ?? '',
                'body' => $customNotification->content ?? '',
            ],
        ];

        $template = $template[$type];
        $channel = self::findChannelByType($type);

        return [
            'title' => Html2Text::convert($template['title'] ?? '', ['ignore_errors' => true]),
            'body' => Html2Text::convert($template['body'] ?? '', ['ignore_errors' => true]),
            'channel' => $channel,
            'notice_type' => $type,
        ];
    }

    public static function sendNotification($deviceToken, $params, $data, $userID = null, $referenceData = [], $skipLog = false)
    {
        if ($params['notice_type'] == 'transferSended') {
            return true;
        } // Temporary Hide this notification

        if (isset($userID)) {
            $username = User::select('username')->where('id', $userID)->first()['username'] ?? null;
            $v2AuthorizedUser = json_decode((SystemSetting::where('name', 'v2AuthorizedUser')->first()->value ?? null), true) ?? [];

            if (in_array($username, $v2AuthorizedUser)) {
                return true;
            }
        }

        $message = CloudMessage::withTarget('token', $deviceToken)
            ->withNotification(
                Notification::create(
                    $params['title'] ?? '',
                    $params['body'] ?? '',
                    null
                )
            )
            ->withData($data)
            ->withAndroidConfig(
                AndroidConfig::fromArray([
                    'notification' => [
                        'channel_id' => $params['channel'],
                    ],
                ])
            );

        try {
            if (isset($userID) && ! $skipLog) {
                UserNotification::create([
                    'user_id' => $userID,
                    'notice_type' => UserNotification::$noticeType[$params['notice_type']],
                    'reference_data' => $referenceData,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            $result = Firebase::messaging()->send($message);
        } catch (\Throwable) {
            return false;
        }

        return $result;
    }

    public static function sendMulticastNotification($notifications, $skipUserCheck = false)
    {
        if (empty($notifications)) {
            return [];
        }

        $userNotificationData = [];
        
        $notificationsByLang = [];

        // Process and filter notifications
        foreach ($notifications as $notification) {
            // Skip transferSended notifications
            if ($notification['params']['notice_type'] == 'transferSended') {
                continue;
            }

            // Check if user is authorized (if not skipping check)
            if (!$skipUserCheck && isset($notification['userId'])) {
                $username = User::select('username')->where('id', $notification['userId'])->first()['username'] ?? null;
                $v2AuthorizedUser = json_decode((SystemSetting::where('name', 'v2AuthorizedUser')->first()->value ?? null), true) ?? [];

                if (in_array($username, $v2AuthorizedUser)) {
                    continue;
                }
            }

            // Prepare user notification log entry if needed
            if (isset($notification['userId']) && !($notification['skipLog'] ?? false)) {
                $userNotificationData[] = [
                    'user_id' => $notification['userId'],
                    'notice_type' => UserNotification::$noticeType[$notification['params']['notice_type']],
                    'reference_data' => $notification['referenceData'] ?? [],
                    'notification_id' => $notification['notificationId'] ?? null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            // Group by language
            $lang = $notification['lang'] ?? 'en';
            if (!isset($notificationsByLang[$lang])) {
                $notificationsByLang[$lang] = [];
            }

            $notificationsByLang[$lang][] = $notification;
        }

        // Insert notification logs in bulk if any
        if (!empty($userNotificationData)) {
            UserNotification::insert($userNotificationData);
        }

        // Send notifications by language group
        $results = [];

        foreach ($notificationsByLang as $lang => $langNotifications) {
            $deviceTokens = array_map(function($n) { return $n['token']; }, $langNotifications);

            if (empty($deviceTokens)) {
                continue;
            }

            // Create the base message for this language group
            $baseMessage = CloudMessage::new()
                ->withNotification(
                    Notification::create(
                        $langNotifications[0]['params']['title'] ?? '',
                        $langNotifications[0]['params']['body'] ?? '',
                        null
                    )
                )
                ->withData($langNotifications[0]['data'] ?? [])
                ->withAndroidConfig(
                    AndroidConfig::fromArray([
                        'notification' => [
                            'channel_id' => $langNotifications[0]['params']['channel'],
                        ],
                    ])
                );

            try {
                // Send multicast message for this language group
                $sendReport = Firebase::messaging()->sendMulticast($baseMessage, $deviceTokens);

                $results[$lang] = [
                    'success' => $sendReport->successes()->count(),
                    'failures' => $sendReport->failures()->count(),
                    'tokens' => count($deviceTokens)
                ];
            } catch (\Throwable $ex) {
                $results[$lang] = [
                    'success' => 0,
                    'failures' => count($deviceTokens),
                    'error' => $ex->getMessage(),
                    'tokens' => count($deviceTokens)
                ];
            }
        }

        // Aggregate results
        $totalSuccess = array_sum(array_column($results, 'success'));
        $totalFailures = array_sum(array_column($results, 'failures'));

        return [
            'success' => $totalSuccess,
            'failures' => $totalFailures,
            'by_language' => $results
        ];
    }

    public static function broadcastNotification($title, $body, $image)
    {
        $topic = 'all';
        $message = CloudMessage::withTarget('topic', $topic)
            ->withNotification(Notification::create($title, $body, $image));

        return Firebase::messaging()->send($message);
    }

    private static function findChannelByType(string $type)
    {
        switch ($type) {
            case 'onlineBankingDepositApproved':
            case 'manualDepositApproved':
                $channel = Channel::DEPOSIT->value;
                break;
            case 'withdrawalApproved':
            case 'rebateApproved':
                $channel = Channel::WITHDRAWAL->value;
                break;
            default:
                $channel = Channel::DEFAULT->value;
                break;
        }

        return $channel;
    }

    private static function getNotificationDetails($id, $lang)
    {
        $customNotification = NotificationDetail::withTrashed()
            ->where('notification_id', $id)
            ->where(function ($query) use ($lang) {
                $query->where('language_type', $lang);
            })
            ->first();

        if (! $customNotification) {
            $customNotification = NotificationDetail::withTrashed()
                ->where('notification_id', $id)
                ->where(function ($query) {
                    $query->where('language_type', 'en');
                })
                ->first();

            if (! $customNotification) {
                $customNotification = NotificationDetail::withTrashed()
                    ->where('notification_id', $id)
                    ->first();
            }
        }

        return $customNotification;
    }
}
