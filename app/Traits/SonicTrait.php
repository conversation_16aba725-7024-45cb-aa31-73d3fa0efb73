<?php

namespace App\Traits;

use App\Models;

trait SonicTrait
{
    private static function generateSecrect()
    {
        $millis = time(); // current UNIX timestamp;
        $secretKey = env('') ?? null;
        $secret = sha1($millis . $secretKey);
        return ["millis" => $millis, "secret" => $secret];
    }

    protected static function traitEnv()
    {
        // $systemSetting = Models\SystemSetting::where('name','ocTraitEnvironment')->first()['value'] ?? 'production';
        $object = Models\SystemSetting::where('name', 'sonicTraitEnvironment')->first();
        // $disabledProducts = json_decode($object->reference) ?? [];
        $systemSetting['env'] = $object->value ?? 'production';
        // $systemSetting['enabled'] = (int)!in_array("SONIC", $disabledProducts);
        return $systemSetting;
    }

    protected static function getEndUrl($type = null)
    {
        switch ($type) {
            case 'change_password':
                return 'ChangePassword';
                break;
            case 'check_game_credit':
                return 'CheckGameCredit';
                break;
            case 'get_all_card':
                return 'GetAllCardFromPhone';
                break;
            case 'get_store':
                return 'GetStore';
                break;
            case 'validate_user':
                return 'ValidateUser';
                break;
            case 'deposit':
                return 'DepositGameCredit';
                break;
            case 'withdraw':
                return 'WithdrawGameCredit';
            case 'pos_record':
                return 'CurrencyDetail';
            case 'pos_reload':
                return 'MembershipData';
            case 'pos_withdraw':
                return 'MembershipData';
                break;
            case 'machine_summary':
                return 'MachineSummary';
                break;
            case 'currency_detail':
                return 'CurrencyDetail';
                break;
            case 'pos_status':
                return 'GetStore';
        }
        return $type;
    }

    public static function post($params, $type, $isCrawler = false)
    {
        $presetBody = [];

        unset($body);
        unset($webhookParam);

        $method = 'POST';
        switch ($type) {
            case 'change_password':
                $body = [
                    'storeID' => $params['storeId'] ?? null,
                    'cardID' => $params['cardId'] ?? null,
                    'md5' => $params['password'] ?? null,
                ];
                break;
            case 'check_game_credit':
                $body = [
                    'storeID' => $params['storeId'] ?? null,
                    'cardID' => $params['cardId'] ?? null,
                ];
                break;
            case 'get_all_card':
                $body = [
                    'storeID' => $params['storeId'] ?? null,
                    'phone' => $params['phone'] ?? null,
                ];
                break;
            case 'get_store':
                $body = [
                    // 'storeID' => $params['storeId'] ?? null,
                    // 'cardID' => $params['cardId'] ?? null,
                ];
                $method = 'GET';
                break;
            case 'validate_user':
                $body = [
                    'storeID' => $params['storeId'] ?? null,
                    'cardID' => $params['cardId'] ?? null,
                ];
                break;
            case 'deposit':
                $body = [
                    'storeID' => $params['storeId'] ?? null,
                    'cardID' => $params['cardId'] ?? null,
                    // 'md5' => $params['password'] ?? null,
                    'credit' => $params['amount'] ?? null,
                ];
                break;
            case 'withdraw':
                $body = [
                    'storeID' => $params['storeId'] ?? null,
                    'cardID' => $params['cardId'] ?? null,
                    // 'md5' => $params['password'] ?? null,
                    'credit' => $params['amount'] ?? null,
                ];
                break;
            case 'pos_record':
                $body = [
                    'storeID' => $params['storeId'] ?? null,
                    'page' => $params['page'] ?? null,
                    'pageSize' => config('app.pagination_rows'),
                ];
                break;
            case 'pos_reload':
            case 'pos_withdraw':
                $body = [
                    'storeID' => $params['storeId'] ?? null,
                    'page' => $params['page'] ?? null,
                    'pageSize' => $isCrawler ? $params['pageSize'] : config('app.pagination_rows'),
                    'operationType' => $params['operationType'] ?? null,
                    'startDate' => $params['startDate'] ?? null,
                    'endDate' => $params['endDate'] ?? null,
                    'isIncludeOnline' => $isCrawler ? true : $params['isIncludeOnline'] ?? false,
                ];
                break;
            case 'machine_summary':
                $body = [
                    'storeID' => $params['storeId'] ?? null,
                    'startDate' => $params['startDate'] ?? null,
                    'endDate' => $params['endDate'] ?? null,
                ];
                break;
            case 'currency_detail':
                $body = [
                    'storeID' => $params['storeId'] ?? null,
                    'page' => $params['page'] ?? null,
                    'pageSize' => $isCrawler ? $params['pageSize'] : config('app.pagination_rows'),
                    'startDate' => $params['startDate'] ?? null,
                    'endDate' => $params['endDate'] ?? null,
                ];
                break;
            case 'pos_status':
                $body = [];
                $method = 'GET';
        }

        $body = $presetBody + ($body ?? []);
        $endUrl = self::getEndUrl($type);
        $endpoint = env('TRAIT_URL_SONIC') . $endUrl;

        // Prepare curl
        $request = [
            'method' => $method,
            'endpoint' => $endpoint,
            'header' => [
                'Accept: */*',
                'Content-Type: application/json-patch+json',
            ],
            'body' => json_encode($body),
        ];

        // $webhookParam = [];
        $webhookParam = null;

        if (env('APP_ENV') == self::traitEnv()['env']) {
            // if(true){
            try {
                $result = CurlTrait::doCurl($request, 'post-sonic', $webhookParam);
            } catch (\Exception $e) {

                $returnData = ['status' => false, "refund" => 0, 'msg' => $e->getMessage() ?? "[$type] Something went wrong."];

                return $returnData;
            }
        } else {
            $result = null;
            switch ($type) {
                case 'change_password':
                    break;
                case 'check_game_credit':
                    $result = '{"credit":"66000"}';
                    break;
                case 'get_all_card':
                    $result = '[{"member_card_id":"001","member_card_no":"2634796084","会员姓名":"test","会员生日":"2024-07-30T00:00:00","登记设备编号":"1","member_balance":63000,"accumulated_balance":0,"member_status":1,"操作人员工号":"222062","创建时间":"2024-07-30T16:43:54.833","更新时间":"2024-08-01T16:57:02.72","累积起始时间":"2024-07-30T16:43:54.833","card_image":"","更新次数":183,"创建门店":"82407020C","branch_code":"82407020C"},{"member_card_id":"001","member_card_no":"2634796074","会员姓名":"test","会员生日":"2024-07-30T00:00:00","登记设备编号":"1","member_balance":0,"accumulated_balance":0,"member_status":1,"操作人员工号":"222062","创建时间":"2024-07-30T16:43:54.833","更新时间":"2024-08-01T16:57:02.72","累积起始时间":"2024-07-30T16:43:54.833","card_image":"","更新次数":99,"创建门店":"82407020C","branch_code":"82407020C"},{"member_card_id":"001","member_card_no":"3122232","会员姓名":"test","会员生日":"2024-07-30T00:00:00","登记设备编号":"1","member_balance":68960,"accumulated_balance":0,"member_status":1,"操作人员工号":"222062","创建时间":"2024-07-30T16:43:54.833","更新时间":"2024-08-01T16:57:02.72","累积起始时间":"2024-07-30T16:43:54.833","card_image":"","更新次数":54,"创建门店":"82407020C","branch_code":"82407020C"},{"member_card_id":"001","member_card_no":"3212","会员姓名":"tt","会员生日":"2024-08-14T00:00:00","登记设备编号":"1","member_balance":0,"accumulated_balance":0,"member_status":1,"操作人员工号":"221034","创建时间":"2024-08-14T16:20:50.8","更新时间":"2024-08-14T16:44:09","累积起始时间":"2024-08-14T16:20:50.8","card_image":"","更新次数":14,"创建门店":"82407020C","branch_code":"82407020C"},{"member_card_id":"001","member_card_no":"87","会员姓名":"tt","会员生日":"2024-08-14T00:00:00","登记设备编号":"1","member_balance":0,"accumulated_balance":0,"member_status":1,"操作人员工号":"221034","创建时间":"2024-08-14T16:20:50.8","更新时间":"2024-08-14T16:20:50.8","累积起始时间":"2024-08-14T16:20:50.8","card_image":"","更新次数":14,"创建门店":"82407020C","branch_code":"82407020C"},{"member_card_id":"001","member_card_no":"2634000458","会员姓名":"tt","会员生日":"2024-08-14T00:00:00","登记设备编号":"1","member_balance":0,"accumulated_balance":0,"member_status":1,"操作人员工号":"221034","创建时间":"2024-08-14T16:20:50.8","更新时间":"2024-08-14T16:20:50.8","累积起始时间":"2024-08-14T16:20:50.8","card_image":"","更新次数":13,"创建门店":"82407020C","branch_code":"82407020C"}]';
                    break;
                case 'get_store':
                    $result = '[{"id":1,"name":"StoreName1"},{"id":2,"name":"test"},{"id":3,"name":"les"},{"id":4,"name":"locl"}]';
                    break;
                case 'validate_user':
                    $result = '[{"member_phone": "60189742481","member_password": "e10adc3949ba59abbe56e057f20f883e"}]';
                    // $result = '[]';
                    break;
                case 'deposit':
                    $result = '[{"member_balance":95002,"latest_member_balance":95003}]';
                    break;
                case 'withdraw':
                    $result = '[{"member_balance":95002,"latest_member_balance":95003}]';
                    break;
            }
        }

        if (!$result) {
            return ["status" => false, "refund" => 0];
        }

        $result = json_decode($result, true);

        $returnData = ['status' => true, 'data' => null];

        switch ($type) {
            case 'change_password':
                break;
            case 'check_game_credit':
                $returnData = [
                    'status' => true,
                    'data' => $result,
                ];
                break;
            case 'get_all_card':
                $returnData = [
                    'status' => true,
                    'data' => $result,
                ];
                break;
            case 'get_store':
                $returnData = [
                    'status' => true,
                    'data' => $result,
                ];
                break;
            case 'validate_user':
                $returnData = [
                    'status' => true,
                    'data' => $result,
                ];
                break;
            case 'deposit':
                $returnData = [
                    'status' => true,
                    'data' => $result,
                ];
                break;
            case 'withdraw':
                $returnData = [
                    'status' => true,
                    'data' => $result,
                ];
                break;
            case 'withdraw':
                $returnData = [
                    'status' => true,
                    'data' => $result,
                ];
                break;
            case 'pos_record':
            case 'pos_reload':
            case 'pos_withdraw':
            case 'pos_status':
                $returnData = [
                    'status' => true,
                    'data' => $result,
                ];
                break;
            case 'machine_summary':
                $returnData = [
                    'status' => true,
                    'data' => $result,
                ];
                break;
            case 'currency_detail':
                $returnData = [
                    'status' => true,
                    'data' => $result,
                ];
                break;
        }

        return $returnData;
    }
}
