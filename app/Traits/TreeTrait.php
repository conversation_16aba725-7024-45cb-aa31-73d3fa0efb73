<?php

namespace App\Traits;

use App\Models\TreeSponsor;
use App\Models\UserDetail;

trait TreeTrait
{
    public static function getSponsorTreeDownlines($clientID, $includeSelf = true)
    {
        $result = TreeSponsor::select('trace_key')->where('user_id', $clientID)->first();

        if (! $result) {
            return;
        }

        $result = TreeSponsor::select('user_id')->where('trace_key', 'LIKE', $result->trace_key.'%')->get()->all();

        $downlineIDArray = [];

        foreach ($result as $key => $val) {
            $downlineIDArray[$val['user_id']] = $val['user_id'];
        }

        if (! $includeSelf) {
            unset($downlineIDArray[$clientID]);
        }

        return $downlineIDArray;
    }

    public static function insertSponsorTree($clientID, $uplineID)
    {
        $result = TreeSponsor::select('level', 'trace_key')->lockForUpdate()->where('user_id', $uplineID)->first();

        if (! $result) {
            return;
        }

        $level = $result->level + 1;
        $traceKey = $result->trace_key.'/'.$clientID;

        $insertData = [
            'user_id' => $clientID,
            'upline_id' => $uplineID,
            'level' => $level,
            'trace_key' => $traceKey,
        ];

        $result = TreeSponsor::create($insertData);

        if (! $result) {
            return;
        }

        return true;
    }

    public static function updateTotalDownline($clientID)
    {
        $result = TreeSponsor::select('trace_key')->where('user_id', $clientID)->first();
        $traceKey = $result->trace_key;

        $uplineArray = explode('/', $traceKey);

        $clientIDAry = [];

        foreach ($uplineArray as $uplineID) {
            if ($uplineID == $clientID) {
                continue;
            }

            $clientIDAry[] = $uplineID;
        }

        $insertData = [
            'user_id' => $clientID,
            'name' => 'totalDownline',
            'value' => 0,
        ];
        UserDetail::create($insertData);

        if (! empty($clientIDAry)) {
            $clientStg = UserDetail::whereIn('user_id', $clientIDAry)
                ->where('name', 'totalDownline');

            $checkClientStg = clone $clientStg;
            $checkClientStg->increment('value');

            $clientIDArr = $checkClientStg->pluck('user_id')
                ->toArray();

            $diff = array_diff($clientIDAry, $clientIDArr);
            if (! $clientIDArr && ! $diff) {
                $diff = $clientIDAry;
            }

            if (count($diff) > 0) {
                foreach ($diff as $clientID) {
                    $insertData = [
                        'user_id' => $clientID,
                        'name' => 'totalDownline',
                        'value' => 1,
                    ];
                    UserDetail::create($insertData);
                }
            }
        }

        return true;
    }
}
