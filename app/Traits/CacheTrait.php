<?php

namespace App\Traits;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use App\Traits\CurlTrait;

trait CacheTrait {

    static function getCache($cacheName) {
        if (request()->nocache || env('APP_LISTING_NO_CACHE', false)) {
            return false;
        }

        if (env('APP_ENV') != 'production')
            Log::debug("fired : getCache($cacheName)");


        $result = Cache::get($cacheName);
        if ($result) {
            $result = unserialize($result);
            $result->cacheName = $cacheName;
            $result->cacheTtl = Redis::ttl($cacheName);
            return $result;
        }
        return false;
    }

    static function setCache($cacheName, $result, $cacheExpiry) {

        if (env('APP_ENV') != 'production')
            Log::debug("fired : setCache($cacheName, $cacheExpiry)");

        Cache::put($cacheName, serialize($result), $cacheExpiry);
    }

    /*
     * for : global select with uid
     * note : call this after insert/update/delete/alter to the given model
     */

    static function clearCacheByUid($cachePrefix, $uid) {
        if (env('APP_ENV') != 'production')
            Log::debug("fired : clearCacheByUid($cachePrefix, $uid)");

        $cachePattern = $cachePrefix . $uid . '-';
        return self::clearCache($cachePattern);
    }

    /*
     * for : global select without uid
     * call this after insert/update/delete/alter the given model
     */

    static function clearCacheWithoutUid($cachePrefix) {
        if (env('APP_ENV') != 'production')
            Log::debug("fired : clearCacheWithoutUid($cachePrefix)");

        $cachePattern = $cachePrefix;
        return self::clearCache($cachePattern);
    }

    protected static function clearCache($cachePattern) {
        $allKeys = self::getCacheToClear($cachePattern);

        //remove the key
        foreach ($allKeys as $key) {
            Redis::del($key);
        }

        return true;
    }

    private static function getCacheToClear($cachePattern, $cursor = null, $results = []) {
        if ($cursor === '0') {
            return $results;
        }
        if ($cursor === null) {
            $cursor = 0;
        }

        list($cursor, $result) = Redis::scan($cursor, "match", "$cachePattern*", 'count', 1000);

        $results = array_merge($results, $result);

        return self::getCacheToClear($cachePattern, $cursor, $results);
    }

    static function clearLangCache($langType = 'lang-')
    {
        self::clearCacheWithoutUid($langType);

        return true;
    }
}
