<?php

namespace App\Traits;

use App\Models;
use App\Models\Services;
use DB;
use Illuminate\Support\Facades\Log;

trait OCTK8Trait
{
    private static function generateOcSecrect()
    {
        $millis = time(); // current UNIX timestamp;
        $secretKey = env('OC_TK8_SECRET_KEY') ?? null;
        $secret = sha1($millis . $secretKey);
        return ["millis" => $millis, "secret" => $secret];
    }

    protected static function ocTraitEnv()
    {
        // $systemSetting = Models\SystemSetting::where('name','ocTraitEnvironment')->first()['value'] ?? 'production';
        $object = Models\SystemSetting::where('name', 'ocTraitEnvironment')->first();
        $disabledProducts = json_decode($object->reference) ?? [];
        $systemSetting['env'] = $object->value ?? 'production';
        $systemSetting['enabled'] = (int)!in_array("TK8", $disabledProducts);
        return $systemSetting;
    }

    public static function generateSignature($params)
    {
        $company = $params['company'] ?? null;
        $account = $params['account'] ?? null;
        $amount = $params['amount'] ?? null;
        $merchantReference = $params['merchant_reference'] ?? null;
        $secretKey = env('OC_TK8_SIGNATURE_HASH_KEY') ?? null;

        $reversedString = strrev(((string)$company) . ((string)$account) . ((string)$amount) . ((string)$merchantReference));
        $combineString = $reversedString . $secretKey;
        $signature = hash('sha256', $combineString);
        return $signature;
    }

    public static function postOC($params, $type)
    {
        $ocSecrect = self::generateOcSecrect();
        $presetBody = [
            'millis' => (int) $ocSecrect['millis'] ?? null,
            'secret' => $ocSecrect['secret'] ?? null,
            'language' => $params['language'] ?? 'en',
            'company' => env('OC_TK8_COMPANY') ?? null,
        ];

        unset($body);
        unset($webhookParam);
        switch ($type) {
            case 'register':
                $body = [
                    'ip' => $params['ip'] ?? null,
                    'account' => $params['account'] ?? null,
                    'password' => $params['password'] ?? null,
                ];

                if (isset($params['referral']) && !empty($params['referral'])) $body['referral'] = $params['referral'];
                break;

            case 'auto_login':
                $body = [
                    'account' => $params['account'] ?? null,
                ];
                break;

            case 'check_balance':
                $body = [
                    'account' => $params['account'] ?? null,
                ];
                break;

            case 'callback/deposit':
                $body['transaction_id'] = $params['transaction_id'] ?? null;
                $body['status'] = $params['status'] ?? null;
                $body['merchant_reference'] = $params['merchant_reference'] ?? null;
                $body['transaction_time'] = $params['transaction_time'] ?? null;
                $body['promo_id'] = $params['promo_id'] ?? null;
                $body['signature'] = self::generateSignature($params);
            case 'deposit':
                $body['account'] = $params['account'] ?? null;
                $body['amount'] = $params['amount'] ?? null;
                $body['wallet_type'] = $params['wallet_type'] ?? null;

                // optional
                if (isset($params['user_bank_holder_name']) && !empty($params['user_bank_holder_name'])) $body['user_bank_holder_name'] = $params['user_bank_holder_name'];
                if (isset($params['remark']) && !empty($params['remark'])) $body['remark'] = $params['remark'];
                break;

            case 'withdraw':
                $body = [
                    'account' => $params['account'] ?? null,
                    'wallet_type' => $params['wallet_type'] ?? null,
                ];

                // optional
                if (isset($params['waive']) && !empty($params['waive'])) $body['waive'] = $params['waive'];
                if (isset($params['user_bank_holder_name']) && !empty($params['user_bank_holder_name'])) $body['user_bank_holder_name'] = $params['user_bank_holder_name'];
                if (isset($params['remark']) && !empty($params['remark'])) $body['remark'] = $params['remark'];
                break;

                // case 'get_player_gamelog':
                //     $body = [
                //         'date' => $params['date'] ?? date("Y-m-d", strtotime('-1 day')),
                //     ];
                //     break;

            case 'verify_transaction':
                $body = [
                    'data' => json_encode($params['data']) ?? null,
                ];
                break;

            case 'callback/withdraw':
                $body = [
                    // 'txn_id' => $params['txn_id'] ?? null,
                    // 'status' => $params['status'] ?? null, // SUCCESS, FAIL
                    'transaction_id' => $params['transaction_id'] ?? null,
                    // 'company' => '',
                    'account' => $params['account'] ?? null,
                    'amount' => $params['amount'] ?? null,
                    'status' => $params['status'] ?? null,
                    'merchant_ref' => $params['merchant_ref'] ?? null,
                    'transaction_time' => $params['transaction_time'] ?? null,
                    'wallet_type' => $params['wallet_type'] ?? null,
                    // 'signature' => '',
                ];
                $body['wallet_type'] = $params['wallet_type'] ?? null;
                $body['signature'] = self::generateSignature($params);
                break;

            case 'transfer': // Direct Launch Game
                $body = [
                    'auth_code' => $params['auth_code'] ?? null,
                    'game' => $params['game'],
                    'gameID' => $params['gameID'],
                    'gameCategory' => $params['gameCategory'],
                    'gameListID' => $params['gameListID'],
                    'source' => $params['source'] ?? 'MOBILE', // enum: WEBSITE, MOBILE
                    'ip' => $params['ip'] ?? null,
                ];
                break;

            case 'get_game_list':
                $body = [
                    'productID' => $params['productID'] ?? null,
                    'ip' => $params['ip'] ?? null,
                ];
                break;

            default:
                $body = [];
                break;
        }

        // Prepare endpoint
        switch ($type) {
            case 'callback/deposit':
            case 'callback/withdraw':
            case 'verify_transaction':
                $endpoint = env('OC_TK8_API_URL') . $type;
                break;
            case 'transfer':
                $endpoint = env('OC_TK8_API_V2_URL') . 'apiv2/' . $type;
                break;
            default:
                $endpoint = env('OC_TK8_API_URL') . 'api_ext/' . $type;
                break;
        }
        $webhookParam['type'] = $type;
        $webhookParam['params'] = $params;
        $webhookParam['deposit_id'] = $params['deposit_id'] ?? null;
        $webhookParam['withdraw_id'] = $params['withdraw_id'] ?? null;

        $body = $presetBody + ($body ?? []);
        // Prepare curl
        $request = [
            'method' => 'POST',
            'endpoint' => $endpoint,
            'header' => [
                'X-API-KEY: ' . env('OC_TK8_UTILITY_TOKEN'),
                'Content-Type: multipart/form-data',
            ],
            'body' => $body,
            'message' => $body,
        ];

        if (env('APP_ENV') == self::ocTraitEnv()['env'] && self::ocTraitEnv()['enabled']) {
            try {
                $result = CurlTrait::doCurl($request, 'postOC-tk8', $webhookParam);
            } catch (\Exception $e) {
                $returnData = ['status' => false, "refund" => 0, 'msg' => $e->getMessage() ?? "[$type] Something went wrong."];
                return $returnData;
            }
        } else {
            $result = null;
            switch ($type) {
                case 'register':
                    $result = '{"status":"SUCCESS","message":"Successfully Registered User"}';
                    break;

                case 'auto_login':
                    $result = '{"status":"SUCCESS","message":"Successfully Returned Login Link","result":{"login_link":"https:\/\/www.tk8.io?auth_code=af5065d7"}}';
                    break;

                case 'check_balance':
                    $result = '{"status":"SUCCESS","message":"Balance Refreshed Successfully","result":{"wallet_currency":"MYR","main_wallet":"10.00","lottery_wallet":"80.00","game_wallet":"20.00","last_played_game":"Main Wallet"}}';
                    break;

                case 'callback/deposit':
                case 'deposit':
                    $result = '{"status":"SUCCESS","message":"Deposit Success!","result": {"transaction_id": *********, "reference": "xxx099xx88", "deposit_amount": "10.00", "bonus_amount": "0.00", "before_balance": "254.00", "current_balance": "264.00"}}';
                    // $result = '{"status": "ERROR","message": "The Amount field must contain a number greater than 0."}';
                    break;

                case 'withdraw':
                    $result = '{"status":"SUCCESS","message":"Withdraw Success!","result":{"transaction_id":*********,"reference":"00xxx0x00","witdraw_amount":"100.00","waive_amount":"0.00","before_balance":"200.00","current_balance":"0.00"}}';
                    break;

                    // case 'get_player_gamelog': 
                    //     $result = '{"status":"SUCCESS","message":"Successfully returned result","result":[{"account":"**********","game_category":"SLOT","turnover_achieved":"126.75"},{"account":"***********","game_category":"SLOT","turnover_achieved":"122.00"},{"account":"**********","game_category":"SLOT","turnover_achieved":"162.31"},{"account":"**********","game_category":"SLOT","turnover_achieved":"53.15"},{"account":"**********","game_category":"SLOT","turnover_achieved":"158.65"},{"account":"**********","game_category":"LIVE","turnover_achieved":"1960.00"},{"account":"**********","game_category":"FISHING","turnover_achieved":"220.10"},{"account":"***********","game_category":"SLOT","turnover_achieved":"0.28"},{"account":"***********","game_category":"SLOT","turnover_achieved":"8.10"},{"account":"***********","game_category":"SLOT","turnover_achieved":"2.04"},{"account":"**********","game_category":"SLOT","turnover_achieved":"0.10"},{"account":"**********","game_category":"SLOT","turnover_achieved":"1.07"},{"account":"***********","game_category":"SLOT","turnover_achieved":"25.88"},{"account":"**********","game_category":"SLOT","turnover_achieved":"1.20"},{"account":"**********","game_category":"SLOT","turnover_achieved":"324.00"},{"account":"**********","game_category":"SLOT","turnover_achieved":"13.35"}]}';
                    //     break;

                case 'verify_transaction':
                    $result = '{"status":"SUCCESS","message":"Deposit Valid","result":{"order_id":"A0006","account":"********","currency":"MYR","amount":"10.00","pay_url":"https://bill"}}';
                    break;

                case 'callback/withdraw':
                    $result = '{"status":"SUCCESS","message":"OK","result":{"order_id":"A0006"}}';
                    break;
                default:
                    break;
            }
        }

        if (!$result) {
            return [
                "status" => false,
                "refund" => 0
            ];
        }

        $result = json_decode($result, true);
        if (!isset($result['status']) || empty($result['status'])) {
            $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong.", "data" => null];
            return $returnData;
        }

        if (strtolower($result['status']) != 'success') {
            $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong.", "data" => null];
            return $returnData;
        }

        $returnData = ['status' => false, "data" => null];
        switch ($type) { // different api give different style for of result
            case 'auto_login':
                if (!isset($result['result']) || empty($result['result'])) {
                    $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong. Result Missing.", "data" => null];
                    return $returnData;
                }

                // if(!isset($result['result']['login_link']) || empty($result['result']['login_link'])){
                //     $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong. Log In Link Missing.", "data" => null];
                //     return $returnData;
                // }
                if (!isset($result['result']['auth_code']) || empty($result['result']['auth_code'])) {
                    $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong. Log In Link Missing.", "data" => null];
                    return $returnData;
                }

                if (isset($params['service_id'])) {
                    $url = Services::find($params['service_id'])->url . "?auth_code=" . $result['result']['auth_code'];
                }

                if ($params['is_game'] == true) {
                    $returnData = [
                        'status' => true,
                        "data" => [
                            'auth_code' => $result['result']['auth_code']
                        ],
                    ];
                } else {
                    $returnData = [
                        'status' => true,
                        // "data" => $result['result']['login_link'],
                        // "data" => env('FUN_WALLET_AUTO_LOGIN_URL').$result['result']['auth_code'],
                        "data" => $url ?? env('FUN_WALLET_AUTO_LOGIN_URL') . $result['result']['auth_code'],
                    ];
                }
                break;

            case 'check_balance':
                if (!isset($result['result']) || empty($result['result'])) {
                    $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong. Result Missing.", "data" => null];
                    return $returnData;
                }

                $returnData = [
                    'status' => true,
                    "data" => $result['result'],
                ];
                break;

            case 'callback/deposit':
                $returnData = [
                    'status' => true,
                    "data" => $result,
                ];
                break;
            case 'deposit':
                if (!isset($result['result']) || empty($result['result'])) {
                    $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong. Result Missing.", "data" => null];
                    return $returnData;
                }

                $returnData = [
                    'status' => true,
                    "data" => $result['result'],
                ];
                break;

            case 'withdraw':
                if (!isset($result['result']) || empty($result['result'])) {
                    $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong. Result Missing.", "data" => null];
                    return $returnData;
                }

                if (!isset($result['result']['witdraw_amount']) || $result['result']['witdraw_amount'] <= 0) {
                    $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong. Invalid withdraw amount.", "data" => null];
                    return $returnData;
                }

                $returnData = [
                    'status' => true,
                    "data" => $result['result'],
                ];
                break;

                // case 'get_player_gamelog': 
                //     if(!isset($result['result']) || empty($result['result'])){
                //         $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong. Result Missing.", "data" => null];
                //         return $returnData;
                //     }

                //     $returnData = [
                //         'status' => true, 
                //         "data" => $result['result'],
                //     ];
                //     break;

            case 'verify_transaction':
                if (!isset($result['result']) || empty($result['result'])) {
                    $returnData = ['status' => false, 'msg' => $result['message'] ?? "[$type] Something went wrong. Result Missing.", "data" => null];
                    return $returnData;
                }

                $returnData = [
                    'status' => true,
                    "data" => $result['result'],
                ];
                break;

            case 'transfer':
                $returnData = [
                    'status' => true,
                    "data" => $result['result']['game_url'],
                ];
                break;

            case 'get_game_list':
                $returnData = [
                    'status' => true,
                    "data" => $result['result'],
                ];
                break;

            case 'callback/withdraw': // do nothing
            default:
                $returnData = ['status' => true, "data" => null];
                break;
        }

        return $returnData;
    }
}
