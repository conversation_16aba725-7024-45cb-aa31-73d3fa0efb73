<?php

namespace App\Traits;

use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Lang;

trait ThirdPartyTrait
{
    public static function getDataFromPath($path = '') {
        $product = Str::afterLast($path, '-');
        $product = strtolower($product);
        
        return self::get($product);
    }

    public static function getDataFromCurrency($currency = '') {
        // $product = Str::afterLast($path, '-');
        $currency = strtolower($currency);

        switch($currency){
            default:
                $product = null;
            break;
        }
        return self::get($product);
    }

    public static function get($product = '') {
        switch($product){
            default:
                $party = 'default';
                $depositType = null;
                $key = null;
            break;
        }

        $data['party'] = $party;
        $data['key'] = $key;
        $data['deposit_type'] = $depositType;
        return $data;
    }
}
