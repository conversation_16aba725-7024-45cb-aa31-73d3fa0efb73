<?php

namespace App\Traits;

trait ApiKeyTrait
{
    /**
     * The cipher method. For a list of available cipher methods, use openssl_get_cipher_methods().
     */
    private static $cipher = 'aes-256-cbc';

    public static function generateEncryptedApiKey($params = [])
    {
        if (in_array(self::$cipher, openssl_get_cipher_methods())) {
            $plaintext = base64_encode(json_encode($params['data']));

            $passphrase = hex2bin(env('API_KEY_ENCKEY')); // The passphrase. If the passphrase is shorter than expected, it is silently padded with NUL characters; if the passphrase is longer than expected, it is silently truncated.
            $ivLen = openssl_cipher_iv_length(self::$cipher);
            $iv = openssl_random_pseudo_bytes($ivLen); // A non-NULL Initialization Vector.

            $cipherText = openssl_encrypt($plaintext, self::$cipher, $passphrase, $options = 0, $iv);

            // Store $iv, $cipher and $passphrase for decryption later
            return [
                'iv' => $iv,
                'api_key' => $cipherText,
            ];
        }
    }

    public static function decryptApiKey($params = [])
    {
        $original_plaintext = openssl_decrypt($params['api_key'], self::$cipher, hex2bin(env('API_KEY_ENCKEY')), $options = 0, $params['iv']);

        if (! $original_plaintext) {
            return false;
        }

        return json_decode(base64_decode($original_plaintext), true);
    }
}
