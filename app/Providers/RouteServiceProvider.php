<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to the "home" route for your application.
     *
     * Typically, users are redirected here after authentication.
     *
     * @var string
     */
    public const HOME = '/home';

    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     *
     * @return void
     */
    public function boot()
    {
        $this->configureRateLimiting();

        $this->routes(function () {
            $requestRoot = str_replace('https://', '', str_replace('http://', '', Request()->root()));

            if ($requestRoot == config('app.subdomain_api') || app()->runningInConsole()) {
                if (! app()->runningInConsole()) {
                    define('MODULE', 'api');
                }
                Route::middleware('api')
                    ->domain(config('app.subdomain_api'))
                    ->group(base_path('routes/api.php'));
            }

            if ($requestRoot == config('app.subdomain_api_admin') || app()->runningInConsole()) {
                if (! app()->runningInConsole()) {
                    define('MODULE', 'admin');
                }

                Route::middleware('api')
                    ->domain(config('app.subdomain_api_admin'))
                    ->group(base_path('routes/api_admin.php'));
            }

            if ($requestRoot == config('app.subdomain_api_user') || app()->runningInConsole()) {
                if (! app()->runningInConsole()) {
                    define('MODULE', 'user');
                }

                Route::middleware('api')
                    ->domain(config('app.subdomain_api_user'))
                    ->group(base_path('routes/api_user.php'));
            }

            if ($requestRoot == config('app.subdomain_api_app') || app()->runningInConsole()) {
                if (! app()->runningInConsole()) {
                    define('MODULE', 'app');
                }

                Route::middleware('api')
                    ->domain(config('app.subdomain_api_app'))
                    ->group(base_path('routes/api_user.php'));
            }

            if ($requestRoot == config('app.subdomain_api_user_v2') || app()->runningInConsole()) {
                if (! app()->runningInConsole()) {
                    define('MODULE', 'app');
                }

                Route::middleware('api')
                    ->domain(config('app.subdomain_api_user_v2'))
                    ->group(base_path('routes/api_user.php'));
            }
        });
    }

    /**
     * Configure the rate limiters for the application.
     *
     * @return void
     */
    protected function configureRateLimiting()
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });
    }
}
