<?php

namespace App\Providers;

use App\Models\Permissions;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Schema;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();
        $this->permission_list();
        //
    }

    public function permission_list(){
        $is_master = auth("admin")->user()->is_master ?? 0;

        $res = $permission_list = [];

        // check  table permissions exist or not
        if(Schema::hasTable('permissions') == false){
            return;
        }

        $res = Permissions::when($is_master,
        function($q){
            return $q->where('master_disabled',0);
        },
        function($q){
            $q->where('disabled',0);
            return $q->where('master_disabled',0);
        })
        ->pluck('api_url', 'id')->toArray();

        foreach ($res as $id => $value) {
            if(!$value) continue;
            $temp = json_decode($value, true);
            foreach ($temp as $api) {
                $permission_list[$api][] = $id;
            }
        }

        $role = auth('admin')->user()->role_id ?? 0;

        foreach ($permission_list as $api => $ary) {
            Gate::define($api, function ($admin) use ($ary) {
                $is_master = $admin->is_master ?? 0;
                $permission_ary = ($is_master) ? $ary : ($admin->getRoles->permissions_id ?? []);

                if(empty($permission_ary)){
                    return 0;
                }

                return count(array_intersect($permission_ary, $ary));
            });
        }
    }
}
