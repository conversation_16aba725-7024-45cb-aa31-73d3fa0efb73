<?php

namespace App\Providers;

use App\Services\PaymentGateway\FPay;
use App\Services\PaymentGateway\FPayEWallet;
use App\Services\PaymentGateway\FPayPayout;
use App\Services\PaymentGateway\FPayTelco;
use App\Services\PaymentGateway\FPayThai;
use App\Services\PaymentGateway\OnePay;
use App\Services\PaymentGateway\RapidPay;
use App\Services\PaymentGateway\WePay;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        // FPay
        $this->app->bind(FPay::class, function ($app) {
            return new FPay(
                env('FPAY_USERNAME'),
                env('FPAY_API_KEY'),
                env('FPAY_SECRET_KEY'),
                env('FPAY_API_URL')
            );
        });

        // FPay EWallet
        $this->app->bind(FPayEWallet::class, function ($app) {
            return new FPayEWallet(
                env('FPAY_USERNAME_EWALLET'),
                env('FPAY_API_KEY_EWALLET'),
                env('FPAY_SECRET_KEY_EWALLET'),
                env('FPAY_API_URL_EWALLET')
            );
        });

        // FPay Thai
        $this->app->bind(FPayThai::class, function ($app) {
            return new FPayThai(
                env('FPAY_USERNAME_THAI'),
                env('FPAY_API_KEY_THAI'),
                env('FPAY_SECRET_KEY_THAI'),
                env('FPAY_API_URL_THAI')
            );
        });

        // FPay Telco
        $this->app->bind(FPayTelco::class, function ($app) {
            return new FPayTelco(
                env('FPAY_USERNAME_TELCO'),
                env('FPAY_API_KEY_TELCO'),
                env('FPAY_SECRET_KEY_TELCO'),
                env('FPAY_API_URL_TELCO')
            );
        });

        // FPay Payout
        $this->app->bind(FPayPayout::class, function ($app) {
            return new FPayPayout(
                env('FPAY_USERNAME_PAYOUT'),
                env('FPAY_API_KEY_PAYOUT'),
                env('FPAY_SECRET_KEY_PAYOUT'),
                env('FPAY_API_URL_PAYOUT')
            );
        });

        // OnePay Payout
        $this->app->bind(OnePay::class, function ($app) {
            return new OnePay(
                env('ONEPAY_USERNAME'),
                env('ONEPAY_API_KEY'),
                env('ONEPAY_SECRET_KEY'),
                env('ONEPAY_API_URL')
            );
        });

        // RapidPay
        $this->app->bind(RapidPay::class, function ($app) {
            return new RapidPay(
                env('RAPIDPAY_API_KEY'),
                env('RAPIDPAY_API_URL')
            );
        });

        // WePay
        $this->app->bind(WePay::class, function ($app) {
            return new WePay(
                env('WEPAY_API_KEY'),
                env('WEPAY_API_URL')
            );
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // 
    }
}
