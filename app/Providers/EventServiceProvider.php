<?php

namespace App\Providers;

use App\Models;
use App\Observers;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        Models\Admin::observe(Observers\AdminObserver::class);
        Models\AdminRoles::observe(Observers\AdminRolesObserver::class);
        Models\CreditTransaction::observe(Observers\CreditTransactionObserver::class);
        Models\Convert::observe(Observers\ConvertObserver::class);
        Models\Transfer::observe(Observers\TransferObserver::class);
        Models\User::observe(Observers\UserObserver::class);
        Models\UserBank::observe(Observers\UserBankObserver::class);
        Models\UserDetail::observe(Observers\UserDetailObserver::class);
        Models\ExTransfer::observe(Observers\ExTransferObserver::class);
        Models\Providers\MtMachine::observe(Observers\MachineObserver::class);
        Models\CurrencyDetail::observe(Observers\CurrencyDetailObserver::class);
        // Models\UserLevel::observe(Observers\UserLevelObserver::class);

        // Register observers for live transactions
        Models\Deposit::observe(Observers\DepositObserver::class);
        Models\Withdrawal::observe(Observers\WithdrawalObserver::class);
        Models\UserRebate::observe(Observers\UserRebateObserver::class);

        // Native
        self::registerGlobalObserver();
    }

    private static function registerGlobalObserver()
    {
        /** @var \Illuminate\Database\Eloquent\Model[] $MODELS */
        $MODELS = [
            Models\AnnouncementBar::class,
            Models\AnnouncementBarDetail::class,
            Models\AnnouncementBarSetting::class,
            Models\Announcement::class,
            Models\AnnouncementDetail::class,
            Models\Memo::class,
            Models\MemoDetail::class,
            Models\MemoSetting::class,
            Models\Banner::class,
            Models\BannerDetail::class,
            Models\BannerSetting::class,
            Models\Product::class,
            Models\ProductPrice::class,
            Models\Document::class,
            Models\DocumentDetail::class,
            Models\CurrencyRate::class,
            Models\Kyc::class,
            // ...... more models here
        ];

        foreach ($MODELS as $MODEL) {
            $MODEL::observe(Observers\GlobalObserver::class);
        }
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     *
     * @return bool
     */
    public function shouldDiscoverEvents()
    {
        return false;
    }
}
