<?php

namespace App\Providers;

use App\Services\GameProvider\CQ9;
use App\Services\GameProvider\GSC;
use App\Services\GameProvider\JILI;
use App\Services\GameProvider\JK;
use App\Services\GameProvider\KISS;
use App\Services\GameProvider\MEGA;
use App\Services\GameProvider\MT;
use App\Services\GameProvider\PUSSY;
use Illuminate\Support\ServiceProvider;

class GameProviderServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        // MT
        $this->app->bind(MT::class, function ($app) {
            if (env('APP_ENV') == 'local' || env('APP_ENV') == 'staging') {
                return new MT(
                    env('MT_STAGING_API_URL'),
                    env('MT_STAGING_ID'),
                    env('MT_STAGING_SECRET')
                );
            } elseif (env('APP_ENV') == 'production') {
                return new MT(
                    env('MT_LIVE_API_URL'),
                    env('MT_LIVE_ID'),
                    env('MT_LIVE_SECRET')
                );
            }
        });

        // CQ9
        $this->app->bind(CQ9::class, function ($app) {
            if (env('APP_ENV') == 'local' || env('APP_ENV') == 'staging') {
                return new CQ9(
                    env('CQ9_STAGING_API_URL'),
                    env('CQ9_STAGING_ID'),
                    env('CQ9_STAGING_SECRET')
                );
            } elseif (env('APP_ENV') == 'production') {
                return new CQ9(
                    env('CQ9_LIVE_API_URL'),
                    env('CQ9_LIVE_ID'),
                    env('CQ9_LIVE_SECRET')
                );
            }
        });

        // JK
        $this->app->bind(JK::class, function ($app) {
            if (env('APP_ENV') == 'local' || env('APP_ENV') == 'staging') {
                return new JK(
                    env('JK_STAGING_API_URL'),
                    env('JK_STAGING_AGENT_ID'),
                    env('JK_STAGING_AGENT_KEY')
                );
            } elseif (env('APP_ENV') == 'production') {
                return new JK(
                    env('JK_LIVE_API_URL'),
                    env('JK_LIVE_AGENT_ID'),
                    env('JK_LIVE_AGENT_KEY')
                );
            }
        });

        // JILI
        $this->app->bind(JILI::class, function ($app) {
            if (env('APP_ENV') == 'local' || env('APP_ENV') == 'staging') {
                return new JILI(
                    env('JILI_STAGING_API_URL'),
                    env('JILI_STAGING_AGENT_ID'),
                    env('JILI_STAGING_AGENT_KEY')
                );
            } elseif (env('APP_ENV') == 'production') {
                return new JILI(
                    env('JILI_LIVE_API_URL'),
                    env('JILI_LIVE_AGENT_ID'),
                    env('JILI_LIVE_AGENT_KEY')
                );
            }
        });

        // GSC
        $this->app->bind(GSC::class, function ($app) {
            if (env('APP_ENV') == 'local' || env('APP_ENV') == 'staging') {
                return new GSC(
                    env('GSC_LIVE_API_URL'),
                    env('GSC_LIVE_OPERATOR_CODE'),
                    env('GSC_LIVE_AGENT_KEY')
                );
            } elseif (env('APP_ENV') == 'production') {
                return new GSC(
                    env('GSC_LIVE_API_URL'),
                    env('GSC_LIVE_OPERATOR_CODE'),
                    env('GSC_LIVE_AGENT_KEY')
                );
            }
        });

        // 918KISS
        $this->app->bind(KISS::class, function ($app) {
            return new KISS(
                env('KISS_LIVE_API_URL'),
                env('KISS_LIVE_API_URL_2'),
                env('KISS_LIVE_AUTH_CODE'),
                env('KISS_LIVE_SECRET'),
            );
        });

        // MEGA
        $this->app->bind(MEGA::class, function ($app) {
            return new MEGA(
                env('MEGA_LIVE_API_URL'),
                env('MEGA_LIVE_SN'),
                env('MEGA_LIVE_SECRET'),
                env('MEGA_LIVE_AGENT_ID'),
            );
        });

        // PUSSY888
        $this->app->bind(PUSSY::class, function ($app) {
            return new KISS(
                env('PUSSY_LIVE_API_URL'),
                env('PUSSY_LIVE_API_URL_2'),
                env('PUSSY_LIVE_AUTH_CODE'),
                env('PUSSY_LIVE_SECRET'),
            );
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
