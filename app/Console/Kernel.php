<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [];

    /**
     * Define the application's command schedule.
     *
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('angpau:fortune')->dailyAt('00:00');

        $schedule->command('crawl:pos-withdraw-auto')->everyFiveMinutes();
        $schedule->command('crawl:pos-reload-auto')->everyFiveMinutes();
        // $schedule->command('wwj:game-tournament')->everyThirtyMinutes();
        $schedule->command('pos:crawl-currency-detail')->hourly();

        // $schedule->command('broadcast:live-transactions')->cron('*/5 * * * * *')->withoutOverlapping();
        $schedule->command('add:dummy-spin-log')->everyFiveMinutes();

        // $schedule->command('pump:user-data')->everyFiveMinutes();
        // $schedule->command('pump:deposit-data')->everyFifteenMinutes();
        // $schedule->command('pump:wallet-transfer-data')->everyFifteenMinutes();

        // Get Turnover Data
        $schedule->command('crawl:user-turnover-summary')->dailyAt('11:00');

        // Get referral data
        $schedule->command('crawl:user-referral')->dailyAt('12:00');

        // Distribute VIP
        // $schedule->command('vip:deposit')->dailyAt('00:45');
        $schedule->command('vip:turnover')->dailyAt('13:00');
        // $schedule->command('vip:profit-loss')->dailyAt('02:00');

        // $schedule->command('pos:check-connection')->everyTenMinutes();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }

    /**
     * Get the timezone that should be used by default for scheduled events.
     *
     * @return \DateTimeZone|string|null
     */
    protected function scheduleTimezone()
    {
        return env('APP_TIMEZONE'); // need confirm on this
    }
}
