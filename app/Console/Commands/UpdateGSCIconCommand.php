<?php

namespace App\Console\Commands;

use App\Models\Services;
use Illuminate\Console\Command;

class UpdateGSCIconCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'gsc:update-icon';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Services::where('product_id', 2006)->each(function ($query) {
            // replace icon url
            $query->update([
                'icon' => str_replace('/FR/', '/PT/', $query->icon)
            ]);
        });

        return Command::SUCCESS;
    }
}
