<?php

namespace App\Console\Commands;

use App\Traits\DateTrait;
use App\Traits\DecimalTrait;
use App\Traits\GenerateNumberTrait;
use App\Models\Credit;
use App\Models\CreditTransaction;
use Carbon\Carbon;
use App\Models\User;
use App\Models\UserCard;
use App\Models\ExTransfer;
use App\Models\Store;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class WalletTransferDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pump:wallet-transfer-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $storeIds = Store::where('is_dummy', 1)->get()->pluck('store_id')->toArray();
        $checkUser = User::where('user_type', User::$userType['user-account'])->whereIn('store_id', $storeIds)->inRandomOrder()->first();

        $user_card = UserCard::where('status', 1)->where('user_id', $checkUser->id)->first();

        if (!isset($user_card)) {
            $user = $this->createCard([
                'user_id' => $checkUser->id,
                'store_id' => $checkUser->store_id,
                'phone_no' => $checkUser->phone_no
            ]);
            $user_card = UserCard::where('status', 1)->where('user_id', $user->id)->first();
        } else {
            $user = User::where('id', $user_card->user_id)->where('store_id', $user_card->store_id)->first();
        }

        $amountArr = [50, 80, 100, 500, 600, 1000, 1200, 1500, 2000, 3000, 5000, 8000, 10000, random_int(10, 1000)];
        $amount = $amountArr[array_rand($amountArr)];
        $randomSeconds = rand(-300, 300);

        $exTransfer1 = $this->add([
            'user_id' => $user->id,
            'type' => ExTransfer::$type['in'],
            'amount' => $amount,
            'credit_id' => 1000,
            'card_id' => $user_card->id,
            'wallet_data' => [
                'amount' => $amount,
                'balance' => $amount,
                'credit' => 1000,
                'credit_type' => 'myr-credit',
            ],
        ]);
        // $exTransfer1->created_at = Carbon::now()->addSeconds($randomSeconds);
        // $exTransfer1->save();

        $amountArr = [50, 80, 100, 500, 600, 1000, 1200, 1500, 2000, 3000, 5000, 8000, 10000, random_int(10, 1000)];
        $amount = $amountArr[array_rand($amountArr)];

        $exTransfer2 = $this->add([
            'user_id' => $user->id,
            'type' => ExTransfer::$type['out'],
            'amount' => $amount,
            'credit_id' => 1000,
            'card_id' => $user_card->id,
            'wallet_data' => [
                'amount' => $amount,
                'balance' => $amount,
                'credit' => 1000,
                'credit_type' => 'myr-credit',
            ],
        ]);

        // $exTransfer2->created_at = Carbon::now()->addSeconds($randomSeconds);
        // $exTransfer2->save();

        return Command::SUCCESS;
    }

    public function add(array $params = [])
    {
        $datetime = date('Y-m-d H:i:s');
        $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;
        $userId = $params['user_id'] ?? null;
        $walletData = $params['wallet_data'] ?? null;
        $creditId = $params['credit_id'] ?? null;
        $cardId = $params['card_id'] ?? null;
        $amount = $params['amount'] ?? null;
        $type = $params['type'] ?? null;
        // $password = $params['password'] ?? null;

        $trxId = GenerateNumberTrait::generateReferenceNo(ExTransfer::query(), 'transaction_id', null);

        $userCard = UserCard::find($cardId);
        $credit = Credit::find($creditId);

        $insertData = [
            'user_id' => $userId,
            'transaction_id' => $trxId,
            'credit_id' => $creditId ?? null,
            'type' => $type ?? null,
            'card_id' => $cardId ?? null,
            'amount' => DecimalTrait::setDecimal($amount, 2) ?? null,
            'receivable_amount' => $amount ?? null,
            'status' => ExTransfer::$status['confirmed'],
            "belong_id" => $belongId,
            "created_at" => $datetime,
            // "password" => $password,
        ];

        return DB::transaction(function () use ($insertData, $walletData, $userCard, $trxId, $datetime, $credit) {
            $internalId = User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;

            $exTransfer = ExTransfer::create($insertData);
            if (!$exTransfer) {
                throw new \Exception('Ex Transfer Failed');
            }

            $randomSeconds = rand(-300, 300);
            $exTransfer->created_at = Carbon::now()->addSeconds($randomSeconds);
            $exTransfer->save();

            $jobData = [
                'ex_transfer_id' => $exTransfer->id,
                'remark' => $trxId,
                'curl_type' => 'withdraw',

                'ucId' => $userCard->id,
                'storeId' => $userCard->store_id,
                'cardId' => $userCard->card_id,
                // 'password' => $insertData['password'],
                'amount' => $insertData['amount'],
            ];

            if (array_search($insertData['type'], ExTransfer::$type) == 'in') {
                CreditTransaction::insertTransaction($insertData['user_id'], $internalId, $insertData['user_id'], $walletData['credit_type'], $insertData['amount'], "ex-transfer-out", $insertData['belong_id'], $insertData['belong_id'], null, $datetime, $trxId, null, null, $exTransfer->id, null, false);
                $jobData['curl_type'] = "deposit";
                $jobData['amount'] = $insertData['amount'];
            }

            // $job = new \App\Jobs\ProcessMerchant(json_encode($jobData));
            // // dispatch($job)->onQueue('merchant');
            // dispatch_sync($job);

            $status = array_search($insertData['status'], ExTransfer::$status);
            return [
                "status" => $status,
                "status_display" => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                "transaction_id" => $insertData['transaction_id'],
                "amount" => $insertData['amount'],
                "credit_name" => $credit->name,
                "credit_display" => Lang::has('lang.' . $credit->name) ? Lang::get('lang.' . $credit->name) : $credit->name,
                "date_time" => DateTrait::dateFormat($datetime),
            ];
        });
    }

    public function createCard(array $params = [])
    {
        $card_no = 'F' . $params['store_id'] . random_int(0001, 9999);
        $insertUserCard['user_id'] = $params['user_id'];
        $insertUserCard['store_id'] = $params['store_id'];
        $insertUserCard['phone_no'] = $params['phone_no'] ?? null;

        $insertUserCard['card_id'] = $card_no;
        $insertUserCard['card_name'] = 'FCard001';
        $insertUserCard['card_serial_no'] = $card_no;
        $insertUserCard['member_balance'] = 0;
        $insertUserCard['accumulated_balance'] = 0;
        $insertUserCard['member_status'] = 0;

        $insertUserCard['card_image'] = $data['card_image'] ?? null;
        $insertUserCard['branch_code'] = $data['branch_code'] ?? null;

        $insertUserCard['status'] = UserCard::$status['active'];

        UserCard::updateOrCreate([
            "user_id" => $params['user_id'],
            "store_id" => $params['store_id'],
        ], $insertUserCard);

        return UserCard::where('user_id', $params['user_id'])->where('store_id', $params['store_id'])->first();
    }
}
