<?php

namespace App\Console\Commands;

use App\Models\GameCategoryProvider;
use App\Models\GameSetting;
use App\Models\Services;
use App\Services\GameProvider\JILI;
use Illuminate\Console\Command;

class CrawlJILIGameList extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'crawl:jili-game-list';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $product_id = JILI::$productId;
        $jili = resolve(JILI::class);
        $gameList = $jili->getGameList()['data'];

        foreach ($gameList as $game) {
            $gameSettingInsert = [
                'product_id' => $product_id,
                'game' => $game['name']['en-US'],
                'game_id' => $game['GameId'],
                'game_category' => $game['GameCategoryId'],
            ];

            $gameSetting = GameSetting::firstOrCreate($gameSettingInsert, $gameSettingInsert);

            $categoryProvider = GameCategoryProvider::find(
                JILI::getGameCategoryProviderIdByGameId($game['GameCategoryId'], $game['GameId'])
            );

            $servicesInsert = [
                'name' => $game['name']['en-US'],
                'product_id' => $product_id,
                'game_setting_id' => $gameSetting->id,
                'game_category_id' => $categoryProvider->game_category_id,
                'game_provider_id' => $categoryProvider->game_provider_id,
                'game_category_provider_id' => $categoryProvider->id,
                'icon' => $game['Image'],
                'wallet_type' => 'Main',
                'status' => Services::$status['active'],
            ];

            Services::firstOrCreate($servicesInsert, $servicesInsert);
        }

        return Command::SUCCESS;
    }
}
