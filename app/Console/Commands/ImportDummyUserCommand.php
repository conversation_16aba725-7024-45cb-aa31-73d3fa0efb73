<?php

namespace App\Console\Commands;

use App\Models\Credit;
use App\Models\CreditTransaction;
use App\Models\ExTransfer;
use App\Models\Product;
use App\Models\Services;
use App\Models\User;
use App\Models\UserProduct;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ImportDummyUserCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:dummy-user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Dummy User List';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        define('MODULE', 'admin');
        $service = Services::where('name', 'Buaya')->first();
        $service_id = $service->id;
        $product_id = $service->product_id;

        $users = [
            [
                "store_id" => "3",
                "username" => "swudelandu",
                "phone_number" => "60-*********",
                "amount" => "800"
            ],
        ];

        foreach ($users as $item) {
            // Step 1: Create User Account
            User::addUser([
                'country_id' => 129,
                'name' => $item['username'],
                'password' => 'Qwer1234',
                'password_confirmation' => 'Qwer1234',
                'phone_no' => $item['phone_number'],
                'store_id' => $item['store_id'],
                'preferred_language' => 'en',
                'dial_code' => '60',
                'is_dummy' => true,
            ]);

            sleep(1);

            $user = User::where('phone_no', $item['phone_number'])->where('username', $item['username'])->first();
            $user_id = $user->id;
            $amount = (int)$item['amount'];

            // Step 2: Adjustment In
            CreditTransaction::adjustment([
                'user_id' => $user_id,
                'amount' => $amount,
                'credit_id' => 1000,
                'type' => 'in',
            ]);

            Log::info('User Id: ' . $user->username . ' Adjustment In: ' . $amount);

            // Step 3: Access Game
            $product = Product::with([
                'productSetting' => function ($q) {
                    $q->where('name', 'hasWalletList');
                    $q->where('value', '1');
                },
            ])
                ->where('id', $product_id)
                ->where('status', Product::$status['active'])
                ->first() ?? null;

            $userProduct = UserProduct::with(['product', 'user'])
                ->where('user_id', $user_id)
                ->where('product_id', $product_id)
                ->first();

            // Deposit Fund
            ExTransfer::transferIn([
                'product_data' => $product,
                'wallet_data' => [],
                'exMemberId' => $userProduct->member_id,
                'wallet_type' => 'Main',
                'amount' => $amount,
                'credit_id' => 1000,
                'product_name' => $product->name,
                'product_id' => $product_id,
                'user_id' => $user_id,
                'credit_type' => Credit::first()->type,
            ]);

            Log::info('User Id: ' . $user->username . ' Deposit: ' . $amount);

            // Login to the Game
            UserProduct::productAutoLogin([
                'user_id' => $user_id,
                'product_id' => $product_id,
                'service_id' => $service_id,
                'balance' => $amount,
            ]);

            Log::info('User Id: ' . $user->username . ' Login: ' . $amount);
        }

        return Command::SUCCESS;
    }
}
