<?php

namespace App\Console\Commands;

use App\Jobs\ProcessPosReload;
use App\Models\Store;
use App\Models\Telegram;
use App\Models\UserCard;
use App\Models\UserCardLog;
use App\Models\UserTicketReward;
use App\Traits;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CrawlPostCardReloadCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'crawl:pos-reload';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // operationType = 1 => reload
        // operationType = 2 => withdraw

        // $dates = [];
        // for ($i = 1; $i <= 8; $i++) {
        //     $dates[] = ($i < 10) ? '2024-12-' . '0' . $i : '2024-12-' . $i;
        // }

        // $dates[] = Carbon::now()->subDay()->format('Y-m-d');

        // foreach ($dates as $date) {
        //     $stores = Store::where('status', 1)->get()->pluck('store_id')->toArray();
        //     foreach ($stores as $store) {
        //         $this->process($date, $store);
        //         sleep(1);

        //         $this->info('Store : ' . $store . '. Start At : ' . $date);
        //     }
        // }

        $store = Store::where('store_id', 30)->first();
        $this->process('2025-02-07', $store);

        return Command::SUCCESS;
    }

    public function process($date, $store)
    {
        $page = 1;
        $type = 'pos_reload';
        $operationType = 1;
        $limit = 1000;

        $curlParams = [
            'storeId' => $store['store_id'],
            'page' => $page,
            'operationType' => $operationType,
            'pageSize' => $limit,
            'startDate' => $date . 'T00:00:00.000Z',
            'endDate' => $date . 'T23:59:59.999Z',
        ];

        $result = Traits\SonicTrait::post($curlParams, $type, true);
        if ($result['status']) {

            foreach ($result['data']['list'] as $eachData) {
                $user_id = UserCard::where(
                    'card_serial_no',
                    $eachData['member_card_no'] ?? null
                )->first()->user_id ?? null;

                $res = UserCardLog::updateOrCreate([
                    'store_id' => $store['store_id'],
                    'user_id' => $user_id,
                    'terminal_serial' => $eachData['terminal_serial'] ?? null,
                    'terminal_address' => $eachData['terminal_address'] ?? null,
                    'member_card_id' => $eachData['member_card_id'] ?? null,
                    'member_card_no' => $eachData['member_card_no'] ?? null,
                    'member_phone' => $eachData['member_phone'] ?? null,
                    'date_time' => $eachData['date_time'],
                    'transaction_at' => Carbon::parse($eachData['date_time'])->format('Y-m-d H:i:s') ?? null,
                    'member_balance' => Traits\DecimalTrait::setDecimal($eachData['member_balance'] ?? 0),
                    'operation_qty' => Traits\DecimalTrait::setDecimal($eachData['operation_qty'] ?? 0),
                    'latest_member_balance' => Traits\DecimalTrait::setDecimal($eachData['latest_member_balance'] ?? 0),
                ], [
                    'store_id' => $store['store_id'],
                    'user_id' => $user_id,
                    'terminal_serial' => $eachData['terminal_serial'] ?? null,
                    'terminal_address' => $eachData['terminal_address'] ?? null,
                    'member_card_id' => $eachData['member_card_id'] ?? null,
                    'member_card_no' => $eachData['member_card_no'] ?? null,
                    'member_phone' => $eachData['member_phone'] ?? null,
                    'operation_type' => $operationType,
                    'date_time' => $eachData['date_time'],
                    'transaction_at' => Carbon::parse($eachData['date_time'])->format('Y-m-d H:i:s') ?? null,
                    'member_balance' => Traits\DecimalTrait::setDecimal($eachData['member_balance'] ?? 0),
                    'operation_qty' => Traits\DecimalTrait::setDecimal($eachData['operation_qty'] ?? 0),
                    'latest_member_balance' => Traits\DecimalTrait::setDecimal($eachData['latest_member_balance'] ?? 0),
                    'value_type' => $eachData['价值类型'] ?? null,
                    'remark' => $eachData['备注'] ?? null,
                    'status' => $eachData['会员状态'] ?? null,
                ]);

                $inStores = [29, 30, 31, 6, 7, 8, 12];
                if ($eachData['operation_qty'] >= 2000 && in_array($store['store_id'], $inStores)) {
                    $bool = Telegram::cardRechargeOrder([
                        'store' => $store['name'],
                        'card_no' => $eachData['member_card_no'] ?? null,
                        'phone_no' => $eachData['member_phone'] ?? null,
                        'amount' => Traits\DecimalTrait::setDecimal($eachData['operation_qty'] ?? 0),
                        'datetime' => $eachData['date_time'],
                    ]);
                }


                // UserTicketReward::add([
                //     'user_id' => $user_id,
                //     'amount' => $eachData['operation_qty'] ?? 0,
                //     'ref_id' => $res->id,
                // ]);
            }
        }
    }
}
