<?php

namespace App\Console\Commands;

use App\Models\Services;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use <PERSON><PERSON>\LaravelImageOptimizer\Facades\ImageOptimizer;

class CrawlGameImageCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'crawl:game-image';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Services::with('game_provider')->get()->each(function ($service) {
            $url = $service->icon;
            if (!filter_var($service->icon, FILTER_VALIDATE_URL) && str_contains($service->icon, 'cdn.luckydonutshop.com') && str_contains($service->icon, 'playtech')) {
                $provider = preg_replace('/\s+/', '_', str($service->game_provider->name)->lower());
                $name = preg_replace('/\s+/', '_', str($service->name)->lower());

                // $imageContent = file_get_contents(str_replace(' ', '%20', $url));

                // if (!File::exists(public_path('games/' . $provider))) {
                //     File::makeDirectory(public_path('games/' . $provider));
                // }

                // $path = public_path('games/' . $provider . '/' . $name . '.png');
                // file_put_contents($path, $imageContent);

                $service->update(['icon' => 'production/games/' . $provider . '/' . $name . '.png']);
            }
        });

        return Command::SUCCESS;
    }
}
