<?php

namespace App\Console\Commands;

use App\Services\VipMemberService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class DistributeVIPTurnoverCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vip:turnover';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'VIP turnover distribution';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $today = now()->subDay();

        // $dates = [23, 24, 25, 26, 27, 28, 29, 30];

        // foreach ($dates as $date) {
        // $today = '2025-05-' . $date;
        $fromDate = Carbon::parse($today)->format('Y-m-d 00:00:00');
        $toDate = Carbon::parse($today)->format('Y-m-d 23:59:59');

        VipMemberService::addTurnoverRebate([
            'from_datetime' => $fromDate,
            'to_datetime' => $toDate,
        ]);

        sleep(1);
        // }

        return Command::SUCCESS;
    }
}
