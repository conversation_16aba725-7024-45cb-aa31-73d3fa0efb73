<?php

namespace App\Console\Commands;

use App\Models\UserPromotion;
use Illuminate\Console\Command;

class CleanUserPromotionDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'promotion:clean-user-promotion';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // UserPromotion::where('promotion_id', 1000)
        //     ->whereNull('achieved_at')
        //     ->whereNotNull('meta_transaction')
        //     ->each(function ($promotion) {
        //         // Get Last Record from meta_transaction
        //         $meta_transaction = json_decode($promotion->meta_transaction);
        //         if (!empty($meta_transaction)) {
        //             $lastRecord = end($meta_transaction);
        //             $promotion->update([
        //                 'game_return_amount' => $lastRecord->return
        //             ]);
        //         }
        //     });

        UserPromotion::where('promotion_id', 1000)
            ->whereNotNull('meta_transaction')
            ->where('game_return_amount', '>', 0)
            ->where('max_withdraw_amount', '>', 0)
            ->where('burn_amount', 0)
            ->each(function ($promotion) {
                $burn_amount = 0;
                if ($promotion->game_return_amount > $promotion->max_withdraw_amount) {
                    $burn_amount = $promotion->game_return_amount - $promotion->max_withdraw_amount;
                }

                $promotion->update([
                    'burn_amount' => $burn_amount
                ]);
            });

        return Command::SUCCESS;
    }
}
