<?php

namespace App\Console\Commands;

use App\Models\Store;
use App\Models\UserCardLog;
use App\Traits;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CrawlPostCardWithdrawCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'crawl:pos-withdraw';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // operationType = 1 => reload
        // operationType = 2 => withdraw
        $dates = [];
        for ($i = 1; $i <= 8; $i++) {
            $dates[] = ($i < 10) ? '2024-12-' . '0' . $i : '2024-12-' . $i;
        }

        // $dates[] = Carbon::now()->subDay()->format('Y-m-d');

        foreach ($dates as $date) {
            $stores = Store::where('status', 1)->get()->pluck('store_id')->toArray();
            foreach ($stores as $store) {
                sleep(1);
                // ProcessPosReload::dispatch($date, $store);

                $this->process($date, $store);

                $this->info('Store : ' . $store . '. Start At : ' . $date);
            }
        }
    }

    public function process($date, $store)
    {
        $page = 1;
        $type = 'pos_withdraw';
        $operationType = 2;
        $limit = 1000;

        $curlParams = [
            'storeId' => $store,
            'page' => $page,
            'operationType' => $operationType,
            'pageSize' => $limit,
            'startDate' => $date . 'T00:00:00.000Z',
            'endDate' => $date . 'T23:59:59.999Z',
        ];

        $result = Traits\SonicTrait::post($curlParams, $type);
        if ($result['status']) {
            foreach ($result['data']['list'] as $eachData) {
                UserCardLog::updateOrCreate([
                    'store_id' => $store,
                    'terminal_serial' => $eachData['terminal_serial'] ?? null,
                    'terminal_address' => $eachData['terminal_address'] ?? null,
                    'member_card_id' => $eachData['member_card_id'] ?? null,
                    'member_card_no' => $eachData['member_card_no'] ?? null,
                    'member_phone' => $eachData['member_phone'] ?? null,
                    'date_time' => $eachData['date_time'],
                    'transaction_at' => Carbon::parse($eachData['date_time'])->format('Y-m-d H:i:s') ?? null,
                    'member_balance' => Traits\DecimalTrait::setDecimal($eachData['member_balance'] ?? 0),
                    'operation_qty' => Traits\DecimalTrait::setDecimal($eachData['operation_qty'] ?? 0),
                    'latest_member_balance' => Traits\DecimalTrait::setDecimal($eachData['latest_member_balance'] ?? 0),
                ], [
                    'store_id' => $store,
                    'terminal_serial' => $eachData['terminal_serial'] ?? null,
                    'terminal_address' => $eachData['terminal_address'] ?? null,
                    'member_card_id' => $eachData['member_card_id'] ?? null,
                    'member_card_no' => $eachData['member_card_no'] ?? null,
                    'member_phone' => $eachData['member_phone'] ?? null,
                    'operation_type' => $operationType,
                    'date_time' => $eachData['date_time'],
                    'transaction_at' => Carbon::parse($eachData['date_time'])->format('Y-m-d H:i:s') ?? null,
                    'member_balance' => Traits\DecimalTrait::setDecimal($eachData['member_balance'] ?? 0),
                    'operation_qty' => Traits\DecimalTrait::setDecimal($eachData['operation_qty'] ?? 0),
                    'latest_member_balance' => Traits\DecimalTrait::setDecimal($eachData['latest_member_balance'] ?? 0),
                    'value_type' => $eachData['价值类型'] ?? null,
                    'remark' => $eachData['备注'] ?? null,
                    'status' => $eachData['会员状态'] ?? null,
                ]);
            }
        }
    }
}
