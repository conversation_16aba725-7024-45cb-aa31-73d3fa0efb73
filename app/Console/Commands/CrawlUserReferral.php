<?php

namespace App\Console\Commands;

use App\Models\ReferralTier;
use App\Models\TreeSponsor;
use App\Models\User;
use App\Models\UserPromotion;
use App\Models\UserReferral;
use App\Models\UserReferralSummary;
use App\Models\UserTurnoverSummary;
use Illuminate\Console\Command;

class CrawlUserReferral extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'crawl:user-referral';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    private $uplines = [];

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->uplines = [];

        $turnoverSummaries = UserTurnoverSummary::getSummariesGroupByUserId();

        $turnoverSummaries->each(function ($e) {
            $uplineId = TreeSponsor::getUplineIdByUserId($e->user_id);
            $downlineId = $e->user_id;

            $totalAchievedTurnovers = UserPromotion::getTotalAchievedTurnoverByUserId($downlineId);
            $latestBatch = UserReferralSummary::getLatestBatchByUserIdAndDownlineUserId($uplineId, $e->user_id);
            $totalTurnover = $e->total_turnover - $totalAchievedTurnovers;

            array_push($this->uplines, $uplineId);

            UserReferralSummary::create([
                'user_id' => $uplineId,
                'downline_user_id' => $downlineId,
                'total_turnover' => $totalTurnover > 0 ? $totalTurnover : 0,
                'batch' => $latestBatch + 1,
            ]);
        });

        $uniqueUplines = array_unique($this->uplines);

        // Downline turnover referral
        array_map(function ($userId) {
            $isAgent = User::isAgent($userId);
            $claimableAmount = UserReferralSummary::getClaimableAmountByUserId($userId);
            $amount = $claimableAmount * ($isAgent ? UserReferral::$agentMultiplier : UserReferral::$turnoverMultiplier);

            if ($amount > 0 && ! $isAgent) {
                UserReferral::create([
                    'user_id' => $userId,
                    'type' => UserReferral::$type['turnover'],
                    'amount' => $amount,
                ]);
            }
        }, $uniqueUplines);

        $referralTiers = ReferralTier::getReferralTiers();
        $latestTotalTurnoverSumGroups = UserReferralSummary::getLatestTotalDownlineTurnoverGroupByUserId();

        // Tier bonus
        $latestTotalTurnoverSumGroups->each(function ($e) use ($referralTiers) {
            $totalTurnoverSum = $e->total_downline_turnover;
            $userId = $e->user_id;

            $referralTiers->each(function ($e) use ($totalTurnoverSum, $userId) {
                $referral = UserReferral::getUserReferralByUserIdAndReferralTierId($userId, $e->id);
                $isAgent = User::isAgent($userId);

                if (! $isAgent && ! $referral && $totalTurnoverSum >= $e->target_amount) {
                    UserReferral::create([
                        'user_id' => $userId,
                        'referral_tier_id' => $e->id,
                        'type' => UserReferral::$type['tier'],
                        'amount' => $e->reward_amount,
                    ]);
                }
            });
        });

        return Command::SUCCESS;
    }
}
