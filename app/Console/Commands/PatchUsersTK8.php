<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models;

class PatchUsersTK8 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:patchUsersTK8';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch Users to TK8';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->logs('Running Cron ' . $this->signature);

        $this->process();

        $this->logs('End Cron ' . $this->signature);
        $this->logs('=======================================');
    }

    public function process()
    {
        $err = null;
        try {
            Models\User::where('user_type', Models\User::$userType['user-account'])->orderBy('id', 'asc')->get()->map(function ($q) {
                $referralOcMemberId = Models\UserProduct::where('user_id', $q->sponsor_id)->first()->member_id ?? null;
                $phoneAry = explode('-', $q->phone_no);
                $ocAccountNo = str_replace('6', '', $phoneAry[0]) . $phoneAry[1];
                if ($ocAccountNo[0] != 0) {
                    $ocAccountNo = "0" . $ocAccountNo;
                }

                Models\UserProduct::subscribe([
                    'user_id' => $q->id,
                    // 'account' => $ocAccountNo,
                    'account' => env('USER_ACCOUNT_PREFIX') . $q->uuid,
                    "referral" => $referralOcMemberId,
                    // "password" => $q->id . $q->member_id,
                    "password" => $q->uuid,
                    "dial_code" => $phoneAry[0],
                ]);
            });
        } catch (\Throwable $e) {
            $this->logs($e->getMessage());
            $err = $e->getMessage();
        }

        $this->logs('Completed');
    }

    protected function logs($data)
    {
        $this->info(date('Y-m-d H:i:s') . ' : ' . json_encode($data));
    }
}
