<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models;

class PatchDefaultUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:patchDefaultUsers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch Default Users';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->logs('Running Cron '.$this->signature);

        $this->process();

        $this->logs('End Cron '.$this->signature);
        $this->logs('=======================================');
    }

    public function process()
    {
        $err = null;
        try{
            define('MODULE', 'system');
            $firstUser = Models\User::where('name', 'fwuser')->first();
            $store = Models\Store::create([
                'store_id' => '999999',
                'user_id' => $firstUser->id,
                'name' => 'Master',
            ]);
            $country = Models\Country::where('name','malaysia')->first();
            $dParam = [
                "country_id"=> $country->id,
                "store_id"=> '999999',
                "password"=> "ASDBNM567",
                'preferred_language' => 'en',
                "dial_code"=> $country->country_code,
            ];
            $userParam[] = array_merge($dParam, [
                "name"=> "FW01",
                "phone_no"=> "60-12500001",
                "sponsor"=> "fwuser",
            ]);
            $userParam[] = array_merge($dParam, [
                "name"=> "FW02",
                "phone_no"=> "60-12500002",
                "sponsor"=> "FW01",
            ]);
            $userParam[] = array_merge($dParam, [
                "name"=> "FW03",
                "phone_no"=> "60-12500003",
                "sponsor"=> "FW02",
            ]);
            $userParam[] = array_merge($dParam, [
                "name"=> "FW04",
                "phone_no"=> "60-12500004",
                "sponsor"=> "FW03",
            ]);
            $userParam[] = array_merge($dParam, [
                "name"=> "FW05",
                "phone_no"=> "60-12500005",
                "sponsor"=> "FW04",
            ]);
            $userParam[] = array_merge($dParam, [
                "name"=> "FW06",
                "phone_no"=> "60-12500006",
                "sponsor"=> "FW05",
            ]);
            $userParam[] = array_merge($dParam, [
                "name"=> "FW07",
                "phone_no"=> "60-12500007",
                "sponsor"=> "FW06",
            ]);
            $userParam[] = array_merge($dParam, [
                "name"=> "FW08",
                "phone_no"=> "60-12500008",
                "sponsor"=> "FW07",
            ]);
            $userParam[] = array_merge($dParam, [
                "name"=> "FW09",
                "phone_no"=> "60-12500009",
                "sponsor"=> "FW08",
            ]);
            $userParam[] = array_merge($dParam, [
                "name"=> "FW10",
                "phone_no"=> "60-12500010",
                "sponsor"=> "FW09",
            ]);

            // create user
            foreach($userParam as $eachParam){
                Models\User::addUser($eachParam);
            }

            // patch sponsor
            foreach($userParam as $eachParam){
                $sponsor = Models\User::where('name', $eachParam['sponsor'])->first();
                Models\User::where('name', $eachParam['name'])->update(['sponsor_id'=>$sponsor->id]);
            }

            // update default user
            Models\SystemSetting::where('name','defaultReferralUsername')->update(['value'=>'FW10']);
            
        } catch (\Throwable $e) {
            $this->logs($e->getMessage());
            $err = $e->getMessage();
        }

        $this->logs('Completed');
    }

    protected function logs($data)
    {
        $this->info(date('Y-m-d H:i:s').' : '.json_encode($data));
    }
}