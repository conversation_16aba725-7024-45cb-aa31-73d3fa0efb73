<?php

namespace App\Console\Commands;

use App\Models;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class JwtKickUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jwt:kickuser  {token?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To kick certain user.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->logs('Running Cron '.$this->signature);

        $this->process();

        $this->logs('End Cron '.$this->signature);
        $this->logs('=======================================');
    }

    public function process()
    {
        $token = $this->argument('token') ?? null;

        try {
            Auth::guard('users')->setToken($token)->invalidate(true);

        } catch (\Throwable $e) {
            $this->logs($e->getMessage());
            $err = $e->getMessage();
        }

        $this->logs('Completed');
    }

    protected function logs($data)
    {
        $this->info(date('Y-m-d H:i:s').' : '.json_encode($data));
    }
}
