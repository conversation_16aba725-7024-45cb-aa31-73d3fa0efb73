<?php

namespace App\Console\Commands;

use App\Models\Store;
use Illuminate\Console\Command;

class CreateStoreCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:create';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        define('MODULE', 'admin');

        // for ($i = 11; $i <= 11; $i++) {
        //     $invID = str_pad($i, 4, '0', STR_PAD_LEFT);
        //     $name = 'SPT ' . $invID;

        //     $store = Store::create([
        //         'name' => $name,
        //         'store_id' => '2' . $invID,
        //         'is_disable' => 0,
        //         'is_allow_deposit' => 1,
        //         'is_allow_withdraw' => 1,
        //         'is_pos' => 0,
        //         'is_dummy' => 0,
        //         'jk_agent_id' => 'funwallet_01',
        //         'status' => 1,
        //     ]);
        // }

        $i = 1;
        $invID = str_pad($i, 4, '0', STR_PAD_LEFT);

        $store = Store::create([
            'name' => 'SS1234',
            'store_id' => '3' . $invID,
            'is_disable' => 0,
            'is_allow_deposit' => 1,
            'is_allow_withdraw' => 1,
            'is_pos' => 0,
            'is_dummy' => 0,
            'jk_agent_id' => 'funwallet_01',
            'status' => 1,
        ]);

        Store::insertDefaultUser();

        return Command::SUCCESS;
    }
}
