<?php

namespace App\Console\Commands;

use App\Jobs\ProcessPosReload;
use App\Models\Store;
use App\Models\UserCard;
use App\Models\UserCardLog;
use App\Traits;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CrawlPostCardWithdrawAutoCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'crawl:pos-withdraw-auto';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // operationType = 1 => reload
        // operationType = 2 => withdraw

        $latestDate = UserCardLog::orderBy('date_time', 'DESC')->where('operation_type', 2)->first();
        $currentDate = $latestDate && $latestDate->date_time ? Carbon::parse($latestDate->date_time) : null;
        $startDate = '';

        $isDefault = false;
        $endDate = Carbon::now();

        $dates = [];
        if (empty($currentDate)) {

            if ($isDefault) {
                $startDate = Carbon::now()->toDateString() . 'T00:00:00.000Z';
            } else {
                $defaultDate = Carbon::create(2024, 11, 06);

                $dates[] = $defaultDate->format('Y-m-d\TH:i:s.000\Z');

                while ($defaultDate <= $endDate) {
                    $dates[] = $defaultDate->addDay()->format('Y-m-d\TH:i:s.000\Z');
                    if ($defaultDate->isToday()) {
                        break;
                    }
                }
            }
        } else {
            // $time = UserCardLog::whereDate('date_time' , '=' ,Carbon::today())->where('date_time', '<=', $currentDate->format('Y-m-d\TH:i:s.u'))->first();

            // if(!$time){
            //     $startDate = Carbon::now()->toDateString() . 'T00:00:00.000Z';
            // }else{
            //     $startDate = $currentDate->format('Y-m-d\TH:i:s.000\Z');
            // }
            $currentDate->subDay();
            while ($currentDate <= $endDate) {
                $dates[] = $currentDate->addDay()->toDateString() . 'T00:00:00.000Z';
                if ($currentDate->isToday()) {
                    break;
                }
            }
        }

        $stores = Store::where('status', 1)->get()->pluck('store_id')->toArray();

        // Need Combine this 2 function   
        if (!empty($currentDate)) {
            foreach ($dates as $date) {
                foreach ($stores as $store) {
                    $this->process($date, $store);
                    sleep(1);
                    $this->info('Store : ' . $store . '. Start At : ' . $date);
                }
            }
        } else {
            foreach ($isDefault ? [''] : $dates as $date) {
                foreach ($stores as $store) {
                    $this->process($isDefault ? $startDate : $date, $store);
                    sleep(1);
                    $this->info('Store : ' . $store . '. Start At : ' . ($isDefault ? $startDate : $date));
                }
            }
        }

        return Command::SUCCESS;
    }

    public function process($date, $store)
    {
        $page = 1;
        $type = 'pos_withdraw';
        $operationType = 2;
        $limit = 1000;
        $dateOnly = Carbon::parse($date)->toDateString();

        $curlParams = [
            'storeId' => $store,
            'page' => $page,
            'operationType' => $operationType,
            'pageSize' => $limit,
            'startDate' => $date,
            'endDate' => $dateOnly . 'T23:59:59.999Z',
        ];

        $result = Traits\SonicTrait::post($curlParams, $type, true);

        if ($result['status']) {
            foreach ($result['data']['list'] as $eachData) {
                $user_id = UserCard::where(
                    'card_serial_no',
                    $eachData['member_card_no'] ?? null
                )->first()->user_id ?? null;
                UserCardLog::updateOrCreate([
                    'store_id' => $store,
                    'user_id' => $user_id,
                    'terminal_serial' => $eachData['terminal_serial'] ?? null,
                    'terminal_address' => $eachData['terminal_address'] ?? null,
                    'member_card_id' => $eachData['member_card_id'] ?? null,
                    'member_card_no' => $eachData['member_card_no'] ?? null,
                    'member_phone' => $eachData['member_phone'] ?? null,
                    'date_time' => $eachData['date_time'],
                    'transaction_at' => Carbon::parse($eachData['date_time'])->format('Y-m-d H:i:s') ?? null,
                    'member_balance' => Traits\DecimalTrait::setDecimal($eachData['member_balance'] ?? 0),
                    'operation_qty' => Traits\DecimalTrait::setDecimal($eachData['operation_qty'] ?? 0),
                    'latest_member_balance' => Traits\DecimalTrait::setDecimal($eachData['latest_member_balance'] ?? 0),
                ], [
                    'store_id' => $store,
                    'user_id' => $user_id,
                    'terminal_serial' => $eachData['terminal_serial'] ?? null,
                    'terminal_address' => $eachData['terminal_address'] ?? null,
                    'member_card_id' => $eachData['member_card_id'] ?? null,
                    'member_card_no' => $eachData['member_card_no'] ?? null,
                    'member_phone' => $eachData['member_phone'] ?? null,
                    'operation_type' => $operationType,
                    'date_time' => $eachData['date_time'],
                    'transaction_at' => Carbon::parse($eachData['date_time'])->format('Y-m-d H:i:s') ?? null,
                    'member_balance' => Traits\DecimalTrait::setDecimal($eachData['member_balance'] ?? 0),
                    'operation_qty' => Traits\DecimalTrait::setDecimal($eachData['operation_qty'] ?? 0),
                    'latest_member_balance' => Traits\DecimalTrait::setDecimal($eachData['latest_member_balance'] ?? 0),
                    'value_type' => $eachData['价值类型'] ?? null,
                    'remark' => $eachData['备注'] ?? null,
                    'status' => $eachData['会员状态'] ?? null,
                ]);
            }
        }
    }
}
