<?php

namespace App\Console\Commands;

use App\Services\VipMemberService;
use Illuminate\Console\Command;

class DistributeVIPDepositCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vip:deposit';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Distribute VIP Deposit';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        VipMemberService::addDepositPoint([
            'from_datetime' => now()->subDay()->format('Y-m-d') . ' 00:00:00',
            'to_datetime' => now()->subDay()->format('Y-m-d') . ' 23:59:59'
        ]);

        return Command::SUCCESS;
    }
}
