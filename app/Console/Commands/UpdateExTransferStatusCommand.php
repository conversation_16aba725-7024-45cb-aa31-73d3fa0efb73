<?php

namespace App\Console\Commands;

use App\Models\CreditTransaction;
use App\Models\ExTransfer;
use Illuminate\Console\Command;

class UpdateExTransferStatusCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:ex_transfer_status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ExTransfer::where('type', 1)
            ->where('status', 0)
            ->each(function ($item) {
                $exist = CreditTransaction::where('user_id', $item->user_id)
                    ->where('data', $item->transaction_id)->first();

                if ($exist) {
                    $item->update(['status' => 1]);
                }
            });

        return Command::SUCCESS;
    }
}
