<?php

namespace App\Console\Commands;

use App\Jobs\ProcessPosReload;
use App\Models\Store;
use App\Models\Telegram;
use App\Models\UserCard;
use App\Models\UserCardLog;
use App\Models\UserReward;
use App\Models\UserTicketReward;
use App\Traits;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CrawlPostCardReloadAutoCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'crawl:pos-reload-auto';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // operationType = 1 => reload
        // operationType = 2 => withdraw
        $latestDate = UserCardLog::orderBy('date_time', 'DESC')->where('operation_type', 1)->first();
        $currentDate = $latestDate && $latestDate->date_time ? Carbon::parse($latestDate->date_time) : null;
        $startDate = '';

        $isDefault = false;
        $endDate = Carbon::now();

        $dates = [];
        if (empty($currentDate)) {

            if ($isDefault) {
                $startDate = Carbon::now()->toDateString() . 'T00:00:00.000Z';
            } else {
                $defaultDate = Carbon::create(2024, 11, 28);

                $dates[] = $defaultDate->format('Y-m-d\TH:i:s.000\Z');

                while ($defaultDate <= $endDate) {
                    $dates[] = $defaultDate->addDay()->format('Y-m-d\TH:i:s.000\Z');
                    if ($defaultDate->isToday()) {
                        break;
                    }
                }
            }
        } else {
            $currentDate->subDay();
            while ($currentDate <= $endDate) {
                $dates[] = $currentDate->addDay()->toDateString() . 'T00:00:00.000Z';
                if ($currentDate->isToday()) {
                    break;
                }
            }
        }

        $stores = Store::where('status', 1)->get()->toArray();

        // Need Combine this 2 function
        if (!empty($currentDate)) {
            foreach ($dates as $date) {
                foreach ($stores as $store) {
                    $this->process($date, $store);
                    sleep(1);
                    $this->info('Store : ' . $store['store_id'] . '. Start At : ' . $date);
                }
            }
        } else {

            foreach ($isDefault ? [''] : $dates as $date) {
                foreach ($stores as $store) {
                    $this->process($isDefault ? $startDate : $date, $store);
                    sleep(1);
                    $this->info('Store : ' . $store['store_id'] . '. Start At : ' . ($isDefault ? $startDate : $date));
                }
            }
        }

        return Command::SUCCESS;
    }

    public function process($date, $store)
    {
        $page = 1;
        $type = 'pos_reload';
        $operationType = 1;
        $limit = 1000;
        $dateOnly = Carbon::parse($date)->toDateString();
        // $startDate = '';
        // if(Carbon::parse($date)->isToday()){
        //     $latestDate = UserCardLog::orderBy('date_time','DESC')->first();
        //     $parsedDate = $latestDate && $latestDate->date_time ? Carbon::parse($latestDate->date_time)->format('Y-m-d\TH:i:s.000\Z') : null;
        //     if(Carbon::parse($date)->gt($parsedDate)){
        //         $startDate = $dateOnly . 'T00:00:00.000Z';
        //     }else{
        //         $startDate = Carbon::parse($date)->toISOString();
        //     }
        // }

        $curlParams = [
            'storeId' => $store['store_id'],
            'page' => $page,
            'operationType' => $operationType,
            'pageSize' => $limit,
            'startDate' => $date,
            'endDate' => $dateOnly . 'T23:59:59.999Z',
        ];

        $result = Traits\SonicTrait::post($curlParams, $type, true);

        if ($result['status']) {

            foreach ($result['data']['list'] as $eachData) {
                $user_id = UserCard::where('card_serial_no', $eachData['member_card_no'] ?? null)->first()->user_id ?? null;

                $res = UserCardLog::updateOrCreate([
                    'store_id' => $store['store_id'],
                    'user_id' => $user_id,
                    'terminal_serial' => $eachData['terminal_serial'] ?? null,
                    'terminal_address' => $eachData['terminal_address'] ?? null,
                    'member_card_id' => $eachData['member_card_id'] ?? null,
                    'member_card_no' => $eachData['member_card_no'] ?? null,
                    'member_phone' => $eachData['member_phone'] ?? null,
                    'date_time' => $eachData['date_time'],
                    'transaction_at' => Carbon::parse($eachData['date_time'])->format('Y-m-d H:i:s') ?? null,
                    'member_balance' => Traits\DecimalTrait::setDecimal($eachData['member_balance'] ?? 0),
                    'operation_qty' => Traits\DecimalTrait::setDecimal($eachData['operation_qty'] ?? 0),
                    'latest_member_balance' => Traits\DecimalTrait::setDecimal($eachData['latest_member_balance'] ?? 0),
                ], [
                    'store_id' => $store['store_id'],
                    'user_id' => $user_id,
                    'terminal_serial' => $eachData['terminal_serial'] ?? null,
                    'terminal_address' => $eachData['terminal_address'] ?? null,
                    'member_card_id' => $eachData['member_card_id'] ?? null,
                    'member_card_no' => $eachData['member_card_no'] ?? null,
                    'member_phone' => $eachData['member_phone'] ?? null,
                    'operation_type' => $operationType,
                    'date_time' => $eachData['date_time'],
                    'transaction_at' => Carbon::parse($eachData['date_time'])->format('Y-m-d H:i:s') ?? null,
                    'member_balance' => Traits\DecimalTrait::setDecimal($eachData['member_balance'] ?? 0),
                    'operation_qty' => Traits\DecimalTrait::setDecimal($eachData['operation_qty'] ?? 0),
                    'latest_member_balance' => Traits\DecimalTrait::setDecimal($eachData['latest_member_balance'] ?? 0),
                    'value_type' => $eachData['价值类型'] ?? null,
                    'remark' => $eachData['备注'] ?? null,
                    'status' => $eachData['会员状态'] ?? null,
                ]);

                $inStores = [29, 30, 31, 6, 7, 8, 12];
                if (
                    Carbon::parse($eachData['date_time'])->format('Y-m-d H:i:s') >= now()->subMinutes(5)->format('Y-m-d H:i:s')
                    && $eachData['operation_qty'] >= 2000
                    && in_array($store['store_id'], $inStores)
                ) {
                    $bool = Telegram::cardRechargeOrder([
                        'store' => $store['name'],
                        'card_no' => $eachData['member_card_no'] ?? null,
                        'phone_no' => $eachData['member_phone'] ?? null,
                        'amount' => Traits\DecimalTrait::setDecimal($eachData['operation_qty'] ?? 0),
                        'datetime' => $eachData['date_time'],
                    ]);
                }

                UserReward::depositUserReward($user_id, $res->id, $eachData['operation_qty'] ?? 0);
                UserTicketReward::add([
                    'user_id' => $user_id,
                    'amount' => $eachData['operation_qty'] ?? 0,
                    'ref_id' => $res->id,
                    'is_online' => false,
                ]);

                // UserReward::giveFirstTopUpFreeCredit($user_id, false);
            }
        }
    }
}
