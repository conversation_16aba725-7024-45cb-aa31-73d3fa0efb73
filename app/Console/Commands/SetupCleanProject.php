<?php

namespace App\Console\Commands;

use Illuminate\Support\Arr;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use App\Models;
use App\Traits;
use App\Observers;

// Hash::make
class SetupCleanProject extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'setup:cleanProject';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup Clean Project';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(){
        $this->logs('Start cleaning project ' . $this->signature);
        $this->process();
        $this->logs('Done cleaning project ' . $this->signature);
    }

    protected function process(){
        define('MODULE', 'system');
       
        //Truncate Table

        //Internal Account
        $internalAccountAry[] = [
                                "id" => 1,
                                "username" => "creditSales",
                                "name" => "creditSales",
                                "user_type" => Models\User::$userType['internal-account'],
                            ];

        $internalAccountAry[] = [
                                "id" => 2,
                                "username" => "withdrawal",
                                "name" => "withdrawal",
                                "user_type" => Models\User::$userType['internal-account'],
                            ];

        $internalAccountAry[] = [
                                "id" => 3,
                                "username" => "convert",
                                "name" => "convert",
                                "user_type" => Models\User::$userType['internal-account'],
                            ];

        $internalAccountAry[] = [
                                "id" => 4,
                                "username" => "transfer",
                                "name" => "transfer",
                                "user_type" => Models\User::$userType['internal-account'],
                            ];

        $internalAccountAry[] = [
                                "id" => 5,
                                "username" => "bonusPayout",
                                "name" => "bonusPayout",
                                "user_type" => Models\User::$userType['internal-account'],
                            ];

        $internalAccountAry[] = [
                                "id" => 6,
                                "username" => "telexTransfer",
                                "name" => "telexTransfer",
                                "user_type" => Models\User::$userType['internal-account'],
                            ];
        $internalAccountAry[] = [
                                "id" => 7,
                                "username" => "exTransfer",
                                "name" => "exTransfer",
                                "user_type" => Models\User::$userType['internal-account'],
        ];
        
        $internalAccountAry[] = [
                                "id" => 8,
                                "username" => "freeCredit",
                                "name" => "freeCredit",
                                "user_type" => Models\User::$userType['internal-account'],
        ];
        
        //Admin
        $adminAry[] = [
            "name" => "fwAdmin",
            "username" => "fwadmin",
            "email" => "<EMAIL>",
            "phone_no" => "60-********",
            "password" => Hash::make("fwAdmin@988"),
            "activated" => 1,
            "is_master" => 1,
        ];

        //user         
        $userAry[] = [
            "id" => "1000000",
            "member_id" => Traits\GenerateNumberTrait::GenerateMemberID(Models\User::query()),
            "username" => "fwuser",
            "name" => "fwuser",
            "email" => "<EMAIL>",
            "password" => Hash::make('FWMEMBER198'),
            "phone_no" => '60-*********',
            "country_id" => Models\Country::where(['name' => 'malaysia', 'status' => 1])->first()->id ?? 0,
            "sponsor_id" => 0,
            "activated" => 1,
            "currency_id" => Models\Currency::where('iso','MYR')->where('disabled',0)->first()->id ?? 0,
            "transaction_password" => "123456",
        ];

        //credit
        $walletAry[] = [
            "name" => "myr-credit",
            "type" => "myr-credit",
            "display" => [
                "en" => "MYR Wallet",
                "vn" => "MYR Wallet",
                "th" => "MYR Wallet",
                "indo" => "MYR Wallet",
                "cn" => "MYR 钱包",
                "my" => "MYR Wallet",
            ],
            "setting" => [
                "is-wallet" => ["value"=>1,"admin"=>1,"member"=>1],
                "show-transaction-history" => ["value"=>1,"admin"=>1,"member"=>1],
                "is-adjustable" => ["value"=>1,"admin"=>1,"member"=>0],
                "is-transferable" => ["value"=>1,"admin"=>1,"member"=>1],
                "transfer-charge" => ["value"=>0,"admin"=>0,"member"=>0,"reference"=>0],
                "transfer-multiplier" => ["value"=>0.01,"admin"=>1,"member"=>1],
                "min-transfer" => ["value"=>1,"admin"=>1,"member"=>1],
                "is-fundinable" => ["value"=>1,"admin"=>0,"member"=>1],
                "fundin-type" => ["value"=>1,"admin"=>0,"member"=>0, 'reference'=>1],
                "fundin-charge" => ["value"=>0,"admin"=>0,"member"=>0,"reference"=>0],
                "min-fundin" => ["value"=>1,"admin"=>1,"member"=>1,"reference"=>"USD"],
                "fundin-multiplier" => ["value"=>1,"admin"=>0,"member"=>0,"reference"=>""],
                "is-withdrawable" => ["value"=>1,"admin"=>0,"member"=>1],
                "withdrawal-charge" => ["value"=>0,"admin"=>0,"member"=>0,"reference"=>""],
                "withdrawal-multiplier" => ["value"=>1,"admin"=>0,"member"=>0,"reference"=>""],
                "min-withdrawal" => ["value"=>1,"admin"=>0,"member"=>0,"reference"=>"USD"],
                "is-convertible" => ["value"=>0,"admin"=>0,"member"=>0],
            ],
            "code" => "MW",
            "description" => "MYR Wallet",
            "dcm" => 2,
            "rate" => 1,
            "priority" => 1,
        ];

        //product
        /*$productAry[] = [
                            "name" => "mon-investor",
                            "code" => "P001",
                            "type" => "credit",
                            "priority" => "1",
                            "status" => "1",
                            "image_name" => '',
                        ];

        $productAry[] = [
                            "name" => "mon-special",
                            "code" => "P002",
                            "type" => "credit",
                            "priority" => "2",
                            "status" => "1",
                            "image_name" => '',
                        ];

        $productAry[] = [
                            "name" => "mon-loan",
                            "code" => "P003",
                            "type" => "credit",
                            "priority" => "3",
                            "status" => "1",
                            "image_name" => '',
                        ];

        // product setting
        $productSettingAry["mon-investor"] = [
            ["name" => "user-purchase", "value" => 1],
            ["name" => "admin-purchase", "value" => 1],
            ["name" => "expired-period", "value" => "1 year"]
        ];
        $productSettingAry["mon-special"] = [
            ["name" => "user-purchase", "value" => 0],
            ["name" => "admin-purchase", "value" => 1]
        ];
        $productSettingAry["mon-loan"] = [
            ["name" => "user-purchase", "value" => 0],
            ["name" => "admin-purchase", "value" => 0]
        ];*/

        // Bonus 
        $bonusAry[] = [
            "name" => "agent-bonus",
            "bonus_source" => "bonus_value",
            "calculation" => "Instant",
            "payment" => "Instant",
            "table_name" => "bonus_agent",
            "allow_rank_maintain" => "1",
            "rank_type" => "agent-tier",
            "disabled" => "0",
            'display' => [
                            "en" => "Agent Bonus",
                            "vn" => "Agent Bonus",
                            "th" => "Agent Bonus",
                            "indo" => "Agent Bonus",
                            "cn" => "Agent Bonus",
                            "my" => "Agent Bonus",
                        ],
            "type" => "mlm",
            "setting" => [
                
            ],
            "paymentMethod" =>  [
                                    [
                                        "percentage" => "100", 
                                        "credit_type" => "myr-credit", 
                                        "description" => "agent-bonus-payout"
                                    ],
                                ],
        ];

        $rankAry[] = [
            "name" => "agent",
            "priority" => "1",
            "type" => "agent-tier",
            "display" =>    [
                                "en" => "Agent",
                                "vn" => "Agent",
                                "th" => "Agent",
                                "indo" => "Agent",
                                "cn" => "Agent",
                                "my" => "Agent",
                            ],
            "setting" =>    [
                                "agent-bonus" =>   [
                                    "value" => "0", 
                                    "type" => "percentage"
                                ]
            ]
        ];


        // payment method
        /*$paymentMethod[] = [
                            "credit_type" => "manual-banking",
                            "status" => 1,
                            "min_percentage" => 0,
                            "max_percentage" => 100,
                            "payment_type" => "package-register",
                            "type" => "register",
                            "is_external" => 1,
                        ];*/
        // Permission
        $permissions[] = [
            'name' => 'perm-currency-batch-update',
            'level' => 'api',
            'parent_id' => 'perm-currency',
            'api_url' => ["currency/batch-edit"],
            'enLang' => 'Currency Batch Update',
        ];

        $permissions[] = [
            'name' => 'perm-deposit-list',
            'level' => 'menu',
            'api_url' => ["credit/get-deposit-list", "credit/get-deposit-det"],
            'parent_id' => 0,
            'enLang' => 'Deposit Listing',
            'disabled' => 0,
            'master_disabled' => 0
        ];

        $permissions[] = [
            "name" => "perm-update-deposit",
            'level' => 'api',
            'api_url' => ["credit/update-deposit"],
            'parent_id' => 'perm-deposit-list', 
            'enLang' => 'Update Deposit', 
            'disabled' => 0,
            'master_disabled' => 0
        ];

        $permissions[] = [
            'name' => 'perm-service',
            'level' => 'menu',
            'parent_id' => 0,
            'api_url' => ['service/get-service-list'],
            'enLang' => 'Services',
        ];

        $permissions[] = [
            'name' => 'perm-service-action',
            'level' => 'api',
            'parent_id' => 'perm-service',
            'api_url' => ['service/add-service','service/get-service-detail','service/edit-service','service/arrange-service'],
            'enLang' => 'Services Action',
        ];

        $permissions[] = [
            'name' => 'perm-bank',
            'level' => 'menu',
            'parent_id' => 0,
            'api_url' => ['bank/get-bank-list'],
            'enLang' => 'Bank',
        ];

        $permissions[] = [
            'name' => 'perm-bank-action',
            'level' => 'api',
            'parent_id' => 'perm-bank',
            'api_url' => ['bank/add-bank','bank/get-bank-detail','bank/edit-bank'],
            'enLang' => 'Bank Action',
        ];

        $permissions[] = [
            'name' => 'perm-export-report',
            'level' => 'menu',
            'parent_id' => 0,
            'api_url' => ["report/export-report"],
            'enLang' => 'Export Report',
        ];

        $permissions[] = [
            'name' => 'perm-download-export-report',
            'level' => 'api',
            'parent_id' => 'perm-export-report',
            'api_url' => ["report/download-export-report"],
            'enLang' => 'Download',
        ];

        $permissions[] = [
            'name' => 'perm-telex-transfer',
            'level' => 'menu',
            'parent_id' => 0,
            'api_url' => ['tt-transfer/get-telex-transfer-list'],
            'enLang' => 'Telegraphic Transfer',
        ];

        $permissions[] = [
            'name' => 'perm-telex-transfer-action',
            'level' => 'api',
            'parent_id' => 'perm-telex-transfer',
            'api_url' => ['tt-transfer/get-telex-transfer-detail','tt-transfer/edit-telex-transfer'],
            'enLang' => 'Telegraphic Transfer Action',
        ];

        $permissions[] = [
            'name' => 'perm-user-rank-list',
            'level' => 'sub_menu',
            'parent_id' => 'perm-user',
            'api_url' => ['member/user-rank-list','member/user-rank-history'],
            'enLang' => 'Rank List',
        ];

        $permissions[] = [
            'name' => 'perm-user-rank-detail',
            'level' => 'api',
            'parent_id' => 'perm-user-list',
            'api_url' => ['member/user-rank-detail','member/set-user-rank'],
            'enLang' => 'Set User Rank',
        ];

        // System Setting Admin
        // $sysSettingAdmin[] = ["name" => "crypto-charge", "type" => "crypto_charge", "value" => "1", "reference" => "0", "ref_id" => "0", "status" => "active", "created_at" => DB::raw('now()'), "active_at" => DB::raw('now()'), "creator_id" => "0"];

        // System Setting
        // $sysSetting[] = ["name" => "register-free-maintain", "type" => "maintenance", "value" => "2 months", "reference" => "", "created_at" => DB::raw('now()')];
        // $sysSetting[] = ["name" => "smsEnvironment", "type" => "", "value" => "production", "reference" => "", "created_at" => DB::raw('now()')];
        $sysSetting[] = ["name" => "freeCredit", "type" => "", "value" => "{\"myr-credit\": 10}", "reference" => "", "created_at" => DB::raw('now()')];

        // Zone
        $zoneAry[] = [
            'name' => 'west',
            'country' => 'malaysia',
            'display' => [
                'en' => 'West',
                'vn' => 'West',
                'th' => 'West',
                'indo' => 'West',
                'cn' => 'West',
                'my' => 'West',
            ],
        ];

        $zoneAry[] = [
            'name' => 'east',
            'country' => 'malaysia',
            'display' => [
                'en' => 'East',
                'vn' => 'East',
                'th' => 'East',
                'indo' => 'East',
                'cn' => 'East',
                'my' => 'East',
            ],
        ];

        // State
        $stateAry[] = [
            'name' => 'johor',
            'zone' => 'west',
            'display' => [
                'en' => 'Johor',
                'vn' => 'Johor',
                'th' => 'Johor',
                'indo' => 'Johor',
                'cn' => 'Johor',
                'my' => 'Johor',
            ],
        ];

        $stateAry[] = [
            'name' => 'malacca',
            'zone' => 'west',
            'display' => [
                'en' => 'Malacca',
                'vn' => 'Malacca',
                'th' => 'Malacca',
                'indo' => 'Malacca',
                'cn' => 'Malacca',
                'my' => 'Malacca',
            ],
        ];

        $stateAry[] = [
            'name' => 'kedah',
            'zone' => 'west',
            'display' => [
                'en' => 'Kedah',
                'vn' => 'Kedah',
                'th' => 'Kedah',
                'indo' => 'Kedah',
                'cn' => 'Kedah',
                'my' => 'Kedah',
            ],
        ];

        $stateAry[] = [
            'name' => 'kelantan',
            'zone' => 'west',
            'display' => [
                'en' => 'Kelantan',
                'vn' => 'Kelantan',
                'th' => 'Kelantan',
                'indo' => 'Kelantan',
                'cn' => 'Kelantan',
                'my' => 'Kelantan',
            ],
        ];

        $stateAry[] = [
            'name' => 'kuala-lumpur',
            'zone' => 'west',
            'display' => [
                'en' => 'Kuala Lumpur',
                'vn' => 'Kuala Lumpur',
                'th' => 'Kuala Lumpur',
                'indo' => 'Kuala Lumpur',
                'cn' => 'Kuala Lumpur',
                'my' => 'Kuala Lumpur',
            ],
        ];

        $stateAry[] = [
            'name' => 'negeri-sembilan',
            'zone' => 'west',
            'display' => [
                'en' => 'Negeri Sembilan',
                'vn' => 'Negeri Sembilan',
                'th' => 'Negeri Sembilan',
                'indo' => 'Negeri Sembilan',
                'cn' => 'Negeri Sembilan',
                'my' => 'Negeri Sembilan',
            ],
        ];

        $stateAry[] = [
            'name' => 'pahang',
            'zone' => 'west',
            'display' => [
                'en' => 'Pahang',
                'vn' => 'Pahang',
                'th' => 'Pahang',
                'indo' => 'Pahang',
                'cn' => 'Pahang',
                'my' => 'Pahang',
            ],
        ];

        $stateAry[] = [
            'name' => 'penang',
            'zone' => 'west',
            'display' => [
                'en' => 'Penang',
                'vn' => 'Penang',
                'th' => 'Penang',
                'indo' => 'Penang',
                'cn' => 'Penang',
                'my' => 'Penang',
            ],
        ];

        $stateAry[] = [
            'name' => 'perak',
            'zone' => 'west',
            'display' => [
                'en' => 'Perak',
                'vn' => 'Perak',
                'th' => 'Perak',
                'indo' => 'Perak',
                'cn' => 'Perak',
                'my' => 'Perak',
            ],
        ];

        $stateAry[] = [
            'name' => 'perlis',
            'zone' => 'west',
            'display' => [
                'en' => 'Perlis',
                'vn' => 'Perlis',
                'th' => 'Perlis',
                'indo' => 'Perlis',
                'cn' => 'Perlis',
                'my' => 'Perlis',
            ],
        ];

        $stateAry[] = [
            'name' => 'putrajaya',
            'zone' => 'west',
            'display' => [
                'en' => 'Putrajaya',
                'vn' => 'Putrajaya',
                'th' => 'Putrajaya',
                'indo' => 'Putrajaya',
                'cn' => 'Putrajaya',
                'my' => 'Putrajaya',
            ],
        ];

        $stateAry[] = [
            'name' => 'selangor',
            'zone' => 'west',
            'display' => [
                'en' => 'Selangor',
                'vn' => 'Selangor',
                'th' => 'Selangor',
                'indo' => 'Selangor',
                'cn' => 'Selangor',
                'my' => 'Selangor',
            ],
        ];

        $stateAry[] = [
            'name' => 'terengganu',
            'zone' => 'west',
            'display' => [
                'en' => 'Terengganu',
                'vn' => 'Terengganu',
                'th' => 'Terengganu',
                'indo' => 'Terengganu',
                'cn' => 'Terengganu',
                'my' => 'Terengganu',
            ],
        ];

        $stateAry[] = [
            'name' => 'sarawak',
            'zone' => 'east',
            'display' => [
                'en' => 'Sarawak',
                'vn' => 'Sarawak',
                'th' => 'Sarawak',
                'indo' => 'Sarawak',
                'cn' => 'Sarawak',
                'my' => 'Sarawak',
            ],
        ];

        $stateAry[] = [
            'name' => 'sabah',
            'zone' => 'east',
            'display' => [
                'en' => 'Sabah',
                'vn' => 'Sabah',
                'th' => 'Sabah',
                'indo' => 'Sabah',
                'cn' => 'Sabah',
                'my' => 'Sabah',
            ],
        ];

        // Bank
        $bankAry[] = [
            'name' => 'Affin Bank',
            'translationCode' => 'affin-bank',
            'country' => 'malaysia',
            'display' => [
                'en' => 'Affin Bank',
                'vn' => 'Affin Bank',
                'th' => 'Affin Bank',
                'indo' => 'Affin Bank',
                'cn' => 'Affin Bank',
                'my' => 'Affin Bank',
            ],
        ];

        $bankAry[] = [
            'name' => 'Alliance Bank Malaysia',
            'translationCode' => 'alliance-bank-malaysia',
            'country' => 'malaysia',
            'display' => [
                'en' => 'Alliance Bank Malaysia',
                'vn' => 'Alliance Bank Malaysia',
                'th' => 'Alliance Bank Malaysia',
                'indo' => 'Alliance Bank Malaysia',
                'cn' => 'Alliance Bank Malaysia',
                'my' => 'Alliance Bank Malaysia',
            ],
        ];

        $bankAry[] = [
            'name' => 'Ambank Malaysia',
            'translationCode' => 'ambank-malaysia',
            'country' => 'malaysia',
            'display' => [
                'en' => 'Ambank Malaysia',
                'vn' => 'Ambank Malaysia',
                'th' => 'Ambank Malaysia',
                'indo' => 'Ambank Malaysia',
                'cn' => 'Ambank Malaysia',
                'my' => 'Ambank Malaysia',
            ],
        ];

        $bankAry[] = [
            'name' => 'Bank Islam Malaysia',
            'translationCode' => 'bank-islam-malaysia',
            'country' => 'malaysia',
            'display' => [
                'en' => 'Bank Islam Malaysia',
                'vn' => 'Bank Islam Malaysia',
                'th' => 'Bank Islam Malaysia',
                'indo' => 'Bank Islam Malaysia',
                'cn' => 'Bank Islam Malaysia',
                'my' => 'Bank Islam Malaysia',
            ],
        ];

        $bankAry[] = [
            'name' => 'Bank Simpanan Nasional (BSN)',
            'translationCode' => 'bank-simpanan-nasional-bsn',
            'country' => 'malaysia',
            'display' => [
                'en' => 'Bank Simpanan Nasional (BSN)',
                'vn' => 'Bank Simpanan Nasional (BSN)',
                'th' => 'Bank Simpanan Nasional (BSN)',
                'indo' => 'Bank Simpanan Nasional (BSN)',
                'cn' => 'Bank Simpanan Nasional (BSN)',
                'my' => 'Bank Simpanan Nasional (BSN)',
            ],
        ];

        $bankAry[] = [
            'name' => 'CIMB Bank',
            'translationCode' => 'cimb-bank',
            'country' => 'malaysia',
            'display' => [
                'en' => 'CIMB Bank',
                'vn' => 'CIMB Bank',
                'th' => 'CIMB Bank',
                'indo' => 'CIMB Bank',
                'cn' => 'CIMB Bank',
                'my' => 'CIMB Bank',
            ],
        ];

        $bankAry[] = [
            'name' => 'Citibank',
            'translationCode' => 'citi-bank',
            'country' => 'malaysia',
            'display' => [
                'en' => 'Citibank',
                'vn' => 'Citibank',
                'th' => 'Citibank',
                'indo' => 'Citibank',
                'cn' => 'Citibank',
                'my' => 'Citibank',
            ],
        ];

        $bankAry[] = [
            'name' => 'Hong Leong Bank',
            'translationCode' => 'hong-leong-bank',
            'country' => 'malaysia',
            'display' => [
                'en' => 'Hong Leong Bank',
                'vn' => 'Hong Leong Bank',
                'th' => 'Hong Leong Bank',
                'indo' => 'Hong Leong Bank',
                'cn' => 'Hong Leong Bank',
                'my' => 'Hong Leong Bank',
            ],
        ];

        $bankAry[] = [
            'name' => 'HSBC Bank Malaysia',
            'translationCode' => 'hsbc-bank-malaysia',
            'country' => 'malaysia',
            'display' => [
                'en' => 'HSBC Bank Malaysia',
                'vn' => 'HSBC Bank Malaysia',
                'th' => 'HSBC Bank Malaysia',
                'indo' => 'HSBC Bank Malaysia',
                'cn' => 'HSBC Bank Malaysia',
                'my' => 'HSBC Bank Malaysia',
            ],
        ];

        $bankAry[] = [
            'name' => 'Maybank',
            'translationCode' => 'maybank',
            'country' => 'malaysia',
            'display' => [
                'en' => 'Maybank',
                'vn' => 'Maybank',
                'th' => 'Maybank',
                'indo' => 'Maybank',
                'cn' => 'Maybank',
                'my' => 'Maybank',
            ],
        ];

        $bankAry[] = [
            'name' => 'OCBC Bank Malaysia',
            'translationCode' => 'ocbc-bank-malaysia',
            'country' => 'malaysia',
            'display' => [
                'en' => 'OCBC Bank Malaysia',
                'vn' => 'OCBC Bank Malaysia',
                'th' => 'OCBC Bank Malaysia',
                'indo' => 'OCBC Bank Malaysia',
                'cn' => 'OCBC Bank Malaysia',
                'my' => 'OCBC Bank Malaysia',
            ],
        ];

        $bankAry[] = [
            'name' => 'Public Bank',
            'translationCode' => 'public-bank',
            'country' => 'malaysia',
            'display' => [
                'en' => 'Public Bank',
                'vn' => 'Public Bank',
                'th' => 'Public Bank',
                'indo' => 'Public Bank',
                'cn' => 'Public Bank',
                'my' => 'Public Bank',
            ],
        ];

        $bankAry[] = [
            'name' => 'RHB Bank',
            'translationCode' => 'rhb-bank',
            'country' => 'malaysia',
            'display' => [
                'en' => 'RHB Bank',
                'vn' => 'RHB Bank',
                'th' => 'RHB Bank',
                'indo' => 'RHB Bank',
                'cn' => 'RHB Bank',
                'my' => 'RHB Bank',
            ],
        ];

        $bankAry[] = [
            'name' => 'Standard Chartered Bank Malaysia',
            'translationCode' => 'Standard-chartered-bank-malaysia',
            'country' => 'malaysia',
            'display' => [
                'en' => 'Standard Chartered Bank Malaysia',
                'vn' => 'Standard Chartered Bank Malaysia',
                'th' => 'Standard Chartered Bank Malaysia',
                'indo' => 'Standard Chartered Bank Malaysia',
                'cn' => 'Standard Chartered Bank Malaysia',
                'my' => 'Standard Chartered Bank Malaysia',
            ],
        ];

        $bankAry[] = [
            'name' => 'Tabung Haji',
            'translationCode' => 'tabung-haji',
            'country' => 'malaysia',
            'display' => [
                'en' => 'Tabung Haji',
                'vn' => 'Tabung Haji',
                'th' => 'Tabung Haji',
                'indo' => 'Tabung Haji',
                'cn' => 'Tabung Haji',
                'my' => 'Tabung Haji',
            ],
        ];

        $bankAry[] = [
            'name' => 'United Overseas Bank (UOB) Malaysia',
            'translationCode' => 'united-overseas-bank-uob-malaysia',
            'country' => 'malaysia',
            'display' => [
                'en' => 'United Overseas Bank (UOB) Malaysia',
                'vn' => 'United Overseas Bank (UOB) Malaysia',
                'th' => 'United Overseas Bank (UOB) Malaysia',
                'indo' => 'United Overseas Bank (UOB) Malaysia',
                'cn' => 'United Overseas Bank (UOB) Malaysia',
                'my' => 'United Overseas Bank (UOB) Malaysia',
            ],
        ];

        // Cron Setting
        $cronSetting[] = [
            'name' => 'process:processExchangeRate {date?}'
        ];

        $cronSetting[] = [
            'name' => 'process:processBonuses {bonusDate?}'
        ];

        //Role
        $roles[] = [
            "name" => "admin",
            "permissions_ary" => [] //Full Permission just put as empty, else put permission name
        ];

        // Process Query
        // if(isset($walletAry)){
        //     $this->logs('inserting wallet....');
        //     $this->insertWallet($walletAry);
        // }

        // if(isset($bonusAry)){
        //     $this->logs('inserting bonus....');
        //     $this->insertBonus($bonusAry);
        // }

        // if(isset($permissions)){
        //     $this->logs('inserting permission....');
        //     $this->insertPermissions($permissions);
        // }

        if(isset($internalAccountAry)){
            $this->logs('inserting internal account....');
            $this->insertInternalAccount($internalAccountAry);
        }

        // if(isset($productAry)){
        //     $this->logs('inserting product....');
        //     $this->insertProduct($productAry, $productSettingAry ?? []);
        // }

        // if(isset($rankAry)){
        //     $this->logs('inserting rank....');
        //     $this->insertRank($rankAry);
        // }

        // if (isset($paymentMethod)) {
        //     $this->logs('inserting payment method....');
        //     $this->insertPaymentMethod($paymentMethod);
        // }

        if(isset($sysSetting)){
            $this->logs('inserting system setting....');
            $this->insertSysSetting($sysSetting);
        }

        // if(isset($sysSettingAdmin)){
        //     $this->logs('inserting system setting admin....');
        //     $this->insertSysSettingAdmin($sysSettingAdmin);
        // }

        // if(isset($zoneAry)){
        //     $this->logs('inserting zone....');
        //     $this->insertZone($zoneAry);
        // }

        // if(isset($stateAry)){
        //     $this->logs('inserting state....');
        //     $this->insertState($stateAry);
        // }

        // if(isset($bankAry)){
        //     $this->logs('inserting bank....');
        //     $this->insertBank($bankAry);
        // }

        // if (isset($cronSetting)) {
        //     $this->logs('inserting cron setting....');
        //     $this->insertCronSetting($cronSetting);
        // }

        // if (isset($departmentAry)) {
        //     $this->logs('inserting department....');
        //     $this->insertDepartment($departmentAry);
        // }

        // Custom Query
        // Disabled Permissions
        $permissionData = [
            'perm-download',
            'perm-experience-management',
            'perm-vip-experience-summary',
            'perm-bank',
            'perm-telex-transfer',
            'perm-currency',
            'perm-platform-payment-log',
            'perm-bonus-report',
            'perm-schedule',
            
            // sub
            'perm-withdraw-third-party-list',
            'perm-withdraw-third-party-det',
            'perm-withdraw-third-party-update',

            'perm-user-rank-detail',
            'perm-user-rank-list',

        ];
        
        // Disable Parent    
        Models\Permissions::query()
            ->whereIn('name',$permissionData)
            ->update([
                'disabled' => 1,
                'master_disabled' => 1,
            ]);

        // Disable Children
        Models\Permissions::query()
            ->whereIn('parent_id',Models\Permissions::query()->selectRaw('id')->whereIn('name',$permissionData)->pluck('id')->toArray())
            ->update([
                'disabled' => 1,
                'master_disabled' => 1,
            ]);

        // if (isset($roles)) {
        //     $this->logs('inserting role....');
        //     $this->insertRole($roles);
        // }

        // if (isset($staffRoles)) {
        //     $this->logs('inserting staff role....');
        //     $this->insertStaffRole($staffRoles);
        // }

        if (isset($adminAry)) {
            $this->logs('inserting admin....');
            $this->insertAdmin($adminAry);
        }

        // if (isset($staffAry)) {
        //     $this->logs('inserting staff....');
        //     $this->insertStaff($staffAry);
        // }

        if (isset($userAry)) {
            $this->logs('inserting user....');
            $this->insertMember($userAry);
        }

        // to hide function
        $this->logs('hiding info....');
        $this->hide();
    }

    protected function insertInternalAccount($internalAccountAry = []){
        foreach($internalAccountAry as $internalAccountRow){
            $internalAccountRow['user_type'] = Models\User::$userType['internal-account'];
            DB::table("users")->insert($internalAccountRow);
        }
    }

    protected function insertAdmin($adminAry = []){
        foreach($adminAry as $adminRow){
            Models\Admin::create($adminRow);
        }
    }

    protected function insertMember($userAry = []){
        foreach($userAry as $userRow){
            $trxPw = $userRow['transaction_password'];
            unset($userRow['transaction_password']);
            $userID = Models\User::create($userRow);
            Models\UserDetail::changeTransactionPassword(['user_id' => $userID->id,"password"=>$trxPw]);
            $treeSponsorRow = [
                                    "user_id" => $userID->id,
                                    "upline_id" => $userRow["sponsor_id"],
                                    "level" => 0,
                                    "trace_key" => $userID->id,
                                ];
            Models\TreeSponsor::create($treeSponsorRow);

            $userSales = [
                            'user_id' => $userID->id,
                            'sponsor_id' => $userRow["sponsor_id"],
                            'product_id' => 0,
                            'downline_count' => 0,
                            'direct_downline_count' => 0,
                            'own_sales' => 0,
                            'group_sales' => 0,
                            'direct_sales' => 0,
                            'own_product_sales' => 0,
                            'group_product_sales' => 0,
                            'direct_product_sales' => 0,
                        ];
            Models\UserSales::create($userSales);
        }
    }

    protected function insertWallet($walletAry = []){

        foreach ($walletAry as $walletRow) {
            unset($permissionsAry);
            $displayData = $walletRow['display'];
            $walletStg = $walletRow['setting'] ?? [];
            unset($walletRow['display'],$walletRow['setting']);

            $creditRes = Models\Credit::create($walletRow);

            $walletName = $walletRow['name'];
            $displayData['slug'] = $walletRow['name'];
            $displayData['type'] = 'Credit';
            $displayData['created_at'] = DB::Raw("now()");
            DB::table('lang')->updateOrInsert(['slug' => $displayData['slug']],$displayData);
            
            foreach ($walletStg as $stgName => $stgValue) {
                switch ($stgName) {
                    case 'is-transferable':
                        $isTransferable = $stgValue["value"];
                        break;
                    
                    case 'is-withdrawable':
                        $isWithdrawable = $stgValue["value"];
                        break;

                    case 'is-convertible':
                        $isConvertible = $stgValue["value"];
                        break;
                }
                $stgValue['credit_id'] = $creditRes->id;
                $stgValue['name'] = $stgName;
                Models\CreditSetting::create($stgValue);
            }

            //permission
            $permissionsAry[] = [
                                    'name' => 'perm-'.$walletName,
                                    'level' => 'sub_menu',
                                    'parent_id' => 'perm-wallet',
                                    'api_url' => ['credit/balance-list'],
                                    'enLang' => $displayData['en'],
                                    'disabled' => 0,
                                    'master_disabled' => 0,
                                ];

            $permissionsAry[] = [
                                    'name' => 'perm-transaction-'.$walletName,
                                    'level' => 'api',
                                    'parent_id' => 'perm-'.$walletName,
                                    'api_url' => ['credit/transaction-list'],
                                    'enLang' => 'Transaction History',
                                    'disabled' => 0,
                                    'master_disabled' => 0,
                                ];
            
            $permissionsAry[] = [
                                    'name' => 'perm-adjustment-'.$walletName,
                                    'level' => 'api',
                                    'parent_id' => 'perm-'.$walletName,
                                    'api_url' => ['credit/adjustment'],
                                    'enLang' => 'Credit Adjustment',
                                    'disabled' => 0,
                                    'master_disabled' => 0,
                                ];

            $permissionsAry[] = [
                                    'name' => 'perm-transfer-'.$walletName,
                                    'level' => 'api',
                                    'parent_id' => 'perm-'.$walletName,
                                    'api_url' => ['credit/transfer-list','credit/transfer'],
                                    'enLang' => 'Credit Transfer',
                                    'disabled' => $isTransferable == 1 ? 0 : 1,
                                    'master_disabled' => $isTransferable == 1 ? 0 : 1,
                                ];

            $permissionsAry[] = [
                                    'name' => 'perm-convert-'.$walletName,
                                    'level' => 'api',
                                    'parent_id' => 'perm-'.$walletName,
                                    'api_url' => ['credit/convert-list','credit/convert'],
                                    'enLang' => 'Credit Convert',
                                    'disabled' => $isConvertible == 1 ? 0 : 1,
                                    'master_disabled' => $isConvertible == 1 ? 0 : 1,
                                ];

            $permissionsAry[] = [
                                    'name' => 'perm-user-wallet-detail-'.$walletName,
                                    'level' => 'api',
                                    'parent_id' => 'perm-'.$walletName,
                                    'api_url' => ['credit/user-wallet-detail'],
                                    'enLang' => 'User Wallet Detail',
                                    'disabled' => 0,
                                    'master_disabled' => 0,
                                ];
            $this->insertPermissions($permissionsAry);
            $this->logs('Success Generate - '.$walletRow['name']);
        }
    }

    protected function insertPermissions($permissionsAry = []){
        foreach($permissionsAry as $permission){
            $enLang = $permission['enLang'];
            unset($permission['enLang']);

            $parentID = ($permission['parent_id'] != 0) ? (Models\Permissions::where('name',$permission['parent_id'])->first()->id ?? 0) : 0;
            $priority = Models\Permissions::selectRaw('(MAX(priority) + 1) AS priority')->where('parent_id',$parentID)->first()->priority ?? 1;

            $permission['parent_id'] = $parentID ?? 0;
            $permission['priority'] = $priority;
            $permission['api_url'] = json_encode($permission['api_url'] ?? []);
            $permission['disabled'] = $permission['disabled'] ?? 0;
            $permission['master_disabled'] = $permission['master_disabled'] ?? 0;
            $permission['created_at'] = DB::raw('NOW()');
            $permission['level'] = Models\Permissions::$level[$permission['level']];

            DB::table("permissions")->insert($permission);
            
            $lang = [];

            $lang['slug'] = $permission['name'];
            $lang['en'] = $enLang;
            $lang['type'] = "Permissions";

            $langExist = DB::table('lang')
                ->where('slug',$permission['name'])
                ->whereNull('deleted_at')
                ->first();

            if(empty($langExist)){
                DB::table('lang')->insert($lang);
            }else{
                $updateData = [];
                $updateData['en'] = $enLang;
                DB::table('lang')->where('id',$langExist->id)->update($updateData);
            }
        }

    }

    protected function insertBonus($bonusArray = []){
        echo "Start Insert Bonus...\n"; 
        $priority = 1;
        $mainPermissions = DB::table('permissions')->where('name', 'perm-bonus-report')->first();
        if(!isset($mainPermissions) || empty($mainPermissions)){
            $this->insertPermissions([[
                                'name' => 'perm-bonus-report',
                                'level' => 'menu',
                                'parent_id' => 0,
                                'api_url' => [],
                                'enLang' => 'Bonus Report',
                                'disabled' => 0,
                                'master_disabled' => 0,
                            ]]);
            $mainPermissions = DB::table('permissions')->where('name', 'perm-bonus-report')->first();
        }

        $mainPermissionsID = $mainPermissions->id;
        foreach($bonusArray as $bonus){
            echo "Inserting bonus $bonus[name]...\n"; 
            $bonus['priority'] = $priority;
            $displayData = $bonus['display'];
            $bonusSetting = $bonus["setting"];
            $bonusPayment = $bonus["paymentMethod"];

            unset($bonus['display']);
            unset($bonus["setting"]);
            unset($bonus["paymentMethod"]);
            $bonus["created_at"] = DB::Raw("now()");
            $bonusID = DB::table("bonus")->insertGetId($bonus);

            $displayData['slug'] = $bonus['name'];
            $displayData['type'] = 'Bonus';
            $displayData['created_at'] = DB::Raw("now()");
            DB::table('lang')->updateOrInsert(['slug' => $displayData['slug']],$displayData);
            
            // $permissions = [
            //     "name" => "perm-".$bonus["name"]."-report",
            //     'level' => 'sub_menu',
            //     'parent_id' => 'perm-bonus-report',
            //     'api_url' => ["bonus-report/".str_replace("-bonus", "", $bonus["name"])."-report"],
            //     'enLang' => $displayData['en'],
            //     'disabled' => 0,
            //     'master_disabled' => 0,
            // ];

            foreach ($bonusSetting as $bonusSettingValue) {
                if(isset($bonusSettingValue) && !empty($bonusSettingValue)){
                    if(in_array($bonusSettingValue['type'],['Pool','Rank'])){
                        $bonusSettingValue['value'] = Models\Rank::where('name',$bonusSettingValue['value'])->first()->id;
                    }
                    $bonusSettingValue["bonus_id"] = $bonusID;
                    DB::table("bonus_setting")->insert($bonusSettingValue);
                }
            }
            foreach($bonusPayment as $key => $bonusPaymentRow){
                if(isset($bonusPaymentRow) && !empty($bonusPaymentRow)){
                    $bonusPaymentRow["bonus_id"] = $bonusID;
                    DB::table("bonus_payment_method")->insert($bonusPaymentRow);
                }
            }

            if(isset($permissions)){
                $permissionsAry[] = $permissions ?? null;
            }
            $priority++;
        }

        if(!empty($permissionsAry)){
            $this->insertPermissions($permissionsAry);
        }
        echo "Done Insert Bonus...\n"; 
    }

    protected function insertRank($rankAry = []){
        echo "Start Insert Rank...\n"; 
        foreach ($rankAry as $rank) {
            echo "Inserting rank $rank[name]...\n"; 
            $rank['created_at'] = date("Y-m-d H:i:s");
            $displayData = $rank['display'];
            $rankSetting = $rank['setting'];
            unset($rank['display']);
            unset($rank['setting']);

            $displayData['slug'] = $rank['name'];
            $displayData['type'] = 'Rank';
            $displayData['created_at'] = DB::Raw("now()");
            DB::table('lang')->updateOrInsert(['slug' => $displayData['slug']],$displayData);

            DB::table("rank")->insert($rank);
            $rankID = DB::getPdo()->lastInsertId();
            foreach($rankSetting AS $name => $settingAry){
                foreach($settingAry as $settingRow){
                    if(is_array($settingRow)){
                        $insert = $settingRow;
                        $insert['name'] = $name;
                        $insert['rank_id'] = $rankID;
                        $insert['created_at'] = date("Y-m-d H:i:s");
                        DB::table('rank_setting')->insert($insert);
                    }else{
                        $insert = $settingAry;
                        $insert['name'] = $name;
                        $insert['rank_id'] = $rankID;
                        $insert['created_at'] = date("Y-m-d H:i:s");
                        DB::table('rank_setting')->insert($insert);
                        break;
                    }
                }
            }
        }
        echo "Done Insert Rank...\n"; 
    }

    protected function insertPaymentMethod($data = []) {
        Models\PaymentMethod::insert($data);
    }

    protected function insertSysSettingAdmin($data = []) {
        Models\SystemSettingsAdmin::insert($data);
    }

    protected function insertSysSetting($data = []) {
        Models\SystemSetting::insert($data);
    }

    protected function insertProduct($data = [], $settings = [])
    {
        foreach($data as $d) {
            Models\Product::insert($d);
            if (isset($settings[$d['name']])) {
                $product_id = DB::getPdo()->lastInsertId();
                foreach($settings[$d['name']] as $setting) {
                    Models\ProductSetting::insert(array_merge($setting, ["product_id" => $product_id]));
                }
            }
        }
    }

    protected function insertZone($zoneAry = []){
        echo "Start Insert Zone...\n"; 
        $priority = 1;

        $countryRes = Models\Country::query()
            ->where('status',1)
            ->whereNull('deleted_at')
            ->get()
            ->pluck('id','name')
            ->toArray();

        foreach ($zoneAry as $zoneRow) {
            $langData = $zoneRow['display'];
            unset($zoneRow['display']);

            $countryID = isset($countryRes[$zoneRow['country']]) ? $countryRes[$zoneRow['country']] : null;

            if ($countryID != null) {
                $zoneData = [
                    'name' => $zoneRow['name'],
                    'country_id' => $countryID,
                    'status' => Models\Zone::$status['active'],
                    'priority' => $priority,
                ];
                $zoneRes = Models\Zone::create($zoneData);
                $priority += 1;

                $langData['slug'] = $zoneRow['name'];
                $langData['type'] = 'Zone';
                $langData['created_at'] = DB::raw('now()');
                DB::table('lang')->updateOrInsert(['slug' => $langData['slug']],$langData);
            }
        }
        echo "Done Insert Zone...\n"; 
    }

    protected function insertState($stateAry = []){
        echo "Start Insert State...\n"; 
        $priority = 1;

        $zoneRes = Models\Zone::query()
            ->selectRaw('id,name,country_id')
            ->where('status',1)
            ->whereNull('deleted_at')
            ->get()
            ->keyBy('name')
            ->toArray();

        foreach ($stateAry as $stateRow) {
            $langData = $stateRow['display'];
            unset($stateRow['display']);

            $zoneID = isset($zoneRes[$stateRow['zone']]['id']) ? $zoneRes[$stateRow['zone']]['id'] : null;
            $countryID = isset($zoneRes[$stateRow['zone']]['country_id']) ? $zoneRes[$stateRow['zone']]['country_id'] : null;

            if (($zoneID != null) && ($countryID != null)) {
                $stateData = [
                    'name' => $stateRow['name'],
                    'country_id' => $countryID,
                    'zone_id' => $zoneID,
                    'status' => Models\State::$status['active'],
                    'priority' => $priority,
                ];
                $stateRes = Models\State::create($stateData);
                $priority += 1;

                $langData['slug'] = $stateRow['name'];
                $langData['type'] = 'State';
                $langData['created_at'] = DB::raw('now()');
                DB::table('lang')->updateOrInsert(['slug' => $langData['slug']],$langData);
            }
        }
        echo "Done Insert State...\n"; 
    }

    protected function insertBank($bankAry = []){
        echo "Start Insert Bank...\n"; 
        $priority = 1;

        $countryRes = Models\Country::query()
            ->where('status',1)
            ->whereNull('deleted_at')
            ->get()
            ->pluck('id','name')
            ->toArray();

        foreach ($bankAry as $bankRow) {
            $langData = $bankRow['display'];
            unset($bankRow['display']);

            $countryID = isset($countryRes[$bankRow['country']]) ? $countryRes[$bankRow['country']] : null;

            if ($countryID != null) {
                $bankData = [
                    'country_id' => $countryID,
                    'name' => $bankRow['name'],
                    'translation_code' => $bankRow['translationCode'],
                    'status' => Models\Bank::$status['active'],
                    'transfer_status' => Models\Bank::$transferStatus['active'],
                    'priority' => $priority,
                ];
                $bankRes = Models\Bank::create($bankData);
                $priority += 1;

                $langData['slug'] = $bankRow['translationCode'];
                $langData['type'] = 'Bank';
                $langData['created_at'] = DB::raw('now()');
                DB::table('lang')->updateOrInsert(['slug' => $langData['slug']],$langData);
            }
        }
        echo "Done Insert Bank...\n"; 
    }

    protected function insertCronSetting($cronSettingAry = [])
    {
        foreach ($cronSettingAry as $cronSetting) {
            Models\CronSetting::create(["name" => $cronSetting['name']]);
        }
    }

    protected function insertRole($roleAry = []){
        echo "Start Insert Role...\n"; 
        foreach ($roleAry as $roleData) {
            $permissionName =  $roleData['permissions_ary'] ?? null;

            $permissionIds = Models\Permissions::when(!empty($permissionName),function ($q) use ($permissionName){
                                return $q->whereIn('name',$permissionName);
                            })
                            ->where('disabled',0)
                            ->where('master_disabled',0)->pluck('id')->toArray();

            Models\AdminRoles::create([
                "name" => $roleData['name'],
                "permissions_id" => $permissionIds ?? [],
                "status" => Models\AdminRoles::$status['active']
            ]);
        }
        echo "Done Insert Role...\n"; 
    }
    
    protected function hide(){
        $now = date('Y-m-d H:i:s');

        // hide credit
        {
            $hideCreditName = [
                'thb-credit',
                'usdt-credit',
            ];
            Models\Credit::whereIn('name', $hideCreditName)->update(['deleted_at' => $now]);
    
            $hideCreditPerms = [
                'perm-thb-credit',
                'perm-usdt-credit',

                'perm-usdt-deposit-list',
                'perm-usdt-wallet-transaction-list',
                'perm-usdt-transfer-transaction-list',

                'perm-thb-ewallet-deposit-list',
                'perm-thb-wallet-transaction-list',
                'perm-thb-platform-fpay-deposit-list',
                'perm-thb-transfer-transaction-list',
            ];
            // Disable Parent    
            Models\Permissions::query()
                ->whereIn('name',$hideCreditPerms)
                ->update([
                    'disabled' => 1,
                    'master_disabled' => 1,
                ]);
    
            // Disable Children
            Models\Permissions::query()
                ->whereIn('parent_id',Models\Permissions::query()->selectRaw('id')->whereIn('name',$hideCreditPerms)->pluck('id')->toArray())
                ->update([
                    'disabled' => 1,
                    'master_disabled' => 1,
                ]);
        }
        
        // tune MYR credit
        {
            $creditId = Models\Credit::where('name','myr-credit')->first()->id;

            // hide convert function
            Models\CreditSetting::where('credit_id',$creditId)->where('name', 'is-convertible')->update(['value' => 0,'admin' => 0,'member' => 0]);

            // remove telco pin
            $fundinType = implode(',',Arr::only(Models\Deposit::$type,['manual-bank','online-bank','ewallet']));
            Models\CreditSetting::where('credit_id',$creditId)->where('name', 'fundin-type')->update(['reference' => $fundinType]);
        }
    }

    protected function logs($data)
    {
        $this->info(date('Y-m-d H:i:s') . ' : ' . json_encode($data));
    }
}
