<?php

namespace App\Console\Commands;

use App\Models\Store;
use App\Services\VipMemberService;
use Illuminate\Console\Command;

class DistributeVIProfitLossCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vip:profit-loss';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Check Each Store Cut Off Time
        $stores = Store::where('status', 1)
            ->where('is_pos', 1)
            ->get();

        foreach ($stores as $store) {
            VipMemberService::addProfitLossRebate([
                'from_datetime' => now()->subDay()->format('Y-m-d') . ' 00:00:00',
                'to_datetime' => now()->subDay()->format('Y-m-d') . ' 23:59:59',
                'store_id' => $store->id
            ]);
        }

        return Command::SUCCESS;
    }
}
