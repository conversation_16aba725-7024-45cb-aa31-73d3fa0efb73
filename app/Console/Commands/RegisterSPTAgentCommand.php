<?php

namespace App\Console\Commands;

use App\Models\Store;
use App\Models\SystemSetting;
use App\Models\User;
use App\Models\UserDeviceInfo;
use App\Models\UserProduct;
use App\Models\UserSales;
use App\Services;
use App\Traits;
use App\Traits\GenerateNumberTrait;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Lang;

class RegisterSPTAgentCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pump:spt-agent';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // sleep(rand(20, 80));
        define('MODULE', 'app');

        for ($i = 0; $i < 30; $i++) {
            $username = 'SPT Agent_' . str_pad($i + 1, 2, '0', STR_PAD_LEFT);
            $password = 'Qwer1234';
            $storeId = 30006;
            // $storeId = 999999;
            $lastUserId = User::all()->last()->id + 1 ?? 0;
            $params = [
                'name' => str()->snake(str()->lower($username)),
                'password' => $password,
                'phone_no' => '60-125' . str_pad($lastUserId, 8, '0', STR_PAD_LEFT),
                'country_id' => 129,
                'preferred_language' => 'en',
                'store_id' => $storeId,
                'module' => 'admin',
                'dial_code' => '60',
                'is_agent' => true,
            ];

            User::addUser($params);
            // $this->addUser([
            //     'name' => str()->snake(str()->lower($username)),
            //     'password' => Hash::make($password),
            //     'phone_no' => '60-125'.str_pad($lastUserId, 8, '0', STR_PAD_LEFT),
            //     'country_id' => 129 ?? null,
            //     'preferred_language' => 'en',
            //     'store_id' => $storeId ?? null,
            //     'module' => 'admin',
            //     'dial_code' => '60',
            //     'is_agent' => true,
            // ]);
        }

        return Command::SUCCESS;
    }

    public function addUser(array $params = [], $useDefaultReferral = false)
    {
        $isAuth = Auth::check() ?? false;

        // Checking for sponsor id if not exists
        if ($useDefaultReferral) {
            // Get Default Referral
            $defaultReferral = SystemSetting::where('name', 'defaultReferralUsername')->pluck('value', 'name')->toArray() ?? null;

            if (! isset($defaultReferral['defaultReferralUsername']) || empty($defaultReferral['defaultReferralUsername'])) {
                abort(400, Lang::get('lang.invalid-referral-username-error'));
            }

            $sponsorId = User::where('username', $defaultReferral['defaultReferralUsername'])->first()->id ?? null;
            $commPerc = 0;
        } elseif (isset($params['store_id'])) {
            $storeRes = Store::where('store_id', $params['store_id'])->first();
            $sponsorId = $storeRes->user_id;
            $commPerc = 0;
        } else {
            // Get Default Referral
            $defaultReferral = SystemSetting::where('name', 'defaultReferralUsername')->pluck('value', 'name')->toArray() ?? null;

            if (! isset($defaultReferral['defaultReferralUsername']) || empty($defaultReferral['defaultReferralUsername'])) {
                abort(400, Lang::get('lang.invalid-referral-username-error'));
            }

            $sponsorId = User::where('username', $defaultReferral['defaultReferralUsername'])->first()->id ?? null;
            $commPerc = 0;
        }
        if (! $sponsorId) {
            abort(400, json_encode(Lang::get('lang.invalid-referral-username-error')));
        }

        $memberID = Traits\GenerateNumberTrait::GenerateMemberID(User::query());

        // Get Fix Phone No
        $v2AuthorizedUser = json_decode((SystemSetting::where('name', 'v2AuthorizedUser')->first()->value ?? null), true) ?? [];

        $insertUser = [
            'member_id' => $memberID,
            // "username" => str_replace("-","",$params['phone_no']),
            'username' => $params['name'],
            'name' => $params['name'],
            'password' => Hash::make($params['password']),
            'phone_no' => $params['phone_no'],
            'country_id' => $params['country_id'] ?? null,
            'sponsor_id' => $sponsorId,
            'lang' => $params['preferred_language'],
            'store_id' => $params['store_id'] ?? null,
            'activated' => 1,
            'referral_code' => GenerateNumberTrait::generateReferralCode(User::query(), 'referral_code'),
            'is_agent' => $params['is_agent'],
        ];

        return DB::transaction(function () use ($insertUser, $params, $sponsorId, $v2AuthorizedUser) {
            $checkUser = User::where('phone_no', $params['phone_no'])->first();
            if (isset($checkUser) && in_array($checkUser->username, $v2AuthorizedUser)) {
                if (isset($checkUser)) {
                    $checkUser->update(['name' => $insertUser['name'], 'activated' => 1, 'disabled' => 0, 'suspended' => 0, 'fail_login' => 0, 'deleted_at' => null]);
                }
                if (MODULE == 'app') {
                    $loginData = Services\AuthService::login(['phone_no' => $checkUser->phone_no, 'password' => $params['password']], 'users');

                    return $loginData;
                }

                return true;
            }

            // Insert User
            $user = User::create($insertUser);
            if (! $user) {
                abort(400, Lang::get('lang.user-create-error'));
            }

            $insertSponsorTree = Traits\TreeTrait::insertSponsorTree($user->id, $sponsorId);
            if (! $insertSponsorTree) {
                abort(400, Lang::get('lang.user-create-error'));
            }

            // Insert Sales
            $sales = [
                'user_id' => $user->id,
                'sponsor_id' => $sponsorId,
                'module_type' => 'register',
            ];
            $updateSales = UserSales::updateSales($sales, 'package');
            if (! $updateSales) {
                abort(400, Lang::get('lang.user-create-error'));
            }

            // Send SMS if above all success
            /*
                $smsParams = [
                    'phone' => $params['phone_no'] ?? null,
                    'lang' => 'en',
                    'type' => 'register-success',
                    'user_id' => $user->id ?? null,
                ];
                $smsParams = json_encode($smsParams);
                $job = new \App\Jobs\SendSMS($smsParams);
                dispatch($job);
            */

            $referralOcMemberId = UserProduct::where('user_id', $sponsorId)->first()->member_id ?? null;
            $phoneAry = explode('-', $params['phone_no']);
            $ocAccountNo = str_replace('6', '', $phoneAry[0]) . $phoneAry[1];
            if ($ocAccountNo[0] != 0) {
                $ocAccountNo = '0' . $ocAccountNo;
            }

            // if (env('APP_ENV') != 'production'){
            //     $ocAccountNo = $ocAccountNo.$user->member_id;
            // }
            // UserProduct::subscribe([
            //     "user_id" => $user->id,
            //     // "account" => $ocAccountNo,
            //     "account" => env('USER_ACCOUNT_PREFIX') . $user->uuid,
            //     "referral" => $referralOcMemberId,
            //     // "password" => $user->id . $insertUser['member_id'],
            //     "password" => $user->uuid,
            //     "dial_code" => $params['dial_code'],
            // ]);

            // self::getFreeCredit($user->id);

            if (MODULE == 'app') {
                $checkParams = [
                    'user_id' => $params['user_id'] ?? null,
                    'uuid' => $params['uuid'],
                ];
                // $valid = Models\UserDeviceInfo::checkValidDeviceToLogin($checkParams);
                $valid = true;
                if ($valid) {
                    $loginData = Services\AuthService::login(['phone_no' => $user->phone_no, 'password' => $params['password']], 'users');
                    $id = $loginData['data']['user']['id'] ?? null;
                    if (isset($id)) {
                        $params['user_id'] = $loginData['data']['user']['id'] ?? null;
                        UserDeviceInfo::insertUserDInfo($params);
                    }

                    $loginData['access_token'] = $loginData['data']['access_token'] ?? null;

                    return $loginData;
                }
            }

            return true;
        });
    }
}
