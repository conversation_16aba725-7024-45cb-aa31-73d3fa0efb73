<?php

namespace App\Console\Commands;

use App\Models\Credit;
use App\Traits;
use App\Traits\ThirdPartyTrait;
use App\Traits\DecimalTrait;
use App\Traits\DateTrait;
use App\Models\CreditSetting;
use App\Models\CreditTransaction;
use App\Models\CurrencyRate;
use App\Models\Deposit;
use App\Models\DepositDetail;
use App\Models\Store;
use App\Models\User;
use App\Models\UserDevice;
use App\Services\PaymentGateway\FPay;
use App\Services\PaymentGateway\FPayEWallet;
use App\Services\PaymentGateway\FPayTelco;
use App\Services\PaymentGateway\FPayThai;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class PumpDepositDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pump:deposit-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        sleep(rand(1, 50));

        $storeIds = Store::where('is_dummy', 1)->get()->pluck('store_id')->toArray();
        $user = User::where('user_type', User::$userType['user-account'])->whereIn('store_id', $storeIds)->inRandomOrder()->first();
        $amountArr = [50, 80, 100, 500, 600, 1000, 1200, 1500, 2000, 3000, 5000, 8000, 10000];

        if (!isset($user)) {
            return Command::SUCCESS;
        }

        $res = $this->addDeposit([
            'currency' => 'MYR',
            'amount' => $amountArr[array_rand($amountArr)],
            'credit_id' => 1000,
            'type' => 2,
            'user_id' => $user->id,
            'deposit_type' => 'manual-bank',
            'deposit_status' => 'pending'
        ]);

        $deposit = Deposit::where('code', $res['transaction_id'])->first();

        if ($res) {
            // {"order_id":"Pxg2araIcSC1R1KD","amount":"300.0000","currency":"MYR","order_status":"completed","status":true,"charge":"4.2000","token":"902a88bb53d440f5df461161b180b0a3","name":null,"type":"deposit","ccno":null,"mode":null,"payment_type":"QR","username":"Tkash3DS","utr":null}
            $this->updateDeposit([
                'id' => $deposit->id,
                'order_id' => $res['transaction_id'],
                'amount' => $res['amount'],
                'currency' => 'MYR',
                'order_status' => 'completed',
                'charge' => $res['amount'] * 0.02,
                'token' => '902a88bb53d440f5df461161b180b0a3',
                'name' => null,
                'type' => 'deposit',
                'ccno' => null,
                'mode' => null,
                'payment_type' => 'manual-bank',
                'username' => 'Tkash3DS',
                'utr' => null,
                'status' => 'approved'
            ]);

            if ($res['amount'] >= 1000) {
                $deposit->update([
                    'type' => Deposit::$type['online-bank']
                ]);
            } else {
                $types = [2, 4];
                $deposit->update([
                    'type' => $types[array_rand($types)]
                ]);
            }
        }

        return Command::SUCCESS;
    }

    public function addDeposit(array $params = [])
    {
        if (!isset($params['deposit_type']) || !in_array($params['deposit_type'], array_keys(Deposit::$type))) {
            throw new \Exception("Invalid Deposit Type", 400);
        }

        if (!isset($params['deposit_status']) || !in_array($params['deposit_status'], array_keys(Deposit::$status))) {
            throw new \Exception("Invalid Deposit Status", 400);
        }

        $creatorId = isset($params["creator_id"]) ? $params["creator_id"] : 0;
        $creatorType = isset($params["creator_type"]) ? $params["creator_type"] : 'System';
        $depositStatus = $params['deposit_status'] ?? null;
        $userData = User::find($params['user_id']);
        $creditId = $params['credit_id'] ?? null;

        $depositCredit = CreditSetting::with(['credit.currency'])
            ->when(isset($creditId), function ($q) use ($creditId) {
                return $q->where('credit_id', $creditId);
            })
            ->where([
                "name" => array("is-fundinable"),
                "value" => 1,
            ])->first();
        if (empty($depositCredit)) {
            throw new \Exception("Deposit Credit Not Found", 400);
        }

        $creditType = $depositCredit->credit->type;
        $currency = $params['currency'] ?? ($depositCredit->credit->currency->iso ?? null);

        $dateTime = $params['datetime'] ?? date("Y-m-d H:i:s");
        $amount = $params['amount'] ?? 0;
        $remark = $params['remark'] ?? null;
        $party = $params['party'] ?? null;

        $charge = 0;

        // IMPORTANT : if rate change from 1 and charge change, need tune fpay callback for online-bank-telco
        $depositCode = Traits\GenerateNumberTrait::GenerateRandomAlphanumeric(Deposit::query(), 'code') ?? 0;
        switch ($params['deposit_type']) {
            case 'manual-bank':
            case 'thb-manual-bank':
                $rate = CurrencyRate::getCurrencyRate($currency, $currency);
                if (!isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
                    throw new \Exception("Deposit Rete Not Found", 400);
                }

                $convertedAmount = $amount / $rate['deposit_rate'];
                $charge = 0; // temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            case 'online-bank':
                $userPhoneAry = explode('-', $userData->phone_no);
                $fpay = resolve(FPay::class);
                $fpayOrder = $fpay->generateOrder($amount, $currency, null, $userPhoneAry[0] . $userPhoneAry[1], $mode = '', $depositCode);
            case 'online-bank-tk8':
                $params['FPay-order-id'] = $fpayOrder['orderId'] ?? $params['orderId'] ?? null;
                $params['FPay-url'] = $fpayOrder['payUrl'] ?? $params['payUrl'] ?? null;
                $depositCode = $params['FPay-order-id'];

                $rate = CurrencyRate::getCurrencyRate($currency, $currency);
                if (!isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
                    throw new \Exception("Deposit Rete Not Found", 400);
                }

                $convertedAmount = $amount / $rate['deposit_rate'];
                $charge = 0; // temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            case 'crypto':
                $rate = CurrencyRate::getCurrencyRate('USDT', 'USDT');

                $convertedAmount = $amount / $rate['deposit_rate'];

                $charge = 0; // temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            case 'ewallet':
                $userPhoneAry = explode('-', $userData->phone_no);
                $fpay_ewallet = resolve(FPayEWallet::class);
                $fpayOrder = $fpay_ewallet->generateOrder($amount, $currency, null, $userPhoneAry[0] . $userPhoneAry[1], $mode = '', $depositCode);

                $params['FPay-order-id'] = $fpayOrder['orderId'];
                $params['FPay-url'] = $fpayOrder['payUrl'];

                $rate = CurrencyRate::getCurrencyRate($currency, $currency);
                if (!isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
                    throw new \Exception('Deposit Rete Not Found', 400);
                }

                $convertedAmount = ($amount / $rate['deposit_rate']);
                $charge = 0; // Temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            case 'online-bank-telco':
                $userPhoneAry = explode('-', $userData->phone_no);
                $fpayTelco = resolve(FPayTelco::class);
                $fpayOrder = $fpayTelco->generateOrder($amount, $currency, null, $userPhoneAry[0] . $userPhoneAry[1], $mode = '', $depositCode);
                $params['FPay-order-id'] = $fpayOrder['orderId'] ?? $params['orderId'] ?? null;
                $params['FPay-url'] = $fpayOrder['payUrl'] ?? $params['payUrl'] ?? null;
                $depositCode = $params['FPay-order-id'];

                $rate = CurrencyRate::getCurrencyRate($currency, $currency);
                if (!isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
                    throw new \Exception('Deposit Rete Not Found', 400);
                }

                // IMPORTANT : if rate change from 1 and charge change, need tune fpay callback for online-bank-telco
                $convertedAmount = ($amount / $rate['deposit_rate']);
                $charge = 0; // Temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            case 'online-bank-thai':
                $userPhoneAry = explode('-', $userData->phone_no);
                $fpayThai = resolve(FPayThai::class);
                $fpayOrder = $fpayThai->generateOrder($amount, $currency, null, $userPhoneAry[0] . $userPhoneAry[1], $mode = '', $depositCode);
            case 'online-bank-tk8th':
                $params['FPay-order-id'] = $fpayOrder['orderId'] ?? $params['orderId'] ?? null;
                $params['FPay-url'] = $fpayOrder['payUrl'] ?? $params['payUrl'] ?? null;
                $depositCode = $params['FPay-order-id'];

                $rate = CurrencyRate::getCurrencyRate($currency, $currency);
                if (!isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
                    throw new \Exception('Deposit Rete Not Found', 400);
                }

                $convertedAmount = ($amount / $rate['deposit_rate']);
                $charge = 0; // Temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            default:

                break;
        }

        $depositId = NULL;

        $receivableAmount = Traits\DecimalTrait::setDecimal(($convertedAmount - $charge));
        if (!isset($receivableAmount) || $receivableAmount < 0) {
            $receivableAmount = 0;
        }

        $insert = [
            'code' => $depositCode,
            'user_id' => $params['user_id'] ?? 0,
            'type' => Deposit::$type[$params['deposit_type']],
            'reference_id' => $params['reference_id'] ?? 0,
            'credit_type' => $creditType,
            'currency' => $currency,
            'amount' => $amount,
            'rate' => $rate['deposit_rate'],
            'converted_amount' => $convertedAmount,
            'charges' => $charge,
            'receivable_amount' => $receivableAmount,
            'status' => Deposit::$status['pending'],
            'user' => Deposit::$status['pending'],
            'creator_id' =>  $creatorId,
            'creator_type' => $creatorType,
            'created_at' => $dateTime,
            'user_remark' => $remark ?? null,
            'party' => $party ?? null,
        ];

        $depositDetail = $params;
        unset($depositDetail['user_id']);
        unset($depositDetail['deposit_type']);
        unset($depositDetail['deposit_status']);
        unset($depositDetail['reference_id']);
        unset($depositDetail['currency']);
        unset($depositDetail['remark']);
        unset($depositDetail['creator_type']);
        unset($depositDetail['creator_id']);

        DB::transaction(function () use (&$insert, $depositStatus, &$depositId, $dateTime, $depositDetail) {
            $deposit = Deposit::create($insert);
            if (!$deposit) {
                throw new \Exception("Failed add deposit", 400);
            }

            $depositId = $deposit->id ?? NULL;

            if ($depositStatus == 'approved' && !empty($depositId)) {
                $approveParams = [
                    'id' => $depositId,
                    'status' => $depositStatus,
                    'datetime' => $dateTime
                ];

                $updateRes = Deposit::updateDeposit($approveParams);
            }

            if (isset($depositDetail) && !empty($depositDetail)) {
                foreach ($depositDetail as $detailName => $detailValue) {
                    DepositDetail::create(['deposit_id' => $depositId,  'name' => $detailName, 'value' => $detailValue]);
                }
            }
        });

        return  [
            'transaction_id' => $insert['code'] ?? null,
            'reference_no' => $depositDetail['reference_no'] ?? null,
            'remark' => $insert['user_remark'] ?? null,
            'created_at' => $dateTime ? DateTrait::dateFormat($dateTime) : "-",
            'deposit_type' => Lang::has('lang.' . $params['deposit_type']) ? Lang::get('lang.' . $params['deposit_type']) : $params['deposit_type'],
            'amount' => $insert['amount'] ? DecimalTrait::setDecimal($insert['amount']) : 0.00,
            'fpay_url' => $params['FPay-url'] ?? null,
            'fpay_redirect_url' => env('FPAY_REDIRECT_URL') . '?order_id=' . $depositCode,
            'currency' => $currency,
        ];
    }

    public function updateDeposit(array $params = [])
    {
        $dateTime = $params['datetime'] ?? date("Y-m-d H:i:s");
        $updateStatus = $params['status'];

        if (!isset($params['status']) || !in_array($updateStatus, ['approved', 'rejected', 'cancelled'])) {
            throw new \Exception("Invalid Update Status", 400);
        }

        DB::transaction(function () use ($params, $dateTime, $updateStatus, &$depositData) {
            $depositData = Deposit::where('id', $params['id'])->lockForUpdate()->first();
            $depositId = $depositData->id;

            $belongId = $batchId = null;
            if ($depositData->status != Deposit::$status['pending']) {
                throw new \Exception("Record Not In Pending Status", 400);
            }

            $userId = $depositData->user_id ?? 0;
            $receivableAmount = $depositData->receivable_amount ?? 0;
            $charges = $depositData->charges ?? 0;
            $creditType = $depositData->credit_type ?? 0;
            $remark = $params['remark'] ?? null;

            if ($updateStatus == 'approved' && !empty($userId) && $receivableAmount > 0) {
                $internalID = User::select('id')->where('username', 'creditSales')->where('user_type', User::$userType['internal-account'])->first()->id ?? Null;
                if (empty($internalID)) {
                    throw new \Exception("Invalid Internal Account.", 400);
                }
                $belongId = Traits\GenerateNumberTrait::GenerateNewId() ?? 0;
                $batchId = $belongId;
                $creditName = Credit::where('type', $creditType)->orderBy('priority', 'ASC')->first()->name;

                CreditTransaction::insertTransaction($internalID, $userId, $userId, $creditName, $receivableAmount, "deposit", $belongId, $batchId, $remark, $dateTime, null, null, null);
            }

            $res = Deposit::find($depositId)->update([
                'status' => Deposit::$status[$updateStatus],
                'admin_remark' => $remark,
                'belong_id' => $belongId,
                'batch_id' => $batchId,
                'approved_at' => $dateTime,
                'updater_id' => auth()->user()->id ?? 0,
                'created_at' => Carbon::now()->addSeconds(rand(-300, 300)),
            ]);

            // FEAT: User Reward for Daily Checkin
            // if ($updateStatus == 'approved' && !empty($userId) && $receivableAmount > 0) {
            //     UserReward::depositUserReward($userId, $depositId, $receivableAmount);
            //     UserTicketReward::add([
            //         'user_id' => $userId,
            //         'amount' => $receivableAmount,
            //         'ref_id' => $depositId,
            //     ]);
            // }
        });

        if (in_array($updateStatus, ['approved', 'rejected'])) {
            $userID = $depositData->user_id;
            $userDeviceToken = UserDevice::where('user_id', $userID)->where('status', UserDevice::$status['active'])->first();
            if (isset($userDeviceToken)) {
                $users = User::find($userID);
                $lang = $users->lang ?? 'en';
                $templateType = null;
                $template = null;
                switch ($updateStatus) {
                    case 'approved':
                        switch (array_search($depositData->type, Deposit::$type)) {
                            case 'thb-manual-bank':
                                $templateType = 'thbManualDepositApproved';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'online-bank':
                                $templateType = 'onlineBankingDepositApproved';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'manual-bank':
                                $templateType = 'manualDepositApproved';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'crypto':
                                $templateType = 'cryptoDepositApproved';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'ewallet':
                                $templateType = 'ewalletDepositApproved';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'online-bank-thai':
                                $templateType = 'onlineBankingThaiDepositApproved';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                        }
                        break;

                    case 'rejected':
                        switch (array_search($depositData->type, Deposit::$type)) {
                            case 'thb-manual-bank':
                                $templateType = 'thbManualDepositRejected';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'online-bank':
                                $templateType = 'onlineBankingDepositRejected';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'manual-bank':
                                $templateType = 'manualDepositRejected';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'crypto':
                                $templateType = 'cryptoDepositRejected';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'ewallet':
                                $templateType = 'ewalletDepositRejected';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'online-bank-thai':
                                $templateType = 'onlineBankingThaiDepositRejected';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                        }
                        break;
                }
                if (isset($templateType) && isset($template)) {
                    $template['body'] = str_replace('{{amount}}', DecimalTrait::setDecimal($depositData->receivable_amount), $template['body']);
                    $credit = Credit::where('type', $depositData->credit_type)->first();
                    Traits\FirebaseTrait::sendNotification($userDeviceToken->token, $template, ['deposit' => $depositData->id, 'type' => $templateType, 'credit_id' => (int) ($credit->id ?? null)], $userDeviceToken->user_id, ["amount" => DecimalTrait::setDecimal($depositData->receivable_amount)]);
                }
            }
        }

        return true;
    }
}
