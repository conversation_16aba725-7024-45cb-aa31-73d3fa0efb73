<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models;

class PatchUsersCQ9 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:patchUsersCQ9';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->logs('Running Cron ' . $this->signature);

        $this->process();

        $this->logs('End Cron ' . $this->signature);
        $this->logs('=======================================');
    }

    public function process()
    {
        $err = null;
        try {
            Models\User::where('user_type', Models\User::$userType['user-account'])->orderBy('id', 'asc')->get()->map(function ($q) {
                $referralOcMemberId = Models\UserProduct::where('user_id', $q->sponsor_id)->first()->member_id ?? null;

                $productId = 2003;
                if (Models\UserProduct::where('user_id', $q->id)->where('product_id', $productId)->exists()) {
                    return;
                }

                $phoneAry = explode('-', $q->phone_no);
                $ocAccountNo = str_replace('6', '', $phoneAry[0]) . $phoneAry[1];
                if ($ocAccountNo[0] != 0) {
                    $ocAccountNo = "0" . $ocAccountNo;
                }

                Models\UserProduct::subscribe([
                    'user_id' => $q->id,
                    // 'account' => $ocAccountNo,
                    'account' => env('USER_ACCOUNT_PREFIX') . $q->uuid,
                    "referral" => $referralOcMemberId,
                    // "password" => $q->id . $q->member_id,
                    "password" => $q->uuid,
                    "dial_code" => $phoneAry[0],
                    'product_id' => $productId
                ]);
            });
        } catch (\Throwable $e) {
            $this->logs($e->getMessage());
            $err = $e->getMessage();
        }

        $this->logs('Completed');
    }

    protected function logs($data)
    {
        $this->info(date('Y-m-d H:i:s') . ' : ' . json_encode($data));
    }
}
