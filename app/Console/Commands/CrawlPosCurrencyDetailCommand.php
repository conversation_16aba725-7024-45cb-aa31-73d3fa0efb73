<?php

namespace App\Console\Commands;

use App\Models\CurrencyDetail;
use App\Models\PosCrawlLog;
use App\Models\Store;
use App\Models\UserCard;
use App\Traits;
use Carbon\Carbon;
use DateTime;
use Illuminate\Console\Command;

class CrawlPosCurrencyDetailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:crawl-currency-detail';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $stores = Store::where('status', 1)->where('is_pos', 1)->get()->toArray();
        foreach ($stores as $store) {
            $this->process($store);
            $date = now();
            $this->info('Store : ' . $store['name'] . '. Start At : ' . $date);
        }

        return Command::SUCCESS;
    }

    public function process($store)
    {
        $type = 'currency_detail';

        $pos_crawl = PosCrawlLog::where('type', $type)
            ->where('store_id', $store['store_id'])
            ->whereNotNull('end_at')
            ->orderBy('end_at', 'DESC')->first();
        if (!isset($pos_crawl)) {
            $start = Carbon::parse('2024-09-03 00:00:00');
            $end = Carbon::parse('2024-09-03 00:00:00')->addHours(6);

            $startDateTime = $start->format('Y-m-d\TH:i:s.') . substr($start->format('u'), 0, 3) . 'Z';
            $endDateTime = $end->format('Y-m-d\TH:i:s.') . substr($end->format('u'), 0, 3) . 'Z';

            $log = PosCrawlLog::create([
                'type' => 'currency_detail',
                'store_id' => $store['store_id'],
                'start_at' => $start,
                'end_at' => $end,
            ]);
        } else {

            // $isToday = Carbon::parse($pos_crawl['end_at'])->isToday();
            // if ($isToday) {
            //     return;
            // }

            $start = Carbon::parse($pos_crawl['end_at'])->addSeconds(1);
            $end = Carbon::parse($pos_crawl['end_at'])->addHours(1);

            $startDateTime = $start->format('Y-m-d\TH:i:s.') . substr($start->format('u'), 0, 3) . 'Z';
            $endDateTime = $end->format('Y-m-d\TH:i:s.') . substr($end->format('u'), 0, 3) . 'Z';

            $log = PosCrawlLog::create([
                'type' => 'currency_detail',
                'store_id' => $store['store_id'],
                'start_at' => $start,
                'end_at' => $end,
            ]);
        }

        $curlParams = [
            'storeId' => $store['store_id'],
            'page' => 1,
            'pageSize' => 10000,
            'startDate' => $startDateTime,
            'endDate' => $endDateTime,
        ];

        $result = Traits\SonicTrait::post($curlParams, $type, true);
        if ($result['status']) {
            foreach ($result['data'] as $key => $data) {
                $user_card = UserCard::where('card_serial_no', $data['member_card_no'])->first();
                CurrencyDetail::updateOrCreate([
                    'store_id' => $store['id'],
                    'operation_qty' => $data['operation_qty'],
                    'terminal_serial' => $data['terminal_serial'],
                    'machine_name' => $data['machine_name'],
                    'machine_serial' => $data['machine_serial'],
                    'member_card_no' => $data['member_card_no'],
                    'date_time' => $data['date_time'],
                    'member_balance' => $data['member_balance'],
                    'latest_member_balance' => $data['latest_member_balance']
                ], [
                    'user_id' => $user_card?->user_id ?? null,
                    'user_card_id' => $user_card?->id ?? null,
                    'store_id' => $store['id'],
                    'operation_qty' => $data['operation_qty'],
                    'terminal_serial' => $data['terminal_serial'],
                    'machine_name' => $data['machine_name'],
                    'machine_serial' => $data['machine_serial'],
                    'member_card_id' => $data['member_card_id'],
                    'member_card_no' => $data['member_card_no'],
                    'member_phone' => $data['member_phone'],
                    'operation_type' => $data['operation_type'],
                    'operation_type_id' => $data['operation_type_id'],
                    'date_time' => $data['date_time'],
                    'member_balance' => $data['member_balance'],
                    'latest_member_balance' => $data['latest_member_balance'],
                    'transaction_at' => Carbon::parse($data['date_time'])->format('Y-m-d H:i:s')
                ]);

                $log->update([
                    'status' => true,
                    'count' => $data['rowNum']
                ]);
            }
        }
    }
}
