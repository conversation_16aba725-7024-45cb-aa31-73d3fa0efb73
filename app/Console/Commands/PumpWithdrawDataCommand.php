<?php

namespace App\Console\Commands;

use App\Traits\DecimalTrait;
use App\Traits\GenerateNumberTrait;
use App\Http\Resources\ItemsCollection;
use App\Traits\DateTrait;
use App\Traits;
use App\Models\Credit;
use App\Models\CreditTransaction;
use App\Models\Store;
use App\Models\User;
use App\Models\UserBank;
use App\Models\UserDevice;
use App\Models\Withdrawal;
use App\Services\PaymentGateway\FPayPayout;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class PumpWithdrawDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pump:withdraw-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $storeIds = Store::where('is_dummy', 1)->get()->pluck('store_id')->toArray();
        $user = User::where('user_type', User::$userType['user-account'])->whereIn('store_id', $storeIds)->inRandomOrder()->first();
        if (!isset($user)) {
            return Command::SUCCESS;
        }

        $user_bank = UserBank::where('status', 1)->where('user_id', $user->id)->first();
        if (!isset($user_bank)) {
            UserBank::add([
                'user_id' => $user->id,
                'bank_id' => 1,
                'bank_account_holder' => $user->name,
                'bank_account_number' => rand(**********, **********),
            ]);

            $user_bank = UserBank::where('status', 1)->where('user_id', $user->id)->first();
        }

        $amountArr = [50, 80, 100, 500, 600, 1000, 1200, 1500, 2000, 3000, 5000, 8000, 10000];

        (new PumpWithdrawDataCommand())->add([
            'uid' => $user->id,
            'user_id' => $user->id,
            'credit_id' => 1000,
            'channel_id' => $user_bank->id,
            'amount' => $amountArr[array_rand($amountArr)],
            'wallet_data' => [
                "credit_type" => 'myr-credit',
                "processing_fee" => 0,
                "rate" => 1,
                "currency_id" => 5,
            ]
        ]);

        return Command::SUCCESS;
    }

    public static function add($data = [])
    {
        $returnData =  DB::transaction(function () use ($data) {
            $internalID = User::select('id')->where('username', 'withdrawal')->where('user_type', User::$userType['internal-account'])->first()->id ?? Null;
            if (empty($internalID)) {
                abort(400, 'Invalid Internal Account.');
            }

            $dateTime = date('Y-m-d H:i:s');
            $userID = $data['uid'] ?? 0;
            $dateTime = $data['datetime'] ?? date('Y-m-d H:i:s');
            $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;
            $refNo = GenerateNumberTrait::generateReferenceNo(Withdrawal::query(), 'serial_number', NULL, 'bank-withdrawal');
            $amount = $data['amount'];
            $creditId = $data['credit_id'];
            $walletData = $data['wallet_data'];

            $bankDetaill = UserBank::with(['bank'])->where('id', $data['channel_id'])->first();

            // Transaction
            $creditType = $walletData['credit_type'];
            $charge = (empty($walletData['processing_fee']) || !isset($walletData['processing_fee'])) ? 0 : $walletData['processing_fee'];

            if (isset($charge) && $charge > 0) {
                $charge = DecimalTrait::setDecimal($charge);
                CreditTransaction::insertTransaction($userID, $internalID, $userID, $creditType, $charge, "withdrawal-charge", $belongId, $belongId, null, $dateTime, null, null, null, null, null, false);
            }
            $currencyRate = $walletData['rate'];


            $convertAmount = $amount;
            $amountAfterCharge = $amount; // Charge separate from amount logic
            if (isset($currencyRate)) $convertAmount = $amountAfterCharge * $currencyRate;
            else abort(400, json_encode(Lang::get('lang.withdrawal-invalid-currency-rate')));

            if ($amountAfterCharge > 0) {
                CreditTransaction::insertTransaction($userID, $internalID, $userID, $creditType, $amountAfterCharge, "withdrawal-out", $belongId, $belongId, null, $dateTime, null, null, null, null, null, true);
            }

            Withdrawal::create([
                'serial_number' => $refNo,
                'user_id' => $userID,
                'status' => Withdrawal::$status['waiting-approval'],
                'remark' => null,
                'user_bank_id' => $data['channel_id'] ?? null,
                'approved_at' => null,
                'approved_by' => null,
                'updater_id' => $userID,
                'credit_type' => $creditType,
                'amount' => $amount,
                'charges' => $charge,
                'receivable_amount' => $amountAfterCharge,
                'currency_id' => $walletData['currency_id'],
                'currency_rate' => $currencyRate,
                'converted_amount' => $convertAmount,
                'withdrawal_type' => Withdrawal::$withdrawalType['bank'],
                'belong_id' => $belongId,
                'batch_id' => $belongId,
            ]);

            return array_merge([
                'transaction_id' => $refNo,
                'bank_name' => Lang::has('lang.' . $bankDetaill->bank->translation_code) ? Lang::get('lang.' . $bankDetaill->bank->translation_code) : $bankDetaill->bank->translation_code,
                'account_holder_name' => $bankDetaill->account_no,
                'account_holder_number' => $bankDetaill->account_holder,
                "amount" => $amount,
                "charges" => $charge,
                "receivable_amount" => DecimalTrait::setDecimal($amountAfterCharge + $charge), // payable_amount
                "balance" => Credit::getBalance($userID, $creditType), // total balance amouint
                "date_time" => DateTrait::dateFormat($dateTime)
            ], ($extraParams ?? []));
        });

        // $sendTelegram = Telegram::withdrawalOrder($returnData);
        // $returnData['send'] = $sendTelegram ?? null;

        return $returnData;
    }

    public static function updateStatus($data = [])
    {
        $notification = [];
        DB::transaction(function () use ($data, &$notification) {
            $withdrawalIDAry = $data['withdrawal_id_ary'];
            $action = $data['action'];
            $remark = $data['remark'] ?? null;
            $dateTime = $data['datetime'] ?? date('Y-m-d H:i:s');
            $updaterId = auth()->user()->id ?? 0;

            Withdrawal::with('credit', 'userBank')->whereIn('id', $withdrawalIDAry)->get()->map(function ($withdrawalData) use ($data, $action, $remark, $dateTime, $updaterId, &$notification) {
                $withdrawalStatus = isset($withdrawalData->status) ? array_search($withdrawalData->status, Withdrawal::$status) : null;

                switch ($action) {
                    case 'approved':
                        if ($withdrawalData['user_id'] == 1000031) {
                            // TODO: payout services
                            $fpay_payout = resolve(FPayPayout::class);
                            $fpay_payout->generateWithdrawOrder(
                                $withdrawalData->receivable_amount,
                                $withdrawalData->credit->code,
                                $withdrawalData->userBank->bank->ref_bank_id,
                                $withdrawalData->userBank->account_holder,
                                $withdrawalData->userBank->account_no,
                                $withdrawalData->serial_number
                            );
                        }

                        $withdrawalData->update([
                            'remark' => $remark,
                            'status' => Withdrawal::$status[$data['action']],
                            'approved_at' => $dateTime,
                            'approved_by' => $updaterId,
                            'updater_id' => $updaterId,
                        ]);
                        break;

                    case 'pending':
                    case 'waiting-approval':
                        $processingWithdrawal = [
                            'remark' => $remark,
                            'status' => Withdrawal::$status[$action],
                            'updater_id' => $updaterId,
                        ];

                        $withdrawalData->update($processingWithdrawal);
                        break;

                    case 'rejected':
                    case 'cancel':
                        $internalID = User::select('id')->where('username', 'telexTransfer')->where('user_type', User::$userType['internal-account'])->first()->id ?? Null;
                        if (empty($internalID)) {
                            abort(400, 'Invalid Internal Account.');
                        }

                        $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;
                        $creditType   = $withdrawalData->credit_type;

                        $refundRes = CreditTransaction::with(['creditInfo'])->selectRaw('SUM(amount) AS refundAmt, ANY_VALUE(credit_id) AS credit_id, ANY_VALUE(user_id) AS user_id')
                            ->whereIn('subject_type', Arr::only(config('subject'), ['withdrawal-out', 'withdrawal-charge']))
                            ->where('belong_id', $withdrawalData->belong_id)->groupBy('credit_id')->get()->map(function ($q) {
                                return  [
                                    "refundAmt" => $q->refundAmt,
                                    "credit_type" => $q->creditInfo->name,
                                    "user_id" => $q->user_id,
                                ];
                            });

                        foreach ($refundRes as $refundRow) {
                            $creditType = $refundRow['credit_type'];
                            $refundAmt = $refundRow['refundAmt'];
                            $userID = $refundRow['user_id'];

                            if (empty($creditType) || empty($refundAmt) || empty($userID)) {
                                abort(400, 'Failed to update status.');
                            }
                            if ($refundAmt > 0) {
                                CreditTransaction::insertTransaction($internalID, $userID, $userID, $creditType, $refundAmt, "withdrawal-refund", $belongId, $withdrawalData->belong_id, null, $dateTime, null, null, null, null, null);
                            }
                        }

                        $cancelWithdrawal = [
                            'remark' => $remark,
                            'status' => Withdrawal::$status[$action],
                            'updater_id' => $updaterId,
                        ];

                        $withdrawalData->update($cancelWithdrawal);
                        break;
                }

                if (in_array($action, ['pending', 'approved', 'rejected'])) {
                    $userID = $withdrawalData->user_id;
                    $userDeviceToken = UserDevice::where('user_id', $userID)->where('status', UserDevice::$status['active'])->first();
                    if (isset($userDeviceToken)) {
                        $users = User::find($userID);
                        $lang = $users->lang ?? 'en';
                        $templateType = null;
                        $template = null;
                        switch ($action) {
                            case 'pending':
                                $templateType = 'withdrawalProcessing';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;

                            case 'approved':
                                $templateType = 'withdrawalApproved';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;

                            case 'rejected':
                                $templateType = 'withdrawalRejected';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                        }
                        $template['body'] = str_replace('{{serial_no}}', $withdrawalData->serial_number, $template['body']);
                        $notification[] = [
                            'user_id' => $userDeviceToken->user_id,
                            'token' => $userDeviceToken->token,
                            'template' => $template,
                            'data' => ['withdrawal' => $withdrawalData->id, 'type' => $templateType, 'credit_id' => $withdrawalData->credit->id ?? ""],
                            'reference_data' => ["serial_no" => $withdrawalData->serial_number]
                        ];
                    }
                }
            });
        });
        foreach ($notification as $noti) {
            Traits\FirebaseTrait::sendNotification($noti['token'], $noti['template'], $noti['data'], $noti['user_id'], $noti['reference_data']);
        }

        return true;
    }
}
