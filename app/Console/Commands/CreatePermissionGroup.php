<?php

namespace App\Console\Commands;

use App\Models\Permissions;
use Illuminate\Console\Command;

class CreatePermissionGroup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permission-group:new {permission-name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a permission group';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $largestPriority = Permissions::select("priority")->orderByDesc("priority")->limit(1)->get();
        $permission = Permissions::create([
            "name" => $this->argument("permission-name"),
            "api_url" => "[]",
            "level" => 1,
            "parent_id" => 0,
            "priority" => $largestPriority[0]["priority"] + 1,
        ]);

        if (!$permission) {
            $this->comment("Something went wrong");
            return Command::FAILURE;
        }

        $this->comment($permission);
        return Command::SUCCESS;
    }
}
