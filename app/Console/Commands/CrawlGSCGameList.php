<?php

namespace App\Console\Commands;

use App\Models\GameCategory;
use App\Models\GameCategoryProvider;
use App\Models\GameSetting;
use App\Models\Services;
use App\Services\GameProvider\GSC;
use App\Models\Product;
use App\Models\GameProvider;
use Illuminate\Console\Command;

class CrawlGSCGameList extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'crawl:gsc-game-list';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $products = Product::where('aggregator', GSC::$name)
            ->where('status', Product::$status['active'])
            ->get(); // You can also use Product::where('column', 'value') for more specific queries

        foreach ($products as $key => $data) {
            $product = Product::with([
                'productSetting' => function ($q) {
                    $q->where('name', 'hasWalletList');
                    $q->where('value', '1');
                },
            ])
                ->where('id', $data->id)
                ->where('status', Product::$status['active'])
                ->first() ?? null;
            $this->info(' This is an informational message 1' . $data->provider_code);

            $getOrCreateGameProvider = GameProvider::firstOrCreate(
                ['slug' => $this->getProviderSlug($data->code)],
                [
                    'name' => $this->getProviderName($data->code),
                    'slug' => $this->getProviderSlug($data->code),
                ]
            );

            // $this->info(' This is an informational message 1' . $gameProviderExist);

            $gsc = resolve(GSC::class);
            // sleep();
            $gameList = $gsc->getGameList($data->provider_code)['data'];


            $gameCategories = array_unique(array_column($gameList, 'game_category_id'));

            foreach ($gameCategories as $gameCategoryId) {
                $gameCatoryName = GameCategory::find($gameCategoryId)->name;
                // $this->info($gameCategoryId.' '.$getOrCreateGameProvider->id.' '. $data->code. '-' .$gameCatoryName);
                GameCategoryProvider::firstOrCreate(
                    [
                        'game_category_id' => $gameCategoryId,
                        'game_provider_id' => $getOrCreateGameProvider->id,
                    ],
                    [
                        'game_category_id' => $gameCategoryId,
                        'game_provider_id' => $getOrCreateGameProvider->id,
                        'name' => $data->code . ' - ' . $gameCatoryName,
                    ]
                );
            }

            $index = 0;

            foreach ($gameList as $game) {

                $gameSettingInsert = [
                    'product_id' => $product->id,
                    'game' => $game['game_name'],
                    'game_id' => $game['game_code'],
                    'game_category' => $game['game_category_name'],
                ];

                $gameSetting = GameSetting::firstOrCreate($gameSettingInsert, $gameSettingInsert);

                $categoryProvider = GameCategoryProvider::where(
                    'game_category_id',
                    $game['game_category_id']
                )->where(
                    'game_provider_id',
                    $getOrCreateGameProvider->id,
                )->first();
                $this->info(' This is an informational message 1' . $data->provider_code);

                $servicesInsert = [
                    'name' => $game['game_name'],
                    'product_id' => $product->id,
                    'game_setting_id' => $gameSetting->id,
                    'game_category_id' => $categoryProvider->game_category_id,
                    'game_provider_id' => $categoryProvider->game_provider_id,
                    'game_category_provider_id' => $categoryProvider->id,
                    'icon' => $game['imageUrl'],
                    'wallet_type' => 'Main',
                    'status' => Services::$status['active'],
                ];

                Services::firstOrCreate($servicesInsert, $servicesInsert);
            }
        }



        return Command::SUCCESS;
    }


    public function getProviderSlug($code)
    {
        switch ($code) {
            case 'PT':
                return 'playtech';
                break;
            case 'CQ9':
                return 'cq9';
            case 'MG':
                return 'micro-gaming';
            case 'BG':
                return 'big-gaming';
            case 'VPOWER':
                return 'vpower';
            case 'ACE333':
                return 'ace333';
            case 'JILI':
                return 'jili';
            case 'JOKER':
                return 'joker';
            case 'PRAGMATIC':
                return 'pragmatic-play';
            default:
                break;
        }
    }

    public function getProviderName($code)
    {
        switch ($code) {
            case 'PT':
                return 'Playtech';
                break;
            case 'CQ9':
                return 'CQ9';
            case 'MG':
                return 'Mirco Gaming';
            case 'BG':
                return 'Big Gaming';
            case 'VPOWER':
                return 'VPower';
            case 'ACE333':
                return 'ACE333';
            case 'JILI':
                return 'JILI';
            case 'JOKER':
                return 'JOKER';
            case 'PRAGMATIC':
                return 'Pragmatic Play';
            default:
                break;
        }
    }
}
