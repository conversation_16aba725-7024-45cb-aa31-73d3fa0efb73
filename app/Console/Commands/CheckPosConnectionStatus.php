<?php

namespace App\Console\Commands;

use App\Models\Telegram;
use App\Traits;
use Illuminate\Console\Command;

class CheckPosConnectionStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:check-connection';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check POS Connection Status';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $list[] = null;

        $result = Traits\SonicTrait::post(null, 'pos_status');
        if ($result['status']) {
            foreach ($result['data'] as $eachData) {
                if (!$eachData['onlineStatus']) {
                    $list[] = $eachData['name'];
                }
            }
        }

        Telegram::sendStoreStatus([
            'title' => "❗️❗️ POS OFFLINE ❗️❗️",
            'content' => implode("\n", $list)
        ]);

        return Command::SUCCESS;
    }
}
