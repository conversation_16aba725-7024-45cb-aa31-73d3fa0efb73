<?php

namespace App\Console\Commands;

use App\Http\Controllers\LiveTransactionController;
use App\Jobs\BroadcastLiveTransactionJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class BroadcastLiveTransactions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'broadcast:live-transactions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Broadcast a single live transaction (real or random)';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $lastBroadcastTime = Cache::get('last_live_transaction_broadcast_time');
        $currentTime = now();

        if ($lastBroadcastTime && $currentTime->diffInSeconds($lastBroadcastTime) < 5) {
            return 0;
        }

        Cache::put('last_live_transaction_broadcast_time', $currentTime, 10);

        $controller = new LiveTransactionController();

        $transaction = $controller->getSingleTransaction();
        BroadcastLiveTransactionJob::dispatchSync($transaction);

        return 0;
    }
}
