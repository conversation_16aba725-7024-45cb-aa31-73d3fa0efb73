<?php

namespace App\Console\Commands;

use App\Models\Providers\MtMachine;
use Illuminate\Console\Command;

use function Laravel\Prompts\text;

class CreateMachineCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'machine:new';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        /*
        $name = text(
            label: 'What is machine name?',
            hint: 'Display at the machine top right corner.',
        );
        $description = text(
            label: 'What is machine description?',
            default: $name,
        );
        $store_id = text(
            label: 'What is store ID?',
            hint: 'Can check at the store_id at stores table.',
        );
        */

        $machines = [
            [
                'name' => 'Master Machine 1',
                'description' => 'Master Machine 1',
                'store_id' => 1,
            ],
        ];

        foreach ($machines as $machine) {
            MtMachine::create([
                'name' => $machine['name'],
                'description' => $machine['description'],
                'store_id' => $machine['store_id'],
            ]);
        }

        return Command::SUCCESS;
    }
}
