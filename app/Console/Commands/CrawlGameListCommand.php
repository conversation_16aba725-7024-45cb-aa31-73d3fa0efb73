<?php

namespace App\Console\Commands;

use App\Models\GameCategoryProvider;
use App\Models\GameSetting;
use App\Traits;
use App\Models\Services;
use Illuminate\Console\Command;

class CrawlGameListCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'crawl:game-list';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->update();
        // $product_id = 2001;
        // $game_url = 'https://www.funwallet88.com/en/game/slot/vpower';
        // $gameProductID = 49;
        // $game_category_provider_id = 18;

        // $response = Traits\OCTK8Trait::postOC(["productID" => $gameProductID, "ip" => "127.0.0.1"], "get_game_list");
        // $result = $response['data'];

        // $category_provider = GameCategoryProvider::find($game_category_provider_id);

        // foreach ($result as $key => $value) {
        //     $game_setting = GameSetting::firstOrCreate([
        //         'product_id' => $product_id,
        //         'game' => $value['product'],
        //         'game_id' => $value['product_id'],
        //         'game_category' => $value['game_type'],
        //         'game_list_id' => $value['game_list_id'],
        //     ], [
        //         'product_id' => $product_id,
        //         'game' => $value['product'],
        //         'game_id' => $value['product_id'],
        //         'game_category' => $value['game_type'],
        //         'game_list_id' => $value['game_list_id'],
        //     ]);

        //     Services::addService([
        //         'product_id' => $product_id,
        //         'wallet_type' => 'Main',
        //         'name' => $value['game_name'],
        //         'language' => [
        //             ['type' => 'en', 'name' => $value['game_name']],
        //         ],
        //         'url' => $game_url,
        //         'icon' => $value['game_image'],
        //         'status' => 1,
        //         'game_category_id' => $category_provider->game_category_id,
        //         'game_provider_id' => $category_provider->game_provider_id,
        //         'game_category_provider_id' => $game_category_provider_id,
        //         'game_setting_id' => $game_setting->id,
        //     ]);
        // }

        return Command::SUCCESS;
    }

    public function update()
    {
        $product_id = 2001;
        $game_url = 'https://www.funwallet88.com/en/game/slot/vpower';
        $gameProductID = 49;
        $game_category_provider_id = 18;

        $response = Traits\OCTK8Trait::postOC(["productID" => $gameProductID, "ip" => "127.0.0.1"], "get_game_list");
        $result = $response['data'];

        $category_provider = GameCategoryProvider::find($game_category_provider_id);

        foreach ($result as $key => $value) {
            $game_setting = GameSetting::firstOrCreate([
                'product_id' => $product_id,
                'game' => $value['product'],
                'game_id' => $value['product_id'],
                'game_category' => $value['game_type'],
                'game_list_id' => $value['game_list_id'],
            ], [
                'product_id' => $product_id,
                'game' => $value['product'],
                'game_id' => $value['product_id'],
                'game_category' => $value['game_type'],
                'game_list_id' => $value['game_list_id'],
            ]);

            $service = Services::where('name', $value['game_name'])
                ->where('product_id', $product_id)
                ->where('icon', $value['game_image'])
                ->first();

            if ($service) {
                $service->update([
                    'game_category_id' => $category_provider->game_category_id,
                    'game_provider_id' => $category_provider->game_provider_id,
                    'game_category_provider_id' => $game_category_provider_id,
                    'game_setting_id' => $game_setting->id,
                ]);
            }
        }
    }
}
