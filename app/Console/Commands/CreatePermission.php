<?php

namespace App\Console\Commands;

use App\Models\Permissions;
use Illuminate\Console\Command;

class CreatePermission extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permission:new {permission-name} {--parent=} {--url=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a permission';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $parentId = $this->option("parent");
        $this->comment($parentId);
        $parent = Permissions::find($parentId)->get();

        if (!$parent) {
            $this->comment("Parent not found");
            return Command::FAILURE;
        }

        $permission = Permissions::create([
            "name" => $this->argument("permission-name"),
            "api_url" => "[\"" . $this->option("url") . "\"]",
            "level" => 2,
            "parent_id" => $this->option("parent"),
            "priority" => 1,
        ]);

        if (!$permission) {
            $this->comment("Something went wrong");
            return Command::FAILURE;
        }

        $this->comment($permission);
        return Command::SUCCESS;
    }
}
