<?php

namespace App\Console\Commands;

use App\Models\CreditTransaction;
use App\Models\ExTransfer;
use Illuminate\Console\Command;

class PatchExTransferType extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:ex_transfer_type';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ExTransfer::where('type', 2)
            ->whereNotNull('product_id')
            ->whereNull('card_id')
            ->get()
            ->map(function ($query) {
                $transaction = CreditTransaction::where('user_id', $query->user_id)
                    ->where('subject_type', 62)
                    ->where('reference_id', $query->id)
                    ->first();

                if (isset($transaction) && $transaction->from_id > 1000000) {
                    $query->update([
                        'type' => 1
                    ]);
                }
            });

        return Command::SUCCESS;
    }
}
