<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\WWJAccountBetTransaction;
use Illuminate\Console\Command;
use Illuminate\Http\Response;
use Illuminate\Http\Client\Response as HttpResponse;
use Illuminate\Support\Facades\Http;

class GameTournamentCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wwj:game-tournament';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $params = [
            'start' => '2025-02-14 00:00:00',
            'end' => '2025-02-21 23:59:59',
        ];

        $result = $this->httpPostRequest('/wwj/account-bet-transaction-date', $params);
        if ($result['status'] == true) {

            $list = $result['data'];
            foreach ($list as $key => $value) {
                $user = User::where('uuid', str_replace("fw_", "", $value['account']))->first();

                WWJAccountBetTransaction::updateOrCreate([
                    'account' => $value['account'],
                ], [
                    'user_id' => $user->id ?? null,
                    'total_turn_over' => $value['total_turn_over'],
                    'total_bet' => $value['total_bet'],
                    'total_return' => $value['total_return'],
                    'total_win_loss' => $value['total_win_loss'],
                ]);
            }
        }

        return Command::SUCCESS;
    }

    protected $baseUrl = 'https://gagamelog.funwallet.vip/api';

    protected function httpPostRequest(string $path, array $params): HttpResponse
    {
        $response = Http::asForm()->timeout(3)->post($this->baseUrl . $path, $params);

        if ($response->json('code') && $response->json('code') != 1000) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, $response->json('message'));
        } else if (!$response->ok()) {
            abort(Response::HTTP_FAILED_DEPENDENCY, __('Game Log Provider server error.'));
        }

        return $response;
    }
}
