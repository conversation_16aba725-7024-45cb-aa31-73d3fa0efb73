<?php

namespace App\Console\Commands;

use App\Models\Services;
use App\Models\Product;
use App\Services\GameProvider\GSC;
use App\Models\GameSetting;
use App\Traits;
use Aws\Api\Service;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class CrawlDisableGame extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'game:disable';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check POS Connection Status';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Product::where('aggregator', GSC::$name)
            ->where('status', Product::$status['active'])
            ->where('product_id', '!=', 2012)
            ->get()
            ->each(function ($product) {
                Services::where('product_id', $product->id)
                    ->get()
                    ->each(function ($query) {
                        $response = Http::asForm()->timeout(10)->get($query->icon);

                        if (str_contains($response, 'Error')) {
                            $query->update(['status' => 0]);
                            printf("%s\n", $query->icon);
                        }
                    });
            });

        return Command::SUCCESS;
    }
}
