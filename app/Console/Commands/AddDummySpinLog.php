<?php

namespace App\Console\Commands;

use App\Models\RewardSpin;
use App\Models\RewardSpinLog;
use Illuminate\Console\Command;

class AddDummySpinLog extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add:dummy-spin-log';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add dummy spin log';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $reward = RewardSpin::dummySpin();
        $name = substr(str_shuffle(implode(range('a', 'z'))), 0, 5);

        RewardSpinLog::create([
            'name' => $name,
            'reward_spin_id' => $reward['id'],
            'reward_type' => $reward['reward_type'],
            'value' => $reward['value'] ?? '',
            'is_dummy' => true,
        ]);

        return Command::SUCCESS;
    }
}
