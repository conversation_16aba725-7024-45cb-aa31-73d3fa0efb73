<?php

namespace App\Console\Commands;

use App\Models\AngpauEvent;
use App\Models\User;
use App\Models\UserAngpau;
use App\Models\UserTicketReward;
use Carbon\Carbon;
use Illuminate\Console\Command;

class DistributeFortuneAngpauCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'angpau:fortune';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // if (now()->format('Y-m-d H:i') != now()->format('Y-m-d 00:00')) {
        //     return;
        // }

        $angpauEvent = AngpauEvent::getCurrentAngpauEvent();
        $total = $angpauEvent['max_winners'];

        $userAngpauCount = UserAngpau::where('angpau_event_id', $angpauEvent['id'])->count();
        if ($userAngpauCount > 0) {
            return;
        }

        if ($angpauEvent) {
            $userTicketReward = UserTicketReward::where('ref_event_id', $angpauEvent['id'])
                ->where('is_selected', true)
                ->get();

            foreach ($userTicketReward as $reward) {
                $userAngpau = UserAngpau::where('angpau_event_id', $angpauEvent['id'])
                    ->where('user_id', $reward->user_id)
                    ->first();
                if (!isset($userAngpau)) {

                    $amount = $reward->total_token >= $angpauEvent['min_ticket'] ? $angpauEvent['max_amount'] : $angpauEvent['min_amount'];
                    $userAngpau = UserAngpau::create([
                        'angpau_event_id' => $angpauEvent['id'],
                        'user_id' => $reward->user_id,
                        'name' => $reward->name,
                        'ticket' => $reward->total_token,
                        'amount' => $amount,
                        'status' => 0, // Pending
                    ]);

                    User::getFreeCredit($userAngpau->user_id, $amount);
                    $userAngpau->update([
                        'status' => 1, // Disbursed
                    ]);

                    $total = $total - 1;
                }
            }

            while ($total > 0) {
                $angpauEventId = $angpauEvent['id'];

                $dummyAmount = rand(0, 1) ? $angpauEvent['max_amount'] : $angpauEvent['min_amount'];
                $randTicket = $dummyAmount <= $angpauEvent['min_amount'] ? rand(1, 4) : rand($angpauEvent['max_amount'], $angpauEvent['min_amount']);

                // $dummyAmount = 100;
                // $randTicket = rand(10, 120);

                $randString = str_shuffle('abcdefghijklmnopqrstuvwxyz');

                UserAngpau::create([
                    'angpau_event_id' => $angpauEventId,
                    'name' => $randString,
                    'ticket' => $randTicket,
                    'amount' => $dummyAmount,
                    'is_dummy' => true,
                ]);

                $total = $total - 1;
            }
        }

        AngpauEvent::create([
            'name' => 'Fortune Angpau',
            'promotion_id' => 1001,
            'start_date' => now()->addDay()->startOfDay(),
            'end_date' => now()->addDay()->endOfDay(),
            'total_participants' => 0,
            'pool_amount' => 0,
            'pool_amount_display' => 0,
            'current_pool_amount' => 0,
            'min_amount' => 5,
            'max_amount' => 50,
            'min_ticket' => 5,
            'max_winners' => 20,
            'is_active' => 1,
        ]);

        return Command::SUCCESS;
    }
}
