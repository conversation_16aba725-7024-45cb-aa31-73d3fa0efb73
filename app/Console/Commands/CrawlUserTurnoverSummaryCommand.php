<?php

namespace App\Console\Commands;

use App\Models\UserProduct;
use App\Models\UserTurnoverSummary;
use App\Services\BetLogService;
use App\Services\GameProvider\JK;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CrawlUserTurnoverSummaryCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'crawl:user-turnover-summary';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $today = now()->subDay();

        // $dates = [23, 24, 25, 26, 27, 28, 29, 30];

        // foreach ($dates as $date) {
        //     $today = '2025-05-' . $date;
        $fromDate = Carbon::parse($today)->format('Y-m-d 00:00:00');
        $toDate = Carbon::parse($today)->format('Y-m-d 23:59:59');

        $user_product = UserProduct::with('user', 'product')
            ->where('updated_at', '>=', $fromDate)
            ->get();

        $user_product->each(function ($item) use ($fromDate, $toDate) {

            $user_turnover = UserTurnoverSummary::where('user_id', $item->user_id)
                ->where('product_id', $item->product_id)
                ->whereDate('transaction_at', Carbon::parse($fromDate)->format('Y-m-d'))
                ->where('status', 0)
                ->first();
            if (!isset($user_turnover)) {
                $total_bet = 0;
                $total_win_loss = 0;
                $total_bet_count = 0;
                $total_return = 0;

                switch ($item->product_id) {
                    case JK::$productId:
                        $jk = resolve(JK::class);
                        $result = $jk->getBetTransactionByAccount(
                            $item->member_id,
                            $fromDate,
                            $toDate,
                            $item->user->store->jk_agent_id
                        );

                        if ($result['Error'] == 0 && $result['Data']['List']) {
                            foreach ($result['Data']['List'] as $eachData) {
                                $total_bet += $eachData['Bet'];
                                $total_win_loss += $eachData['WinLoss'];
                                $total_bet_count += 1;
                                $total_return += $eachData['Return'];
                            }

                            if (isset($user_turnover)) {
                                $user_turnover->update([
                                    'turnover' => $total_bet,
                                    'win_loss' => $total_win_loss,
                                    'bet_count' => $total_bet_count,
                                    'return' => $total_return,
                                ]);
                            } else {
                                UserTurnoverSummary::firstOrCreate([
                                    'user_id' => $item->user_id,
                                    'product_id' => 2004,
                                    'transaction_at' => $fromDate,
                                ], [
                                    'user_id' => $item->user_id,
                                    'product_id' => 2004,
                                    'bet_count' => $total_bet_count,
                                    'turnover' => $total_bet,
                                    'return' => $total_return,
                                    'win_loss' => $total_win_loss,
                                    'transaction_at' => $fromDate,
                                ]);
                            }
                        }
                        break;
                    default:
                        if ($item->product->name == 'GSC') {
                            $res = (new BetLogService)->getBetRecords($fromDate, $toDate, $item->member_id, $item->product->provider_code);
                            if (isset($user_turnover)) {
                                $user_turnover->update([
                                    'bet_count' => $res['data']['total_bet_count'],
                                    'turnover' => $res['data']['total_turnover'],
                                    'return' => $res['data']['total_payout'],
                                    'win_loss' => $res['data']['total_payout'] - $res['data']['total_turnover'],
                                ]);
                            } else {
                                UserTurnoverSummary::firstOrCreate([
                                    'user_id' => $item->user_id,
                                    'product_id' => $item->product_id,
                                    'transaction_at' => $fromDate,
                                ], [
                                    'user_id' => $item->user_id,
                                    'product_id' => $item->product_id,
                                    'bet_count' => $res['data']['total_bet_count'],
                                    'turnover' => $res['data']['total_turnover'],
                                    'return' => $res['data']['total_payout'],
                                    'win_loss' => $res['data']['total_payout'] - $res['data']['total_turnover'],
                                    'transaction_at' => $fromDate,
                                ]);
                            }
                            break;
                        }
                }
            }

            sleep(1); // Force Slow Down
        });
        // }
    }
}
