<?php

namespace App\Console\Commands;

use App\Models\UserLevel;
use App\Models\UserLevelTransaction;
use Illuminate\Console\Command;

class UpdateUserRebateAmountCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vip:rebate-update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Rebate to the correct amount.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $userLevel = UserLevel::first();

        UserLevelTransaction::whereIn('type', [UserLevelTransaction::$type['turnover'], UserLevelTransaction::$type['token']])->get()->each(function ($q) use ($userLevel) {

            $points = 0;
            $rebate = 0;
            $meta = json_decode($q->meta, true);
            $amount = $meta['amount'] ?? 0;

            if (UserLevelTransaction::$type['turnover'] == $q->type) {
                $points = (int)$amount;
                $rebate = $amount * $userLevel->vip_level->rebate;
            }

            if (UserLevelTransaction::$type['token'] == $q->type) {
                $rebate = $amount * $userLevel->vip_level->rebate_offline;
            }

            $q->update([
                'rebate_amount' => $rebate,
                'point' => $points
            ]);
        });

        return Command::SUCCESS;
    }
}
