<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;

class ItemsCollection extends ResourceCollection {

    private $pagination;

    public function __construct($resource, $isSimple = false) {
        if ($isSimple) {
            $lastPage = ceil($resource->total / $resource->perPage());
            $this->pagination = [
                'current_page' => $resource->currentPage(),
                'from' => $resource->firstItem(),
                'last_page' =>  $lastPage,
                'per_page' => $resource->perPage(),
                'to' => $resource->lastItem(),
                'total' => $resource->total,
            ];
        } else {
            $this->pagination = [
                'current_page' => $resource->currentPage(),
                'from' => $resource->firstItem(),
                'last_page' => $resource->lastPage(),
                'per_page' => $resource->perPage(),
                'to' => $resource->lastItem(),
                'total' => $resource->total(),
            ];
        }

        $resource = $resource->getCollection();

        parent::__construct($resource);
    }

    public function toArray($request = []) {
        return [
            'list' => $this->collection,
            'pagination' => $this->pagination,
            'meta' => $this->meta ?? NULL,
                ] + @$this->additional; // if got any additional attributes
    }

}
