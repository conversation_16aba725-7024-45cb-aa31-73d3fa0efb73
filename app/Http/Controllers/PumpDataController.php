<?php

namespace App\Http\Controllers;

use App\Console\Commands\PumpDepositDataCommand;
use App\Console\Commands\PumpWithdrawDataCommand;
use App\Console\Commands\RegisterUserDataCommand;
use App\Console\Commands\WalletTransferDataCommand;
use App\Models\Deposit;
use App\Models\ExTransfer;
use App\Models\Withdrawal;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class PumpDataController extends Controller
{
    public function pumpUserData(Request $request)
    {
        $phone_no = $request->phone_no;
        $username = $request->username;
        $password = $request->password;
        $storeId = $request->store_id;

        (new RegisterUserDataCommand())->addUser([
            "name" => $username,
            "password" => Hash::make('password'),
            "phone_no" => $phone_no,
            "country_id" => 129 ?? null,
            "preferred_language" => 'en',
            "store_id" => $storeId ?? null,
            "module" => 'admin',
            'dial_code' => '60'
        ]);
    }

    public function pumpTransferData(Request $request)
    {
        $user_id = $request->user_id;
        $amount = $request->amount;
        $user_card_id = $request->user_card_id;
        $type = $request->type;

        (new WalletTransferDataCommand())->addTransfer([
            'user_id' => $user_id,
            'type' => ExTransfer::$type['in'],
            'amount' => $amount,
            'credit_id' => 1000,
            'card_id' => $user_card_id,
            'wallet_data' => [
                'amount' => $amount,
                'balance' => $amount,
                'credit' => 1000,
                'credit_type' => 'myr-credit',
            ],
        ]);
    }

    public function pumpDepositData(Request $request)
    {
        $user_id = $request->user_id;
        $amount = $request->amount;
        $type = $request->type;

        $res = (new PumpDepositDataCommand())->addDeposit([
            'currency' => 'MYR',
            'amount' => $amount,
            'credit_id' => 1000,
            'type' => 2,
            'user_id' => $user_id,
            'deposit_type' => 'manual-bank',
            'deposit_status' => 'pending'
        ]);

        $deposit = Deposit::where('code', $res['transaction_id'])->first();

        (new PumpDepositDataCommand())->updateDeposit([
            'id' => $deposit->id,
            'order_id' => $res['transaction_id'],
            'amount' => $res['amount'],
            'currency' => 'MYR',
            'order_status' => 'completed',
            'charge' => $res['amount'] * 0.02,
            'token' => '902a88bb53d440f5df461161b180b0a3',
            'name' => null,
            'type' => 'deposit',
            'ccno' => null,
            'mode' => null,
            'payment_type' => 'manual-bank',
            'username' => 'Tkash3DS',
            'utr' => null,
            'status' => 'approved'
        ]);

        $deposit->update([
            'type' => $type
        ]);
    }

    public function pumpWithdrawData(Request $request)
    {
        $user_id = $request->user_id;
        $amount = $request->amount;
        $channel_id = $request->channel_id; // user bank id

        (new PumpWithdrawDataCommand())->add([
            'uid' => $user_id,
            'user_id' => $user_id,
            'credit_id' => 1000,
            'channel_id' => $channel_id,
            'amount' => $amount,
            'wallet_data' => [
                "credit_type" => 'myr-credit',
                "processing_fee" => 0,
                "rate" => 1,
                "currency_id" => 5,
            ]
        ]);
    }

    public function pumpApproveWithdrawData(Request $request)
    {
        $withdrawal = Withdrawal::find($request->id);

        if ($withdrawal) {
            $withdrawal->update([
                'status' => Withdrawal::$status['approved']
            ]);
        }
    }
}
