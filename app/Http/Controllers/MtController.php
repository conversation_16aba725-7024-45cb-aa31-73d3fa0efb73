<?php

namespace App\Http\Controllers;

use App\Events\MtUserLoggedIn;
use App\Models\AccountBalance;
use App\Models\Credit;
use App\Models\ExTransfer;
use App\Models\GameCategory;
use App\Models\GameCategoryProvider;
use App\Models\Product;
use App\Models\Promotion;
use App\Models\Providers\MtMachine;
use App\Models\Services;
use App\Models\User;
use App\Models\UserProduct;
use App\Models\UserPromotion;
use App\Services\GameProvider\CQ9;
use App\Services\GameProvider\JILI;
use App\Services\GameProvider\GSC;
use App\Services\GameProvider\JK;
use App\Services\GameProvider\MT;
use App\Traits;
use App\Traits\DecimalTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;

class MtController extends Controller
{
    public function __construct() {}

    public function login(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $user_id = auth()->user()->id ?? null;
            $request->request->add(['user_id' => $user_id]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer',
            'machine_uuid' => 'required',
            // "machine_uuid" => "required|exists:mt_machines,uuid,deleted_at,NULL",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $service_id = null;
        if (str_contains($request->machine_uuid, '&')) {
            $machine_arr = explode('&', $request->machine_uuid);
            $request->request->add(['machine_uuid' => $machine_arr[0]]);
            $service_id = $machine_arr[1];
        }

        $auth = DB::table('jwt_token')->where('uid', $request->user_id)->where('guard', 'users')->first();

        $machine = MtMachine::with('service')->where('uuid', $request->machine_uuid)
            ->where('status', 1)->first();
        if (empty($machine)) {
            abort(400, json_encode(['Invalid Machine']));
        }

        // Check Machine Store ID
        if (auth()->user()->store_id != $machine->store_id) {
            abort(400, json_encode(['Please Login from the same store']));
        }

        $request->request->add(['service_id' => $service_id ?? $machine->service_id]);
        $service = Services::where('id', $request->service_id)->first();

        $request->request->add(['product_id' => $service->product_id]);

        $product = Product::with([
            'productSetting' => function ($q) {
                $q->where('name', 'hasWalletList');
                $q->where('value', '1');
            },
        ])
            ->where('id', $service->product_id)
            ->where('status', Product::$status['active'])
            ->first() ?? null;
        if (empty($product)) {
            abort(400, json_encode(['Invalid Product']));
        }

        // 09 Feb 2025 5:00 AM until 6:00 AM Game Maintenance
        // if ($service->product_id == 2004) {
        //     $now = Carbon::now();
        //     $startTime = Carbon::parse('4:30')->format('H:i');
        //     $endTime = Carbon::parse('5:30')->format('H:i');
        //     if ($now->format('H:i') >= $startTime && $now->format('H:i') <= $endTime) {
        //         abort(400, json_encode(['Game Under Maintenance']));
        //     }
        // }

        // NOTES: add balance amount for this user
        $amount = Credit::getBalance($request->user_id, Credit::first()->type) ?? $request->amount;
        $params = array_merge($validator->validated(), [
            'amount' => $amount,
            'credit_id' => 1000,
            'product_name' => $product->name,
            'product_id' => $request->product_id,
            'user_id' => $request->user_id,
            'credit_type' => Credit::first()->type,
        ]);
        // Access Game Clear Move All Amount to Game
        AccountBalance::updateBalance($request->user_id, 0);

        $exMemberId = UserProduct::where('user_id', $request->user_id)->where('product_id', $request->product_id)->first()->member_id ?? null;
        if (empty($exMemberId)) {
            abort(400, json_encode(['Invalid User Product']));

            return;
        }

        $userPromotion = UserPromotion::checkUserActivePromotion();
        if (! isset($userPromotion) && $request->product_id == 2004) {
            if ($params['amount'] > 0) {
                $res = ExTransfer::transferIn($params + [
                    'product_data' => $product,
                    'wallet_data' => [],
                    'exMemberId' => $exMemberId,
                    'wallet_type' => 'Main',
                    'machine_id' => $machine?->id ?? null,
                ]);
                if (! $res) {
                    abort(400, json_encode(['Transfer Failed']));
                }
            }
        }

        if ($request->product_id != 2004) {
            if ($params['amount'] > 0) {
                $res = ExTransfer::transferIn($params + [
                    'product_data' => $product,
                    'wallet_data' => [],
                    'exMemberId' => $exMemberId,
                    'wallet_type' => 'Main',
                    'machine_id' => $machine?->id ?? null,
                ]);
                if (! $res) {
                    abort(400, json_encode(['Transfer Failed']));
                }
            }
        }

        // Third Party Login
        // TODO: performance need optimise take 1.89s
        $data = UserProduct::productAutoLogin([
            'user_id' => $request->user_id,
            'product_id' => $request->product_id,
            'service_id' => $request->service_id,
            'balance' => $amount,
        ]);

        $gameUrl = $data['login_link'] ?? null;

        if (isset($gameUrl)) {
            MtUserLoggedIn::dispatch($request->machine_uuid, $user_id, $gameUrl, $auth->token, [
                'product_id' => $request->product_id,
                'service_id' => $request->service_id,
            ]);

            $machine->increment('transfer_in', $amount);
        } else {
            abort(400, json_encode(['Invalid URL']));
        }

        abort(200, json_encode(['data' => [
            'login_link' => $gameUrl,
        ]]));
    }

    public function logout(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id,deleted_at,NULL',
            'product_id' => 'nullable|integer',
            'machine_uuid' => 'required|exists:mt_machines,uuid,deleted_at,NULL',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $machine = MtMachine::with('service')->where('uuid', $request->machine_uuid)->first();

        if (isset($machine->service_id)) {
            $service = Services::where('id', $machine->service_id)->first();
            $request->request->add(['product_id' => $service->product_id]);
        }

        $total_balance = 0;
        UserProduct::with('product', 'user')
            ->where('user_id', $request->user_id)
            ->where(function ($query) use ($request) {
                if (isset($request->product_id)) {
                    $query->where('product_id', $request->product_id);
                }
            })
            ->get()
            ->map(function ($q) use ($validator, $machine) {
                switch ($q->name) {
                    case 'TK8':
                        $prodBalRes = Traits\OCTK8Trait::postOC(['account' => $q->member_id], 'check_balance');
                        break;
                    case 'MT':
                        $prodBalRes = resolve(MT::class)->checkBalance($q->member_id);
                        break;
                    case 'CQ9':
                        $prodBalRes = resolve(CQ9::class)->checkBalance($q->user);
                        break;
                    case 'JK':
                        $prodBalRes = resolve(JK::class)->getWithdraw($q->member_id, $q->user->store->jk_agent_id);
                        break;
                    case 'JILI':
                        $prodBalRes = resolve(JILI::class)->withdraw($q->member_id);
                        break;
                    case 'GSC':
                        $prodBalRes = resolve(GSC::class)->withdraw($q->member_id);
                        break;
                }

                $mainWallet = 0;
                $gameWallet = 0;
                $lotteryWallet = 0;
                if (isset($prodBalRes['data']) && ! empty($prodBalRes['data'])) {
                    $mainWallet = isset($prodBalRes['data']['main_wallet']) && is_numeric($prodBalRes['data']['main_wallet']) ? $prodBalRes['data']['main_wallet'] : 0;
                    $gameWallet = isset($prodBalRes['data']['game_wallet']) && is_numeric($prodBalRes['data']['game_wallet']) ? $prodBalRes['data']['game_wallet'] : 0;
                    $lotteryWallet = isset($prodBalRes['data']['lottery_wallet']) && is_numeric($prodBalRes['data']['lottery_wallet']) ? $prodBalRes['data']['lottery_wallet'] : 0;
                }
                $prodBal = $mainWallet + $gameWallet;
                $prodLotteryBal = $lotteryWallet;

                if (isset($prodBalRes)) {
                    AccountBalance::updateBalance($q->user_id, $prodBal);
                }

                $params = array_merge($validator->validated(), [
                    'amount' => $prodBal,
                    'credit_id' => 1000,
                    'product_name' => $q->product->name,
                    'product_id' => $q->product_id,
                    'user_id' => $q->user_id,
                    'credit_type' => Credit::first()->type,
                ]);

                ExTransfer::transferOut($params + [
                    'product_data' => $q->product,
                    'wallet_data' => [],
                    'exMemberId' => $q->member_id,
                    'wallet_type' => 'Main',
                    'machine_id' => $machine?->id,
                ]);

                $machine->increment('transfer_out', $prodBal);
            });

        $amount = Credit::getBalance($request->user_id, Credit::first()->type);
        if ($amount > 0) {
            AccountBalance::updateBalance($request->user_id, $amount);
        } else {
            $amount = AccountBalance::where('user_id', $request->user_id)->first()->balance ?? 0;
        }

        $data['data'] = [
            'balance' => $amount,
        ];

        abort(200, json_encode($data));

        // $validator = Validator::make($request->all(), [
        //     "user_id" => "required|integer",
        //     "machine_uuid" => "required|exists:mt_machines,uuid,deleted_at,NULL",
        // ]);

        // if ($validator->fails()) {
        //     abort(400, json_encode($validator->errors()));
        // }

        // $user_id = $request->user_id;

        // $machine = MtMachine::with('service')->where('uuid', $request->machine_uuid)->first();
        // if (empty($machine)) {
        //     abort(400, json_encode(['Invalid Machine']));
        // }

        // UserProduct::with('product', 'user')
        //     ->where('user_id', $user_id)
        //     ->where(function ($query) use ($request) {
        //         if (isset($request->product_id)) {
        //             $query->where('product_id', $request->product_id);
        //         }
        //     })
        //     ->get()
        //     ->map(function ($q) use ($request, $validator, $machine) {
        //         switch ($q->name) {
        //             case 'CQ9':
        //                 $prodBalRes = resolve(CQ9::class)->checkBalance($q->user);
        //                 break;
        //         }

        //         $mainWallet = 0;
        //         $gameWallet = 0;
        //         $lotteryWallet = 0;
        //         if (isset($prodBalRes['data']) && !empty($prodBalRes['data'])) {
        //             $mainWallet = isset($prodBalRes['data']['main_wallet']) && is_numeric($prodBalRes['data']['main_wallet']) ? $prodBalRes['data']['main_wallet'] : 0;
        //             $gameWallet = isset($prodBalRes['data']['game_wallet']) && is_numeric($prodBalRes['data']['game_wallet']) ? $prodBalRes['data']['game_wallet'] : 0;
        //             $lotteryWallet = isset($prodBalRes['data']['lottery_wallet']) && is_numeric($prodBalRes['data']['lottery_wallet']) ? $prodBalRes['data']['lottery_wallet'] : 0;
        //         }
        //         $prodBal = $mainWallet + $gameWallet;
        //         $prodLotteryBal = $lotteryWallet;

        //         if (isset($prodBalRes)) {
        //             AccountBalance::updateBalance($q->user_id, $prodBal);
        //         }

        //         $params = array_merge($validator->validated(), [
        //             'amount' => $prodBal,
        //             'credit_id' => 1000,
        //             'product_name' => $q->product->name,
        //             'product_id' => $q->product_id,
        //             'user_id' => $q->user_id,
        //             'credit_type' => Credit::first()->type
        //         ]);

        //         ExTransfer::transferOut($params + [
        //             "product_data" => $q->product,
        //             "wallet_data" => [],
        //             "exMemberId" => $q->member_id,
        //             "wallet_type" => 'Main'
        //         ]);

        //         $machine->increment('transfer_out', $params["amount"]);
        //     });

        // $amount = Credit::getBalance($user_id, Credit::first()->type);
        // if ($amount > 0) {
        //     AccountBalance::updateBalance($user_id, $amount);
        // } else {
        //     $amount = AccountBalance::where('user_id', $user_id)->first()->balance ?? 0;
        // }

        // abort(200, json_encode(['data' => [
        //     'message' => 'Game Log Out'
        // ]]));
    }

    public function getMachineDetail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'machine_uuid' => 'required|exists:mt_machines,uuid,deleted_at,NULL',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $machine = MtMachine::with('service')->where('uuid', $request->machine_uuid)->first();
        if (empty($machine)) {
            abort(400, json_encode(['Invalid Machine']));
        }

        abort(200, json_encode(['data' => [
            'name' => $machine->name,
            'uuid' => $machine->uuid,
            'service' => $machine->service?->name ?? null,
            'service_id' => $machine->service?->id ?? null,
        ]]));
    }

    public function getForceLogOutStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'machine_uuid' => 'required|exists:mt_machines,uuid,deleted_at,NULL',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $machine = MtMachine::where('uuid', $request->machine_uuid)->first();
        if (empty($machine)) {
            abort(400, json_encode(['Invalid Machine']));
        }

        $data = [];

        if ($machine->status == 0) {
            $data['status'] = $machine->status == 0 ? true : false;
            $data['url'] = 'https://youtube.com';
        } else {
            $data['status'] = $machine->status == 1 ? false : true;
            $data['url'] = '';
        }

        abort(200, json_encode(['data' => $data]));
    }

    public function getDemoGameUrl($id, $game = null)
    {
        $user = new User;
        $user->name = "demo00$id";
        $user->uuid = "demo00$id";

        if ($game == 'm2') {
            $mt = resolve(MT::class);
            $res = $mt->getDemoGameUrl($user->name, $user->uuid);
        } else {
            $cq9 = resolve(CQ9::class);
            $res = $cq9->getGameUrl($user, 7);
        }

        abort(200, json_encode(['data' => $res]));
    }

    public function getGameList(Request $request)
    {
        $hot_games = [
            'id' => 0,
            'name' => 'Hot Games',
            'display' => 'Hot Games',
            'is_game_list' => true,
            'list' => Services::getHotServices(),
        ];

        $game_list = GameCategory::getListByCategory();

        $response = array_merge([$hot_games], $game_list->toArray() ?? []);

        abort(200, json_encode(['data' => $response]));
    }

    public function getGameListByCategoryId(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:game_categories,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $list = GameCategoryProvider::with([
            'services' => function ($query) {
                $query->whereIn('status', Arr::except(Services::$status, ['inactive']));
                $query->orderBy('priority', 'ASC');
            },
            'services.product',
            'game_provider',
        ])
            ->where('game_category_id', $request->id)
            ->orderBy('priority', 'ASC')
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'icon' => $item->game_provider->icon,
                    'category_id' => $item->game_category_id,
                    'provider_id' => $item->game_provider_id,
                    'service_list' => $item->services->map(function ($q) {
                        $status = array_search($q->status, Services::$status) ?? null;

                        return [
                            'service_id' => $q->id,
                            'name' => $q->name,
                            'display' => Lang::has('langCustom.service-' . $q->name) ? Lang::get('langCustom.service-' . $q->name) : $q->name,
                            'url' => $q->url,
                            'icon' => isset($q->icon) ? ((filter_var($q->icon, FILTER_VALIDATE_URL) || substr($q->icon, 0, 5) == 'https') ? str_replace(' ', '%20', $q->icon) : env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $q->icon) : null,
                            'status' => $status,
                            'isTK8' => 0,
                            'showCashInOutWarning' => 0,
                            'product_id' => (int) $q->product_id,
                            'product_name' => $q->product->name ?? null,
                            'filter_type' => $q->filter_type,
                            'is_deeplink' => $q->is_deeplink,
                            'is_game' => 1,
                        ];
                    }),
                ];
            });

        $filters = [];
        foreach (Services::$filterType as $key => $value) {
            $filters[$key] = [
                'name' => Lang::has('lang.' . $key) ? Lang::get('lang.' . $key) : $key,
                'value' => $value,
            ];
        }

        $data['data'] = [
            'filters' => $filters,
            'list' => $list,
        ];
        abort(200, json_encode($data));
    }

    public function claimPromotion(Request $request)
    {
        abort(400, json_encode('Promotion Fully Claimed'));

        if (in_array(MODULE, ['user', 'app'])) {
            $user_id = auth()->user()->id ?? null;
            $request->request->add(['user_id' => $user_id]);
        }

        // WARNING: Hardcode
        $request->request->add(['promotion_id' => 1000]);

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer',
            'promotion_id' => 'required|integer',
            'machine_uuid' => 'required',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $auth = DB::table('jwt_token')->where('uid', $request->user_id)->where('guard', 'users')->first();

        $machine = MtMachine::with('service')->where('uuid', $request->machine_uuid)
            ->where('status', 1)->first();
        if (empty($machine)) {
            abort(400, json_encode('Invalid Machine'));
        }

        // Check Machine Store ID
        if (auth()->user()->store_id != $machine->store_id) {
            abort(400, json_encode('Please Login from the same store'));
        }

        $request->request->add(['service_id' => $machine->service_id]);
        $service = Services::where('id', $request->service_id)->first();

        $request->request->add(['product_id' => $service->product_id]);

        $product = Product::with([
            'productSetting' => function ($q) {
                $q->where('name', 'hasWalletList');
                $q->where('value', '1');
            },
        ])
            ->where('id', $service->product_id)
            ->where('status', Product::$status['active'])
            ->first() ?? null;
        if (empty($product)) {
            abort(400, json_encode(['Invalid Product']));
        }

        $promotion = Promotion::find($request->promotion_id);
        if (! $promotion) {
            abort(400, json_encode('Promotion not found'));
        }

        if ($promotion->service_id != $service->id) {
            abort(400, json_encode('Invalid service'));
        }

        $userPromotion = UserPromotion::where('user_id', auth()->user()?->id ?? $user_id)
            ->orderByDesc('updated_at')
            ->first();

        if ($userPromotion?->updated_at->isToday()) {
            abort(400, json_encode('You have already claimed today'));
        }

        // WARNING: Hardcoded
        // $bonusAmount = rand(10, 10);
        $bonusAmount = 100;

        // $userPromotion = UserPromotion::getUserPromotionByUserId($request->user_id);
        $userPromotion = UserPromotion::with('promotion')
            ->where('user_id', $user_id)
            ->where('promotion_id', 1000)
            ->whereDate('updated_at', today())
            ->first();
        if (!isset($userPromotion) && empty($userPromotion)) {
            // Get Total Bet Transaction > 500
            $totalTurnover = 0;

            $user_product = UserProduct::with('user')->where('product_id', 2004)
                ->where('user_id', $user_id)
                ->first();

            $jk = resolve(JK::class);
            $transaction = $jk->getBetTransactionByAccount($user_product->member_id, today()->format('Y-m-d 00:00:00'), now(), $user_product->user->store->jk_agent_id);
            if ($transaction['Error'] == 0 && isset($transaction['Data']['List'])) {
                foreach ($transaction['Data']['List'] as $key => $value) {
                    $totalTurnover += $value['Bet'];
                }
            }

            if ($totalTurnover < 500) {
                abort(400, json_encode(['msg' => ['Please hit Minimum Turnover 500 for claiming promotion. T&C Applied']]));
            } else {
                UserPromotion::applyPromotion($promotion->id, $bonusAmount, $machine?->id);
            }
        }

        $amount = $userPromotion->bonus_amount;
        $data = UserProduct::productAutoLogin([
            'user_id' => $request->user_id,
            'product_id' => $request->product_id,
            'service_id' => $request->service_id,
            'balance' => $amount,
        ]);

        $gameUrl = $data['login_link'] ?? null;

        if (isset($gameUrl)) {
            MtUserLoggedIn::dispatch($request->machine_uuid, $user_id, $gameUrl, $auth->token, [
                'product_id' => $request->product_id,
                'service_id' => $request->service_id,
            ]);

            $machine->increment('transfer_in', $amount);
        } else {
            abort(400, json_encode(['Invalid URL']));
        }

        abort(200, json_encode([
            'data' => [
                'message' => 'Promotion claimed.',
                'promotion' => array_merge(
                    $promotion->toArray(),
                    ['terms' => Lang::has('langReward.promotion-terms') ? Lang::get('langReward.promotion-terms') : '']
                ),
                'amount' => (string) DecimalTrait::setDecimal($bonusAmount, 2),
            ],
        ]));
    }
}
