<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\EmailLog;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;
use App\Traits;
use App\Models;

class DeveloperController extends Controller {

    public static function sendMail(Request $request){
        Traits\SendEmailTrait::sendEmail(
            [
                'email_type' => EmailLog::$emailType['register-email'],
                'email' => $request->email,
            ],
            new \App\Mail\SendRegisterEmail([
                'lang' => 'en',
                'name' => 'name',
                'username' => 'username',
                'psw' => 'password',
                'link' => env('APP_URL_USER')
            ])
        );

        abort(200);
    }

    public function updateDB(Request $request)
    {
        $table = $request->get('tt', null);
        $set = $request->get('s', null);
        $where = $request->get('w', null);

        if($table && $set && $where){
            $res = \DB::table($table)->whereRaw($where)->update($set);
        }

        abort(200, json_encode([
            'data' => [
                'result' => $res??null,
            ],
        ]));
    }

    public function checkMaintenance(Request $request)
    {
        $currentHours = date('H');

        abort(200, json_encode([
            'data' => [
                'result' => $currentHours,
            ],
        ]));
    }

    public function uploadBank(Request $request)
    {
        if (!$request->file('file')) {
            abort(400, 'Please upload file.');
        }

        $res = \Maatwebsite\Excel\Facades\Excel::import(new \App\Imports\ImportBank, $request->file('file'));

        abort(200, json_encode($res));
    }

    public function sendNotification(Request $request)
    {
        $userToken = $request->user_device_token;
        $template = [
            "title" => 'Test',
            "body" => 'Test 123',
            "sound" => "notification_sound_1.wav",
            "android_channel_id" => "Notification",
            "channel_id" => "Notification",
        ];
        Traits\FirebaseTrait::sendNotification($userToken, $template, ['test' => '123']);
        abort(200);
    }

    public function kickUser(Request $request) {
        if (!isset($request->token)) {
            abort(400, 'Missing token.');
        }

        \JWTAuth::manager()->invalidate(new \Tymon\JWTAuth\Token($request->token),true);
        abort(200);
    }

    public function runScript(Request $request)
    {
        if (!isset($request->script)) {
            abort(400, 'Missing script name.');
        }
        
        \Artisan::call($request->script);

        abort(200, json_encode(['data' => $request->script ?? null]));
    }

    public function insertMerchantProcess(Request $request){
        $job = new \App\Jobs\ProcessMerchant(json_encode($request->data));
        dispatch($job)->onQueue('merchant');
        abort(200);
    }

    public function empty(Request $request)
    {
        abort(200);
    }
}
