<?php

namespace App\Http\Controllers;

use App\Models;
use App\Models\Country;
use App\Models\Currencies;
use App\Models\Currency;
use App\Models\SMSLog;
use App\Models\Store;
use App\Models\SystemSetting;
use App\Models\TreeSponsor;
use App\Models\User;
use App\Models\UserAddress;
use App\Models\UserBank;
use App\Models\UserDetail;
use App\Models\UserFavourite;
use App\Models\UserNotification;
use App\Traits;
use App\Traits\Sms;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use <PERSON><PERSON>an\Location\Facades\Location;

class UserController extends Controller
{
    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Member Register
     *
     * @module = admin
     *
     * @path = member/register
     *
     * @method = POST
     *
     * @description = To register new account.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = name|<name>|string|required|Name|Name.
     * @body = country_id|<country_id>|integer|required|129|Country's ID. dropdown: country_state.
     * @body = phone_no|<phone_no>|string|required|60-*********|Phone no.
     * @body = referral_code|<referral_code>|string|optional|TKS123|Referral's Code.
     *
     * @response = {"status":true,"message":"Created Successfully.","timezone":"Asia\/Kuala_Lumpur","environment":"development","execution_duration":"0.**************** sec","log_id":"2d0fd7f1-512a-45d8-bf80-e731e5d9f6bf"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Register
     *
     * @module = user,app
     *
     * @path = register
     *
     * @method = POST
     *
     * @description = To register new account.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = ip_address|<ip_address>|string|required|238.903.94.32|Ip address (required when is app and user register but not admin register)
     * @body = name|<name>|string|required|first user|Account Display Name.
     * @body = country_id|<country_id>|integer|required|1|Country's Id Get From API : country/get.
     * @body = phone_no|<phone_no>|string|required|60-*********|Phone no.
     * @body = otp_code|<otp_code>|string|required|123456|OTP Code.
     * @body = step|<step>|string|required|1,2|Step 1: Register detail, Step 2: Enter OTP
     * @body = password|<password>|string|required|SEED41269|Password alphabet + number, min:8, max:20 (not case sensitive, so we use UPPERCASE)
     * @body = password_confirmation|<password_confirmation>|string|required|SEED41269|Password (not case sensitive, so we use UPPERCASE)
     * @body = referral_code|<referral_code>|string|optional|TKS123|Referral's Code.
     * @body = store_id|<store_id>|string|optional|1|Branch Id or Store Id.
     *
     * @response = {"data":{"message":"Created Successfully.","apk_download_url":"https:\/\/www.google.com","ios_download_url":"https:\/\/www.google.com"},"status":true,"timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.67681503295898 sec","log_id":"fb1e9b74-e774-4ba8-8ef4-f8b30f15d3e5","valid_version":false}
     * ##docs end##
     */
    public function register(Request $request)
    {
        $lang = $request->header('lang') ?? 'en';
        $otpType = null;
        $isAuth = Auth::check() ?? false;
        $phone_no = null;
        if (! isset($request['referral_code']) && ! isset($request['store_id'])) {
            $request->request->add(['store_id' => 999999]);
        }

        if (! isset($request['preferred_language'])) {
            $request->request->add(['preferred_language' => 'en']);
        }
        if (isset($request->password)) {
            $request->merge(['password' => strtoupper($request->password)]);
        }
        if (isset($request->password_confirmation)) {
            $request->merge(['password_confirmation' => strtoupper($request->password_confirmation)]);
        }

        // $countryValid = MODULE == 'admin' ? 1 : 0;
        if (MODULE == 'user' && $request->header('ip-web')) {
            $request->request->add(['ip_address' => $request->header('ip-web')]);
        }
        if (isset($request['ip_address'])) {
            abort_if(! filter_var($request['ip_address'], FILTER_VALIDATE_IP, FILTER_FLAG_IPV4 | FILTER_FLAG_IPV6), 400, json_encode('Invalid IP'));
            $location = Location::get($request['ip_address']);
            if (! empty($location) && in_array($location->countryCode, ['MY', 'TH'])) {
                $countryValid = 1;
            }
        }
        // if (! $countryValid) {
        //     switch (MODULE) {
        //         case 'app':
        //             abort(400, json_encode(['ip_address' => 'This app is exclusively available to users in Malaysia.']));
        //             break;
        //         default:
        //             abort(400, json_encode('This app is exclusively available to users in Malaysia.'));
        //             break;
        //     }
        // }

        $isDashboardV2 = false;
        $fixOTP = null;
        if (defined('MODULE') && MODULE == 'app') {
            $username = User::select('username')->where('phone_no', ($request->phone_no ?? null))->first()['username'] ?? null;
            $sysSetting = SystemSetting::whereIn('name', ['v2AuthorizedUser', 'fixOTP'])->whereNull('deleted_at')->get()->pluck('value', 'name');
            $v2Users = json_decode($sysSetting['v2AuthorizedUser'] ?? '') ?? [];
            $fixOTP = $sysSetting['fixOTP'] ?? null;
            if (in_array($username ?? '', $v2Users)) {
                $isDashboardV2 = true;
            }

            if ((isset($request->device_info))) {
                $request->merge(['device_info' => json_encode($request->device_info)]);

                // process device_info
                $deviceInfo = json_decode($request->device_info);
                if ($request->type == 'iOS') {
                    $name = $deviceInfo->name ?? null;
                    $brand = $deviceInfo->model ?? null;
                    $model = $deviceInfo->device_product_name ?? null;
                } else {
                    $name = $deviceInfo->name ?? null;
                    $brand = $deviceInfo->brand ?? null;
                    $model = $deviceInfo->model ?? null;
                }
                $request->merge(['device_brand' => $brand, 'device_model' => $model, 'device_name' => $name]);
                $request->merge(['user_id' => null]);
            }
        }

        if (isset($request->phone_no)) {
            $replace = str_replace(' ', '', $request->phone_no);
            $request->merge(['phone_no' => $replace]);
        }

        $validator = Validator::make($request->all(), [
            'preferred_language' => 'required|in:'.implode(',', array_keys(config('language'))),
            'name' => [
                'required',
                'string',
                'unique:users,username',
            ],
            'country_id' => 'required|integer|exists:country,id,status,'.Models\Country::$status['active'],
            'phone_no' => [
                'required',
                'string',
                'regex:/^[0-9]{1,3}-[0-9]{7,13}$/',
                function ($q, $value, $fail) use (&$request) {
                    if (! isset($request->store_id)) {
                        return;
                    }
                    $phoneDetail = explode('-', $value);
                    $country = Country::where('country_code', $phoneDetail[0])->first()->name ?? null;

                    if (empty($country)) {
                        abort(400, json_encode('Invalid Phone Number'));
                    }

                    $phoneDetail[1] = (int) $phoneDetail[1];
                    $phone_no = $phoneDetail[0].'-'.$phoneDetail[1];

                    $check = User::where('phone_no', $phone_no)->where('store_id', $request->store_id)->whereNULL('deleted_at')->first();
                    if (! empty($check)) {
                        $fail('This phone number is already used');

                        return;
                    }

                    $request->merge(['dial_code' => $phoneDetail[0]]);
                    $request->merge(['phone_no' => $phone_no]);
                },
            ],
            'otp_code' => [
                'nullable',
                'string',
                function ($q, $value, $fail) use ($request, &$otpType, $isAuth, $isDashboardV2, $fixOTP) {
                    if ($isDashboardV2 == true) {
                        if ($value == $fixOTP) {
                            return;
                        }
                    }

                    if ($request->step == 2 && empty($isAuth)) {
                        $verifyParams = [
                            'type' => 'register-otp',
                            'phone' => $request->phone_no,
                            'otp_code' => $request->otp_code,
                        ];
                        $verifyOTPCode = Sms::verifyOTPCode($verifyParams);
                        if (! $verifyOTPCode) {
                            $fail(Lang::get('lang.invalid-otp'));
                        }
                        $otpType = $verifyOTPCode->type ?? null;
                    }
                },
                Rule::when(MODULE != 'admin', function ($q) use ($isAuth) {
                    if ($q->step == 2 && empty($isAuth)) {
                        return $q = 'required';
                    }
                }),
            ],
            'step' => [
                'integer',
                'in:1,2',
                Rule::when((MODULE != 'admin' && empty($isAuth)), function ($q) {
                    return $q = 'required';
                }),
            ],
            'uuid' => [
                // Rule::when((MODULE == 'app'), function ($q) {
                //     return $q = 'required_without:login_token';
                // }),
                'string',
            ],
            'type' => 'nullable|string',
            'password' => self::userPasswordRule(['required', 'confirmed']),
            'device_brand' => 'nullable|string',
            'device_model' => 'nullable|string',
            'device_name' => 'nullable|string',
            'device_info' => [
                // Rule::when((MODULE == 'app' && $request->step == 2), function ($q) {
                //     return $q = 'required';
                // }),
                'json',
                function ($q, $value, $fail) use (&$user) {
                    // if(isset($request->device_info)){
                    //     $params = [
                    //         'user_id' => $request->user_id,
                    //         'uuid' => $request->uuid,
                    //     ];
                    //     $valid = Models\UserDeviceInfo::checkValidDeviceToLogin($params);
                    //     if(!$valid){
                    //         $fail(Lang::get("lang.login-user-device-not-match"));
                    //     }
                    // }
                },
            ],
            'dial_code' => [
                'nullable',
            ],
            'referral_code' => [
                'nullable',
                function ($q, $value, $fail) {
                    if (isset($value)) {
                        $user = Models\User::where('referral_code', $value)->first();
                        if (! isset($user)) {
                            $fail('Referral code not exists');
                        }
                    }
                },
            ],
            'store_id' => [
                'required_without:referral_code',
                function ($q, $value, $fail) {
                    if (isset($value)) {
                        $store = Models\Store::where('store_id', $value)->first();
                        if (! isset($store)) {
                            $fail(Lang::get('validation.exists'));
                        }
                    }
                },
            ],
        ], [
            'password.regex' => Lang::get('lang.input-field-alphanumspace-error'),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $store_id = null;
        // Get Store
        if (! isset($request->store_id)) {
            $user = User::where('referral_code', $request->referral_code)->first();
            if (isset($user)) {
                $store_id = $user->store_id;
            }
        } else {
            $store_id = $request->store_id;
        }

        $phoneDetail = explode('-', $request->phone_no);
        $country = Country::where('country_code', $phoneDetail[0])->first()->name ?? null;
        if (empty($country)) {
            abort(400, json_encode('Invalid Phone Number'));
        }

        $phoneDetail[1] = (int) $phoneDetail[1];
        $phone_no = $phoneDetail[0].'-'.$phoneDetail[1];
        $check = User::where('phone_no', $phone_no)->where('store_id', $store_id)->whereNULL('deleted_at')->first();
        if (! empty($check)) {
            abort(400, json_encode('This phone number is already used.'));
        }

        // Send OTP
        $validator->setData(array_merge($validator->validated(), ['phone_no' => $phone_no, 'dial_code' => $request->dial_code]));
        $step = $validator->validated()['step'] ?? null;
        $phone_no = $validator->validated()['phone_no'] ?? null;

        if ($step == 1 && empty($isAuth) && ($isDashboardV2 == false)) {
            $sendParams = [
                'phone_no' => $phone_no,
                'otp_type' => 'register-otp',
                'lang' => $lang,
            ];
            Sms::sendOTPCode($sendParams);
        }

        if ($step == 2 || MODULE == 'admin') {
            $params = array_merge($validator->validated(), [
                'store_id' => $store_id,
            ]);
            $createUser = User::addUser($params);

            if (isset($otpType)) {
                $phoneNo = preg_replace('/[^0-9]/', '', $phone_no);
                SMSLog::where('phone_no', $phoneNo)->where('type', $otpType)->update(['deleted_at' => now()]);
            }
            $data['data'] = [
                'message' => (is_array($createUser) ? json_encode($createUser) : Lang::get('lang.user-create-success')),
                'apk_download_url' => env('APP_APK_DOWNLOAD'),
                'ios_download_url' => env('APP_IOS_DOWNLOAD'),
            ];
            abort(200, json_encode($data));
        }

        abort(200);
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = My Profile
     * @module = user,app
     * @path = user/my-profile
     * @method = post
     * @description = To get User own profile.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @response = {"data":{"id":1000000,"member_id":"324874366","name":"tkNotes","username":"tkNotes","rank_requirement":[{"id":2,"name":"level-0","display":"level-0","min_deposit":"1"},{"id":3,"name":"level-1","display":"level-1","min_deposit":"1000"},{"id":4,"name":"level-2","display":"level-2","min_deposit":"10000"},{"id":5,"name":"level-3","display":"level-3","min_deposit":"30000"}],"total_deposit":"0","rank":{"id":1,"name":"member","display":"Member","is_completed":0,"status":"in-progress","status_display":"In Progress"},"ic_no":null,"dob":null,"gender":null,"gender_id":null,"race":null,"race_id":null,"email":"<EMAIL>","phone_no":"60-**********","user_type":"user-account","user_type_display":"User Account","country":"malaysia","country_display":"Malaysia","referral_member_id":null,"referral_name":null,"referral_phone_no":null,"status":"active","status_display":"Active","created_at":"28\/04\/2023 18:43:56","activated_at":false,"currency_code":"MYR","profile_picture":null,"profile_picture_path":null,"d_token":"cuGppb1xbERimpWd7T4S9i:APA91bFXQgWecynyUoJ9uHUhCu83YuMkAlMixCmRERyIqOlccl_hJAZN1HMGDXF8Zz3N5iupHGYiDcfqEiaJPMaiaxDalaXNJvhlmouNN6MVg-N2nzs5tjr3z2dN0AnSruuYT6_ZTbnU","kyc_type":"passport","kyc_type_display":"Passport","image_1":"https:\/\/fw-pvt.s3.ap-southeast-1.amazonaws.com\/asd.jpg?response-content-disposition=inline%3B%20filename%3D%22asd.jpg%22&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=FwoGZXIvYXdzELz%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaDBUWEenT%2FQgvyOha%2FSKxATm17lFEffUq7MEE9f8UordZPQ7km4knHVXpxrxAuPg5xlSrxBRxNGtRAtopeRggfeoxweHdcXK9SEnHonO0SP9wpYQELk9xTRe%2FW9TENh1TzOJmp3PZDwoCGBmJJF0VJpwvUv4NC8j58NhMkrrJGAzmAMwzs%2Fxi4DzJMRJ8zUfKSBtwBtrzJ7rY1l8bujb7BqGtTlaiKK1Uq1kwUFFkzuYNaaM4I5W7moW%2F9s8w6Ic5Gij5y42pBjItbQgzZKFMAtJz3pR%2FN1oMvonV7bO%2Fd3poGfk1jCs1733RcIe8OQKbAUrTcxip&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIA43LUKFW24EUOHJGO%2F20231009%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20231009T023422Z&X-Amz-SignedHeaders=host&X-Amz-Expires=900&X-Amz-Signature=0a43e428d5455fdbcac589cd5adca1b4d0b27b290b47126fb29f1a9eccecf047","image_1_path":"asd.jpg","image_2":null,"image_2_path":null,"kyc_status":"successful","kyc_status_display":"successful","updated_at":"25\/09\/2023 00:43:35"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS91c2VyL215LXByb2ZpbGUiLCJpYXQiOjE2OTY4MTg2NzUsImV4cCI6MTY5NjgyMjQ2MiwibmJmIjoxNjk2ODE4ODYyLCJqdGkiOiJGdFJIbG9TTG40c3FTNEVwIiwic3ViIjoiMTAwMDAwMCIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.RRbQ_vu4IsZANJlgQJl2VkXkkNAPn97UBDw5cfh_nXo","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.20920300483704 sec","log_id":"81411180-e329-4fdc-b7a3-c51af926fe8e"}
     * ##docs end##
     */
    public function myProfile(Request $request)
    {
        $userData = User::getProfile($request->user()->id);
        $data['data'] = $userData;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Member List
     *
     * @module = admin
     *
     * @path = member/list
     *
     * @permissionName = User List
     *
     * @menuType = sub_menu
     *
     * @parent = User
     *
     * @method = post
     *
     * @description = To get User list.

     *
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
     *
     * @body = member_id|<member_id>|int|optional|123809234|User's Member id.
     * @body = name|<name>|string|optional|User1|User's name.
     * @body = username|<username>|string|optional|user1|User's username.
     * @body = country|<country>|integer|optional|100|Country filter. (Dropdown: country)
     * @body = phone_no|<phone_no>|integer|optional|011111111|User's phone_no.
     * @body = sponsor_username|<sponsor_username>|string|optional|757531627|Filter by sponsor member id.
     * @body = sponsor_member_id|<sponsor_member_id>|string|optional|sponsorusername|Filter by sponsor username.
     * @body = leader_username|<leader_username>|string|optional|leaderusername|Filter by leader username.
     * @body = leader_member_id|<leader_member_id>|string|optional|757531627|Filter by leader member id.
     * @body = agent_rank_id|<agent_rank_id>|integer|optional|27|agent rank id. (dropdown: agent_rank)
     * @body = store_id|<store_id>|integer|optional|27|Store. (dropdown: store)
     * @body = status|<status>|string|optional|Active|User's status. Dropdown: member_status
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = last_update_from_date|<last_update_from_date>|string|optional|2022-08-28|Create Last Update Date filter.
     * @body = last_update_to_date|<last_update_to_date>|string|optional|2022-08-28|Create Last Update Date filter.
     * @body = platform|<platform>|string|optional|fw|Platform Name Filter. (dropdown: platform_list)

     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = export_data|<export_data>|array|option|{"data":{"member_id":"User ID","name":"Name","country_display":"Country","phone_no":"Phone No","referral_id":"Referral ID","created_at":"Created Date","updated_at":"Updated Date"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|sponsor-bonus|name of the export file. Required for export
     *
     * @response = {"data":{"list":[{"id":1000003,"member_id":"968508064","name":"test200","phone_no":"60124000100","country_display":"Malaysia","rank":"member","rank_display":"lang.member","rank_percentage":"0.00","referral_user_id":1000000,"referral_id":"917396056","referral_name":"fwuser","referral_phone_no":"60-*********","referral_rank":"member","referral_rank_display":"lang.member","referral_rank_percentage":"0.00","platform":"FW","status":"active","status_display":"Active","created_at":"26\/08\/2024 14:53:42","updated_at":"26\/08\/2024 14:53:42","store":"StoreName1","vip_rank":"vip-0","vip_rank_display":"lang.vip-0","referral_vip_rank":"vip-0","referral_vip_rank_display":"lang.vip-0"},{"id":1000002,"member_id":"462739949","name":"test200","phone_no":"60124000091","country_display":"Malaysia","rank":"member","rank_display":"lang.member","rank_percentage":"0.00","referral_user_id":1000000,"referral_id":"917396056","referral_name":"fwuser","referral_phone_no":"60-*********","referral_rank":"member","referral_rank_display":"lang.member","referral_rank_percentage":"0.00","platform":"FW","status":"active","status_display":"Active","created_at":"26\/08\/2024 14:53:12","updated_at":"26\/08\/2024 14:53:12","store":null,"vip_rank":"vip-0","vip_rank_display":"lang.vip-0","referral_vip_rank":"vip-0","referral_vip_rank_display":"lang.vip-0"},{"id":1000001,"member_id":"849669310","name":"test200","phone_no":"60142441456","country_display":"Malaysia","rank":"member","rank_display":"lang.member","rank_percentage":"0.00","referral_user_id":1000000,"referral_id":"917396056","referral_name":"fwuser","referral_phone_no":"60-*********","referral_rank":"member","referral_rank_display":"lang.member","referral_rank_percentage":"0.00","platform":"FW","status":"active","status_display":"Active","created_at":"20\/08\/2024 19:34:23","updated_at":"20\/08\/2024 19:34:23","store":null,"vip_rank":"vip-0","vip_rank_display":"lang.vip-0","referral_vip_rank":"vip-0","referral_vip_rank_display":"lang.vip-0"},{"id":1000000,"member_id":"917396056","name":"fwuser","phone_no":"fwuser","country_display":"Malaysia","rank":"member","rank_display":"lang.member","rank_percentage":"0.00","referral_user_id":null,"referral_id":null,"referral_name":null,"referral_phone_no":null,"referral_rank":"member","referral_rank_display":"lang.member","referral_rank_percentage":"0.00","platform":"FW","status":"active","status_display":"Active","created_at":"20\/08\/2024 19:26:39","updated_at":"20\/08\/2024 19:26:39","store":null,"vip_rank":"vip-0","vip_rank_display":"lang.vip-0","referral_vip_rank":null,"referral_vip_rank_display":null}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":4,"total":4},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZ3LmNvbS9tZW1iZXIvbGlzdCIsImlhdCI6MTcyNDY1NzE1NywiZXhwIjoxNzU2MjE3MTY0LCJuYmYiOjE3MjQ2NTcxNjQsImp0aSI6IkNPdW1TSDBXNHFHSE1rRzIiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.w512leJBzYfIxm-mrIRkk-WAkNIIrz_-WLvVHHpPoaU","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.050220012664795 sec","log_id":"fa59a9ff-796c-479a-a70a-5c1dff4ce5fd","valid_version":false}
     * ##docs end##
     */
    public function get(Request $request)
    {
        if (in_array(MODULE, ['admin'])) {
            $adminId = auth()->user()->id;
            $adminRes = Models\Admin::with(
                [
                    'adminDetail' => function ($q) {
                        return $q->where('name', 'store');
                    },
                ],
            )
                ->find($adminId);

            $isMaster = $adminRes->is_master;
            if (! $isMaster) {
                $branchJE = $adminRes->adminDetail->first()->value ?? '[]';
                $branchDE = json_decode($branchJE);

                $request->merge(['extra_store_id' => $branchDE]);
            }
        }

        $validator = Validator::make($request->all(), [
            'member_id' => 'nullable|int',
            'name' => 'nullable|string',
            'username' => 'nullable|string',
            'account' => 'nullable|string',
            'country' => 'integer',
            'phone_no' => 'nullable|string',
            'sponsor_username' => 'string',
            'sponsor_phone' => 'string',
            'sponsor_member_id' => 'string',
            'leader_username' => 'string',
            'leader_phone' => 'string',
            'leader_member_id' => 'string',
            'agent_rank_id' => 'nullable',
            'store_id' => 'nullable|integer',
            'status' => 'string|in:'.implode(',', array_keys(User::$memberStatus)),
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d|after_or_equal:from_date',
            'last_active_from_date' => 'required_with:last_active_to_date|string|date_format:Y-m-d',
            'last_active_to_date' => 'required_with:last_active_from_date|string|date_format:Y-m-d|after_or_equal:last_active_from_date',
            'last_update_from_date' => 'required_with:last_update_to_date|string|date_format:Y-m-d',
            'last_update_to_date' => 'required_with:last_update_from_date|string|date_format:Y-m-d|after_or_equal:last_update_from_date',
            'platform' => 'nullable|string',
            'extra_store_id' => 'nullable',
            'order_sort' => 'nullable|string|in:asc,desc',
            'see_all' => 'integer|in:1,0',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string',
            'file_name' => 'required_if:export,==,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $UserList = User::getList($validator->validated());

        $data['data'] = $UserList;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Member Detail
     *
     * @module = admin
     *
     * @path = member/detail
     *
     * @method = POST
     *
     * @description = To get User details.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|required|<EMAIL>|User's id.
     *
     * @response = {"data":{"id":1000004,"member_id":"638692746","name":"fwUser0004","username":"60*********","rank_requirement":[{"id":2,"name":"level-0","display":"level-0","min_deposit":"1"},{"id":3,"name":"level-1","display":"level-1","min_deposit":"1000"},{"id":4,"name":"level-2","display":"level-2","min_deposit":"10000"},{"id":5,"name":"level-3","display":"level-3","min_deposit":"30000"}],"total_deposit":"50.********","rank":{"id":2,"name":"level-0","display":"level-0","is_completed":0,"status":"in-progress","status_display":"in progress"},"ic_no":null,"dob":null,"gender":null,"gender_id":null,"race":null,"race_id":null,"email":null,"phone_no":"60-*********","user_type":"user-account","user_type_display":"user-account","country":"malaysia","country_display":"Malaysia","referral_member_id":"*********","referral_name":"tkNotes","referral_phone_no":"60-*********","status":"active","status_display":"lang.active","created_at":"26\/04\/2023 09:14:39","activated_at":false,"currency_code":"MYR","profile_picture":null,"profile_picture_path":null,"d_token":null,"kyc_type":null,"kyc_type_display":null,"image_1":null,"image_1_path":null,"image_2":null,"image_2_path":null,"updated_at":"26\/04\/2023 09:14:39"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************.i0UN6bxPPJZ9bupNfcDoQSkfKYkeG9_XyFb_iLuDfiQ","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"6ca55fc3-6fea-4016-97a1-0c003808437a"}
     * ##docs end##
     */
    public function detail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer|exists:users,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $UserDetails = User::getProfile($request->input('id'));
        $data['data'] = $UserDetails;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Member Edit
     *
     * @module = admin
     *
     * @path = member/edit
     *
     * @permissionName = User Edit
     *
     * @menuType = api
     *
     * @parent = User List
     *
     * @method = POST
     *
     * @description = To edit User details.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|required|merchant123|User's id.
     * @body = name|<name>|string|optional|merchant123|User's name.
     * @body = country_id|<country_id>|integer|optional|1|Country's Id Get From API : country/get.
     * @body = phone_no|<phone_no>|string|optional|60-*********|Phone no.
     * @body = status|<status>|string|optional|active|User's status. active, suspended, disabled
     *
     * @response = {"status":true,"message":"Updated Successfully.","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL21lbWJlci9lZGl0IiwiaWF0IjoxNjgzMDA5NTA5LCJleHAiOjE2ODMwMTgwOTAsIm5iZiI6MTY4MzAxNDQ5MCwianRpIjoibU8wWnRxNFBra2l5SEJESyIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.G0iK9nKMdgJYlKKQnDCEWYuw1kPgNx1NYmOxT7QRNUc","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.07001519203186 sec","log_id":"a222f590-10a3-42b9-9b19-ee2501d1df17"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = User Edit
     *
     * @module = user,app
     *
     * @path = user/edit
     *
     * @method = POST
     *
     * @description = To edit User details.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = name|<name>|string|optional|merchant123|User's name.
     * @body = country_id|<country_id>|integer|optional|1|Country's Id Get From API : country/get.
     * @body = phone_no|<phone_no>|string|optional|60-*********|Phone no.
     * @body = lang|<lang>|string|optional|en|Notification lang.
     *
     * @response = {"status":true,"message":"Updated Successfully.","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL21lbWJlci9lZGl0IiwiaWF0IjoxNjgzMDA5NTA5LCJleHAiOjE2ODMwMTgwOTAsIm5iZiI6MTY4MzAxNDQ5MCwianRpIjoibU8wWnRxNFBra2l5SEJESyIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.G0iK9nKMdgJYlKKQnDCEWYuw1kPgNx1NYmOxT7QRNUc","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.07001519203186 sec","log_id":"a222f590-10a3-42b9-9b19-ee2501d1df17"}
     * ##docs end##
     */
    public function edit(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $request->request->add(['id' => auth()->user()->id]);
            $request->request->remove('status');
            $request->request->remove('username');
            $request->request->remove('store_id');
        } else {
            // $request->request->remove('country_id');
            // $request->request->remove('phone_no');
            // $request->request->remove('username');
            $request->request->remove('store_id');
        }

        if (isset($request->phone_no)) {
            $replace = str_replace(' ', '', $request->phone_no);
            $request->merge(['phone_no' => $replace]);
        }

        // if ($request['name']) {
        //     $request->request->add(['username' => $request['name']]);
        // }

        $validator = Validator::make($request->all(), [
            'id' => 'required|integer|exists:users,id',
            'name' => [
                'required',
                'string',
                Rule::unique('users', 'username')->whereNot('id', $request->id),
            ],
            'country_id' => 'integer|exists:country,id,status,'.Models\Country::$status['active'],
            'phone_no' => [
                'string',
                'regex:/^[0-9]{1,3}-[0-9]{7,13}$/',
                function ($q, $value, $fail) use (&$request) {
                    $phoneDetail = explode('-', $value);

                    $country = Country::where('country_code', $phoneDetail[0])->first()->name ?? null;

                    if (empty($country)) {
                        abort(400, json_encode('Invalid Phone Number'));
                    }

                    $phoneDetail[1] = (int) $phoneDetail[1];
                    $phone_no = $phoneDetail[0].'-'.$phoneDetail[1];

                    if (isset($request->id)) {
                        $user = User::find($request->id);
                        $check = User::where('phone_no', $phone_no)->where('store_id', $user->store_id)->first();
                        if (! empty($check) && ($check->id != $request->id)) {
                            $fail('This phone number is already used');

                            return;
                        }

                        $request->merge(['phone_no' => $phone_no]);
                    }
                },
            ],
            'username' => 'string|unique:users,username,'.$request->id,
            'status' => [
                'string',
                'in:active,suspended,disabled',
            ],
            'lang' => 'string|in:'.implode(',', array_keys(config('language'))),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }
        if (! empty($validator->validated()['phone_no'])) {
            $validator->setData(array_merge($validator->validated(), ['phone_no' => $request->phone_no]));
        }

        User::updateProfile($validator->validated(), $request->get('id'));

        abort(200, Lang::get('lang.user-update-success'));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = user/change-phone-number
     *
     * @module = user
     *
     * @path = user/change-phone-number
     *
     * @method = POST
     *
     * @description = To edit User phone number.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = phone_no|<phone_no>|string|required|60-*********|Phone no.
     * @body = step|<step>|integer|required|1|Step. (1/2)
     * @body = otp_code|<otp_code>|integer|required|539289|Otp Code. (required when step = 2)
     *
     * @response = {"status":true,"message":"Updated Successfully.","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZweHUuY29tL2FkbWluL2VkaXQiLCJpYXQiOjE2NjE0MTI1NTMsImV4cCI6MTY2MTQyMTM0OCwibmJmIjoxNjYxNDE3NzQ4LCJqdGkiOiJoeWcyaDNFeGhtVWhreXo5Iiwic3ViIjoiMiIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.14qtipyjet6n6G4HuIcUioUz76gjpbGJ6AHz2Kder_w","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"development","execution_duration":"0.0071628093719482 sec","log_id":"02e37e3b-b816-4323-ba48-26f8af289ae2"}
     * ##docs end##
     */
    public function changePhoneNumber(Request $request)
    {
        if (MODULE == 'user') {
            $request->request->add(['id' => auth()->user()->id]);
        }

        if (isset($request->phone_no)) {
            $replace = str_replace(' ', '', $request->phone_no);
            $request->merge(['phone_no' => $replace]);
        }

        $user = null;
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer|exists:users,id',
            'phone_no' => [
                'required',
                'string',
                'regex:/^[0-9]{1,3}-[0-9]{7,13}$/',
                'unique:users,phone_no,'.$request->id.',id',
                function ($q, $value, $fail) use ($request, &$user) {
                    $user = User::find($request->id);
                    if ($user->phone_no == $value) {
                        $fail(__('lang.no-changes-detected'));

                        return;
                    }
                },
            ],
            'otp_code' => [
                Rule::requiredIf((MODULE == 'user' && $request->step == 2)),
                'integer',
                function ($q, $value, $fail) use ($request, &$otpType) {
                    $otpDetail = Sms::verifyOTPCode(['type' => 'change-phone-otp', 'otp_code' => $value, 'user_id' => $request->id]);
                    if (empty($otpDetail)) {
                        $fail(__('lang.invalid-otp'));

                        return;
                    }
                    $otpType = $otpDetail->type ?? null;
                },
                'nullable',
            ],
            'step' => 'required|in:1,2',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if ($validator->validate()['step'] == 1) {
            $result = Sms::sendOTPCode(['user_id' => $validator->validate()['id'], 'username' => $user->username, 'type' => 'sms', 'phone_no' => $validator->validate()['phone_no'], 'otp_type' => 'change-phone-otp']);
            abort(200, json_encode($result));
        }
        if (isset($request->step) && $request->step == 2) {
            $user->update(['phone_no' => $validator->validate()['phone_no']]);
            if (isset($otpType)) {
                SMSLog::where(['type' => $otpType, 'user_id' => $request->id])->delete();
            }
        }
        abort(200, Lang::get('lang.user-update-success'));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = request-otp
     *
     * @module = user
     *
     * @path = request-otp
     *
     * @method = POST
     *
     * @description = To request otp.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     *
     * @body = username|<username>|string|required|username|User's username (Username or Id Either 1).
     * @body = user_id|<user_id>|integer|required|1|User's id (Username or Id Either 1).
     * @body = type|<type>|string|required|forgot-password-otp, reset-password-otp, reset-transaction-password-otp|OTP Type.
     *
     * @response = {"message":"Successfully requested OTP Code.","data":{"otpValidTime":300},"status":true,"timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.0040059089660645 sec","log_id":"4b9e78f9-3d3a-4c25-b210-f091be0808b2"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Request OTP
     *
     * @module = app
     *
     * @path = request-otp
     *
     * @method = POST
     *
     * @description = To request otp.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     *
     * @body = phone_no|<phone_no>|string|required|60-*********|User's phone number
     * @body = type|<type>|string|required|forgot-password-otp, reset-password-otp, reset-transaction-password-otp|OTP Type.
     *
     * @response = {"message":"Successfully requested OTP Code.","data":{"otpValidTime":300},"status":true,"timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.0040059089660645 sec","log_id":"4b9e78f9-3d3a-4c25-b210-f091be0808b2"}
     * ##docs end##
     */
    public function requestOTP(Request $request)
    {
        $returnParam = [];
        $returnParam['username'] = null; // Set Default to null
        $validator = Validator::make($request->all() + ['module' => MODULE], [
            'username' => [
                'required_without_all:user_id,phone_no',
                'string',
                function ($q, $value, $fail) use ($request, &$returnParam) {
                    $userDetail = User::where('username', $value)->whereIN('user_type', [User::$userType['user-account']])->first();
                    if (empty($userDetail)) {
                        $fail(__('validation.in', ['attribute' => str_replace('_', ' ', $q)]));

                        return;
                    }

                    if (empty($userDetail->phone_no)) {
                        $fail(Lang::get('lang.invalid-phone-no'));

                        return;
                    }

                    $returnParam['user_id'] = $userDetail->id;
                    $returnParam['type'] = 'sms';
                    $returnParam['phone_no'] = $userDetail->phone_no;
                    $returnParam['username'] = $request->username ?? null;
                },
            ],
            'phone_no' => [
                'required_if:module,==,app',
                'exists:users,phone_no',
                function ($q, $value, $fail) use ($request, &$returnParam) {
                    $userDetail = User::where('phone_no', $value)->whereIN('user_type', [User::$userType['user-account']])->first();
                    if (empty($userDetail)) {
                        $fail(__('validation.in', ['attribute' => str_replace('_', ' ', $q)]));

                        return;
                    }

                    if (empty($userDetail->phone_no)) {
                        $fail(Lang::get('lang.invalid-phone-no'));

                        return;
                    }

                    $returnParam['user_id'] = $userDetail->id;
                    $returnParam['type'] = 'sms';
                    $returnParam['phone_no'] = $request->phone_no;
                    $returnParam['username'] = $userDetail->username ?? null;
                },
            ],
            'user_id' => [
                'required_without_all:username,phone_no',
                'integer',
                function ($q, $value, $fail) use ($request, &$returnParam) {
                    $userDetail = User::where('id', $value)->whereIN('user_type', [User::$userType['user-account']])->first();
                    if (empty($userDetail)) {
                        $fail(__('validation.in', ['attribute' => str_replace('_', ' ', $q)]));

                        return;
                    }

                    if (empty($userDetail->phone_no)) {
                        $fail(Lang::get('lang.invalid-phone-no'));

                        return;
                    }

                    $returnParam['user_id'] = $userDetail->id;
                    $returnParam['type'] = 'sms';
                    $returnParam['phone_no'] = $userDetail->phone_no;
                    if (isset($request->username) && ! empty($request->username)) {
                        $returnParam['username'] = $request->username;
                    } else {
                        $returnParam['username'] = $userDetail->username ?? null;
                    }
                },
            ],
            'type' => 'required|string|in:'.implode(',', array_keys(Sms::$smsType)),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }
        $returnParam['otp_type'] = $request->type;

        $resutl = Sms::sendOTPCode($returnParam);

        abort(200, json_encode($resutl));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = reset-password
     *
     * @module = user,app
     *
     * @path = reset-password
     *
     * @method = POST
     *
     * @description = To reset user password.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     *
     * @body = step|<1,2>|integer|required|1|Step 1 = request OTP, Step 2 = verify OTP Step 3 = Pass all including password to chg password.
     * @body = phone_no|<phone_no>|string|required|60-*********|Phone number.
     * @body = otp_code|<otp_code>|string|required|123456|OTP Code.
     * @body = password|<password>|string|required|********|New password.
     * @body = password_confirmation|<password_confirmation>|string|required|********|Password confirmation.
     *
     * @response = {"status":true,"message":"Updated Successfully.","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZweHUuY29tL2FkbWluL2VkaXQiLCJpYXQiOjE2NjE0MTI1NTMsImV4cCI6MTY2MTQyMTM0OCwibmJmIjoxNjYxNDE3NzQ4LCJqdGkiOiJoeWcyaDNFeGhtVWhreXo5Iiwic3ViIjoiMiIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.14qtipyjet6n6G4HuIcUioUz76gjpbGJ6AHz2Kder_w","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"development","execution_duration":"0.0071628093719482 sec","log_id":"02e37e3b-b816-4323-ba48-26f8af289ae2"}
     * ##docs end##
     */
    public function resetPassword(Request $request)
    {
        $returnParam = [];
        $otpType = null;

        if (isset($request->password)) {
            $request->merge(['password' => strtoupper($request->password)]);
        }
        if (isset($request->password_confirmation)) {
            $request->merge(['password_confirmation' => strtoupper($request->password_confirmation)]);
        }

        $validator = Validator::make($request->all(), [
            'step' => 'required|in:1,2,3',
            'phone_no' => [
                'required',
                'exists:users,phone_no',
                function ($q, $value, $fail) use (&$returnParam) {
                    $userDetail = User::where('phone_no', $value)->whereIN('user_type', [User::$userType['user-account']])->first();
                    if (empty($userDetail)) {
                        $fail(__('validation.in', ['attribute' => str_replace('_', ' ', $q)]));

                        return;
                    }

                    if (empty($userDetail->phone_no)) {
                        $fail(Lang::get('lang.invalid-phone-no'));

                        return;
                    }
                    $returnParam['user_id'] = $userDetail->id;
                    $returnParam['type'] = 'sms';
                    $returnParam['phone_no'] = $value;
                    $returnParam['username'] = $userDetail->username ?? null;
                    $returnParam['lang'] = config('app.locale') ?? 'en';
                },
            ],
            'otp_code' => [
                'required_if:step,=,2,3',
                'string',
                function ($q, $value, $fail) use ($request, &$returnParam, &$otpType) {
                    $userId = User::when(isset($request->phone_no), function ($q) use ($request) {
                        return $q->where('phone_no', $request->phone_no);
                    })->first()->id ?? null;

                    $otpDetail = Sms::verifyOTPCode(['type' => 'forgot-password-otp', 'otp_code' => $value, 'user_id' => $userId]);

                    if (empty($otpDetail)) {
                        $fail(__('lang.invalid-otp'));

                        return;
                    }
                    $returnParam['user_id'] = $userId;
                    $otpType = $otpDetail->type ?? null;
                },
            ],
            'password' => self::userPasswordRule(['required_if:step,=,3', 'confirmed']),
        ], [
            'password.confirmed' => null,
            'password' => Lang::get('lang.password-field-error'),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if ($request['step'] == 1) {
            $returnParam['otp_type'] = 'forgot-password-otp';
            Sms::sendOTPCode($returnParam);
        } elseif ($request['step'] >= 3) {
            $resutl = User::changePassword(array_merge($validator->validated(), $returnParam));

            if (isset($otpType)) {
                SMSLog::where(['type' => $otpType, 'user_id' => $returnParam['user_id']])->delete();
            }

            abort(200, Lang::get('lang.reset-pwd-success'));
        }

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Reset User Password
     *
     * @module = admin
     *
     * @path = member/reset-user-pwd
     *
     * @permissionName = Reset User Password
     *
     * @menuType = api
     *
     * @parent = User List
     *
     * @method = POST
     *
     * @description = To reset user password.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     *
     * @body = username|<username>|string|required|username|Username.
     * @body = password|<password>|string|required|qweqwe11|password.
     * @body = password_confirmation|<password_confirmation>|string|required|qweqwe11|password_confirmation.
     *
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20vbWVtYmVyL3Jlc2V0LXVzZXItcHdkIiwiaWF0IjoxNjY0ODY2OTc3LCJleHAiOjE2NjQ4NzU3MDIsIm5iZiI6MTY2NDg3MjEwMiwianRpIjoiR3FmM3JwWW1oZ0pJaDZTSyIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.kHIqRCO1vmINEwk-yZ13N5EihIAEXRvzkBKMm_ZQGHM","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.010007858276367 sec","log_id":"61b68a01-b0ce-4c74-82f8-92c3f399ba14"}
     * ##docs end##
     */
    public function adminResetPassword(Request $request)
    {
        if (isset($request->password)) {
            $request->merge(['password' => strtoupper($request->password)]);
        }
        if (isset($request->password_confirmation)) {
            $request->merge(['password_confirmation' => strtoupper($request->password_confirmation)]);
        }
        $request->request->add(['type' => 'reset_password']);
        $validator = Validator::make($request->all(), [
            'username' => [
                'required',
                'string',
                Rule::exists('users', 'username')->whereIN('user_type', [User::$userType['user-account']]),
            ],
            'type' => 'required|string',
            'password' => self::userPasswordRule(['required', 'confirmed']),
        ], [
            'password.confirmed' => null,
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $resutl = User::resetPassword($validator->validated());

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = /member/reset-user-transaction-pwd
     *
     * @module = admin
     *
     * @path = member/reset-user-transaction-pwd
     *
     * @permissionName = Reset User Transaction Pin
     *
     * @menuType = api
     *
     * @parent = User List
     *
     * @method = POST
     *
     * @description = To reset user password.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     *
     * @body = username|<username>|string|required|username|Username.
     * @body = password|<password>|string|required|qweqwe11|password.
     * @body = password_confirmation|<password_confirmation>|string|required|qweqwe11|password_confirmation.
     *
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20vbWVtYmVyL3Jlc2V0LXVzZXItdHJhbnNhY3Rpb24tcHdkIiwiaWF0IjoxNjY0ODY2OTc3LCJleHAiOjE2NjQ4NzU5ODIsIm5iZiI6MTY2NDg3MjM4MiwianRpIjoiMGRXZ3ZQS2pKYU15bUsxdCIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.ajHxjtu-JIURHXzT_TD0vGKKZSrzhq2cdfLYHu1HW90","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.*************** sec","log_id":"6735fd44-718f-4604-8b29-b4390a61d637"}
     * ##docs end##
     */
    public function adminResetTransactionPassword(Request $request)
    {
        $request->request->add(['type' => 'reset_transaction_password']);
        $validator = Validator::make($request->all(), [
            'username' => [
                'required',
                'string',
                Rule::exists('users', 'username')->whereIN('user_type', [User::$userType['user-account']]),
            ],
            'type' => 'required|string',
            'password' => self::transactionPasswordRule(['required']),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $resutl = User::resetPassword($validator->validated());

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = verify-user-email
     *
     * @module = user
     *
     * @path = verify-user-email
     *
     * @method = POST
     *
     * @description = To verify user email.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     *
     * @body = code|<code>|string|required|code|Code.
     *
     * @response = {"status":true,"message":"Successfully verified email.","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.**************** sec","log_id":"05b8be50-eb08-49fb-8c34-02d57187c84a"}
     * ##docs end##
     */
    public function verifyUserEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $result = User::verifyUserEmail($validator->validated());

        abort(200, Lang::get('lang.verify-user-email-success'));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = user/change-pwd
     *
     * @module = user,app
     *
     * @path = user/change-pwd
     *
     * @method = POST
     *
     * @description = To change user password.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = current_password|<current_password>|string|required|********|Current Password.
     * @body = password|<password>|string|required|********|New password.
     * @body = password_confirmation|<password_confirmation>|string|required|********|Password confirmation.

     *
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20vbWVtYmVyL3NldC11c2VyLXJhbmsiLCJpYXQiOjE2NjM2Mzg4MDgsImV4cCI6MTY2MzY0ODQwMywibmJmIjoxNjYzNjQ0ODAzLCJqdGkiOiJOQVVtUXlxbzlSenBXRHZ5Iiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.uyZZ07_Wb1OrXstTJRSDAEhwbAm2rann17noFfiNzmo","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.0035791397094727 sec","log_id":"f15d29f5-61b8-4977-9e5d-f5c49c483e57"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = member/change-user-pwd
     *
     * @module = admin
     *
     * @path = member/change-user-pwd
     *
     * @method = POST
     *
     * @description = To change user password.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = user_id|<user_id>|integer|required|1|User's id.
     * @body = password|<password>|string|required|********|New password.
     * @body = password_confirmation|<password_confirmation>|string|required|********|Password confirmation.

     *
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20vbWVtYmVyL3NldC11c2VyLXJhbmsiLCJpYXQiOjE2NjM2Mzg4MDgsImV4cCI6MTY2MzY0ODQwMywibmJmIjoxNjYzNjQ0ODAzLCJqdGkiOiJOQVVtUXlxbzlSenBXRHZ5Iiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.uyZZ07_Wb1OrXstTJRSDAEhwbAm2rann17noFfiNzmo","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.0035791397094727 sec","log_id":"f15d29f5-61b8-4977-9e5d-f5c49c483e57"}
     * ##docs end##
     */
    public function changePassword(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $request->request->add(['user_id' => Auth::user()->id]);
        }
        if (isset($request->current_password)) {
            $request->merge(['current_password' => strtoupper($request->current_password)]);
        }
        if (isset($request->password)) {
            $request->merge(['password' => strtoupper($request->password)]);
        }
        if (isset($request->password_confirmation)) {
            $request->merge(['password_confirmation' => strtoupper($request->password_confirmation)]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => [
                'required',
                'integer',
                Rule::exists('users', 'id')->whereIN('user_type', [User::$userType['user-account']]),
            ],
            'current_password' => [
                'string',
                'different:password',
                'required',
                function ($q, $value, $fail) use (&$request) {
                    $check = User::where('id', $request->user_id)->first();
                    if (isset($check->password) && Hash::check($value, $check->password) != true) {
                        $fail(__('lang.invalid-credentials'));
                    }
                },
            ],
            // 'otp_code' => [
            //     'string',
            //     'required',
            //     function ($q, $value, $fail) use ($request,&$otpType) {
            //         $otpDetail = Sms::verifyOTPCode(['type'=>"reset-password-otp",'otp_code'=>$value,'user_id'=>$request->user_id]);
            //         if(empty($otpDetail)){
            //             $fail(__('lang.invalid-otp'));
            //             return;
            //         }
            //         $otpType = $otpDetail->type ?? Null;
            //     },
            // ],
            'password' => self::userPasswordRule(['required', 'confirmed']),
        ], [
            'password.confirmed' => null,
            'password' => Lang::get('lang.password-field-error'),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        User::changePassword($validator->validated());

        if (isset($otpType)) {
            SMSLog::where(['type' => $otpType, 'user_id' => $request->user_id])->delete();
        }

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Change Transaction PWD
     *
     * @module = user,app
     *
     * @path = user/change-transaction-pwd
     *
     * @method = POST
     *
     * @description = To change transaction password.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = step|<step>|string|required|1,2,3,4|Step 1 Return Trxn Pwd Flag Step 2 Verify Current Password if needed Step 3 Verify field and Request OTP Step 4 Confirmation.
     * @body = current_password|<current_password>|string|required|********|Current Transaction Password. First time no need this. (Step 1 Required)
     * @body = password|<password>|string|required|********|Transaction password. (Step 1 Required)
     * @body = password_confirmation|<password_confirmation>|string|required|********|Transaction Password confirmation. (Step 1 Required)
     * @body = otp_code|<otp_code>|string|required|********|Transaction Password OTP Code. (Step 2 Required)

     *
     * @response = {"data":{"transaction_flag":1},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwudXNlci50a2FzaC5jb20vdXNlci9jaGFuZ2UtdHJhbnNhY3Rpb24tcHdkIiwiaWF0IjoxNjg0NDA3Njc5LCJleHAiOjE2ODQ0MTIzNTUsIm5iZiI6MTY4NDQwODc1NSwianRpIjoibkRkVUNRSmZEQkhKcFFBbiIsInN1YiI6IjEwMDAwMDAiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.0A8tzrrthu0lh6x0mnz1OKYVVPk_yWt87a6Z9mGNObs","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.21832299232483 sec","log_id":"fa7277c8-8206-44e5-8bef-782beacfd559"}
     * ##docs end##
     */
    public function changeTransactionPassword(Request $request)
    {
        // System
        $data = [];

        if (in_array(MODULE, ['user', 'app'])) {
            $request->request->add(['user_id' => Auth::user()->id]);
        }
        $otpType = null;
        $transaction_flag = null;
        $lang = $request->header('lang') ?? 'en';

        if (isset($request->user_id)) {
            $userDetail = User::with('userDetail')->where('id', $request->user_id)->whereIN('user_type', [User::$userType['user-account']])->first();
            if (! empty($userDetail)) {
                $transaction_flag = in_array('transaction_password', array_column($userDetail->userDetail->toArray(), 'name')) ? 1 : 0;
            }
        }

        $validator = Validator::make($request->all(), [
            'step' => [
                'integer',
                'in:1,2,3,4',
                Rule::requiredIf(in_array(MODULE, ['user', 'app'])),
            ],
            'user_id' => [
                'required',
                'integer',
                Rule::exists('users', 'id')->whereIN('user_type', [User::$userType['user-account']]),
            ],
            'current_password' => [
                'string',
                'different:password',
                Rule::requiredIf(($transaction_flag == 1 && $request->step >= 2)),
                function ($q, $value, $fail) use (&$request) {
                    $check = UserDetail::where('user_id', $request->user_id)->where('name', 'transaction_password')->first();
                    if (isset($check->value) && Hash::check($value, $check->value) != true && $request->step >= 2) {
                        $fail(__('lang.invalid-credentials'));
                    }
                },
            ],
            'otp_code' => [
                'string',
                // Rule::requiredIf(in_array(MODULE,["user","app"]) && ($request->step >= 4) && ($transaction_flag == 1)),
                function ($q, $value, $fail) use (&$otpType, $transaction_flag) {
                    if ($transaction_flag == 1) {
                        // $otpDetail = Sms::verifyOTPCode(['type'=>"change-transaction-password-otp",'otp_code'=>$value,'user_id'=>$request->user_id]);
                        // if(empty($otpDetail)){
                        //     $fail(__('lang.invalid-otp'));
                        //     return;
                        // }
                        // $otpType = $otpDetail->type ?? Null;
                    }
                },
            ],
            'password' => self::transactionPasswordRule([Rule::requiredIf(($request->step >= 3))]),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if (($validator->validated()['step'] == 3) && ($transaction_flag == 1)) {
            // $sendParams = [
            //     'user_id' => $userDetail->id,
            //     'username' => $userDetail->name,
            //     'phone_no' => $userDetail->phone_no,
            //     'otp_type' => 'change-transaction-password-otp',
            //     'lang' => $lang,
            // ];
            // Sms::sendOTPCode($sendParams);
        }

        if ($validator->validated()['step'] == 4) {
            UserDetail::changeTransactionPassword($validator->validated());

            if (isset($otpType)) {
                SMSLog::where(['type' => $otpType, 'user_id' => $request->user_id])->delete();
            }
        }

        $data['data'] = ['transaction_flag' => $transaction_flag];
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Reset Transaction Password
     *
     * @module = user,app
     *
     * @path = user/reset-transaction-pwd
     *
     * @method = POST
     *
     * @description = To reset transaction password.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = phone_no|<phone_no>|string|required|60-12345689|User's phone number. (Step 1 Required)
     * @body = otp_code|<otp_code>|string|required|********|Transaction Password OTP Code. (Step 2 Required)
     * @body = password|<password>|string|required|********|Transaction password. (Step 3 Required)
     * @body = password_confirmation|<password_confirmation>|string|required|********|Transaction Password confirmation. (Step 3 Required)
     * @body = step|<step>|int|required|1|Step 1 = Get OTP. Step 2 = Verify OTP Code, Step 3 = Change Transaction Pin

     *
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20vbWVtYmVyL3NldC11c2VyLXJhbmsiLCJpYXQiOjE2NjM2Mzg4MDgsImV4cCI6MTY2MzY0ODQwMywibmJmIjoxNjYzNjQ0ODAzLCJqdGkiOiJOQVVtUXlxbzlSenBXRHZ5Iiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.uyZZ07_Wb1OrXstTJRSDAEhwbAm2rann17noFfiNzmo","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.0035791397094727 sec","log_id":"f15d29f5-61b8-4977-9e5d-f5c49c483e57"}
     * ##docs end##
     */
    public function resetTransactionPassword(Request $request)
    {
        $lang = $request->header('lang') ?? 'en';
        $otpType = null;
        $checkUser = null;

        $validator = Validator::make($request->all(), [
            'phone_no' => [
                'required',
                'string',
                function ($q, $value, $fail) use (&$checkUser) {
                    $phoneDetail = explode('-', $value);
                    $phoneDetail[1] = (int) $phoneDetail[1];
                    $phone_no = $phoneDetail[0].'-'.$phoneDetail[1];
                    $checkUser = User::where('id', auth()->user()->id)->where('phone_no', $phone_no)->first();

                    if (empty($checkUser)) {
                        $fail(__('validation.exists'));

                        return;
                    }
                },
            ],
            'otp_code' => [
                'string',
                Rule::requiredIf(($request->step >= 2)),
                function ($q, $value, $fail) use ($request, &$otpType) {
                    $otpDetail = Sms::verifyOTPCode(['type' => 'reset-transaction-password-otp', 'otp_code' => $value, 'user_id' => $request->user_id]);
                    if (empty($otpDetail)) {
                        $fail(__('lang.invalid-otp'));

                        return;
                    }
                    $otpType = $otpDetail->type ?? null;
                },
            ],
            'step' => 'required|int|in:1,2,3',
            'password' => self::transactionPasswordRule(['required_if:step,3']),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if ($request->step == 1) {
            $sendParams = [
                'user_id' => $checkUser->id,
                'username' => $checkUser->name,
                'phone_no' => $checkUser->phone_no,
                'otp_type' => 'reset-transaction-password-otp',
                'lang' => $lang,
            ];
            Sms::sendOTPCode($sendParams);
        } elseif ($request->step == 3) {
            Models\UserDetail::changeTransactionPassword(array_merge($validator->validated(), ['user_id' => $checkUser->id]));

            if (isset($otpType)) {
                SMSLog::where(['type' => $otpType, 'user_id' => $checkUser->id])->delete();
            }
        }

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = forgot-username
     *
     * @module = user
     *
     * @path = forgot-username
     *
     * @method = POST
     *
     * @description = To get user username
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     *
     * @body = email|<email>|string|required|<EMAIL>|User email.
     * @body = phone_no|<phone_no>|string|required|60-*********|Phone no.
     *
     * @response = {"status":true,"message":"Updated Successfully.","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZweHUuY29tL2FkbWluL2VkaXQiLCJpYXQiOjE2NjE0MTI1NTMsImV4cCI6MTY2MTQyMTM0OCwibmJmIjoxNjYxNDE3NzQ4LCJqdGkiOiJoeWcyaDNFeGhtVWhreXo5Iiwic3ViIjoiMiIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.14qtipyjet6n6G4HuIcUioUz76gjpbGJ6AHz2Kder_w","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"development","execution_duration":"0.0071628093719482 sec","log_id":"02e37e3b-b816-4323-ba48-26f8af289ae2"}
     * ##docs end##
     */
    public function forgotUsername(Request $request)
    {
        $type = 'forgot-username';
        $dateTime = date('Y-m-d H:i:s');

        if (isset($request->phone_no)) {
            $replace = str_replace(' ', '', $request->phone_no);
            $request->merge(['phone_no' => $replace]);
        }

        $validator = Validator::make($request->all(), [
            'email' => 'required|string',
            'phone_no' => [
                'required',
                'string',
                'regex:/^[0-9]{1,3}-[0-9]{7,13}$/',
                Rule::exists('users')->where('email', $request->email)->where('phone_no', $request->phone_no),
            ],
        ], [
            'phone_no.exists' => Lang::get('lang.username-not-found'),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $username = User::select('username', 'id')->where(['email' => $request->email, 'phone_no' => $request->phone_no])->first();

        $returnParam = [
            'otp_type' => $type,
            'phone_no' => $request->phone_no,
            'user_id' => $username->id,
            'username' => $username->username,
        ];

        $resutl = Sms::sendOTPCode($returnParam);

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = member/login-to-member
     *
     * @module = admin
     *
     * @path = member/login-to-member
     *
     * @permissionName = Login To Member
     *
     * @menuType = api
     *
     * @parent = User List
     *
     * @method = POST
     *
     * @description = To login member site.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|required|1|User's id.
     *
     * @response = {"data":{"login_token":"7f116b8ddbede94da40a7834e27b3829","login_url":"local-api-user.martin.com"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************.NI_bJerJPyVBfBkWGEjD5A4VOUeZhtZeGe1ROyVMukk","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.*************** sec","log_id":"d6044ed6-5f7d-4ff8-8af6-268d215bad8e"}
     * ##docs end##
     */
    public function loginToMember(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => [
                'required',
                'integer',
                Rule::exists('users', 'id')->whereIN('user_type', [User::$userType['user-account']]),
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = User::loginToMember($validator->validated());

        abort(200, json_encode($res));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Switch Account
     *
     * @module = app
     *
     * @path = user/switch-account
     *
     * @permissionName = Switch Account
     *
     * @menuType = api
     *
     * @parent = User List
     *
     * @method = POST
     *
     * @description = To switch account.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = store_id|<store_id>|integer|required|1|Store Id
     *
     * @response = {"data":{"login_token":"7f116b8ddbede94da40a7834e27b3829","login_url":"local-api-user.martin.com"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************.NI_bJerJPyVBfBkWGEjD5A4VOUeZhtZeGe1ROyVMukk","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.*************** sec","log_id":"d6044ed6-5f7d-4ff8-8af6-268d215bad8e"}
     * ##docs end##
     */
    public function switchAccount(Request $request)
    {
        $phoneNo = Auth::user()->phone_no;
        $validator = Validator::make($request->all(), [
            'store_id' => [
                'required',
                'integer',
                function ($q, $value, $fail) use (&$request, $phoneNo, &$userId) {
                    $selectedUser = User::where('phone_no', $phoneNo)->where('store_id', $value)->first();
                    if (! isset($selectedUser)) {
                        $fail(Lang::get('lang.invalid-credentials'));
                    }
                    $userId = $selectedUser->id;
                },
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = User::loginToMember(array_merge($validator->validated(), ['user_id' => $userId]));

        abort(200, json_encode($res));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Get Delivery Address List
     *
     * @module = user
     *
     * @path = /user/get-delivery-address
     *
     * @method = POST
     *
     * @description = To get delivery address list.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @response = {"data":[{"id":12,"user_id":1000000,"label":"Test 9","name":"Cino","phone_no":"60-121212121","address":"Jln 4 Bandar Hussien 43000, Cheras, selangor, west, Malaysia","is_default":1},{"id":2,"user_id":1000000,"label":"Default","name":"Ghost","phone_no":"121212","address":"Jln Cheras mewah 43000, Cheras, selangor, west, Malaysia","is_default":0},{"id":3,"user_id":1000000,"label":"Test 1","name":"Cino","phone_no":"60-121212121","address":"Jln 4 Bandar Hussien 43000, Cheras, selangor, west, Malaysia","is_default":0},{"id":5,"user_id":1000000,"label":"Test 2","name":"Cino","phone_no":"60-121212121","address":"Jln 4 Bandar Hussien 43000, Cheras, selangor, west, Malaysia","is_default":0},{"id":6,"user_id":1000000,"label":"Test 3","name":"Cino","phone_no":"60-121212121","address":"Jln 4 Bandar Hussien 43000, Cheras, selangor, west, Malaysia","is_default":0},{"id":7,"user_id":1000000,"label":"Test 4","name":"Cino","phone_no":"60-121212121","address":"Jln 4 Bandar Hussien 43000, Cheras, selangor, west, Malaysia","is_default":0},{"id":8,"user_id":1000000,"label":"Test 5","name":"Cino","phone_no":"60-121212121","address":"Jln 4 Bandar Hussien 43000, Cheras, selangor, west, Malaysia","is_default":0},{"id":9,"user_id":1000000,"label":"Test 6","name":"Cino","phone_no":"60-121212121","address":"Jln 4 Bandar Hussien 43000, Cheras, selangor, west, Malaysia","is_default":0},{"id":10,"user_id":1000000,"label":"Test 7","name":"Cino","phone_no":"60-121212121","address":"Jln 4 Bandar Hussien 43000, Cheras, selangor, west, Malaysia","is_default":0},{"id":11,"user_id":1000000,"label":"Test 8","name":"Cino","phone_no":"60-121212121","address":"Jln 4 Bandar Hussien 43000, Cheras, selangor, west, Malaysia","is_default":0}],"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLXVzZXIuY3FxLmNvbS91c2VyL2dldC1kZWxpdmVyeS1hZGRyZXNzIiwiaWF0IjoxNjcwOTEzNTM3LCJleHAiOjE2NzA5MjE4MjUsIm5iZiI6MTY3MDkxODIyNSwianRpIjoidkhNU3RmZ205SFEzQkFBbiIsInN1YiI6IjEwMDAwMDAiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.kbYOyPI3Q3oCqPKcxRTSeO98kKhJL9QD_AXBdZ6-QPw","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.08899998664856 sec","log_id":"053f2c0e-236c-46ce-ad13-d2c81f7581ba"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Get User Delivery Address List
     *
     * @module = admin
     *
     * @path = /member/get-delivery-address
     *
     * @method = POST
     *
     * @description = To get user delivery address list.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = username|<username>|string|optional|12345678|User's username.
     * @body = receiver_name|<receiver_name>|string|optional|12345678|Receiver's name.
     * @body = receiver_contact|<receiver_contact>|string|optional|60-18291237492|Receiver's contact number.
     * @body = country_id|<country_id>|integer|optional|1|Contry's id (Get From Dropdown - productCountry).
     * @body = state_id|<state_id>|integer|optional|1|State's id (Get From Dropdown - productCountry).
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     *
     * @response = {"data":{"list":[{"created_at":"2023-01-06 10:49:28","username":"87186585","address_label":"Default 1","receiver_name":"Cino Main","receiver_contact":"60-179321525","address_1":"Jalan Melana 2","address_2":"Setia Walk","country":"Malaysia","state_id":3,"state":"Kedah","zone_id":1,"zone":"West","city":"Kuala Lumpur","post_code":"43000"},{"created_at":"2023-01-09 18:37:28","username":"30712505","address_label":"Default 1","receiver_name":"Cino Dino","receiver_contact":"60-182121212","address_1":"Jalan Melana 2","address_2":"Setia Walk","country":"Malaysia","state_id":3,"state":"Kedah","zone_id":1,"zone":"West","city":"Kuala Lumpur","post_code":"43000"},{"created_at":"2023-01-06 11:09:06","username":"71254308","address_label":"Default 1","receiver_name":"Cino Main","receiver_contact":"60-179321525","address_1":"Jalan Melana 2","address_2":"Setia Walk","country":"Malaysia","state_id":3,"state":"Kedah","zone_id":1,"zone":"West","city":"Kuala Lumpur","post_code":"43000"},{"created_at":"2023-01-06 10:48:50","username":"88177624","address_label":"Default 1","receiver_name":"Cino Main","receiver_contact":"60-179321525","address_1":"Jalan Melana 2","address_2":"Setia Walk","country":"Malaysia","state_id":3,"state":"Kedah","zone_id":1,"zone":"West","city":"Kuala Lumpur","post_code":"43000"},{"created_at":"2023-01-06 10:48:17","username":"45264620","address_label":"Default 1","receiver_name":"Cino Main","receiver_contact":"60-179321525","address_1":"Jalan Melana 2","address_2":"Setia Walk","country":"Malaysia","state_id":3,"state":"Kedah","zone_id":1,"zone":"West","city":"Kuala Lumpur","post_code":"43000"}],"pagination":{"current_page":1,"from":1,"last_page":50,"per_page":5,"to":5,"total":246},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmNxcS5jb20vbWVtYmVyL2dldC1kZWxpdmVyeS1hZGRyZXNzIiwiaWF0IjoxNjczNDkwOTAwLCJleHAiOjE2NzM0OTU2NjQsIm5iZiI6MTY3MzQ5MjA2NCwianRpIjoiVUNmUEQ2bUZuWDV0TEM3YiIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.pMoIMqop0CFlk7g0qlf8yLOIMJvUqEO4IroCxi4amc4","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.11875915527344 sec","log_id":"f4236a0b-c80c-43de-8995-b0583354bef4"}
     * ##docs end##
     */
    public function getDeliveryAddress(Request $request)
    {
        if (MODULE == 'user') {
            $request->request->add(['uid' => Auth::user()->id ?? null]);
        }
        if (MODULE == 'admin') {
            $request->request->add(['isListing' => 1]);
        }

        $validator = Validator::make($request->all(), [
            'uid' => [
                'integer',
                Rule::exists('users', 'id')->whereIN('user_type', [User::$userType['user-account']]),
                Rule::when((MODULE == 'user'), function ($q) {
                    return $q = 'required';
                }),
            ],
            'username' => 'string',
            'receiver_name' => 'string',
            'receiver_contact' => 'string',
            'country_id' => 'integer',
            'state_id' => 'integer',
            'isListing' => 'integer',
            'limit' => 'integer',
            'page' => 'integer',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = UserAddress::get($validator->validated());

        $data['data'] = $res;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Create New Delivery Address
     *
     * @module = user
     *
     * @path = /v1/user/add-delivery-address
     *
     * @method = POST
     *
     * @description = To add new delivery address.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = label|<label>|string|required|label|Delivery address label.
     * @body = name|<name>|string|required|name|name.
     * @body = phone_no|<phone_no>|string|required|60-*********|Phone no.
     * @body = country_id|<country_id>|integer|required|129|Country.
     * @body = state_id|<state_id>|integer|required|1000|State.
     * @body = address_1|<address_1>|string|required|Address 1|Address 1.
     * @body = address_2|<address_2>|string|required|Address 2|Address 2.
     * @body = city|<city>|string|required|Cheras|City.
     * @body = post_code|<post_code>|string|required|56000|Post Code.
     * @body = is_default|<is_default>|integer|required|1 / 0|Default. 1 = Default, 0 = Not default.
     * @body = status|<0 / 1>|integer|required|1|Delivery Address's status. 1 = active , 0 = inactive
     *
     * @response = {"status":true,"message":"Created Successfully.","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLXVzZXIuY3FxLmNvbS91c2VyL2FkZC1kZWxpdmVyeS1hZGRyZXNzIiwiaWF0IjoxNjcwOTEzNTM3LCJleHAiOjE2NzA5MTgzMjEsIm5iZiI6MTY3MDkxNDcyMSwianRpIjoibnN5TnNLZ2NqV1VVZE1CQSIsInN1YiI6IjEwMDAwMDAiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.u5AP8yXWWXXxRikN1_-zx1AnLLo55oJDL-2IXk9IfHU","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.10747003555298 sec","log_id":"2f78abe1-c16d-4d44-a9a4-95549dd6123d"}
     * ##docs end##
     */
    public function addDeliveryAddress(Request $request)
    {

        if (MODULE == 'user') {
            $request->request->add(['uid' => Auth::user()->id ?? null]);
        }

        if (isset($request->phone_no)) {
            $replace = str_replace(' ', '', $request->phone_no);
            $request->merge(['phone_no' => $replace]);
        }

        $validator = Validator::make(
            $request->all(),
            [
                'uid' => [
                    'required',
                    'integer',
                    Rule::exists('users', 'id')->whereIN('user_type', [User::$userType['user-account']]),
                ],
                'label' => 'required|string|unique:user_address,label,NULL,id,status,1,deleted_at,NULL,user_id,'.$request['uid'],
                'name' => 'required|string',
                'phone_no' => [
                    'required',
                    'string',
                    'regex:/^[0-9]{1,3}-[0-9]{7,13}$/',
                    function ($q, $value, $fail) use (&$request) {
                        $phoneDetail = explode('-', $value);

                        $country = Country::where('country_code', $phoneDetail[0])->first()->name ?? null;

                        if (empty($country)) {
                            abort(400, 'Invalid Phone Number');
                        }

                        if ($country == 'vietnam') {
                            $phoneDetail[1] = (int) $phoneDetail[1];
                        }

                        $phone_no = $phoneDetail[0].'-'.$phoneDetail[1];
                        $request->merge(['phone_no' => $phone_no]);
                    },
                ],
                'country_id' => 'required|integer|exists:country,id,status,1',
                'state_id' => 'required|integer|exists:state,id,status,1',
                'address_1' => 'required_with:state_id|string',
                'address_2' => 'required_with:state_id|string',
                'city' => 'required_with:state_id|string',
                'post_code' => 'required_with:state_id|string',
                'is_default' => 'required|integer|in:0,1',
                'status' => 'required|integer|in:'.UserAddress::$status['active'],
            ],
            [
                'status.in' => 'Initial status can only be active',
            ]
        );

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = UserAddress::addAddress($validator->validated());
        abort(200, Lang::get('lang.admin-create-success'));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Get Delivery Address Detail
     *
     * @module = user
     *
     * @path = /v1/user/get-delivery-address-det
     *
     * @method = POST
     *
     * @description = To get delivery address details.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|optional|1|Delivery Address's id.
     *
     * @response = {"data":{"id":1,"user_id":1000000,"label":"Default 1","name":"ghostmate","phone_no":"60-*********","country_id":129,"state_id":2,"zone_id":1,"address_1":"Jalan Melana 1","address_2":"Setia Walk","city":"Kuala Lumpur","post_code":"43000","is_default":0,"status":1,"currency_id":5},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLXVzZXIuY3FxLmNvbS91c2VyL2dldC1kZWxpdmVyeS1hZGRyZXNzLWRldCIsImlhdCI6MTY3MjgyNjEzMCwiZXhwIjoxNjcyODI5Nzc3LCJuYmYiOjE2NzI4MjYxNzcsImp0aSI6Ik14eHZuZkpQTklUaGRFQ2MiLCJzdWIiOiIxMDAwMDAwIiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.GZaq3MaY-p7kbo9JMTw0-xGrp4Aor0D_o0BG50ejHTg","token_type":"bearer","kyc_flag":0,"timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.1285879611969 sec","log_id":"065feec6-1cd0-42d1-85f4-cd55ea4b5205"}
     * ##docs end##
     */
    public function getDeliveryAddressDetail(Request $request)
    {
        if (MODULE == 'user') {
            $uid = Auth::user()->id;
        }

        $validator = Validator::make($request->all(), [
            'id' => [
                Rule::when(MODULE == 'user', function ($q) use ($uid) {
                    return $q = 'exists:user_address,id,status,1,deleted_at,NULL,user_id,'.$uid;
                }, function ($q) {
                    return $q = 'exists:user_address,id,status,1,deleted_at,NULL';
                }),
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if (isset($request['id'])) {
            $categoryDetails = UserAddress::getDetails($validator->validated());
            abort(200, json_encode(['data' => $categoryDetails]));
        }
        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Edit Delivery Address Detail
     *
     * @module = user
     *
     * @path = /v1/user/edit-delivery-address
     *
     * @method = POST
     *
     * @description = To edit delivery address details.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|required|1|Delivery Address's id.
     * @body = label|<label>|string|required|label|Delivery address label.
     * @body = name|<name>|string|required|name|name.
     * @body = phone_no|<phone_no>|string|required|60-*********|Phone no.
     * @body = country_id|<country_id>|integer|required|129|Country.
     * @body = state_id|<state_id>|integer|required|1000|State.
     * @body = address_1|<address_1>|string|required|Address 1|Address 1.
     * @body = address_2|<address_2>|string|required|Address 2|Address 2.
     * @body = city|<city>|string|required|Cheras|City.
     * @body = post_code|<post_code>|string|required|56000|Post Code.
     * @body = is_default|<is_default>|integer|required|1 / 0|Default. 1 = Default, 0 = Not default.
     * @body = status|<0 / 1>|integer|required|1|Delivery Address's status. 1 = active , 0 = inactive
     *
     * @response = {"data":"Successfully updated address!","status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLXVzZXIuY3FxLmNvbS91c2VyL2VkaXQtZGVsaXZlcnktYWRkcmVzcyIsImlhdCI6MTY3MDkxMzUzNywiZXhwIjoxNjcwOTI0NTM5LCJuYmYiOjE2NzA5MjA5MzksImp0aSI6ImlUOG9nbTFKRUQ5Q3U5d3QiLCJzdWIiOiIxMDAwMDAwIiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.Te_URMW44ryGOglBgRNyOWotm9frnsCVPwkS1OWrZGU","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.11382699012756 sec","log_id":"25f78735-3367-4eee-8c3a-10142f91ef87"}
     * ##docs end##
     */
    public function editDeliveryAddress(Request $request)
    {
        if (MODULE == 'user') {
            $request->request->add(['uid' => Auth::user()->id ?? null]);
        }

        if (isset($request->phone_no)) {
            $replace = str_replace(' ', '', $request->phone_no);
            $request->merge(['phone_no' => $replace]);
        }

        $validator = Validator::make($request->all(), [
            'id' => [
                Rule::when(MODULE == 'user', function ($q) use ($request) {
                    return $q = 'exists:user_address,id,status,1,deleted_at,NULL,user_id,'.$request['uid'];
                }, function ($q) {
                    return $q = 'exists:user_address,id,status,1,deleted_at,NULL';
                }),
            ],
            'uid' => [
                'required',
                'integer',
                Rule::exists('users', 'id')->whereIN('user_type', [User::$userType['user-account']]),
            ],
            'label' => 'required|string|unique:user_address,label,'.$request['id'].',id,status,1,deleted_at,NULL,user_id,'.$request['uid'],
            'name' => 'required|string',
            'phone_no' => [
                'required',
                'string',
                'regex:/^[0-9]{1,3}-[0-9]{7,13}$/',
                function ($q, $value, $fail) use (&$request) {
                    $phoneDetail = explode('-', $value);

                    $country = Country::where('country_code', $phoneDetail[0])->first()->name ?? null;

                    if (empty($country)) {
                        abort(400, 'Invalid Phone Number');
                    }

                    if ($country == 'vietnam') {
                        $phoneDetail[1] = (int) $phoneDetail[1];
                    }

                    $phone_no = $phoneDetail[0].'-'.$phoneDetail[1];
                    $request->merge(['phone_no' => $phone_no]);
                },
            ],
            'country_id' => 'required|integer|exists:country,id,status,1',
            'state_id' => 'required|integer|exists:state,id,status,1',
            'address_1' => 'required_with:state_id|string',
            'address_2' => 'required_with:state_id|string',
            'city' => 'required_with:state_id|string',
            'post_code' => 'required_with:state_id|string',
            'is_default' => 'required|integer|in:0,1',
            'status' => 'required|integer|in:'.implode(',', array_values(UserAddress::$status)),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $updateDeliveryAddress = UserAddress::updateDeliveryAddress($validator->validated());
        abort(200, json_encode(['data' => $updateDeliveryAddress]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = member/user-primary-bank
     *
     * @module = admin
     *
     * @path = member/user-primary-bank
     *
     * @permissionName = User Primary Bank
     *
     * @menuType = api
     *
     * @parent = User List
     *
     * @method = POST
     *
     * @description = To get User's Primary Bank.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = user_id|<user_id>|integer|required|1|User's id.

     *
     * @response = {"data":{"country":"Malaysia","bank_name":"OCBC Bank Malaysia","bank_acc_no":"*********0","bank_acc_holder":"alicecheck","bank_branch":"-","bank_swift_code":"-"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************.wXvJxr_8hRNTpaZeucHYz7Rqkp1iHo2oDMaoEOQlmCw","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"b6e803f9-8c35-4c3a-8095-a31726d3320c"}
     * ##docs end##
     */
    public function getUserPrimaryBank(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => [
                'required',
                'integer',
                Rule::exists('users', 'id')->whereIN('user_type', [User::$userType['user-account']]),
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = UserBank::getUserPrimaryBank($validator->validated());

        abort(200, json_encode(['data' => $res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = member/user-default-address
     *
     * @module = admin
     *
     * @path = member/user-default-address
     *
     * @permissionName = User Default Address
     *
     * @menuType = api
     *
     * @parent = User List
     *
     * @method = POST
     *
     * @description = To get User's Default Address.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = user_id|<user_id>|integer|required|1|User's id.

     *
     * @response = {"data":{"id":1,"user_id":1000030,"label":"Home sweet home","name":"Stepgt","phone_no":"60-*********","country_id":129,"country":"Malaysia","state_id":5,"state":"Kuala Lumpur","zone_id":1,"zone":"West","address_1":"NO 007","address_2":"Jalan SPY","city":"SPY x Family","post_code":"56000","is_default":1,"status":1,"currency_id":5},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmMxcTIuY29tL21lbWJlci91c2VyLWRlZmF1bHQtYWRkcmVzcyIsImlhdCI6MTY3Mjg5MTc5NSwiZXhwIjoxNjcyODk4Njk0LCJuYmYiOjE2NzI4OTUwOTQsImp0aSI6InZORWhIc1FHTTczeGVQODQiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.T6tf7EZAT8X9o46aLTj8yLxJ2MDj3N3G9U7UHT4rHoI","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"be6e805c-f917-441c-b95b-52b7fd56b614"}
     * ##docs end##
     */
    public function getUserDefaultAddress(Request $request)
    {
        $request->request->add(['get_default' => 1]);
        $validator = Validator::make($request->all(), [
            'user_id' => [
                'required',
                'integer',
                Rule::exists('users', 'id')->whereIN('user_type', [User::$userType['user-account']]),
            ],
            'get_default' => 'required|integer|in:1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = UserAddress::getDetails($validator->validated());

        abort(200, json_encode(['data' => $res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = member/update-user-primary-bank
     *
     * @module = admin
     *
     * @path = member/update-user-primary-bank
     *
     * @permissionName = Update User Primary Bank
     *
     * @menuType = api
     *
     * @parent = User List
     *
     * @method = POST
     *
     * @description = To update User's Primary Bank.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|required|1|Primary User Bank's id.
     * @body = name_id|<name_id>|integer|required|1| Bank name's id from dropdown.
     * @body = account_holder|<account_holder>|string|required|Name|Account Holder's name.
     * @body = bank_account_number|<bank_account_number>|integer|required|1221212| Bank account number.
     * @body = branch|<branch>|integer|optional|1221212| Branch.
     * @body = swift_code|<swift_code>|integer|optional|1221212| Swift code.

     *
     * @response = {"data":{"country":"Malaysia","bank_name":"OCBC Bank Malaysia","bank_acc_no":"*********0","bank_acc_holder":"alicecheck","bank_branch":"-","bank_swift_code":"-"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************.wXvJxr_8hRNTpaZeucHYz7Rqkp1iHo2oDMaoEOQlmCw","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"b6e803f9-8c35-4c3a-8095-a31726d3320c"}
     * ##docs end##
     */
    public function updateUserPrimaryBank(Request $request)
    {
        if (MODULE != 'admin') {
            abort(400, 'invalid-function');
        }

        $validator = Validator::make($request->all(), [
            'id' => 'required|integer|exists:user_bank',
            'name_id' => 'required|integer',
            'account_holder' => 'required|string',
            'bank_account_number' => [
                'integer',
                'required',
            ],
            'branch' => [
                'string',
                'nullable',
            ],
            'swift_code' => [
                'string',
                'nullable',
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = UserBank::edit($validator->validated());

        abort(200, json_encode(['data' => $res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = member/update-user-default-address
     *
     * @module = admin
     *
     * @path = member/update-user-default-address
     *
     * @permissionName = Update User Default Address
     *
     * @menuType = api
     *
     * @parent = User List
     *
     * @method = POST
     *
     * @description = To update User's Default Address.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = label|<label>|string|required|label|Delivery address label.
     * @body = name|<name>|string|required|name|name.
     * @body = phone_no|<phone_no>|string|required|60-*********|Phone no.
     * @body = country_id|<country_id>|integer|required|129|Country.
     * @body = state_id|<state_id>|integer|required|1000|State.
     * @body = address_1|<address_1>|string|required|Address 1|Address 1.
     * @body = address_2|<address_2>|string|required|Address 2|Address 2.
     * @body = city|<city>|string|required|Cheras|City.
     * @body = post_code|<post_code>|string|required|56000|Post Code.
     *
     * @response = {"data":{"country":"Malaysia","bank_name":"OCBC Bank Malaysia","bank_acc_no":"*********0","bank_acc_holder":"alicecheck","bank_branch":"-","bank_swift_code":"-"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************.wXvJxr_8hRNTpaZeucHYz7Rqkp1iHo2oDMaoEOQlmCw","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"b6e803f9-8c35-4c3a-8095-a31726d3320c"}
     * ##docs end##
     */
    public function updateUserDefaultAddress(Request $request)
    {
        if (MODULE != 'admin') {
            abort(400, 'invalid-function');
        }

        if (isset($request->phone_no)) {
            $replace = str_replace(' ', '', $request->phone_no);
            $request->merge(['phone_no' => $replace]);
        }

        $validator = Validator::make($request->all(), [
            'id' => 'required:exists:user_address,id,status,1,is_default,1',
            'user_id' => [
                'required',
                'integer',
                Rule::exists('users', 'id')->whereIN('user_type', [User::$userType['user-account']]),
            ],
            'label' => 'required|string|unique:user_address,label,'.$request['id'].',id,status,1,deleted_at,NULL,user_id,'.$request['uid'],
            'name' => 'required|string',
            'phone_no' => [
                'required',
                'string',
                'regex:/^[0-9]{1,3}-[0-9]{7,13}$/',
                function ($q, $value, $fail) use (&$request) {
                    $phoneDetail = explode('-', $value);

                    $country = Country::where('country_code', $phoneDetail[0])->first()->name ?? null;

                    if (empty($country)) {
                        abort(400, 'Invalid Phone Number');
                    }

                    if ($country == 'vietnam') {
                        $phoneDetail[1] = (int) $phoneDetail[1];
                    }

                    $phone_no = $phoneDetail[0].'-'.$phoneDetail[1];
                    $request->merge(['phone_no' => $phone_no]);
                },
            ],
            'country_id' => 'required|integer|exists:country,id,status,1',
            'state_id' => 'required|integer|exists:state,id,status,1',
            'address_1' => 'required_with:state_id|string',
            'address_2' => 'required_with:state_id|string',
            'city' => 'required_with:state_id|string',
            'post_code' => 'required_with:state_id|string',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        UserAddress::updateDeliveryAddress($validator->validated() + ['is_default' => '1', 'status' => UserAddress::$status['active']]);

        abort(200, Lang::get('lang.admin-create-success'));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Set Favourite
     *
     * @module = user,app
     *
     * @path = user/set-favourite
     *
     * @method = POST
     *
     * @description = To set user favourite.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     *
     * @body = username|<username>|string|required|username|Username.
     * @body = status|<status>|integer|required|1/0|1 = Active, 0 = Inactive.
     *
     * @response = {"status":true,"message":"lang.granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS91c2VyL3NldC1mYXZvdXJpdGUiLCJpYXQiOjE2ODIzODY0NDYsImV4cCI6MTY4MjM5MzYyMywibmJmIjoxNjgyMzkwMDIzLCJqdGkiOiI2NDE0SjJqQ045eE80TlVDIiwic3ViIjoiMTAwMDAwMSIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.5fsbT_IPaAKFpUqvNlnA-k49FvMHURT5k1t7XqxR_ic","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.12333297729492 sec","log_id":"2d4020d1-5cf8-410f-8a59-663007814403"}
     * ##docs end##
     */
    public function setFavourite(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $user_id = Auth::user()->id ?? null;
            $request->request->add(['user_id' => $user_id]);
        }

        $userIDAry = [];
        $extraParam = [];
        $validator = Validator::make($request->all(), [
            'user_id' => [
                'required',
                'integer',
                function ($q, $value, $fail) use (&$userDetail, &$userIDAry) {
                    $user_id = User::with(['userDetail', 'treeSponsor'])->where('id', $value)->whereIN('user_type', [User::$userType['user-account']])->get()->map(function ($q) use (&$userDetail) {
                        foreach ($q->userDetail->keyBy('name') as $name => $value) {
                            $userDetail[$name] = $value->value;
                        }

                        return $q;
                    })->first();

                    if (empty($user_id)) {
                        $fail(Lang::get('lang.transfer-invalid-user'));

                        return;
                    }

                    $uplineIDAry = explode('/', $user_id->treeSponsor->trace_key);
                    $downlineIDAry = TreeSponsor::where('trace_key', 'like', $user_id->treeSponsor->trace_key.'/%')->pluck('user_id')->toArray();
                    $userIDAry = array_filter(array_merge($uplineIDAry, $downlineIDAry), function ($q) use ($value) {
                        return ! in_array($q, [$value, 1000000]);
                    });
                },
            ],
            'username' => [
                'required',
                'string',
                function ($q, $value, $fail) use (&$userIDAry, &$extraParam) {
                    $userDetail = User::where('username', $value)->whereIN('user_type', [User::$userType['user-account']])->first();
                    if (empty($userDetail) || ! in_array($userDetail->id, $userIDAry)) {
                        $fail(__('validation.exists', ['attribute' => str_replace('_', ' ', $q)]));

                        return;
                    }
                    $extraParam['favourite_id'] = $userDetail->id;
                },
            ],
            'status' => 'required|integer|in:'.implode(',', UserFavourite::$status),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $extraParam['type'] = 'transfer';
        UserFavourite::setFavourite(array_merge($validator->validated(), $extraParam));

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Third Party Login
     *
     * @module = user,app
     *
     * @path = user/third-party-login
     *
     * @method = POST
     *
     * @description = To login to third party.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     *
     * @body = product_id|<product_id>|integer|required|2000|Product ID.
     *
     * @response = {"data":{"login_link":"https:\/\/www.tk8.io?auth_code=af5065d7"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS91c2VyL3RoaXJkLXBhcnR5LWxvZ2luIiwiaWF0IjoxNjg2OTEzMjQ1LCJleHAiOjE2ODY5MTcxNTksIm5iZiI6MTY4NjkxMzU1OSwianRpIjoiQW1YdmloSWZveUF6bUlobiIsInN1YiI6IjEwMDAwNTMiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.5gEeODq1vSTvJ7mGvvwHfDWTcZiEErunrwIAGyNBlPs","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.13273811340332 sec","log_id":"65fc7406-d294-4e6b-9889-fc8e788b962b"}
     * ##docs end##
     */
    public function thirdPartyLogin(Request $request)
    {
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => Auth::user()->id]);
        }
        $validator = Validator::make($request->all(), [
            'user_id' => [
                'required',
                'integer',
                Rule::exists('users', 'id')->whereIN('user_type', [Models\User::$userType['user-account']]),
            ],
            'product_id' => [
                'required',
                'integer',
                function ($q, $value, $fail) use ($request) {
                    if (isset($request->user_id)) {
                        $userProductID = Models\UserProduct::where('user_id', $request->user_id)->where('product_id', $value)->first()->id ?? null;
                        if (empty($userProductID)) {
                            abort(400, json_encode(['Invalid User Product']));
                        }
                    }

                    $productID = Models\Product::where('id', $value)->where('status', Models\Product::$status['active'])->first()->id ?? null;
                    if (empty($productID)) {
                        abort(400, json_encode(['Invalid Product']));
                    }
                },
            ],
            'service_id' => [
                'nullable',
                'integer',
                Rule::exists('services', 'id')->where('status', true),
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data = Models\UserProduct::productAutoLogin($validator->validated());

        abort(200, json_encode(['data' => $data]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Update user Device Token
     *
     * @module = user,app
     *
     * @path = user/update-device-token
     *
     * @method = POST
     *
     * @description = To get user notification.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = user_id|<user_id>|integer|optional|<EMAIL>|user's id.
     * @body = token|<token>|string|optional|abcdfge12355|user's Device Token.
     *
     * @response = {"data":true,"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC5wdS1icmVlZC5jb20vc3RhZmYvdXBkYXRlLWRldmljZS10b2tlbiIsImlhdCI6MTY4MTM0ODkwNywiZXhwIjoxNjgxMzUyNTE5LCJuYmYiOjE2ODEzNDg5MTksImp0aSI6ImxHUUVmdTlzYlJRVGhVTGkiLCJzdWIiOiIyIiwicHJ2IjoiN2ViYjhhMmNjMWQ5NWI2MmM5NTk0YTIyYzljZWMyMmYzOGRiNWQzMSJ9.SI0Hzz6FtC3wfIJy3CXfqIIUoht2_1gtcCNJF2MVwEo","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.085700035095215 sec","log_id":"ebf9f35e-52f1-4828-8639-5a92c9143640"}
     * ##docs end##
     */
    public function updateDeviceToken(Request $request)
    {
        if ((in_array(MODULE, ['user', 'app'])) && (isset($request->user()->id))) {
            $request->request->add(['user_id' => auth()->user()->id]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'token' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data['data'] = Models\UserDevice::updateUserDToken($validator->validated());

        abort(200, json_encode($data));
    }

    public function sureceiveCallback(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone_no' => 'required|string',
            'otp_code' => 'required|integer',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $phoneNo = $validator->validated()['phone_no'];
        $otpCode = $validator->validated()['otp_code'];
        $phoneNo = Str::contains($phoneNo, '+') ? str_replace('+', '', $phoneNo) : $phoneNo;
        $getLastSR = SmsLog::where(['phone_no' => $phoneNo, 'otp_code' => $otpCode, 'provider' => 'sureceive'])->first();
        if (isset($getLastSR) && ($getLastSR->expired_at >= date('Y-m-d H:i:s')) && $getLastSR->deleted_at == null) {
            $getLastSR->update(['expired_at' => date('Y-m-d H:i:s', strtotime('-1 second'))]);
            $fullPhoneNo = json_decode($getLastSR->provider_request, true);
            $sendParams = [
                'phone_no' => $fullPhoneNo[0]['request']['countryCode'].'-'.$fullPhoneNo[0]['request']['phoneNo'],
                'otp_type' => array_search($getLastSR['type'], SmsLog::$type),
                'bypass_provider' => ['sureceive'],
                'otp_code' => $otpCode,
            ];
            Traits\Sms::sendOTPCode($sendParams);
        }

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Delete Account
     *
     * @module = app
     *
     * @path = user/delete-account
     *
     * @method = POST
     *
     * @description = To delete account
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = verify_string|<verify_string>|string|required|timetosaybyebye|Verification string.
     *
     * @response = {"status":true,"message":"Granted","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"3847160a-aeef-47e8-81ec-d988e0c7805d"}
     * ##docs end##
     */
    public function deleteAccount(Request $request)
    {
        $user = auth()->user();
        $v2AuthorizedUser = json_decode((SystemSetting::where('name', 'v2AuthorizedUser')->first()->value ?? null), true) ?? [];

        $request->merge(['user_id' => $user->id]);
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'verify_string' => 'required|string|in:timetosaybyebye',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if (in_array($user->username, $v2AuthorizedUser)) {
            User::deleteAccount($validator->validated());
        }

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Notification List
     *
     * @module = user,app
     *
     * @path = user/notification-list
     *
     * @method = POST
     *
     * @description = To get notification list.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.

     *
     * @response = {"list":[{"id":14,"notice_type":"manualDepositApproved","title":"Great news!","content":"Your cash deposit of RM200.00 is approved and credited to your wallet.","read":0,"created_at":"19\/09\/2023 15:43:52"},{"id":13,"notice_type":"manualDepositRejected","title":"Oh-no!","content":"We're sorry to inform you that your cash deposit of RM200.00 has been declined. Check your Top Up history for details.","read":0,"created_at":"19\/09\/2023 15:43:45"}],"pagination":{"current_page":1,"from":1,"last_page":6,"per_page":2,"to":2,"total":12},"meta":null,"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS91c2VyL25vdGlmaWNhdGlvbi1saXN0IiwiaWF0IjoxNjk1MDkwNTkxLCJleHAiOjE2OTc3MDIzMjcsIm5iZiI6MTY5NTExMDMyNywianRpIjoiY1l0RzVtRXpoVGZzWFY1ViIsInN1YiI6IjEwMDAwMDEiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.svGMxS6W5JkjoWInZpiOeFBOFETBUSaVH_cmEA_KWoM","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.14221906661987 sec","log_id":"64c94107-954f-4d24-9246-aad2a2aae079"}
     * ##docs end##
     */
    public function getNotificationList(Request $request)
    {
        if ((in_array(MODULE, ['user', 'app'])) && (isset($request->user()->id))) {
            $request->request->add(['user_id' => auth()->user()->id]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => [
                'required',
                'integer',
                Rule::exists('users', 'id')->whereIN('user_type', [User::$userType['user-account']]),
            ],
            'limit' => 'integer',
            'page' => 'integer',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\UserNotification::getList($validator->validated());

        abort(200, json_encode($res));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Read Notification
     *
     * @module = user,app
     *
     * @path = user/read-notification
     *
     * @method = POST
     *
     * @description = To read notification.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id_ary.*|<id_ary.*>|array|optional|[1,2,3]|Read's id.

     *
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS91c2VyL3JlYWQtbm90aWZpY2F0aW9uIiwiaWF0IjoxNjk1MDkwNTkxLCJleHAiOjE2OTc3MDI5OTcsIm5iZiI6MTY5NTExMDk5NywianRpIjoid1Y3WFVJWmIxUmNBdGZ3RiIsInN1YiI6IjEwMDAwMDEiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.YNJ8Y6zWhE3pmxuFzYGDO_4kFBLngP1-CBQe0KZ_Asg","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.18447113037109 sec","log_id":"5ad93984-e67f-4c08-a590-c6e0fbed8495"}
     * ##docs end##
     */
    public function readNotification(Request $request)
    {
        if ((in_array(MODULE, ['user', 'app'])) && (isset($request->user()->id))) {
            $request->request->add(['user_id' => auth()->user()->id]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => [
                'required',
                'integer',
                Rule::exists('users', 'id')->whereIN('user_type', [User::$userType['user-account']]),
            ],
            'id_ary' => 'array',
            'id_ary.*' => [
                'required_with:id_ary',
                'integer',
                function ($q, $value, $fail) use ($request) {
                    $checkNotification = UserNotification::where('id', $value)
                        ->where('user_id', ($request->user_id ?? null))
                        ->first();
                    if (empty($checkNotification)) {
                        $fail(__('validation.exists'));

                        return;
                    }
                },
            ],

        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        Models\UserNotification::readNotification($validator->validated());

        abort(200, Lang::get('lang.read-notification'));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Set Favourite Currency
     *
     * @module = user,app
     *
     * @path = user/set-favourite-currency
     *
     * @method = POST
     *
     * @description = To set user favourite currency.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     *
     * @body = is_clear_all|<is_clear_all>|integer|optional|1|Is Clear All Flag.
     * @body = currency_id|<currency_id>|string|required|1|Currency's Id (Requried Without is_clear_all).
     * @body = status|<status>|integer|required|1/0|1 = Active, 0 = Inactive (Requried Without is_clear_all)..
     *
     * @response = {"status":true,"message":"lang.granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS91c2VyL3NldC1mYXZvdXJpdGUiLCJpYXQiOjE2ODIzODY0NDYsImV4cCI6MTY4MjM5MzYyMywibmJmIjoxNjgyMzkwMDIzLCJqdGkiOiI2NDE0SjJqQ045eE80TlVDIiwic3ViIjoiMTAwMDAwMSIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.5fsbT_IPaAKFpUqvNlnA-k49FvMHURT5k1t7XqxR_ic","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.12333297729492 sec","log_id":"2d4020d1-5cf8-410f-8a59-663007814403"}
     * ##docs end##
     */
    public function setFavouriteCurrency(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $user_id = Auth::user()->id ?? null;
            $request->request->add(['user_id' => $user_id]);
        }

        $sysSetting = SystemSetting::whereIn('name', ['v2AuthorizedUser'])->whereNull('deleted_at')->get()->pluck('value', 'name');
        $v2Users = json_decode($sysSetting['v2AuthorizedUser'] ?? '') ?? [];

        $extraParam = [];
        $validator = Validator::make($request->all(), [
            'user_id' => [
                'required',
                'integer',
                function ($q, $value, $fail) use (&$userDetail, &$v2Users) {
                    $userDetail = User::where('id', $value)->whereIN('user_type', [User::$userType['user-account']])->first();

                    if (empty($userDetail) || (! in_array(($userDetail->username ?? null), $v2Users))) {
                        $fail(__('validation.in', ['attribute' => str_replace('_', ' ', $q)]));

                        return;
                    }
                },
            ],
            'is_clear_all' => 'integer|in:1',
            'currency_id' => [
                'required_without:is_clear_all',
                'integer',
                function ($q, $value, $fail) use (&$userIDAry, &$extraParam) {
                    $currency = Currencies::where('id', $value)->where('disabled', Currencies::$disabled['active'])->first();

                    if (empty($currency)) {
                        $fail(__('validation.exists', ['attribute' => str_replace('_', ' ', $q)]));

                        return;
                    }
                    $extraParam['favourite_id'] = $currency->id;
                },
            ],
            'status' => 'required_without:is_clear_all|integer|in:'.implode(',', UserFavourite::$status),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $extraParam['type'] = 'currency';
        UserFavourite::setFavourite(array_merge($validator->validated(), $extraParam));

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Get User Name
     *
     * @module = user,app
     *
     * @path = user/get-user-name
     *
     * @method = POST
     *
     * @description = To get user name.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     *
     * @body = username|<username>|string|required|60*********|downline username
     *
     * @response = {"data":{"name":"Alex"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS91c2VyL2dldC11c2VyLW5hbWUiLCJpYXQiOjE3MDQyNTI5MTQsImV4cCI6MTcwNDI1NjUzNSwibmJmIjoxNzA0MjUyOTM1LCJqdGkiOiJCTGk3Tm1DRE5MbzUyajhVIiwic3ViIjoiMTAwMDAwNSIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.CGUplkbD4ErMxeyBIAkAKt0GXqvFIiGTcmkeYgihgx8","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.1258430480957 sec","log_id":"fee7aeca-722a-47b7-9b60-f07fc7c2684b"}
     * ##docs end##
     */
    public function getUserName(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data = Models\User::getName(array_merge($validator->validated()));
        abort(200, json_encode(['data' => $data]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Get Link Device
     *
     * @module = user,app
     *
     * @path = user/get-link-device
     *
     * @method = POST
     *
     * @description = To linked device.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     *
     * @response = {"data":{"linked":true,"model":"CPH2217","brand":"OPPO","name":"super","created_at":"05\/04\/2024 11:43:09"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS91c2VyL2dldC1saW5rZWQtZGV2aWNlIiwiaWF0IjoxNzEyMjg4Nzk0LCJleHAiOjE3MTIyOTIzOTcsIm5iZiI6MTcxMjI4ODc5NywianRpIjoiSnVJeXQwVFdlMU1MVTdRVyIsInN1YiI6IjEwMDAwODYiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.msDoZbrj2N9TL91X2ly94XwJqKf6DTs3KI5gq63QzEg","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.032288789749146 sec","log_id":"148c4e4f-50a0-40d6-83d4-4218b2ee1b23"}
     * ##docs end##
     */
    public function getLinkedDevice(Request $request)
    {
        $user = auth()->user();
        $request->merge(['user_id' => $user->id]);

        $validator = Validator::make($request->all(), [
            'user_id' => 'required',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data = Models\UserDeviceInfo::getLinkedDevice(array_merge($validator->validated()));
        abort(200, json_encode(['data' => $data]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Get All Account
     *
     * @module = app
     *
     * @path = user/get-all-account
     *
     * @permissionName = Get All Account
     *
     * @menuType = api
     *
     * @parent = User List
     *
     * @method = POST
     *
     * @description = To get all account.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @response = {"data":{"login_token":"7f116b8ddbede94da40a7834e27b3829","login_url":"local-api-user.martin.com"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************.NI_bJerJPyVBfBkWGEjD5A4VOUeZhtZeGe1ROyVMukk","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.*************** sec","log_id":"d6044ed6-5f7d-4ff8-8af6-268d215bad8e"}
     * ##docs end##
     */
    public function getAllAccount(Request $request)
    {
        $userId = Auth::user()->id;
        $request->merge(['user_id' => $userId]);
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = User::getAllAccount($userId);

        abort(200, json_encode($res));
    }

    public function search(Request $request)
    {
        $query = $request->input('search');

        // $users = User::whereHas('device', function (Builder $query) {
        //     $query->whereNotNull('token')
        //         ->active();
        // })
        //     ->user()
        //     ->active()
        //     ->when($query, function ($query, $value) {
        //         $query->where('name', 'LIKE', "%{$value}%")
        //             ->orWhere('email', 'LIKE', "%{$value}%");
        //     })
        //     ->limit(10)
        //     ->get()
        //     ->map(function ($user, $key) {
        //         return [
        //             'id' => $user->id,
        //             'name' => $user->name,
        //             'email' => $user->email,
        //         ];
        //     });
        // ->whereHas('device', function (Builder $q) {
        //     $q->whereNotNull('token')->active();
        // })
        $users = User::select('id', 'name', 'email')
            ->user()
            ->active()
            ->when($query, function ($q, $value) {
                $q->where(function ($subQuery) use ($value) {
                    $subQuery->where('name', 'LIKE', "%{$value}%");
                });
            })
            ->limit(10)
            ->get()
            ->map(function ($user, $key) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                ];
            });

        abort(200, json_encode([
            'data' => $users,
        ]));
    }

    public function searchStore(Request $request)
    {
        $query = $request->input('search');

        $stores = Store::select(DB::raw('store_id AS id'), 'name')
            ->active()
            ->when($query, function ($q, $value) {
                $q->where(function ($subQuery) use ($value) {
                    $subQuery->where('name', 'LIKE', "%{$value}%")
                        ->orWhere('store_id', 'LIKE', "%{$value}%");
                });
            })
            ->limit(10)
            ->get();

        abort(200, json_encode([
            'data' => $stores,
        ]));
    }
}
