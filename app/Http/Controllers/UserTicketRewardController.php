<?php

namespace App\Http\Controllers;

use App\Exports\UserTicketRewardExport;
use App\Imports\UserTicketRewardImport;
use App\Models\RewardSetting;
use App\Models\RewardTicketPrize;
use App\Models\Store;
use App\Models\UserTicketReward;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class UserTicketRewardController extends Controller
{
    public function getUserTicketReward()
    {
        $data = [];

        $data['deposit_list'] = UserTicketReward::where('status', true)
            ->orderBy('total_deposit', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => substr($item->name, 0, 1) . '********' . substr($item->name, -1),
                    'total_deposit' => $item->total_deposit,
                    'total_token' => $item->total_token
                ];
            });

        $data['terms'] = RewardSetting::where('name', 'event.terms')->first()->value ?? '';

        abort(200, json_encode([
            'data' => $data
        ]));
    }

    public function getPrizeList()
    {
        $data = [];

        $data['prizes'] = RewardTicketPrize::where('status', true)
            ->orderBy('priority', 'asc')
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'image' => isset($item->image) ? env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $item->image : null,
                    'tagline' => $item->tagline,
                    'drawing_time' => $item->drawing_time,
                    'url' => $item->url,
                    'winner' => $item->winner_image ? explode(",", $item->winner_image) : null
                ];
            });

        $data['terms'] = RewardSetting::where('name', 'event.terms')->first()->value ?? '';

        abort(200, json_encode([
            'data' => $data,
        ]));
    }

    public function getAllUserTicketReward(Request $request)
    {
        $user_ticket_reward = UserTicketReward::all();
        abort(200, json_encode(['data' => $user_ticket_reward]));
    }

    public function storeUserTicketReward(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "user_id" => "nullable|integer",
            "store_id" => "nullable|integer",
            "name" => "required|string",
            "total_token" => "required|string",
            "total_deposit" => "required|string",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $user_ticket_reward = UserTicketReward::create([
            "user_id" => $request->user_id,
            "store_id" => $request->store_id,
            "name" => $request->name,
            "total_token" => $request->total_token,
            "total_deposit" => $request->total_deposit,
        ]);

        abort(200, json_encode(['data' => $user_ticket_reward]));
    }

    public function editUserTicketReward(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "user_id" => "nullable|integer",
            "store_id" => "nullable|integer",
            "name" => "required|string",
            "total_token" => "required|string",
            "total_deposit" => "required|string",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $user_ticket_reward = UserTicketReward::find($request->id);

        if (!$user_ticket_reward) {
            abort(400, 'User Ticket Reward found.');
        }

        $user_ticket_reward->update([
            "user_id" => $request->user_id,
            "store_id" => $request->store_id,
            "name" => $request->name,
            "total_token" => $request->total_token,
            "total_deposit" => $request->total_deposit,
        ]);

        abort(200, json_encode(['data' => $user_ticket_reward]));
        //
    }

    public function deleteUserTicketReward(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer'
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $user_ticket_reward = UserTicketReward::find($request->id);

        if (!$user_ticket_reward) {
            abort(400, 'User Ticket Reward not found.');
        }

        $user_ticket_reward->delete();

        abort(200, json_encode(['data' => []]));
    }

    public function importUserTicketReward(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:csv,txt,xlsx'
        ]);

        $res = (new UserTicketRewardImport())->import($request->file('file'), null, \Maatwebsite\Excel\Excel::XLSX);

        abort(200, json_encode(['data' => $res]));
    }

    public function exportUserTicketReward(Request $request)
    {
        $store = Store::where('store_id', $request->id)->first();
        if (!isset($store) && empty($store)) {
            abort(400, 'Store not found.');
        }

        $filename = 'ticket_reward_' . $store->name . '_' . date('Y-m-d_H_i');
        (new UserTicketRewardExport($store->store_id))->queue($filename . '.csv');

        sleep(1);

        $pathToFile = storage_path('app') . '/' . $filename . '.csv';
        if (!file_exists($pathToFile)) abort(400, json_decode('file-not-exists'));
        $data['base64'] = 'data:application/octet-stream;base64,' . base64_encode(file_get_contents($pathToFile));
        $data['file_name'] = $filename ?? null;
        $data['file_extension'] = 'csv';

        abort(200, json_encode(['data' => $data]));
    }
}
