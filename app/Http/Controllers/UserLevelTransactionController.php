<?php

namespace App\Http\Controllers;

use App\Models\UserLevelTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class UserLevelTransactionController extends Controller
{
    public function getRebateAmountReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d',
            'username' => 'string',
            'phone_no' => 'string',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $userLevelTransactions = UserLevelTransaction::getRebateAmountReport($validator->validated());
        $data = ['data' => $userLevelTransactions];

        abort(200, json_encode($data));
    }

    public function addUserLevelTransaction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'rebate_amount' => 'required|decimal:0,2|gt:0',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();
        $userId = $params['user_id'];
        $rebateAmount = $params['rebate_amount'];

        UserLevelTransaction::addUserLevelTransaction($userId, $rebateAmount);

        abort(200);
    }
}
