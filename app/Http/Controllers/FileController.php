<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Traits\S3Trait;

use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Lang;

class FileController extends Controller
{
    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = upload-s3
     * @module = admin,user,app
     * @path = upload-s3
     * @method = POST
     * @description = Upload to s3.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = filname|<filname>|string|required|Filname|Filname.
     * @body = content_type|<content_type>|string|required|image/JPEG|Content Type.
     * @body = is_public|<is_public>|boolean|optional|true|S3 is public folder
     * @body = min_size|<min_size>|int|optional|1024|Size of file
     * @body = max_size|<max_size>|int|optional|1024|Size of file
     * @response = {"status": true,"message": "Created Successfully.","access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLXVzZXIubWFydGluLmNvbS9reWMvcmVnaXN0ZXIta3ljIiwiaWF0IjoxNjYzMTM0MTA3LCJleHAiOjE2NjMxMzc3MDgsIm5iZiI6MTY2MzEzNDEwOCwianRpIjoicXhzTFBQZnRwd3IwMzRlVSIsInN1YiI6IjEwMDAwMDAiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.Vr_XvazQNH75iFPN9yVNw_iVRztaJqtHAxZyypS0PBY","token_type": "bearer","timezone": "Asia/Kuala_Lumpur","environment": "local","execution_duration": "0.010209798812866 sec","log_id": "0599ee6f-d36e-455f-92cd-afe92916629e"}
     * ##docs end##
     */
    public function uploadS3(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "filename" => "required|string",
            "content_type" => "required|string|in:image/png,image/jpg,image/jpeg,application/pdf",
            "is_public" => "boolean",
            "min_size" => "int",
            "max_size" => "int",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();
        $s3 = S3Trait::uploadToS3($params['filename'], $params['content_type'], $params['is_public'] ?? false, $params['min_size'] ?? 1024, $params['max_size'] ?? 10485760);

        abort(200, json_encode(["data" => $s3]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = upload-s3-multi
     * @module = admin,user,app
     * @path = upload-s3-multi
     * @method = POST
     * @description = Upload Multiple to s3.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = upload.*.filename|<filename>|string|required|Filname|Filname.
     * @body = upload.*.content_type|<content_type>|string|required|image/jpeg|Content Type.
     * @body = upload.*.is_public|<is_public>|boolean|optional|true|S3 is public folder
     * @body = upload.*.min_size|<min_size>|int|optional|1024|Size of file
     * @body = upload.*.max_size|<max_size>|int|optional|1024|Size of file
     * @response = {"data":[{"body":{"action":"https:\/\/martin-pub.s3.ap-southeast-1.amazonaws.com","method":"POST","enctype":"multipart\/form-data"},"field":{"acl":"private","X-Amz-Security-Token":"FwoGZXIvYXdzECkaDO2ILftSL35+Oi8wdiKxAc3GWQuFZKWi8pYJlvMVrCqHmr39Bs+9+vYYfFiMCk1abRgi4nQITcY3autLP+JI1ulHuvllQjWo6+n1ox63K+N2ABlHgY2NBFHJNpe5ug6y9cwHKSR1ML14hMCOegKW5DW3dtEKVzlg6wDyWj5Xdd1nWiIyJSxyNyAYee+cDImgOAA+y7gnJnmuVGy0VsI6kO1jYAxvDXbjWPn9JoXj7slgYLtJMtn7gj0g+EyV5yWityjK3fSZBjItvgioOTsTNJIcvEdhV70\/ZXYQIdabaXZ2vyQfQoGzXe4FWv5XHJ2gZhQlTW5d","key":"local\/2022\/10\/1664954058_8132","X-Amz-Credential":"ASIA43LUKFW24ZAWVONG\/20221005\/ap-southeast-1\/s3\/aws4_request","X-Amz-Algorithm":"AWS4-HMAC-SHA256","X-Amz-Date":"20221005T071418Z","Policy":"eyJleHBpcmF0aW9uIjoiMjAyMi0xMC0wNVQwODoxNDoxOFoiLCJjb25kaXRpb25zIjpbeyJhY2wiOiJwcml2YXRlIn0seyJidWNrZXQiOiJtYXJ0aW4tcHViIn0sWyJlcSIsIiRrZXkiLCJsb2NhbFwvMjAyMlwvMTBcLzE2NjQ5NTQwNThfODEzMiJdLFsiY29udGVudC1sZW5ndGgtcmFuZ2UiLCIxIiwiMTAwMCJdLHsiY29udGVudC10eXBlIjoiaW1hZ2VcL2pwZyJ9LHsieC1hbXotc2VjdXJpdHktdG9rZW4iOiJGd29HWlhJdllYZHpFQ2thRE8ySUxmdFNMMzUrT2k4d2RpS3hBYzNHV1F1RlpLV2k4cFlKbHZNVnJDcUhtcjM5QnMrOSt2WVlmRmlNQ2sxYWJSZ2k0blFJVGNZM2F1dExQK0pJMXVsSHV2bGxRaldvNituMW94NjNLK04yQUJsSGdZMk5CRkhKTnBlNXVnNnk5Y3dIS1NSMU1MMTRoTUNPZWdLVzVEVzNkdEVLVnpsZzZ3RHlXajVYZGQxbldpSXlKU3h5TnlBWWVlK2NESW1nT0FBK3k3Z25Kbm11Vkd5MFZzSTZrTzFqWUF4dkRYYmpXUG45Sm9YajdzbGdZTHRKTXRuN2dqMGcrRXlWNXlXaXR5akszZlNaQmpJdHZnaW9PVHNUTkpJY3ZFZGhWNzBcL1pYWVFJZGFiYVhaMnZ5UWZRb0d6WGU0Rld2NVhISjJnWmhRbFRXNWQifSx7IlgtQW16LURhdGUiOiIyMDIyMTAwNVQwNzE0MThaIn0seyJYLUFtei1DcmVkZW50aWFsIjoiQVNJQTQzTFVLRlcyNFpBV1ZPTkdcLzIwMjIxMDA1XC9hcC1zb3V0aGVhc3QtMVwvczNcL2F3czRfcmVxdWVzdCJ9LHsiWC1BbXotQWxnb3JpdGhtIjoiQVdTNC1ITUFDLVNIQTI1NiJ9XX0=","X-Amz-Signature":"9c993a15881fd8fe16f73ef16ccd8f19d30ceb1d16514fa37a4749ebc28afb87"},"url":"https:\/\/martin-pub.s3.ap-southeast-1.amazonaws.com\/local\/2022\/10\/1664954058_8132"},{"body":{"action":"https:\/\/martin-pub.s3.ap-southeast-1.amazonaws.com","method":"POST","enctype":"multipart\/form-data"},"field":{"acl":"private","X-Amz-Security-Token":"FwoGZXIvYXdzECkaDO2ILftSL35+Oi8wdiKxAc3GWQuFZKWi8pYJlvMVrCqHmr39Bs+9+vYYfFiMCk1abRgi4nQITcY3autLP+JI1ulHuvllQjWo6+n1ox63K+N2ABlHgY2NBFHJNpe5ug6y9cwHKSR1ML14hMCOegKW5DW3dtEKVzlg6wDyWj5Xdd1nWiIyJSxyNyAYee+cDImgOAA+y7gnJnmuVGy0VsI6kO1jYAxvDXbjWPn9JoXj7slgYLtJMtn7gj0g+EyV5yWityjK3fSZBjItvgioOTsTNJIcvEdhV70\/ZXYQIdabaXZ2vyQfQoGzXe4FWv5XHJ2gZhQlTW5d","key":"local\/2022\/10\/1664954058_5653","X-Amz-Credential":"ASIA43LUKFW24ZAWVONG\/20221005\/ap-southeast-1\/s3\/aws4_request","X-Amz-Algorithm":"AWS4-HMAC-SHA256","X-Amz-Date":"20221005T071418Z","Policy":"eyJleHBpcmF0aW9uIjoiMjAyMi0xMC0wNVQwODoxNDoxOFoiLCJjb25kaXRpb25zIjpbeyJhY2wiOiJwcml2YXRlIn0seyJidWNrZXQiOiJtYXJ0aW4tcHViIn0sWyJlcSIsIiRrZXkiLCJsb2NhbFwvMjAyMlwvMTBcLzE2NjQ5NTQwNThfNTY1MyJdLFsiY29udGVudC1sZW5ndGgtcmFuZ2UiLCIxIiwiMTAwMCJdLHsiY29udGVudC10eXBlIjoiaW1hZ2VcL2pwZyJ9LHsieC1hbXotc2VjdXJpdHktdG9rZW4iOiJGd29HWlhJdllYZHpFQ2thRE8ySUxmdFNMMzUrT2k4d2RpS3hBYzNHV1F1RlpLV2k4cFlKbHZNVnJDcUhtcjM5QnMrOSt2WVlmRmlNQ2sxYWJSZ2k0blFJVGNZM2F1dExQK0pJMXVsSHV2bGxRaldvNituMW94NjNLK04yQUJsSGdZMk5CRkhKTnBlNXVnNnk5Y3dIS1NSMU1MMTRoTUNPZWdLVzVEVzNkdEVLVnpsZzZ3RHlXajVYZGQxbldpSXlKU3h5TnlBWWVlK2NESW1nT0FBK3k3Z25Kbm11Vkd5MFZzSTZrTzFqWUF4dkRYYmpXUG45Sm9YajdzbGdZTHRKTXRuN2dqMGcrRXlWNXlXaXR5akszZlNaQmpJdHZnaW9PVHNUTkpJY3ZFZGhWNzBcL1pYWVFJZGFiYVhaMnZ5UWZRb0d6WGU0Rld2NVhISjJnWmhRbFRXNWQifSx7IlgtQW16LURhdGUiOiIyMDIyMTAwNVQwNzE0MThaIn0seyJYLUFtei1DcmVkZW50aWFsIjoiQVNJQTQzTFVLRlcyNFpBV1ZPTkdcLzIwMjIxMDA1XC9hcC1zb3V0aGVhc3QtMVwvczNcL2F3czRfcmVxdWVzdCJ9LHsiWC1BbXotQWxnb3JpdGhtIjoiQVdTNC1ITUFDLVNIQTI1NiJ9XX0=","X-Amz-Signature":"42caa4c778d02e978ae14dea3f8d4ca5e82aee8a46f84ac53b949805d48c2dac"},"url":"https:\/\/martin-pub.s3.ap-southeast-1.amazonaws.com\/local\/2022\/10\/1664954058_5653"}],"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLXVzZXIubWFydGluLmNvbS91cGxvYWQtczMtbXVsdGkiLCJpYXQiOjE2NjQ5NTM3ODEsImV4cCI6MTY2NDk1NzY1OCwibmJmIjoxNjY0OTU0MDU4LCJqdGkiOiJvNXBhamtjNklhRjNNUzhaIiwic3ViIjoiMTAwMDAwMCIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.xMPt2g97HKTi_nH1uTWOjk3g05Re3VgpyGg7i5R-Ma0","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.014318943023682 sec","log_id":"11a56e3a-160e-4040-b081-f668f50a70a2"}
     * ##docs end##
     */
    public function uploadS3Multi(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "upload" => "required|array",
            "upload.*.filename" => "required|string",
            "upload.*.content_type" => "required|string|in:image/png,image/jpg,image/jpeg,application/pdf",
            "upload.*.is_public" => "boolean",
            "upload.*.min_size" => "int",
            "upload.*.max_size" => "int",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();
        $s3 = S3Trait::uploadToS3Multi($validator->validated());

        abort(200, json_encode(["data" => $s3]));
    }
}
