<?php

namespace App\Http\Controllers;

use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;
use App\Models;
use Illuminate\Database\Eloquent\Model;

class ServiceController extends Controller
{
    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Add Services
     * @module = admin
     * @path = service/add-service
     * @method = POST
     * @description = To add service.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = step|<step>|integer|required|1/2|Step 1 for get Detail, Step 2 for Add Service. 
     * @body = language|<array>|array|required|[{"type":"en","name":"Transport"}|Service Name Language's Array. 
     * @body = language.*.type|<type>|string|required|en, cn, my|Language's type.
     * @body = language.*.name|<description>|string|required|Transport|Language's name.
     * @body = url|<url>|string|optional|http://www.gg.com|Service Url.
     * @body = icon|<icon>|string|required|image|Service Icon.
     * @body = status|<status>|int|required|1/0|Status. (Get From Dropdown : service_status_list)
     * @body = product_id|<product_id>|int|optional|2001|product_id. (Get From Dropdown : product)
     * @body = wallet_type|<wallet_type>|string|optional|wallet_type|wallet_type. Required if got option (Get From Dropdown : product)
     * 
     * @response = {"status":true,"message":"lang.granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnB1LWhvc3BpdGFsLmNvbS9wZXQvYWRkLXNwZWNpZXMiLCJpYXQiOjE2NzY4ODcwNDAsImV4cCI6MTY3Njg5NTc5NCwibmJmIjoxNjc2ODkyMTk0LCJqdGkiOiJFN2ZiTjBvc0x6RUhKb3ozIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.zMU-2ynTe1lEjtZnrjh4_aRERQX-z-voqjfiYxBknSI","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.27610182762146 sec","log_id":"49baea52-9cf1-4e75-ab5a-965c8ef8cee9"}
     * ##docs end##
     */
    public function addService(Request $request){
        $enLangKey = isset($request['language']) ? array_search('en',array_column($request['language'],'type')) : null;
        $request->request->add(['name' => Str::slug($request['language.'.$enLangKey.'.name'], '-')]);

        $validator = Validator::make($request->all(), [
            'step' => 'required|in:1,2',
            'name' => 'required_if:step,==,2|string|unique:services,name',
            'language' => 'required_if:step,==,2|array',
            'language.*.type' => [
                'required_if:step,==,2',
                'string',
                'distinct',
                'in:'.implode(',',array_keys(config('language'))),
                'uniqueType:language.*.type',
                Validator::extendDependent('uniqueType', function($attribute, $value, $parameters, $validator) use (&$request) {
                    if(!in_array('en',array_column($validator->getData()['language'],'type'))){
                        $customMessage = Lang::get("lang.en-required");
                        $validator->addReplacer('uniqueType', 
                            function($message, $attribute, $rule, $parameters) use ($customMessage) {
                                return \str_replace(':custom_message', $customMessage, $message);
                            }
                        );
                        return false;
                    }
                    
                    return true;

                }, ':custom_message'),
            ],
            'language.*.name' => [
                'required_if:step,==,2',
                'string',
                'uniqueName:language.*.type',
                Validator::extendDependent('uniqueName', function($attribute, $value, $parameters, $validator) {
                    $langType = Arr::get($validator->getData(), $parameters[0]);

                    $check = Models\LangCustom::where($langType, $value)->where('type', Models\LangCustom::$type['service'])->first();
                    if($check) return false;

                    return true;

                }, ':input has been used!'),
            ],
            "status" => "required_if:step,==,2|integer|in:".implode(",",Models\Services::$status),
            "url" => "nullable|string|url",
            'icon' => "required_if:step,==,2|string",
            "product_id" => [
                "nullable",
                "integer",
                function ($q, $value, $fail) use ($request, &$hasWallet, &$walletList) {
                    $product = Models\Product::with([
                        'productSetting' => function ($q){
                            $q->where('name', 'hasWalletList');
                            $q->where('value', '1');
                        }
                    ])
                    ->where('id', $value)->where('status', Models\Product::$status['active'])->first() ?? null;

                    $productID = $product->id;
                    if(empty($productID)){
                        abort(400, json_encode(['Invalid Product']));
                    }

                    $hasWallet = false;
                    $walletList = [];
                    if(!empty($product->productSetting)){
                        if(!empty($product->productSetting->first()->type)){
                            $hasWallet = true;
                            $walletList = explode(',',$product->productSetting->first()->type);
                        }
                    }
                },
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $validator2 = Validator::make($request->all(), [
            "wallet_type" => [
                Rule::requiredIf($hasWallet),
                Rule::prohibitedIf(!$hasWallet),
                "in:".implode(",",$walletList??[]),
            ],
        ]);

        if ($validator2->fails()) {
            abort(400, json_encode($validator2->errors()));
        }

        if($validator->validated()['step'] == 2){
            Models\Services::addService(array_merge($validator->validated(),['wallet_type'=>$request->wallet_type,]));
        }

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Service List
     * @module = admin
     * @path = service/get-service-list
     * @method = POST
     * @description = To get service list.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = status|<status>|integer|optional|1|To filter listing by status column (Get From Dropdown : service_status_list)
     * @body = order_by|<order_by>|string|optional|email|Order listing by column. (status , name)
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.
     * 
     * @response = {"data":{"list":[{"id":20,"name":"super90","url":null,"icon":null,"priority":21,"product_id":2001,"product_name":"TK8","product_display":"TK8","status":"active","status_display":"active","updated_by":"tkMins","updated_at":"19\/02\/2024 16:56:47"},{"id":19,"name":"super1","url":null,"icon":null,"priority":20,"product_id":null,"product_name":null,"product_display":"","status":"active","status_display":"active","updated_by":"tkMins","updated_at":"19\/02\/2024 16:44:11"},{"id":18,"name":"superm","url":null,"icon":null,"priority":19,"product_id":null,"product_name":null,"product_display":"","status":"active","status_display":"active","updated_by":"tkMins","updated_at":"19\/02\/2024 16:43:26"},{"id":16,"name":"Test Image Size","url":"http:\/\/www.test.xyz","icon":"staging\/2023\/12\/1701672022_7930","priority":18,"product_id":null,"product_name":null,"product_display":"","status":"inactive","status_display":"inactive","updated_by":"tkMins","updated_at":"04\/12\/2023 14:40:26"},{"id":15,"name":"Facebook","url":"https:\/\/www.facebook.com\/","icon":"staging\/2023\/10\/1696478224_2982","priority":2,"product_id":null,"product_name":null,"product_display":"","status":"coming-soon","status_display":"coming-soon","updated_by":"linhun1","updated_at":"05\/10\/2023 11:57:05"},{"id":14,"name":"Youtube","url":"https:\/\/www.youtube.com\/","icon":"staging\/2023\/10\/1696478263_1849","priority":4,"product_id":null,"product_name":null,"product_display":"","status":"active","status_display":"active","updated_by":"linhun1","updated_at":"05\/10\/2023 11:57:44"},{"id":13,"name":"Google","url":"https:\/\/telegram.me\/vmcs","icon":"staging\/2023\/10\/1696478248_8863","priority":3,"product_id":null,"product_name":null,"product_display":"","status":"active","status_display":"active","updated_by":"linhun1","updated_at":"20\/12\/2023 14:22:34"},{"id":17,"name":"entertainment11","url":"https:\/\/www.tk8.io\/en\/Slot","icon":"staging\/2023\/07\/1688381964_6094","priority":1,"product_id":null,"product_name":null,"product_display":"","status":"active","status_display":"active","updated_by":"admintest","updated_at":"26\/07\/2023 00:09:00"},{"id":12,"name":"Game","url":"https:\/\/www.tk8.io\/en\/Slot","icon":"staging\/2023\/07\/1688381964_6094","priority":1,"product_id":null,"product_name":null,"product_display":"","status":"active","status_display":"active","updated_by":"admintest","updated_at":"26\/07\/2023 00:09:00"},{"id":11,"name":"Advance Salary","url":"https:\/\/docs.google.com\/forms\/d\/1msAI46ywGFJrETaORacmB41zYPgOYLR-eZ67GUBS770\/edit?pli=1","icon":"staging\/2023\/06\/1686117993_4018","priority":17,"product_id":null,"product_name":null,"product_display":"","status":"inactive","status_display":"inactive","updated_by":"admintest","updated_at":"28\/06\/2023 23:35:12"},{"id":10,"name":"Advance Gaji","url":null,"icon":"staging\/2023\/06\/1686116252_2746","priority":16,"product_id":null,"product_name":null,"product_display":"","status":"inactive","status_display":"inactive","updated_by":"admintest","updated_at":"28\/06\/2023 23:35:12"},{"id":9,"name":"movie","url":"https:\/\/admin.fw.xyz\/","icon":"staging\/2023\/06\/1686123100_8588","priority":5,"product_id":null,"product_name":null,"product_display":"","status":"inactive","status_display":"inactive","updated_by":"admintest","updated_at":"28\/06\/2023 23:35:12"},{"id":8,"name":"testet","url":"https:\/\/admin.fw.xyz\/","icon":"staging\/2023\/05\/1684754410_6983","priority":14,"product_id":null,"product_name":null,"product_display":"","status":"inactive","status_display":"inactive","updated_by":"lele","updated_at":"28\/06\/2023 23:35:12"},{"id":7,"name":"test3","url":"https:\/\/www\/test.xyz","icon":"staging\/2023\/05\/1683711140_3852","priority":13,"product_id":null,"product_name":null,"product_display":"","status":"inactive","status_display":"inactive","updated_by":"pikachu","updated_at":"28\/06\/2023 23:35:12"},{"id":6,"name":"test1","url":"https:\/\/www\/test.xyz","icon":"staging\/2023\/05\/1683710955_3404","priority":12,"product_id":null,"product_name":null,"product_display":"","status":"inactive","status_display":"inactive","updated_by":"pikachu","updated_at":"28\/06\/2023 23:35:12"},{"id":5,"name":"test2","url":null,"icon":"staging\/2023\/05\/1683701213_6857","priority":7,"product_id":null,"product_name":null,"product_display":"","status":"inactive","status_display":"inactive","updated_by":"tkMins","updated_at":"28\/06\/2023 23:35:12"},{"id":4,"name":"Test","url":"https:\/\/www\/test.xyz","icon":"staging\/2023\/04\/1682679453_8221","priority":8,"product_id":null,"product_name":null,"product_display":"","status":"inactive","status_display":"inactive","updated_by":"pikachu","updated_at":"28\/06\/2023 23:35:12"},{"id":3,"name":"ultraman","url":"https:\/\/www.ultraman.xyz","icon":"staging\/2023\/04\/1682679487_2801","priority":9,"product_id":null,"product_name":null,"product_display":"","status":"inactive","status_display":"inactive","updated_by":"pikachu","updated_at":"28\/06\/2023 23:35:12"},{"id":2,"name":"kate2","url":"https:\/\/www.kate2.xyz","icon":"staging\/2023\/04\/1682679498_4055","priority":10,"product_id":null,"product_name":null,"product_display":"","status":"inactive","status_display":"inactive","updated_by":"pikachu","updated_at":"28\/06\/2023 23:35:12"},{"id":1,"name":"Kate","url":"https:\/\/www.kate.xyz","icon":"staging\/2023\/04\/1682679507_3556","priority":11,"product_id":null,"product_name":null,"product_display":"","status":"inactive","status_display":"inactive","updated_by":"pikachu","updated_at":"28\/06\/2023 23:35:12"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":20,"total":20},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL3NlcnZpY2UvZ2V0LXNlcnZpY2UtbGlzdCIsImlhdCI6MTcwODMzNzA0NSwiZXhwIjoxNzA4MzQwODIwLCJuYmYiOjE3MDgzMzcyMjAsImp0aSI6InNnaWR5N3dsdWZobUZyUXQiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.T4Sf1zBz7_SC9ywUyljfAp-xaNn7Tn9avpdcaZURRts","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.029500007629395 sec","log_id":"51d3912c-4d12-4a3e-9062-3e879bd269b6"}
     * ##docs end##
     */
    public function getServiceList(Request $request){
        $validator = Validator::make($request->all(), [
            'status' => 'integer|in:'.implode(',',Models\Services::$status),
            'order_by' => 'string',
            'order_sort' => 'string|in:asc,desc',
            'limit' => 'integer',
            'page' => 'integer',
            'see_all' => 'integer|in:0,1',
            'created_from' => 'required_with:created_to|string|date_format:Y-m-d',
            'created_to' => 'required_with:created_from|string|date_format:Y-m-d|after_or_equal:created_from',
            'updated_from' => 'required_with:updated_to|string|date_format:Y-m-d',
            'updated_to' => 'required_with:updated_from|string|date_format:Y-m-d|after_or_equal:updated_from',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $report = Models\Services::getServiceList($validator->validated());

        $data['data'] = $report;
        abort(200,json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Service Detail
     * @module = admin
     * @path = service/get-service-detail
     * @method = POST
     * @description = To get service detail.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|interger|required|1|Service's id.
     * 
     * @response = {"data":{"id":20,"language":[{"type":"en","name":"super90"},{"type":"cn","name":"super9"},{"type":"vn","name":"super90"},{"type":"indo","name":"super9"},{"type":"th","name":"super90"},{"type":"ben","name":"super9"}],"url":null,"priority":21,"product_id":2001,"product_name":"TK8","product_display":"TK8","icon":null,"icon_display":null,"status":1,"status_display":"active"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL3NlcnZpY2UvZ2V0LXNlcnZpY2UtZGV0YWlsIiwiaWF0IjoxNzA4MzM3MDQ1LCJleHAiOjE3MDgzNDA5MjcsIm5iZiI6MTcwODMzNzMyNywianRpIjoiWWh0SHlsa3FQT2hpM0Y4biIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.DDc9aR-3aiOX-TgAs6wS9B0o8536dh-VHPOLTBjBFkk","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.087066888809204 sec","log_id":"5e5a206b-0dce-4661-ab52-e02077e72a9a"}
     * ##docs end##
     */
    public function getServiceDetail(Request $request){

        $validator = Validator::make($request->all(), [
            'id'=> 'required|exists:services,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $report = Models\Services::getServiceDetail($validator->validated());

        $data['data'] = $report;
        abort(200,json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Edit Services
     * @module = admin
     * @path = service/edit-service
     * @method = POST
     * @description = To edit service.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|interger|required|1|Service's id.
     * @body = language|<array>|array|required|[{"type":"en","name":"Dog"}|Service Name Language's Array. 
     * @body = language.*.type|<type>|string|required|en, cn, my|Language's type.
     * @body = language.*.name|<description>|string|required|Dog|Language's name.
     * @body = priority|<priority>|integer|optional|1|Service Priority.
     * @body = url|<url>|string|optional|http://www.gg.com|Service Url.
     * @body = icon|<icon>|string|optional|image|Service Icon.
     * @body = status|<status>|int|required|1/0|Status. (Get From Dropdown : service_status_list)
     * @body = product_id|<product_id>|int|optional|2001|product_id. (Get From Dropdown : product)
     * @body = wallet_type|<wallet_type>|string|optional|wallet_type|wallet_type. Required if got option (Get From Dropdown : product)
     * 
     * @response = {"data":"Successfully updated breed!","status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnB1LWhvc3BpdGFsLmNvbS9wZXQvZWRpdC1icmVlZCIsImlhdCI6MTY3Njk2MDU2OSwiZXhwIjoxNjc2OTY1NzkwLCJuYmYiOjE2NzY5NjIxOTAsImp0aSI6InVhb0JNTFVIcU8wMGtqVmQiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.y2IvUPlTduyTAx8q0sFHtv7n-XpzzxQweygssTRX-sM","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.22529792785645 sec","log_id":"1a1388ce-f8b9-4ee7-b822-e6e452bae09a"}
     * ##docs end##
     */
    public function editService(Request $request){
        $enLangKey = isset($request['language']) ? array_search('en',array_column($request['language'],'type')) : null;
        if(isset($enLangKey)) $request->request->add(['name' => Str::slug($request['language.'.$enLangKey.'.name'], '-')]);
        $slug = null;

        $validator = Validator::make($request->all(), [
            'id'=> [
                'required',
                function ($q, $value, $fail) use (&$slug) {
                    $service = Models\Services::find($value);
                    if(empty($service)){
                        $fail(__('validation.in', ["attribute" => str_replace('_', ' ', $q)]));
                        return;
                    }

                    $slug = $service->name;
                }
            ],
            'name' => 'required_with:language|string|unique:services,name,'.$request->id.',id',
            'language' => 'array',
            'language.*.type' => [
                'string',
                'distinct',
                'in:'.implode(',',array_keys(config('language'))),
                'uniqueType:language.*.type',
                Validator::extendDependent('uniqueType', function($attribute, $value, $parameters, $validator) use (&$request) {
                    if(!in_array('en',array_column($validator->getData()['language'],'type'))){
                        $customMessage = Lang::get('lang.en-required');
                        $validator->addReplacer('uniqueType', 
                            function($message, $attribute, $rule, $parameters) use ($customMessage) {
                                return \str_replace(':custom_message', $customMessage, $message);
                            }
                        );
                        return false;
                    }
                    return true;
                }, ':custom_message'),
            ],
            'language.*.name' => [
                'string',
                'uniqueName:language.*.type',
                Validator::extendDependent('uniqueName', function($attribute, $value, $parameters, $validator) use (&$slug){
                    $langType = Arr::get($validator->getData(), $parameters[0]);
                    $check = Models\LangCustom::where($langType, $value)->where('type',Models\LangCustom::$type['service'])->first();
                    if($check && ($check->slug != $slug)) return false;

                    return true;

                }, ':input has been used!'),
            ],
            "priority" => "integer",
            "url" => "nullable|string|url",
            'icon' => "string",
            "status" => [
                "integer",
                "in:".implode(",",Models\Services::$status),
                function ($q, $value, $fail) use ($request){
                    $service = Models\Services::whereIn('status',Arr::except(Models\Services::$status,['inactive']))->pluck('id')->toArray();
                    $service = array_filter($service,function ($q) use ($request){
                        return $q != $request->id;
                    });

                    if((in_array($value,Arr::except(Models\Services::$status,['inactive']))) && (COUNT($service) + 1) > config('general.max_services')){
                        $fail('Exceed Limit');
                        return;
                    }
                }
            ],
            "product_id" => [
                "nullable",
                "integer",
                function ($q, $value, $fail) use ($request, &$hasWallet, &$walletList) {
                    $product = Models\Product::with([
                        'productSetting' => function ($q){
                            $q->where('name', 'hasWalletList');
                            $q->where('value', '1');
                        }
                    ])
                    ->where('id', $value)->where('status', Models\Product::$status['active'])->first() ?? null;

                    $productID = $product->id;
                    if(empty($productID)){
                        abort(400, json_encode(['Invalid Product']));
                    }

                    $hasWallet = false;
                    $walletList = [];
                    if(!empty($product->productSetting)){
                        if(!empty($product->productSetting->first()->type)){
                            $hasWallet = true;
                            $walletList = explode(',',$product->productSetting->first()->type);
                        }
                    }
                },
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $validator2 = Validator::make($request->all(), [
            "wallet_type" => [
                Rule::requiredIf($hasWallet),
                Rule::prohibitedIf(!$hasWallet),
                "in:".implode(",",$walletList??[]),
            ],
        ]);

        if ($validator2->fails()) {
            abort(400, json_encode($validator2->errors()));
        }

        $updateRes = Models\Services::editService(array_merge($validator->validated(),['wallet_type'=>$request->wallet_type,]));

        abort(200, json_encode(['data'=> $updateRes]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Arrange Services
     * @module = admin
     * @path = service/arrange-service
     * @method = POST
     * @description = To edit service.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = arrange_data.*|<arrange_data>.*|array|required|[{'id':1,'priority':2},{'id':2,'priority':1}]|Arrange Services Priority Array
     * @body = arrange_data.*.id|<id>|integer|required|1|Services's id
     * @body = arrange_data.*.priority|<priority>|integer|required|1|Services's priority
     * 
     * @response = {"status":true,"message":"lang.update-successfully","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL3NlcnZpY2UvYXJyYW5nZS1zZXJ2aWNlIiwiaWF0IjoxNjgyNjUyMjE2LCJleHAiOjE2ODI2NTY5NTgsIm5iZiI6MTY4MjY1MzM1OCwianRpIjoiQXJGS0JSWmFMOFFmdHdXeCIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.TkSAVVvWZH59ZWD8Dng0-m_2XdEH-4BS429lhlu1mhE","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.13086485862732 sec","log_id":"734c2a99-4c42-47da-89a2-43525abf4ae0"}
     * ##docs end##
     */
    public function arrangeService(Request $request){
        $validator = Validator::make($request->all(), [
            'arrange_data' => [
                'required',
                'array',
                function ($q, $value, $fail) use ($request){
                    $count = Models\Services::get()->count();
                    if($count != COUNT($value)){
                        $fail('Service data incompleted. Failed to arrange.');
                        return;
                    }
                }
            ],
            'arrange_data.*.id' => [
                'required',
                'integer',
                'exists:services,id',
                'distinct'
            ],
            'arrange_data.*.priority' => [
                'required',
                'integer',
                'distinct',
                'between:1,'.COUNT($request->arrange_data),
            ]
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $updateRes = Models\Services::arrangeService($validator->validated());

        abort(200, $updateRes);
    }
}
