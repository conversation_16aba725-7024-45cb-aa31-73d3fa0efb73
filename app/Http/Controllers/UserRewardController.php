<?php

namespace App\Http\Controllers;

use App\Models\Deposit;
use App\Models\Reward;
use App\Models\User;
use App\Models\UserReward;
use App\Models\UserRewardLog;
use App\Models\UserTicketReward;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class UserRewardController extends Controller
{
    public function getUserReward(Request $request)
    {
        $userReward = UserReward::where('user_id', auth()->user()->id)->first();
        if ($userReward == null) {
            UserReward::create([
                'user_id' => auth()->user()->id,
                'total_points' => 0,
                'total_claimed' => 0,
                'last_deposit_at' => null,
                'ref_deposit_id' => null,
                'status' => true,
            ]);
        }

        $lastClaimAt = $userReward->last_claimed_at ?? '';
        $data = [];
        $data['total_points'] = (string) $userReward?->total_token ?? (string) 0;
        $data['total_claimed'] = (string) $userReward?->total_claimed ?? (string) 0;
        $data['is_claimed'] = $lastClaimAt && $lastClaimAt->isToday();

        $data['can_claim'] = $userReward?->last_deposit_at && $userReward->last_deposit_at->isToday();

        // Deposit::where('user_id', auth()->user()->id)
        //     ->whereDate('created_at', Carbon::today())
        //     ->where('receivable_amount', '>=', 10)
        //     ->where('status', 1)
        //     ->count() > 0;

        if ($userReward && ! ($lastClaimAt && ($lastClaimAt->isToday() || $lastClaimAt->isYesterday()))) {
            $userReward->update([
                'total_claimed' => 0,
            ]);
            $userReward = UserReward::where('user_id', auth()->user()->id)->first();
        }

        // $count = 1;
        $data['items'] = Reward::where('status', true)->select('id', 'name', 'point')
            ->get()->map(function ($q) use (&$count, $userReward) {
                return [
                    'days' => $q->id,
                    'name' => $q->name,
                    'point' => $q->point,
                    'is_claimed' => (isset($userReward) && $userReward != null) ? $userReward->total_claimed >= $q->id : false,
                ];

                // $count++;
            });

        $data['deposit_list'] = UserTicketReward::where('status', true)
            ->orderBy('total_deposit', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => substr($item->name, 0, 1) . '********' . substr($item->name, -1),
                    'total_deposit' => $item->total_deposit,
                    'total_token' => $item->total_token,
                ];
            });

        $data['terms'] = 'Event: Daily Check In and Lucky Wheel Spin How To Claim:<br />
&bull;&#8288; &#8288;This promotion is available for Uwin members.<br />
&bull;&#8288; &#8288;Prizes won in the Lucky Wheel Spin will be issued directly into members main wallet.<br />
&bull;&#8288; &#8288;Members are required to verify their identity with their phone number and screenshot of the pop-up message for the Special Prize to Customer Service to claim the special prize.<br /><br />
&bull;&#8288; &#8288;All member needs to deposit at least RM100 in shop or online deposit to get 1 free spin daily, the free spin will not accumulate and will expire/ be forfeited the next day.<br />
&bull;&#8288; &#8288;600 points will be automatically converted to one (1) additional spin. The total points displayed on the daily mission page will only be deducted after members used up their daily login spins.<br />
&bull;&#8288; &#8288;There will be no expiration nor max cap for the activity points earned.<br />
&bull;&#8288; &#8288;Physical prizes, if applicable, are expected to be received by winners via their registered shop.<br />
&bull;&#8288; &#8288;Prizes are subject to availability and photos are for illustration purposes only.<br />
&bull;&#8288; &#8288;Members who have not made a deposit or have a history risk record will be ineligible to participate in this promotion.<br />
&bull;&#8288; &#8288;Participating members must accept and comply with all the terms mentioned above as well as all relevant and rules and regulations stated on the Uwin Apps.<br />
&bull;&#8288; &#8288;Uwin may at its discretion disqualify/reject any Eligible Participant who does not comply with the Terms and Conditions stated herein and/or is found or suspected of tampering with the Campaign and/or its process or the operation of the Campaign. Tampering shall include fraudulent activities involving any act of deceit and/or deception and/or cheating with regards to the Campaign and/or the eligible channel.';
        abort(200, json_encode(['data' => $data]));
    }

    public function claimUserReward(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $request->request->add(['user_id' => auth()->user()->id]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id,deleted_at,NULL',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $userReward = UserReward::where('user_id', $request->user_id)->first();
        if (isset($userReward) && $userReward != null) {

            $lastClaimAt = $userReward->last_claimed_at;

            // Once Claim, need to wait next day 00:00
            if ($lastClaimAt && $lastClaimAt->isToday()) {
                // User already checked in today
                abort(400, json_encode('You have already claimed this reward! your next check in date is ' . now()->addDay()->startOfDay()));
            }

            // Reset user reward after 7 days or total claimed is 7
            $count = Reward::where('status', true)->count();
            if ($userReward->total_claimed == $count) {
                $userReward->total_claimed = 0;
            }

            // If Reward not found
            $reward = Reward::where('id', $userReward->total_claimed + 1)->first();
            if (! isset($reward)) {
                abort(400, json_encode('Reward not found'));
            }

            // Minimum deposit not met
            $canClaim = $userReward?->last_deposit_at && $userReward->last_deposit_at->isToday();
            if (! $canClaim) {
                abort(400, json_encode('Minimum deposit not met'));
            }

            // Update existing data
            $userReward->update([
                'total_token' => $userReward->total_token + $reward->point,
                'total_claimed' => $userReward->total_claimed + 1,
                'last_claimed_at' => now(),
            ]);
            $userReward = UserReward::where('user_id', $request->user_id)->first();
        } else {
            $reward = Reward::where('id', Reward::orderBy('created_at', 'asc')->min('id'))->first();
            UserReward::create([
                'user_id' => $request->user_id,
                'total_token' => $reward->point, // Hard code
                'total_claimed' => 1,
                'last_claimed_at' => now(),
            ]);
        }

        UserRewardLog::create([
            'user_id' => $request->user_id,
            'reward_id' => $reward->id,
            'points' => $reward?->point,
            'meta' => json_encode([
                'user_id' => $request->user_id,
                'reward_id' => $reward->id,
                'point' => $reward?->point,
                'total_token' => $userReward?->total_token ?? 0 + $reward?->point ?? 0,
                'total_claimed' => $userReward?->total_claimed ?? 0 + 1,
                'last_claimed_at' => now(),
            ]),
        ]);

        // $count = 1;
        $data = [
            'total_points' => (string) $userReward?->total_token ?? (string) 0 + $reward?->point ?? (string) 0,
            'total_claimed' => (string) $userReward?->total_claimed ?? (string) 0 + 1,
            'last_claimed_at' => now(),
            'is_claimed' => true,
            'can_claim' => false,
            'items' => Reward::where('status', true)->select('id', 'name', 'point')
                ->get()->map(function ($q) use (&$count, $userReward) {
                    return [
                        'days' => $q->id,
                        'name' => $q->name,
                        'point' => $q->point,
                        'is_claimed' => (isset($userReward) && $userReward != null) ? $userReward->total_claimed >= $q->id : false,
                    ];
                    // $count++;
                }),

        ];

        abort(200, json_encode(['data' => $data]));

        // abort(200, json_encode(['data' => $data]));

        // if (in_array(MODULE, ['user', 'app'])) {
        //     $request->request->add(['user_id' => auth()->user()->id]);
        // }

        // $validator = Validator::make($request->all(), [
        //     "user_id" => "required|exists:users,id,deleted_at,NULL",
        // ]);

        // if ($validator->fails()) {
        //     abort(400, json_encode($validator->errors()));
        // }

        // $userReward = UserReward::where('user_id', $request->user_id)->first();
        // if (isset($userReward) && $userReward != null) {

        //     if ($userReward->last_claimed_at->diffInDays(now()) < 1) {
        //         abort(400, json_encode(['You have already claimed this reward']));
        //     }

        //     // Reset user reward after 7 days or total claimed is 7
        //     $count = Reward::where('status', true)->count();
        //     if ($userReward->total_claimed == $count) {
        //         $userReward->total_claimed = 0;
        //     }

        //     $reward = Reward::where('id', $userReward->total_claimed + 1)->first();
        //     if (!isset($reward)) {
        //         abort(400, json_encode(['Reward not found']));
        //     }

        //     if ($userReward->last_claimed_at->diffInDays(now()) > 1) {
        //         $userReward->update([
        //             'total_points' => $userReward->total_points + $reward->point,
        //             'total_claimed' => 1,
        //             'last_claimed_at' => now(),
        //         ]);
        //     } else {
        //         $userReward->update([
        //             'total_points' => $userReward->total_points + $reward->point,
        //             'total_claimed' => $userReward->total_claimed + 1,
        //             'last_claimed_at' => now(),
        //         ]);
        //     }
        // } else {

        //     UserReward::create([
        //         'user_id' => $request->user_id,
        //         'total_points' => 10, // Hard code
        //         'total_claimed' => 1,
        //         'last_claimed_at' => now(),
        //     ]);
        // }

        // $count = 1;
        // $data = [
        //     'total_points' => $userReward?->total_points ?? 0 + $reward?->point ?? 0,
        //     'total_claimed' => $userReward?->total_claimed ?? 0 + 1,
        //     'last_claimed_at' => now(),
        //     'is_claimed' => true,
        //     'items' => Reward::where('status', true)->select('id', 'name', 'point')
        //         ->get()->map(function ($q) use (&$count, $userReward) {
        //             return [
        //                 'days' => $q->id,
        //                 'name' => $q->name,
        //                 'point' => $q->point,
        //                 'is_claimed' => (isset($userReward) && $userReward != null) ? $userReward->total_claimed >= $count : false
        //             ];
        //             $count++;
        //         })
        // ];

    }
}
