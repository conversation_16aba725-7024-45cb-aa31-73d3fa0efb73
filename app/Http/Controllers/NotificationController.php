<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\User;
use App\Traits\FirebaseTrait;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class NotificationController extends Controller
{
    public function getNotifications(Request $request)
    {
        $notifications = Notification::getList($request->all());

        abort(200, json_encode([
            'data' => $notifications,
        ]));
    }

    public function storeNotification(Request $request)
    {
        $data = $request->validate([
            'notif_data' => 'required|array',
            'notif_data.*.title' => 'required|string',
            'notif_data.*.content' => 'nullable|string',
            'notif_data.*.language_type' => 'required|string|distinct|in:'.implode(',', array_keys(config('language'))),
        ]);

        $notification = Notification::create();
        $details = $data['notif_data'] ?? null;

        if ($details) {
            foreach ($details as $detail) {
                $notification->details()->create([
                    'language_type' => $detail['language_type'] ?? null,
                    'title' => $detail['title'] ?? null,
                    'content' => $detail['content'] ?? null,
                ]);
            }
        }

        abort(200, json_encode([
            'data' => $notification->load('details'),
        ]));
    }

    public function editNotification(Request $request)
    {
        $request->validate([
            'id' => ['required', 'exists:notifications,id'],
        ]);

        $notification = Notification::with('details')
            ->where('id', $request->id)
            ->first();

        if (! $notification) {
            abort(404, 'Notification not found.');
        }

        $data = [
            'id' => $notification->id,
            'notif_data' => $notification->details->toArray(),
        ];

        abort(200, json_encode([
            'data' => $data,
        ]));
    }

    public function updateNotification(Request $request)
    {
        $data = $request->validate([
            'id' => 'required|exists:notifications,id',
            'notif_data' => 'required|array',
            'notif_data.*.title' => 'required|string',
            'notif_data.*.content' => 'nullable|string',
            'notif_data.*.language_type' => 'required|string|distinct|in:'.implode(',', array_keys(config('language'))),
        ]);

        DB::beginTransaction();

        try {
            $notification = Notification::find($request->id);

            if (! $notification) {
                throw new Exception('Notification not found.', 404);
            }

            $details = $data['notif_data'] ?? null;
            $lang = [];

            if ($details) {
                foreach ($details as $detail) {
                    $lang[] = $detail['language_type'];

                    $notification->details()
                        ->updateOrCreate(
                            ['language_type' => $detail['language_type']],
                            [
                                'title' => $detail['title'] ?? null,
                                'content' => $detail['content'] ?? null,
                            ]
                        );
                }
            }

            $notification->details()
                ->whereNotIn('language_type', $lang)
                ->delete();

            DB::commit();

            abort(200, json_encode([
                'data' => $notification->load('details'),
            ]));
        } catch (Exception $e) {
            DB::rollBack();

            abort($e->getCode(), $e->getMessage());
        }
    }

    public function deleteNotification(Request $request)
    {
        $request->validate([
            'id' => ['required', 'exists:notifications,id'],
        ]);

        $notification = Notification::find($request->id);

        if (! $notification) {
            abort(400, 'Notification not found.');
        }

        $notification->delete();

        abort(200, json_encode([
            'data' => [],
        ]));
    }

    public function sendNotification(Request $request)
    {
        $request->validate([
            'id' => ['required', 'exists:notifications,id'],
            'send_to' => ['required', Rule::in(['all_users', 'specific_users', 'specific_stores'])],
            'user_ids' => ['nullable', 'array', 'required_if:send_to,specific_users'],
            'user_ids.*' => ['exists:users,id'],
            'store_ids' => ['nullable', 'array', 'required_if:send_to,specific_stores'],
        ]);

        $notification = Notification::with('details')->find($request->id);

        if (! $notification) {
            abort(400, 'Notification not found.');
        }

        $allLanguageVersions = [];

        foreach ($notification->details as $detail) {
            $lang = $detail->language_type;
            $allLanguageVersions[$lang] = [
                'title' => $detail->title ?? '',
                'content' => $detail->content ?? '',
            ];
        }
        if (!isset($allLanguageVersions['en']) && count($allLanguageVersions) > 0) {
            $firstLang = array_key_first($allLanguageVersions);
            $allLanguageVersions['en'] = $allLanguageVersions[$firstLang];
        }

        $users = User::with(['device' => function ($query) {
            $query->whereNotNull('token')
                ->active();
        }])
            ->user()
            ->active()
            ->when($request->send_to === 'specific_users', function ($query) use ($request) {
                $query->whereIn('id', $request->user_ids);
            })
            ->when($request->send_to === 'specific_stores', function ($query) use ($request) {
                $query->whereIn('store_id', $request->store_ids);
            })
            ->get();

        $userNotificationData = [];
        $notificationsToSend = [];

        foreach ($users as $user) {
            $userLang = $user->lang ?? 'en';

            $userNotificationData[] = [
                'user_id' => $user->id,
                'notice_type' => 25,
                'notification_id' => $notification->id,
                'reference_data' => json_encode($allLanguageVersions),
                'created_at' => now(),
                'updated_at' => now(),
            ];

            if ($user?->device) {
                $params = FirebaseTrait::template('custom', $userLang, $notification);

                $notificationsToSend[] = [
                    'token' => $user->device->token,
                    'params' => $params,
                    'data' => null,
                    'userId' => $user->id,
                    'lang' => $userLang,
                    'notificationId' => $notification->id,
                    'referenceData' => [
                        'title' => $params['title'] ?? '',
                        'content' => $params['body'] ?? '',
                    ],
                    'skipLog' => true
                ];
            }
        }

        if ($userNotificationData) {
            DB::table('user_notification')->insert($userNotificationData);
        }

        if ($notificationsToSend) {
            FirebaseTrait::sendMulticastNotification($notificationsToSend, true);
        }

        abort(200, json_encode([
            'data' => [],
        ]));
    }
}
