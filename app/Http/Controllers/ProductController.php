<?php

namespace App\Http\Controllers;

use App\Models;
use App\Models\AccountBalance;
use App\Models\Credit;
use App\Models\ExTransfer;
use App\Models\GameLogin;
use App\Models\Product;
use App\Models\User;
use App\Models\UserProduct;
use App\Models\UserPromotion;
use App\Services\GameProvider\CQ9;
use App\Services\GameProvider\GSC;
use App\Services\GameProvider\JILI;
use App\Services\GameProvider\JK;
use App\Services\GameProvider\KISS;
use App\Services\GameProvider\MEGA;
use App\Services\GameProvider\MT;
use App\Services\GameProvider\PUSSY;
use App\Traits;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ProductController extends Controller
{
    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = product/get-product-balance
     *
     * @module = user,app
     *
     * @path = product/get-product-balance
     *
     * @method = POST
     *
     * @description = To login to third party.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     *
     * @body = product_id|<product_id>|integer|required|2000|Product ID.
     * @body = wallet_type|<wallet_type>|string|optional|main_wallet|wallet_type. Required if available (from dashboard api)
     *
     * @response = {"data":{"wallet_currency":"MYR","main_wallet":"0.00","game_wallet":"100.00","last_played_game":"JILI"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS9wcm9kdWN0L2dldC1wcm9kdWN0LWJhbGFuY2UiLCJpYXQiOjE2ODcyMjcyOTMsImV4cCI6MTY4NzIzMDk2MiwibmJmIjoxNjg3MjI3MzYyLCJqdGkiOiJqbmkxMTNPZXlwSkI5T1I5Iiwic3ViIjoiMTAwMDA1MyIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ymvGgkJsHrk9WNDkYo_32H3n1moBBX3X9nQVttko_HU","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.11535096168518 sec","log_id":"3e2cc214-5424-48c5-b4d1-f5fe9603c1e7"}
     * ##docs end##
     */
    public function getProductBalance(Request $request)
    {
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => Auth::user()->id]);
        }
        $validator = Validator::make($request->all(), [
            'user_id' => [
                'required',
                'integer',
                Rule::exists('users', 'id')->whereIN('user_type', [Models\User::$userType['user-account']]),
            ],
            'product_id' => [
                'required',
                'integer',
                function ($q, $value, $fail) use ($request, &$hasWallet, &$walletList) {
                    $product = Models\Product::with([
                        'productSetting' => function ($q) {
                            $q->where('name', 'hasWalletList');
                            $q->where('value', '1');
                        },
                    ])
                        ->where('id', $value)->where('status', Models\Product::$status['active'])->first() ?? null;

                    $productID = $product->id;
                    if (empty($productID)) {
                        abort(400, json_encode(['Invalid Product']));
                    }

                    $hasWallet = false;
                    $walletList = [];
                    if (! empty($product->productSetting)) {
                        if (! empty($product->productSetting->first()->type)) {
                            $hasWallet = true;
                            $walletList = explode(',', $product->productSetting->first()->type);
                        }
                    }

                    if (isset($request->user_id)) {
                        $userProductID = Models\UserProduct::where('user_id', $request->user_id)->where('product_id', $value)->first()->id ?? null;
                        if (empty($userProductID)) {
                            abort(400, json_encode(['Invalid User Product']));
                        }
                    }
                },
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $validator2 = Validator::make($request->all(), [
            'wallet_type' => [
                Rule::requiredIf($hasWallet),
                Rule::prohibitedIf(! $hasWallet),
                'in:'.implode(',', $walletList),
            ],
        ]);

        if ($validator2->fails()) {
            abort(400, json_encode($validator2->errors()));
        }

        $data = Models\UserProduct::getProductBalance(array_merge($validator->validated(), ['wallet_type' => $request->wallet_type]));

        abort(200, json_encode(['data' => $data]));
    }

    public function getGameList(Request $request)
    {
        $prodBalRes = resolve(GSC::class)->getGameList('');

        // dd(array_unique(array_column($prodBalRes['data'], 'gametype')));
        // $key = array_search('Arcade', array_column($prodBalRes['data'], 'gameCategoryName'));
        // $result = $prodBalRes['data'][$key];

        return $prodBalRes;
    }

    /* This is needed to generate API docs
        * ##docs start##
        * @postmanName = External Transfer (Deposit & Withdraw)
        * @module = user,app
        * @path = product/add-ex-transfer
        * @method = post
        * @description = To transfer credit other platform.

        * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
        * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
        * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

        * @body = step|<step>|int|required|1,2|Step 1 = Validate Input Data, Step 2 = Confirmation.
        * @body = credit_id|<credit_id>|int|optional|1000|Credit Unique Id for credit want to tranfer (Step 1 required).
        * @body = product_id|<product_id>|int|required|1000|Product Unique Id for credit want to tranfer (Step 1 required).
        * @body = type|<type>|int|required|1,2|In or Out (Step 1 required)(Get From Dropdown : ex_transfer_type).
        * @body = amount|<amount>|int|required|100|Credit Amount want to transfer (Step 1 required).
        * @body = wallet_type|<wallet_type>|string|optional|main_wallet|wallet_type. Required if available (from dashboard api)

        * @response = {"data":{"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS9wcm9kdWN0L2FkZC1leC10cmFuc2ZlciIsImlhdCI6MTY4NzMxMDgxNiwiZXhwIjoxNjg3MzE1Mjg1LCJuYmYiOjE2ODczMTE2ODUsImp0aSI6Imc0UHZ4RHI1WlNJMDZVTTgiLCJzdWIiOiIxMDAwMDUzIiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.6Oz270H0L4-qROjK4m_jHaef-1aYjO1NE0p94gQLfuI","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.14557218551636 sec","log_id":"1bbc1d44-59db-4a29-9b23-61f70aaf766c"}
        * ##docs end##*/
    public function addExTransfer(Request $request)
    {
        $walletData = [];
        $productData = [];
        $userDetail = [];
        $exMemberId = null;
        $hasReturn = true;

        if (isset($request->has_return)) {
            $hasReturn = $request->has_return;
        }

        if (in_array(MODULE, ['user', 'app'])) {
            $user_id = auth()->user()->id ?? null;
            $request->request->add(['user_id' => $user_id]);
            $validateCol = 'member';
        } else {
            $validateCol = 'admin';
        }

        // Special Handle for Page without Credit ID
        if (! isset($request->credit_id)) {
            $creditFunction = Models\CreditSetting::where([
                'name' => 'is-ex-transferable',
                'value' => '1',
                $validateCol => '1',
            ])->first();

            if (empty($creditFunction)) {
                abort(400, json_encode(Lang::get('lang.function-unavailable')));
            }

            $request->request->add(['credit_id' => ($creditFunction->credit_id ?? 0)]);
        }

        if (isset($request->amount)) {
            $request->request->add(['amount' => (string) floatval(str_replace(',', '', $request->amount))]);
        }

        $validator = Validator::make($request->all() + ['module' => MODULE], [
            'step' => 'integer|required|in:1,2',
            'user_id' => [
                'required',
                'integer',
                function ($q, $value, $fail) use (&$userDetail) {
                    $checkUser = Models\User::with(['userDetail'])->where('id', $value)->whereIN('user_type', [Models\User::$userType['user-account']])->get()->map(function ($q) use (&$userDetail) {
                        foreach ($q->userDetail->keyBy('name') as $name => $value) {
                            $userDetail[$name] = $value->value;
                        }

                        return $q;
                    })->first();
                    if (empty($checkUser)) {
                        $fail(Lang::get('validation.exists'));

                        return;
                    }
                },
            ],
            'credit_id' => [
                'required',
                'integer',
                'exists:credit,id',
                function ($q, $value, $fail) use ($request, $validateCol, &$walletData, &$isExTransferable, &$transferableProduct, &$exTransferableMinAmt, &$exTransferableMultiplier) {
                    $isExTransferable = false;
                    $transferableProduct = [];
                    $exTransferableMinAmt = null;
                    $exTransferableMultiplier = null;

                    $credit = Models\Credit::with([
                        'creditSetting' => function ($q) use ($validateCol) {
                            $q->whereIn('name', ['is-ex-transferable', 'ex-transfer-min-amt', 'ex-transfer-multiplier']);
                            $q->where($validateCol, '1');
                        },
                    ])
                        ->where('id', $request->credit_id ?? 0)
                        ->get();
                    $credit->map(function ($q) use (&$fail, &$isExTransferable, &$transferableProduct, &$exTransferableMinAmt, &$exTransferableMultiplier) {
                        $q->creditSetting->map(function ($credit_setting) use (&$fail, &$isExTransferable, &$transferableProduct, &$exTransferableMinAmt, &$exTransferableMultiplier) {
                            switch ($credit_setting->name) {
                                case 'is-ex-transferable':
                                    if ($credit_setting->value == 1) {
                                        $transferableProduct = json_decode($credit_setting->type, true);
                                        $isExTransferable = true;
                                    }
                                    break;

                                case 'ex-transfer-min-amt':
                                    $exTransferableMinAmt = $credit_setting->value;
                                    break;

                                case 'ex-transfer-multiplier':
                                    $exTransferableMultiplier = $credit_setting->value;
                                    break;
                            }
                        });
                    });
                    if (! $isExTransferable) {
                        $fail(Lang::get('lang.function-unavailable'));

                        return;
                    }

                    $credit = $credit->first();
                    $creditType = $credit->type ?? null;

                    $walletAmt = Models\Credit::getBalance($request->user_id, $creditType);

                    $walletData = [
                        'credit_id' => $value,
                        'credit_name' => $credit->name,
                        'credit_type' => $credit->type,
                        'credit_display' => Lang::has('lang.'.$credit->name) ? Lang::get('lang.'.$credit->name) : $credit->name,
                        'balance' => $walletAmt,
                    ];
                },
            ],
            'product_id' => [
                Rule::requiredIf(($request->step >= 1)),
                'integer',
                function ($q, $value, $fail) use ($request, &$productData, &$exMemberId, &$transferableProduct, &$hasWallet, &$walletList) {
                    if (empty($transferableProduct) || ! in_array($value, $transferableProduct)) {
                        $fail(Lang::get('lang.function-unavailable'));

                        return;
                    }

                    $productData = Models\Product::with([
                        'productSetting' => function ($q) {
                            $q->where('name', 'hasWalletList');
                            $q->where('value', '1');
                        },
                    ])
                        ->where('id', $value)->get()->map(function ($q) use ($request, &$exMemberId, &$fail) {
                            $exMemberId = Models\UserProduct::where('user_id', $request->user_id)->where('product_id', $q->id)->first()->member_id ?? null;
                            if (empty($exMemberId)) {
                                $fail(Lang::get('validation.exists'));

                                return;
                            }

                            switch ($q->name) {
                                case 'TK8':
                                    $prodBalRes = Traits\OCTK8Trait::postOC(['account' => $exMemberId], 'check_balance');
                                    break;
                                case 'MT':
                                    $prodBalRes = resolve(MT::class)->checkBalance($exMemberId);
                                    break;
                            }

                            $mainWallet = 0;
                            $gameWallet = 0;
                            $lotteryWallet = 0;
                            if (isset($prodBalRes['data']) && ! empty($prodBalRes['data'])) {
                                $mainWallet = isset($prodBalRes['data']['main_wallet']) && is_numeric($prodBalRes['data']['main_wallet']) ? $prodBalRes['data']['main_wallet'] : 0;
                                $gameWallet = isset($prodBalRes['data']['game_wallet']) && is_numeric($prodBalRes['data']['game_wallet']) ? $prodBalRes['data']['game_wallet'] : 0;
                                $lotteryWallet = isset($prodBalRes['data']['lottery_wallet']) && is_numeric($prodBalRes['data']['lottery_wallet']) ? $prodBalRes['data']['lottery_wallet'] : 0;
                            }
                            $prodBal = $mainWallet + $gameWallet;
                            $prodLotteryBal = $lotteryWallet;

                            if (isset($prodBalRes)) {
                                AccountBalance::updateBalance($request->user_id, $prodBal);
                            }

                            return [
                                'id' => $q->id,
                                'name' => $q->name,
                                'display' => Lang::has('lang.'.$q->name) ? Lang::get('lang.'.$q->name) : $q->name,
                                'image' => env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN').$q->image_url,
                                'balance' => $prodBal,
                                'lottery_balance' => $lotteryWallet,
                                'productSetting' => $q->productSetting->toArray() ?? null,
                            ];
                        })->first();

                    if (empty($productData)) {
                        $fail(Lang::get('validation.exists'));

                        return;
                    }

                    $hasWallet = false;
                    $walletList = [];
                    if (! empty($productData['productSetting'])) {
                        if (! empty($productData['productSetting'][0]['type'])) {
                            $hasWallet = true;
                            $walletList = explode(',', $productData['productSetting'][0]['type']);
                        }
                    }
                },
            ],
            'type' => [
                Rule::requiredIf(($request->step >= 1)),
                'integer',
                'in:'.implode(',', Models\ExTransfer::$type),
            ],
            'amount' => [
                // Rule::requiredIf(($request->step >= 1) && ($request->type == Models\ExTransfer::$type['in'])),
                'nullable',
                'string',
                'gt:0',
                'regex:/^(\\d+\\.?\\d{0,2})$/',
                function ($q, &$value, $fail) use ($request, &$walletData, &$productData, &$exTransferableMinAmt, &$exTransferableMultiplier) {
                    $balance = $walletData['balance'] ?? 0;
                    $prodBalance = $productData['balance'] ?? 0;
                    $prodLotteryBal = $productData['lottery_balance'] ?? 0;
                    $compareBal = 0;
                    // if (isset($request->wallet_type) && $request->wallet_type == Models\UserProduct::$walletType['Lottery']) {
                    //     $compareBal = $prodLotteryBal;
                    // } else {
                    $compareBal = $prodBalance;
                    // }

                    $type = array_search($request->type, Models\ExTransfer::$type);

                    switch ($type) {
                        case 'in':
                            if (isset($exTransferableMinAmt)) {
                                if ($value < ($exTransferableMinAmt)) {
                                    $fail(Lang::get('validation.gte.numeric', ['value' => $exTransferableMinAmt]));

                                    return;
                                }
                            }

                            if (isset($exTransferableMultiplier)) {
                                $cmpValue = bcdiv($value, $exTransferableMultiplier);
                                if ($cmpValue != (int) ($cmpValue)) {
                                    $fail(Lang::get('lang.amount-not-in-multiplier'));

                                    return;
                                }
                            }

                            if ($balance < ($value)) {
                                $fail(Lang::get('lang.credit-insufficient-balance'));

                                return;
                            }
                            break;

                        case 'out':
                            if ($compareBal <= 0) {
                                $fail('Product balance insufficient.');

                                return;
                            }
                            break;
                    }
                },
            ],
            // "pin" => [
            //     Rule::requiredIf(function () use ($request){
            //         return (in_array(MODULE,['user', 'app']) && ($request->step == 4));
            //     }),
            //     function ($q, $value, $fail) use (&$request, &$userDetail) {
            //         if(isset($userDetail['transaction_password']) && Hash::check($value, $userDetail['transaction_password']) != true){
            //             $fail(Lang::get("lang.invalid-pin"));
            //             return;
            //         }
            //     }
            // ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $validator2 = Validator::make($request->all(), [
            'wallet_type' => [
                Rule::requiredIf($hasWallet),
                Rule::prohibitedIf(! $hasWallet),
                'in:'.implode(',', $walletList),
            ],
        ]);

        if ($validator2->fails()) {
            abort(400, json_encode($validator2->errors()));
        }

        if ($validator->validated()['step'] == 2) {
            $exists = Models\ApiStatus::where('user_id', $request->user_id)->where('api_path', $request->path())->where('status', Models\ApiStatus::$status['pending'])->first();
            if ($exists) {
                abort(400, Lang::get('lang.duplicate-api-detected'));
            } else {
                $apiCreated = Models\ApiStatus::create([
                    'user_id' => $request->user_id,
                    'api_path' => $request->path(),
                    'status' => Models\ApiStatus::$status['pending'],
                ]);
            }

            try {
                if ($request->type == Models\ExTransfer::$type['in']) {
                    // NOTES: add balance amount for this user
                    $amount = Credit::getBalance($request->user_id, Credit::first()->type) ?? $request->amount;
                    $params = array_merge($validator->validated(), ['amount' => $amount]);

                    // Access Game Clear Move All Amount to Game
                    AccountBalance::updateBalance($request->user_id, 0);
                } else {
                    $params = $validator->validated();
                }

                $res = Models\ExTransfer::addI8($params + ['product_data' => $productData, 'wallet_data' => $walletData, 'exMemberId' => $exMemberId, 'wallet_type' => $request->wallet_type]);
            } catch (\Throwable $e) {
                Models\ApiStatus::find($apiCreated->id)->update(['status' => Models\ApiStatus::$status['failed']]);
                throw ($e);
            }
            Models\ApiStatus::find($apiCreated->id)->update(['status' => Models\ApiStatus::$status['success']]);
        }

        if ($hasReturn) {
            abort(200, (isset($res) ? json_encode(['data' => $res]) : ''));
        }
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = External Transfer List
     * @module = admin
     * @path = product/ex-transfer-list
     * @method = post
     * @description = To external transfer list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = username|<username>|string|optional|test_user|Username.
     * @body = product_id|<product_id>|integer|optional|1|product's id (Get from dropdown : product).
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = credit_id|<credit_id>|integer|optional|1000|Credit's Id.
     * @body = transaction_id|<transaction_id>|string|optional|GFX12312312|Transaction's Id.
     * @body = transfer_type|<transfer_type>|interger|optional|1|Transfer's Type. (Get from dropdown : ex_transfer_type)
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = export_data|<export_data>|array|option|{"data": {"Data KEY": "Column Display"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|goldmine-bonus|name of the export file. Required for export

     * @response = {"data":{"list":[{"ex_transfer_id":179,"created_at":"15\/07\/2024 15:30:44","transaction_id":"TK488893","reference":null,"username":"60122000051","product_name":"TK8","product_name_display":"TK8","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"13.00","updated_at":"15\/07\/2024 15:30:44","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"wallet_type":"lottery_wallet","wallet_type_display":"lottery_wallet"}],"pagination":{"current_page":1,"from":1,"last_page":147,"per_page":1,"to":1,"total":147},"meta":null,"grand_total":{"grand_amount":"13.00"},"summary_data":{"transfer_amount":"27743137.76"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL3Byb2R1Y3QvZXgtdHJhbnNmZXItbGlzdCIsImlhdCI6MTcyMTAyODUxNiwiZXhwIjoxNzIxMDMyMzkzLCJuYmYiOjE3MjEwMjg3OTMsImp0aSI6IjhlQUkyY1JVR2RHQ0M0WHEiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.ctZ_z9B0OaYaazm_D0iqi-n9Lg-U2LHepL2DJz8ZY2s","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.05034613609314 sec","log_id":"ba4d2b49-583e-4f66-989c-0dc72c2d5bd3"}
     * ##docs end##
     */

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = External Transfer List
     * @module = user,app
     * @path = product/ex-transfer-list
     * @method = post
     * @description = To get currency rate history.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = product_id|<product_id>|integer|optional|1|product's id (Get from dropdown : product).
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = transaction_id|<transaction_id>|string|optional|GFX12312312|Transaction's Id.
     * @body = transfer_type|<transfer_type>|interger|optional|1|Transfer's Type. (Get from dropdown : ex_transfer_type)

     * @response = {"data":{"list":[{"created_at":"15\/07\/2024 15:30:44","transaction_id":"TK488893","product_name":"TK8","product_name_display":"TK8","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"13.00","wallet_type":"lottery_wallet","wallet_type_display":"lottery_wallet"},{"created_at":"15\/07\/2024 15:30:23","transaction_id":"TK868446","product_name":"TK8","product_name_display":"TK8","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"13.00","wallet_type":null,"wallet_type_display":null},{"created_at":"10\/07\/2024 11:32:37","transaction_id":"TK120508","product_name":"TK8","product_name_display":"TK8","type":"out","type_display":"out","status":"confirmed","status_display":"Confirmed","amount":"100.00","wallet_type":"main_wallet","wallet_type_display":"main_wallet"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":3,"total":3},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS9wcm9kdWN0L2V4LXRyYW5zZmVyLWxpc3QiLCJpYXQiOjE3MjEwMjk0NjMsImV4cCI6MTcyMTAzMzA4NiwibmJmIjoxNzIxMDI5NDg2LCJqdGkiOiJTZkNHbDBvbjhYb3BrWjQwIiwic3ViIjoiMTAwMDM5NSIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.9ovpMjmV8Zc51pvGG_BHht5h0vLswaEiWPhiDhpQtUs","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.062613964080811 sec","log_id":"6492c115-d1a6-4479-83f1-52640bf31f1f","call_cur_list_flag":false}
     * ##docs end##
     */

    public function getExTransferList(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $user_id = auth()->user()->id ?? null;
            $request->request->add(['user_id' => $user_id]);
        }
        $validator = Validator::make($request->all(), [
            'order_sort' => 'string|in:asc,desc',
            'user_id' => 'integer',
            'username' => 'string',
            'product_id' => 'integer',
            'credit_id' => 'integer',
            'transaction_id' => 'string',
            'transfer_type' => 'integer',
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d|after_or_equal:from_date',
            'limit' => 'int',
            'see_all' => 'integer|in:1,0',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string',
            'file_name' => 'required_if:export,==,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $list = Models\ExTransfer::getExTransferList($request->all());
        $data['data'] = $list;
        abort(200, json_encode($data));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = User Product List
     * @module = user,app
     * @path = product/get-user-tk8-product
     * @method = post
     * @description = To get user product data.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @response = {"data":{"product":{"id":188,"product_id":2002,"member_id":"0123456789","balance":"150.00000000","updated_at":"2024-02-20T03:21:41.000000Z","last_update":"20\/02\/2024 11:21:41","wallet_currency":"THB","product":{"id":2002,"name":"TK8TH","image_url":"TK8TH.png","product_setting":{"display_deposit":{"value":"1","type":null,"reference":null},"display_top_up":{"value":"1","type":null,"reference":null},"display_withdraw":{"value":"1","type":null,"reference":null}},"display_name":"TK8TH"}},"credit":{"id":1002,"name":"thb-credit","display":"thb-credit","is-ex-transferable":{"in":1,"out":1},"balance":"77.00000000","from_currency_iso":"THB","to_currency_iso":"MYR","from_rate":"1.00","to_rate":"0.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS9wcm9kdWN0L2dldC11c2VyLXByb2R1Y3QiLCJpYXQiOjE3MDg1NzMyNjUsImV4cCI6MTcwODU3Njg3NiwibmJmIjoxNzA4NTczMjc2LCJqdGkiOiJZVDdVbXphdlVvbjF4YzJMIiwic3ViIjoiMTAwMDAwMSIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.3fn4DH70avCMsPKHhAk2uI5Do8I4qMmhHKrCrKzUsrk","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.068090200424194 sec","log_id":"eb25d15e-58ba-4d6f-9223-4e26f2ad0300"}
     * ##docs end##
     */

    public function getUserProduct(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $user_id = auth()->user()->id ?? null;
            $request->request->add(['user_id' => $user_id]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer',
            'product_id' => [
                'required',
                'integer',
                function ($q, $value, $fail) use ($request) {
                    if (isset($request->user_id)) {
                        $userProductID = Models\UserProduct::where('user_id', $request->user_id)->where('product_id', $value)->first()->id ?? null;
                        if (empty($userProductID)) {
                            abort(400, json_encode(['Invalid User Product']));
                        }
                    }

                    $productID = Models\Product::where('id', $value)->where('status', Models\Product::$status['active'])->first()->id ?? null;
                    if (empty($productID)) {
                        abort(400, json_encode(['Invalid Product']));
                    }
                },
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $list = Models\UserProduct::getUserProductData($request->all());
        $data['data'] = $list;
        abort(200, json_encode($data));
    }

    public function restoreProductBalance(Request $request)
    {
        // abort(400, json_encode(['Invalid Request']));

        if (in_array(MODULE, ['user', 'app'])) {
            $user_id = auth()->user()->id ?? null;
            $request->request->add(['user_id' => $user_id]);
            $request->request->add(['is_manual_restore' => false]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer',
            'product_id' => 'nullable|integer',
            'is_manual_restore' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $transfer = ExTransfer::where('user_id', $request->user_id)
            ->orderBy('id', 'desc')->first();
        if ($request->is_manual_restore && $transfer->type == ExTransfer::$type['out'] && $transfer->machine_id != null) {
            $amount = AccountBalance::where('user_id', $user_id)->first()->balance ?? 0;
            $data['data'] = [
                'balance' => $amount,
            ];

            abort(200, json_encode($data));
        }

        $total_balance = 0;
        $prodBal = 0;

        Models\UserProduct::with('user', 'product')
            ->where('user_id', $user_id)
            ->where(function ($query) use ($request) {
                if (isset($request->product_id)) {
                    $query->where('product_id', $request->product_id);
                }
            })
            ->whereHas('product', function ($query) {
                $query->where('status', Models\Product::$status['active']);
            })
            // ->where('balance', '>', 0)
            ->get()
            ->each(function ($q) {
                Models\ExTransfer::transferOut([
                    'amount' => 0,
                    'credit_id' => 1000,
                    'product_name' => $q->product->name,
                    'product_id' => $q->product_id,
                    'user_id' => $q->user_id,
                    'credit_type' => Credit::first()->type,
                    'product_data' => $q->product,
                    'wallet_data' => [],
                    'exMemberId' => $q->member_id,
                    'wallet_type' => 'Main',
                ]);

                // abort(500, json_encode(['p' => $isPromotion, 'q' => $q->name, 'p' => $q->product, 'ap' => $activePromotion, 'cp' => $completedPromotion]));
                //
                // switch ($q->name) {
                //     case 'TK8':
                //         $prodBalRes = Traits\OCTK8Trait::postOC(['account' => $q->member_id], 'check_balance');
                //         break;
                //     case 'MT':
                //         $prodBalRes = resolve(MT::class)->checkBalance($q->user_id);
                //         break;
                //     case 'CQ9':
                //         $prodBalRes = resolve(CQ9::class)->checkBalance($q->user);
                //         break;
                //     case 'JK':
                //         $jk = resolve(JK::class);
                //         if (isset($promotion) && $promotion['data']['remaining_turnover'] > 0) {
                //             $prodBalRes = $jk->getWithdraw($q->member_id, $q->user->store->jk_agent_id, true);
                //         } else {
                //             $prodBalRes = $jk->getWithdraw($q->member_id, $q->user->store->jk_agent_id);
                //         }
                //         break;
                //     case 'JILI':
                //         $prodBalRes = resolve(JILI::class)->withdraw($q->member_id);
                //         break;
                // }
                //
                // $mainWallet = 0;
                // $gameWallet = 0;
                // $lotteryWallet = 0;
                // if (isset($prodBalRes['data']) && ! empty($prodBalRes['data'])) {
                //     $mainWallet = isset($prodBalRes['data']['main_wallet']) && is_numeric($prodBalRes['data']['main_wallet']) ? $prodBalRes['data']['main_wallet'] : 0;
                //     $gameWallet = isset($prodBalRes['data']['game_wallet']) && is_numeric($prodBalRes['data']['game_wallet']) ? $prodBalRes['data']['game_wallet'] : 0;
                //     $lotteryWallet = isset($prodBalRes['data']['lottery_wallet']) && is_numeric($prodBalRes['data']['lottery_wallet']) ? $prodBalRes['data']['lottery_wallet'] : 0;
                // }
                // $prodBal = $mainWallet + $gameWallet;
                // $prodLotteryBal = $lotteryWallet;
                //
                // if (isset($prodBalRes) && $completedPromotion && $isCompletedPromotion) {
                //     $oriProdBal = $prodBal;
                //     // $ccpi = $completedPromotion?->promotion->product_id;
                //     // $pn = Product::$id[$q->product->name];
                //     // $prodBal2 = $prodBal > $completedPromotion->max_withdraw_amount ? $completedPromotion->max_withdraw_amount : $prodBal;
                //     $prodBal = $oriProdBal > $completedPromotion->max_withdraw_amount ? $completedPromotion->max_withdraw_amount : $oriProdBal;
                //     $burnAmount = $oriProdBal > $completedPromotion->max_withdraw_amount ? $oriProdBal - $completedPromotion->max_withdraw_amount : 0;
                //     // abort(500, json_encode(['ccpi' => $ccpi, 'pn' => $pn, 'prodbal' => $prodBal, 'prodbal2' => $prodBal2, 'mwa' => $completedPromotion->max_withdraw_amount, 'ba' => $burnAmount]));
                //     UserPromotion::claimUserPromotionByUserId($user_id, $burnAmount);
                // }
                //
                // if (isset($prodBalRes)) {
                //     AccountBalance::updateBalance($q->user_id, $prodBal);
                // }

                // $isPromotion = ($activePromotion?->product_id ?? -1) == (Product::$id[$q->product->name] ?? -2);
                // $isCompletedPromotion = ($completedPromotion?->promotion_id ?? -1) == (Product::$id[$q->product->name] ?? -2);
                // $shouldWithdraw = ! ($activePromotion && $isPromotion);
                //
                // if ($shouldWithdraw) {
                //     switch ($q->product->name) {
                //         case 'TK8':
                //             $prodBalRes = Traits\OCTK8Trait::postOC(['account' => $q->member_id], 'check_balance');
                //             break;
                //         case 'MT':
                //             $prodBalRes = resolve(MT::class)->checkBalance($q->user_id);
                //             break;
                //         case 'CQ9':
                //             $prodBalRes = resolve(CQ9::class)->checkBalance($q->user);
                //             break;
                //         case 'JK':
                //             $jk = resolve(JK::class);
                //
                //             $promotion = $jk->checkPromotion($q->member_id, $q->user->store->jk_agent_id);
                //             if (isset($promotion) && $promotion['data']['remaining_turnover'] > 0) {
                //                 $prodBalRes = $jk->getWithdraw($q->member_id, $q->user->store->jk_agent_id, true);
                //             } else {
                //                 $prodBalRes = $jk->getWithdraw($q->member_id, $q->user->store->jk_agent_id);
                //             }
                //             break;
                //         case 'JILI':
                //             $prodBalRes = resolve(JILI::class)->withdraw($q->member_id);
                //             break;
                //     }
                // }
                //
                // $mainWallet = 0;
                // $gameWallet = 0;
                // $lotteryWallet = 0;
                //
                // if (isset($prodBalRes['data']) && ! empty($prodBalRes['data'])) {
                //     $mainWallet = isset($prodBalRes['data']['main_wallet']) && is_numeric($prodBalRes['data']['main_wallet']) ? $prodBalRes['data']['main_wallet'] : 0;
                //     $gameWallet = isset($prodBalRes['data']['game_wallet']) && is_numeric($prodBalRes['data']['game_wallet']) ? $prodBalRes['data']['game_wallet'] : 0;
                //     $lotteryWallet = isset($prodBalRes['data']['lottery_wallet']) && is_numeric($prodBalRes['data']['lottery_wallet']) ? $prodBalRes['data']['lottery_wallet'] : 0;
                // }
                //
                // $prodBal = $mainWallet + $gameWallet;
                //
                // // if ($completedPromotion && $isCompletedPromotion) {
                // //     $prodBal = $prodBal > $completedPromotion->max_withdraw_amount ? $completedPromotion->max_withdraw_amount : $prodBal;
                // //     $burnAmount = $prodBal > $completedPromotion->max_withdraw_amount ? $prodBal - $completedPromotion->max_withdraw_amount : 0;
                // //     UserPromotion::claimUserPromotionByUserId($user_id, $burnAmount);
                // // }
                //
                // $prodLotteryBal = $lotteryWallet;
                //
                // if (isset($prodBalRes)) {
                //     AccountBalance::updateBalance($q->user_id, $prodBal);
                // }

                // $params = array_merge($validator->validated(), [
                //     'amount' => $prodBal,
                // ]);
            });

        // $userProduct = Models\UserProduct::with('product', 'user')
        //     ->where('user_id', $user_id)
        //     ->where(function ($query) use ($request) {
        //         // $query->where('product_id', $request->product_id);
        //         $query->where('product_id', '2004');
        //         // if (isset($request->product_id)) {
        //         //     $query->where('product_id', $request->product_id);
        //         // }
        //     })
        //     ->get()
        //     ->first();
        // $params = array_merge($validator->validated(), [
        //     'amount' => $prodBal,
        //     'credit_id' => 1000,
        //     'product_name' => $userProduct->product->name,
        //     'product_id' => $userProduct->product_id,
        //     'user_id' => $userProduct->user_id,
        //     'credit_type' => Credit::first()->type,
        // ]);

        // Models\ExTransfer::transferOut($params + [
        //     'product_data' => $userProduct->product,
        //     'wallet_data' => [],
        //     'exMemberId' => $userProduct->member_id,
        //     'wallet_type' => 'Main',
        // ]);

        $amount = Credit::getBalance($user_id, Credit::first()->type);
        if ($amount > 0) {
            AccountBalance::updateBalance($user_id, $amount);
        } else {
            $amount = AccountBalance::where('user_id', $user_id)->first()->balance ?? 0;
        }

        if (isset($request->product_id)) {
            GameLogin::where([
                'user_id' => $request->user_id,
                'product_id' => $request->product_id,
            ])->delete();
        } else {
            GameLogin::where([
                'user_id' => $request->user_id,
            ])->delete();
        }

        $data['data'] = [
            'balance' => $amount,
        ];

        abort(200, json_encode($data));
    }

    public function getAllProductBalance(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $user_id = auth()->user()->id ?? null;
            $request->request->add(['user_id' => $user_id]);
        }

        $amount = Credit::getBalance($user_id, Credit::first()->type);

        if ($amount > 0) {
            AccountBalance::updateBalance($user_id, $amount);
        } else {
            $amount = AccountBalance::where('user_id', $user_id)->first()->balance ?? 0;
        }

        $data['data'] = [
            'balance' => $amount,
        ];

        abort(200, json_encode($data));
    }

    public function getEnterGame(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $user_id = auth()->user()->id ?? null;
            $request->request->add(['user_id' => $user_id]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer',
            'product_id' => [
                'required',
                'integer',
            ],
            'service_id' => [
                'nullable',
                'integer',
                Rule::exists('services', 'id')->where('status', true),
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        // WARNING: Custom Hardcode Place

        // 15 May 2025 3:30 AM until 4:30 AM Game Maintenance
        // if ($request->product_id == 2004) {
        //     $now = Carbon::now();
        //     $startTime = Carbon::parse('3:30')->format('H:i');
        //     $endTime = Carbon::parse('4:30')->format('H:i');
        //     if ($now->format('H:i') >= $startTime && $now->format('H:i') <= $endTime) {
        //         abort(400, json_encode(['Game Under Maintenance']));
        //     }
        // }

        if (in_array($user_id, [1002990, 1002696, 1002841, 1003450, 1002790])) {
            abort(400, json_encode(['Not Allowed Access to Game']));
        }

        if (in_array($user_id, [1000160, 1004122, 1004295])) {
            // check ex_transfer got duplicate record
            $duplicate = ExTransfer::where('user_id', $user_id)
                ->where('product_id', $request->product_id)
                ->where('type', ExTransfer::$type['in'])
                ->where('created_at', '>=', Carbon::now()->subMinutes(1))
                ->where('created_at', '<=', Carbon::now())
                ->first();
            if (isset($duplicate)) {
                abort(400, json_encode(['Please Wait the Game Is Processing']));
            }
        }

        $product = Models\Product::with([
            'productSetting' => function ($q) {
                $q->where('name', 'hasWalletList');
                $q->where('value', '1');
            },
        ])
            ->where('id', $request->product_id)
            ->where('status', Models\Product::$status['active'])
            ->first() ?? null;
        if (empty($product)) {
            abort(400, json_encode(['Invalid Product']));
        }

        $user = User::find($request->user_id);

        $userProduct = UserProduct::with(['product', 'user'])->where('user_id', $request->user_id)->where('product_id', $request->product_id)->first();
        if ($request->product_id == JILI::$productId && ! $userProduct) {
            resolve(JILI::class)->createMember($user);
        }

        if ($request->product_id == MT::$productId && ! $userProduct) {
            resolve(MT::class)->createMember($user);
        }

        if ($product->aggregator == GSC::$name) {
            $gsc = resolve(GSC::class);
            $currentProductId = UserProduct::with(['product', 'user'])->where('user_id', $request->user_id)->whereHas('product', function ($query) {
                $query->where('aggregator', 'GSC');
            })->first();

            $gsc->createMember($user, $currentProductId->product_id ?? -1, $product->id);
        }

        if (! $userProduct) {
            switch ($request->product_id) {
                case JK::$productId:
                    UserProduct::subscribeGame($request->user_id, $request->product_id, env('USER_ACCOUNT_PREFIX').$user->uuid);
                    break;

                case KISS::$productId:
                    resolve(KISS::class)->addUser($user->uuid);
                    UserProduct::subscribeGame($request->user_id, $request->product_id, $user->uuid);
                    break;

                case MEGA::$productId:
                    $account = resolve(MEGA::class)->createMemberAccount();
                    $loginId = $account['login_id'] ?? '';
                    UserProduct::subscribeGame($request->user_id, $request->product_id, $loginId);
                    break;

                case PUSSY::$productId:
                    resolve(PUSSY::class)->addUser($user->uuid);
                    UserProduct::subscribeGame($request->user_id, $request->product_id, $user->uuid);
                    break;
            }
        }

        // get-user-product
        // $userProduct = Models\UserProduct::getUserProductData($request->all());

        // get-product-balance
        // TODO: performance need optimise take 2.03s
        // $productBalance = Models\UserProduct::getProductBalance(array_merge($validator->validated(), ['wallet_type' => 'Main',]));

        // add-ex-transfer
        // TODO: performance need optimise take 1.49s
        // $this->addExTransfer(new Request([
        //     'user_id' => $request->user_id,
        //     'step' => 2,
        //     'credit_id' => 1000,
        //     'product_id' => $request->product_id,
        //     'type' => 2,
        //     'wallet_type' => 'Main',
        //     'has_return' => false,
        // ]));

        // NOTES: add balance amount for this user
        $amount = Credit::getBalance($request->user_id, Credit::first()->type) ?? $request->amount;
        $params = array_merge($validator->validated(), [
            'amount' => $amount,
            'credit_id' => 1000,
            'product_name' => $product->name,
            'product_id' => $request->product_id,
            'user_id' => $request->user_id,
            'credit_type' => Credit::first()->type,
        ]);

        if ($amount < 0) {
            abort(400, json_encode(['Insufficient Balance']));
        }

        $loginCount = Models\GameLogin::where([
            'user_id' => $request->user_id,
            'product_id' => $request->product_id,
        ])->count();

        if ($loginCount > 0) {
            $data = Models\UserProduct::productAutoLogin([
                'user_id' => $request->user_id,
                'product_id' => $request->product_id,
                'service_id' => $request->service_id,
                'balance' => 0,
            ]);

            abort(200, json_encode(['data' => $data]));
        }

        // Access Game Clear Move All Amount to Game
        AccountBalance::updateBalance($request->user_id, 0);

        $exMemberId = Models\UserProduct::where('user_id', $request->user_id)->where('product_id', $request->product_id)->first()->member_id ?? null;
        if (empty($exMemberId)) {
            abort(400, json_encode(['Invalid User Product']));

            return;
        }

        $userPromotion = UserPromotion::checkUserActivePromotion();

        // if (! isset($userPromotion) || $userPromotion['product_id'] != $request->product_id) {
        if (! isset($userPromotion)) {
            if ($params['amount'] > 0) {

                // $ex_transfer = ExTransfer::where('user_id', $request->user_id)
                //     ->where('product_id',  $request->product_id)
                //     ->where('created_at', '>=', now()->subSeconds(10))
                //     ->where('created_at', '<=', now())
                //     ->first();
                // if (!isset($ex_transfer)) {
                // }

                $res = Models\ExTransfer::transferIn($params + [
                    'product_data' => $product,
                    'wallet_data' => [],
                    'exMemberId' => $exMemberId,
                    'wallet_type' => 'Main',
                    'is_jk_enter_game' => $request->product_id == Product::$id['JK'],
                ]);
                if (! $res) {
                    abort(400, json_encode(['Transfer Failed']));
                }
            }
        }

        // if ($request->product_id != 2004) {
        //     if ($params['amount'] > 0) {
        //         $res = Models\ExTransfer::transferIn($params + [
        //             'product_data' => $product,
        //             'wallet_data' => [],
        //             'exMemberId' => $exMemberId,
        //             'wallet_type' => 'Main',
        //         ]);
        //         if (! $res) {
        //             abort(400, json_encode(['Transfer Failed']));
        //         }
        //     }
        // }

        // Third Party Login
        // TODO: performance need optimise take 1.89s
        $data = Models\UserProduct::productAutoLogin([
            'user_id' => $request->user_id,
            'product_id' => $request->product_id,
            'service_id' => $request->service_id,
            'balance' => $amount,
        ]);

        Models\GameLogin::create([
            'user_id' => $request->user_id,
            'product_id' => $request->product_id,
        ]);

        abort(200, json_encode(['data' => $data]));
    }

    public function getTransferIn(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $user_id = auth()->user()->id ?? null;
            $request->request->add(['user_id' => $user_id]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer',
            'product_id' => [
                'required',
                'integer',
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $product = Models\Product::with([
            'productSetting' => function ($q) {
                $q->where('name', 'hasWalletList');
                $q->where('value', '1');
            },
        ])
            ->where('id', $request->product_id)
            ->where('status', Models\Product::$status['active'])
            ->first() ?? null;
        if (empty($product)) {
            abort(400, json_encode(['Invalid Product']));
        }

        $amount = Credit::getBalance($request->user_id, Credit::first()->type) ?? $request->amount;
        $params = array_merge($validator->validated(), [
            'amount' => $amount,
            'credit_id' => 1000,
            'product_name' => $product->name,
            'product_id' => $request->product_id,
            'user_id' => $request->user_id,
            'credit_type' => Credit::first()->type,
        ]);
        // Access Game Clear Move All Amount to Game
        AccountBalance::updateBalance($request->user_id, $amount);

        $exMemberId = Models\UserProduct::where('user_id', $request->user_id)->where('product_id', $request->product_id)->first()->member_id ?? null;
        if (empty($exMemberId)) {
            abort(400, json_encode(['Invalid User Product']));

            return;
        }

        if ($params['amount'] <= 0) {
            abort(200, json_encode(['data' => null]));

            return;
        }

        $res = Models\ExTransfer::transferIn($params + [
            'product_data' => [],
            'wallet_data' => [],
            'exMemberId' => $exMemberId,
            'wallet_type' => 'Main',
        ]);

        abort(200, json_encode(['data' => $res]));
    }

    public function restoreProductBalanceCredit(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $user_id = auth()->user()->id ?? null;
            $request->request->add(['user_id' => $user_id]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer',
            'product_id' => 'nullable|integer',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $total_balance = 0;
        Models\UserProduct::with('product', 'user')
            ->where('user_id', $user_id)
            ->where(function ($query) use ($request) {
                if (isset($request->product_id)) {
                    $query->where('product_id', $request->product_id);
                }
            })
            ->get()
            ->map(function ($q) use ($validator) {
                switch ($q->name) {
                    case 'TK8':
                        $prodBalRes = Traits\OCTK8Trait::postOC(['account' => $q->member_id], 'check_balance');
                        break;
                    case 'MT':
                        $prodBalRes = resolve(MT::class)->checkBalance($q->member_id);
                        break;
                    case 'CQ9':
                        $prodBalRes = resolve(CQ9::class)->checkBalance($q->user);
                        break;
                    case 'JK':
                        $prodBalRes = resolve(JK::class)->getWithdraw($q->member_id, $q->user->store->jk_agent_id);
                        break;
                    case 'JILI':
                        $prodBalRes = resolve(JILI::class)->withdraw($q->member_id);
                        break;
                }

                $mainWallet = 0;
                $gameWallet = 0;
                $lotteryWallet = 0;
                if (isset($prodBalRes['data']) && ! empty($prodBalRes['data'])) {
                    $mainWallet = isset($prodBalRes['data']['main_wallet']) && is_numeric($prodBalRes['data']['main_wallet']) ? $prodBalRes['data']['main_wallet'] : 0;
                    $gameWallet = isset($prodBalRes['data']['game_wallet']) && is_numeric($prodBalRes['data']['game_wallet']) ? $prodBalRes['data']['game_wallet'] : 0;
                    $lotteryWallet = isset($prodBalRes['data']['lottery_wallet']) && is_numeric($prodBalRes['data']['lottery_wallet']) ? $prodBalRes['data']['lottery_wallet'] : 0;
                }
                $prodBal = $mainWallet + $gameWallet;
                $prodLotteryBal = $lotteryWallet;

                if (isset($prodBalRes)) {
                    AccountBalance::updateBalance($q->user_id, $prodBal);
                }

                $params = array_merge($validator->validated(), [
                    'amount' => $prodBal,
                    'credit_id' => 1000,
                    'product_name' => $q->product->name,
                    'product_id' => $q->product_id,
                    'user_id' => $q->user_id,
                    'credit_type' => Credit::first()->type,
                ]);

                Models\ExTransfer::transferOut($params + [
                    'product_data' => $q->product,
                    'wallet_data' => [],
                    'exMemberId' => $q->member_id,
                    'wallet_type' => 'Main',
                ]);
            });

        $amount = Credit::getBalance($user_id, Credit::first()->type);
        if ($amount > 0) {
            AccountBalance::updateBalance($user_id, $amount);
        } else {
            $amount = AccountBalance::where('user_id', $user_id)->first()->balance ?? 0;
        }

        $data['data'] = [
            'balance' => $amount,
        ];

        abort(200, json_encode($data));
    }
}
