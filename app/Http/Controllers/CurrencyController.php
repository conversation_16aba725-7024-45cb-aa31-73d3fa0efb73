<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Convert;
use App\Models\Currency;
use App\Models\CurrencyRate;
use App\Models\CurrencyRateHourly;
use App\Models\CurrencySetting;
use App\Models\CurrencySettingHistory;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;
use DB;
use Illuminate\Support\Facades\Lang;

class CurrencyController extends Controller
{

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Currency Preset
     * @module = admin
     * @path = currency/preset
     * @method = post
     * @description = To get currency data.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = iso|<iso>|string|optional|albania|Country name (dropdown:currency_filter)
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @response = {"data":{"list":[{"iso":"VND","country":"vietnam","country_display":"Vietnam","rate":"5246.62","sell_rate":"5246.62","fee":"0","min_amount":"0","instant_rate":"5246.62","instant_disabled":0,"normal_rate":"5246.62","normal_disabled":0,"daily_rate":"5246.62","daily_limit":"0.00","status_id":0,"status_name":"active","status_display":"Active","updated_at":"28\/04\/2023 18:10:03","updated_by":"System"},{"iso":"VND","country":"vietnam","country_display":"Vietnam","rate":"2.00","sell_rate":"2.00","fee":"0","min_amount":"0","instant_rate":"2.00","instant_disabled":0,"normal_rate":"2.00","normal_disabled":0,"daily_rate":"2.00","daily_limit":"0.00","status_id":0,"status_name":"active","status_display":"Active","updated_at":false,"updated_by":"System"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":2,"total":2},"meta":null},"status":true,"dropdown":{"currency_filter":[{"id":"USD","name":"USD","display":"USD"},{"id":"VND","name":"VND","display":"VND"},{"id":"IDR","name":"IDR","display":"IDR"},{"id":"SGD","name":"SGD","display":"SGD"},{"id":"INR","name":"INR","display":"INR"},{"id":"BDT","name":"BDT","display":"BDT"},{"id":"PHP","name":"PHP","display":"PHP"},{"id":"PKR","name":"PKR","display":"PKR"},{"id":"KHR","name":"KHR","display":"KHR"}]},"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2N1cnJlbmN5L3ByZXNldCIsImlhdCI6MTY4MzA4Mzg4OSwiZXhwIjoxNjgzMDg4Njc2LCJuYmYiOjE2ODMwODUwNzYsImp0aSI6Ik5WU0hBc3BucXA4RDRQbmYiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.Y-DWeIeKZmDfb08jety438dKgy41SPl7RnFhe0XXi9c","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.14773201942444 sec","log_id":"8fcbf6a6-606f-4a2d-9fa8-58bbe22a6df0"}
     * ##docs end##
     */

    public function preset(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "iso" => 'string|exists:currency,iso',
            "order_sort" => "string|in:asc,desc",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }
        $list = CurrencyRate::preset($validator->validated());
        $data["data"] = $list;
        abort(200, json_encode($data));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Withdrawal Currency Preset
     * @module = admin
     * @path = currency/withdrawal-preset
     * @method = post
     * @description = To get withdrawal currency data.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = iso|<iso>|string|optional|albania|Country name (dropdown:currency_filter)
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @response = {"data":{"list":[{"iso":"PHP","country":"philippines","country_display":"Philippines","rate":"12.46","withdrawal_rate":"12.46","withdrawal_fee":"6","withdrawal_min_amount":"60","status_id":0,"status_name":"active","status_display":"Active","updated_at":"08\/05\/2023 19:15:06","updated_by":"maskman"},{"iso":"PKR","country":"pakistan","country_display":"Pakistan","rate":"63.92","withdrawal_rate":"63.92","withdrawal_fee":"0","withdrawal_min_amount":"0","status_id":0,"status_name":"active","status_display":"Active","updated_at":"08\/05\/2023 15:03:21","updated_by":"System"},{"iso":"SGD","country":"singapore","country_display":"Singapore","rate":"0.29","withdrawal_rate":"0.29","withdrawal_fee":"0","withdrawal_min_amount":"0","status_id":0,"status_name":"active","status_display":"Active","updated_at":"08\/05\/2023 15:03:21","updated_by":"System"},{"iso":"USD","country":"american-samoa","country_display":"American Samoa","rate":"0.22","withdrawal_rate":"0.22","withdrawal_fee":"0","withdrawal_min_amount":"0","status_id":0,"status_name":"active","status_display":"Active","updated_at":"08\/05\/2023 15:03:21","updated_by":"System"},{"iso":"VND","country":"vietnam","country_display":"Vietnam","rate":"5284.46","withdrawal_rate":"5284.46","withdrawal_fee":"0","withdrawal_min_amount":"0","status_id":0,"status_name":"active","status_display":"Active","updated_at":"08\/05\/2023 15:03:21","updated_by":"System"},{"iso":"BDT","country":"bangladesh","country_display":"Bangladesh","rate":"24.14","withdrawal_rate":"24.14","withdrawal_fee":"0","withdrawal_min_amount":"0","status_id":0,"status_name":"active","status_display":"Active","updated_at":"08\/05\/2023 15:03:20","updated_by":"System"},{"iso":"IDR","country":"indonesia","country_display":"Indonesia","rate":"3298.65","withdrawal_rate":"3298.65","withdrawal_fee":"0","withdrawal_min_amount":"0","status_id":0,"status_name":"active","status_display":"Active","updated_at":"08\/05\/2023 15:03:20","updated_by":"System"},{"iso":"INR","country":"india","country_display":"India","rate":"18.41","withdrawal_rate":"18.41","withdrawal_fee":"0","withdrawal_min_amount":"0","status_id":0,"status_name":"active","status_display":"Active","updated_at":"08\/05\/2023 15:03:20","updated_by":"System"},{"iso":"KHR","country":"cambodia","country_display":"Cambodia","rate":"928.72","withdrawal_rate":"928.72","withdrawal_fee":"0","withdrawal_min_amount":"0","status_id":0,"status_name":"active","status_display":"Active","updated_at":"08\/05\/2023 15:03:20","updated_by":"System"},{"iso":"MYR","country":"malaysia","country_display":"Malaysia","rate":"1.00","withdrawal_rate":"1.60","withdrawal_fee":"6","withdrawal_min_amount":"60","status_id":0,"status_name":"active","status_display":"Active","updated_at":"08\/05\/2023 19:26:30","updated_by":"maskman"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":10,"total":10},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2N1cnJlbmN5L3dpdGhkcmF3YWwtcHJlc2V0IiwiaWF0IjoxNjgzNTM4NTQwLCJleHAiOjE2ODM1NDkwMTgsIm5iZiI6MTY4MzU0NTQxOCwianRpIjoia2dreW51RFZhVGxzdVRkdiIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.srW1P0HptOrwBNXZ5-GRxZCResJSTErtJwD-NKj-S0k","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.25751805305481 sec","log_id":"15896af4-8649-426f-abfe-44b482d69774"}
     * ##docs end##
     */

    public function withdrawalCurrencyPreset(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "iso" => 'string|exists:currency,iso',
            "order_sort" => "string|in:asc,desc",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }
        $list = CurrencyRate::preset($validator->validated(), true);
        $data["data"] = $list;
        abort(200, json_encode($data));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Conversion Currency Preset
     * @module = admin
     * @path = currency/conversion-preset
     * @method = post
     * @description = To get conversion currency data.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = iso|<iso>|string|optional|albania|Country name (dropdown:currency_filter_convert_myr)
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @response = {"data":{"list":[{"iso":"USDT","country":"american-samoa","country_display":"American Samoa","rate":"0.21","convert_in_rate":"5.00","convert_out_rate":"0.20","convert_in_daily_limit":"300.00","convert_out_daily_limit":"1000.00","conversion_min_amount":"50","conversion_max_amount":"50000","status_id":0,"status_name":"active","status_display":"active","updated_at":"27\/02\/2024 11:28:10","updated_by":"tkAdms"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2N1cnJlbmN5L2NvbnZlcnNpb24tcHJlc2V0IiwiaWF0IjoxNzA5Njk4MzA3LCJleHAiOjE3MDk3MDIxMjAsIm5iZiI6MTcwOTY5ODUyMCwianRpIjoiSlJzZUN0blB5ZnFhYnBEcCIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.vxjsirQVU3wfWjRmUqgo2cNfuoz7rZtTEess8YluTag","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.044820070266724 sec","log_id":"203955c2-b644-42fc-9ed4-5ce26d35f041"}
     * ##docs end##
     */

    public function conversionCurrencyPreset(Request $request)
    {
        $request->merge(['iso' => Convert::$inputConversion[$request->iso] ?? $request->iso]);
        $validator = Validator::make($request->all(), [
            "iso" => 'string|exists:currency,iso|nullable',
            "order_sort" => "string|in:asc,desc",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }
        $list = CurrencyRate::preset($validator->validated(), false, true);
        $data["data"] = $list;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Currency Edit
     * @module = admin
     * @path = currency/edit
     * @method = POST
     * @description = To edit currency setting.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = iso|<iso>|string|required|MYR|Country ISO.
     * @body = sell_rate|<sell_rate>|numeric|required|5.00|Sell rate.
     * @body = fee|<fee>|numeric|required|5.00|Processing Fee.
     * @body = min_amount|<min_amount>|numeric|required|5.00|Min Order (MYR).
     * @body = instant_rate|<instant_rate>|numeric|required|5.00| Instant Transfer Rate.
     * @body = instant_disabled|<instant_disabled>|integer|required|0| Instant Transfer Display. 0 = enable, 1 = disable
     * @body = normal_rate|<normal_rate>|numeric|required|5.00| Normal Transfer Rate.
     * @body = normal_disabled|<normal_disabled>|integer|required|0| Normal Transfer Display. 0 = enable, 1 = disable
     * @body = daily_rate|<daily_rate>|numeric|required|5.00| Daily Rate.
     * @body = daily_limit|<daily_limit>|numeric|required|5.00| Daily Limit.
     * @body = status|<status>|integer|required|1| Currency Status. (Dropdown: currency_status)
     * 
     * @response = {"status":true,"message":"Updated Successfully.","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmNxcS5jb20vY3VycmVuY3kvZWRpdCIsImlhdCI6MTY3MjgyNzY4NCwiZXhwIjoxNjcyODMyMDE4LCJuYmYiOjE2NzI4Mjg0MTgsImp0aSI6IkpORGp2NVhwZHpvTTFVSlkiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.-eqGQ-ROauel9TjvWt4kipoyHIU8qGsVYO9eQRe1vFk","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.16409707069397 sec","log_id":"24070a7e-d9ed-41a1-bb0c-df52a2d4e9ba"}
     * ##docs end##
     */
    public function edit(Request $request)
    {
        //handle setting's type and type's disabled
        $request->request->add(['type' => CurrencySetting::$type['fiat'], 'disabled' => 0]);

        $validator = Validator::make($request->all(), [
            'iso' => [
                'required',
                'string',
                Rule::exists('currency', 'iso')->whereNotIn('iso', ['USDT']),
            ],
            'sell_rate' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1,3})?$/|gt:0',
            'fee' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1})?$/',
            'min_amount' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1})?$/',
            'instant_rate' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1,3})?$/|gt:0',
            'instant_disabled' => 'required|integer|in:0,1',
            'normal_rate' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1,3})?$/|gt:0',
            'normal_disabled' => 'required|integer|in:0,1',
            'daily_rate' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1,3})?$/|gt:0',
            'daily_limit' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1,2})?$/',
            'status' => 'required|integer|in:' . implode(',', Currency::$disabled),
            'type' => 'required|integer|in:' . implode(',', CurrencySetting::$type),
            'disabled' => 'required|integer|in:0,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $adminDetails = CurrencySetting::edit($validator->validated());
        abort(200, Lang::get('lang.admin-update-success'));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Withdrawal Currency Edit
     * @module = admin
     * @path = currency/withdrawal-edit
     * @method = POST
     * @description = To edit currency withdrawal setting.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = iso|<iso>|string|required|MYR|Country ISO.
     * @body = withdrawal_rate|<withdrawal_rate>|numeric|required|5.00|Withdrawal rate.
     * @body = withdrawal_fee|<withdrawal_fee>|numeric|required|5.00|Withdrawal Processing Fee.
     * @body = withdrawal_min_amount|<withdrawal_min_amount>|numeric|required|5.00|Withdrawal Min Order (MYR).
     * @body = status|<status>|integer|required|1| Currency Status. (Dropdown: currency_status)
     * 
     * @response = {"status":true,"message":"Updated Successfully.","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2N1cnJlbmN5L3dpdGhkcmF3YWwtZWRpdCIsImlhdCI6MTY4MzUzODU0MCwiZXhwIjoxNjgzNTQ3NDYxLCJuYmYiOjE2ODM1NDM4NjEsImp0aSI6Ik4zSUF2QkloNElOMmJIcVkiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.3ZLmzRRKunl38QUH1QqmFxus3tdXh9EKPEsvsoA9QqI","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.09850001335144 sec","log_id":"bb1e586c-d359-4435-9c86-bee72c1c02f3"}
     * ##docs end##
     */
    public function withdrawalCurrencyEdit(Request $request)
    {
        //handle setting's type and type's disabled
        $request->request->add(['type' => CurrencySetting::$type['fiat'], 'disabled' => 0]);

        $validator = Validator::make($request->all(), [
            'iso' => [
                'required',
                'string',
                Rule::exists('currency', 'iso')->whereNotIn('iso', ['USDT']),
            ],
            'withdrawal_rate' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1,2})?$/|gt:0',
            'withdrawal_fee' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1})?$/',
            'withdrawal_min_amount' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1})?$/',
            'status' => 'required|integer|in:' . implode(',', Currency::$disabled),
            'type' => 'required|integer|in:' . implode(',', CurrencySetting::$type),
            'disabled' => 'required|integer|in:0,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        CurrencySetting::edit($validator->validated());
        abort(200, Lang::get('lang.admin-update-success'));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Conversion Currency Edit
     * @module = admin
     * @path = currency/conversion-edit
     * @method = POST
     * @description = To edit currency conversion setting.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = iso|<iso>|string|required|MYR|Country ISO.
     * @body = convert_out_rate|<convert_out_rate>|numeric|required|5.00|Convert out rate. (MYR to others)
     * @body = convert_in_rate|<convert_in_rate>|numeric|required|5.00|Convert in rate.  (others to MYR)
     * @body = conversion_min_amount|<conversion_min_amount>|numeric|required|5.00|Conversion Min Order.
     * @body = conversion_max_amount|<conversion_max_amount>|numeric|required|5.00|Conversion Max Order.
     * @body = convert_in_daily_limit|<convert_in_daily_limit>|numeric|required|5.00|Convert In Daily Limit.
     * @body = convert_out_daily_limit|<convert_out_daily_limit>|numeric|required|5.00|Convert Out Daily Limit.
     * @body = status|<status>|integer|required|1| Currency Status. (Dropdown: currency_convert_status)
     * 
     * @response = {"status":true,"message":"lang.admin-update-success","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2N1cnJlbmN5L2NvbnZlcnNpb24tZWRpdCIsImlhdCI6MTcwNjU5Nzg4NSwiZXhwIjoxNzA2NjA4NzAzLCJuYmYiOjE3MDY2MDUxMDMsImp0aSI6Ikw4YlZYbzV6aGtrblRiRWYiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.HDztwkyEwhQrhSwU-ieUDU9mpd9OOGuubTr0hfhTAZI","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.046898126602173 sec","log_id":"805440ba-e0d5-486a-bde7-b4b7490856f2"}
     * ##docs end##
     */
    public function conversionCurrencyEdit(Request $request)
    {
        //handle setting's type and type's disabled
        $request->request->add(['type' => CurrencySetting::$type['fiat'], 'disabled' => 0]);

        $request->merge(['iso' => Convert::$inputConversion[$request->iso] ?? $request->iso]);

        $validator = Validator::make($request->all(), [
            'iso' => [
                'required',
                'string',
                Rule::exists('currency', 'iso')->whereNotIn('iso', ['USDT']),
            ],
            'convert_out_rate' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1,3})?$/|gt:0',
            'convert_in_rate' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1,3})?$/|gt:0',
            'conversion_min_amount' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1})?$/',
            'conversion_max_amount' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1})?$/',
            'convert_in_daily_limit' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1})?$/',
            'convert_out_daily_limit' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1})?$/',
            'status' => 'required|integer|in:' . implode(',', Currency::$disabled),
            'type' => 'required|integer|in:' . implode(',', CurrencySetting::$type),
            'disabled' => 'required|integer|in:0,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        CurrencySetting::edit($validator->validated(), false, true);
        abort(200, Lang::get('lang.admin-update-success'));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = currency/batch-edit
     * @module = admin
     * @path = currency/batch-edit
     * @method = POST
     * @description = To batch edit currency setting.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = iso|<array>|array|required|['MYR','SGD','VND']|Country ISO Array.
     * @body = iso.*|<*>|string|required|MYR|Country ISO.
     * @body = instant_disabled|<instant_disabled>|integer|required|0| Instant Transfer Display. 0 = enable, 1 = disable
     * @body = normal_disabled|<normal_disabled>|integer|required|0| Normal Transfer Display. 0 = enable, 1 = disable
     * @body = daily_limit|<daily_limit>|numeric|required|5.00| Daily Limit.
     * @body = fee|<fee>|numeric|required|5.00|Processing Fee.
     * @body = min_amount|<min_amount>|numeric|required|5.00|Min Order (MYR).
     * @body = status|<status>|integer|required|1| Currency Status. (Dropdown: currency_convert_status)
     * 
     * @body = withdrawal_rate|<withdrawal_rate>|integer|optional|1|Withdrawal rate. (If no deposit_rate then is required)
     * @response = {"status":true,"message":"Updated Successfully.","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmNxcS5jb20vY3VycmVuY3kvZWRpdCIsImlhdCI6MTY3MjgyNzY4NCwiZXhwIjoxNjcyODMyMDE4LCJuYmYiOjE2NzI4Mjg0MTgsImp0aSI6IkpORGp2NVhwZHpvTTFVSlkiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.-eqGQ-ROauel9TjvWt4kipoyHIU8qGsVYO9eQRe1vFk","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.16409707069397 sec","log_id":"24070a7e-d9ed-41a1-bb0c-df52a2d4e9ba"}
     * ##docs end##
     */
    public function batchEdit(Request $request)
    {
        //handle setting's type and type's disabled
        $request->request->add(['type' => CurrencySetting::$type['fiat'], 'disabled' => 0]);

        $validator = Validator::make($request->all(), [
            'iso' => 'required|array',
            'iso.*' => [
                'required_with:iso',
                'string',
                Rule::exists('currency', 'iso')->whereNotIn('iso', ['USDT']),
            ],
            'instant_disabled' => 'required_without_all:normal_disabled,daily_limit,fee,min_amount,status|nullable|integer|in:0,1',
            'normal_disabled' => 'required_without_all:instant_disabled,daily_limit,fee,min_amount,status|nullable|integer|in:0,1',
            'daily_limit' => 'required_without_all:instant_disabled,normal_disabled,fee,min_amount,status|nullable|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1,2})?$/',
            'fee' => 'required_without_all:instant_disabled,normal_disabled,daily_limit,min_amount,status|nullable|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1,2})?$/',
            'min_amount' => 'required_without_all:instant_disabled,normal_disabled,daily_limit,fee,status|nullable|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1,2})?$/',
            'status' => 'required_without_all:instant_disabled,normal_disabled,daily_limit,fee,min_amount|integer|in:' . implode(',', Currency::$disabled),
            'type' => 'required|integer|in:' . implode(',', CurrencySetting::$type),
            'disabled' => 'required|integer|in:0,1',
        ], [
            "instant_disabled.required_without_all" => __('validation.required'),
            "normal_disabled.required_without_all" => __('validation.required'),
            "daily_limit.required_without_all" => __('validation.required'),
            "fee.required_without_all" => __('validation.required'),
            "min_amount.required_without_all" => __('validation.required'),
            "status.required_without_all" => __('validation.required'),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $adminDetails = CurrencySetting::batchEdit($validator->validated());
        abort(200, Lang::get('lang.admin-update-success'));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Batch Withdrawal Currency Edit
     * @module = admin
     * @path = currency/batch-withdrawal-edit
     * @method = POST
     * @description = To batch edit currency setting.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = iso|<array>|array|required|['MYR','SGD','VND']|Country ISO Array.
     * @body = iso.*|<*>|string|required|MYR|Country ISO.
     * @body = withdrawal_fee|<withdrawal_fee>|numeric|required|5.00|Withdrawal Processing Fee.
     * @body = withdrawal_min_amount|<withdrawal_min_amount>|numeric|required|5.00|Withdrawal Min Order (MYR).
     * @body = status|<status>|integer|required|1| Currency Status. (Dropdown: currency_status)
     * 
     * @response = {"status":true,"message":"Updated Successfully.","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmNxcS5jb20vY3VycmVuY3kvZWRpdCIsImlhdCI6MTY3MjgyNzY4NCwiZXhwIjoxNjcyODMyMDE4LCJuYmYiOjE2NzI4Mjg0MTgsImp0aSI6IkpORGp2NVhwZHpvTTFVSlkiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.-eqGQ-ROauel9TjvWt4kipoyHIU8qGsVYO9eQRe1vFk","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.16409707069397 sec","log_id":"24070a7e-d9ed-41a1-bb0c-df52a2d4e9ba"}
     * ##docs end##
     */
    public function batchWithdrawalCurrencyEdit(Request $request)
    {
        //handle setting's type and type's disabled
        $request->request->add(['type' => CurrencySetting::$type['fiat'], 'disabled' => 0]);

        $validator = Validator::make($request->all(), [
            'iso' => 'required|array',
            'iso.*' => [
                'required',
                'string',
                Rule::exists('currency', 'iso')->whereNotIn('iso', ['USDT']),
            ],
            'withdrawal_fee' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1,2})?$/',
            'withdrawal_min_amount' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1,2})?$/',
            'status' => 'required|integer|in:' . implode(',', Currency::$disabled),
            'type' => 'required|integer|in:' . implode(',', CurrencySetting::$type),
            'disabled' => 'required|integer|in:0,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $adminDetails = CurrencySetting::batchEdit($validator->validated());
        abort(200, Lang::get('lang.admin-update-success'));
    }


    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Batch Conversion Currency Edit
     * @module = admin
     * @path = currency/batch-conversion-edit
     * @method = POST
     * @description = To batch edit currency setting.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = iso|<array>|array|required|['MYR','SGD','VND']|Country ISO Array.
     * @body = iso.*|<*>|string|required|MYR|Country ISO.
     * @body = conversion_min_amount|<conversion_min_amount>|numeric|required|5.00|Conversion Min Order.
     * @body = conversion_max_amount|<conversion_max_amount>|numeric|required|5.00|Conversion Max Order.
     * @body = status|<status>|integer|required|1| Currency Status. (Dropdown: currency_status)
     * 
     * @response = {"status":true,"message":"lang.admin-update-success","dropdown":{"currency_status":[{"id":0,"name":"active","display":"active"},{"id":1,"name":"inactive","display":"inactive"}]},"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2N1cnJlbmN5L2JhdGNoLWNvbnZlcnNpb24tZWRpdCIsImlhdCI6MTcwNjU5Nzg4NSwiZXhwIjoxNzA2NjA4NzM2LCJuYmYiOjE3MDY2MDUxMzYsImp0aSI6IktrM2F0YUNjOEJlQkk4Sk8iLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.QdzKSQXv-KgfBkfqcgL_WO3l1gf2xTemtMJWAKydLSs","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.034539937973022 sec","log_id":"cc9dedea-ae9c-4087-8477-98e9c71a2065"}
     * ##docs end##
     */
    public function batchConversionCurrencyEdit(Request $request)
    {
        //handle setting's type and type's disabled
        $request->request->add(['type' => CurrencySetting::$type['fiat'], 'disabled' => 0]);

        $iso_input = $request->input('iso');
        if (isset($iso_input)) {
            foreach ($iso_input as $key => $iso) {
                $iso_input[$key] = Convert::$inputConversion[$iso] ?? $iso;
            }
            $request->merge(['iso' => $iso_input]);
        }

        $validator = Validator::make($request->all(), [
            'iso' => 'required|array',
            'iso.*' => [
                'required',
                'string',
                Rule::exists('currency', 'iso')->whereNotIn('iso', ['USDT']),
            ],
            'conversion_min_amount' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1,2})?$/',
            'conversion_max_amount' => 'required|numeric|regex:/^-?[0-9]+(?:\.[0-9]{1,2})?$/',
            'status' => 'required|integer|in:' . implode(',', Currency::$disabled),
            'type' => 'required|integer|in:' . implode(',', CurrencySetting::$type),
            'disabled' => 'required|integer|in:0,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $adminDetails = CurrencySetting::batchEdit($validator->validated());
        abort(200, Lang::get('lang.admin-update-success'));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = currency/rate-history
     * @module = admin
     * @path = currency/rate-history
     * @method = post
     * @description = To get currency rate history.
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = iso|<iso>|string|required|MYR|Country ISO.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @response = {"data":{"list":[{"date":"2023-01-04 18:33:38","deposit_rate":"5.00","withdrawal_rate":"10.00","creator":"maskman"},{"date":"2022-12-09 12:35:02","deposit_rate":"0.00","withdrawal_rate":"4.50","creator":"maskman"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":2,"total":2},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmNxcS5jb20vY3VycmVuY3kvcmF0ZS1oaXN0b3J5IiwiaWF0IjoxNjcyODI3Njg0LCJleHAiOjE2NzI4MzIwMjcsIm5iZiI6MTY3MjgyODQyNywianRpIjoiRUtVQXNtV0dvZ1dSQ0Q5ZiIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.Oh1pIDYTrn_QpvHOGgw3AToe9x0Wjm14HQvk1Gkthn0","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.10389685630798 sec","log_id":"369b980e-8cc1-47f0-85d3-a1d855f05704"}
     * ##docs end##
     */

    public function getRateHistory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "order_sort" => "string|in:asc,desc",
            "iso" => "required|exists:currency,iso",
            "from_date" => "required_with:to_date|string|date_format:Y-m-d",
            "to_date" => "required_with:from_date|string|date_format:Y-m-d|after_or_equal:from_date",
            "limit" => "int",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $list = CurrencyRate::getHistory($request->all());
        $data["data"] = $list;
        abort(200, json_encode($data));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Currency Rate
     * @module = user,app
     * @path = currency/get
     * @method = post
     * @description = To get currency rate.
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @body = ip_address|<ip_address>|string|optional|127.0.0.1|IP Address.
     * @response = {"data":{"currency":[{"from":"MYR","to":"BDT","rate":"24.14145201"},{"from":"MYR","to":"IDR","rate":"3298.65986305"},{"from":"MYR","to":"INR","rate":"18.41992135"},{"from":"MYR","to":"KHR","rate":"928.72271920"},{"from":"MYR","to":"MYR","rate":"1.00000000"},{"from":"MYR","to":"PHP","rate":"12.46588333"},{"from":"MYR","to":"PKR","rate":"63.92005555"},{"from":"MYR","to":"SGD","rate":"0.29863427"},{"from":"MYR","to":"USD","rate":"0.22540956"},{"from":"MYR","to":"VND","rate":"5284.46885969"}],"country":[{"name":"bangladesh","country_display":"Bangladesh","iso_code":"BD","currency_code":"BDT"},{"name":"cambodia","country_display":"Cambodia","iso_code":"KH","currency_code":"KHR"},{"name":"india","country_display":"India","iso_code":"IN","currency_code":"INR"},{"name":"indonesia","country_display":"Indonesia","iso_code":"ID","currency_code":"IDR"},{"name":"malaysia","country_display":"Malaysia","iso_code":"MY","currency_code":"MYR"},{"name":"pakistan","country_display":"Pakistan","iso_code":"PK","currency_code":"PKR"},{"name":"philippines","country_display":"Philippines","iso_code":"PH","currency_code":"PHP"},{"name":"singapore","country_display":"Singapore","iso_code":"SG","currency_code":"SGD"},{"name":"united-states","country_display":"United States","iso_code":"US","currency_code":"USD"},{"name":"vietnam","country_display":"Vietnam","iso_code":"VN","currency_code":"VND"}],"validCountry":1},"status":true,"timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.35908985137939 sec","log_id":"3839f71f-9f5e-4431-a490-1389f67a510a"}
     * ##docs end##
     */

    public function getCurrency(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ip_address' => 'string|nullable'
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $list = CurrencyRate::getCurrency($validator->validated());
        $data["data"] = $list;
        abort(200, json_encode($data));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Myr Currency Rate
     * @module = app
     * @path = currency/myr-currency-rate
     * @method = post
     * @description = To get currency rate.
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @body = user_id|<user_id>|integer|optional|1|User's Id.
     * @response = {"data":{"list":[{"from":"MYR","to":"AED","to_iso":"AE","rate":"0.79584000"},{"from":"MYR","to":"AFN","to_iso":"AF","rate":"18.58739000"},{"from":"MYR","to":"ALL","to_iso":"AL","rate":"20.97698000"},{"from":"MYR","to":"AMD","to_iso":"AM","rate":"82.83871000"},{"from":"MYR","to":"ANG","to_iso":"AN","rate":"0.38675000"},{"from":"MYR","to":"AOA","to_iso":"AO","rate":"150.99178000"},{"from":"MYR","to":"ARS","to_iso":"AR","rate":"53.83974000"},{"from":"MYR","to":"AUD","to_iso":"AU","rate":"0.31481000"},{"from":"MYR","to":"AWG","to_iso":"AW","rate":"0.38948000"},{"from":"MYR","to":"AZN","to_iso":"AZ","rate":"0.36811000"},{"from":"MYR","to":"BAM","to_iso":"BA","rate":"0.38794000"},{"from":"MYR","to":"BBD","to_iso":"BB","rate":"0.43308000"},{"from":"MYR","to":"BDT","to_iso":"BD","rate":"23.36911000"},{"from":"MYR","to":"BGN","to_iso":"BG","rate":"0.38936000"},{"from":"MYR","to":"BHD","to_iso":"BH","rate":"0.08141000"},{"from":"MYR","to":"BIF","to_iso":"BI","rate":"609.98184000"},{"from":"MYR","to":"BMD","to_iso":"BM","rate":"0.21643000"},{"from":"MYR","to":"BND","to_iso":"BN","rate":"0.28958000"},{"from":"MYR","to":"BOB","to_iso":"BO","rate":"1.49899000"},{"from":"MYR","to":"BRL","to_iso":"BR","rate":"1.04130000"},{"from":"MYR","to":"BSD","to_iso":"BS","rate":"0.21684000"},{"from":"MYR","to":"BTN","to_iso":"BT","rate":"17.77651000"},{"from":"MYR","to":"BWP","to_iso":"BW","rate":"2.87757000"},{"from":"MYR","to":"BZD","to_iso":"BZ","rate":"0.43601000"},{"from":"MYR","to":"CAD","to_iso":"CA","rate":"0.28664000"},{"from":"MYR","to":"CDF","to_iso":"CD","rate":"507.41761000"},{"from":"MYR","to":"CHF","to_iso":"CH","rate":"0.19306000"},{"from":"MYR","to":"CLP","to_iso":"CL","rate":"171.91269000"},{"from":"MYR","to":"CNY","to_iso":"CN","rate":"1.54309000"},{"from":"MYR","to":"COP","to_iso":"CO","rate":"898.07437000"},{"from":"MYR","to":"CUP","to_iso":"CU","rate":"5.15411000"},{"from":"MYR","to":"CVE","to_iso":"CV","rate":"21.83629000"},{"from":"MYR","to":"CZK","to_iso":"CZ","rate":"4.71592000"},{"from":"MYR","to":"DJF","to_iso":"DJ","rate":"38.52735000"},{"from":"MYR","to":"DKK","to_iso":"DK","rate":"1.47560000"},{"from":"MYR","to":"DOP","to_iso":"DO","rate":"11.86838000"},{"from":"MYR","to":"DZD","to_iso":"DZ","rate":"29.41618000"},{"from":"MYR","to":"EGP","to_iso":"EG","rate":"6.68815000"},{"from":"MYR","to":"ERN","to_iso":"ER","rate":"3.27593000"},{"from":"MYR","to":"ETB","to_iso":"ET","rate":"11.78448000"},{"from":"MYR","to":"EUR","to_iso":"ES","rate":"0.19803000"},{"from":"MYR","to":"FJD","to_iso":"FJ","rate":"0.47601000"},{"from":"MYR","to":"FKP","to_iso":"FK","rate":"0.16939000"},{"from":"MYR","to":"GBP","to_iso":"GB","rate":"0.16939000"},{"from":"MYR","to":"GEL","to_iso":"GE","rate":"0.56515000"},{"from":"MYR","to":"GHS","to_iso":"GH","rate":"2.37076000"},{"from":"MYR","to":"GMD","to_iso":"GM","rate":"12.54745000"},{"from":"MYR","to":"GNF","to_iso":"GN","rate":"1853.30108000"},{"from":"MYR","to":"GTQ","to_iso":"GT","rate":"1.69521000"},{"from":"MYR","to":"GYD","to_iso":"GY","rate":"45.87297000"},{"from":"MYR","to":"HKD","to_iso":"HK","rate":"1.69446000"},{"from":"MYR","to":"HNL","to_iso":"HN","rate":"5.32802000"},{"from":"MYR","to":"HRK","to_iso":"HR","rate":"1.43397000"},{"from":"MYR","to":"HTG","to_iso":"HT","rate":"30.40965000"},{"from":"MYR","to":"HUF","to_iso":"HU","rate":"74.07479000"},{"from":"MYR","to":"IDR","to_iso":"ID","rate":"3233.90113000"},{"from":"MYR","to":"ILS","to_iso":"IL","rate":"0.76971000"},{"from":"MYR","to":"INR","to_iso":"IN","rate":"17.75009000"},{"from":"MYR","to":"IQD","to_iso":"IQ","rate":"283.08026000"},{"from":"MYR","to":"IRR","to_iso":"IR","rate":"9092.38895000"},{"from":"MYR","to":"ISK","to_iso":"IS","rate":"29.51578000"},{"from":"MYR","to":"JMD","to_iso":"JM","rate":"33.25406000"},{"from":"MYR","to":"JOD","to_iso":"JO","rate":"0.15341000"},{"from":"MYR","to":"JPY","to_iso":"JP","rate":"30.58684000"},{"from":"MYR","to":"KES","to_iso":"KE","rate":"30.24077000"},{"from":"MYR","to":"KHR","to_iso":"KH","rate":"890.09100000"},{"from":"MYR","to":"KGS","to_iso":"KG","rate":"18.76829000"},{"from":"MYR","to":"KMF","to_iso":"KM","rate":"97.87279000"},{"from":"MYR","to":"KRW","to_iso":"KR","rate":"275.82696000"},{"from":"MYR","to":"KWD","to_iso":"KW","rate":"0.06649000"},{"from":"MYR","to":"KYD","to_iso":"KY","rate":"0.17751000"},{"from":"MYR","to":"KZT","to_iso":"KZ","rate":"97.66123000"},{"from":"MYR","to":"LAK","to_iso":"LA","rate":"3978.10120000"},{"from":"MYR","to":"LBP","to_iso":"LB","rate":"3249.11239000"},{"from":"MYR","to":"LKR","to_iso":"LK","rate":"66.86689000"},{"from":"MYR","to":"LRD","to_iso":"LR","rate":"37.40074000"},{"from":"MYR","to":"LSL","to_iso":"LS","rate":"3.94742000"},{"from":"MYR","to":"LYD","to_iso":"LY","rate":"1.04131000"},{"from":"MYR","to":"MAD","to_iso":"MA","rate":"2.16366000"},{"from":"MYR","to":"MDL","to_iso":"MD","rate":"3.84073000"},{"from":"MYR","to":"MGA","to_iso":"MG","rate":"966.66226000"},{"from":"MYR","to":"MKD","to_iso":"MK","rate":"12.21082000"},{"from":"MYR","to":"MMK","to_iso":"MM","rate":"453.52942000"},{"from":"MYR","to":"MNT","to_iso":"MN","rate":"744.95047000"},{"from":"MYR","to":"MOP","to_iso":"MO","rate":"1.65805000"},{"from":"MYR","to":"MUR","to_iso":"MU","rate":"10.00137000"},{"from":"MYR","to":"MYR","to_iso":"MY","rate":"1.00000000"},{"from":"MYR","to":"MVR","to_iso":"MV","rate":"3.34417000"},{"from":"MYR","to":"MWK","to_iso":"MW","rate":"221.94216000"},{"from":"MYR","to":"MXN","to_iso":"MX","rate":"3.71731000"},{"from":"MYR","to":"MZN","to_iso":"MZ","rate":"13.78594000"},{"from":"MYR","to":"NAD","to_iso":"NA","rate":"3.93682000"},{"from":"MYR","to":"NGN","to_iso":"NG","rate":"99.97239000"},{"from":"MYR","to":"NOK","to_iso":"NO","rate":"2.27371000"},{"from":"MYR","to":"NPR","to_iso":"NP","rate":"28.39289000"},{"from":"MYR","to":"NZD","to_iso":"NZ","rate":"0.34772000"},{"from":"MYR","to":"OMR","to_iso":"OM","rate":"0.08315000"},{"from":"MYR","to":"PAB","to_iso":"PA","rate":"0.21652000"},{"from":"MYR","to":"PEN","to_iso":"PE","rate":"0.78469000"},{"from":"MYR","to":"PGK","to_iso":"PG","rate":"0.76913000"},{"from":"MYR","to":"PHP","to_iso":"PH","rate":"12.09629000"},{"from":"MYR","to":"PKR","to_iso":"PK","rate":"62.20306000"},{"from":"MYR","to":"PLN","to_iso":"PL","rate":"0.88152000"},{"from":"MYR","to":"PYG","to_iso":"PY","rate":"1571.05761000"},{"from":"MYR","to":"QAR","to_iso":"QA","rate":"0.78782000"},{"from":"MYR","to":"RON","to_iso":"RO","rate":"0.98219000"},{"from":"MYR","to":"RSD","to_iso":"RS","rate":"23.14602000"},{"from":"MYR","to":"RUB","to_iso":"RU","rate":"18.18542000"},{"from":"MYR","to":"RWF","to_iso":"RW","rate":"247.03567000"},{"from":"MYR","to":"SAR","to_iso":"SA","rate":"0.81096000"},{"from":"MYR","to":"SCR","to_iso":"SC","rate":"3.05301000"},{"from":"MYR","to":"SDG","to_iso":"SD","rate":"129.86151000"},{"from":"MYR","to":"SGD","to_iso":"SG","rate":"0.28970000"},{"from":"MYR","to":"SEK","to_iso":"SE","rate":"2.29858000"},{"from":"MYR","to":"SHP","to_iso":"SH","rate":"0.16939000"},{"from":"MYR","to":"SLL","to_iso":"SL","rate":"4861.38778000"},{"from":"MYR","to":"SOS","to_iso":"SO","rate":"123.38689000"},{"from":"MYR","to":"SRD","to_iso":"SR","rate":"8.11850000"},{"from":"MYR","to":"SYP","to_iso":"SY","rate":"543.81115000"},{"from":"MYR","to":"SZL","to_iso":"SZ","rate":"3.94487000"},{"from":"MYR","to":"THB","to_iso":"TH","rate":"7.50556000"},{"from":"MYR","to":"TJS","to_iso":"TJ","rate":"2.36142000"},{"from":"MYR","to":"TMT","to_iso":"TM","rate":"0.75850000"},{"from":"MYR","to":"TND","to_iso":"TN","rate":"0.66791000"},{"from":"MYR","to":"TOP","to_iso":"TO","rate":"0.50275000"},{"from":"MYR","to":"TRY","to_iso":"TR","rate":"5.12902000"},{"from":"MYR","to":"TTD","to_iso":"TT","rate":"1.46156000"},{"from":"MYR","to":"TWD","to_iso":"TW","rate":"6.66077000"},{"from":"MYR","to":"TZS","to_iso":"TZ","rate":"517.40487000"},{"from":"MYR","to":"UAH","to_iso":"UA","rate":"7.94279000"},{"from":"MYR","to":"UGX","to_iso":"UG","rate":"803.19538000"},{"from":"MYR","to":"USD","to_iso":"US","rate":"0.21668000"},{"from":"MYR","to":"VND","to_iso":"VN","rate":"5092.01290000"},{"from":"MYR","to":"UYU","to_iso":"UY","rate":"8.28480000"},{"from":"MYR","to":"UZS","to_iso":"UZ","rate":"2484.50077000"},{"from":"MYR","to":"VUV","to_iso":"VU","rate":"25.35335000"},{"from":"MYR","to":"WST","to_iso":"WS","rate":"0.58060000"},{"from":"MYR","to":"XAF","to_iso":"CF","rate":"129.91182000"},{"from":"MYR","to":"XCD","to_iso":"AG","rate":"0.58492000"},{"from":"MYR","to":"XOF","to_iso":"NE","rate":"129.91182000"},{"from":"MYR","to":"XPF","to_iso":"PF","rate":"23.60947000"},{"from":"MYR","to":"YER","to_iso":"YE","rate":"54.07638000"},{"from":"MYR","to":"ZAR","to_iso":"ZA","rate":"3.94802000"},{"from":"MYR","to":"ZMW","to_iso":"ZM","rate":"4.17683000"}],"updated_at":"16.06.2023 03:42PM"},"status":true,"timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.15964913368225 sec","log_id":"46f6b531-a71d-471e-87fe-1a4cd35bab34"}
     * ##docs end##
     */

    public function getMyrCurrencyRate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => "integer",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $list = CurrencyRateHourly::getMyrCurrencyRate($validator->validated());
        $data["data"] = $list;
        abort(200, json_encode($data));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Currency Rate Hourly
     * @module = user,app
     * @path = currency/get-hourly
     * @method = post
     * @description = To get currency rate hourly.
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @body = ip_address|<ip_address>|string|optional|127.0.0.1|IP Address.
     * @response = {"data":{"currency":[{"from":"MYR","to":"BDT","rate":"24.14145201"},{"from":"MYR","to":"IDR","rate":"3298.65986305"},{"from":"MYR","to":"INR","rate":"18.41992135"},{"from":"MYR","to":"KHR","rate":"928.72271920"},{"from":"MYR","to":"MYR","rate":"1.00000000"},{"from":"MYR","to":"PHP","rate":"12.46588333"},{"from":"MYR","to":"PKR","rate":"63.92005555"},{"from":"MYR","to":"SGD","rate":"0.29863427"},{"from":"MYR","to":"USD","rate":"0.22540956"},{"from":"MYR","to":"VND","rate":"5284.46885969"}],"country":[{"name":"bangladesh","country_display":"Bangladesh","iso_code":"BD","currency_code":"BDT"},{"name":"cambodia","country_display":"Cambodia","iso_code":"KH","currency_code":"KHR"},{"name":"india","country_display":"India","iso_code":"IN","currency_code":"INR"},{"name":"indonesia","country_display":"Indonesia","iso_code":"ID","currency_code":"IDR"},{"name":"malaysia","country_display":"Malaysia","iso_code":"MY","currency_code":"MYR"},{"name":"pakistan","country_display":"Pakistan","iso_code":"PK","currency_code":"PKR"},{"name":"philippines","country_display":"Philippines","iso_code":"PH","currency_code":"PHP"},{"name":"singapore","country_display":"Singapore","iso_code":"SG","currency_code":"SGD"},{"name":"united-states","country_display":"United States","iso_code":"US","currency_code":"USD"},{"name":"vietnam","country_display":"Vietnam","iso_code":"VN","currency_code":"VND"}],"validCountry":1},"status":true,"timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.35908985137939 sec","log_id":"3839f71f-9f5e-4431-a490-1389f67a510a"}
     * ##docs end##
     */

    public function getCurrencyHourly(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ip_address' => 'string|nullable'
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $list = CurrencyRateHourly::getCurrency($validator->validated());
        $data["data"] = $list;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Exchange Data
     * @module = user,app
     * @path = currency/get-exchange-data
     * @method = POST
     * @description = To get exchange data.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @response = {"data":{"currency":[{"id":2,"country_id":226,"from_currency_iso":"MYR","currency_iso":"USD","country_iso_code":"US","country_name":"united-states","country_name_display":"United States","rate":"0.22540956","processing_fee":"1.00000000","daily_limit":"10.00000000","daily_rate":"0.22540956","min_order":"0.00000000"},{"id":3,"country_id":232,"from_currency_iso":"MYR","currency_iso":"VND","country_iso_code":"VN","country_name":"vietnam","country_name_display":"Vietnam","rate":"5284.46885969","processing_fee":"0.00000000","daily_limit":"0.00000000","daily_rate":"5284.46885969","min_order":"0.00000000"},{"id":4,"country_id":100,"from_currency_iso":"MYR","currency_iso":"IDR","country_iso_code":"ID","country_name":"indonesia","country_name_display":"Indonesia","rate":"3298.65986305","processing_fee":"0.00000000","daily_limit":"0.00000000","daily_rate":"3298.65986305","min_order":"0.00000000"},{"id":5,"country_id":129,"from_currency_iso":"MYR","currency_iso":"MYR","country_iso_code":"MY","country_name":"malaysia","country_name_display":"Malaysia","rate":"1.00000000","processing_fee":"0.00000000","daily_limit":"0.00000000","daily_rate":"1.00000000","min_order":"10.00000000"},{"id":6,"country_id":192,"from_currency_iso":"MYR","currency_iso":"SGD","country_iso_code":"SG","country_name":"singapore","country_name_display":"Singapore","rate":"0.29863427","processing_fee":"0.00000000","daily_limit":"0.00000000","daily_rate":"0.29863427","min_order":"0.00000000"},{"id":7,"country_id":99,"from_currency_iso":"MYR","currency_iso":"INR","country_iso_code":"IN","country_name":"india","country_name_display":"India","rate":"18.41992135","processing_fee":"0.00000000","daily_limit":"0.00000000","daily_rate":"18.41992135","min_order":"0.00000000"},{"id":8,"country_id":18,"from_currency_iso":"MYR","currency_iso":"BDT","country_iso_code":"BD","country_name":"bangladesh","country_name_display":"Bangladesh","rate":"24.14145201","processing_fee":"0.00000000","daily_limit":"0.00000000","daily_rate":"24.14145201","min_order":"0.00000000"},{"id":9,"country_id":169,"from_currency_iso":"MYR","currency_iso":"PHP","country_iso_code":"PH","country_name":"philippines","country_name_display":"Philippines","rate":"12.46588333","processing_fee":"0.00000000","daily_limit":"0.00000000","daily_rate":"12.46588333","min_order":"0.00000000"},{"id":10,"country_id":162,"from_currency_iso":"MYR","currency_iso":"PKR","country_iso_code":"PK","country_name":"pakistan","country_name_display":"Pakistan","rate":"63.92005555","processing_fee":"0.00000000","daily_limit":"0.00000000","daily_rate":"63.92005555","min_order":"0.00000000"},{"id":11,"country_id":36,"from_currency_iso":"MYR","currency_iso":"KHR","country_iso_code":"KH","country_name":"cambodia","country_name_display":"Cambodia","rate":"928.72271920","processing_fee":"0.00000000","daily_limit":"0.00000000","daily_rate":"928.72271920","min_order":"0.00000000"}],"total_transfer_amt":0,"transfer_type_id":1},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS9jdXJyZW5jeS9nZXQtZXhjaGFuZ2UtZGF0YSIsImlhdCI6MTY4NTAxMTYxMywiZXhwIjoxNjg1MDE1MjUzLCJuYmYiOjE2ODUwMTE2NTMsImp0aSI6IjhCRzdOWkx3Z2k1dXRGbUEiLCJzdWIiOiIxMDAwMDAxIiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.2jUQiIcosf2D3kN48cp4tjaFVIH4OzSzqBLbeyjdlyQ","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"3363ecba-5b60-4f7b-aae9-8888d2f5eb3d"}
     * ##docs end##
     */
    public function getExchangeData(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) $request->request->add(['user_id' => auth()->user()->id]);
        $validator = Validator::make($request->all(), [
            "user_id" => "integer|exists:users,id,user_type," . User::$userType['user-account']
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $list = CurrencyRate::getExchangeData($validator->validated());
        $data["data"] = $list;
        abort(200, json_encode($data));
    }
}
