<?php

namespace App\Http\Controllers;

use App\Models\PaymentMethod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PaymentMethodController extends Controller
{
    public function getPaymentMethods(Request $request)
    {
        $paymentMethods = PaymentMethod::orderBy('priority', 'ASC')->get();
        abort(200, json_encode(['data' => $paymentMethods]));
    }

    public function updatePaymentMethodStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer|exists:payment_methods,id',
            'status' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        PaymentMethod::find($request->id)->update(['status' => $request->status]);

        abort(200, 'Payment Gateway Updated');
    }
}
