<?php

namespace App\Http\Controllers;

use App\Models\AngpauEvent;
use App\Models\RewardSetting;
use App\Models\Store;
use App\Models\UserAngpau;
use App\Models\UserTicketReward;
use App\Models\WWJAccountBetTransaction;
use App\Traits;
use DateTime;
use DateTimeZone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;

class BuayaTournamentController extends Controller
{
    public function getTopTurnoverAndDeposit(Request $request)
    {
        $user_id = auth()->user()->id ?? null;
        $store_id = auth()->user()->store_id ?? null;
        $inStores = [29, 30, 31, 6, 7, 8, 12, 20001, 20002, 20003, 20004, 20005, 20006, 20007, 20008, 20009, 20010, 20011];

        $betTransaction = WWJAccountBetTransaction::with('user')
            ->whereHas('user', function ($q) use ($inStores) {
                $q->whereIn('store_id', $inStores);
            })
            ->orderByDesc('total_bet')
            ->limit(13)
            ->get()
            ->map(function ($q) {
                $name = $q->user->name ?? null;

                return [
                    'name' => substr($name, 0, 1) . '******' . substr($name, -1),
                    'turnover_amount' => (string) Traits\DecimalTrait::setDecimal($q->total_bet ?? 0),
                    'total_token' => ($q->total_bet > 0 ? ceil($q->total_bet / 100) : 0),
                ];
            })
            ->toArray();

        $yourRank = null;
        $allTransactionId = WWJAccountBetTransaction::with('user')
            ->whereHas('user', function ($q) use ($inStores) {
                $q->whereIn('store_id', $inStores);
            })
            ->orderByDesc('total_bet')
            ->select('user_id')
            ->get()
            ->toArray();

        foreach (array_values($allTransactionId) as $i => $value) {
            if ($value['user_id'] == $user_id) {
                $yourRank = $i + 1;
            }
        }

        $currentTurnover = (string) (WWJAccountBetTransaction::where('user_id', $user_id)->first()?->total_bet ?? 0);

        $depositList = UserTicketReward::where('status', true)
            ->where('store_id', auth()->user()->store_id)
            ->orderBy('total_deposit', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    // 'name' => $item->name,
                    'name' => substr($item->name, 0, 1) . '********' . substr($item->name, -1),
                    'total_deposit' => (string) Traits\DecimalTrait::setDecimal($item->total_deposit ?? 0),
                    'total_token' => $item->total_token,
                ];
            });

        $depositRank = null;
        $allDepositId = UserTicketReward::orderByDesc('total_deposit')
            ->select('user_id')
            ->get()
            ->toArray();

        foreach (array_values($allDepositId) as $i => $value) {
            if ($value['user_id'] == $user_id) {
                $depositRank = $i + 1;
            }
        }

        $userTicketReward = UserTicketReward::where('user_id', $user_id)->first();
        $totalDeposit = $userTicketReward?->total_deposit ?? 0;
        $totalTicket = $userTicketReward?->total_token ?? 0;

        $terms = RewardSetting::where('name', 'event.tournament.terms')->first()->value;
        $user = auth()->user();
        $dateRange = Store::getDrawDateByStoreId($user->store_id);
        $dt = new DateTime('now', new DateTimeZone('Asia/Singapore'));
        $dateNow = date_format($dt, 'Y-m-d H:i:s');
        $currentAngpauEvent = AngpauEvent::getCurrentAngpauEvent();
        $angpauEvent = $currentAngpauEvent ? array_merge($currentAngpauEvent, ['current_date' => $dateNow]) : null;
        $canClaimAngpau = true;

        $userAngpaus = UserAngpau::where('user_id', $user_id)->get();

        if (! $angpauEvent || ! UserAngpau::isUserEntitle($user->id, $angpauEvent['min_ticket'])) {
            $canClaimAngpau = false;
        }

        if ($angpauEvent) {
            foreach (($userAngpaus ?? []) as $angpau) {
                if ($angpau?->angpau_event_id == $angpauEvent['id'] ?? null) {
                    $canClaimAngpau = false;
                }
            }
        }

        abort(200, json_encode([
            'data' => [
                'bg_image' => 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/2024/12/image_95.png',
                'top_turn_over' => [
                    'description' => '', // Lang::has('lang.top-turnover-announcement-statement') ? Lang::get('lang.top-turnover-announcement-statement') : '',
                    'rank_list' => $betTransaction,
                    'current_rank' => (string) ($yourRank ?? '-'),
                    'current_turnover' => (string) Traits\DecimalTrait::setDecimal($currentTurnover ?? 0),
                    'current_ticket' => ($currentTurnover > 0 ? ceil($currentTurnover / 100) : 0),
                    'prize_list' => [
                        'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/reward/pmt_prize_01.png',
                        'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/reward/pmt_prize_02.png',
                        'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/reward/pmt_prize_03.png',
                        'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/reward/pmt_prize_04.png',
                    ],
                    'terms' => Lang::has('langReward.tournament-turnover-terms') ? Lang::get('langReward.tournament-turnover-terms') : '',
                    // "terms" => RewardSetting::where("name", "event.tournament.terms")->first()->value,
                ],
                'top_deposit' => [
                    'lucky_draw' => [
                        'description' => 'Coming Soon',
                        'list' => [
                            // [
                            //     'date' => '8/2',
                            //     'title' => 'XSD',
                            //     'status' => $store_id == 8,
                            // ],
                            // [
                            //     'date' => '9/2',
                            //     'title' => 'CLP',
                            //     'status' => $store_id == 29,
                            // ],
                            // [
                            //     'date' => '15/2',
                            //     'title' => 'Atta',
                            //     'status' => $store_id == 12,
                            // ],
                            // [
                            //     'date' => '1/3',
                            //     'title' => 'BM Plaza',
                            //     'status' => $store_id == 30,
                            // ],
                            // [
                            //     'date' => '15/3',
                            //     'title' => 'Sg Lembu',
                            //     'status' => $store_id == 7,
                            // ],
                            // [
                            //     'date' => '8/3',
                            //     'title' => 'Asas Dunia',
                            //     'status' => $store_id == 10,
                            // ],
                            // [
                            //     'date' => '22/2',
                            //     'title' => 'PCF',
                            //     'status' => $store_id == 6,
                            // ],
                            [
                                'date' => 'Coming Soon',
                                'title' => 'Lucky Draw Event',
                                'status' => true,
                            ],
                        ],
                    ],
                    'description' => Lang::has('lang.top-deposit-announcement-statement') ? Lang::get('lang.top-deposit-announcement-statement') : '',
                    'rank_list' => $depositList,
                    'current_rank' => (string) ($$depositRank ?? '-'),
                    'total_deposit' => (string) $totalDeposit,
                    'total_ticket' => $totalTicket,
                    'prize_list' => [
                        'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/reward/prize_1.png',
                        'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/reward/prize_2.png',
                        'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/reward/prize_3.png',
                        // 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/reward/prize_4.png',
                    ],
                    'terms' => Lang::has('langReward.tournament-deposit-terms') ? Lang::get('langReward.tournament-deposit-terms') : '',
                    'result_url' => 'https://luckydraw.funwallet.co/section?id=30',
                    // "terms" => RewardSetting::where("name", "event.deposit.terms")->first()->value,
                    'angpau_event' => $angpauEvent ? array_merge($angpauEvent, [
                        'title' => Lang::has('lang.angpau_title') ? Lang::get('lang.angpau_title') : '',
                        'subtitle' => Lang::has('lang.angpau_subtitle') ? Lang::get('lang.angpau_subtitle') . ' RM' . $angpauEvent['pool_amount_display'] : '',
                        'image' => 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/angpau/angpau.png',
                        'inactive_image' => 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/angpau/inactive_angpau.png',
                        'can_claim' => $canClaimAngpau,
                    ]) : null,
                ],
                'date_range' => $dateRange,
            ],
        ]));
    }
}
