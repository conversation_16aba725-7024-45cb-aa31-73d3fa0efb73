<?php

namespace App\Http\Controllers;

use App\Events\MtForceRefresh;
use App\Events\MtUserForceLogout;
use App\Events\SendStoreEvent;
use App\Models\ActivityLog;
use App\Models\AdminDetail;
use App\Models\Providers\MtMachine;
use App\Models\Store;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class MtMachineController extends Controller
{
    public function __construct() {}

    private function addKillSwitchActivityLog($action, $userId, $storeId, $status)
    {
        $data = [
            'storeId' => $storeId,
            'status' => $status,
        ];
        ActivityLog::insertActivityLog($action, $userId, $data, $userId);
    }

    public function getMachineList(Request $request)
    {
        $machines = MtMachine::all();

        abort(200, json_encode(['data' => $machines]));
    }

    public function getMechineDetail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:mt_machines,id,deleted_at,NULL',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $machine = MtMachine::find($request->id);

        if (! $machine) {
            abort(400, 'MT Machine not found.');
        }

        abort(200, json_encode(['data' => $machine]));
    }

    public function storeMachine(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'description' => 'nullable|string',
            'store_id' => 'required|exists:store,id,deleted_at,NULL',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $machines = MtMachine::create([
            'name' => $request->name,
            'description' => $request->description,
            'store_id' => $request->store_id,
        ]);

        abort(200, json_encode(['data' => $machines]));
    }

    public function updateMachine(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:mt_machines,id,deleted_at,NULL',
            'name' => 'required|string',
            'description' => 'nullable|string',
            'status' => 'required|boolean',
            'store_id' => 'required|exists:store,id,deleted_at,NULL',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $machine = MtMachine::find($request->id);

        if (! $machine) {
            abort(400, 'MT Machine not found.');
        }

        $machine->update([
            'name' => $request->name,
            'description' => $request->description,
            'status' => $request->status,
            'store_id' => $request->store_id,
        ]);

        abort(200, json_encode(['data' => $machine]));
    }

    public function deleteMachine(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:mt_machines,id,deleted_at,NULL',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $machine = MtMachine::find($request->id);

        if (! $machine) {
            abort(400, 'MT Machine not found.');
        }

        $machine->delete();

        abort(200, json_encode(['data' => []]));
    }

    public function killSwitchStatus(Request $request)
    {
        $store_ids = AdminDetail::where('admin_id', auth()->user()->id)->first()->value ?? [];
        $exist = MtMachine::whereIn('store_id', json_decode($store_ids))
            ->where('status', 1)
            ->exists();

        if ($exist) {
            abort(200, json_encode(['data' => [
                'status' => true,
            ]]));
        }

        abort(200, json_encode(['data' => [
            'status' => false,
        ]]));
    }

    public function killSwitchByStore(Request $request)
    {
        $admin = AdminDetail::where('admin_id', auth()->user()->id)->first();
        $store_ids = $admin->value ?? [];
        $ids = Store::whereIn('id', json_decode($store_ids))->pluck('id')->toArray();

        MtMachine::whereIn('store_id', $ids)
            ->get()
            ->map(function ($machine) {
                $machine->update([
                    'status' => 0,
                ]);

                MtUserForceLogout::dispatch($machine->uuid, 'https://youtube.com', false);
            });

        Store::whereIn('id', $ids)->get()->each(function ($store) {
            SendStoreEvent::dispatch('kill_switch', $store->store_id, [
                'url' => 'https://youtube.com',
                'status' => false,
            ]);
        });

        // Once force quite is done, set admin to be disabled
        auth()->user()->update([
            'disabled' => 1,
        ]);

        abort(200, json_encode(['data' => [
            'status' => true,
        ]]));
    }

    public function killSwitchByStoreOnly(Request $request)
    {
        $userId = auth()->user()->id;
        $validator = Validator::make($request->all(), [
            'store_id' => 'required',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $storeId = $request->store_id;
        Store::where('store_id', $storeId)->update(
            [
                'is_disable' => 1,
            ]
        );

        MtMachine::where('store_id', $storeId)
            ->get()
            ->map(function ($machine) {
                $machine->update([
                    'id_disable' => 1,
                ]);

                MtUserForceLogout::dispatch($machine->uuid, 'https://youtube.com', false);
            });

        SendStoreEvent::dispatch('kill_switch', $storeId, [
            'url' => 'https://youtube.com',
            'status' => false,
        ]);

        $this->addKillSwitchActivityLog('machine-kill-switch', $userId, $storeId, 0);

        abort(200, json_encode(['data' => [
            'status' => true,
        ]]));
    }

    public function killSwitchByAll(Request $request)
    {
        MtMachine::all()->map(function ($machine) {
            $machine->update([
                'status' => 0,
            ]);

            MtUserForceLogout::dispatch($machine->uuid, 'https://youtube.com', false);
        });

        Store::where('status', 1)->get()->each(function ($store) {
            SendStoreEvent::dispatch('kill_switch', $store->store_id, [
                'url' => 'https://youtube.com',
                'status' => false,
            ]);
        });

        abort(200, json_encode(['data' => [
            'status' => true,
        ]]));
    }

    public function restoreMachineByStore(Request $request)
    {
        $userId = auth()->user()->id;
        $validator = Validator::make($request->all(), [
            'store_id' => 'required',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $storeId = $request->store_id;
        MtMachine::where('store_id', $storeId)
            ->get()
            ->map(function ($machine) {
                $machine->update([
                    'status' => 1,
                ]);

                MtForceRefresh::dispatch($machine->uuid, true);
            });

        SendStoreEvent::dispatch('restore_switch', $storeId, [
            'url' => 'https://projector.uwin2u.com?store=' . $storeId,
            'status' => true,
        ]);

        $this->addKillSwitchActivityLog('machine-restore', $userId, $storeId, 1);

        abort(200, 'Machine Refreshed');
    }

    public function forceMachineRefresh(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'machine_uuid' => 'required|exists:mt_machines,uuid,deleted_at,NULL',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if ($request->machine_uuid) {
            MtForceRefresh::dispatch($request->machine_uuid, true);
        }

        abort(200, 'Machine Refreshed');
    }
}
