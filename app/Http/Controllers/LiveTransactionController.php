<?php

namespace App\Http\Controllers;

use App\Models\Deposit;
use App\Models\UserRebate;
use App\Models\Withdrawal;
use App\Traits\DecimalTrait;

class LiveTransactionController extends Controller
{
    /**
     * Get a single transaction for broadcasting
     * Uses a real transaction if available, otherwise generates a random one
     *
     * @return array
     */
    public function getSingleTransaction()
    {
        // 20% chance to try to get a real transaction first
        if (mt_rand(1, 5) == 1) {
            $realTransactions = $this->getRealTransactions(1);
            if (count($realTransactions) > 0) {
                return $realTransactions[0];
            }
        }

        // Otherwise or if no real transaction found, generate a random one
        $randomTransactions = $this->generateRandomTransactions(1);
        return $randomTransactions[0];
    }

    /**
     * Generate completely random transactions (not from DB)
     *
     * @param int $count Number of transactions to return
     * @return array
     */
    private function generateRandomTransactions($count = 5)
    {
        $transactions = [];

        $typeDistribution = [
            'deposit' => 50,     // 50% chance
            'withdrawal' => 30,  // 30% chance
            'rebate' => 20       // 20% chance
        ];

        $names = $this->getRandomNames($count);

        for ($i = 0; $i < $count; $i++) {
            $rand = mt_rand(1, 100);
            if ($rand <= $typeDistribution['deposit']) {
                $type = 'deposit';
            } elseif ($rand <= $typeDistribution['deposit'] + $typeDistribution['withdrawal']) {
                $type = 'withdrawal';
            } else {
                $type = 'rebate';
            }

            // Get a random name
            $username = $names[$i % count($names)];

            switch ($type) {
                case 'deposit':
                    // Deposits tend to be larger
                    $amount = $this->getRandomAmount(100, 10000);
                    break;
                case 'withdrawal':
                    // Withdrawals tend to be medium-sized
                    $amount = $this->getRandomAmount(100, 5000);
                    break;
                case 'rebate':
                    // Rebates tend to be smaller
                    $amount = $this->getRandomAmount(10, 500);
                    break;
                default:
                    $amount = $this->getRandomAmount(100, 5000);
            }

            $transactions[] = [
                'type' => $type,
                'username' => $username,
                'amount' => $amount
            ];
        }

        // Shuffle to randomize the order
        shuffle($transactions);

        return $transactions;
    }

    /**
     * Get a collection of random masked usernames
     *
     * @param int $count Number of names to generate
     * @return array
     */
    private function getRandomNames($count)
    {
        static $firstNames = [
            'John', 'Mary', 'James', 'Patricia', 'Robert', 'Jennifer', 'Michael', 'Linda', 'William', 'Elizabeth',
            'David', 'Susan', 'Richard', 'Jessica', 'Joseph', 'Sarah', 'Thomas', 'Karen', 'Charles', 'Nancy',
            'Wei', 'Li', 'Ming', 'Yan', 'Chen', 'Zhang', 'Liu', 'Wang', 'Hiroshi', 'Yuki',
            'Akira', 'Haruka', 'Jin', 'Mei', 'Seo', 'Ji', 'Min', 'Hyun', 'Raj', 'Priya',
            'Carlos', 'Maria', 'Juan', 'Sofia', 'Luis', 'Ana', 'Mohammed', 'Fatima', 'Ahmed', 'Aisha',
            'Ali', 'Zara', 'Omar', 'Layla', 'Ivan', 'Olga', 'Dmitri', 'Natasha', 'Pierre', 'Celine'
        ];

        static $maskedNames = null;
        if ($maskedNames === null) {
            $maskedNames = [];
            foreach ($firstNames as $name) {
                $maskedNames[] = $this->maskUsername($name);
            }

            for ($i = 0; $i < 30; $i++) {
                $randomName = $firstNames[array_rand($firstNames)] . mt_rand(1, 999);
                $maskedNames[] = $this->maskUsername($randomName);
            }
        }

        if ($count <= count($maskedNames)) {
            return array_slice($maskedNames, 0, $count);
        }

        $result = [];
        while (count($result) < $count) {
            shuffle($maskedNames);
            $result = array_merge($result, $maskedNames);
        }

        return array_slice($result, 0, $count);
    }

    /**
     * Generate a random amount with realistic distribution
     *
     * @param float $min Minimum amount
     * @param float $max Maximum amount
     * @return string Formatted amount
     */
    private function getRandomAmount($min, $max)
    {
        $lambda = 5 / ($max - $min);
        $u = mt_rand() / mt_getrandmax();

        $x = -log(1 - $u) / $lambda;

        $amount = min($min + $x, $max);

        return number_format(round($amount * 100) / 100, 2, '.', '');
    }

    /**
     * Get real transactions from the database
     *
     * @param int $count Number of transactions to return
     * @return array
     */
    private function getRealTransactions($count = 5)
    {
        // Get some real deposits
        $deposits = Deposit::where('status', 1)
            ->with(['user:id,name,username'])
            ->select(['id', 'user_id', 'amount'])
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get()
            ->map(function ($deposit) {
                return [
                    'type' => 'deposit',
                    'username' => $this->maskUsername($deposit->user->name ?? $deposit->user->username ?? 'User'),
                    'amount' => DecimalTrait::setDecimal($deposit->amount)
                ];
            })
            ->values();

        // Get some real withdrawals
        $withdrawals = Withdrawal::where('status', 1)
            ->with(['user:id,name,username'])
            ->select(['id', 'user_id', 'amount'])
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get()
            ->map(function ($withdrawal) {
                return [
                    'type' => 'withdrawal',
                    'username' => $this->maskUsername($withdrawal->user->name ?? $withdrawal->user->username ?? 'User'),
                    'amount' => DecimalTrait::setDecimal($withdrawal->amount)
                ];
            })
            ->values();

        // Get some real rebates
        $rebates = UserRebate::where('status', 1)
            ->with(['user:id,name,username'])
            ->select(['id', 'user_id', 'amount'])
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get()
            ->map(function ($rebate) {
                return [
                    'type' => 'rebate',
                    'username' => $this->maskUsername($rebate->user->name ?? $rebate->user->username ?? 'User'),
                    'amount' => DecimalTrait::setDecimal($rebate->amount)
                ];
            })
            ->values();

        // Combine all transactions
        $allTransactions = collect()
            ->merge($deposits)
            ->merge($withdrawals)
            ->merge($rebates);

        // Shuffle and take random transactions
        return $allTransactions->shuffle()->take($count)->values()->all();
    }

    /**
     * Mask a username for privacy
     *
     * @param string $username
     * @return string
     */
    private function maskUsername($username)
    {
        if (empty($username)) {
            return 'U****r';
        }

        $length = mb_strlen($username);

        if ($length <= 2) {
            return $username . str_repeat('*', 6 - $length);
        } elseif ($length <= 6) {
            $firstChar = mb_substr($username, 0, 1);
            $lastChar = mb_substr($username, -1, 1);
            $maskedPart = str_repeat('*', $length - 2);
            return $firstChar . $maskedPart . $lastChar;
        } else {
            $firstChar = mb_substr($username, 0, 1);
            $lastChar = mb_substr($username, -1, 1);
            return $firstChar . '****' . $lastChar;
        }
    }
}
