<?php

namespace App\Http\Controllers;

use App\Jobs\RandomLogoutWWJ;
use App\Services\GameProvider\JK;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;

class DemoController extends Controller
{
    public function getDemoWWJ(Request $request)
    {
        abort(500, json_encode(
            [
                'error' => 'Game Under Maintenance'
            ]
        ));

        $executed = RateLimiter::attempt(
            'demo:' . $request->ip(),
            $perMinute = 5,
            function () {
                $jk = resolve(JK::class, ['']);
                $res =  $jk->getLoginUrl('fw_' . Str::random(8), 500, 'Dev-01');

                if (!isset($res['status']) || $res['status'] != true) {
                    abort(400, json_encode(['msg' => array('Login Failed')]));
                }

                abort(200, json_encode(
                    [
                        'link' => $res['data']
                    ],
                ));
            }
        );

        if (! $executed) {
            abort(500, json_encode(
                [
                    'error' => 'Too many request! Please wait 30 seconds before trying.'
                ],
            ));
        }
    }

    public function randomLogoutWWJ(Request $request)
    {
        $firstBatchRemoved = [
            '8MIsW310jTvk',
            '1YQaM589wXeg',
            '6JFdO468vWis',
            '4OThQ588dVry',
            '5HWcP555cHcq',
            '4MAiS211mYyc',
            '3IQnY799aPcw',
            '1RDzE716aEkm',
            '3YVsS881wQps',
            '6EHrQ988vAgx',
            '8NKfR372rDrp',
            '3SPaQ474bCca',
            '8QFdU951aYbz',
            '3QHiX710pKuy',
            '0YHyY796oFjv',
            '8VNzR426gZgy',
            '4MAiX721bTsj',
            '1KIgC886rQin',
            '5YIuG827uJog',
            '2RZwO656xCzm',
            '2CFaR776yRmj',
            '1OEuS218kDae',
            '9HSoA720tFct',
            '3TNjF387dHnb',
            '6KJfP844rLyw',
            '7JNjC516sTwy',
            '5FZoO711vHll',
            '6SXuD329eSoo',
            '6NYpK128qRju',
            '3WFdY523eYxo',
            '1BNpE921iWhw',
            '7QReS609aWts',
            '1QQvX080vJzy',
            '7FFeC417uVoo',
            '6UDnO265hFep',
            '7QBdH341lWod',
            '9QLbZ501pAxp',
            '6WHbY849oAnj',
            '3UMeT684sHqd',
            '1AXkW454cAcn'
        ];
        foreach ($firstBatchRemoved as $key => $userId) {
            $delay = \Carbon\Carbon::now()->addSeconds(rand(1, 60000));
            dispatch(new RandomLogoutWWJ('fw_' . $userId, 'funwallet_01'))->delay($delay);
        }
        abort(200, json_encode(
            $firstBatchRemoved,
        ));
    }

    public function getAdminWWJ(Request $request)
    {
        if ($request['p'] != "uwg112") {
            abort(403, json_encode([
                'error' => 'Unauthorized! '
            ]));
        }

        $amount = 0;

        $randomStrings = [
            'fw_demoTPYu30931',
            'fw_demoBezG21541',
            'fw_demobwqE68112',
            'fw_demoDbJB37586',
            'fw_demokSKT89585',
            'fw_demoXTFO04105',
            'fw_demotlXN58125',
            'fw_demoMTdW42610',
            'fw_demozeyz37446',
            'fw_demoYXhR00043',
            'fw_demoAhhY88694',
            'fw_demohFrr32155',
            'fw_demoQHtO08190',
            'fw_demoMRhz00699',
            'fw_demoiVrp99114',
            'fw_demoqpzg14603',
            'fw_demouQsZ38333',
            'fw_demobKeW90570',
            'fw_demockMO06200',
            'fw_demoCpiS74795',
            'fw_demomhRX82946',
            'fw_democRZn59798',
            'fw_demorGgn88487',
            'fw_demoWnrr26189',
            'fw_demomkfJ59411',
            'fw_demolEin34008',
            'fw_demoysjp78146',
            'fw_demogdBN96391',
            'fw_demonuSJ47848',
            'fw_demoNvVT17820',
            'fw_demovaQi75082',
            'fw_demoEumQ18075',
            'fw_demoNCfH81165',
            'fw_demoBXdi63910',
            'fw_demodlvu03938',
            'fw_demoyMnd37082',
            'fw_demojKur44044',
            'fw_demozYBC83125',
            'fw_demokoVn49900',
            'fw_demoTtce79224',
            'fw_demokTUV82205',
            'fw_demowhuf77094',
            'fw_demoEafA68609',
            'fw_demoSnua35495',
            'fw_demoUitO59162',
            'fw_demozRRU51594',
            'fw_demoViGg77393',
            'fw_demojjXj08955',
            'fw_demoIBLP89544',
            'fw_demowJze63551',
            'fw_demoXWlg86985',
            'fw_demoqDRG41085',
            'fw_demoWHzj13847',
            'fw_demoWgxh45042',
            'fw_demoTeXQ45744',
            'fw_demoaVNc03891',
            'fw_demoSEWV80485',
            'fw_demohRFI03594',
            'fw_demomKms43478',
            'fw_demooLGG26405',
            'fw_demoByNv66491',
            'fw_demoRKyp53451',
            'fw_demolTQb64969',
            'fw_demozKXe93353',
            'fw_demoLWoZ10162',
            'fw_demoqrQY74588',
            'fw_demovRqC54167',
            'fw_demoAdvs62034',
            'fw_demoVZJv40389',
            'fw_demoOlPd45352',
            'fw_demoSToo71698',
            'fw_demopmpI83925',
            'fw_demoBIOT25103',
            'fw_demohoMF53978',
            'fw_demoGpPB44656',
            'fw_demoyETh18052',
            'fw_demoxaqd21545',
            'fw_demoHFbQ10278',
            'fw_demolsvs45207',
            'fw_demouODm65027',
            'fw_demoLLWd69988',
            'fw_demoGugz91512',
            'fw_demogoda12950',
            'fw_demozZqe86255',
            'fw_demoWaUV98394',
            'fw_demolAzL73916',
            'fw_demoAJPS66144',
            'fw_demoFKQN91448',
            'fw_demobgKn18433',
            'fw_demouHBT01920',
            'fw_demoitrd25121',
            'fw_democrPx70450',
            'fw_demoLOiR27162',
            'fw_demoFfuV66399',
            'fw_demoAZBZ42483',
            'fw_demotjRw18266',
            'fw_demosokE01350',
            'fw_demovnAE10330',
            'fw_demobJjF87366',
            'fw_demoYblM59732'
        ];

        $jk = resolve(JK::class, ['']);

        // $delay = \Carbon\Carbon::now()->addSeconds(rand(300, 900));
        // dispatch(new RandomLogoutWWJ('fw_' . $randomStrings[$request['u']], 'funwallet_01'))->delay($delay);

        // odd number using funwallet_01 even number using funwallet_02 when $request['u'] % 2 == 0
        $agentId = $request['u'] % 2 == 0 ? 'funwallet_02' : 'funwallet_01';

        $res =  $jk->getLoginUrl('fw_' . $randomStrings[$request['u']], $amount, $agentId);
        if (!isset($res['status']) || $res['status'] != true) {
            abort(400, json_encode(['msg' => array('Login Failed')]));
        }

        abort(200, json_encode(
            [
                'link' => $res['data']
            ],
        ));
    }
}
