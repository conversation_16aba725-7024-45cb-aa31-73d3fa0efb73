<?php

namespace App\Http\Controllers;

use App;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Traits\CurlTrait;
use App\Traits\CacheTrait;

class LangController extends Controller {
    public function __construct(Request $request)
    {
        $pwd = 'ason'.date('id'); // ason[minutes][date]

        if (App::environment(['staging', 'production'])) {
            $validator = Validator::make($request->all(),[
                'secret' => "required|string|in:$pwd",
            ]);

            if($validator->fails()){
                abort(400,json_encode($validator->errors()));
            }
        }
    }

    public function uploadLanguage(Request $request)
    {
        if(!$request->file('langFile')){
            abort(400,'Please upload file.');
        }

        $res = \Maatwebsite\Excel\Facades\Excel::import(new \App\Imports\ImportLanguage,$request->file('langFile'));

        abort(200,json_encode($res));
    }

    public function clearLangCache(Request $request)
    {
        $pwd = 'ason' . date('id'); // ason[minutes][date]
        $validator = Validator::make($request->all(), [
            'secret' => "required|string|in:$pwd",
            'lang_type' => "string|in:lang-,lang-custom-"
        ]);
        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }
        CacheTrait::clearCacheWithoutUid(($validator->validated()['lang_type'] ?? 'lang-'));
        abort(200);
    }
}
