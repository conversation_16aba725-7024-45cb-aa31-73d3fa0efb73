<?php

namespace App\Http\Controllers;

use App\Models\Admin;
use App\Models\SMSLog;
use App\Models\SystemSetting;
use App\Models\User;
use App\Models\UserDeviceInfo;
use App\Services;
use App\Traits\Sms;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class AuthController extends Controller
{
    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = login
     * @module = admin
     * @path = login
     * @method = post
     * @description = Login to system and get bearer token.
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
     * @body = username|<username>|String|required|admin|Admin username.
     * @body = password|<password>|String|required|aA@12345|User password.
     * @response = {"data":{"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************.pqFx4SFw3zlQ_zmvXQSkk1uW-IUZJb48FFUTQ-8coLg","token_type":"bearer","expires_in":3600,"user":{"id":1000000,"member_id":"*********","name":"ghostmate","username":"ghostmate","email":"<EMAIL>","phone_no":"60-*********","sponsor_username":null,"user_type":"user-account","user_type_display":"User Account","status":"active","status_display":"Active","email_verified":0,"transaction_flag":1,"no_transaction":1,"admin_reset_transaction":0,"kyc_flag":1,"created_at":"2022-09-07 07:45:18","updated_at":"2022-10-27 05:49:18","activated_at":false,"current_rank":null,"current_rank_display":null,"set_rank":"system","currency_code":"MYR","pending_activate_email":null},"force_reinsert":null,"memo":[],"permission":[]},"status":true,"timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"3dc7b180-74e6-4e78-9673-2fd9fe2d0d1d"}
     * ##docs end##
     */
    public function adminLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string',
            'password' => [
                'required',
                'string',
                Password::min(8)->letters()->numbers()->mixedCase(),
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $login = Services\AuthService::login($validator->validated(), 'admin');

        abort(200, json_encode($login));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = login
     * @module = app
     * @path = login
     * @method = post
     * @description = Login to system and get bearer token.
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
     * @body = username|<username>|String|required|superdog|User name.
     * @body = password|<password>|String|required|211222|User password.
     * @body = step|<step>|string|required|1,2|Step 1: Login detail, Step 2: Enter OTP
     * @body = uuid|<uuid>|string|optional|abcdfge12355|user's Device uuid.
     * @body = type|<type>|string|optional|abcdfge12355|user's Device type.
     * @body = device_info|<device_info>|json|optional|{}|user's Device info.
     * @response = {"data":{"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************.NvsZpMSFcz73E0gObdlv1TIqiYy1NyR1J3vqgqOQbyo","token_type":"bearer","expires_in":********,"user":{"id":1000009,"member_id":"*********","name":"super","username":"super","ic_no":null,"dob":null,"gender":null,"gender_id":null,"race":null,"race_id":null,"email":null,"phone_no":"60-*********","user_type":"user-account","user_type_display":"User Account","country":"malaysia","country_display":"Malaysia","referral_member_id":"*********","referral_name":"test","referral_phone_no":"60-**********","status":"active","status_display":"Active","created_at":"05\/09\/2024 11:56:29","activated_at":false,"currency_code":"MYR","profile_picture":null,"profile_picture_path":null,"d_token":null,"store":{"id":7,"store_id":2,"store_name":"test"}},"is_dashboard_v2":0,"permission":[]},"status":true,"timezone":"Asia\/Kuala_Lumpur","environment":"dev","execution_duration":"0.************** sec","log_id":"2d9ef57c-793b-4bc1-ab32-f4009f144800","valid_version":false}
     * ##docs end##
     */
    public function memberAppLogin(Request $request)
    {
        if (isset($request->password)) {
            $request->merge(['password' => strtoupper($request->password)]);
        }
        if (isset($request->password_confirmation)) {
            $request->merge(['password_confirmation' => strtoupper($request->password_confirmation)]);
        }

        $lang = $request->header('lang') ?? 'en';
        $phone_no = null;
        $otpType = null;

        $isDashboardV2 = false;
        if (defined('MODULE') && MODULE == 'app') {
            if (! empty($request->username)) {
                $user = User::select('username', 'id')->where('username', $request->username)->first();
            } else {
                $user = User::select('username', 'id')->where('phone_no', $request->phone_no)->first();
            }
            $username = $user['username'] ?? null;
            $userId = $user['id'] ?? null;
            $sysSetting = SystemSetting::whereIn('name', ['v2AuthorizedUser', 'fixOTP'])->whereNull('deleted_at')->get()->pluck('value', 'name');
            $v2Users = json_decode($sysSetting['v2AuthorizedUser'] ?? '') ?? [];
            $fixOTP = $sysSetting['fixOTP'];
            if (in_array($username ?? '', $v2Users)) {
                $isDashboardV2 = true;
            }

            if ((isset($request->device_info))) {
                $request->merge(['device_info' => json_encode($request->device_info)]);

                // process device_info
                $deviceInfo = json_decode($request->device_info);
                if ($request->type == 'iOS') {
                    $name = $deviceInfo->name ?? null;
                    $brand = $deviceInfo->name ?? null;
                    $model = $deviceInfo->device_product_name ?? null;
                } else {
                    $name = $deviceInfo->name ?? null;
                    $brand = $deviceInfo->brand ?? null;
                    $model = $deviceInfo->model ?? null;
                }
                $request->merge(['device_brand' => $brand, 'device_model' => $model, 'device_name' => $name]);
                $request->merge(['user_id' => $userId]);
            }
        }

        if (isset($request->phone_no)) {
            $replace = str_replace(' ', '', $request->phone_no);
            $request->merge(['phone_no' => $replace]);
        }
        $user = null;
        $validator = Validator::make($request->all(), [
            'username' => [
                'string',
                Rule::when(empty($request->login_token), function ($q) {
                    return $q = 'required';
                }),
            ],
            // 'phone_no' => [
            //     'string',
            //     // 'exists:users,phone_no',
            //     Rule::when((empty($request->login_token)),function($q){
            //         return $q = 'required';
            //     }),
            //     function ($q, $value, $fail) use ($request, &$user) {
            //         $user = User::where('user_type', User::$userType['user-account'])->where('phone_no',$value)->whereNull('deleted_at')->first();
            //         if(empty($user)){
            //             $fail(Lang::get("lang.phone-no-not-exsit"));
            //             return;
            //         }
            //     }
            // ],
            // 'otp_code' => [
            //     'nullable',
            //     'string',
            //     Rule::when((empty($request->login_token) && $request->step == 2),function($q){
            //         return $q = 'required';
            //     }),
            //     function ($q, $value, $fail) use ($request,&$otpType, $isDashboardV2, $fixOTP) {
            //         if(empty($request->login_token) && $request->step == 2) {
            //             $phone_no = $request->phone_no;

            //             $otpDetail = Sms::verifyOTPCode(['type'=>'login-otp','otp_code'=>$value,'phone'=>$phone_no]);
            //             if ($isDashboardV2 == true) {
            //                 if ($value != $fixOTP) {
            //                     $fail(__('lang.invalid-otp'));
            //                     return;
            //                 }
            //                 $otpDetail = true;
            //             }

            //             if(empty($otpDetail)){
            //                 $fail(__('lang.invalid-otp'));
            //                 return;
            //             }
            //             $otpType = $otpDetail->type ?? Null;
            //         }
            //     },
            // ],
            'step' => [
                'integer',
                'in:1,2',
                Rule::when((empty($request->login_token)), function ($q) {
                    return $q = 'required';
                }),
            ],
            'login_token' => 'string',
            'user_id' => 'nullable',
            'uuid' => 'required_without:login_token|string',
            'type' => 'nullable|string',
            'password' => [
                'string',
                Rule::when(empty($request->login_token), function ($q) {
                    return $q = 'required';
                }),
            ],
            'firebase_fwc_token' => 'nullable|string',
            'device_brand' => 'nullable|string',
            'device_model' => 'nullable|string',
            'device_name' => 'nullable|string',
            'device_info' => [
                'required_without:login_token',
                'json',
                function ($q, $value, $fail) use ($request, &$user, $isDashboardV2) {
                    if (! empty($request->user_id) && isset($request->device_info)) {
                        // $params = [
                        //     'user_id' => $request->user_id,
                        //     'uuid' => $request->uuid,
                        // ];
                        // $valid = UserDeviceInfo::checkValidDeviceToLogin($params);
                        $valid = true;
                        if ($isDashboardV2) {
                            $valid = true;
                        }
                        if (! $valid) {
                            $fail(Lang::get('lang.login-user-device-not-match'));
                        }
                    }
                },
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        // Send OTP
        $step = $validator->validated()['step'] ?? null;
        // $phone_no = $validator->validated()['phone_no'] ?? null;

        // if($step == 1 && empty($request->login_token)) {
        //     $sendParams = [
        //         'phone_no' => $phone_no,
        //         'otp_type' => 'login-otp',
        //         'lang' => $lang,
        //         'isDashboardV2' => $isDashboardV2
        //     ];
        //     Sms::sendOTPCode($sendParams);
        // }

        if ($step == 2 || $request->login_token) {
            $login = Services\AuthService::login($validator->validated(), 'users');

            // if(isset($otpType)){
            //     SMSLog::where(['type' => $otpType, 'user_id' => $user->id])->delete();
            // }
            UserDeviceInfo::insertUserDInfo($validator->validated());

            abort(200, json_encode($login));
        }

        abort(200);
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = login
     * @module = user
     * @path = login
     * @method = post
     * @description = Login to system and get bearer token.
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
     * @body = username|<username>|String|required|<EMAIL>|User email address.
     * @body = password|<password>|String|required|aA@12345|User password.
     * @response = {"data":{"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************.9ecEsOcMMGMyEJu0Q4XqT5XSYgNcbFrE2ZMhkZM2OeY","token_type":"bearer","expires_in":3600,"user":{"id":1000000,"member_id":"*********","name":"ghostmate","username":"ghostmate","ic_no":null,"dob":null,"gender":null,"gender_id":null,"race":null,"race_id":null,"email":"<EMAIL>","phone_no":"60-*********","user_type":"user-account","user_type_display":"User Account","status":"active","status_display":"Active","created_at":"18\/04\/2023 09:03:34","activated_at":false,"currency_code":"MYR","profile_picture":null,"profile_picture_path":null},"memo":[],"permission":[]},"status":true,"timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"435ec6c7-9ee7-4f3f-871b-122d7b629792"}
     * ##docs end##
     */
    public function memberLogin(Request $request)
    {
        if (isset($request->password)) {
            $request->merge(['password' => strtoupper($request->password)]);
        }
        if (isset($request->password_confirmation)) {
            $request->merge(['password_confirmation' => strtoupper($request->password_confirmation)]);
        }

        $validator = Validator::make($request->all(), [
            'username' => [
                'string',
                Rule::when(empty($request->login_token), function ($q) {
                    return $q = 'required';
                }),
            ],
            'password' => [
                'string',
                'between:'.(config('users.minPasswordLength') ?? 8).','.(config('users.maxPasswordLength') ?? 20),
                // Password::min(8)->letters()->numbers()->mixedCase(),
                Rule::when(empty($request->login_token), function ($q) {
                    return $q = 'required';
                }),
            ],
            'login_token' => 'string',
        ], [
            'password' => Lang::get('lang.password-field-error'),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $login = Services\AuthService::login($validator->validated(), 'users');

        abort(200, json_encode($login));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = logout
     * @module = admin
     * @path = logout
     * @method = post
     * @description = To logout.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @response = {"status":true,"message":"Granted","timezone":"Asia\/Kuala_Lumpur","environment":"development","execution_duration":"0.016417026519775 sec","log_id":"6376439e-5cf1-4e08-9e28-55e8bba1f280"}
     * ##docs end##
     */
    public function adminLogout(Request $request)
    {
        $logout = Services\AuthService::logout('admin');
        abort(200);
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = logout
     * @module = user
     * @path = logout
     * @method = post
     * @description = To logout.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @response = {"status":true,"message":"Granted","timezone":"Asia\/Kuala_Lumpur","environment":"development","execution_duration":"0.016417026519775 sec","log_id":"6376439e-5cf1-4e08-9e28-55e8bba1f280"}
     * ##docs end##
     */
    public function memberLogout(Request $request)
    {
        $logout = Services\AuthService::logout('users');
        abort(200, lang::get('lang.logout'));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = change-pwd
     * @module = admin
     * @path = change-pwd
     * @method = post
     * @description = To change password.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = current_password|<current_password>|string|required|password|User Original Password
     * @body = password|<password>|string|required|new_password|User New Password
     * @body = password_confirmation|<password_confirmation>|string|required|new_password|User New Password Confirmation

     * @response = {"status":true,"message":"Password changed successful.","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZweHUuY29tL2NoYW5nZS1wd2QiLCJpYXQiOjE2NjE0MTgyMDYsImV4cCI6MTY2MTQyMTgwNiwibmJmIjoxNjYxNDE4MjA2LCJqdGkiOiJMYUdJbk5HZ2VVTnlHNjNuIiwic3ViIjoiMiIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.AXx2ae_h5NhFtSlKXWkWJHUAWzbpTYwIrKqMwiHAsVg","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"development","execution_duration":"0.011609077453613 sec","log_id":"845d3439-d6b5-4a8d-8778-e9db09c41dd3"}
     * ##docs end##
     */
    public function adminChangePassword(Request $request)
    {
        $params = $request->all();

        $validator = Validator::make($params, [
            'current_password' => 'required|filled|different:password',
            'password' => self::passwordRule(['required']),
        ], [
            'password.confirmed' => null,
            'password' => Lang::get('lang.password-field-error'),
        ]);
        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $credentials = ['username' => auth()->user()->username, 'password' => $request->get('current_password')];

        if (! $token = auth('admin')->attempt($credentials)) {
            $errMsg['current_password'][] = __('lang.invalid-credentials');
        }

        if (isset($errMsg)) {
            $errReturn = $errMsg;
            if (isset($errData)) {
                $errReturn = ['data' => $errData, 'message' => $errMsg];
            }
            abort(400, json_encode($errReturn));
        }

        $params['user_id'] = auth()->user()->id;
        Admin::changePassword($params);

        abort(200, Lang::get('lang.change-pwd-success'));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = change-pwd
     * @module = users
     * @path = change-pwd
     * @method = post
     * @description = To change password.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = current_password|<current_password>|string|required|password|User Original Password
     * @body = password|<password>|string|required|new_password|User New Password
     * @body = password_confirmation|<password_confirmation>|string|required|new_password|User New Password Confirmation

     * @response = {"status":true,"message":"Password changed successful.","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLmZhY3N4LmNvbS9jaGFuZ2UtcHdkIiwiaWF0IjoxNjYyNTM3OTIxLCJleHAiOjE2NjI1NDE1MjEsIm5iZiI6MTY2MjUzNzkyMSwianRpIjoiT25UZWlEdnFZSDZWMllDZiIsInN1YiI6IjEiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.VoOkBEi8v6MFGV6vhUBXaS6y_lzgnHCL5j-4Dld-wDY","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"development","execution_duration":"0.0099930763244629 sec","log_id":"ef2698ee-3b8e-4ec1-b697-698790c279e6"}
     * ##docs end##
     */
    public function memberChangePassword(Request $request)
    {
        $params = $request->all();

        $validator = Validator::make($params, [
            'current_password' => 'required|filled|different:password',
            'password' => self::passwordRule(['required', 'between:'.(config('users.minPasswordLength') ?? 8).','.(config('users.maxPasswordLength') ?? 20)]),
        ], [
            'password.confirmed' => null,
            'password' => Lang::get('lang.password-field-error'),
        ]);
        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $credentials = ['username' => auth()->user()->username, 'password' => $request->get('current_password')];

        if (! $token = auth('users')->attempt($credentials)) {
            $errMsg['current_password'][] = __('lang.invalid-credentials');
        }

        if (isset($errMsg)) {
            $errReturn = $errMsg;
            if (isset($errData)) {
                $errReturn = ['data' => $errData, 'message' => $errMsg];
            }
            abort(400, json_encode($errReturn));
        }

        $params['user_id'] = auth()->user()->id;
        User::changePassword($params);

        abort(200, Lang::get('lang.change-pwd-success'));
    }
}
