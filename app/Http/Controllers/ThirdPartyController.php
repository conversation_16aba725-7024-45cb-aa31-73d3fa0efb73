<?php

namespace App\Http\Controllers;

use App;
use App\Models\Country;
use App\Models\SMSLog;
use App\Models\SystemSetting;
use App\Models\User;
use App\Models\UserAddress;
use App\Models\UserBank;
use App\Models\UserDetail;
use App\Services;
use App\Models;
use App\Models\CreditTransaction;
use App\Models\Currencies;
use App\Models\Currency;
use App\Models\TreeSponsor;
use App\Models\UserFavourite;
use App\Models\UserNotification;
use App\Traits\ThirdPartyTrait;
use App\Traits\DecimalTrait;
use App\Traits\Sms;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Traits;

use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use <PERSON><PERSON>an\Location\Facades\Location;

class ThirdPartyController extends Controller
{
    public function __construct(Request $request)
    {
        $data = ThirdPartyTrait::getDataFromPath($request->path());

        $millis = $request->millis; // current UNIX timestamp;
        $secretKey = $data['key'] ?? null; 
        $secret = sha1($millis.$secretKey);
        // p($secret);

        if (App::environment(['local','staging', 'production'])) {
            $validator = Validator::make($request->all(),[
                'secret' => "required_without:token|string|in:$secret",
                'token' => "required_without:secret|string|in:$secret",
            ]);

            if($validator->fails()){
                abort(400,json_encode($validator->errors()));
            }
        }
    }
}