<?php

namespace App\Http\Controllers;

use App\Http\Resources\ItemsCollection;
use App\Models\Transfer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Traits\DateTrait;
use App\Traits\DecimalTrait;
use Illuminate\Support\Facades\Lang;

class TransferController extends Controller
{
    public function getAllTransferList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'integer',
            'limit' => 'integer',
            'order_by' => 'string',
            'order_sort' => 'string',
            'transaction_id' => 'nullable|string',
            'store_id' => 'nullable|integer',
            'from_username' => 'nullable|string',
            'to_username' => 'nullable|string',
            'status' => 'nullable|string',
            "from_date" => "nullable|date|date_format:Y-m-d",
            "to_date" => "nullable|date|date_format:Y-m-d|after_or_equal:from_date",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();

        $transactionId = $params['transaction_id'] ?? null;
        $storeId = $params['store_id'] ?? null;
        $fromUsername = $params['from_username'] ?? null;
        $toUsername = $params['to_username'] ?? null;
        $fromDate = $params['from_date'] ?? Null;
        $toDate = $params['to_date'] ?? null;
        $status = isset($params['status']) ? Transfer::$status[$params['status']] : null;
        $seeAll = $params['see_all'] ?? null;

        $sortableColumns = [];

        $tableTotal['amount'] = 0;

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        if (isset($params['export']) && ($params['export'] == 1)) {
            $jobData = $params;
            $jobData["model"] = get_class() ?? null;
            $jobData["function"] = __FUNCTION__;
            $jobData["module"] = MODULE;
            $jobData["creator_type"] = MODULE;
            $jobData["creator_id"] = Auth::user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, "Export Successfully");
        }

        $items = Transfer::query()
            ->with('fromUser', 'toUser', 'creditTransaction.userDetail.store')
            ->when($fromUsername, function ($query) use ($fromUsername) {
                return $query->whereRelation('fromUser', 'username', 'like', '%' . $fromUsername . '%');
            })
            ->when($toUsername, function ($query) use ($toUsername) {
                return $query->whereRelation('toUser', 'username', 'like', '%' . $toUsername . '%');
            })
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->whereDate('created_at', ">=", $fromDate);
                return $q->whereDate('created_at', "<=", $toDate);
            })
            ->when(isset($transactionId), function ($query) use ($transactionId) {
                return $query->where('transaction_id', $transactionId);
            })
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('creditTransaction.userDetail.store', 'id', $storeId);
            })
            ->when(isset($status), function ($query) use ($status) {
                return $query->where('status', $status);
            })
            ->orderBy($order_by, $order_sort)
            ->paginate($limit);

        $mapFunc = function ($q) use (&$tableTotal) {
            $status = array_search($q->status, Transfer::$status);

            $res = [
                'transaction_id' => $q->transaction_id,
                'created_at' => DateTrait::dateFormat($q->created_at),
                'from_username' => $q->fromUser->username ?? null,
                'to_username' => $q->toUser->username ?? null,
                'amount' => DecimalTrait::setDecimal($q->amount),
                'status' => $status,
                'status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                'store_name' => $q->creditTransaction->userDetail->store->name ?? null,
            ];

            $tableTotal['amount'] = DecimalTrait::setDecimal($tableTotal['amount']) + $q->amount;

            return (object) $res;
        };

        if (($seeAll == 1)) {
            $data = $items->get()->map($mapFunc)->toArray();
        } else {
            $items->getCollection()->transform($mapFunc);
            $data = new ItemsCollection($items);
        }

        abort(200, json_encode([
                'data' => $data,
            ]));
    }
}
