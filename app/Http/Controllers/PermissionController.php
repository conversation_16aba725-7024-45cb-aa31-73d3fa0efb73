<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Permissions;
use Illuminate\Support\Facades\Validator;
use DB;
use Illuminate\Support\Facades\Lang;

class PermissionController extends Controller
{
    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = permission/list
     * @module = admin
     * @path = permission/list
     * @permissionName = Permission List
     * @menuType = sub_menu
     * @parent = Permission
     * @method = POST
     * @description = To get admin permissions list.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @response = {"data":[{"id":1,"name":"perm-admin","display":"Admin Module","parent_id":0,"level":1},{"id":2,"name":"perm-admin-create","display":"Admin Create","parent_id":1,"level":2},{"id":3,"name":"perm-admin-list","display":"Admin List","parent_id":1,"level":2},{"id":4,"name":"perm-admin-detail","display":"Admin Detail","parent_id":1,"level":2},{"id":5,"name":"perm-admin-edit","display":"Admin Edit","parent_id":1,"level":2},{"id":6,"name":"perm-fingerprint","display":"Fingerprint Module","parent_id":0,"level":1},{"id":7,"name":"perm-fingerprint-list","display":"Fingerprint List","parent_id":6,"level":2},{"id":8,"name":"perm-currency","display":"Currency Module","parent_id":0,"level":1},{"id":9,"name":"perm-currency-setting-edit","display":"Currency Setting Edit","parent_id":8,"level":2},{"id":10,"name":"perm-currency-setting-history","display":"Currency Setting History","parent_id":8,"level":2},{"id":11,"name":"perm-rate-history","display":"Currency Rate History","parent_id":8,"level":2},{"id":12,"name":"perm-permission","display":"Permission Module","parent_id":0,"level":1},{"id":13,"name":"perm-permission-list","display":"Permission List","parent_id":12,"level":2},{"id":14,"name":"perm-activity-log","display":"Activity Log Module","parent_id":0,"level":1},{"id":15,"name":"perm-activity-log-list","display":"Activity Log List","parent_id":14,"level":2}],"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZhY3N4LmNvbS9wZXJtaXNzaW9uL2xpc3QiLCJpYXQiOjE2NjI1Mzc1MTEsImV4cCI6MTY2MjU0MTQyNiwibmJmIjoxNjYyNTM3ODI2LCJqdGkiOiJzOEVtV0RQbHBlUDFkeDV1Iiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.w0FKzTGCpyDfYEfHpbDHeUA4L4OHg1beZH_s5ya_hyI","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"locals","execution_duration":"0.0057988166809082 sec","log_id":"9c64c4db-644e-42ee-9610-3b8941680d8b"}
     * ##docs end##
     */
    public function get(Request $request){
        $validator = Validator::make($request->all(), [
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $permissionsList = Permissions::getList($validator->validated());
        $data["data"] = $permissionsList;
        abort(200, json_encode($data));
    }
}
