<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Fingerprint;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

use Illuminate\Support\Facades\Validator;
use DB;
use Illuminate\Support\Facades\Lang;

class FingerprintController extends Controller {

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = fingerprint/list
     * @module = admin
     * @path = fingerprint/list
     * @method = post
     * @description = To get fingerprint list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
    
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.

     * @response = {"data":{"list":[{"ip_address":"127.0.0.1","browser":"Chrome","browser_version":"*********","os_platform":"mac","user_agent":"Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","device":"Desktop","created_at":"2022-08-29T04:06:18.000000Z"}],"pagination":{"current_page":1,"first_page_url":"http:\/\/local-api-admin.fpxu.com\/fingerprint\/list?page=1","from":1,"last_page":5,"last_page_url":"http:\/\/local-api-admin.fpxu.com\/fingerprint\/list?page=5","next_page_url":"http:\/\/local-api-admin.fpxu.com\/fingerprint\/list?page=2","path":"http:\/\/local-api-admin.fpxu.com\/fingerprint\/list","per_page":1,"prev_page_url":null,"to":1,"total":5},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZweHUuY29tL2ZpbmdlcnByaW50L2xpc3QiLCJpYXQiOjE2NjE3NDczOTQsImV4cCI6MTY2MTc1MDk5NSwibmJmIjoxNjYxNzQ3Mzk1LCJqdGkiOiJuMlo0b1ZQenF6MmRKc2dEIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.dW3CujUIbIfdgY20FmeOvAS8uNu0zcb33R_Z5pTmDuo","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.06017804145813 sec","log_id":"10b59609-4e50-45b3-b03f-696c3bf26ef0"}
     * ##docs end##
     */

    public function get(Request $request)
    {           
        $validator = Validator::make($request->all(), [
            "order_sort" => "string|in:asc,desc",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $list = Fingerprint::getList($request->all());
        $data["data"] = $list;
        abort(200, json_encode($data));
    }
}
