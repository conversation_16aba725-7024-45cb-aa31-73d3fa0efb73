<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Lang;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use App\Models;

class AnnouncementBarController extends Controller
{
    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = announcement-bar/add
     * @module = admin
     * @path = announcement-bar/add
     * @permissionName = Add Announcement Bar
     * @menuType = api
     * @parent = Announcement Bar
     * @method = POST
     * @description = To add announcement bar.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = announcement_bar_data|<array>|array|required|[{"subject":"TEST Announcement Bar 1","language_type":"en","description":"TEST Description","upload":{"upload_name":"image1.jpg","upload_type":"image\/jpg"}}]|Announcement Bar's Data. 
     * @body = announcement_bar_data.*.subject|<subject>|string|required|test subject|Announcement Bar's Subject.
     * @body = announcement_bar_data.*.description|<description>|string|required|test description|Announcement Bar's Description.
     * @body = announcement_bar_data.*.language_type|<language_type>|string|required|en, cn, my|Announcement Bar's Language.
     * 
     * @body = valid_country|<valid_country>|array|required|[1,2,3,4]|Announcement Bar's Valid Contry.
     * @body = valid_country.*|<country_id>|integer|required|1|Announcement Bar's Valid Contry Id.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Announcement Bar's activate date.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Announcement Bar's activate date.
     * 
     * @body = announcement_bar_url|<announcement_bar_url>|array|optional|{"url":"www.google.com","internal_flag":"0"}|Announcement Bar's Url setting.
     * @body = announcement_bar_url.url|<url>|string|required|www.google.com / login.php|Announcement Bar's redirect url.
     * @body = announcement_bar_url.internal_flag|<internal_flag>|integer|required|1/0|Announcement Bar's url type flag.
     * 
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20vYW5ub3VuY2VtZW50LWJhci9hZGQiLCJpYXQiOjE2NjQ3NzA0MTIsImV4cCI6MTY2NDc3NDM4NywibmJmIjoxNjY0NzcwNzg3LCJqdGkiOiJ1V0lnSjdLQWdCUUtPTlhFIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.bqUOkQFq4bnr34JmQjTer26GKx4oXNUns7Cl3O8gNa4","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.0048220157623291 sec","log_id":"d0dbde71-638f-4c59-9a36-dc9f8a394027"}
     * ##docs end##
     */
    public function addAnnouncementBar(Request $request){

        $validator = Validator::make($request->all(), [
            "announcement_bar_data" => "required|array",
            "announcement_bar_data.*.language_type" => "required|string|distinct|in:".implode(',',array_keys(config('language'))),
            "announcement_bar_data.*.subject" => "required|string",
            "announcement_bar_data.*.description" => "required|string",

            "valid_country" => "required|array",
            "valid_country.*" => "required|integer|exists:country,id,status,1",

            "announcement_bar_url" => "array",
            "announcement_bar_url.url" => "required_with:announcement_bar_url|string",
            "announcement_bar_url.internal_flag" => "required_with:announcement_bar_url|integer|in:1,0",

            "start_date" => "required_with:end_date|string|date_format:Y-m-d|after_or_equal:today",
            "end_date" => "required_with:start_date|string|date_format:Y-m-d|after_or_equal:start_date",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\AnnouncementBar::add($validator->validated());

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = announcement-bar/list
     * @module = admin
     * @path = announcement-bar/list
     * @permissionName = Announcement Bar
     * @menuType = sub_menu
     * @parent = Document
     * @method = POST
     * @description = To get announcement bar list.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.

     * @response = {"list":[{"id":7,"subject":"bar 1","description":"bar description","start_date":"2022-10-01 00:00:00","end_date":"2022-10-02 00:00:00","status":"active","creator":"admin","created_at":"2022-10-03 12:21:15","updated_at":"2022-10-03 12:21:15"},{"id":6,"subject":"bar 1","description":"bar description","start_date":"2022-10-01 00:00:00","end_date":"2022-10-02 00:00:00","status":"active","creator":"admin","created_at":"2022-10-03 12:19:47","updated_at":"2022-10-03 12:19:47"},{"id":5,"subject":"TEST Annoucment 123","description":"TEST Description","start_date":"2022-10-01 00:00:00","end_date":"2022-10-02 00:00:00","status":"inactive","creator":"admin","created_at":"2022-09-30 17:41:18","updated_at":"2022-10-03 10:35:11"}],"pagination":{"current_page":1,"from":1,"last_page":1,"path":"http:\/\/local-api-admin.martin.com\/announcement-bar\/list","per_page":30,"to":3,"total":3},"meta":null,"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20vYW5ub3VuY2VtZW50LWJhci9saXN0IiwiaWF0IjoxNjY0NzcwNDEyLCJleHAiOjE2NjQ3NzQ1NTgsIm5iZiI6MTY2NDc3MDk1OCwianRpIjoiZm1HNkt6ZjVyanhpV0NNSiIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.xiQxDfiJxa1UOUJD06pg_f-ytrfY5_CXbVrbDBr5kGY","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.0053589344024658 sec","log_id":"c6d013f4-46a4-4d7c-81df-d918a3c63411"}
     * ##docs end##
     */
    public function getAnnouncementBarList(Request $request){

        $validator = Validator::make($request->all(), [
            "limit" => "integer",
            "page" => "integer",
            "from_date" => "required_with:to_date|string|date_format:Y-m-d",
            "to_date" => "required_with:from_date|string|date_format:Y-m-d|after_or_equal:from_date",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\AnnouncementBar::getList($validator->validated());

        abort(200,json_encode($res));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = announcement-bar/get
     * @module = admin
     * @path = announcement-bar/get
     * @permissionName = Announcement Bar Detail
     * @menuType = api
     * @parent = Announcement Bar
     * @method = POST
     * @description = To get announcement detail.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|required|1|Announcement Bar's id.

     * @response = {"data":{"id":7,"start_date":"2022-10-01 00:00:00","end_date":"2022-10-02 00:00:00","status":"active","status_display":"Active","announcement_bar_data":[{"subject":"bar 1","description":"bar description","language_type":"en","language_type_display":"English"},{"subject":"bar 3 MY","description":"bar description MY","language_type":"my","language_type_display":"Malay"}],"valid-country":[129,232]},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20vYW5ub3VuY2VtZW50LWJhci9nZXQiLCJpYXQiOjE2NjQ3NzA0MTIsImV4cCI6MTY2NDc3NDk2NSwibmJmIjoxNjY0NzcxMzY1LCJqdGkiOiJKazdnN2JJVllnbGx1WXN0Iiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.ExS0d_7I0brvCI4iTr5-keAF9wtSBwcwB1GRE4GTtN0","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.0051238536834717 sec","log_id":"d861f70d-1e1e-4d2d-b953-d3d5d2b67d96"}
     * ##docs end##
     */
    public function getAnnouncementBarDetail(Request $request){

        $validator = Validator::make($request->all(), [
            "id" => "integer|required|exists:announcement_bar,id",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\AnnouncementBar::getDetail($validator->validated());

        abort(200,json_encode($res));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = announcement-bar/edit
     * @module = admin
     * @path = announcement-bar/edit
     * @permissionName = Edit Announcement Bar
     * @menuType = api
     * @parent = Announcement Bar
     * @method = POST
     * @description = To edit announcement bar.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|required|1|Announcement Bar's id.
     * @body = announcement_bar_data|<array>|array|required|[{"subject":"TEST Announcement Bar 1","language_type":"en","description":"TEST Description","upload":[{"upload_name":"image1.jpg","upload_type":"image\/jpg"}]}]|Announcement Bar's Data. 
     * @body = announcement_bar_data.*.subject|<subject>|string|required|test subject|Announcement Bar's Subject.
     * @body = announcement_bar_data.*.description|<description>|string|required|test description|Announcement Bar's Description.
     * @body = announcement_bar_data.*.language_type|<language_type>|string|required|en, cn, my|Announcement Bar's Language.
     * @body = valid_country|<valid_country>|array|required|[1,2,3,4]|Announcement Bar's Valid Contry.
     * @body = valid_country.*|<country_id>|integer|required|1|Announcement Bar's Valid Contry Id.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Announcement Bar's activate date.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Announcement Bar's activate date.
     * @body = status|<active / inactive>|string|optional|active|Announcement Bar's status.
     *  
     * @body = announcement_bar_url|<announcement_bar_url>|array|optional|{"url":"www.google.com","internal_flag":"0"}|Announcement Bar's Url setting.
     * @body = announcement_bar_url.url|<url>|string|required|www.google.com / login.php|Announcement Bar's redirect url.
     * @body = announcement_bar_url.internal_flag|<internal_flag>|integer|required|1/0|Announcement Bar's url type flag.
     * 
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20vZG9jdW1lbnQvYWRkLW1lbW8iLCJpYXQiOjE2NjQxNjY1NjcsImV4cCI6MTY2NDE4NDYyMywibmJmIjoxNjY0MTgxMDIzLCJqdGkiOiI1RUpyajNkNldZVFNURE9MIiwic3ViIjoiMiIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.VrXHrJ6x6cOB03nZK8YGxuAy_VfAfnqyZHmkI2lqCvU","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.0040340423583984 sec","log_id":"7518ad4b-dc0c-484b-9a47-5e2893e28256"}
     * ##docs end##
     */
    public function editAnnouncementBar(Request $request){

        $validator = Validator::make($request->all(), [
            "id" => "required|integer|exists:announcement_bar,id",
            "announcement_bar_data" => "required|array",
            "announcement_bar_data.*.language_type" => "required|string|distinct|in:".implode(',',array_keys(config('language'))),
            "announcement_bar_data.*.subject" => "required|string",
            "announcement_bar_data.*.description" => "required|string",

            "valid_country" => "required|array",
            "valid_country.*" => "required|integer|exists:country,id,status,1",

            "announcement_bar_url" => "array",
            "announcement_bar_url.url" => "required_with:announcement_bar_url|string",
            "announcement_bar_url.internal_flag" => "required_with:announcement_bar_url|integer|in:1,0",

            "start_date" => "required_with:end_date|string|date_format:Y-m-d|after_or_equal:today",
            "end_date" => "required_with:start_date|string|date_format:Y-m-d|after_or_equal:start_date",
            "status" => "required|string|in:".implode(",",array_keys(Models\AnnouncementBar::$status))
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\AnnouncementBar::edit($validator->validated());

        abort(200);
    }
}
