<?php

namespace App\Http\Controllers;

use App\Http\Resources\ItemsCollection;
use App\Models\UserRebate;
use App\Traits\DateTrait;
use App\Traits\DecimalTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class UserRebateController extends Controller
{
    public function getUserRebates(Request $request)
    {
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => Auth::user()->id]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'integer|required|exists:users,id',
            'from_date' => 'nullable|required_with:start_date|string|date_format:Y-m-d',
            'to_date' => 'nullable|required_with:end_date|string|date_format:Y-m-d|after_or_equal:from_date',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data = UserRebate::getRebatesByUserId($request->user_id, $request->from_date, $request->to_date);

        abort(200, json_encode(['data' => $data]));
    }

    public function requestRebate(Request $request)
    {
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => Auth::user()->id]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'integer|required|exists:users,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data = UserRebate::requestRebate($request->user_id);

        abort(200, json_encode(['data' => $data]));
    }

    public function getUserRebateList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'integer',
            'limit' => 'integer',
            'order_by' => 'string',
            'order_sort' => 'string',
            'member_id' => 'nullable|integer',
            'username' => 'nullable|string',
            'phone_no' => 'nullable|string',
            'store_id' => 'nullable|integer',
            'withdrawal_reference' => 'nullable|string',
            'status' => 'nullable|string',
            "from_date" => "nullable|date|date_format:Y-m-d",
            "to_date" => "nullable|date|date_format:Y-m-d|after_or_equal:from_date",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();

        $fromDate = $params['from_date'] ?? null;
        $toDate = $params['to_date'] ?? null;
        $updatedFrom = $params['updated_from'] ?? null;
        $updatedTo = $params['updated_to'] ?? null;
        $approvalFromDate = $params['approval_from_date'] ?? null;
        $approvalToDate = $params['approval_to_date'] ?? null;
        $storeId = $params['store_id'] ?? null;
        $username = $params['username'] ?? null;
        $memberId = $params['member_id'] ?? null;
        $phoneNo = $params['phone_no'] ?? null;
        $exStoreId = $params['extra_store_id'] ?? null;
        $status = $params['status'] ?? null;
        $type = $params['type'] ?? null;
        $userFrom = $params['user_from'] ?? null;
        $withdrawal_reference = $params['withdrawal_reference'] ?? null;
        $lang = config('app.locale') ?? 'en';
        $seeAll = $params['see_all'] ?? null;

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        if (isset($params['export']) && ($params['export'] == 1)) {
            $jobData = $params;
            $jobData['model'] = get_class() ?? null;
            $jobData['function'] = __FUNCTION__;
            $jobData['module'] = MODULE;
            $jobData['creator_type'] = MODULE;
            $jobData['creator_id'] = Auth::user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, 'Export Successfully');
        }

        $items = UserRebate::query()
            ->with('user', 'user.userLevelTransactions')
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('DATE(created_at)'), '>=', $fromDate);

                return $q->where(DB::raw('DATE(created_at)'), '<=', $toDate);
            })
            ->when(isset($username), function ($q) use ($username) {
                return $q->whereRelation('user', 'username', 'LIKE', '%'.$username.'%');
            })
            ->when(isset($memberId), function ($q) use ($memberId) {
                return $q->whereRelation('user', 'member_id', $memberId);
            })
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('user.store', 'id', $storeId);
            })
            ->when(isset($phoneNo), function ($q) use ($phoneNo) {
                return $q->whereRelation('user', function ($q) use ($phoneNo) {
                    $phoneNo = preg_replace('/[^0-9]/', '', $phoneNo);
                    $q->where('phone_no', 'LIKE', "%$phoneNo%");
                });
            })
            ->when(isset($status), function ($q) use ($status) {
                return $q->where('status', UserRebate::$status[$status]);
            })
            ->when(isset($withdrawal_reference), function ($q) use ($withdrawal_reference) {
                return $q->where('serial_number', $withdrawal_reference);
            })
            ->orderBy($order_by, $order_sort)
            ->paginate($limit);

        $totalTable = [
            'total_amount' => 0,

            'total_amount_approved' => 0,
            'total_amount_pending' => 0,
            'total_amount_rejected' => 0,
        ];

        $mapFunc = function ($q) use (&$totalTable) {
            $status = array_search($q->status, UserRebate::$status);
            $isEditable = in_array($q->status, [UserRebate::$status['pending']]) ? 1 : 0;

            if ($q->updater_id >= 1000000) {
                $updater = $q->updateUser ? $q->updateUser->username : null;
            } elseif ($q->updater_id == 0) {
                $updater = 'system';
            } else {
                $updater = $q->updateAdmin ? $q->updateAdmin->username : null;
            }

            $turnover = $q->user->userLevelTransactions->filter(function ($transaction) use ($q) {
                    return $transaction->rebate_amount > 0 &&
                        $transaction->created_at >= $q->from &&
                        $transaction->created_at <= $q->to;
                })
                ->sum(function ($transaction) {
                    $meta = json_decode($transaction->meta);
                    return $meta->turnover ?? 0;
                });

            $res = [
                'id' => $q->id,
                'withdrawal_reference' => $q->serial_number,
                'created_at' => DateTrait::dateFormat($q->created_at),
                'username' => $q->user->username ?? null,
                'member_id' => $q->user->member_id ?? null,
                'phone_no' => $q->user->phone_no ?? null,
                'amount' => DecimalTrait::setDecimal($q->amount ?? 0),
                'updated_at' => DateTrait::dateFormat($q->updated_at),
                'updated_by' => $updater,
                'status' => $status,
                'status_display' => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                'is_editable' => $isEditable,
                'user_from' => $q->user->party ?? 'FW',
                'store_name' => $q->user->store->name ?? null,
                'turnover' => $turnover,
            ];
            $totalTable['total_amount'] += $q->amount;

            $status = array_search($q->status, UserRebate::$status);
            if (isset($totalTable['total_amount_'.$status])) {
                $totalTable['total_amount_'.$status] += $q->amount;
            }

            return (object) $res;
        };

        if (($seeAll == 1)) {
            $data = $items->get()->map($mapFunc);
        } else {
            $items->getCollection()->transform($mapFunc);
            $data = (new ItemsCollection($items));
        }

        abort(200, json_encode([
            'data' => $data,
        ]));
    }

    public function updateRebateStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'integer|required|exists:user_rebates,id',
            // 'status' => 'integer|required|in:' . implode(',', array_values(UserRebate::$status)),
            'action' => [
                'required',
                'string',
                Rule::when((MODULE == 'admin'), function ($q) {
                    return $q = 'in:'.implode(',', array_keys(Arr::only(UserRebate::$status, ['pending', 'approved', 'rejected'])));
                }),
            ],
            'remark' => 'required_if:action,=,rejected|string|max:255|nullable',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        UserRebate::updateRebateStatus($request->id, $request->action);

        abort(200, 'User Rebate status updated successfully');
    }

    public function userRebateDetail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => [
                'required',
                'int',
                Rule::exists('user_rebates', 'id'),
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $getWithdrawalDet = UserRebate::getUserRebateDetail($validator->validated());
        $data['data'] = $getWithdrawalDet;
        abort(200, json_encode($data));
    }
}
