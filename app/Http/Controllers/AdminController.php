<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services;
use App\Models;
use App\Models\Admin;
use App\Models\AdminRoles;
use App\Models\Country;
use App\Models\Deposit;
use App\Models\ExTransfer;
use App\Models\Permissions;
use App\Models\User;
use App\Models\UserCardLog;
use App\Models\Withdrawal;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Illuminate\Validation\Rule;
use App\Traits\DecimalTrait;


class AdminController extends Controller
{

    public function dashboard(Request $request)
    {

        $validator = Validator::make($request->all(), [
            "from_date" => "nullable|date|date_format:Y-m-d H:i:s",
            "to_date" => "nullable|date|date_format:Y-m-d H:i:s|after_or_equal:from_date",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $dashboardData = Admin::dashboard($validator->validated());
        $data["data"] = $dashboardData;
        abort(200, json_encode($data));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = my-profile
     * @module = admin
     * @path = my-profile
     * @method = post
     * @description = To get admin own profile.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
	
     * @response = {"data":{"id":1,"name":"admin","username":"admin","email":"<EMAIL>","status":"active","status_display":"Active","created_at":"2022-06-13 18:01:03"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZhY3N4LmNvbS9teS1wcm9maWxlIiwiaWF0IjoxNjYyNTMzNTk2LCJleHAiOjE2NjI1MzcyODEsIm5iZiI6MTY2MjUzMzY4MSwianRpIjoialgwRDNucGN2UVVnRUhudyIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.IRLh01yEcuGuR70XXZ79kLcI5YTMOVIKDiL3SpbIaMQ","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"development","execution_duration":"0.012151002883911 sec","log_id":"ea29989c-82e2-43e9-98e0-d82b4c0590a9"}
     * ##docs end##
     */
    public function myProfile(Request $request)
    {
        $adminData = Admin::getProfile($request->user()->id);
        $data["data"] = $adminData;
        abort(200, json_encode($data));
    }


    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Admin List
     * @module = admin
     * @path = admin/list
     * @method = post
     * @permissionName = Admin
     * @menuType = menu
     * @description = To get admin list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
    
     * @body = username|<username>|string|optional|test|Admin's username.
     * @body = name|<name>|string|optional|test|Admin's name.
     * @body = email|<email>|string|optional|<EMAIL>|Admin's email.
     * @body = phone_no|<phone_no>|integer|optional|011111111|Admin's phone_no.
     * @body = role|<role>|integer|optional|1|Admin's role id. (Dropdown: admin_roles)
     * @body = status|<status>|integer|optional|1|Admin's status. (Dropdown: admin_status)
     * @body = date_from|<date_from>|string|optional|2022-12-12|Created date.
     * @body = date_to|<date_to>|string|optional|2022-12-12|Created date.
     * @body = updated_date_from|<updated_date_from>|string|optional|2022-12-12|Updated date.
     * @body = updated_date_to|<updated_date_to>|string|optional|2022-12-12|Updated date.
     * 
     * @body = order_by|<order_by>|string|optional|email|Order listing by column. (email , status , name)
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * 
     * @body = export|<1/0>|integer|optional|1|export the listing or not. 
     * @body = export_data|<export_data>|array|option|{"data":{"Key":"Column Name"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|sponsor-bonus|name of the export file. Required for export

     * @response = {"data":{"list":[{"id":3,"name":"samchang5","username":"samchang1","email":"<EMAIL>","created_at":"2023-03-07 12:46:50 PM","updated_at":"2023-03-07 12:59:56 PM","role_id":1,"role_name":"admin","status":"active","status_display":"Active","updated_by":"maskman"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnB1LXN1cGVyYXBwLmNvbS9hZG1pbi9saXN0IiwiaWF0IjoxNjc4MTYxODI2LCJleHAiOjE2NzgxNjg5MDksIm5iZiI6MTY3ODE2NTMwOSwianRpIjoiS2Fja3QxcVRBR0VBdTM0ZSIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.rNqWP2R3BsVKaoB1Pe14p3Lo8YRe893PLav9i7zobo0","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.093126058578491 sec","log_id":"f57767a2-97f3-4e68-a321-8304ad261b5f"}
     * ##docs end##
     */

    public function get(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'string',
            'name' => 'string',
            'email' => 'email',
            "phone_no" => "string",
            "role" => "integer",
            'status' => 'integer|in:' . implode(",", array_values(Admin::$activatedDisplay)),
            'date_from' => 'required_with:date_to|date_format:Y-m-d',
            'date_to' => 'required_with:date_from|date_format:Y-m-d|after_or_equal:date_from',
            'updated_date_from' => 'required_with:updated_date_to|date_format:Y-m-d',
            'updated_date_to' => 'required_with:updated_date_from|date_format:Y-m-d|after_or_equal:updated_date_from',
            'order_by' => 'string',
            'order_sort' => 'string|in:asc,desc',
            'limit' => 'integer',
            'page' => 'integer',
            'see_all' => 'integer|in:0,1',
            "export" => "in:1,0|nullable",
            "export_data" => "required_if:export,==,1",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $adminList = Admin::getList($validator->validated());

        $data["data"] = $adminList;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Admin Detail
     * @module = admin
     * @path = admin/detail
     * @method = POST
     * @permissionName = Admin Detail
     * @menuType = api
     * @parent = Admin
     * @description = To get admin details.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = id|<id>|integer|required|<EMAIL>|Admin's id.
     * @response = {"data":{"id":3,"name":"samchang1","username":"samchang1","email":"<EMAIL>","status":"active","status_display":"Active","created_at":"2023-03-07 12:46:50 PM","updated_at":"2023-03-07 12:46:50 PM","permissions_id":{"1":1,"2":2,"3":3,"4":4,"5":5,"6":6,"7":7,"8":8,"9":9,"10":10,"11":11,"12":12,"13":13,"14":14,"15":15,"20":20,"21":21,"22":22,"23":23,"24":24,"25":25,"26":26,"27":27,"28":28,"29":29,"30":30,"31":31,"39":39,"40":40,"41":41,"42":42,"43":43,"44":44,"45":45,"46":46,"51":51,"52":52,"53":53,"54":54,"55":55,"56":56,"57":57,"58":58,"59":59,"69":69,"70":70,"71":71,"72":72,"73":73,"74":74,"75":75,"76":76,"77":77,"78":78},"role_id":1,"role_name":"admin","is_master":0},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnB1LXN1cGVyYXBwLmNvbS9hZG1pbi9kZXRhaWwiLCJpYXQiOjE2NzgxNjE4MjYsImV4cCI6MTY3ODE2ODY3MSwibmJmIjoxNjc4MTY1MDcxLCJqdGkiOiJkTXhBS0R4NlJSUE1ablRXIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.AW1dC-mPHTTv7EnBMIMYSUgUnOz4HjnYwLgZU8vS7pU","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.093169927597046 sec","log_id":"ef4c2020-f2a1-45a5-8577-52d11a298a10"}
     * ##docs end##
     */
    public function detail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer|exists:admin,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $adminDetails = Admin::getProfile($request->input("id"));
        $data["data"] = $adminDetails;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Admin Create
     * @module = admin
     * @path = admin/create
     * @method = POST
     * @permissionName = Admin Create
     * @menuType = api
     * @parent = Admin
     * @description = To new admin account.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = username|<username>|string|required|admin|Admin's username.
     * @body = name|<name>|string|required|Admin1|Admin's name.
     * @body = email|<email>|string|required|<EMAIL>|Admin's email.
     * @body = phone_no|<phone_no>|string|required|60-*********|Admin's phone number.
     * @body = status|<status>|integer|required|1|Admin's status.(Dropdown: admin_status)
     * @body = password|<password>|string|required|********|Minimum 8 characters , alphanumeric and mix case.
     * @body = password_confirmation|<password_confirmation>|string|required|********|Password confirmation.
     * @body = role_id|<role_id>|integer|required|1|Admin's Role id (dropdown: admin_roles)
     * @body = store_id|<store_id>|array|required|[1,2,3,4,5]|Store' id. (dropdown: store)
     * @body = store_id.*|<store_id.*>|integer|required|1|Store' id.
     * @response = {"status":true,"message":"Created Successfully.","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZweHUuY29tL2FkbWluL2NyZWF0ZSIsImlhdCI6MTY2MTQxMjU1MywiZXhwIjoxNjYxNDIwOTU2LCJuYmYiOjE2NjE0MTczNTYsImp0aSI6IkNTTHhIU1hqajFrWUpvekkiLCJzdWIiOiIyIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.qriv0doGuAxdQoK8y8xKWoR0fr6KVfLFAAPVZTeAedc","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"development","execution_duration":"0.0099189281463623 sec","log_id":"d6954239-f6fc-46b9-8d8a-31764775fbde"}
     * ##docs end##
     */
    public function create(Request $request)
    {
        $user = Auth::user();
        $is_master = $user->is_master ?? 0;

        if (isset($request->phone_no)) {
            $replace = str_replace(" ", "", $request->phone_no);
            $request->merge(["phone_no" => $replace]);
        }

        $validator = Validator::make($request->all(), [
            'username' => 'required|string|unique:admin,username',
            'name' => 'required|string',
            'status' => 'required|integer|in:' . implode(',', Admin::$activatedDisplay),
            'email' => 'required|email',
            'phone_no' => [
                'required',
                'string',
                'unique:admin,phone_no',
                'regex:/^[0-9]{1,3}-[0-9]{7,13}$/',
                function ($q, $value, $fail) use (&$request) {
                    $phoneDetail = explode('-', $value);

                    $country = Country::where('country_code', $phoneDetail[0])->first()->name ?? null;

                    if (empty($country)) {
                        abort(400, json_encode("Invalid Phone Number"));
                    }

                    $phoneDetail[1] = (int)$phoneDetail[1];
                    $phone_no = $phoneDetail[0] . "-" . $phoneDetail[1];
                    $request->merge(["phone_no" => $phone_no]);
                }
            ],
            'password' => self::passwordRule(['required']),
            'role_id' => 'required|exists:admin_roles,id,status,' . AdminRoles::$status['active'],
            'store_id' => 'nullable|array',
            'store_id.*' => [
                'nullable',
                'integer',
                Rule::exists('store', 'id'),
            ],
        ], [
            'password.confirmed' =>  null,
            'password' => Lang::get('lang.password-field-error'),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }
        $validator->setData(array_merge($validator->validated(), ["phone_no" => $request->phone_no]));

        Admin::addAdmin($validator->validated());
        abort(200, Lang::get('lang.admin-create-success'));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Admin Edit
     * @module = admin
     * @path = admin/edit
     * @method = POST
     * @permissionName = Admin Edit
     * @menuType = api
     * @parent = Admin
     * @description = To edit admin details.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = id|<id>|integer|required|merchant123|Admin's id.
     * @body = name|<name>|string|required|merchant123|Admin's name.
     * @body = email|<email>|string|required|<EMAIL>|Admin's email.
     * @body = phone_no|<phone_no>|string|required|60-*********|Admin's phone number.
     * @body = status|<status>|integer|required|1|Admin's status. (Dropdown: admin_status)
     * @body = role_id|<role_id>|integer|required|1|Admin's Role id (dropdown: admin_roles)
     * @body = store_id|<store_id>|array|required|[1,2,3,4,5]|Store' id. (dropdown: store)
     * @body = store_id.*|<store_id.*>|integer|required|1|Store' id.
     * @response = {"status":true,"message":"Updated Successfully.","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZweHUuY29tL2FkbWluL2VkaXQiLCJpYXQiOjE2NjE0MTI1NTMsImV4cCI6MTY2MTQyMTM0OCwibmJmIjoxNjYxNDE3NzQ4LCJqdGkiOiJoeWcyaDNFeGhtVWhreXo5Iiwic3ViIjoiMiIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.14qtipyjet6n6G4HuIcUioUz76gjpbGJ6AHz2Kder_w","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"development","execution_duration":"0.0071628093719482 sec","log_id":"02e37e3b-b816-4323-ba48-26f8af289ae2"}
     * ##docs end##
     */
    public function edit(Request $request)
    {
        $user = Auth::user();
        $is_master = $user->is_master ?? 0;

        if (isset($request->phone_no)) {
            $replace = str_replace(" ", "", $request->phone_no);
            $request->merge(["phone_no" => $replace]);
        }

        $validator = Validator::make($request->all(), [
            'id' => 'required|integer|exists:admin,id',
            'name' => 'required|string',
            'email' => 'required|email',
            'phone_no' => [
                'required',
                'string',
                Rule::unique('admin', 'phone_no')->where(function ($q) use ($request) {
                    return $q->where('id', '!=', $request->id);
                }),
                'regex:/^[0-9]{1,3}-[0-9]{7,13}$/',
                function ($q, $value, $fail) use (&$request) {
                    $phoneDetail = explode('-', $value);

                    $country = Country::where('country_code', $phoneDetail[0])->first()->name ?? null;

                    if (empty($country)) {
                        abort(400, json_encode("Invalid Phone Number"));
                    }

                    if ($country == 'vietnam') {
                        $phoneDetail[1] = (int)$phoneDetail[1];
                    }

                    $phone_no = $phoneDetail[0] . "-" . $phoneDetail[1];
                    $request->merge(["phone_no" => $phone_no]);
                }
            ],
            'status' => 'required|integer|in:' . implode(',', Admin::$activatedDisplay),
            'role_id' => 'required|exists:admin_roles,id,status,' . AdminRoles::$status['active'],
            'store_id' => 'nullable|array',
            'store_id.*' => [
                'nullable',
                'integer',
                Rule::exists('store', 'id'),
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $adminDetails = Admin::updateProfile($request->all(), $request->get('id'), true);
        abort(200, $adminDetails);
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Role List
     * @module = admin
     * @path = admin/get-role-list
     * @method = POST
     * @description = To get admin role list.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * 
     * @body = status|<status>|string|optional|active|Admin roles' status. (dropdown: admin_role_status)
     * @body = date_from|<date_from>|string|optional|2022-12-12|Created date.
     * @body = date_to|<date_to>|string|optional|2022-12-12|Created date.
     * @body = name|<name>|string|optional|admin|Role's name.
     * @body = updated_date_from|<updated_date_from>|string|optional|2022-12-12|Updated date.
     * @body = updated_date_to|<updated_date_to>|string|optional|2022-12-12|Updated date.
     * @body = order_by|<order_by>|string|optional|email|Order listing by column.
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * 
     * @response = {"list":[{"id":2,"name":"admin","created_at":"2023-01-18 16:35:27","updated_at":"2023-01-18 16:35:27","status":1,"status_display":"active"},{"id":3,"name":"admin1","created_at":"2023-01-18 16:35:27","updated_at":"2023-01-18 16:35:27","status":0,"status_display":"inactive"}],"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZmLmNvbS9hZG1pbi9nZXQtcm9sZS1saXN0IiwiaWF0IjoxNjc0MDMxMDgzLCJleHAiOjE2NzQwMzUyNTIsIm5iZiI6MTY3NDAzMTY1MiwianRpIjoiN2p0VDZXYjJkM0VhZVpLbyIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.Uof9clNmVyKFFy-RkrTR6NUJ8c_vZ71h8_eEislp820","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.097305059432983 sec","log_id":"0ae3953b-45d9-4c0c-a8e1-69723b45a705"}
     * ##docs end##
     */

    public function getRoleList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'status' => "string|in:" . implode(",", array_keys(AdminRoles::$status)),
            'date_from' => 'required_with:date_to|date_format:Y-m-d',
            'date_to' => 'required_with:date_from|date_format:Y-m-d|after_or_equal:date_from',
            'name' => 'string',
            'updated_date_from' => 'required_with:updated_date_to|date_format:Y-m-d',
            'updated_date_to' => 'required_with:updated_date_from|date_format:Y-m-d|after_or_equal:updated_date_from',
            "order_sort" => "string|in:asc,desc",
            "see_all" => "integer|in:1,0",
            "limit" => "integer",
            "page" => "integer",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data = AdminRoles::getRoleList($validator->validated());
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Role Detail
     * @module = admin
     * @path = admin/get-role-detail
     * @method = POST
     * @description = To get admin role detail.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = role_id|<role_id>|integer|optional|1|Role's id.
     * @response = {"data":{"details":{"name":"admin","permissions_id":[1,2],"status":1,"status_display":"active","created_at":"2023-01-18 16:35:27","updated_at":"2023-01-18 16:35:27"},"permissions":[{"id":1,"name":"perm-dashboard","display":"Dashboard","parent_id":0,"level":1}]},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZmLmNvbS9hZG1pbi9nZXQtcm9sZS1kZXRhaWwiLCJpYXQiOjE2NzQwMzEwODMsImV4cCI6MTY3NDAzNzczMCwibmJmIjoxNjc0MDM0MTMwLCJqdGkiOiJVSXdYOFBDTjhkNXFyY1JOIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ._6gsZHrrsJcyqDQPZQlmEgaIOuktpBAxav3NFv2c7Zk","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.10476803779602 sec","log_id":"8846ab83-187d-4ba5-9a8c-529222a8aaa2"}
     * ##docs end##
     */

    public function getRoleDetail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'role_id' => 'integer|exists:admin_roles,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }
        $data = AdminRoles::getRoleDetail($validator->validated());
        abort(200, json_encode(["data" => $data]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Add Role
     * @module = admin
     * @path = admin/add-role
     * @method = POST
     * @description = To add admin role.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * 
     * @body = name|<name>|string|required|admin_test|Admin role name.
     * @body = permissions_id|<permissions_id>|array|required|[1,2,3,4,5]|Permissions' id.
     * @body = permissions_id.*|<permissions_id.*>|integer|required|1|Permissions' id.
     * @body = status|<status>|string|required|active|Admin role status.(dropdown: admin_role_status)
     * 
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZmLmNvbS9hZG1pbi9hZGQtcm9sZSIsImlhdCI6MTY3NDAzMTA4MywiZXhwIjoxNjc0MDM5MjA4LCJuYmYiOjE2NzQwMzU2MDgsImp0aSI6IjFNOHNGVGFxTkZJZEFBRTgiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.3mwY0NvjTV996S7YbbvTP_-t9_pjZ6Kf_9V3teqQaMQ","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.13261485099792 sec","log_id":"d27b77b2-72aa-47f1-9605-eb5a415c128e"}
     * ##docs end##
     */

    public function addRole(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => [
                'required',
                'string',
                function ($q, $value, $fail) {
                    $checkExists = AdminRoles::where('name', $value)->first();
                    if ($checkExists) {
                        $fail(Lang::get('lang.duplicate-name'));
                        return;
                    }
                }
            ],
            'permissions_id' => 'required|array',
            'permissions_id.*' => [
                'required',
                'integer',
                Rule::exists('permissions', 'id')->where('master_disabled', 0)->when(!auth()->user()->is_master, function ($q) {
                    return $q->where('disabled', 0);
                }),
            ],
            'status' => 'required|string|in:' . implode(',', array_keys(AdminRoles::$status)),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        AdminRoles::addRole($validator->validated());
        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Edit Role
     * @module = admin
     * @path = admin/edit-role-detail
     * @method = POST
     * @description = To edit admin role details.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * 
     * @body = role_id|<role_id>|integer|required|1|Admin role id.
     * @body = name|<name>|string|required|admin_test|Admin role name.
     * @body = permissions_id|<permissions_id>|array|required|[1,2,3,4,5]|Permissions' id.
     * @body = permissions_id.*|<permissions_id.*>|integer|required|1|Permissions' id.
     * @body = status|<status>|string|required|active|Admin role status.(dropdown: admin_role_status)
     * 
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZmLmNvbS9hZG1pbi9hZGQtcm9sZSIsImlhdCI6MTY3NDAzMTA4MywiZXhwIjoxNjc0MDM5MjA4LCJuYmYiOjE2NzQwMzU2MDgsImp0aSI6IjFNOHNGVGFxTkZJZEFBRTgiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.3mwY0NvjTV996S7YbbvTP_-t9_pjZ6Kf_9V3teqQaMQ","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.13261485099792 sec","log_id":"d27b77b2-72aa-47f1-9605-eb5a415c128e"}
     * ##docs end##
     */

    public function editRoleDetail(Request $request)
    {
        $getRoleData = null;
        $validator = Validator::make($request->all(), [
            'role_id' => [
                'required',
                'integer',
                'exists:admin_roles,id',
                function ($q, $value, $fail) use ($request, &$getRoleData) {
                    $getRoleData = AdminRoles::where('id', $request->role_id)->first();
                    if (isset($getRoleData) && in_array($getRoleData->id, [1, 2]) && !auth()->user()->is_master) {
                        $fail(Lang::get('lang.failed'));
                        return;
                    }
                }
            ],
            'name' => [
                'required',
                'string',
                function ($q, $value, $fail) use ($request, &$getRoleData) {
                    $checkExists = AdminRoles::where('name', $value)->first();
                    if ($checkExists && ($checkExists->id != $request->role_id)) {
                        $fail(Lang::get('lang.duplicate-name'));
                        return;
                    }
                }
            ],
            'permissions_id' => 'required|array',
            'permissions_id.*' => [
                'required',
                'integer',
                Rule::exists('permissions', 'id')->where('master_disabled', 0)->when(!auth()->user()->is_master, function ($q) {
                    return $q->where('disabled', 0);
                }),
            ],
            'status' => 'required|string|in:' . implode(',', array_keys(AdminRoles::$status)),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        AdminRoles::editRole($validator->validated());
        abort(200, Lang::get('lang.admin-update-success'));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Device List
     * @module = admin
     * @path = admin/get-device-list
     * @method = POST
     * @description = To get device list.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * 
     * @body = member_id|<member_id>|string|optional|test|Admin's member_id.
     * @body = phone_no|<phone_no>|integer|optional|011111111|Admin's phone_no.
     * @body = status|<status>|integer|optional|1|Admin's status. (Dropdown: user_device_status)
     * 
     * @body = order_by|<order_by>|string|optional|email|Order listing by column.
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * 
     * @body = export|<1/0>|integer|optional|1|export the listing or not. 
     * @body = export_data|<export_data>|array|option|{"data":{"Key":"Column Name"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|sponsor-bonus|name of the export file. Required for export
     * 
     * @response = {"data":{"list":[{"id":1,"brand":"iPhone","model":"iPhone15,4","member_id":"178412734","phone_no":"60120000051","status":1,"statusName":"active","status_display":"Active","updated_at":"28\/03\/2024 15:46:37","updated_by":"tkMins"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2FkbWluL2dldC1kZXZpY2UtbGlzdCIsImlhdCI6MTcxMTYxMTkwOSwiZXhwIjoxNzExNjE1ODE2LCJuYmYiOjE3MTE2MTIyMTYsImp0aSI6InFyQWl6SnpGM2h5T2lOQzMiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.O4csZ2BKP3e2jrPxQJDqZGosXjwCdm8aNHF4vh999ok","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.054338932037354 sec","log_id":"68a22562-b32f-4921-9de9-25e86de3d5e2"}
     * ##docs end##
     */
    public static function getDeviceList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'member_id' => 'nullable|string',
            'phone_no' => 'nullable|string',
            'status' => 'nullable|integer|in:' . implode(",", array_values(Models\UserDeviceInfo::$status)),
            'order_by' => 'string',
            'order_sort' => 'string|in:asc,desc',
            'limit' => 'integer',
            'page' => 'integer',
            'see_all' => 'integer|in:0,1',
            "export" => "in:1,0|nullable",
            "export_data" => "required_if:export,==,1",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $dataList = Models\UserDeviceInfo::getList($validator->validated());

        $data["data"] = $dataList;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Edit Device
     * @module = admin
     * @path = admin/edit-device
     * @method = POST
     * @description = To edit device.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * 
     * @body = id|<id>|string|optional|test|Device list's id.
     * @body = status|<status>|integer|optional|1|Admin's status. (Dropdown: user_device_status)
     * 
     * @response = {"data":true,"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2FkbWluL2VkaXQtZGV2aWNlIiwiaWF0IjoxNzExNjExOTA5LCJleHAiOjE3MTE2MTU5NTksIm5iZiI6MTcxMTYxMjM1OSwianRpIjoiem51VEdGUDJlVDZkWmFJaiIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.Hv5hOVk7bih2Y_7Dkp2N53WJ_h2fCc2-VJVKBTOf8hc","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.029803991317749 sec","log_id":"2d7ba18f-adea-436a-ba52-2b0d0fb6f9d8"}
     * ##docs end##
     */
    public static function editDevice(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:user_device_info,id',
            'status' => 'required|integer|in:' . implode(",", array_values(Models\UserDeviceInfo::$status)),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $dataList = Models\UserDeviceInfo::updateUserDInfo($validator->validated());

        $data["data"] = $dataList;
        abort(200, json_encode($data));
    }
}
