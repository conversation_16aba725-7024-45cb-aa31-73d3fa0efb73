<?php

namespace App\Http\Controllers;

use App\Http\Resources\ItemsCollection;
use App\Models\UserLevel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class UserLevelController extends Controller
{
    public function getUserLevel(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();

        $userLevel = UserLevel::getByUserId($params['user_id']);

        abort(200, json_encode(['data' => $userLevel]));
    }

    public function getUserLevelList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'integer',
            'limit' => 'integer',
            'order_by' => 'string',
            'order_sort' => 'string',
            'username' => 'string|nullable',
            'phone_no' => 'string|nullable',
            'user_id' => 'integer|nullable',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();
        $username = $params['username'] ?? null;
        $phoneNo = $params['phone_no'] ?? null;
        $userID = $params['user_id'] ?? null;
        $fromDate = $params['from_date'] ?? null;
        $toDate = $params['to_date'] ?? null;
        $seeAll = $params['see_all'] ?? null;

        $sortableColumns = ['id', 'created_at'];

        // Order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // Sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = ($seeAll == 1) ? null : ($params['limit'] ?? config('app.pagination_rows'));

        $items = UserLevel::query()
            ->with('user')
            // ->select(['id', 'user_id', 'promotion_id', 'target_turnover', 'achieved_turnover', 'bonus_amount', 'max_withdraw_amount', 'game_return_amount', 'status', 'created_at'])
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('created_at'), '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where(DB::raw('created_at'), '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(in_array(MODULE, ['app', 'user']), function ($q) use ($userID) {
                $q->where('user_id', $userID);
            })
            ->when($username, function ($query) use ($username) {
                $query->whereRelation('user', 'username', 'like', "%$username%");
            })
            ->when(isset($phoneNo), function ($query) use ($phoneNo) {
                $phoneNo = preg_replace('/[^0-9]/', '', $phoneNo);
                $query->whereRelation('user', 'phone_no', 'LIKE', "%$phoneNo%");
            })
            ->orderBy($order_by, $order_sort)
            ->when((! isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $mapFunc = function ($q) {
            return [
                'id' => $q->id,
                'user_id' => $q->user->id,
                'username' => $q->user->username,
                'rebate' => $q->rebate,
                'vip_level_id' => $q->vip_level_id,
                'created_at' => $q->created_at,
                'updated_at' => $q->updated_at,
            ];
        };

        if (($seeAll == 1)) {
            return ['list' => $items->get()->map($mapFunc)->toArray()];
        } else {
            $items->getCollection()->transform($mapFunc);

            return (new ItemsCollection($items))->toArray();
        }
    }

    public function updateUserLevel(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'vip_level_id' => 'required|integer|exists:vip_levels,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();
        $adminId = auth()->user()->id;
        $userId = $params['user_id'];
        $vipLevelId = $params['vip_level_id'];

        UserLevel::updateVIPLevelAndRebateByUserId($adminId, $userId, $vipLevelId);

        abort(200);
    }
}
