<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services;
use App\Models\Kyc;

use Illuminate\Support\Facades\Validator;
use DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Validation\Rule;

class KycController extends Controller
{
    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = kyc/register-kyc
     * @module = user,app
     * @path = kyc/register-kyc
     * @method = POST
     * @description = To register kyc.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = name|<name>|string|required|Jessy|User's name same as passport or ic
     * @body = ic_no|<ic_no>|string|required|0001001110111|User's passport or ic number
     * @body = country_id|<country_id>|integer|required|129|User's nationality (dropdown: country)
     * @body = email|<email>|string|required|<EMAIL>|User's email
     * @body = image_1|<image_1>|string|required|staging/kyc/1234.jpg|AWS url link (required if no document)
     * @body = image_2|<image_2>|string|required|staging/kyc/3245.jpg|AWS url link (required if type = ic)
     * @body = self_image|<self_image>|string|required|staging/kyc/selfie_image.jpg|AWS url link
     * @body = document|<document>|string|required|staging/kyc/selfie_image.pdf|AWS url link (required if no image)
     * @body = type|<type>|string|required|passport|KYC identity type.
     * @response = {"status":true,"message":"Created Successfully.","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS9reWMvcmVnaXN0ZXIta3ljIiwiaWF0IjoxNjk2ODI0ODk4LCJleHAiOjE2OTY4Mjk0OTYsIm5iZiI6MTY5NjgyNTg5NiwianRpIjoiclJPYkFzUXo3eXFhQ0Y2RCIsInN1YiI6IjEwMDAwMDAiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.xE0qdHjaH12ZZNswfcS5vplbZdl-4r0habmuoobv7fI","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.12570095062256 sec","log_id":"e5eaf944-a4d6-4e15-9100-2c911bc2202e"}
     * ##docs end##
     */
    public function registerKyc(Request $request)
    {
        $request->request->add(['user_id' => auth()->user()->id]);
        $validator = Validator::make($request->all(), [
            'user_id' => [
                'required',
                'integer',
                Rule::unique('kyc', 'user_id')
                    ->whereNot('status', Kyc::$kycStatus['declined'])
            ],
            'name' => [
                'required',
                'string',
                function ($q, $value, $fail) use ($request) {
                    if (!preg_match("/^[A-Z\s]+$/", $value)) {
                        return $fail(Lang::get('lang.uppercase-input'));
                    }
                }
            ],
            'ic_no' => [
                'required',
                'string',
                Rule::unique('kyc', 'ic_no')->whereNot('status', Kyc::$kycStatus['declined']),
                function ($q, $value, $fail) use ($request) {
                    if (isset($request->type) && $request->type == 'ic' && !preg_match("/^[0-9]+$/", $value)) {
                        return $fail(Lang::get('lang.numeric-input'));
                    }
                },
            ],
            'country_id' => 'required|integer|exists:country,id',
            'email' => 'required|string|email',
            'image_1' => 'required_without:document|string',
            'image_2' => [
                'string',
                Rule::requiredIf(function() use ($request) {
                    return !isset($request->document) && ($request->type == 'ic');
                }),
            ],
            'self_image' => 'required_without:document|string',
            'document' => 'required_without:image_1|string',
            'type' => 'required|string|in:ic,passport'
        ], [
            'user_id.unique' => Lang::get('lang.kyc-duplicate-register'),
            'ic_no.unique' => Lang::get('lang.kyc-duplicate-ic')
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $registerKyc = Kyc::registerKyc($validator->validated());

        abort(200, Lang::get('lang.user-create-success'));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = kyc/get-own-kyc
     * @module = user,app
     * @path = kyc/get-own-kyc
     * @method = POST
     * @description = To check own kyc.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @response = {"data":{"user_id":1000000,"name":"asd","ic":"*********","email":"<EMAIL>","country_name":"malaysia","country_name_display":"Malaysia","type":"ic","image_1":"https:\/\/fw-pvt.s3.ap-southeast-1.amazonaws.com\/asd.jpg?response-content-disposition=inline%3B%20filename%3D%22asd.jpg%22&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=FwoGZXIvYXdzEL7%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaDLk8l9nVK5FN4d1IiiKxAUQJONI9%2Fjn8fBKSJEPA8S9B%2BuVWpGv%2FHRKT8EcPwr3EcaXAfjFijKPOimlTiXrq3CovWBRoy8yEEOzkahJTp%2BgcDavJh%2BqO5vgDycPidYXsorUe47f3n%2Bz6TLkpoT1gcpcEPcCbppw6EvJV%2FpPqCTF6APEgcUIyecIBbI2okI9QJMFve5nR9jOFT2Y7BsR4dNrxLYnky%2BNDbSp1pdnQLIdyhKbnUhPMUoc3pPvFqj0ObSjkgo6pBjItU23he8KLeXeq%2BqLlbuQ1mvFBW9C6pGT1nV9penI%2BB%2BWS1m3r%2Fo3VTzeXCeWA&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIA43LUKFW2XTS4ZBD3%2F20231009%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20231009T043218Z&X-Amz-SignedHeaders=host&X-Amz-Expires=900&X-Amz-Signature=d2205ca252df0e74eb692c3504506b2c4e5d6f9141a630cdd355157761798e82","image_2":"https:\/\/fw-pvt.s3.ap-southeast-1.amazonaws.com\/asd.jpg?response-content-disposition=inline%3B%20filename%3D%22asd.jpg%22&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=FwoGZXIvYXdzEL7%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaDLk8l9nVK5FN4d1IiiKxAUQJONI9%2Fjn8fBKSJEPA8S9B%2BuVWpGv%2FHRKT8EcPwr3EcaXAfjFijKPOimlTiXrq3CovWBRoy8yEEOzkahJTp%2BgcDavJh%2BqO5vgDycPidYXsorUe47f3n%2Bz6TLkpoT1gcpcEPcCbppw6EvJV%2FpPqCTF6APEgcUIyecIBbI2okI9QJMFve5nR9jOFT2Y7BsR4dNrxLYnky%2BNDbSp1pdnQLIdyhKbnUhPMUoc3pPvFqj0ObSjkgo6pBjItU23he8KLeXeq%2BqLlbuQ1mvFBW9C6pGT1nV9penI%2BB%2BWS1m3r%2Fo3VTzeXCeWA&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIA43LUKFW2XTS4ZBD3%2F20231009%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20231009T043218Z&X-Amz-SignedHeaders=host&X-Amz-Expires=900&X-Amz-Signature=d2205ca252df0e74eb692c3504506b2c4e5d6f9141a630cdd355157761798e82","self_image":"https:\/\/fw-pvt.s3.ap-southeast-1.amazonaws.com\/asd.jpg?response-content-disposition=inline%3B%20filename%3D%22asd.jpg%22&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=FwoGZXIvYXdzEL7%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaDLk8l9nVK5FN4d1IiiKxAUQJONI9%2Fjn8fBKSJEPA8S9B%2BuVWpGv%2FHRKT8EcPwr3EcaXAfjFijKPOimlTiXrq3CovWBRoy8yEEOzkahJTp%2BgcDavJh%2BqO5vgDycPidYXsorUe47f3n%2Bz6TLkpoT1gcpcEPcCbppw6EvJV%2FpPqCTF6APEgcUIyecIBbI2okI9QJMFve5nR9jOFT2Y7BsR4dNrxLYnky%2BNDbSp1pdnQLIdyhKbnUhPMUoc3pPvFqj0ObSjkgo6pBjItU23he8KLeXeq%2BqLlbuQ1mvFBW9C6pGT1nV9penI%2BB%2BWS1m3r%2Fo3VTzeXCeWA&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIA43LUKFW2XTS4ZBD3%2F20231009%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20231009T043218Z&X-Amz-SignedHeaders=host&X-Amz-Expires=900&X-Amz-Signature=d2205ca252df0e74eb692c3504506b2c4e5d6f9141a630cdd355157761798e82","document":null,"status_display":"Pending","status":"pending","remark":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS9reWMvZ2V0LW93bi1reWMiLCJpYXQiOjE2OTY4MjQ4OTgsImV4cCI6MTY5NjgyOTUzOCwibmJmIjoxNjk2ODI1OTM4LCJqdGkiOiJUVTAzRlJlZ1JxNWFpd1VCIiwic3ViIjoiMTAwMDAwMCIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.fSfzR6Q0SXkMLxAb5z7wcy1xJ5qNyYZE3VOaRKHJMsI","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.12965202331543 sec","log_id":"4e1fbf40-e76c-44a1-a59c-b287f9cbd171"}
     * ##docs end##
     */
    public function getOwnKyc(Request $request)
    {
        $getKyc = Kyc::getOwnKyc();
        $data['data'] = $getKyc;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = kyc/get-user-kyc
     * @module = admin
     * @path = kyc/get-user-kyc
     * @permissionName = KYC
     * @menuType = menu
     * @method = POST
     * @description = To get all user kyc request.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = username|<username>|string|optional|Username|Create username filter.
     * @body = phone_no|<phone_no>|string|optional|012-34567890|Create Phone No filter.
     * @body = member_id|<member_id>|string|optional|1112211111|Create memeber ID filter.
     * @body = type|<type>|string|optional|passport|Create type filter
     * @body = ic_no|<ic_no>|string|optional|12345678|Create IC / Passport filter.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = approve_from_date|<approve_from_date>|string|optional|2022-08-28|Create Approve Date filter.
     * @body = approve_to_date|<approve_to_date>|string|optional|2022-08-28|Create Approve Date filter.
     * @body = country|<country>|integer|optional|100|Country filter. (Dropdown: all_country)
     * @body = email|email|integer|optional|<EMAIL>|email filter.
     * @body = status|<status>|integer|optional|1|status filter. (Dropdown: kyc_status)
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = export_data|<export_data>|array|option|{"data": {"value": "Header"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|balance-report|name of the export file. Required for export
     * @response = {"data":{"list":[{"id":1,"created_at":"03\/10\/2023 12:19:14","name":"asd","username":"tkNotes","ic_no":"123","email":"<EMAIL>","type":"passport","status_display":"Approved","status":"successful","country_name":"malaysia","country_name_display":"Malaysia","remarks":null,"updater":"tkMins","approved_at":"03\/10\/2023 12:38:05"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null},"status":true,"dropdown":{"kyc_status":[{"id":0,"name":"pending","display":"Pending"},{"id":1,"name":"successful","display":"Approved"},{"id":2,"name":"declined","display":"Rejected"}]},"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2t5Yy9nZXQtdXNlci1reWMiLCJpYXQiOjE2OTY5ODk0MzgsImV4cCI6MTY5Njk5MzYzNCwibmJmIjoxNjk2OTkwMDM0LCJqdGkiOiJFZm9RZGNqV2hMOXhVYnJuIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.-QAxoUbtHwxaTZGko61I7W6xxtOLrXy8A4FuZGA4Z5k","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.084751844406128 sec","log_id":"71cafb48-a75b-40f9-bcb9-c655e123da18"}
     * ##docs end##
     */
    public function getUserKyc(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "order_sort" => "string|in:asc,desc",
            'username' => 'string',
            'member_id' => 'string',
            'phone_no' => 'string',
            'email' => 'string',
            'ic_no' => 'string',
            'type' => 'string|in:ic,passport',
            'status' => 'integer|in:'.implode(',', array_values(Kyc::$kycStatus)),
            "from_date" => "required_with:to_date|string|date_format:Y-m-d",
            "to_date" => "required_with:from_date|string|date_format:Y-m-d|after_or_equal:from_date",
            "approve_from_date" => "required_with:approve_to_date|string|date_format:Y-m-d",
            "approve_to_date" => "required_with:approve_from_date|string|date_format:Y-m-d|after_or_equal:approve_from_date",
            "country" => "integer",
            "see_all" => "integer|in:1,0",
            "export" => "in:1,0|nullable",
            "export_data" => "required_if:export,==,1",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $getUserKyc = Kyc::getUserKyc($validator->validated());
        $data['data'] = $getUserKyc;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = kyc/get-user-kyc-det
     * @module = admin
     * @path = kyc/get-user-kyc-det
     * @permissionName = KYC List
     * @menuType = sub_menu
     * @parent = KYC
     * @method = POST
     * @description = To get specific user kyc request.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = id|<id>|int|required|1|Get specific details.
     * @response = {"data":{"data":{"created_at":"03\/10\/2023 12:19:14","type":"passport","name":"asd","username":"tkNotes","phone_no":"60-1160905696","ic_no":"123","email":"<EMAIL>","country_name":"malaysia","country_name_display":"Malaysia","image_1":"https:\/\/fw-pvt.s3.ap-southeast-1.amazonaws.com\/asd.jpg?response-content-disposition=inline%3B%20filename%3D%22asd.jpg%22&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=FwoGZXIvYXdzEOz%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaDAtkIB%2BIn4hnb45oQCKxAcNh7v1APMKOoTTRGYEB4J1m%2BaopiotnEf3HjArLkM6JFITf13UOMU6paSHGznNBNpRbBDQnePNMJ3rxMeqBZbiWI%2B3agqy1YC7yPivc%2Fged63tJ8eFRH5Gaf159oMdfGFitVuLaRBDTwPAiF2BU6eeUV2aOmt6vcVD%2FOebWETzMhMkyXaNxEBm6yZB7qRs4ba5XDXbUhb5zu3h5NOb7EVrTqw2WLPA2nSlHWLFfKc%2FKAyjlhpipBjItBshw2iqunZhNgibdUroJ3mwBkMhWA5KDQPuht1TSY%2Fo%2FQ3%2F4FvrZ9feiwPqE&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIA43LUKFW26KK7WZII%2F20231011%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20231011T020733Z&X-Amz-SignedHeaders=host&X-Amz-Expires=900&X-Amz-Signature=965c27e56676c2fdb4faf6e9ffe0e5faf85c99ea35ff9bbb43d412fd474566e7","image_2":"https:\/\/fw-pvt.s3.ap-southeast-1.amazonaws.com\/asd.jpg?response-content-disposition=inline%3B%20filename%3D%22asd.jpg%22&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=FwoGZXIvYXdzEOz%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaDAtkIB%2BIn4hnb45oQCKxAcNh7v1APMKOoTTRGYEB4J1m%2BaopiotnEf3HjArLkM6JFITf13UOMU6paSHGznNBNpRbBDQnePNMJ3rxMeqBZbiWI%2B3agqy1YC7yPivc%2Fged63tJ8eFRH5Gaf159oMdfGFitVuLaRBDTwPAiF2BU6eeUV2aOmt6vcVD%2FOebWETzMhMkyXaNxEBm6yZB7qRs4ba5XDXbUhb5zu3h5NOb7EVrTqw2WLPA2nSlHWLFfKc%2FKAyjlhpipBjItBshw2iqunZhNgibdUroJ3mwBkMhWA5KDQPuht1TSY%2Fo%2FQ3%2F4FvrZ9feiwPqE&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIA43LUKFW26KK7WZII%2F20231011%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20231011T020733Z&X-Amz-SignedHeaders=host&X-Amz-Expires=900&X-Amz-Signature=965c27e56676c2fdb4faf6e9ffe0e5faf85c99ea35ff9bbb43d412fd474566e7","self_image":"https:\/\/fw-pvt.s3.ap-southeast-1.amazonaws.com\/asd.jpg?response-content-disposition=inline%3B%20filename%3D%22asd.jpg%22&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=FwoGZXIvYXdzEOz%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaDAtkIB%2BIn4hnb45oQCKxAcNh7v1APMKOoTTRGYEB4J1m%2BaopiotnEf3HjArLkM6JFITf13UOMU6paSHGznNBNpRbBDQnePNMJ3rxMeqBZbiWI%2B3agqy1YC7yPivc%2Fged63tJ8eFRH5Gaf159oMdfGFitVuLaRBDTwPAiF2BU6eeUV2aOmt6vcVD%2FOebWETzMhMkyXaNxEBm6yZB7qRs4ba5XDXbUhb5zu3h5NOb7EVrTqw2WLPA2nSlHWLFfKc%2FKAyjlhpipBjItBshw2iqunZhNgibdUroJ3mwBkMhWA5KDQPuht1TSY%2Fo%2FQ3%2F4FvrZ9feiwPqE&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIA43LUKFW26KK7WZII%2F20231011%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20231011T020733Z&X-Amz-SignedHeaders=host&X-Amz-Expires=900&X-Amz-Signature=965c27e56676c2fdb4faf6e9ffe0e5faf85c99ea35ff9bbb43d412fd474566e7","document":null,"status_display":"Approved","status":"successful","remarks":null,"approved_at":"03\/10\/2023 12:38:05"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2t5Yy9nZXQtdXNlci1reWMtZGV0IiwiaWF0IjoxNjk2OTg5NDM4LCJleHAiOjE2OTY5OTM2NTMsIm5iZiI6MTY5Njk5MDA1MywianRpIjoicmNmRE5RMHFlRnNiTkNsYiIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.0M5sfNgB9mGQMDr1dGKc19zuNfn-LZUKB_dTW5wt8HQ","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"1.453057050705 sec","log_id":"ab750986-f7d7-4b87-a6cc-5907832afe8e"}
     * ##docs end##
     */
    public function getUserKycDet(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|int|exists:kyc,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $getUserKycDet = Kyc::getUserKycDet($validator->validated());
        $data['data'] = $getUserKycDet;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = kyc/update-kyc
     * @module = admin
     * @path = kyc/update-kyc
     * @permissionName = KYC List
     * @menuType = sub_menu
     * @parent = KYC
     * @method = POST
     * @description = Update user kyc status.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = id|<id>|int|required|1|Get specific details.
     * @body = status|<status>|string|required|1|KYC status. ("dropdown": "kyc_status")
     * @body = remark|<remark>|string|optional|invalid ic|Remark.
     * @response = {"status":true,"message":"Updated Successfully.","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20va3ljL3VwZGF0ZS1reWMiLCJpYXQiOjE2NjMxNDM0NDgsImV4cCI6MTY2MzE0NzA1MywibmJmIjoxNjYzMTQzNDUzLCJqdGkiOiJwZGo4UHRsN0FvR1RVelRWIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.gZBu4942C12xJn1Dew_m7jDCNO1KucINIiDjIydWGOY","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.011425018310547 sec","log_id":"a563f140-2c86-448c-876a-1d576c0daa80"}
     * ##docs end##
     */
    public function updateKyc(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "id" => [
                "required",
                "int",
                "exists:kyc,id,status," . Kyc::$kycStatus['pending'],
                function ($q, $value, $fail) use ($request) {
                    $kycRes = Kyc::find($value);
                    if(isset($kycRes)){
                        $duplicateIC = Kyc::where('ic_no', $kycRes->ic_no)->whereNot('id', $value)->where('status', Kyc::$kycStatus['successful'])->first();
                        if(isset($duplicateIC)){
                            return $fail("Duplicated record");
                        }
                    }
                    
                }
            ],
            "status" => [
                "required",
                "string",
                "in:" . implode(",", array_keys(Kyc::$kycStatus)),
                function ($q, $value, $fail) use ($request) {
                    $status = $request->status;
                    if ($status == 'approved') {
                        $res = Kyc::where(['status' => Kyc::$kycStatus['pending'], "id" => $request->id])
                            ->whereNotNull('image_1')
                            ->whereNotNull('self_image')
                            ->where(function ($query) {
                                $query->whereRaw('(CASE WHEN type = ' . Kyc::$kycType['ic'] . ' THEN image_2 IS NOT NULL ELSE image_2 IS NULL END)');
                            })
                            ->first();
                        if (empty($res)) {
                            $fail('Image not found');
                        }
                    }
                }
            ],
            "remark" => "required_if:status,==,declined|string|max:255|nullable",
        ],[
            'remark.required_if' => Lang::get('lang.kyc-remark-required')
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $updateKyc = Kyc::updateKyc($validator->validated());

        abort(200, Lang::get('lang.admin-update-success'));
    }
}
