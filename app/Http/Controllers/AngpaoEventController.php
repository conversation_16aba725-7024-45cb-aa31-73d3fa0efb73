<?php

namespace App\Http\Controllers;

use App\Models\AngpauEvent;
use App\Models\UserTicketReward;
use App\Traits\DecimalTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class AngpaoEventController extends Controller
{
    /**
     * Get list of all angpao events
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAngpaoEventList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:100',
            'search' => 'nullable|string',
            'order_by' => 'nullable|string|in:id,name,start_date,end_date,created_at',
            'order_sort' => 'nullable|string|in:asc,desc',
            'date_from' => 'nullable|date_format:Y-m-d|required_with:date_to',
            'date_to' => 'nullable|date_format:Y-m-d|required_with:date_from|after_or_equal:date_from',
            'event_date_from' => 'nullable|date_format:Y-m-d|required_with:event_date_to',
            'event_date_to' => 'nullable|date_format:Y-m-d|required_with:event_date_from|after_or_equal:event_date_from',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        $page = $request->page ?? 1;
        $limit = $request->limit ?? 10;
        $search = $request->search ?? '';
        $orderBy = $request->order_by ?? 'created_at';
        $orderSort = $request->order_sort ?? 'desc';
        $dateFrom = $request->date_from ?? null;
        $dateTo = $request->date_to ?? null;
        $eventDateFrom = $request->event_date_from ?? null;
        $eventDateTo = $request->event_date_to ?? null;

        $query = AngpauEvent::query();

        if (!empty($search)) {
            $query->where('name', 'like', "%{$search}%");
        }

        if ($dateFrom && $dateTo) {
            $query->whereDate('created_at', '>=', $dateFrom)
                  ->whereDate('created_at', '<=', $dateTo);
        }

        if ($eventDateFrom && $eventDateTo) {
            $query->where(function($q) use ($eventDateFrom, $eventDateTo) {
                $q->where(function($q) use ($eventDateFrom, $eventDateTo) {
                    $q->whereDate('start_date', '>=', $eventDateFrom)
                      ->whereDate('start_date', '<=', $eventDateTo);
                })
                ->orWhere(function($q) use ($eventDateFrom, $eventDateTo) {
                    $q->whereDate('end_date', '>=', $eventDateFrom)
                      ->whereDate('end_date', '<=', $eventDateTo);
                })
                ->orWhere(function($q) use ($eventDateFrom, $eventDateTo) {
                    $q->whereDate('start_date', '<=', $eventDateFrom)
                      ->whereDate('end_date', '>=', $eventDateTo);
                });
            });
        }

        $total = $query->count();

        $events = $query->orderBy($orderBy, $orderSort)
            ->skip(($page - 1) * $limit)
            ->take($limit)
            ->get();

        $events = $events->map(function ($event) {

            $participantCount = UserTicketReward::where('ref_event_id', $event->id)
                ->count();

            return [
                'id' => $event->id,
                'name' => $event->name,
                'start_date' => $event->start_date,
                'end_date' => $event->end_date,
                'total_participants' => $participantCount,
                'pool_amount' => DecimalTrait::setDecimal($event->pool_amount),
                'pool_amount_display' => DecimalTrait::setDecimal($event->pool_amount_display),
                'min_amount' => DecimalTrait::setDecimal($event->min_amount),
                'max_amount' => DecimalTrait::setDecimal($event->max_amount),
                'min_ticket' => $event->min_ticket,
                'max_winners' => $event->max_winners,
                'is_active' => $event->is_active,
                'created_at' => $event->created_at,
            ];
        });

        $pagination = [
            'current_page' => (int)$page,
            'from' => ($page - 1) * $limit + 1,
            'last_page' => ceil($total / $limit),
            'per_page' => (int)$limit,
            'to' => min($page * $limit, $total),
            'total' => $total
        ];

        return response()->json([
            'list' => $events,
            'pagination' => $pagination,
            'meta' => null,
            'status' => true,
            'timezone' => config('app.timezone'),
            'environment' => config('app.env'),
            'execution_duration' => number_format(microtime(true) - LARAVEL_START, 15) . ' sec',
            'log_id' => (string) \Illuminate\Support\Str::uuid(),
            'valid_version' => false
        ], 200);
    }

    /**
     * Get angpao event detail
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAngpaoEventDetail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer|exists:angpau_events,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        $event = AngpauEvent::find($request->id);

        $participantCount = UserTicketReward::where('ref_event_id', $event->id)
            ->count();

        $data = [
            'id' => $event->id,
            'name' => $event->name,
            'start_date' => $event->start_date,
            'end_date' => $event->end_date,
            'total_participants' => $participantCount,
            'pool_amount' => DecimalTrait::setDecimal($event->pool_amount),
            'pool_amount_display' => DecimalTrait::setDecimal($event->pool_amount_display),
            'current_pool_amount' => DecimalTrait::setDecimal($event->current_pool_amount),
            'min_amount' => DecimalTrait::setDecimal($event->min_amount),
            'max_amount' => DecimalTrait::setDecimal($event->max_amount),
            'min_ticket' => $event->min_ticket,
            'max_winners' => $event->max_winners,
            'is_active' => $event->is_active,
            'created_at' => $event->created_at,
        ];

        return response()->json([
            'data' => $data,
            'meta' => null,
            'status' => true,
            'timezone' => config('app.timezone'),
            'environment' => config('app.env'),
            'execution_duration' => number_format(microtime(true) - LARAVEL_START, 15) . ' sec',
            'log_id' => (string) \Illuminate\Support\Str::uuid(),
            'valid_version' => false
        ], 200);
    }

    /**
     * Create a new angpao event
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createAngpaoEvent(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after:start_date',
            'min_amount' => 'required|numeric|min:0',
            'max_amount' => 'required|numeric|gte:min_amount',
            'min_ticket' => 'required|integer|min:1',
            'max_winners' => 'required|integer|min:1',
            'is_active' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        $overlappingEvents = AngpauEvent::where(function ($query) use ($request) {
            $query->where(function ($q) use ($request) {
                $q->where('start_date', '>=', $request->start_date)
                    ->where('start_date', '<', $request->end_date);
            })->orWhere(function ($q) use ($request) {
                $q->where('end_date', '>', $request->start_date)
                    ->where('end_date', '<=', $request->end_date);
            })->orWhere(function ($q) use ($request) {
                $q->where('start_date', '<=', $request->start_date)
                    ->where('end_date', '>=', $request->end_date);
            })->orWhere(function ($q) use ($request) {
                $q->where('start_date', '>=', $request->start_date)
                    ->where('end_date', '<=', $request->end_date);
            });
        })->get();

        if ($overlappingEvents->count() > 0) {
            $conflictingEvents = $overlappingEvents->map(function ($event) {
                return [
                    'id' => $event->id,
                    'name' => $event->name,
                    'start_date' => $event->start_date,
                    'end_date' => $event->end_date
                ];
            });

            return response()->json([
                'error' => 'The event dates overlap with existing events',
                'conflicting_events' => $conflictingEvents,
                'status' => false,
                'timezone' => config('app.timezone'),
                'environment' => config('app.env'),
                'execution_duration' => number_format(microtime(true) - LARAVEL_START, 15) . ' sec',
                'log_id' => (string) \Illuminate\Support\Str::uuid(),
                'valid_version' => false
            ], 400);
        }

        $event = AngpauEvent::create([
            'name' => $request->name,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'total_participants' => 0,
            'min_amount' => $request->min_amount,
            'max_amount' => $request->max_amount,
            'min_ticket' => $request->min_ticket,
            'max_winners' => $request->max_winners,
            'is_active' => $request->is_active,
        ]);

        $data = [
            'id' => $event->id,
            'name' => $event->name,
            'message' => 'Angpao event created successfully',
        ];

        return response()->json([
            'data' => $data,
            'meta' => null,
            'status' => true,
            'timezone' => config('app.timezone'),
            'environment' => config('app.env'),
            'execution_duration' => number_format(microtime(true) - LARAVEL_START, 15) . ' sec',
            'log_id' => (string) \Illuminate\Support\Str::uuid(),
            'valid_version' => false
        ], 200);
    }

    /**
     * Update an existing angpao event
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateAngpaoEvent(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer|exists:angpau_events,id',
            'name' => 'nullable|string|max:255',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after:start_date',
            'min_amount' => 'nullable|numeric|min:0',
            'max_amount' => 'nullable|numeric|gte:min_amount',
            'min_ticket' => 'nullable|integer|min:1',
            'max_winners' => 'nullable|integer|min:1',
            'is_active' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        $event = AngpauEvent::find($request->id);

        $updatedEvent = clone $event;
        if (isset($request->start_date)) $updatedEvent->start_date = $request->start_date;
        if (isset($request->end_date)) $updatedEvent->end_date = $request->end_date;

        if (isset($request->start_date) || isset($request->end_date)) {
            $overlappingEvents = AngpauEvent::where('id', '!=', $request->id)
                ->where(function ($query) use ($updatedEvent) {
                    $query->where(function ($q) use ($updatedEvent) {
                        $q->where('start_date', '>=', $updatedEvent->start_date)
                            ->where('start_date', '<', $updatedEvent->end_date);
                    })->orWhere(function ($q) use ($updatedEvent) {
                        $q->where('end_date', '>', $updatedEvent->start_date)
                            ->where('end_date', '<=', $updatedEvent->end_date);
                    })->orWhere(function ($q) use ($updatedEvent) {
                        $q->where('start_date', '<=', $updatedEvent->start_date)
                            ->where('end_date', '>=', $updatedEvent->end_date);
                    })->orWhere(function ($q) use ($updatedEvent) {
                        $q->where('start_date', '>=', $updatedEvent->start_date)
                            ->where('end_date', '<=', $updatedEvent->end_date);
                    });
                })->get();

            if ($overlappingEvents->count() > 0) {
                $conflictingEvents = $overlappingEvents->map(function ($event) {
                    return [
                        'id' => $event->id,
                        'name' => $event->name,
                        'start_date' => $event->start_date,
                        'end_date' => $event->end_date
                    ];
                });

                return response()->json([
                    'error' => 'The updated event dates overlap with existing events',
                    'conflicting_events' => $conflictingEvents,
                    'status' => false,
                    'timezone' => config('app.timezone'),
                    'environment' => config('app.env'),
                    'execution_duration' => number_format(microtime(true) - LARAVEL_START, 15) . ' sec',
                    'log_id' => (string) \Illuminate\Support\Str::uuid(),
                    'valid_version' => false
                ], 400);
            }
        }

        if (isset($request->name)) $event->name = $request->name;
        if (isset($request->start_date)) $event->start_date = $request->start_date;
        if (isset($request->end_date)) $event->end_date = $request->end_date;
        if (isset($request->min_amount)) $event->min_amount = $request->min_amount;
        if (isset($request->max_amount)) $event->max_amount = $request->max_amount;
        if (isset($request->min_ticket)) $event->min_ticket = $request->min_ticket;
        if (isset($request->max_winners)) $event->max_winners = $request->max_winners;
        if (isset($request->is_active)) $event->is_active = $request->is_active;

        $event->save();

        $data = [
            'id' => $event->id,
            'name' => $event->name,
            'message' => 'Angpao event updated successfully',
        ];

        return response()->json([
            'data' => $data,
            'meta' => null,
            'status' => true,
            'timezone' => config('app.timezone'),
            'environment' => config('app.env'),
            'execution_duration' => number_format(microtime(true) - LARAVEL_START, 15) . ' sec',
            'log_id' => (string) \Illuminate\Support\Str::uuid(),
            'valid_version' => false
        ], 200);
    }

    /**
     * Get participants list for an angpao event
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getParticipantsList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|integer|exists:angpau_events,id',
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:100',
            'search' => 'nullable|string',
            'order_by' => 'nullable|string|in:id,name,total_token,total_deposit,created_at',
            'order_sort' => 'nullable|string|in:asc,desc',
            'see_all' => 'integer|in:1,0|nullable',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string|nullable',
            'file_name' => 'required_if:export,==,1',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        $requestData = $request->all();
        $requestData['user_id'] = $request->user()->id ?? null;

        if (isset($requestData['export']) && $requestData['export'] == 1) {
            $requestData['export_data'] = [
                'data' => [
                    'id' => 'ID',
                    'store_name' => 'Store Name',
                    'name' => 'Name',
                    'phone' => 'Phone',
                    'total_token' => 'Total Token',
                    'total_deposit' => 'Total Deposit',
                    'is_selected' => 'Is Selected',
                    'is_joined' => 'Is Joined',
                    'created_at' => 'Created At'
                ]
            ];
        }

        $result = AngpauEvent::getParticipantsList($requestData);

        return response()->json(array_merge($result, [
            'status' => true,
            'timezone' => config('app.timezone'),
            'environment' => config('app.env'),
            'execution_duration' => number_format(microtime(true) - LARAVEL_START, 15) . ' sec',
            'log_id' => (string) \Illuminate\Support\Str::uuid(),
            'valid_version' => false
        ]), 200);
    }



    /**
     * Select winners for an angpao event
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function selectWinners(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|integer|exists:angpau_events,id',
            'winner_ids' => 'required|array',
            'winner_ids.*' => 'required|integer|exists:user_ticket_rewards,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        try {
            $event = AngpauEvent::findOrFail($request->event_id);

            $invalidEventUsers = UserTicketReward::whereIn('id', $request->winner_ids)
                ->where('ref_event_id', '!=', $request->event_id)
                ->get();

            if ($invalidEventUsers->count() > 0) {
                $invalidIds = $invalidEventUsers->pluck('id')->toArray();
                return response()->json([
                    'error' => 'Some selected users do not belong to this event. User IDs: ' . implode(', ', $invalidIds),
                    'status' => false,
                    'timezone' => config('app.timezone'),
                    'environment' => config('app.env'),
                    'execution_duration' => number_format(microtime(true) - LARAVEL_START, 15) . ' sec',
                    'log_id' => (string) \Illuminate\Support\Str::uuid(),
                    'valid_version' => false
                ], 400);
            }

            $currentWinnersCount = UserTicketReward::where('ref_event_id', $request->event_id)
                ->where('is_selected', true)
                ->whereNotIn('id', $request->winner_ids)
                ->count();

            $totalWinnersAfterUpdate = $currentWinnersCount + count($request->winner_ids);

            if ($totalWinnersAfterUpdate > $event->max_winners) {
                return response()->json([
                    'error' => 'The total number of winners (' . $totalWinnersAfterUpdate . ') would exceed the maximum allowed (' . $event->max_winners . '). Current winners: ' . $currentWinnersCount . ', New winners: ' . count($request->winner_ids),
                    'status' => false,
                    'timezone' => config('app.timezone'),
                    'environment' => config('app.env'),
                    'execution_duration' => number_format(microtime(true) - LARAVEL_START, 15) . ' sec',
                    'log_id' => (string) \Illuminate\Support\Str::uuid(),
                    'valid_version' => false
                ], 400);
            }

            DB::beginTransaction();

            UserTicketReward::whereIn('id', $request->winner_ids)
                ->update(['is_selected' => true]);

            DB::commit();

            $winners = UserTicketReward::whereIn('id', $request->winner_ids)
                ->with(['user:id,name,username'])
                ->get()
                ->map(function ($winner) {
                    return [
                        'id' => $winner->id,
                        'user_id' => $winner->user_id,
                        'username' => $winner->user->username ?? $winner->name,
                        'name' => $winner->user->name ?? $winner->name,
                        'total_token' => $winner->total_token,
                    ];
                });

            $data = [
                'event_id' => $request->event_id,
                'winners_count' => count($request->winner_ids),
                'winners' => $winners,
                'message' => 'Winners selected successfully',
            ];

            return response()->json([
                'data' => $data,
                'meta' => null,
                'status' => true,
                'timezone' => config('app.timezone'),
                'environment' => config('app.env'),
                'execution_duration' => number_format(microtime(true) - LARAVEL_START, 15) . ' sec',
                'log_id' => (string) \Illuminate\Support\Str::uuid(),
                'valid_version' => false
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'error' => $e->getMessage(),
                'status' => false,
                'timezone' => config('app.timezone'),
                'environment' => config('app.env'),
                'execution_duration' => number_format(microtime(true) - LARAVEL_START, 15) . ' sec',
                'log_id' => (string) \Illuminate\Support\Str::uuid(),
                'valid_version' => false
            ], 400);
        }
    }
}
