<?php

namespace App\Http\Controllers;

use App\Models\Deposit;
use App\Models\RewardSpin;
use App\Models\RewardSpinLog;
use App\Models\User;
use App\Models\UserPromotion;
use App\Models\UserReward;
use App\Traits\DecimalTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class RewardSpinController extends Controller
{
    /**
     * Get list of lucky spin logs grouped by user, title, and date
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLuckySpinList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:100',
            'search' => 'nullable|string',
            'order_by' => 'nullable|string|in:created_at,user_name,title,value,is_free',
            'order_sort' => 'nullable|string|in:asc,desc',
            'start_date' => 'nullable|date_format:Y-m-d',
            'end_date' => 'nullable|date_format:Y-m-d|after_or_equal:start_date',
            'date_from' => 'nullable|date_format:Y-m-d|required_with:date_to',
            'date_to' => 'nullable|date_format:Y-m-d|required_with:date_from|after_or_equal:date_from',
            'is_free' => 'nullable|string|in:all,true,false',
            'see_all' => 'integer|in:1,0|nullable',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string|nullable',
            'file_name' => 'required_if:export,==,1',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        $requestData = $request->all();
        $requestData['user_id'] = $request->user()->id ?? null;

        // Handle export if requested
        if (isset($requestData['export']) && $requestData['export'] == 1) {
            if (! isset($requestData['export_data']) || ! isset($requestData['export_data']['data'])) {
                $requestData['export_data'] = [
                    'data' => [
                        'id' => 'ID',
                        'user_id' => 'User ID',
                        'user_name' => 'User Name',
                        'title' => 'Title',
                        'is_free' => 'Is Free',
                        'value' => 'Value',
                        'created_at' => 'Created At',
                    ],
                ];
            }
        }

        $result = RewardSpinLog::getLuckySpinList($requestData);

        return response()->json(array_merge($result, [
            'status' => true,
            'timezone' => config('app.timezone'),
            'environment' => config('app.env'),
            'execution_duration' => number_format(microtime(true) - LARAVEL_START, 15) . ' sec',
            'log_id' => (string) \Illuminate\Support\Str::uuid(),
            'valid_version' => false,
        ]), 200);
    }

    public function getSpunList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_by' => 'string',
            'order_sort' => 'string|in:asc,desc',
            'limit' => 'integer',
            'page' => 'integer',
            'see_all' => 'integer|in:0,1',
            'created_from' => 'required_with:created_to|string|date_format:Y-m-d',
            'created_to' => 'required_with:created_from|string|date_format:Y-m-d|after_or_equal:created_from',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $report = RewardSpinLog::getSpunList($validator->validated());
        $data = ['data' => $report];

        abort(200, json_encode($data));
    }

    public function getUserRewardList(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'order_by' => 'string',
            'order_sort' => 'string|in:asc,desc',
            'limit' => 'integer',
            'page' => 'integer',
            'see_all' => 'integer|in:0,1',
            'created_from' => 'required_with:created_to|string|date_format:Y-m-d',
            'created_to' => 'required_with:created_from|string|date_format:Y-m-d|after_or_equal:created_from',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $report = UserReward::getUserRewardList($validator->validated());
        $data = ['data' => $report];

        abort(200, json_encode($data));
    }

    public function getWinningLogs($query)
    {
        return $query
            ->with('user')
            ->where('status', 1)
            ->orderBy('created_at', 'DESC')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                $username = $item->user?->name ?? $item->name ?? '';
                $username = preg_replace('/[^A-Za-z0-9 ]/', '', $username);

                return [
                    'name' => substr($username ?? 'c', 0, 1) . '********' . substr($username ?? 's', -1),
                    'image' => isset($item->image) ? env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $item->image : null,
                    'won' => $item->won,
                    'created_at' => $item->created_at->format('Y-m-d'),
                ];
            });
    }

    public function getRewardSpin(Request $request)
    {
        $userId = auth()->user()?->id;
        $user_reward = UserReward::where('user_id', auth()->user()->id)->first();
        if (! isset($user_reward)) {
            $user_reward = UserReward::create([
                'user_id' => auth()->user()->id,
                'total_points' => 0,
                'total_claimed' => 0,
                'last_deposit_at' => null,
                'ref_deposit_id' => null,
                'status' => true,
            ]);
            // abort(400, json_encode(['User Reward not found']));
        }

        $data = [];
        $draw_points = 600;

        $gotDeposit = Deposit::where('user_id', $userId)->where('status', Deposit::$status['approved'])->whereDate('approved_at', Carbon::yesterday()->format('Y-m-d'))->exists() ?? false;
        $rewards = RewardSpin::where('status', true)->orderBy('priority', 'DESC')
            ->get()
            ->map(function ($item) use ($gotDeposit) {
                return [
                    'id' => $item->id,
                    'title' => $item->title,
                    'image' => isset($item->image) ? env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $item->image : null,
                    'percent' => $gotDeposit ? $item->percent_deposit : $item->percent,
                ];
            });

        // Fetch winning list
        $winning_list = $this->getWinningLogs(RewardSpinLog::query());

        // Fetch user's winning record
        $winning_record = $this->getWinningLogs(RewardSpinLog::where('user_id', auth()->user()->id));

        // Free Spin Chance
        $bool_deposit = $user_reward?->last_deposit_at && $user_reward->last_deposit_at->isToday();
        $bool_spin = isset($user_reward->last_spin_at) ? ! $user_reward->last_spin_at->isToday() : $user_reward?->last_spin_at == null;
        $free_spin_claimed = RewardSpinLog::isFreeClaimedByUserId($userId);
        $free_spin_chance = 0;

        if ($bool_deposit && $bool_spin) {
            $free_spin_chance += 1;
        }

        if (! $free_spin_claimed) {
            $free_spin_chance += 1;
        }

        $data['total_chance'] = ($user_reward->total_points / $draw_points) + $free_spin_chance;
        $data['is_spin_chance'] = (($user_reward->total_points / $draw_points) + $free_spin_chance) >= 1;

        $data['spins'] = $rewards;
        $data['winning_list'] = $winning_list;
        $data['winning_record'] = $winning_record;
        $data['prize_list'] = $rewards;

        $data['terms'] = '<p>Event: Daily Check In and Lucky Wheel Spin How To Claim: &bull;&#8288; &#8288;This promotion is available for Uwin members. &bull;&#8288; &#8288;Prizes won in the Lucky Wheel Spin will be issued directly into members main wallet. &bull;&#8288; &#8288;Members are required to verify their identity with their phone number and screenshot of the pop-up message for the Special Prize to Customer Service to claim the special prize. Terms &amp; Conditions: &bull;&#8288; &#8288;All member needs to deposit at least RM100 in shop or online deposit to get 1 free spin daily, the free spin will not accumulate and will expire/ be forfeited the next day. &bull;&#8288; &#8288;600 points will be automatically converted to one (1) additional spin. The total points displayed on the daily mission page will only be deducted after members used up their daily login spins. &bull;&#8288; &#8288;There will be no expiration nor max cap for the activity points earned. &bull;&#8288; &#8288;Physical prizes, if applicable, are expected to be received by winners via their registered shop.  &bull;&#8288; &#8288;Prizes are subject to availability and photos are for illustration purposes only. &bull;&#8288; &#8288;Members who have not made a deposit or have a history risk record will be ineligible to participate in this promotion. &bull;&#8288; &#8288;Participating members must accept and comply with all the terms mentioned above as well as all relevant and rules and regulations stated on the Uwin Apps. &bull;&#8288; &#8288;Uwin may at its discretion disqualify/reject any Eligible Participant who does not comply with the Terms and Conditions stated herein and/or is found or suspected of tampering with the Campaign and/or its process or the operation of the Campaign. Tampering shall include fraudulent activities involving any act of deceit and/or deception and/or cheating with regards to the Campaign and/or the eligible channel.</p>';

        abort(200, json_encode([
            'data' => $data,
        ]));
    }

    public function spinReward(Request $request)
    {
        $draw_points = 600;
        $user_id = auth()->user()->id;

        $free_spin_claimed = RewardSpinLog::isFreeClaimedByUserId($user_id);
        $reward = RewardSpin::spin($user_id, ! $free_spin_claimed);
        if (! isset($reward) && $reward == null) {
            abort(400, json_encode('Reward not found'));
        }

        $user_reward = UserReward::where('user_id', auth()->user()->id)->first();
        if (! isset($user_reward) && $user_reward == null) {
            abort(400, json_encode('User Reward not found'));
        }

        $bool_deposit = $user_reward?->last_deposit_at && $user_reward->last_deposit_at->isToday();
        $bool_spin = isset($user_reward->last_spin_at) ? ! $user_reward->last_spin_at->isToday() : $user_reward?->last_spin_at == null;
        $free_spin_chance = 0;

        if ($bool_deposit && $bool_spin) {
            $free_spin_chance += 1;
        }

        if (! $free_spin_claimed) {
            $free_spin_chance += 1;
        }

        if ($free_spin_chance == 0 && $user_reward->total_points < $draw_points) {
            abort(400, json_encode('Not enough points'));
        }

        DB::transaction(function () use ($user_id, $reward, $user_reward, $draw_points, $free_spin_chance, $free_spin_claimed) {

            if ($free_spin_chance == 0) {
                $user_reward->update([
                    'total_points' => $user_reward->total_points - $draw_points,
                    'total_spin' => $user_reward->total_spin + 1,
                ]);
            } else {
                $user_reward->update([
                    'last_spin_at' => now(),
                    'total_spin' => $user_reward->total_spin + 1,
                ]);
            }

            if (array_key_exists('promotion', $reward)) {
                if ($reward['increment']) {
                    $userPromotion = UserPromotion::getUserPromotionByUserId($user_id);
                    $userPromotion->increment('bonus_amount', $reward['value']);
                } else {
                    UserPromotion::applyPromotion($reward['promotion']->id, $reward['value']);
                }
            }

            RewardSpinLog::create([
                'reward_spin_id' => $reward['id'],
                'user_id' => $user_id,
                'reward_type' => $reward['reward_type'],
                'value' => $reward['value'] ?? '',
                'is_free' => ! $free_spin_claimed,
            ]);

            if ($reward['reward_type'] == RewardSpin::$reward_type['point']) {
                $user_reward->update([
                    'total_points' => $user_reward->total_points + $reward['value'],
                ]);
            }

            if ($reward['reward_type'] == RewardSpin::$reward_type['cash']) {
                // Deposit
                User::getFreeCredit($user_id, $reward['value']);
            }
        });

        $angpau = array_key_exists('promotion', $reward) ? [
            'message' => 'Congrats!',
            'amount' => (string) $reward['value'],
            'promotion' => $reward['promotion'],
        ] : null;

        abort(200, json_encode([
            'message' => 'Congrats! You won ' . $reward['value'] . ' ' . ($reward['reward_type'] == RewardSpin::$reward_type['point'] ? 'Points' : ''),
            'data' => [
                'id' => $reward['id'],
                'title' => $reward['title'],
                'image' => $reward['image'],
                'reward_type' => $reward['reward_type'],
                'value' => DecimalTrait::setDecimal($reward['value']),
                'angpau' => $angpau,
            ],
        ]));
    }
}
