<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Validation\Rules\Password;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    public static function passwordRule($data = [], $type = ''){
        $res = [
            'string',
            Password::min(8)->letters()->numbers()->mixedCase()->symbols(),
            'confirmed',
        ];

        $res = array_merge($res, $data);

        return $res;
    }

    public static function userPasswordRule($data = [], $type = ''){
        $res = [
            'string',
            Password::min(8)->letters()->numbers(),
            'max:20',
            'regex:/^[a-zA-Z0-9]*$/',
            // 'confirmed',
        ];

        $res = array_merge($res, $data);

        return $res;
    }

    public static function transactionPasswordRule($data = [], $type = ''){
        $res = [
            'string',
            Password::min(6)->numbers(),
            'confirmed',
        ];

        $res = array_merge($res, $data);

        return $res;
    }

    public static function imageRule($data = [], $type = ''){
        $res = [
            'mimes:jpeg,png',
            'max:10000',
        ];

        $res = array_merge($res, $data);

        return $res;
    }
}
