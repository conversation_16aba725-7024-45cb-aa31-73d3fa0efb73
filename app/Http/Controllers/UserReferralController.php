<?php

namespace App\Http\Controllers;

use App\Http\Resources\ItemsCollection;
use App\Models\Credit;
use App\Models\CreditTransaction;
use App\Models\ReferralTier;
use App\Models\Store;
use App\Models\TreeSponsor;
use App\Models\User;
use App\Models\UserReferral;
use App\Models\UserReferralBonus;
use App\Models\UserReferralSummary;
use App\Traits\DecimalTrait;
use App\Traits\GenerateNumberTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;

class UserReferralController extends Controller
{
    public function getUserReferral(Request $request)
    {
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => auth()->user()?->id ?? null]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();
        $isAgent = auth()->user()?->is_agent ?? false;
        $userId = $params['user_id'];
        $referralCode = User::find($userId)?->referral_code ?? '';
        $referralLink = env('APP_MEMBER_URL').'?referral='.$referralCode;

        $downlines = TreeSponsor::getDownlineUserIdsByUserId($userId);
        $pendingClaimAmount = UserReferral::getPendingClaimAmountByUserId($userId);
        $pendingClaimTierAmount = UserReferral::getPendingClaimTierBonusAmountByUserId($userId);
        $totalBonusClaimed = UserReferral::getTotalBonusClaimedByUserId($userId);
        $currentTierProgress = UserReferralSummary::getTotalDownlineTurnoverByUserId($userId);
        $claimTiers = UserReferral::getClaimedTiersByUserId($userId);

        $invitationAccepted = count($downlines);
        $canClaim = $pendingClaimAmount > 0;
        $canClaimTierBonus = $pendingClaimTierAmount > 0;

        $tiers = ReferralTier::getReferralTiers()
            ->map(function ($e) use ($claimTiers) {
                return [
                    'name' => $e->name,
                    'target_amount' => (float) $e->target_amount,
                    'reward_amount' => (float) $e->reward_amount,
                    'is_claimed' => in_array($e->id, $claimTiers),
                ];
            });

        $data = [
            'show_history' => true,
            'can_claim' => $canClaim,
            'pending_claim_amount' => DecimalTrait::setDecimal($pendingClaimAmount),
            'referral_link' => $referralLink,
            'invitation_accepted' => $invitationAccepted,
            'total_bonus_claimed' => DecimalTrait::setDecimal($totalBonusClaimed),
            'referral_tier_bonus' => $isAgent ? null : [
                'referral_info' => Lang::has('langReward.referral_info') ? Lang::get('langReward.referral_info') : '',
                'current_progress' => DecimalTrait::setDecimal($currentTierProgress),
                'can_claim' => $canClaimTierBonus,
                'tiers' => $tiers,
            ],
            'referral_info' => Lang::has('langReward.referral_info') ? Lang::get('langReward.referral_info') : '',
            'terms' => null, // Lang::has('langReward.referral_terms') ? Lang::get('langReward.referral_terms') : '',
        ];

        abort(200, json_encode(['data' => $data]));
    }

    public function claimReferralBonus(Request $request)
    {
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => auth()->user()?->id ?? null]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();
        $userId = $params['user_id'];

        $pendingClaimAmount = UserReferral::getPendingClaimAmountByUserId($userId);
        $pendingClaimUserReferrals = UserReferral::getPendingClaimUserReferralsByUserId($userId);

        if (count($pendingClaimUserReferrals) == 0 || $pendingClaimAmount == 0) {
            abort(400, json_encode('Nothing to be claimed'));
        }

        DB::transaction(function () use ($pendingClaimUserReferrals, $pendingClaimAmount, $userId) {
            $pendingClaimUserReferrals->each(function ($e) {
                $e->update([
                    'is_claimed' => true,
                    'claimed_at' => now(),
                ]);
            });

            UserReferralBonus::create([
                'user_id' => $userId,
                'amount' => $pendingClaimAmount,
                'status' => UserReferralBonus::$status['pending'],
            ]);
        });

        $data = [
            'pending_claim_amount' => DecimalTrait::setDecimal(0),
            'can_claim' => false,
            'type' => UserReferralBonus::$type['rebate'],
        ];

        abort(200, json_encode(['data' => $data]));
    }

    public function claimReferralTierBonus(Request $request)
    {
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => auth()->user()?->id ?? null]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();
        $userId = $params['user_id'];

        $pendingClaimAmount = UserReferral::getPendingClaimTierBonusAmountByUserId($userId);
        $pendingClaimUserReferrals = UserReferral::getPendingClaimTierBonusByUserId($userId);

        if (count($pendingClaimUserReferrals) == 0 || $pendingClaimAmount == 0) {
            abort(400, json_encode('Nothing to be claimed'));
        }

        DB::transaction(function () use ($pendingClaimUserReferrals, $pendingClaimAmount, $userId) {
            $pendingClaimUserReferrals->each(function ($e) {
                $e->update([
                    'is_claimed' => true,
                    'claimed_at' => now(),
                ]);
            });

            $internalID = User::select('id')->where('username', 'creditSales')->where('user_type', User::$userType['internal-account'])->first()->id ?? null;
            if (empty($internalID)) {
                throw new \Exception('Invalid Internal Account.', 400);
            }
            $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;
            $batchId = $belongId;
            $creditName = Credit::first()->name;

            CreditTransaction::insertTransaction($internalID, $userId, $userId, $creditName, $pendingClaimAmount, 'referral-payout', $belongId, $batchId, null, now(), null, null, null);

            UserReferralBonus::create([
                'user_id' => $userId,
                'amount' => $pendingClaimAmount,
                'type' => UserReferralBonus::$type['tier'],
                'status' => UserReferralBonus::$status['approved'],
            ]);
        });

        $data = [
            'can_claim' => false,
        ];

        abort(200, json_encode(['data' => $data]));
    }

    public function getUserReferralBonus(Request $request)
    {
        $request->request->add(['user_id' => auth()->user()?->id ?? null]);

        $validator = Validator::make($request->all(), [
            'user_id' => 'integer',
            'from_date' => 'nullable|string|date_format:Y-m-d',
            'to_date' => 'nullable|string|date_format:Y-m-d|after_or_equal:from_date',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();
        $userId = $params['user_id'];
        $fromDate = $params['from_date'] ?? null;
        $toDate = $params['to_date'] ?? null;

        $data['list'] = UserReferralBonus::getUserReferralBonusByUserId($userId, $fromDate, $toDate);

        abort(200, json_encode(['data' => $data]));
    }

    public function getUserReferralBonusById(Request $request)
    {
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => auth()->user()?->id ?? null]);
        }

        $validator = Validator::make($request->all(), [
            'id' => 'required|integer|exists:user_referral_bonuses,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $bonus = UserReferralBonus::getById($request->id);

        abort(200, json_encode(['data' => $bonus]));
    }

    public function getUserReferralList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'integer',
            'limit' => 'integer',
            'order_by' => 'string',
            'order_sort' => 'string',
            'username' => 'string|nullable',
            'phone_no' => 'string|nullable',
            'branch_id' => 'string|nullable',
            'user_id' => 'integer|nullable',
            'status' => 'integer|nullable|in:'.implode(',', array_values(UserReferralBonus::$status)),
            'type' => 'integer|nullable|in:'.implode(',', array_values(UserReferralBonus::$type)),
            'from_date' => 'string|date_format:Y-m-d',
            'to_date' => 'string|date_format:Y-m-d|after_or_equal:from_date',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();
        $username = $params['username'] ?? null;
        $phoneNo = $params['phone_no'] ?? null;
        $branchId = $params['branch_id'] ?? null;
        $userId = $params['user_id'] ?? null;
        $status = $params['status'] ?? null;
        $type = $params['type'] ?? null;
        $fromDate = $params['from_date'] ?? null;
        $toDate = $params['to_date'] ?? null;
        $seeAll = $params['see_all'] ?? null;

        $sortableColumns = ['id', 'status', 'created_at'];

        // Order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // Sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = ($seeAll == 1) ? null : ($params['limit'] ?? config('app.pagination_rows'));

        $items = UserReferralBonus::query()
            ->with('user')
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('created_at'), '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where(DB::raw('created_at'), '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(isset($userId), function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })
            ->when(isset($status), function ($q) use ($status) {
                $q->where('status', $status);
            })
            ->when(isset($type), function ($q) use ($type) {
                $q->where('type', $type);
            })
            ->when(isset($username), function ($query) use ($username) {
                $query->whereRelation('user', 'username', 'LIKE', "%$username%");
            })
            ->when(isset($phoneNo), function ($query) use ($phoneNo) {
                $phoneNo = preg_replace('/[^0-9]/', '', $phoneNo);
                $query->whereRelation('user', 'phone_no', 'LIKE', "%$phoneNo%");
            })
            ->when(isset($branchId), function ($query) use ($branchId) {
                $query->whereRelation('user.store', 'store_id', $branchId);
            })
            ->orderBy($order_by, $order_sort)
            ->when((! isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $mapFunc = function ($q) {
            return [
                'id' => $q->id,
                'user_id' => $q->user->id,
                'username' => $q->user->username,
                'phone_no' => $q->user->phone_no,
                'branch' => $q->user->store->name,
                'amount' => DecimalTrait::setDecimal($q->amount),
                'status' => $q->status,
                'type' => $q->type,
                'created_at' => $q->created_at,
                'updated_at' => $q->updated_at,
                'updated_by' => $q->updated_by,
            ];
        };

        $dropdown['status'] = array_map(function ($k, $v) {
            return [
                'id' => $v,
                'name' => $k,
            ];
        }, array_keys(UserReferralBonus::$status), UserReferralBonus::$status);

        $stores = Store::all()->map(function ($e) {
            return [
                'id' => $e->store_id,
                'name' => $e->name,
            ];
        });
        $dropdown['branch'] = $stores;

        if (($seeAll == 1)) {
            $data['list'] = $items->get()->map($mapFunc)->toArray();
        } else {
            $items->getCollection()->transform($mapFunc);
            $data = (new ItemsCollection($items))->toArray();
        }

        abort(200, json_encode(['success' => true, 'dropdown' => $dropdown, 'data' => $data]));
    }

    public function approveUserReferral(Request $request)
    {
        $request->request->add(['user_id' => auth()->user()?->id ?? null]);

        $validator = Validator::make($request->all(), [
            'id' => 'required|integer|exists:user_referral_bonuses,id',
            'user_id' => 'nullable|integer|exists:users,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();
        $id = $params['id'];
        $userId = $params['user_id'] ?? null;

        $data = UserReferralBonus::approve($id, $userId);

        abort(200, json_encode(['data' => $data]));
    }

    public function rejectUserReferral(Request $request)
    {
        $request->request->add(['user_id' => auth()->user()?->id ?? null]);

        $validator = Validator::make($request->all(), [
            'id' => 'required|integer|exists:user_referral_bonuses,id',
            'user_id' => 'nullable|integer|exists:users,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();
        $id = $params['id'];
        $userId = $params['user_id'] ?? null;

        $data = UserReferralBonus::reject($id, $userId);

        abort(200, json_encode(['data' => $data]));
    }
}
