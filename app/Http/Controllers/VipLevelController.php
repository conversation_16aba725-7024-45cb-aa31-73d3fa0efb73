<?php

namespace App\Http\Controllers;

use App\Models\UserLevel;
use App\Models\VipLevel;
use Illuminate\Support\Facades\Lang;

class VipLevelController extends Controller
{
    public function getVipLevels()
    {
        $userId = auth()->user()->id;

        $data['vip'] = UserLevel::getVipByUserIdOrCreateIfNull($userId);
        $data['vip_levels'] = VipLevel::getVipLevels();
        $data['how_to_get'] = Lang::has('langReward.vip-how-to-get') ? Lang::get('langReward.vip-how-to-get') : '';
        $data['terms'] = Lang::has('langReward.vip-terms') ? Lang::get('langReward.vip-terms') : '';
        $data['image'] = 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/vip/claim_vip_img.png';

        abort(200, json_encode(['data' => $data]));
    }

    public function getVipLevelList()
    {
        $vipLevels = VipLevel::getVipLevels();

        abort(200, json_encode(['data' => $vipLevels]));
    }
}
