<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Lang;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use App\Models;
use App\Models\Document;

class DocumentController extends Controller
{
    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = document/add
     * @module = admin
     * @path = document/add
     * @permissionName = Add document
     * @menuType = api
     * @parent = document
     * @method = POST
     * @description = To add document.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = type|<type>|string|required|document|Document's Type. (dropdown: document_type)
     * @body = document|<array>|array|required|{"title": "test document", "attachment": "staging/test", "language": "en"}|Document Data. 
     * @body = document.*.title|<title>|string|required|test document|Document's title. 
     * @body = document.*.attachment|<attachment>|string|required|staging/test|Document's Attachment.
     * @body = document.*.language_type|<language_type>|string|required|en|Document's language. (dropdown: language)
     * @body = start_date|<start_date>|string|optional|2022-08-28|Document's activate date.
     * @body = end_date|<end_date>|string|optional|2022-08-28|Document's activate date.
     * @body = status|<status>|string|active|active|Document's status. (dropdown: document_status)

     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmMxcTIuY29tL2RvY3VtZW50L2FkZCIsImlhdCI6MTY3MjczOTc2NiwiZXhwIjoxNjcyNzQ2NTk3LCJuYmYiOjE2NzI3NDI5OTcsImp0aSI6IjI2WmdPNVp5NThjUXlXWUsiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.tX7qI3nGAiHSijbS3_488JwZTlJ-r1x8aCxsjOeZ_hg","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.079342126846313 sec","log_id":"90f50afc-e493-40b6-9f51-f9d0b6ea454e"}
     * ##docs end##
     */
    public function addDocument(Request $request){
        if(MODULE == 'user'){
            abort(400, json_encode('Invalid Access'));
        }

        $validator = Validator::make($request->all(), [
            "type" => "required|in:" . implode(",", array_keys(Document::$type)),

            "document" => "required|array",
            "document.*.title" => "required|string",
            "document.*.attachment" => "required|string",
            "document.*.language_type" => "required|distinct|string|in:" . implode(",", array_keys(config("language"))),

            "start_date" => "required_with:end_date|nullable|string|date_format:Y-m-d|after_or_equal:today",
            "end_date" => "required_with:start_date|nullable|string|date_format:Y-m-d|after_or_equal:start_date",
            "status" => "required|string|in:" . implode(",", array_keys(Document::$status)),
        ],[
            'type.required' => Lang::get('lang.input-field-required-error'),
            'document.required' => Lang::get('lang.input-field-required-error'),
            'document.*.title.required' => Lang::get('lang.input-field-required-error'),
            'document.*.attachment.required' => Lang::get('lang.input-field-required-error'),
            'document.*.language_type.required' => Lang::get('lang.input-field-required-error'),
            'start_date.required_with' => Lang::get('lang.input-field-required-error'),
            'end_date.required_with' => Lang::get('lang.input-field-required-error'),
            'status.required' => Lang::get('lang.input-field-required-error'),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\document::addDocument($validator->validated());

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = document/edit
     * @module = admin
     * @path = document/edit
     * @permissionName = Edit document
     * @menuType = api
     * @parent = document
     * @method = POST
     * @description = To edit document.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|required|1|Document's id.
     * @body = type|<type>|string|required|document|Document's Type. (dropdown: document_type)
     * @body = document|<array>|array|required|{"title": "test document", "attachment": "staging/test", "language": "en"}|Document Data. 
     * @body = document.*.title|<title>|string|required|test document|Document's title. 
     * @body = document.*.attachment|<attachment>|string|required|staging/test|Document's Attachment.
     * @body = document.*.language_type|<language_type>|string|required|en|Document's language. (dropdown: language)
     * @body = start_date|<start_date>|string|optional|2022-08-28|Document's activate date.
     * @body = end_date|<end_date>|string|optional|2022-08-28|Document's activate date.
     * @body = status|<status>|string|active|active|Document's status. (dropdown: document_status)

     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmNxcS5jb20vbWVtby9lZGl0LW1lbW8iLCJpYXQiOjE2NzIzODI4MjAsImV4cCI6MTY3MjM4OTE0NywibmJmIjoxNjcyMzg1NTQ3LCJqdGkiOiJpSlc0WloxRlR6bk5TempzIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.B5QPa4-gnJOs98rnPR6-CY6ad12DkYXQyxe97LkE57o","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.15055394172668 sec","log_id":"a3af5c98-f335-4fe7-8f65-bf627eb75246"}
     * ##docs end##
     */
    public function editDocument(Request $request)
    {
        if (MODULE == 'user') {
            abort(400, json_encode('Invalid Access'));
        }

        $validator = Validator::make($request->all(), [
            "id" => "required|integer|exists:document,id,deleted_at,NULL",
            "type" => "required|in:" . implode(",", array_keys(Document::$type)),

            "document" => "required|array",
            "document.*.title" => "required|string",
            "document.*.attachment" => "required|string",
            "document.*.language_type" => "required|distinct|string|in:" . implode(",", array_keys(config("language"))),

            "start_date" => "required_with:end_date|nullable|string|date_format:Y-m-d",
            "end_date" => "required_with:start_date|nullable|string|date_format:Y-m-d|after_or_equal:start_date",
            "status" => "required|string|in:" . implode(",", array_keys(Document::$status)),
        ],[
            'id.required' => Lang::get('lang.input-field-required-error'),
            'type.required' => Lang::get('lang.input-field-required-error'),
            'document.required' => Lang::get('lang.input-field-required-error'),
            'document.*.title.required' => Lang::get('lang.input-field-required-error'),
            'document.*.attachment.required' => Lang::get('lang.input-field-required-error'),
            'document.*.language_type.required' => Lang::get('lang.input-field-required-error'),
            'start_date.required_with' => Lang::get('lang.input-field-required-error'),
            'end_date.required_with' => Lang::get('lang.input-field-required-error'),
            'status.required' => Lang::get('lang.input-field-required-error'),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\document::editDocument($validator->validated());

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = document/list
     * @module = admin
     * @path = document/list
     * @permissionName = document
     * @menuType = sub_menu
     * @parent = Document
     * @method = POST
     * @description = To get document list.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = title|<title>|string|optional|Announcement|Announcement title filter.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = status|<status>|string|optional|active|Create Status filter. (dropdown: document_status)

     * @response = {"list":[{"id":2,"created_at":"2023-01-03 18:49:56","title":"123\u6728\u982d\u4ebamutouren","type":"image","start_date":"2023-01-04 00:00:00","end_date":"2023-08-28 00:00:00","status":"active","status_display":"Active","updated_by":null,"updated_at":"2023-01-03 18:49:56"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null,"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmMxcTIuY29tL2RvY3VtZW50L2xpc3QiLCJpYXQiOjE2NzI3Mzk3NjYsImV4cCI6MTY3Mjc0NjY1OSwibmJmIjoxNjcyNzQzMDU5LCJqdGkiOiJkRVBDTFF5T1JRa2tJbHd5Iiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.10xuOJhA4BOP3iYeiJ6_1Ba2SAT_Gu56ZAFXLNWaOw8","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.083856105804443 sec","log_id":"5cfafcf3-155c-41c3-ae83-a41a8695aad6"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = document/document-list
     * @module = user
     * @path = document/document-list
     * @method = POST
     * @description = To get document list.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = title|<title>|string|optional|Announcement|Announcement title filter.
     * @body = type|<type>|string|optional|active|Create Type filter. (dropdown: document_type)

     * @response = {"list":[{"id":7,"created_at":"2023-01-04 17:53:06","title":"123\u6728\u982d\u4ebamutouren345","type":"image","type_display":"Image","attachment":"https:\/\/c1q2-pub.s3.ap-southeast-1.amazonaws.com\/staging\/test"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null,"latest":[{"id":7,"created_at":"2023-01-04 17:53:06","title":"123\u6728\u982d\u4ebamutouren345","type":"image","type_display":"Image","attachment":"https:\/\/c1q2-pub.s3.ap-southeast-1.amazonaws.com\/staging\/test"},{"id":6,"created_at":"2023-01-04 17:53:05","title":"123\u6728\u982d\u4ebamutouren","type":"image","type_display":"Image","attachment":"https:\/\/c1q2-pub.s3.ap-southeast-1.amazonaws.com\/staging\/test"},{"id":5,"created_at":"2023-01-04 17:53:04","title":"123\u6728\u982d\u4ebamutouren","type":"image","type_display":"Image","attachment":"https:\/\/c1q2-pub.s3.ap-southeast-1.amazonaws.com\/staging\/test"},{"id":4,"created_at":"2023-01-04 17:53:02","title":"123\u6728\u982d\u4ebamutouren","type":"image","type_display":"Image","attachment":"https:\/\/c1q2-pub.s3.ap-southeast-1.amazonaws.com\/staging\/test"}],"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLXVzZXIuYzFxMi5jb20vZG9jdW1lbnQvZG9jdW1lbnQtbGlzdCIsImlhdCI6MTY3MjgyNDE4OSwiZXhwIjoxNjcyODMwMzEwLCJuYmYiOjE2NzI4MjY3MTAsImp0aSI6InZuU0FBbXZxdVBhbzlWdDMiLCJzdWIiOiIxMDAwMDAwIiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.AjEtt7FNInWoDjKrjTUJll-j_AtLwD0zCxWXXpSjT8k","token_type":"bearer","kyc_flag":0,"timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.16833281517029 sec","log_id":"5b99414b-e0d4-4e73-8c94-10edb5190deb"}
     * ##docs end##
     */
    public function getDocumentList(Request $request){

        $validator = Validator::make($request->all(), [
            "limit" => "integer",
            "page" => "integer",
            "title" => "string",
            "type" => "in:" . implode(",", array_keys(Document::$type)),
            "from_date" => "required_with:to_date|string|date_format:Y-m-d",
            "to_date" => "required_with:from_date|string|date_format:Y-m-d|after_or_equal:from_date",
            "status" => "string|in:" . implode(",", array_keys(Document::$status)),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\Document::getDocumentList($validator->validated());

        abort(200,json_encode($res));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = document/get
     * @module = admin
     * @path = document/get
     * @permissionName = document Detail
     * @menuType = api
     * @parent = document
     * @method = POST
     * @description = To get document detail.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|required|1|document's id.

     * @response = {"data":{"id":1,"created_at":"2023-01-04 17:52:59","type":"image","type_display":"image","start_date":"2023-01-04","end_date":"2023-08-28","status":"active","status_display":"Active","updated_by":null,"updated_at":"2023-01-04 17:52:59","document":[{"title":"123\u6728\u982d\u4ebamutouren","attachment":"https:\/\/c1q2-pub.s3.ap-southeast-1.amazonaws.com\/staging\/test","attachment_name":"staging\/test","language_type":"en"},{"title":"123\u6728\u982d\u4ebamutouren","attachment":"https:\/\/c1q2-pub.s3.ap-southeast-1.amazonaws.com\/staging\/test","attachment_name":"staging\/test","language_type":"cn"}]},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmMxcTIuY29tL2RvY3VtZW50L2dldCIsImlhdCI6MTY3MjkwODY0MCwiZXhwIjoxNjcyOTEyMzMxLCJuYmYiOjE2NzI5MDg3MzEsImp0aSI6Ik5XVVRacHFqNUlGeHdIN24iLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.UBurl_2_bHL9kE_WIAvpjFVDlRSdPZZEyr_iIkkBkRM","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.10419011116028 sec","log_id":"af648bc2-cb59-47f5-be7e-72f6217664ac"}
     * ##docs end##
     */
    public function getDocumentDetail(Request $request){

        $validator = Validator::make($request->all(), [
            "id" => "integer|required|exists:document,id,deleted_at,NULL",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\document::getDocumentDetail($validator->validated());

        abort(200,json_encode($res));
    }
}
