<?php

namespace App\Http\Controllers;

use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;
use App\Models;

class BankController extends Controller
{
    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Add Bank
     * @module = admin
     * @path = bank/add-bank
     * @method = POST
     * @description = To add Bank.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = step|<step>|integer|required|1/2|Step 1 for get Detail, Step 2 for Add Bank. 
     * @body = language|<array>|array|required|[{"type":"en","name":"Malayan Bank"}|Bank Name Language's Array. 
     * @body = language.*.type|<type>|string|required|en, cn, my|Language's type.
     * @body = language.*.name|<description>|string|required|Malayan Bank|Language's name.
     * @body = currency_id|<currency_id>|integer|required|1|Currency's Id (Get From dropdown : currency). 
     * @body = status|<status>|integer|required|1|Bank Status (Get From dropdown : bank_status_list). 
     * @body = transfer_status|<transfer_status>|integer|required|1|Bank Transfer Status (Get From dropdown : bank_transfer_status_list). 
     * 
     * @response = {"status":true,"message":"lang.granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************.LQrxCSHxtR-d1RNO4WsLz1pb7Sq7OlDHQP3mBQIu5CA","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"903e48d9-bcf5-4c09-b9cf-a098ab22e47e"}
     * ##docs end##
     */
    public function addBank(Request $request){
        $enLangKey = isset($request['language']) ? array_search('en',array_column($request['language'],'type')) : null;
        if(isset($enLangKey)) $request->request->add(['translation_code' => Str::slug($request['language.'.$enLangKey.'.name'], '-')]);
        if(isset($enLangKey)) $request->request->add(['name' => $request['language.'.$enLangKey.'.name']]);

        $currencyData = null;
        $validator = Validator::make($request->all(), [
            'step' => 'required|in:1,2',
            "currency_id" => [
                "required_if:step,==,2",
                "integer",
                function ($q, $value, $fail) use ($request, &$currencyData){
                    $currency = Models\Currency::with(['country'=>function($q){
                        return $q->whereIn('name',config('general.valid_exchange_country'));
                    }])->where('id',$value)->where('disabled',Models\Currency::$disabled['active'])->first();

                    if(empty($currency)){
                        $fail(__('validation.exists'));
                        return;
                    }

                    $currencyData = $currency;
                }
            ],
            'name' => [
                'required_if:step,==,2',
                'string',
                function ($q, $value, $fail) use ($request, &$currencyData){
                    $checkBank = Models\Bank::where('name',$value)->where('country_id',($currencyData->country->id ?? null))->first();
                    if(!empty($checkBank)){
                        $fail('Bank Currency Duplicated');
                        return;
                    }
                }
            ],
            'translation_code' => [
                'required_if:step,==,2',
                'string',
            ],
            'language' => 'required_if:step,==,2|array',
            'language.*.type' => [
                'required_if:step,==,2',
                'string',
                'distinct',
                'in:'.implode(',',array_keys(config('language'))),
                'uniqueType:language.*.type',
                Validator::extendDependent('uniqueType', function($attribute, $value, $parameters, $validator) use (&$request) {
                    if(!in_array('en',array_column($validator->getData()['language'],'type'))){
                        $customMessage = Lang::get("lang.en-required");
                        $validator->addReplacer('uniqueType', 
                            function($message, $attribute, $rule, $parameters) use ($customMessage) {
                                return \str_replace(':custom_message', $customMessage, $message);
                            }
                        );
                        return false;
                    }
                    
                    return true;

                }, ':custom_message'),
            ],
            'language.*.name' => [
                'required_if:step,==,2',
                'string',
                'uniqueName:language.*.type',
                Validator::extendDependent('uniqueName', function($attribute, $value, $parameters, $validator) use (&$currencyData) {
                    // $langType = Arr::get($validator->getData(), $parameters[0]);
                    // $check = Models\LangTable::where($langType, $value)->first();
                    // if($check) return false;

                    return true;

                }, ':input has been used!'),
            ],
            "status" => "required_if:step,==,2|integer|in:".implode(',',Models\Bank::$status),
            "transfer_status" => "required_if:step,==,2|integer|in:".implode(',',Models\Bank::$transferStatus)
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if($validator->validated()['step'] == 2){
            Models\Bank::addBank($validator->validated());
        }

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Bank List
     * @module = admin
     * @path = bank/get-bank-list
     * @method = POST
     * @description = To get bank list.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = name|<name>|string|optional|Malayan Bank|To filter listing by name column
     * @body = currency|<currency>|string|optional|USD|To filter listing by currency column (Get From Dropdown : currency)
     * @body = status|<status>|integer|optional|1|To filter listing by status column (Get From Dropdown : bank_status_list)
     * @body = transfer_status|<transfer_status>|integer|optional|1|To filter listing by transfer status column (Get From Dropdown : bank_transfer_status_list)
     * @body = order_by|<order_by>|string|optional|email|Order listing by column. (status , name)
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.
     * @body = export|<1/0>|integer|optional|1|export the listing or not. 
     * @body = export_data|<export_data>|array|option|{"data":{"Key":"Column Name"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|sponsor-bonus|name of the export file. Required for export
     * 
     * @response = {"data":{"list":[{"id":1,"country_id":129,"currency_iso":"MYR","name":"Affin Bank","bank_display":"Affin Bank","status":"active","status_display":"active","transfer_status":"active","transfer_status_display":"active","updated_by":null,"updated_at":"20\/04\/2023 12:46:50"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":15,"to":1,"total":1},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************.RaocKzhYg1btdFKoJeONSLxyhE8DtZrR507WnOtE4_c","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.*************** sec","log_id":"537231c7-523e-44a3-92b0-3c801f37b04a"}
     * ##docs end##
     */
    public function getBankList(Request $request){
        $validator = Validator::make($request->all(), [
            'name' => 'string',
            'currency' => 'string',
            'status' => 'integer|in:'.implode(',',Models\Bank::$status),
            'transfer_status' => 'integer|in:'.implode(',',Models\Bank::$transferStatus),
            'order_by' => 'string',
            'order_sort' => 'string|in:asc,desc',
            'limit' => 'integer',
            'page' => 'integer',
            'see_all' => 'integer|in:0,1',
            "export" => "in:1,0|nullable",
            "export_data" => "required_if:export,==,1",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $report = Models\Bank::getList($validator->validated());

        $data['data'] = $report;
        abort(200,json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Bank Detail
     * @module = admin
     * @path = bank/get-bank-detail
     * @method = POST
     * @description = To get bank detail.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|interger|required|1|bank's id.
     * 
     * @response = {"data":{"id":18,"language":[{"type":"en","name":"Malayan Bank"},{"type":"cn","name":"\u9a6c\u6765\u4e9a\u94f6\u884c"},{"type":"indo","name":"Malayan Bank"},{"type":"ben","name":"Malayan Bank"}],"currency_id":2,"currency":"USD","status":"active","status_display":"active","transfer_status":"active","transfer_status_display":"active"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************.mtBUpz93XzoG1X5Jqhgbbd55FBgIO-Ld4zHeeYZPayM","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"8e88c4eb-70d8-4807-94e6-aa86118f4759"}
     * ##docs end##
     */
    public function getBankDetail(Request $request){

        $validator = Validator::make($request->all(), [
            'id'=> 'required|exists:bank,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $report = Models\Bank::getBankDetail($validator->validated());

        $data['data'] = $report;
        abort(200,json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Edit Bank
     * @module = admin
     * @path = bank/edit-bank
     * @method = POST
     * @description = To edit bank.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|interger|required|1|bank's id.
     * @body = language|<array>|array|optional|[{"type":"en","name":"Dog"}|Bank Name Language's Array. 
     * @body = language.*.type|<type>|string|optional|en, cn, my|Language's type.
     * @body = language.*.name|<description>|string|optional|Malayan Bank|Language's name.
     * @body = currency_id|<currency_id>|integer|optional|1|Currency's Id (Get From dropdown : currency). 
     * @body = status|<status>|integer|optional|1|Bank Status (Get From dropdown : bank_status_list). 
     * @body = transfer_status|<transfer_status>|integer|optional|1|Bank Transfer Status (Get From dropdown : bank_transfer_status_list). 
     * 
     * @response = {"data":"lang.update-successfully","status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************.3o_7CEo3hEDt9pHTkbR0Tme3vQ-5vpaPN_ZOL2JQNPM","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"3ed8c85c-d178-41f9-bbc6-e579d7bcae33"}
     * ##docs end##
     */
    public function editBank(Request $request){
        $enLangKey = isset($request['language']) ? array_search('en',array_column($request['language'],'type')) : null;
        if(isset($enLangKey)) $request->request->add(['translation_code' => Str::slug($request['language.'.$enLangKey.'.name'], '-')]);
        if(isset($enLangKey)) $request->request->add(['name' => $request['language.'.$enLangKey.'.name']]);

        $slug = null;
        $curBank = null;
        $currencyData = null;

        $validator = Validator::make($request->all(), [
            'id'=> [
                'required',
                function ($q, $value, $fail) use (&$slug,&$curBank,&$request) {
                    $bank = Models\Bank::find($value);
                    if(empty($bank)){
                        $fail(__('validation.in', ["attribute" => str_replace('_', ' ', $q)]));
                        return;
                    }
                    $slug = $bank->translation_code;
                    $curBank = $bank;
                }
            ],
            "currency_id" => [
                "integer",
                function ($q, $value, $fail) use ($request, &$currencyData,&$curBank){
                    $currency = Models\Currency::with(['country'=>function($q){
                        return $q->whereIn('name',config('general.valid_exchange_country'));
                    }])->where('id',$value)->where('disabled',Models\Currency::$disabled['active'])->first();

                    if(empty($currency)){
                        $fail(__('validation.exists'));
                        return;
                    }

                    $currencyData = $currency;
                    if(empty($request->name)){
                        $checkBank = Models\Bank::where('name',$curBank->name)->where('country_id',$currencyData->country->id)->first();
                        if(!empty($checkBank) && ($checkBank->country_id != $curBank->country_id)){
                            $fail('Bank Currency Duplicated');
                            return;
                        }
                    }
                }
            ],
            'name' => [
                'string',
                function ($q, $value, $fail) use ($request, &$currencyData, &$curBank){
                    $checkBank = Models\Bank::where('name',$value)->when(!empty($currencyData),function($q) use (&$currencyData){
                        return $q->where('country_id',$currencyData->country->id);
                    },function ($q) use ($curBank){
                        return $q->where('country_id',$curBank->country->id);
                    })->first();
                    if(!empty($checkBank) && ($checkBank->id != $request->id)){
                        $fail('Bank Currency Duplicated');
                        return;
                    }
                }
            ],
            'translation_code' => 'required_with:language|string',
            'language' => 'array',
            'language.*.type' => [
                'string',
                'distinct',
                'in:'.implode(',',array_keys(config('language'))),
                'uniqueType:language.*.type',
                Validator::extendDependent('uniqueType', function($attribute, $value, $parameters, $validator) use (&$request) {
                    if(!in_array('en',array_column($validator->getData()['language'],'type'))){
                        $customMessage = Lang::get("lang.en-required");
                        $validator->addReplacer('uniqueType', 
                            function($message, $attribute, $rule, $parameters) use ($customMessage) {
                                return \str_replace(':custom_message', $customMessage, $message);
                            }
                        );
                        return false;
                    }
                    
                    return true;

                }, ':custom_message'),
            ],
            'language.*.name' => [
                'string',
                'uniqueName:language.*.type',
                Validator::extendDependent('uniqueName', function($attribute, $value, $parameters, $validator) use (&$slug){
                    // $langType = Arr::get($validator->getData(), $parameters[0]);
                    // $check = Models\LangTable::where($langType, $value)->get()->pluck('slug')->toArray();
                    // if(!empty($check) && (!in_array($slug,$check))) return false;
                    return true;

                }, ':input has been used!'),
            ],
            "status" => "integer|in:".implode(',',Models\Bank::$status),
            "transfer_status" => "integer|in:".implode(',',Models\Bank::$transferStatus)
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $updateRes = Models\Bank::editBank($validator->validated());

        abort(200, json_encode(['data'=> $updateRes]));
    }
}
