<?php

namespace App\Http\Controllers;

use App\Traits\FirebaseTrait;
use Illuminate\Http\Request;

use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Lang;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use App\Models;

class BannerController extends Controller
{
    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = banner/add-banner
     * @module = admin
     * @path = banner/add-banner
     * @permissionName = Add banner
     * @menuType = api
     * @parent = Banner
     * @method = POST
     * @description = To add banner.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = banner_data|<array>|array|required|[{"title":"TEST Banner 1","language_type":"en","upload":{"web":{"upload_name":"image1.jpg","upload_type":"image\/jpg"}}}]|Banner's Data. 
     * @body = banner_data.*.title|<title>|string|required|test title|Banner's Title.
     * @body = banner_data.*.language_type|<language_type>|string|required|en, cn, my|Banner's Language.
     * @body = banner_data.*.upload|<array>|array|required|{"mobile":{"upload_name":"image1.jpg","upload_type":"image\/jpg"}}|Banner's Image. (Currently only accept mobile)
     * @body = banner_data.*.upload.mobile|<array>|array|required|{"upload_name":"image1.jpg","upload_type":"image\/jpg"}|Banner's Image.
     * @body = banner_data.*.upload.mobile.upload_name|<upload_name>|string|required|test/2022/09/1663906995_0715_test.png|Banner's Image (Public Type).
     * @body = banner_data.*.upload.mobile.upload_type|<upload_type>|string|required|image/jpg, image/jpeg, image/png|Banner's Image type.
     * @body = priority|<priority>|integer|optional|1|Banner's priority.
     * @body = start_date|<start_date>|string|optional|2022-08-28|Banner's activate date.
     * @body = end_date|<end_date>|string|optional|2022-08-28|Banner's activate date.
     * @body = status|<status>|string|optional|active|Banner's status. (dropdown: banner_status)

     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20vYmFubmVyL2FkZC1iYW5uZXIiLCJpYXQiOjE2NjQyNjczODEsImV4cCI6MTY2NDI4MjIzNiwibmJmIjoxNjY0Mjc4NjM2LCJqdGkiOiJqdEZNMk1pQmpoaXpBOG9tIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.RFGdiyUv6uiSWyzQIROgnlV1Jz6unyD5ftiIh3-3kmg","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.0094289779663086 sec","log_id":"6baba90b-3c15-4edb-96bc-cdf5c1a664ae"}
     * ##docs end##
     */
    public function addBanner(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "banner_data" => "required|array",
            "banner_data.*.title" => "required|string",
            "banner_data.*.terms" => "nullable|string",
            "banner_data.*.description" => "nullable|string",
            "banner_data.*.language_type" => "required|string|distinct|in:" . implode(',', array_keys(config('language'))),

            "banner_data.*.upload.*" => "required|array",
            "banner_data.*.upload.mobile.upload_name" => "required|string",
            "banner_data.*.upload.mobile.upload_type" => "nullable|required_if:banner_data.*.upload.mobile.upload_name,true|in:image/jpg,image/jpeg,image/png",

            "priority" => "int",
            "start_date" => "required_with:end_date|string|date_format:Y-m-d|after_or_equal:today",
            "end_date" => "required_with:start_date|string|date_format:Y-m-d|after_or_equal:start_date",
            "status" => "string|in:" . implode(",", array_keys(Models\Banner::$status)),
            "link" => "nullable|string",
        ], [
            "banner_data.*.title.required" => Lang::get('lang.input-field-required-error'),
            "banner_data.*.upload.*.required" => Lang::get('lang.input-field-required-error'),
            "banner_data.*.upload.mobile.upload_name.required" => Lang::get('lang.input-field-required-error'),
            "banner_data.*.upload.mobile.upload_type.required_with" => '',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data = $validator->validated();
        $res = Models\Banner::addBanner($validator->validated());

        if (!$res) {
            abort(500);
        }

        $image = env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $data['banner_data'][0]['upload']['mobile']['upload_name'];

        FirebaseTrait::broadcastNotification(
            title: $data['banner_data'][0]['title'],
            body: $data['banner_data'][0]['description'] ?? '',
            image: $image,
        );

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = banner/banner-list
     * @module = admin
     * @path = banner/banner-list
     * @permissionName = Banner
     * @menuType = sub_menu
     * @parent = Document
     * @method = POST
     * @description = To get banner list.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = start_from_date|<start_from_date>|string|optional|2022-08-28|Create Date Post filter.
     * @body = start_to_date|<start_to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = title|<title>|string|optional|2022-08-28|Create Date filter.
     * @body = status|<status>|string|optional|active|Status filter.
     * @body = platform_id|<platform_id>|integer|optional|1|Platform filter. (dropdown: platform_all)
     * 
     * @response = {"list":[{"id":7,"title":"test banner 1","priority":8,"start_date":"04\/04\/2023","end_date":"28\/12\/2023","creator":"puAdmin","created_at":"04\/04\/2023 16:04:17","updated_at":"04\/04\/2023 16:19:42","status":"active","image_display":{"mobile":{"upload_name":"image_1.jpg","upload_type":"image\/jpg","image_display":"https:\/\/petuniverse-pub.s3.ap-southeast-1.amazonaws.com\/image_1.jpg"}},"status_display":"Active","app_redirect":"xray","app_redirect_display":"X-Ray","is_deletable":0,"updated_by":"puAdmin"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null,"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnB1LWhvc3BpdGFsLmNvbS9iYW5uZXIvYmFubmVyLWxpc3QiLCJpYXQiOjE2ODA1OTYzNDQsImV4cCI6MTY4MDYwMTM3NCwibmJmIjoxNjgwNTk3Nzc0LCJqdGkiOiJCUDVTNkpMSHJoTzdlSjBBIiwic3ViIjoiMSIsInBydiI6IjdlYmI4YTJjYzFkOTViNjJjOTU5NGEyMmM5Y2VjMjJmMzhkYjVkMzEifQ.i_9n9z67A4pxLnrOpL7rv561qTkJzYfHhHlVbU1kNYw","token_type":"bearer","kyc_flag":1,"timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.067209005355835 sec","log_id":"405379df-50a7-4710-ae5c-ae795bf65224"}
     * ##docs end##
     */
    public function getBannerList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "limit" => "integer",
            "page" => "integer",
            "from_date" => "required_with:to_date|string|date_format:Y-m-d",
            "to_date" => "required_with:from_date|string|date_format:Y-m-d|after_or_equal:from_date",
            "start_from_date" => "required_with:start_to_date|string|date_format:Y-m-d",
            "start_to_date" => "required_with:start_from_date|string|date_format:Y-m-d|after_or_equal:start_from_date",
            "title" => "string",
            "status" => "string",
            "platform_id" => 'integer|exists:platform,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\Banner::getBannerList($validator->validated());

        abort(200, json_encode($res));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = banner/banner-detail
     * @module = admin
     * @path = banner/banner-detail
     * @permissionName = Banner Detail
     * @menuType = api
     * @parent = Banner
     * @method = POST
     * @description = To get banner detail.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|optional|1|Banner's id.

     * @response = {"data":{"id":7,"start_date":"2023-04-04 00:00:00","end_date":"2023-12-28 00:00:00","status":"active","priority":8,"status_display":"Active","banner_data":[{"id":7,"title":"test banner 1","language_type":"en","web_image_data":null,"created_at":null,"updated_at":null,"deleted_at":null,"language_type_display":"English","upload":{"mobile":{"upload_name":"image_1.jpg","upload_type":"image\/jpg","image_display":"https:\/\/petuniverse-pub.s3.ap-southeast-1.amazonaws.com\/image_1.jpg"}}},{"id":8,"title":"test banner 2","language_type":"cn","web_image_data":null,"created_at":null,"updated_at":null,"deleted_at":null,"language_type_display":"Chinese","upload":{"mobile":{"upload_name":"image_1.jpg","upload_type":"image\/jpg","image_display":"https:\/\/petuniverse-pub.s3.ap-southeast-1.amazonaws.com\/image_1.jpg"}}}],"banner_setting":{"app_redirect":"xray"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnB1LWhvc3BpdGFsLmNvbS9iYW5uZXIvYmFubmVyLWRldGFpbCIsImlhdCI6MTY4MDU5NjM0NCwiZXhwIjoxNjgwNjAxNDAxLCJuYmYiOjE2ODA1OTc4MDEsImp0aSI6IlBOcDdyZkRGWFVhTzBBYW8iLCJzdWIiOiIxIiwicHJ2IjoiN2ViYjhhMmNjMWQ5NWI2MmM5NTk0YTIyYzljZWMyMmYzOGRiNWQzMSJ9.4cdvZ9MP69pC3kt3l2vKo0otvDasADmcCQNniqSWD2Y","token_type":"bearer","kyc_flag":1,"timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.056349992752075 sec","log_id":"1097e3a9-6a0d-429d-90f5-0180b8e37ba5"}
     * ##docs end##
     */
    public function getBannerDetail(Request $request)
    {

        $validator = Validator::make($request->all(), [
            "id" => "integer|exists:banner,id",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = ["data" => null];

        if (isset($request->id)) {
            $res = Models\Banner::getBannerDetail($validator->validated());
        }

        abort(200, json_encode($res));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = banner/edit-banner
     * @module = admin
     * @path = banner/edit-banner
     * @permissionName = Edit banner
     * @menuType = api
     * @parent = Banner
     * @method = POST
     * @description = To edit banner.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|required|1|Banner's id.
     * @body = banner_data|<array>|array|required|[{"title":"TEST Banner 1","language_type":"en","upload":"web":{"upload_name":"image1.jpg","upload_type":"image\/jpg"}}]|Banner's Data. 
     * @body = banner_data.*.title|<title>|string|required|test title|Banner's Title.
     * @body = banner_data.*.language_type|<language_type>|string|required|en, cn, my|Banner's Language.
     * @body = banner_data.*.upload|<array>|array|required|{"mobile":{"upload_name":"image1.jpg","upload_type":"image\/jpg"}}|Banner's Image. (Currently only accept mobile)
     * @body = banner_data.*.upload.mobile|<array>|array|required|{"upload_name":"image1.jpg","upload_type":"image\/jpg"}|Banner's Image.
     * @body = banner_data.*.upload.mobile.upload_name|<upload_name>|string|required|test/2022/09/1663906995_0715_test.png|Banner's Image (Public Type).
     * @body = banner_data.*.upload.mobile.upload_type|<upload_type>|string|required|image/jpg, image/jpeg, image/png|Banner's Image type.
     * @body = priority|<priority>|integer|optional|1|Banner's priority.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Banner's activate date.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Banner's activate date.
     * @body = status|<status>|string|required|active|Banner's status (if status = deleted, only need id and status).

     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20vYmFubmVyL2VkaXQtYmFubmVyIiwiaWF0IjoxNjY0MjY3MzgxLCJleHAiOjE2NjQyODIyODgsIm5iZiI6MTY2NDI3ODY4OCwianRpIjoic2pPUURrTTlnUUdFdlh3eiIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.SkBbJFIhB9x2bqqDjh-giaMzwwEApHTZgJAKK0s-PR8","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.019425868988037 sec","log_id":"cbcbd1fd-f6b4-4503-9afb-3ed8625795bb"}
     * ##docs end##
     */
    public function editBanner(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "id" => "required|integer|exists:banner,id",
            "banner_data" => "required_unless:status,deleted|array",
            "banner_data.*.title" => "required_unless:status,deleted|string",
            "banner_data.*.terms" => "nullable|string",
            "banner_data.*.description" => "nullable|string",
            "banner_data.*.language_type" => "required_unless:status,deleted|string|distinct|in:" . implode(',', array_keys(config('language'))),

            "banner_data.*.upload.*" => "required_unless:status,deleted|array",
            "banner_data.*.upload.mobile.upload_name" => "required_unless:status,deleted|required_without:banner_data.*.upload.web|string",
            "banner_data.*.upload.mobile.upload_type" => "required_unless:status,deleted|required_with:banner_data.*.upload.mobile.upload_name|in:image/jpg,image/jpeg,image/png",

            "priority" => "int",
            "start_date" => "required_with:end_date|nullable|string|date_format:Y-m-d",
            "end_date" => "required_with:start_date|nullable|string|date_format:Y-m-d|after_or_equal:start_date",
            "status" => [
                "required",
                "string",
                "in:" . implode(",", array_keys(Models\Banner::$status)),
                function ($q, $value, $fail) use ($request) {
                    if ($value == 'deleted') {
                        $banner = Models\Banner::find($request->id);
                        if ($banner->status != Models\Banner::$status['inactive']) {
                            $fail(__('validation.in', ["attribute" => $q]));
                            return;
                        }
                    }
                }
            ],
            "link" => "nullable|string",
        ], [
            "banner_data.*.title.required" => Lang::get('lang.input-field-required-error'),
            "banner_data.*.upload.*.required" => Lang::get('lang.input-field-required-error'),
            "banner_data.*.upload.mobile.upload_name.required_without" => Lang::get('lang.input-field-required-error'),
            "banner_data.*.upload.mobile.upload_type.required_without" => '',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\Banner::editBanner($validator->validated());

        abort(200);
    }
}
