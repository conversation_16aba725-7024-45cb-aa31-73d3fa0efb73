<?php

namespace App\Http\Controllers;

use App\Models;
use App\Models\Bank;
use App\Models\Convert;
use App\Models\Credit;
use App\Models\CreditSetting;
use App\Models\CreditTransaction;
use App\Models\CurrencySetting;
use App\Models\Deposit;
use App\Models\ExTransfer;
use App\Models\Promotion;
use App\Models\Schedule;
use App\Models\Store;
use App\Models\SystemSetting;
use App\Models\SystemSettingsAdmin;
use App\Models\TpWebHook;
use App\Models\TransactionLogs;
use App\Models\Transfer;
use App\Models\TransferTurnover;
use App\Models\TreeSponsor;
use App\Models\User;
use App\Models\UserBank;
use App\Models\UserCard;
use App\Models\UserCardLog;
use App\Models\UserDetail;
use App\Models\UserPromotion;
use App\Models\Withdrawal;
use App\Models\WithdrawalThirdParty;
use App\Traits;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class CreditController extends Controller
{
    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Credit Adjustment
     * @module = admin
     * @path = credit/adjustment
     * @method = post
     * @description = To credit adjustment.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = user_id|<user_id>|integer|required|1|User's id.
     * @body = credit_id|<credit_id>|integer|required|1|Credit's id.
     * @body = type|<in / out>|string|required|in|Adjust Type.
     * @body = amount|<amount>|decimal|required|20.00|Adjust Amount with 8 decimal.
     * @body = remark|<remark>|string|optional|testing|Remark for the transction.

     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20vY3JlZGl0L2FkanVzdG1lbnQiLCJpYXQiOjE2NjMwNzQzOTIsImV4cCI6MTY2MzA4MDI2MywibmJmIjoxNjYzMDc2NjYzLCJqdGkiOiJXMHNmeHZGMkhhNW0wUFdoIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.h94e2lsWEfpUHlDNTciwlO9EEFakuj_rsd9Oyq4OleI","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.**************** sec","log_id":"020f8c01-a753-4b9d-934f-470547d59cd7"}
     * ##docs end##
     */

    public function adjustment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => [
                'required',
                'integer',
                Rule::exists('users', 'id')->whereIN('user_type', [User::$userType['user-account']]),
            ],
            'credit_id' => 'required|integer|exists:credit,id',
            'type' => 'required|string|in:in,out,manual-bank,advanced-credit,promotion-adjustment',
            'amount' => [
                'required',
                'string',
                'gt:0',
                'regex:/^(\\d+\\.?\\d{0,2})$/',
                function ($q, $value, $fail) use ($request) {
                    $creditType = Credit::find($request->credit_id)->name;
                    $balance = Credit::getBalance($request->user_id, $creditType);
                    if (($balance < $value) && ($request->type == 'out')) {
                        abort(400, json_encode(['Insufficient Balance']));
                    }
                },
                'remark' => 'string',
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if ($request->type == 'manual-bank') {
            $params = $request->all();
            $params['deposit_type'] = 'manual-bank';
            $params['deposit_status'] = 'approved';
            $params['creator_id'] = Auth::user()->id;
            $params['creator_type'] = MODULE;
            $params['currency'] = config('users.default_currency');

            $res = Deposit::addDeposit($params);
        } elseif ($request->type == 'advanced-credit') {
            $res = CreditTransaction::advancedCredit($request->all());
        } elseif ($request->type == 'promotion-adjustment') {
            $res = CreditTransaction::promotionAdjustment($request->all());
        }
        $res = CreditTransaction::adjustment($request->all());

        abort(200);
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Credit Transaction List
     * @module = admin
     * @path = credit/transaction-list
     * @method = post
     * @description = To credit transaction list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = user_id|<user_id>|integer|required|1|User's id.
     * @body = credit_id|<credit_id>|string|required|1|Credit's id. Either credit_type or credit_id
     * @body = credit_type|<credit_type>|string|required|usd-wallet|Credit's type. Either credit_type or credit_id
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.

     * @response = {"list":[{"created_at":"2022-09-14T02:42:12.000000Z","username":"kytest001","type":"out","amount":"11.********","balance":"1.********","subject":"Adjustment Out","remark":"test"},{"created_at":"2022-09-14T02:41:52.000000Z","username":"kytest001","type":"in","amount":"10.********","balance":"12.********","subject":"Adjustment In","remark":"test"},{"created_at":"2022-09-14T02:41:28.000000Z","username":"kytest001","type":"out","amount":"10.********","balance":"2.********","subject":"Adjustment Out","remark":"test"},{"created_at":"2022-09-14T02:41:25.000000Z","username":"kytest001","type":"out","amount":"10.********","balance":"12.********","subject":"Adjustment Out","remark":"test"},{"created_at":"2022-09-14T02:41:25.000000Z","username":"kytest001","type":"out","amount":"10.********","balance":"22.********","subject":"Adjustment Out","remark":"test"}],"pagination":{"current_page":1,"first_page_url":"http:\/\/local-api-admin.martin.com\/credit\/transaction-list?page=1","from":1,"last_page":3,"last_page_url":"http:\/\/local-api-admin.martin.com\/credit\/transaction-list?page=3","next_page_url":"http:\/\/local-api-admin.martin.com\/credit\/transaction-list?page=2","path":"http:\/\/local-api-admin.martin.com\/credit\/transaction-list","per_page":5,"prev_page_url":null,"to":5,"total":12},"meta":null,"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20vY3JlZGl0L3RyYW5zYWN0aW9uLWxpc3QiLCJpYXQiOjE2NjMxNDI0ODgsImV4cCI6MTY2MzE0NjEwMSwibmJmIjoxNjYzMTQyNTAxLCJqdGkiOiI1WG15dDNGSmlhbW1FMXNFIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.NEuuxw2zkqPM2VkrLJUviSP13QewulnwceNAS3JFveU","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.045103073120117 sec","log_id":"9c3b6fd7-7f94-4e32-8a7a-115f5f1657da"}
     * ##docs end##
     */

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Credit Transaction List
     * @module = user,app
     * @path = credit/transaction-list
     * @method = post
     * @description = To credit transaction list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = user_id|<user_id>|integer|required|1|User's id.
     * @body = username|<username>|string|optional|khai hein|Create Username filter.
     * @body = credit_id|<credit_id>|string|required|1|Credit's id. Either credit_type or credit_id
     * @body = credit_type|<credit_type>|string|required|usd-wallet|Credit's type. Either credit_type or credit_id
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter. (app_transaction_date)
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter. (app_transaction_date)
     * @body = subject_id|<subject_id>|integer|optional|1|Subject filter.
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.

     * @response = {"data":{"list":[{"created_at":"02\/06\/2023 00:03:11","username":"60123228192","amount":"300.00","subject":"Send Money (Normal)","remark":null,"reference":null,"type":"out","transaction_id":"TT316530","balance":"2260.00"},{"created_at":"25\/05\/2023 07:16:22","username":"60123228192","amount":"12.00","subject":"Send Money (Normal)","remark":null,"reference":null,"type":"out","transaction_id":"TT508906","balance":"2560.00"},{"created_at":"25\/05\/2023 07:06:39","username":"60123228192","amount":"1.00","subject":"Send Money (Normal)","remark":null,"reference":null,"type":"out","transaction_id":"TT125876","balance":"2572.00"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":3,"total":3},"meta":null,"table_total":{"amount":"-313.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS9jcmVkaXQvdHJhbnNhY3Rpb24tbGlzdCIsImlhdCI6MTY5NDY4MDY0OCwiZXhwIjoxNjk0Njg2Nzk2LCJuYmYiOjE2OTQ2ODMxOTYsImp0aSI6ImxJMXo2bWp1RFFWWmpUb3MiLCJzdWIiOiIxMDAwMDMwIiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.sSJCLrL1wtWYdgWQY5g2nuKyulZML94VH4NmeqpykDo","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.055122852325439 sec","log_id":"d32e9f08-ed26-42d0-9010-5b1b297083ff"}
     * ##docs end##
     */

    public function getTransactionList(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $request->request->add(['user_id' => Auth::user()->id]);
            if (! isset($request->credit_id)) {
                $creditId = Credit::where('name', 'myr-credit')->first()->id ?? null;
                $request->request->add(['credit_id' => $creditId]);
            }
        }

        $validator = Validator::make($request->all(), [
            'limit' => 'integer',
            'page' => 'integer',
            'user_id' => [
                'required',
                'integer',
                Rule::exists('users', 'id')->whereIN('user_type', [User::$userType['user-account']]),
            ],
            'username' => 'string',
            'credit_id' => 'required_without:credit_type|integer|exists:credit,id',
            'credit_type' => 'required_without:credit_id|string|exists:credit,type',
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d|after_or_equal:from_date',
            'subject_id' => 'integer',
            'see_all' => 'integer|in:1,0',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = CreditTransaction::getTransactionList($request->all());

        abort(200, json_encode(['data' => $res]));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = MYR Transaction List
     * @module = admin
     * @path = credit/myr-transaction-list
     * @method = post
     * @description = Get MYR credit transaction list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = username|<username>|string|optional|UserName|User's username.
     * @body = member_id|<member_id>|int|optional|123809234|User's Member id.
     * @body = store_id|<store_id>|integer|optional|27|Store. (dropdown: store)
     * @body = subject_id|<subject_id>|integer|optional|1|Subject filter. (Dropdown: transaction_subject)
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.

     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = export_data|<export_data>|array|option|{"data":{"Key":"Column Name"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|sponsor-bonus|name of the export file. Required for export

     * @response = {"data":{"list":[{"store":"test","created_at":"26\/08\/2024 15:10:48","username":"***********","amount":"10.00","subject":"ex-transfer-in","remark":null,"reference":null,"type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"23\/08\/2024 11:01:08","username":"***********","amount":"100.00","subject":"withdrawal-out","remark":null,"reference":"WB362654","type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"23\/08\/2024 10:49:59","username":"***********","amount":"100.00","subject":"withdrawal-refund","remark":null,"reference":"WB261580","type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"23\/08\/2024 10:45:28","username":"***********","amount":"100.00","subject":"withdrawal-out","remark":null,"reference":"WB261580","type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"23\/08\/2024 10:44:31","username":"***********","amount":"10000.00","subject":"Adjustment in","remark":null,"reference":null,"type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"22\/08\/2024 17:46:09","username":"***********","amount":"10.00","subject":"ex-transfer-out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"22\/08\/2024 17:41:29","username":"***********","amount":"10.00","subject":"ex-transfer-in","remark":null,"reference":null,"type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"22\/08\/2024 17:23:47","username":"***********","amount":"10.00","subject":"ex-transfer-out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"22\/08\/2024 17:23:37","username":"***********","amount":"10.00","subject":"ex-transfer-out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"22\/08\/2024 17:20:53","username":"***********","amount":"10.00","subject":"ex-transfer-out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"22\/08\/2024 17:20:24","username":"***********","amount":"1.00","subject":"ex-transfer-out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"22\/08\/2024 17:20:04","username":"***********","amount":"1.00","subject":"ex-transfer-out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"22\/08\/2024 17:19:54","username":"***********","amount":"1.00","subject":"ex-transfer-out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"22\/08\/2024 17:19:34","username":"***********","amount":"1.00","subject":"ex-transfer-out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"22\/08\/2024 17:19:24","username":"***********","amount":"1.00","subject":"ex-transfer-out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"22\/08\/2024 17:01:30","username":"***********","amount":"1.00","subject":"ex-transfer-out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"22\/08\/2024 17:00:38","username":"***********","amount":"1.00","subject":"ex-transfer-out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"22\/08\/2024 16:49:43","username":"***********","amount":"1.00","subject":"ex-transfer-out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"22\/08\/2024 16:45:20","username":"***********","amount":"1.00","subject":"ex-transfer-out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"22\/08\/2024 15:37:53","username":"***********","amount":"100.00","subject":"Adjustment in","remark":null,"reference":null,"type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"21\/08\/2024 18:06:21","username":"***********","amount":"1.00","subject":"ex-transfer-out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"store":"test","created_at":"21\/08\/2024 14:30:10","username":"***********","amount":"1.00","subject":"Adjustment in","remark":null,"reference":null,"type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":22,"total":22},"meta":null,"table_total":{"amount":"9971.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZ3LmNvbS9jcmVkaXQvbXlyLXRyYW5zYWN0aW9uLWxpc3QiLCJpYXQiOjE3MjQ2NTcxNTcsImV4cCI6MTc1NjIyMjIyMiwibmJmIjoxNzI0NjYyMjIyLCJqdGkiOiJXRXV2NldKSkdkdE9paVBmIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.18v6L4V5Il5KmI4TKnrdTTU7DMHHueGQw9EcSfeLK_Q","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.1545250415802 sec","log_id":"94f3c991-ac98-485e-b555-fa419b428d24","valid_version":false}
     * ##docs end##
     */

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = USDT Transaction List
     * @module = admin
     * @path = credit/usdt-transaction-list
     * @method = post
     * @description = Get USDT credit transaction list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = username|<username>|string|optional|UserName|User's username.
     * @body = subject_id|<subject_id>|integer|optional|1|Subject filter. (Dropdown: transaction_subject)
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.

     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = export_data|<export_data>|array|option|{"data":{"Key":"Column Name"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|sponsor-bonus|name of the export file. Required for export

     * @response = {"data":{"list":[{"created_at":"08\/02\/2024 14:33:25","username":"60333456700","amount":"290.00","subject":"Convert Out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"created_at":"08\/02\/2024 14:32:37","username":"60333456700","amount":"290.00","subject":"Convert In","remark":null,"reference":null,"type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"created_at":"07\/02\/2024 14:33:07","username":"60189742481","amount":"50000.00","subject":"Deposit","remark":null,"reference":"gL2pt2SUXD3KVHkR","type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"created_at":"07\/02\/2024 14:19:26","username":"60189742481","amount":"580.00","subject":"Convert In","remark":null,"reference":null,"type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"created_at":"07\/02\/2024 12:57:17","username":"60189742481","amount":"125.00","subject":"Convert Out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"created_at":"07\/02\/2024 10:14:41","username":"60189742481","amount":"125.80","subject":"Convert In","remark":null,"reference":null,"type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"created_at":"07\/02\/2024 10:13:34","username":"60189742481","amount":"1.00","subject":"Convert In","remark":null,"reference":null,"type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"created_at":"06\/02\/2024 16:36:41","username":"***********","amount":"0.69","subject":"Convert In","remark":null,"reference":null,"type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"created_at":"06\/02\/2024 16:30:12","username":"***********","amount":"382828.28","subject":"Convert In","remark":null,"reference":null,"type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"created_at":"06\/02\/2024 15:34:06","username":"***********","amount":"10.00","subject":"Convert In","remark":null,"reference":null,"type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null}],"pagination":{"current_page":1,"from":1,"last_page":4,"per_page":10,"to":10,"total":31},"meta":null,"table_total":{"amount":"433420.77"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2NyZWRpdC91c2R0LXRyYW5zYWN0aW9uLWxpc3QiLCJpYXQiOjE3MDc5Nzc4MDIsImV4cCI6MTcwNzk4MTgyNCwibmJmIjoxNzA3OTc4MjI0LCJqdGkiOiIxTUowYTJUVmlEeFE5MGhrIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.DtezcMnuQ-Q4rNYoHkm0DNn9iB8kw96Uf9n3Z-52EtE","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.24675798416138 sec","log_id":"0bea98c2-6675-4fd8-86df-0ffac4d99a18"}
     * ##docs end##
     */

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = THB Transaction List
     * @module = admin
     * @path = credit/thb-transaction-list
     * @method = post
     * @description = Get THB credit transaction list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = username|<username>|string|optional|UserName|User's username.
     * @body = subject_id|<subject_id>|integer|optional|1|Subject filter. (Dropdown: transaction_subject)
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = store_id|<store_id>|string|optional|2022-08-28|Store Filter. (dropdown: store)
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.

     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = export_data|<export_data>|array|option|{"data":{"Key":"Column Name"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|sponsor-bonus|name of the export file. Required for export

     * @response = {"data":{"list":[{"created_at":"08\/02\/2024 14:35:41","username":"60333456700","amount":"7500.00","subject":"Convert In","remark":null,"reference":null,"type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"created_at":"07\/02\/2024 18:30:02","username":"60189742481","amount":"500.00","subject":"Deposit","remark":null,"reference":"kFIoc02iF7fh1a1j","type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"created_at":"07\/02\/2024 14:29:54","username":"60189742481","amount":"500.00","subject":"Deposit","remark":null,"reference":"3dYuw8Qr6i1Ch8X2","type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"created_at":"07\/02\/2024 10:14:11","username":"60189742481","amount":"2500.00","subject":"Convert In","remark":null,"reference":null,"type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"created_at":"06\/02\/2024 15:53:28","username":"***********","amount":"10.00","subject":"Convert Out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"created_at":"06\/02\/2024 15:44:40","username":"***********","amount":"36.00","subject":"Convert Out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"created_at":"05\/02\/2024 16:42:56","username":"***********","amount":"12.00","subject":"Convert Out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"created_at":"05\/02\/2024 16:36:57","username":"***********","amount":"1200.00","subject":"Deposit","remark":null,"reference":"LO6SUegwGotJvllQ","type":"in","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"created_at":"05\/02\/2024 16:20:37","username":"***********","amount":"1.00","subject":"Convert Out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null},{"created_at":"05\/02\/2024 16:19:46","username":"***********","amount":"1.00","subject":"Convert Out","remark":null,"reference":null,"type":"out","is-transfer":0,"action_type":"personal","action_type_display":"personal","gain_from":null,"gain_from_display":null}],"pagination":{"current_page":1,"from":1,"last_page":4,"per_page":10,"to":10,"total":34},"meta":null,"table_total":{"amount":"12140.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2NyZWRpdC90aGItdHJhbnNhY3Rpb24tbGlzdCIsImlhdCI6MTcwNzk3NzgwMiwiZXhwIjoxNzA3OTgxODM3LCJuYmYiOjE3MDc5NzgyMzcsImp0aSI6IkpUYXJaVG8yMGJhQ1ZQSzQiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.-gdQRwONqbV8tXGp_xuYoHhwAFD_GVz5vKu9VuxARds","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.19901585578918 sec","log_id":"286330f0-2897-4711-abb4-baa08ee5c6f7"}
     * ##docs end##
     */

    public function allTransactionList(Request $request)
    {

        $creditType = null;
        switch ($request->path()) {
            case 'credit/myr-transaction-list':
                $creditType = 'myr-credit';
                break;
            case 'credit/usdt-transaction-list':
                $creditType = 'usdt-credit';
                break;
            case 'credit/thb-transaction-list':
                $creditType = 'thb-credit';
                break;
        }

        if (isset($creditType)) {
            $request->request->add(['credit_id' => Credit::where('type', $creditType)->first()->id ?? null]);
        }

        if (in_array(MODULE, ['admin'])) {
            $adminId = auth()->user()->id;
            $adminRes = Models\Admin::with(
                [
                    'adminDetail' => function ($q) {
                        return $q->where('name', 'store');
                    },
                ],
            )
                ->find($adminId);

            $isMaster = $adminRes->is_master;
            if (! $isMaster) {
                $branchJE = $adminRes->adminDetail->first()->value ?? '[]';
                $branchDE = json_decode($branchJE);

                $request->merge(['extra_store_id' => $branchDE]);
            }
        }

        $request->request->add(['show_all' => 1]);
        $validator = Validator::make($request->all(), [
            'limit' => 'integer',
            'page' => 'integer',
            'credit_id' => 'required|integer|exists:credit,id',
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|after_or_equal:from_date|string|date_format:Y-m-d',
            'member_id' => 'nullable|integer',
            'username' => 'string',
            'subject_id' => 'integer|in:'.implode(',', array_values(config('subject'))),
            'store_id' => [
                'nullable',
                'integer',
            ],
            'extra_store_id' => 'nullable',
            'see_all' => 'integer|in:1,0',
            'show_all' => 'integer|in:1',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string',
            'file_name' => 'required_if:export,==,1',
            'status' => 'nullable|integer',
            'card_type' => 'nullable|integer',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = CreditTransaction::getTransactionList($request->all());

        abort(200, json_encode(['data' => $res]));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Credit Transfer
     * @module = admin
     * @path = credit/transfer
     * @permissionName = Credit Transfer
     * @menuType = api
     * @parent = Wallet
     * @method = post
     * @description = To credit transfer.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = step|<step>|string|required|1,2|Step 1 Get Credit Data, Step 2 Confirmation.
     * @body = credit_id|<credit_id>|int|required|1000|Credit Unique Id for credit want to tranfer. (Step 1 Required)
     * @body = from_uid|<from_uid>|int|required|user_id|Credit Transfer from user_id. (Step 1 Required)
     * @body = to_phone|<to_phone>|string|required|60-123456790|Credit Transfer to phone. (Step 2 Required)
     * @body = amount|<amount>|int|required|100|Credit Amount want to transfer. (Step 2 Required)
     * @body = remark|<remark>|string|optional|remark|transction's remark.


     * @response = {"data":{"amount":"100.00","iso":"MYR","receiver":"ky test4","receiver_contact":"60-189125492","remark":null,"date_time":"07\/06\/2023 15:00:46"},"to_user":"ky test3","status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS9jcmVkaXQvdHJhbnNmZXIiLCJpYXQiOjE2ODYxMjA0ODQsImV4cCI6MTY4NjEyNDg0NiwibmJmIjoxNjg2MTIxMjQ2LCJqdGkiOiJhT2JIejhCZURHaEwzaTZjIiwic3ViIjoiMTAwMDAwMSIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.GIF33Vn6dOv0eN7NGcq8pDVBXGYIw1_Fs_hDKUf-Vec","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.64091205596924 sec","log_id":"8f057b4e-3073-4dd4-a09a-cb77a150ea7c"}
     * ##docs end##
     */

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Credit Transfer
     * @module = user,app
     * @path = credit/transfer
     * @method = post
     * @description = To credit transfer.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = step|<step>|string|required|1,2,3|Step 1 Get Credit Data, Step 2 Verify Phone Number, Step 3 Confirmation.
     * @body = credit_id|<credit_id>|int|required|1000|Credit Unique Id for credit want to tranfer. (Step 1 Required)
     * @body = to_phone|<to_phone>|string|required|60-123456790|Credit Transfer to phone. (Step 2 Required)
     * @body = amount|<amount>|int|required|100|Credit Amount want to transfer. (Step 3 Required)
     * @body = pin|<pin>|int|required|123456|transction PIN for user perform transfer. (Step 3 Required)
     * @body = remark|<remark>|string|optional|remark|transction's remark.

     * @response = {"data":{"amount":"100.00","iso":"MYR","receiver":"ky test4","receiver_contact":"60-189125492","remark":null,"date_time":"07\/06\/2023 15:00:46"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS9jcmVkaXQvdHJhbnNmZXIiLCJpYXQiOjE2ODYxMjA0ODQsImV4cCI6MTY4NjEyNDg0NiwibmJmIjoxNjg2MTIxMjQ2LCJqdGkiOiJhT2JIejhCZURHaEwzaTZjIiwic3ViIjoiMTAwMDAwMSIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.GIF33Vn6dOv0eN7NGcq8pDVBXGYIw1_Fs_hDKUf-Vec","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.64091205596924 sec","log_id":"8f057b4e-3073-4dd4-a09a-cb77a150ea7c"}
     * ##docs end##
     */
    // public function transferConfirmationCard(Request $request)
    // {

    //     $charge = 0;
    //     $creditType = NULL;
    //     $userDetail = [];
    //     $userIDAry = [];
    //     $walletData = [];
    //     $toUser = [];
    //     $toPostCard = [];
    //     if (in_array(MODULE, ['user', 'app'])) {
    //         $user_id = Auth::user()->id ?? NUll;
    //         $request->request->add(['from_uid' => $user_id]);
    //         $validateCol = "member";
    //     } else {
    //         $validateCol = "admin";
    //     }

    //     if (isset($request->phone_no)) {
    //         $replace = str_replace(" ", "", $request->phone_no);
    //         $request->merge(["phone_no" => $replace]);
    //     }

    //     $validator = Validator::make($request->all() + ['module' => MODULE], [
    //         "step" => "required|in:1,2",
    //         "from_uid" => [
    //             "required",
    //             "integer",
    //             function ($q, $value, $fail) use ($request, &$userDetail, &$userIDAry) {

    //                 $from_uid = User::with(['userDetail', 'treeSponsor'])->where('id', $value)->whereIN('user_type', [User::$userType['user-account']])->get()->map(function ($q) use (&$userDetail) {
    //                     foreach ($q->userDetail->keyBy('name') as $name => $value) {
    //                         $userDetail[$name] = $value->value;
    //                     }
    //                     return $q;
    //                 })->first();

    //                 $isShopOwner = Store::where('store_id', auth()->user()->store_id)->where('user_id', auth()->user()->id)->first() ?? null;
    //                 if (empty($from_uid)) {
    //                     $fail(Lang::get("lang.transfer-invalid-user"));
    //                     return;
    //                 }

    //                 if(empty($isShopOwner)){
    //                     $fail(Lang::get("You are not shop owner!"));
    //                     return;
    //                 }

    //                 $uplineIDAry = explode('/', $from_uid->treeSponsor->trace_key);
    //                 $downlineIDAry = TreeSponsor::where('trace_key', 'like', $from_uid->treeSponsor->trace_key . '/%')->pluck('user_id')->toArray();
    //                 $userIDAry = array_filter(array_merge($uplineIDAry, $downlineIDAry), function ($q) use ($value) {
    //                     return (!in_array($q, [$value, 1000000]));
    //                 });
    //             }
    //         ],
    //         /* "to_username" => [
    //             "required_if:step,==,2",
    //             "string",
    //             Rule::exists('users','username')->whereIN("user_type",[User::$userType['user-account']])->where("id", "!=", $request->from_uid),
    //             function ($q, $value, $fail) use($request, &$userIDAry, &$toUser){
    //                 $toUser = User::where('username', $value)->whereIN('user_type', [User::$userType['user-account']])->first() ?? null;
    //                 if(empty($toUser->id) || !in_array($toUser->id,$userIDAry)){
    //                     $fail(Lang::get("lang.transfer-invalid-user"));
    //                     return;
    //                 }
    //             }
    //         ], */

    //         "to_pos_card" => [
    //             "required_if:step,==,2",
    //             "string",
    //             function ($q, $value, $fail) use ($request, &$userIDAry, &$toPostCard) {
    //                 $toPostCard = UserCard::with([
    //                     'store' => function ($q) {
    //                       $q->select('store_id', 'name');
    //                     },
    //                     'user' => function ($q) {
    //                       $q->select('id', 'name', "user_type");
    //                     },
    //                 ])
    //                 ->where('store_id', Auth::user()->store_id)
    //                 ->where('card_serial_no', $value)
    //                 ->whereHas('user', function ($q) {
    //                     $q->where('user_type', 1); // Filter by user_type
    //                 })
    //                 ->first() ?? null;

    //                 if (empty($toPostCard->id) || ($toPostCard->user_id == $request->from_uid)) {
    //                     $fail(Lang::get("lang.transfer-invalid-user"));
    //                     return;
    //                 }

    //                 // only user is shop owner can do transfer, or user only can transfer to shop owner
    //                 if(auth()->user()->user_type == User::$userType['user-account']) {

    //                     $store = Store::where('store_id', $toPostCard->store_id)->first();

    //                     if (auth()->user()->store_id != $toPostCard->store_id) {
    //                         $fail(Lang::get("Transfer must be same store"));
    //                         return;
    //                     }

    //                     if ($store == null || auth()->user()->id != $store->user_id ? $store->user_id != $toPostCard->id : false) {
    //                         $fail(Lang::get("lang.transfer-invalid-user"));
    //                         return;
    //                     }
    //                 }

    //                 // only user is shop owner can do transfer, or user only can transfer to shop owner
    //                 // if (auth()->user()->user_type == User::$userType['user-account']) {
    //                 //     $store = Store::where('store_id', $toUser->store_id)->first();

    //                 //     if (auth()->user()->store_id != $toUser->store_id) {
    //                 //         $fail(Lang::get("Transfer must be same store"));
    //                 //         return;
    //                 //     }

    //                 //     if ($store == null || auth()->user()->id != $store->user_id ? $store->user_id != $toUser->id : false) {
    //                 //         $fail(Lang::get("lang.transfer-invalid-user"));
    //                 //         return;
    //                 //     }
    //                 // }
    //             }
    //         ],
    //         // "to_phone" => [
    //         //     "required_if:step,==,2",
    //         //     "string",
    //         //     'regex:/^[0-9]{1,3}-[0-9]{7,13}$/',
    //         //     function ($q, $value, $fail) use ($request, &$userIDAry, &$toUser) {
    //         //         $phoneDetail = explode('-', $value);
    //         //         $phoneDetail[1] = array_key_exists(1, $phoneDetail) ? (int)$phoneDetail[1] : null;
    //         //         $phone_no = $phoneDetail[0] . "-" . $phoneDetail[1];

    //         //         $toUser = User::where('phone_no', $phone_no)->whereIN('user_type', [User::$userType['user-account']])
    //         //             ->where('store_id', Auth::user()->store_id)
    //         //             ->first() ?? null;
    //         //         if (empty($toUser->id) || ($toUser->id == $request->from_uid)) {
    //         //             $fail(Lang::get("lang.transfer-invalid-user"));
    //         //             return;
    //         //         }

    //         //         // only user is shop owner can do transfer, or user only can transfer to shop owner
    //         //         if (auth()->user()->user_type == User::$userType['user-account']) {
    //         //             $store = Store::where('store_id', $toUser->store_id)->first();

    //         //             if (auth()->user()->store_id != $toUser->store_id) {
    //         //                 $fail(Lang::get("Transfer must be same store"));
    //         //                 return;
    //         //             }

    //         //             if ($store == null || auth()->user()->id != $store->user_id ? $store->user_id != $toUser->id : false) {
    //         //                 $fail(Lang::get("lang.transfer-invalid-user"));
    //         //                 return;
    //         //             }
    //         //         }
    //         //     }
    //         // ],
    //         "pin" => [
    //             Rule::requiredIf(function () use ($request) {
    //                 return (in_array(MODULE, ['user', 'app']) && ($request->step == 3));
    //             }),
    //             function ($q, $value, $fail) use (&$request) {
    //                 $check = UserDetail::where('user_id', $request->from_uid)->where('name', 'transaction_password')->first();
    //                 $tempPass = str_pad($value, 6, "0", STR_PAD_LEFT);
    //                 if (isset($check->value) && Hash::check($tempPass, $check->value) != true) {
    //                     $fail(Lang::get("lang.invalid-pin"));
    //                     return;
    //                 }
    //             }
    //         ],
    //         "credit_id" => [
    //             "required",
    //             "integer",
    //             "exists:credit,id",
    //             function ($q, $value, $fail) use ($request, &$walletData) {
    //                 $creditFunction = CreditSetting::selectRaw('name,admin,member,value,type,reference')->where(["credit_id" => $value])->get()->keyBy("name");

    //                 if (!isset($creditFunction['is-transferable']) || (isset($creditFunction['is-transferable']) && ($creditFunction['is-transferable']['member'] != 1 || $creditFunction['is-transferable']['value'] != 1))) {
    //                     $fail(Lang::get("lang.function-unavailable"));
    //                     return;
    //                 }

    //                 $credit = Credit::with([
    //                     'currency' => function ($q) {
    //                         return $q->select('id', 'iso', 'symbol');
    //                     }
    //                 ])
    //                     ->where(['id' => $value])->first();
    //                 $walletAmt = Credit::getBalance($request->from_uid, $credit->type);

    //                 $m2mTransferOption = [];
    //                 if (isset($creditFunction['m2m-transfer-option']['value'])) {
    //                     $m2mTransferOptionAry = json_decode($creditFunction['m2m-transfer-option']['value']);
    //                     foreach ($m2mTransferOptionAry as $value) {

    //                         $double = Traits\DecimalTrait::setDecimal($value, 2);
    //                         $display = (string)$value;

    //                         $m2mTransferOption[] = [
    //                             "value" => $double,
    //                             "display" => $display,
    //                         ];
    //                     }
    //                 }

    //                 $recent_list = Transfer::with('toUser')->where('from_id', $request->from_uid)->where('status', 1)->orderBy('created_at', 'desc')->limit(5)->get()->map(function ($q) {
    //                     return [
    //                         "name" => $q->toUser?->name,
    //                         "to_phone" => $q->toUser?->phone_no,
    //                     ];
    //                 });

    //                 $walletData = [
    //                     "credit_id" => $value,
    //                     "credit_name" => $credit->name,
    //                     "credit_display" => Lang::has('lang.' . $credit->name) ? Lang::get('lang.' . $credit->name) : $credit->name,
    //                     "balance" => $walletAmt ?? 0,
    //                     "charges_amount" => $creditFunction['transfer-charge']['reference'] ?? 0,
    //                     "charges_percentage" => $creditFunction['transfer-charge']['value'] ?? 0,
    //                     "multiplier" => $creditFunction['transfer-multiplier']['value'] ?? 0,
    //                     "min-transfer" => $creditFunction['min-transfer']['value'] ?? 0,
    //                     "m2m_transfer_option" => $m2mTransferOption ?? [],
    //                     "currency_iso" => $credit->currency->iso,
    //                     "currency_symbol" => $credit->currency->symbol,
    //                     "recent_list" => $recent_list ?? [],
    //                 ];
    //             }
    //         ],
    //         "amount" => [
    //             Rule::requiredIf(function () use ($request) {
    //                 if (MODULE == 'admin') {
    //                     return ($request->step == 2);
    //                 } else {
    //                     return ($request->step == 3);
    //                 }
    //             }),
    //             "string",
    //             // "gte:50",
    //             "regex:/^(\\d+\\.?\\d{0,2})$/",
    //             function ($q, $value, $fail) use ($request, &$charge, $creditType, &$walletData) {
    //                 if (empty($walletData)) return;
    //                 if (!empty($walletData['charges_percentage']) && !empty($walletData['charges_amount'])) {
    //                     $minCharge = $walletData['charges_amount'] ?? 0;
    //                     $percentageCharge = $walletData['charges_percentage'] ?? 0;
    //                     $tempCharge = $value * $percentageCharge / 100;

    //                     $charge = $tempCharge;
    //                     if ($tempCharge < $minCharge) {
    //                         $charge = $minCharge;
    //                     }
    //                 }
    //                 $balance = $walletData['balance'];
    //                 if ($balance < ($value + $charge)) {
    //                     $fail(Lang::get("lang.credit-insufficient-balance"));
    //                     return;
    //                 }

    //                 $minAmount = $walletData['min-transfer'];
    //                 if ($value < $minAmount) {
    //                     $fail(Lang::get('lang.min-amount-error', ['minAmount' => Traits\DecimalTrait::setDecimal($minAmount)]));
    //                     return;
    //                 }
    //             }
    //         ],
    //         "remark" => "string|max:255|nullable",
    //     ], [
    //         "to_phone.regex" => Lang::has('lang.invalid-phone-format') ? Lang::get('lang.invalid-phone-format') : "Invalid Phone Format",
    //     ]);

    //     if ($validator->fails()) {
    //         abort(400, json_encode($validator->errors()));
    //     }

    //     if ($validator->validated()['step'] == 1) {
    //         $returnData = [
    //             "wallet_data" => $walletData,
    //         ];
    //         abort(200, json_encode($returnData));
    //     }

    //     if ($validator->validated()['step'] == 2 && (in_array(MODULE, ['user', 'app']))) {

    //         $returnData = [
    //             "card_name" => $toPostCard->card_name,
    //             "card_serial_no" => $toPostCard->card_serial_no,
    //             "to_user" => $toPostCard->user->name,
    //         ];

    //         // TODO: Deduct amount from shop owner
    //         // 1. Update credit?
    //         // 2. Create extransfer/credittransfer?

    //         // TODO: Update data on user_card
    //         // $toPostCard->update([
    //         //     "member_balance" => $toPostCard->member_balance + $validator->validated()['amount'],
    //         // ]);

    //         // TODO: Add data to user_card_log
    //         // UserCardLog::create([
    //         //     "" => ""
    //         // ]);

    //         abort(200, json_encode($returnData));
    // }
    //     /*  else {
    //         $res = Transfer::add(array_merge($validator->validated(), ["to_username" => $toPostCard->username]));
    //     } */
    //     // abort(200, json_encode($res));
    // }

    public function transferConfirmation(Request $request)
    {
        $charge = 0;
        $creditType = null;
        $userDetail = [];
        $userIDAry = [];
        $walletData = [];
        $toUser = [];
        if (in_array(MODULE, ['user', 'app'])) {
            $user = Auth::user();
            $user_id = $user?->id ?? null;
            $request->request->add(['from_uid' => $user_id]);
            $validateCol = 'member';
        } else {
            $validateCol = 'admin';
        }

        if (isset($request->phone_no)) {
            $replace = str_replace(' ', '', $request->phone_no);
            $request->merge(['phone_no' => $replace]);
        }

        $validator = Validator::make($request->all() + ['module' => MODULE], [
            'step' => 'required|in:1,2,3',
            'from_uid' => [
                'required',
                'integer',
                function ($q, $value, $fail) use (&$userDetail, &$userIDAry) {

                    $from_uid = User::with(['userDetail', 'treeSponsor'])->where('id', $value)->whereIN('user_type', [User::$userType['user-account']])->get()->map(function ($q) use (&$userDetail) {
                        foreach ($q->userDetail->keyBy('name') as $name => $value) {
                            $userDetail[$name] = $value->value;
                        }

                        return $q;
                    })->first();

                    if (empty($from_uid)) {

                        $fail(Lang::get('lang.transfer-invalid-user'));

                        return;
                    }

                    // $uplineIDAry = explode('/', $from_uid->treeSponsor->trace_key);
                    // $downlineIDAry = TreeSponsor::where('trace_key', 'like', $from_uid->treeSponsor->trace_key . '/%')->pluck('user_id')->toArray();
                    // $userIDAry = array_filter(array_merge($uplineIDAry, $downlineIDAry), function ($q) use ($value) {
                    //     return ! in_array($q, [$value, 1000000]);
                    // });
                },
            ],
            /* "to_username" => [
                "required_if:step,==,2",
                "string",
                Rule::exists('users','username')->whereIN("user_type",[User::$userType['user-account']])->where("id", "!=", $request->from_uid),
                function ($q, $value, $fail) use($request, &$userIDAry, &$toUser){
                    $toUser = User::where('username', $value)->whereIN('user_type', [User::$userType['user-account']])->first() ?? null;
                    if(empty($toUser->id) || !in_array($toUser->id,$userIDAry)){
                        $fail(Lang::get("lang.transfer-invalid-user"));
                        return;
                    }
                }
            ], */
            'to_phone' => [
                'required_if:step,==,2',
                'string',
                'regex:/^[0-9]{1,3}-[0-9]{7,13}$/',
                function ($q, $value, $fail) use ($request, &$userIDAry, &$toUser) {

                    $phoneDetail = explode('-', $value);
                    $phoneDetail[1] = array_key_exists(1, $phoneDetail) ? (int) $phoneDetail[1] : null;
                    $phone_no = $phoneDetail[0].'-'.$phoneDetail[1];

                    $toUser = User::where('phone_no', $phone_no)->whereIN('user_type', [User::$userType['user-account']])
                        ->where('store_id', Auth::user()->store_id)
                        ->first() ?? null;

                    if (empty($toUser->id) || ($toUser->id == $request->from_uid)) {

                        $fail(Lang::get('lang.transfer-invalid-user'));

                        return;
                    }

                    // only user is shop owner can do transfer, or user only can transfer to shop owner
                    if (auth()->user()->user_type == User::$userType['user-account']) {

                        $store = Store::where('store_id', $toUser->store_id)->first();

                        if (auth()->user()->store_id != $toUser->store_id) {
                            $fail(Lang::get('Transfer must be same store'));

                            return;
                        }

                        if ($store->transfer_action == Store::$transfer_action['shop_to_customer']) {
                            if ($store == null || auth()->user()->id != $store->user_id ? $store->user_id != $toUser->id : false) {

                                $fail(Lang::get('lang.transfer-invalid-user'));

                                return;
                            }
                        }
                    }
                },
            ],
            'pin' => [
                Rule::requiredIf(function () use ($request) {
                    return in_array(MODULE, ['user', 'app']) && ($request->step == 3);
                }),
                function ($q, $value, $fail) use (&$request) {
                    $check = UserDetail::where('user_id', $request->from_uid)->where('name', 'transaction_password')->first();
                    $tempPass = str_pad($value, 6, '0', STR_PAD_LEFT);
                    if (isset($check->value) && Hash::check($tempPass, $check->value) != true) {
                        $fail(Lang::get('lang.invalid-pin'));

                        return;
                    }
                },
            ],
            'credit_id' => [
                'required',
                'integer',
                'exists:credit,id',
                function ($q, $value, $fail) use ($request, &$walletData) {
                    $creditFunction = CreditSetting::selectRaw('name,admin,member,value,type,reference')->where(['credit_id' => $value])->get()->keyBy('name');

                    if (! isset($creditFunction['is-transferable']) || (isset($creditFunction['is-transferable']) && ($creditFunction['is-transferable']['member'] != 1 || $creditFunction['is-transferable']['value'] != 1))) {
                        $fail(Lang::get('lang.function-unavailable'));

                        return;
                    }

                    $credit = Credit::with([
                        'currency' => function ($q) {
                            return $q->select('id', 'iso', 'symbol');
                        },
                    ])
                        ->where(['id' => $value])->first();
                    $walletAmt = Credit::getBalance($request->from_uid, $credit->type);

                    $m2mTransferOption = [];
                    if (isset($creditFunction['m2m-transfer-option']['value'])) {
                        $m2mTransferOptionAry = json_decode($creditFunction['m2m-transfer-option']['value']);
                        foreach ($m2mTransferOptionAry as $value) {

                            $double = Traits\DecimalTrait::setDecimal($value, 2);
                            $display = (string) $value;

                            $m2mTransferOption[] = [
                                'value' => $double,
                                'display' => $display,
                            ];
                        }
                    }

                    $recent_list = Transfer::with('toUser')->where('from_id', $request->from_uid)->where('status', 1)->orderBy('created_at', 'desc')->limit(5)->get()->map(function ($q) {
                        return [
                            'name' => $q->toUser?->name,
                            'to_phone' => $q->toUser?->phone_no,
                        ];
                    });

                    $walletData = [
                        'credit_id' => $value,
                        'credit_name' => $credit->name,
                        'credit_display' => Lang::has('lang.'.$credit->name) ? Lang::get('lang.'.$credit->name) : $credit->name,
                        'balance' => $walletAmt ?? 0,
                        'charges_amount' => $creditFunction['transfer-charge']['reference'] ?? 0,
                        'charges_percentage' => $creditFunction['transfer-charge']['value'] ?? 0,
                        'multiplier' => $creditFunction['transfer-multiplier']['value'] ?? 0,
                        'min-transfer' => $creditFunction['min-transfer']['value'] ?? 0,
                        'm2m_transfer_option' => $m2mTransferOption ?? [],
                        'currency_iso' => $credit->currency->iso,
                        'currency_symbol' => $credit->currency->symbol,
                        'recent_list' => $recent_list ?? [],
                    ];
                },
            ],
            'amount' => [
                Rule::requiredIf(function () use ($request) {
                    if (MODULE == 'admin') {
                        return $request->step == 2;
                    } else {
                        return $request->step == 3;
                    }
                }),
                'string',
                // "gte:50",
                'regex:/^(\\d+\\.?\\d{0,2})$/',
                function ($q, $value, $fail) use (&$charge, &$walletData, $user) {
                    if (empty($walletData)) {
                        return;
                    }
                    if (! empty($walletData['charges_percentage']) && ! empty($walletData['charges_amount'])) {
                        $minCharge = $walletData['charges_amount'] ?? 0;
                        $percentageCharge = $walletData['charges_percentage'] ?? 0;
                        $tempCharge = $value * $percentageCharge / 100;

                        $charge = $tempCharge;
                        if ($tempCharge < $minCharge) {
                            $charge = $minCharge;
                        }
                    }
                    $balance = $walletData['balance'];
                    if ($balance < ($value + $charge)) {
                        $fail(Lang::get('lang.credit-insufficient-balance'));

                        return;
                    }

                    $minAmount = $walletData['min-transfer'];
                    if ($value < $minAmount && ! $user->is_agent) {
                        $fail(Lang::get('lang.min-amount-error', ['minAmount' => Traits\DecimalTrait::setDecimal($minAmount)]));

                        return;
                    }
                },
            ],
            'remark' => 'string|max:255|nullable',
        ], [
            'to_phone.regex' => Lang::has('lang.invalid-phone-format') ? Lang::get('lang.invalid-phone-format') : 'Invalid Phone Format',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if (empty($userDetail['transaction_password'])) {
            // abort(400, json_encode(['redirectFlag' => 1, 'errMessage' => Lang::get('lang.no-transaction-pin-error')]));
            abort(400, json_encode(Lang::get('lang.no-transaction-pin-error')));
        }

        if (isset($toUser) && ! $user->is_agent) {
            $turnover = TransferTurnover::updateUserTurnoverStatus($user);
            if ($turnover['status'] == false) {
                abort(400, json_encode('Must Hit At Least Turnover '.Traits\DecimalTrait::setDecimal($turnover['target_turnover']).' to activate transfer'));
            }
        }

        if ($validator->validated()['step'] == 1) {
            $walletData = array_merge($walletData, [
                'terms' => Lang::has('langReward.credit-transfer-terms') ? Lang::get('langReward.credit-transfer-terms') : '',
            ]);
            $returnData = [
                'wallet_data' => $walletData,
            ];
            abort(200, json_encode($returnData));
        }
        if ($validator->validated()['step'] == 2 && (in_array(MODULE, ['user', 'app']))) {
            $returnData = [
                'to_user' => $toUser->name,
            ];
            abort(200, json_encode($returnData));
        } else {
            $res = Transfer::add(array_merge($validator->validated(), ['to_username' => $toUser->username]));
        }

        abort(200, json_encode($res));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = credit/wallet-list
     * @module = user
     * @path = credit/wallet-list
     * @method = post
     * @description = To get wallet list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @response = {"data":{"user_detail":{"full_name":"ghostmate","currency_code":"MYR"},"wallet_data":[{"name":"cash-credit","display":"Cash Wallet","available_balance":"89690.98","balance":"89690.98","credit_setting":[{"credit_id":1,"name":"is-wallet","value":"1","member":1,"reference":null},{"credit_id":1,"name":"show-transaction-history","value":"1","member":1,"reference":null},{"credit_id":1,"name":"is-adjustable","value":"1","member":0,"reference":null},{"credit_id":1,"name":"is-transferable","value":"0","member":0,"reference":null},{"credit_id":1,"name":"transfer-charge","value":"0","member":0,"reference":"5"},{"credit_id":1,"name":"is-fundinable","value":0,"member":0,"reference":null},{"credit_id":1,"name":"fundin-charge","value":"0","member":0,"reference":"0"},{"credit_id":1,"name":"min-fundin","value":"100","member":1,"reference":"USD"},{"credit_id":1,"name":"fundin-multiplier","value":"1","member":0,"reference":""},{"credit_id":1,"name":"is-withdrawable","value":"1","member":1,"reference":null},{"credit_id":1,"name":"withdrawal-charge","value":"0","member":0,"reference":""},{"credit_id":1,"name":"withdrawal-multiplier","value":"1","member":0,"reference":""},{"credit_id":1,"name":"min-withdrawal","value":"1","member":0,"reference":"USD"},{"credit_id":1,"name":"is-convertible","value":0,"member":0,"reference":null}]}],"rate":"0.********","no_transaction":0,"admin_reset_transaction":0,"kyc_flag":0,"kyc_approved_flag":0},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLXVzZXIuY3FxLmNvbS9jcmVkaXQvd2FsbGV0LWxpc3QiLCJpYXQiOjE2NzI3MzQ4NjEsImV4cCI6MTY3MjczODY0NywibmJmIjoxNjcyNzM1MDQ3LCJqdGkiOiJaY1BSRXhMR0h0Vmo3UGRGIiwic3ViIjoiMTAwMDAwMCIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.COPGwyPR71FAGL64pouDGY_5MOtPsZ426K8Ocfx8H8k","token_type":"bearer","kyc_flag":0,"timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.58299612998962 sec","log_id":"7d1d2f7e-ebfa-49ab-8bba-dcb50ebf63ae"}
     * ##docs end##
     */

    public function getWalletList(Request $request)
    {
        $validator = Validator::make($request->all(), []);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Credit::getWalletList($request->all());

        abort(200, json_encode(['data' => $res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Credit Transfer List
     *
     * @module = admin
     *
     * @path = credit/transfer-list
     *
     * @permissionName = Credit Transfer List
     *
     * @menuType = api
     *
     * @parent = Wallet
     *
     * @method = post
     *
     * @description = To get credit transfer list.

     *
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     *
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = from_username|<from_username>|string|optional|sam|From username.
     * @body = to_username|<to_username>|string|optional|sam|To username.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = status|<status>|string|optional|Success|Create status filter.
     * @body = remark|<remark>|string|optional|remark| add remark for transction
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.
     * @body = country|<country>|integer|optional|100|Country filter. (Dropdown: country)

     *
     * @response = {"data":{"list":[{"created_at":"2022-10-20 17:14:05","from_user":"khaihein5","to_user":"khaihein","amount":"635.00","belong_id":1002265,"charges":"0.00","transaction_status":"successful","transaction_status_display":"Successful","country":"vietnam","country_display":"Vietnam","updated_at":"2022-10-20 17:14:05"},{"created_at":"2022-10-14 16:12:20","from_user":"khaihein5","to_user":"katetest","amount":"0.01","belong_id":1002154,"charges":"0.00","transaction_status":"successful","transaction_status_display":"Successful","country":"vietnam","country_display":"Vietnam","updated_at":"2022-10-14 16:12:20"},{"created_at":"2022-10-14 15:46:37","from_user":"khaihein5","to_user":"katetest","amount":"0.01","belong_id":1002153,"charges":"0.00","transaction_status":"successful","transaction_status_display":"Successful","country":"vietnam","country_display":"Vietnam","updated_at":"2022-10-14 15:46:37"},{"created_at":"2022-10-11 11:30:39","from_user":"khaihein5","to_user":"user8","amount":"0.01","belong_id":1001979,"charges":"0.00","transaction_status":"successful","transaction_status_display":"Successful","country":"vietnam","country_display":"Vietnam","updated_at":"2022-10-11 11:30:39"},{"created_at":"2022-10-11 11:30:03","from_user":"khaihein5","to_user":"katetest","amount":"0.01","belong_id":1001978,"charges":"0.00","transaction_status":"successful","transaction_status_display":"Successful","country":"vietnam","country_display":"Vietnam","updated_at":"2022-10-11 11:30:03"},{"created_at":"2022-10-07 17:52:57","from_user":"khaihein5","to_user":"katetest","amount":"100.00","belong_id":1001957,"charges":"0.00","transaction_status":"successful","transaction_status_display":"Successful","country":"vietnam","country_display":"Vietnam","updated_at":"2022-10-07 17:52:57"},{"created_at":"2022-10-07 17:52:12","from_user":"khaihein5","to_user":"katetest","amount":"0.01","belong_id":1001956,"charges":"0.00","transaction_status":"successful","transaction_status_display":"Successful","country":"vietnam","country_display":"Vietnam","updated_at":"2022-10-07 17:52:12"},{"created_at":"2022-10-04 09:31:48","from_user":"khaihein5","to_user":"kh05","amount":"1.00","belong_id":1001817,"charges":"0.00","transaction_status":"pending","transaction_status_display":"Pending","country":"vietnam","country_display":"Vietnam","updated_at":"2022-10-04 09:31:48"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":8,"total":8},"meta":null,"table_total":{"amount":"736.05"},"summary":{"total_transfer":736.05,"total_charge":0}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20vY3JlZGl0L3RyYW5zZmVyLWxpc3QiLCJpYXQiOjE2Njc5NzY3OTksImV4cCI6MTY2Nzk4NDEzOSwibmJmIjoxNjY3OTgwNTM5LCJqdGkiOiJtYU02M1pNSkVoMkJqTUQ5Iiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.qGLczdabginOoatMl2Ee71TLEjik60D_7TdTB2hC-PA","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.051807880401611 sec","log_id":"496b7ab4-af6b-4f7e-9835-081abb8b2b76"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Credit Transfer List
     *
     * @module = user,app
     *
     * @path = credit/transfer-list
     *
     * @method = post
     *
     * @description = To get credit transfer list.

     *
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     *
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = to_username|<to_username>|string|optional|sam|To username.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = status|<status>|string|optional|Success|Create status filter.
     * @body = remark|<remark>|string|optional|remark| add remark for transction
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.

     *
     * @response = {"data":{"list":[{"created_at":"25\/04\/2023 09:43:09","from_user":"*********","to_user":"301556388","amount":"100.00","charges":"0.00","transaction_status":"successful","transaction_status_display":"successful","country":"malaysia","country_display":"Malaysia","updated_at":"25\/04\/2023 09:43:09"},{"created_at":"25\/04\/2023 09:35:14","from_user":"*********","to_user":"301556388","amount":"100.00","charges":"0.00","transaction_status":"successful","transaction_status_display":"successful","country":"malaysia","country_display":"Malaysia","updated_at":"25\/04\/2023 09:35:14"},{"created_at":"20\/04\/2023 19:43:25","from_user":"*********","to_user":"301556388","amount":"100.00","charges":"0.00","transaction_status":"successful","transaction_status_display":"successful","country":"malaysia","country_display":"Malaysia","updated_at":"20\/04\/2023 19:43:25"},{"created_at":"20\/04\/2023 19:42:10","from_user":"*********","to_user":"301556388","amount":"100.00","charges":"0.00","transaction_status":"successful","transaction_status_display":"successful","country":"malaysia","country_display":"Malaysia","updated_at":"20\/04\/2023 19:42:10"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":4,"total":4},"meta":null,"table_total":{"amount":"400.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS9jcmVkaXQvdHJhbnNmZXItbGlzdCIsImlhdCI6MTY4MjM5MjYzNSwiZXhwIjoxNjgyMzk2MzE4LCJuYmYiOjE2ODIzOTI3MTgsImp0aSI6IlBGdmYzQUxCNUx4VUl2QUgiLCJzdWIiOiIxMDAwMDAxIiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.zdddDmeY_KjqVNQcKrO-ttf2BTcgZlTDhr-REjtpCYs","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.14557909965515 sec","log_id":"c2dce4b8-721d-4804-8d08-7e024106b748"}
     * ##docs end##
     */
    public function getTransferList(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $request->request->add(['user_id' => Auth::user()->id]);
        }

        $validator = Validator::make($request->all(), [
            'limit' => 'integer',
            'page' => 'integer',
            'user_id' => 'integer',
            'from_username' => 'string',
            'to_username' => 'string',
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d|after_or_equal:from_date',
            'status' => 'string|in:'.implode(',', array_keys(Transfer::$status)),
            'see_all' => 'integer|in:1,0',
            'country' => 'integer',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if (in_array(MODULE, ['user', 'app'])) {
            $res = Transfer::list($validator->validated());
        } else {
            $res = Transfer::getAppsList($validator->validated());
        }

        abort(200, json_encode(['data' => $res]));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Credit Convert
     * @module = admin
     * @path = credit/convert
     * @permissionName = Credit Convert
     * @menuType = api
     * @parent = Wallet
     * @method = post
     * @description = To credit convert.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = user_id|<user_id>|int|required|user_id|Credit Transfer from user_id.
     * @body = from_credit_id|<from_credit_id>|int|required|1000|Credit Unique Id for credit want to convert from.
     * @body = to_credit_id|<to_credit_id>|int|required|1001|Credit Unique Id for credit want to convert to.
     * @body = amount|<amount>|int|required|100|Credit Amount want to convert from.
     * @body = remark|<remark>|string|optional|remark| add remark for transction

     * @response = {"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20vY3JlZGl0L3RyYW5zZmVyIiwiaWF0IjoxNjYzMzg1MjQ2LCJleHAiOjE2NjMzODg4NjIsIm5iZiI6MTY2MzM4NTI2MiwianRpIjoiTWV6dzhzOXBUYm40aERCdSIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.uEo8THqTg33mTSfBdTugyx4xy7hGiMq0-QhZ5L-Ti2s","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.0071380138397217 sec","log_id":"7cbbac6e-94a6-4f21-bd01-6035f638d285"}
     * ##docs end##
     */

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Credit Convert
     * @module = user,app
     * @path = credit/convert
     * @method = post
     * @description = To credit convert.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = step|<step>|int|required|1|1,2,3
     * @body = from_credit_id|<from_credit_id>|int|required|1000|Credit Unique Id for credit want to convert from.
     * @body = to_credit_id|<to_credit_id>|int|required|1001| (dropdown: currency_filter_convert)
     * @body = from_amount|<from_amount>|int|required|100|Credit Amount want to convert from.
     * @body = to_amount|<to_amount>|int|required|100|Credit Amount want to convert from.
     * @body = amount|<amount>|int|required|100|Credit Amount want to convert from.
     * @body = pin|<pin>|int|required|123456|transction PIN for user perform convert.
     * @body = remark|<remark>|string|optional|remark only|Dont ask me what 7 this for.
     *
     * @response = {"data":{"wallet_info":{"id":1003,"iso":"USDT","balance":"0.00"},"wallet_data":[{"from_credit_id":1003,"to_credit_id":1000,"from_credit_multiplier":"0.01","to_credit_multiplier":"1","id":5,"from_currency_id":2,"from_currency_iso":"USDT","currency_id":5,"currency_iso":"MYR","country_iso_code":"MY","from_rate":"1.00","to_rate":"13.00","min_amount":"1.00","max_amount":"1.00","min_max_amount_iso":"USDT","balance":"0.00"}]},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS9jcmVkaXQvY29udmVydCIsImlhdCI6MTcwODU3MzI2NSwiZXhwIjoxNzA4NTc4NDUzLCJuYmYiOjE3MDg1NzQ4NTMsImp0aSI6InVCMzZGakVyWXZkUklIZ0IiLCJzdWIiOiIxMDAwMDAxIiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.QqG1YHEG5xle9Z47fk-HPJpMUidagOeODY6G0SSItwM","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.065860986709595 sec","log_id":"0e76f924-252d-4415-be9e-8f156aa7a4bc"}
     * ##docs end##
     */

    public function convertConfirmation(Request $request)
    {
        $creditType = null;
        $fromCreditFunction = null;

        if (in_array(MODULE, ['user', 'app'])) {
            $user_id = Auth::user()->id ?? null;
            $request->request->add(['user_id' => $user_id]);
            $validateCol = 'member';
        } else {
            $validateCol = 'admin';
            abort(400, json_encode('Invalid Access'));
        }

        $userDetail = null;
        $walletData = null;
        $summaryData = null;
        $currencyData = null;

        if (isset($request->from_credit_id) && ! empty($request->from_credit_id)) {
            $creditFunction = Credit::with('creditSetting')->where([
                'id' => $request->from_credit_id ?? 0,
            ])->first();

            if (isset($creditFunction->creditSetting) && ! empty($creditFunction->creditSetting)) {
                $creditSettingAry = $creditFunction->creditSetting->toArray();
                foreach ($creditSettingAry as $creditSetting) {
                    $fromCreditFunction[$creditSetting['name']] = $creditSetting;
                }
            }
        }

        if ($request->step >= 2) {
            $request->request->add(['amount' => 0]);
        }

        $validator = Validator::make($request->all() + ['module' => MODULE], [
            'step' => 'required|in:1,2,3',
            'user_id' => [
                'required',
                'integer',
                function ($q, $value, $fail) use (&$userDetail) {
                    $checkUser = User::with(['userDetail'])->where('id', $value)->whereIN('user_type', [User::$userType['user-account']])->get()->map(function ($q) use (&$userDetail) {
                        foreach ($q->userDetail->keyBy('name') as $name => $value) {
                            $userDetail[$name] = $value->value;
                        }

                        return $q;
                    })->first();
                    if (empty($checkUser)) {
                        $fail(Lang::get('validation.exists'));

                        return;
                    }
                },
            ],
            'from_credit_id' => [
                'required',
                'integer',
                'exists:credit,id',
                function ($q, $value, $fail) use ($request, $validateCol, $fromCreditFunction, &$walletData, &$isValidFromCurrency, &$walletInfo) {
                    $convertible = 0;
                    $creditFunction = Credit::with('creditSetting')->where([
                        'id' => $request->from_credit_id ?? 0,
                    ])->first();

                    if (isset($fromCreditFunction['is-convertible']['value']) && $fromCreditFunction['is-convertible']['value'] == 1 && isset($fromCreditFunction['is-convertible'][$validateCol]) && $fromCreditFunction['is-convertible'][$validateCol] == 1) {
                        $convertible = 1;
                    }

                    if ($convertible == 0) {
                        $fail(Lang::get('lang.function-unavailable'));

                        return;
                    }

                    $toCurrencyIds = Convert::getConvertToCurrency($request->from_credit_id);

                    $walletAmt = Credit::getBalance($request->user_id, $creditFunction->type);
                    $cTransaction = Credit::getAllTransactionAmountBySubjectByDay($request->user_id);

                    $currencyData = CurrencySetting::with([
                        'toCredit' => function ($q) {
                            return $q->with(['creditSetting' => function ($q) {
                                $q->where('name', 'convert-multiplier');
                            }]);
                        },
                        'fromCredit' => function ($q) {
                            return $q->with(['creditSetting' => function ($q) {
                                $q->where('name', 'convert-multiplier');
                            }]);
                        },
                        'toCurrency',
                        'toCurrency.country' => function ($q) {
                            return $q->whereIn('name', Config('general.valid_exchange_country'));
                        },
                        'fromCurrency',
                        'fromCurrency.country' => function ($q) {
                            return $q->whereIn('name', Config('general.valid_exchange_country'));
                        },
                    ])
                        ->where('type', CurrencySetting::$type['fiat'])
                        ->where('disabled', 0);

                    // hardcode: do this because only one setting:
                    // if not valid, then inverse fromCurrency and toCurrency
                    $isValidFromCurrency = in_array($creditFunction->code, Config('general.valid_convert_from_currency'));
                    if ($isValidFromCurrency) {
                        $currencyData = $currencyData->whereRelation('toCurrency', function ($q) use ($toCurrencyIds) {
                            return $q->whereIn('id', $toCurrencyIds);
                        }, )
                            ->whereRelation('fromCurrency', 'from_currency_id', $creditFunction->currency_id);
                    } else {
                        $currencyData = $currencyData->whereRelation('toCurrency', function ($q) use ($creditFunction) {
                            return $q->where('id', $creditFunction->currency_id);
                        }, )
                            ->whereRelation('fromCurrency', 'from_currency_id', $toCurrencyIds);
                    }
                    $currencyData = $currencyData
                        ->get()
                        ->map(function ($q) use (&$isValidFromCurrency, $walletAmt, $cTransaction) {
                            $limit = 0;
                            $quotaUsed = 0;
                            // hardcode: do this because only one setting:
                            // if not valid, then inverse fromCurrency and toCurrency
                            if ($isValidFromCurrency) {
                                $fromCreditId = $q->fromCredit->id;
                                $toCreditId = $q->toCredit->id;
                                $fromCreditMultiplier = $q->fromCredit->creditSetting->first()->value;
                                $toCreditMultiplier = $q->toCredit->creditSetting->first()->value;
                                $id = $q->to_currency_id;
                                $fromCurrencyId = $q->fromCurrency->id;
                                $toCurrencyId = $q->toCurrency->id;
                                $fromCurrencyIso = Convert::$outputConversion[$q->fromCurrency->iso] ?? $q->fromCurrency->iso;
                                $currencyIso = Convert::$outputConversion[$q->toCurrency->iso] ?? $q->toCurrency->iso;
                                $countryIsoCode = $q->toCurrency->country->iso_code2;
                                $fromCountryIsoCode = $q->fromCurrency->country->iso_code2;

                                $fromRate = '1';
                                $toRate = $q->convert_out_rate; // MYR to others rate

                                $amountISO = $currencyIso;
                                $limit = $q->convert_out_daily_limit;

                                if (isset($cTransaction[$toCreditId])) {
                                    if (isset($cTransaction[$toCreditId]['convert-in'])) {
                                        $quotaUsed = $cTransaction[$toCreditId]['convert-in'];
                                    }
                                }
                            } else {
                                $fromCreditId = $q->toCredit->id;
                                $toCreditId = $q->fromCredit->id;
                                $fromCreditMultiplier = $q->toCredit->creditSetting->first()->value;
                                $toCreditMultiplier = $q->fromCredit->creditSetting->first()->value;
                                $id = $q->from_currency_id;
                                $fromCurrencyId = $q->toCurrency->id;
                                $toCurrencyId = $q->fromCurrency->id;
                                $fromCurrencyIso = Convert::$outputConversion[$q->toCurrency->iso] ?? $q->toCurrency->iso;
                                $currencyIso = Convert::$outputConversion[$q->fromCurrency->iso] ?? $q->fromCurrency->iso;
                                $countryIsoCode = $q->fromCurrency->country->iso_code2;
                                $fromCountryIsoCode = $q->toCurrency->country->iso_code2;

                                $fromRate = '1';
                                $toRate = $q->convert_in_rate; // others to MYR rate

                                $amountISO = $fromCurrencyIso;
                                $limit = $q->convert_in_daily_limit;

                                if (isset($cTransaction[$fromCreditId])) {
                                    if (isset($cTransaction[$fromCreditId]['convert-out'])) {
                                        $quotaUsed = -$cTransaction[$fromCreditId]['convert-out'];
                                    }
                                }
                            }

                            $quotaRemain = ($limit - $quotaUsed);

                            return [
                                'from_credit_id' => $fromCreditId,
                                'to_credit_id' => $toCreditId,
                                'from_credit_multiplier' => $fromCreditMultiplier,
                                'to_credit_multiplier' => $toCreditMultiplier,
                                'id' => $id,
                                'from_currency_id' => $fromCurrencyId,
                                'from_currency_iso' => $fromCurrencyIso,
                                'from_country_iso_code' => $fromCountryIsoCode,
                                'currency_id' => $toCurrencyId,
                                'currency_iso' => $currencyIso,
                                'country_iso_code' => $countryIsoCode,
                                'from_rate' => Traits\DecimalTrait::setDecimal($fromRate, 3),
                                'to_rate' => Traits\DecimalTrait::setDecimal($toRate, 3),
                                'min_amount' => Traits\DecimalTrait::setDecimal($q->conversion_min_amount),
                                'max_amount' => Traits\DecimalTrait::setDecimal($q->conversion_max_amount),
                                'daily_limit' => Traits\DecimalTrait::setDecimal($quotaRemain),
                                'min_max_amount_iso' => $amountISO,
                                'balance' => Traits\DecimalTrait::setDecimal($walletAmt),
                            ];
                        });

                    $walletInfo = [
                        'id' => $currencyData->first()['from_credit_id'],
                        'iso' => $currencyData->first()['from_currency_iso'],
                        'balance' => Traits\DecimalTrait::setDecimal($walletAmt),
                        'country_iso_code' => $currencyData->first()['from_country_iso_code'],
                    ];
                    $walletData = $currencyData->toArray();
                },
            ],
            'to_credit_id' => [
                Rule::requiredIf(($request->step >= 2)),
                'integer',
                'exists:credit,id',
                function ($q, $value, $fail) use ($request, &$toCreditName) {
                    $toCredit = Credit::find($request->to_credit_id);
                    if (empty($toCredit)) {
                        $fail(Lang::get('lang.invalid-to-credit'));

                        return;
                    }

                    if ($request->to_credit_id == $request->from_credit_id) {
                        $fail(Lang::get('lang.invalid-to-credit'));

                        return;
                    }

                    $toCreditName = $toCredit->name;
                    $convertTo = CreditSetting::where('credit_id', $request->from_credit_id)->where('name', 'convert-to')->where('value', $toCreditName);
                    if (empty($convertTo)) {
                        $fail(Lang::get('lang.invalid-to-credit'));

                        return;
                    }
                },
            ],
            'from_amount' => [
                Rule::requiredIf(($request->step >= 2) && isset($request->from_credit_id)),
                'string',
                'gt:0',
                'regex:/^(\\d+\\.?\\d{0,2})$/',
            ],
            'to_amount' => [
                Rule::requiredIf(($request->step >= 2) && isset($request->from_credit_id)),
                'string',
                'gt:0',
                'regex:/^(\\d+\\.?\\d{0,2})$/',
            ],
            'amount' => [
                function ($q, $value, $fail) use ($request, &$walletData, &$isValidFromCurrency) {
                    if (! isset($request->from_credit_id)) {
                        return;
                    }
                    $selectedOption = Arr::first($walletData, function ($value, $key) use ($request) {
                        return $value['from_credit_id'] == $request->from_credit_id &&
                            $value['to_credit_id'] == $request->to_credit_id;
                    });

                    $processFee = $selectedOption['processing_fee'] ?? 0;

                    $balance = $selectedOption['balance'];
                    $value = $request['from_amount'];
                    if ($balance < ($value + $processFee)) {
                        $fail(Lang::get('lang.credit-insufficient-balance'));

                        return;
                    }

                    // check fromCredit is valid or not
                    if ($isValidFromCurrency) {
                        $request['amount'] = $request['to_amount']; // get the currency instead of credit
                        $convertedAmount = $request['amount'];
                        $checkAmount = $convertedAmount;

                        $cmpValue = bcdiv($convertedAmount, $selectedOption['to_credit_multiplier']);
                        $isValidMultiplier = ($cmpValue == (int) ($cmpValue));
                        if (! $isValidMultiplier) {
                            // prompt error
                            $fail(Lang::get('lang.amount-not-in-multiplier'));

                            return;
                        }
                    } else {
                        $request['amount'] = $request['from_amount']; // get the currency instead of credit
                        $convertedAmount = Traits\DecimalTrait::setDecimal($request['amount'] / $selectedOption['from_rate'] * $selectedOption['to_rate'], null, true);
                        $checkAmount = $request['amount'];

                        $cmpValue = bcdiv($convertedAmount, $selectedOption['from_credit_multiplier']);
                        $isValidMultiplier = ($cmpValue == (int) ($cmpValue));
                        if (! $isValidMultiplier) {
                            // prompt error
                            $fail(Lang::get('lang.amount-not-in-multiplier'));

                            return;
                        }
                    }

                    $dailyLimitAmount = $selectedOption['daily_limit'] ?? 0;
                    if ($dailyLimitAmount < $checkAmount) {
                        $fail(Lang::get('lang.convert-daily-limit-error', ['dailyLimit' => Traits\DecimalTrait::setDecimal($dailyLimitAmount)]));

                        return;
                    }

                    $minAmount = $selectedOption['min_amount'] ?? 0;
                    if ($minAmount > $checkAmount) {
                        $fail(Lang::get('lang.min-amount-error', ['minAmount' => Traits\DecimalTrait::setDecimal($minAmount)]));

                        return;
                    }
                    $maxAmount = $selectedOption['max_amount'] ?? 0;
                    if ($maxAmount < $checkAmount) {
                        $fail(Lang::get('lang.max-amount-error', ['maxAmount' => Traits\DecimalTrait::setDecimal($maxAmount)]));

                        return;
                    }

                    // reform params for rate usage
                    $request->merge(['amount' => $checkAmount]); // return coin currency instead of fiat
                    $request->merge(['isValidFromCurrency' => $isValidFromCurrency]);
                    $request->merge(['from_currency_id' => $selectedOption['from_currency_id']]);
                    $request->merge(['to_currency_id' => $selectedOption['currency_id']]);
                },
            ],
            'pin' => [
                Rule::requiredIf(function () use ($request) {
                    return in_array(MODULE, ['user', 'app']) && ($request->step == 3);
                }),
                function ($q, $value, $fail) use (&$request) {
                    $check = UserDetail::where('user_id', $request->user_id)->where('name', 'transaction_password')->first();
                    $tempPass = str_pad($value, 6, '0', STR_PAD_LEFT);
                    if (isset($check->value) && Hash::check($tempPass, $check->value) != true) {
                        $fail(Lang::get('lang.invalid-pin'));

                        return;
                    }
                },
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if ($validator->validated()['step'] == 1) {
            $returnData = [
                'wallet_info' => $walletInfo,
                'wallet_data' => $walletData,
            ];
            abort(200, json_encode(['data' => $returnData]));
        }
        if ($validator->validated()['step'] == 2 && (in_array(MODULE, ['user', 'app']))) {
            $returnData = [];
            abort(200, json_encode(['data' => $returnData]));
        } else {
            $res = Convert::add($request->all());
        }
        abort(200, json_encode(['data' => $res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Convert List
     *
     * @module = admin
     *
     * @path = credit/convert-list
     *
     * @permissionName = Convert List
     *
     * @menuType = sub_menu
     *
     * @parent = Transaction Report
     *
     * @method = post
     *
     * @description = To get credit convert list.

     *
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     *
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = username|<username>|string|optional|username|Username filter.
     * @body = from_credit_id|<from_credit_id>|string|optional|1000|From credit filter. (dropdown: currency_filter_convert)
     * @body = to_credit_id|<to_credit_id>|string|optional|1000|To credit filter. (dropdown: currency_filter_convert)
     * @body = from_date|<from_date>|string|optional|2022-08-28|Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Date filter.
     * @body = status|<status>|string|optional|Success|Create status filter.
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = export_data|<export_data>|array|option|{"data":{"Key":"Column Name"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|sponsor-bonus|name of the export file. Required for export

     *
     * @response = {"data":{"list":[{"created_at":"02\/02\/2024 12:36:03","username":"60120000001","from_credit":"myr-credit","to_credit":"usdt-credit","from_credit_iso":"MYR","to_credit_iso":"USDT","from_amount":"10.00","rate":"0.21","to_amount":"2.10","status":"pending","status_display":"lang.pending","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 12:36:03"},{"created_at":"02\/02\/2024 11:47:00","username":"60120000001","from_credit":"myr-credit","to_credit":"usdt-credit","from_credit_iso":"MYR","to_credit_iso":"USDT","from_amount":"10.00","rate":"0.21","to_amount":"2.10","status":"pending","status_display":"lang.pending","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 11:47:00"},{"created_at":"31\/01\/2024 19:41:26","username":"60120000001","from_credit":"myr-credit","to_credit":"thb-credit","from_credit_iso":"MYR","to_credit_iso":"THB","from_amount":"10.00","rate":"7.50","to_amount":"75.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"31\/01\/2024 19:54:43"},{"created_at":"31\/01\/2024 19:40:36","username":"60120000001","from_credit":"myr-credit","to_credit":"thb-credit","from_credit_iso":"MYR","to_credit_iso":"THB","from_amount":"10.00","rate":"7.50","to_amount":"1.33","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"31\/01\/2024 19:54:43"},{"created_at":"31\/01\/2024 18:59:58","username":"60120000001","from_credit":"myr-credit","to_credit":"thb-credit","from_credit_iso":"MYR","to_credit_iso":"THB","from_amount":"10.00","rate":"1.00","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"31\/01\/2024 19:54:43"},{"created_at":"30\/01\/2024 18:17:32","username":"60120000001","from_credit":"myr-credit","to_credit":"thb-credit","from_credit_iso":"MYR","to_credit_iso":"THB","from_amount":"1.00","rate":"1.00","to_amount":"1.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"31\/01\/2024 19:54:43"},{"created_at":"30\/01\/2024 18:15:03","username":"60120000001","from_credit":"myr-credit","to_credit":"thb-credit","from_credit_iso":"MYR","to_credit_iso":"THB","from_amount":"1.00","rate":"1.00","to_amount":"1.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"31\/01\/2024 19:54:43"},{"created_at":"29\/01\/2024 16:44:26","username":"60120000001","from_credit":"myr-credit","to_credit":"thb-credit","from_credit_iso":"MYR","to_credit_iso":"THB","from_amount":"1.00","rate":"1.00","to_amount":"1.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"31\/01\/2024 19:54:43"},{"created_at":"29\/01\/2024 16:43:38","username":"60120000001","from_credit":"myr-credit","to_credit":"thb-credit","from_credit_iso":"MYR","to_credit_iso":"THB","from_amount":"1.00","rate":"1.00","to_amount":"1.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"31\/01\/2024 19:54:43"},{"created_at":"29\/01\/2024 16:40:46","username":"60120000001","from_credit":"myr-credit","to_credit":"thb-credit","from_credit_iso":"MYR","to_credit_iso":"THB","from_amount":"1.00","rate":"1.00","to_amount":"1.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"31\/01\/2024 19:54:43"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":10,"total":10},"meta":null,"summary":{"convert_from":{"MYR":{"name":"myr-credit","display":"myr-credit","iso":"MYR","total":"20.********"}},"convert_to":{"THB":{"name":"thb-credit","display":"thb-credit","iso":"THB","total":"91.33000000"},"USDT":{"name":"usdt-credit","display":"usdt-credit","iso":"USDT","total":"4.20000000"}}}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2NyZWRpdC9jb252ZXJzaW9uLWxpc3QiLCJpYXQiOjE3MDY4NDg5NDMsImV4cCI6MTcwNjg1MzIzMiwibmJmIjoxNzA2ODQ5NjMyLCJqdGkiOiJDUHJScjdvVkNWVGhoM01wIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.fcvNa7JYxIBZty_Nv4zIFb9-Vyuhetgc_Y-oEq_l67g","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.060172080993652 sec","log_id":"a03304a3-2380-421b-99c6-3717367c4b99"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = credit/convert-list
     *
     * @module = user,app
     *
     * @path = credit/convert-list
     *
     * @method = post
     *
     * @description = To get credit convert list.

     *
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     *
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = username|<username>|string|optional|username|Username filter.
     * @body = from_credit_id|<from_credit_id>|string|optional|1000|From credit filter.
     * @body = to_credit_id|<to_credit_id>|string|optional|1000|To credit filter.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Date filter.
     * @body = status|<status>|string|optional|Success|Create status filter.
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = export_data|<export_data>|array|option|{"data":{"Key":"Column Name"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|sponsor-bonus|name of the export file. Required for export

     *
     * @response = {"data":{"list":[{"created_at":"02\/02\/2024 13:02:48","username":"60120000001","from_credit":"myr-credit","to_credit":"usdt-credit","from_credit_iso":"MYR","to_credit_iso":"USDT","from_amount":"10.00","rate":"0.21","to_amount":"2.10","status":"pending","status_display":"lang.pending","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 13:02:48"},{"created_at":"02\/02\/2024 12:36:03","username":"60120000001","from_credit":"myr-credit","to_credit":"usdt-credit","from_credit_iso":"MYR","to_credit_iso":"USDT","from_amount":"10.00","rate":"0.21","to_amount":"2.10","status":"pending","status_display":"lang.pending","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 12:36:03"},{"created_at":"02\/02\/2024 11:47:00","username":"60120000001","from_credit":"myr-credit","to_credit":"usdt-credit","from_credit_iso":"MYR","to_credit_iso":"USDT","from_amount":"10.00","rate":"0.21","to_amount":"2.10","status":"pending","status_display":"lang.pending","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 11:47:00"},{"created_at":"02\/02\/2024 10:22:58","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 10:22:59"},{"created_at":"02\/02\/2024 10:11:41","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 10:11:43"},{"created_at":"02\/02\/2024 10:08:19","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 10:08:21"},{"created_at":"02\/02\/2024 10:04:44","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 10:04:44"},{"created_at":"02\/02\/2024 10:04:25","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 10:04:27"},{"created_at":"02\/02\/2024 10:04:03","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 10:04:06"},{"created_at":"02\/02\/2024 10:03:42","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 10:03:44"},{"created_at":"02\/02\/2024 10:02:10","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 10:02:11"},{"created_at":"02\/02\/2024 10:01:30","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 10:01:32"},{"created_at":"02\/02\/2024 10:00:12","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 10:00:12"},{"created_at":"02\/02\/2024 09:59:58","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 10:00:00"},{"created_at":"02\/02\/2024 09:59:08","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 09:59:08"},{"created_at":"02\/02\/2024 09:51:24","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 09:51:27"},{"created_at":"02\/02\/2024 09:50:48","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 09:50:48"},{"created_at":"02\/02\/2024 09:48:42","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 09:48:42"},{"created_at":"02\/02\/2024 09:47:24","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 09:47:24"},{"created_at":"02\/02\/2024 09:34:25","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 09:34:27"},{"created_at":"02\/02\/2024 09:30:55","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 09:30:56"},{"created_at":"02\/02\/2024 09:28:17","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"02\/02\/2024 09:28:19"},{"created_at":"31\/01\/2024 19:47:22","username":"60120000001","from_credit":"thb-credit","to_credit":"myr-credit","from_credit_iso":"THB","to_credit_iso":"MYR","from_amount":"75.00","rate":"7.50","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"31\/01\/2024 19:54:43"},{"created_at":"31\/01\/2024 19:41:26","username":"60120000001","from_credit":"myr-credit","to_credit":"thb-credit","from_credit_iso":"MYR","to_credit_iso":"THB","from_amount":"10.00","rate":"7.50","to_amount":"75.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"31\/01\/2024 19:54:43"},{"created_at":"31\/01\/2024 19:40:36","username":"60120000001","from_credit":"myr-credit","to_credit":"thb-credit","from_credit_iso":"MYR","to_credit_iso":"THB","from_amount":"10.00","rate":"7.50","to_amount":"1.33","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"31\/01\/2024 19:54:43"},{"created_at":"31\/01\/2024 18:59:58","username":"60120000001","from_credit":"myr-credit","to_credit":"thb-credit","from_credit_iso":"MYR","to_credit_iso":"THB","from_amount":"10.00","rate":"1.00","to_amount":"10.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"31\/01\/2024 19:54:43"},{"created_at":"30\/01\/2024 18:17:32","username":"60120000001","from_credit":"myr-credit","to_credit":"thb-credit","from_credit_iso":"MYR","to_credit_iso":"THB","from_amount":"1.00","rate":"1.00","to_amount":"1.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"31\/01\/2024 19:54:43"},{"created_at":"30\/01\/2024 18:15:03","username":"60120000001","from_credit":"myr-credit","to_credit":"thb-credit","from_credit_iso":"MYR","to_credit_iso":"THB","from_amount":"1.00","rate":"1.00","to_amount":"1.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"31\/01\/2024 19:54:43"},{"created_at":"29\/01\/2024 16:44:26","username":"60120000001","from_credit":"myr-credit","to_credit":"thb-credit","from_credit_iso":"MYR","to_credit_iso":"THB","from_amount":"1.00","rate":"1.00","to_amount":"1.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"31\/01\/2024 19:54:43"},{"created_at":"29\/01\/2024 16:43:38","username":"60120000001","from_credit":"myr-credit","to_credit":"thb-credit","from_credit_iso":"MYR","to_credit_iso":"THB","from_amount":"1.00","rate":"1.00","to_amount":"1.00","status":"successful","status_display":"lang.successful","country":"malaysia","country_display":"Malaysia","remark":null,"updated_at":"31\/01\/2024 19:54:43"}],"pagination":{"current_page":1,"from":1,"last_page":2,"per_page":30,"to":30,"total":31},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS9jcmVkaXQvY29udmVydC1saXN0IiwiaWF0IjoxNzA2ODUwMTY2LCJleHAiOjE3MDY4NTM3NzksIm5iZiI6MTcwNjg1MDE3OSwianRpIjoiVGhyRUxrdEU0MVY3SVZPcSIsInN1YiI6IjEwMDAwMDEiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.s-uNqcCdXZwKlQhsKRmJm6f8_qmRT8y6nKnvdTP5dNM","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.085873126983643 sec","log_id":"77455d26-4f50-4935-8651-c8a61cc771ae"}
     * ##docs end##
     */
    public function getConversionList(Request $request)
    {
        if (MODULE == 'user') {
            $request->request->add(['user_id' => Auth::user()->id]);
        }

        $validator = Validator::make($request->all(), [
            'limit' => 'integer',
            'page' => 'integer',
            'username' => 'string',
            'from_credit_id' => 'integer|exists:credit,id',
            'to_credit_id' => 'integer|exists:credit,id',
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d',
            'status' => 'string|in:'.implode(',', array_keys(Convert::$status)),

            'see_all' => 'integer|in:1,0',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string',
            'file_name' => 'required_if:export,==,1',

        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Convert::list($request->all());

        abort(200, json_encode(['data' => $res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = User Wallet Detail
     *
     * @module = admin
     *
     * @path = credit/user-wallet-detail
     *
     * @method = post
     *
     * @description = To get user wallet detail.

     *
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     *
     * @body = id|<id>|integer|required|1|User's id.
     * @body = credit_id|<credit_id>|integer|required|1|Credit's id.

     *
     * @response = {"data":{"user_detail":{"full_name":"ghostmate","currency_code":"MYR","username":"ghostmate"},"walletBalance":{"amount":"89690.98000000","est":0},"setting":{"is-adjustable":1,"is-convertible":0,"is-fundinable":0,"is-transferable":0,"is-wallet":1,"is-withdrawable":0}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmNxcS5jb20vY3JlZGl0L3VzZXItd2FsbGV0LWRldGFpbCIsImlhdCI6MTY3Mjc5NjczOSwiZXhwIjoxNjcyODAwNDM4LCJuYmYiOjE2NzI3OTY4MzgsImp0aSI6IlVxbUxzWmtOMnBuVEZnUjIiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.9C1KPjMgYfhYLgOuojnR-LVaaKH9ZvPGHzspWLx89JY","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.56303787231445 sec","log_id":"091f7e7c-7e6f-4e42-9328-bc6200604475"}
     * ##docs end##
     */
    public function getUserWalletDetail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer|exists:users,id',
            'credit_id' => 'required|integer|exists:credit,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Credit::getUserWalletDetail($request->all());
        abort(200, json_encode(['data' => $res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = credit/manual-bank-deposit
     *
     * @module = user,app
     *
     * @path = credit/manual-bank-deposit
     *
     * @method = POST
     *
     * @description = To upload bank transfer receipt for deposit.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.

     *
     * @body = credit_id|<credit_id>|integer|required|1000|Credit id.
     * @body = amount|<amount>|integer|required|50|amount want to top up
     * @body = attachment|<attachment>|string|required|/local/haha|URL to aws s3
     * @body = reference_no|<reference_no>|string|required|j@u9wj03e2J|Reference number for receipt
     * @body = remark|<remark>|string|optional|Pay Me Double|remark user wish to put

     *
     * @response = {"data":{"transaction_id":"9eDtjG38eVuNJwYx","reference_no":"***********","remark":"remark test","created_at":"26\/04\/2023 15:56:37","deposit_type":"manual-bank","amount":"100.00"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.yo-xlvDYQqStwzEGj28RaHMo7PDHymxtevmHf96nIT4","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"85fd6817-2a31-4bcd-a9bf-098a60a8dceb"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = credit/help-centre-deposit
     *
     * @module = user,app
     *
     * @path = credit/help-centre-deposit
     *
     * @method = POST
     *
     * @description = To upload bank transfer receipt for thb deposit.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.

     *
     * @body = credit_id|<credit_id>|integer|required|1002|Credit id.
     * @body = amount|<amount>|integer|required|50|amount want to top up
     * @body = attachment|<attachment>|string|required|/local/haha|URL to aws s3
     * @body = reference_no|<reference_no>|string|required|j@u9wj03e2J|Reference number for receipt
     * @body = remark|<remark>|string|optional|Pay Me Double|remark user wish to put

     *
     * @response = {"data":{"transaction_id":"Tz9mfKmb2kBgHZhB","reference_no":"haha","remark":"test remark","created_at":"13\/03\/2024 18:14:31","deposit_type":"thb-manual-bank","amount":"100.00","fpay_url":null,"fpay_redirect_url":"?order_id=Tz9mfKmb2kBgHZhB","currency":"THB","send":false},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.ewPFG5vKU2kwUcwSDSe-O1B0AReVhsXT2CgplfXDOro","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"83b121a7-af1e-4e34-8319-0f544111504b"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = credit/manual-bank-deposit
     *
     * @module = admin
     *
     * @path = credit/manual-bank-deposit
     *
     * @method = POST
     *
     * @description = To upload purchase receipt.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = user_id|<user_id>|integer|required|1|User id.
     * @body = attachment|<attachment>|string|required|/local/haha|URL to aws s3
     * @body = reference_no|<reference_no>|string|required|j@u9wj03e2J|Reference number for receipt

     *
     * @response = {"status":true,"message":"Updated Successfully.","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.UjLkJP6dXCa0Mub9lySjAo-pvTNZDq4kTFJyd7rrYg0","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"60bc998d-d929-4b8c-8878-feeb2eb7602b"}
     * ##docs end##
     */
    public function manualBankDeposit(Request $request)
    {
        $userId = Auth::user()->id ?? null;
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => $userId]);
        }

        $referenceNoRequired = 0;
        $specialCreditTypeChecking = 0;
        switch ($request->path()) {
            case 'credit/help-centre-deposit':
                $request->request->add(['deposit_type' => 'thb-manual-bank']);
                $referenceNoRequired = 1;
                $specialCreditTypeChecking = 1;
                break;

            default:
                $request->request->add(['deposit_type' => 'manual-bank']);
                break;
        }

        if (! isset($request->credit_id)) {
            $creditId = Credit::where('name', 'myr-credit')->first()->id ?? null;
            $request->request->add(['credit_id' => $creditId]);
        }

        $validator = Validator::make($request->all(), [
            'credit_id' => [
                'required',
                'exists:credit,id',
                function ($q, $value, $fail) use ($specialCreditTypeChecking) {
                    if ($specialCreditTypeChecking) {
                        $checkCol = MODULE;
                        if (in_array(MODULE, ['app', 'user'])) {
                            $checkCol = 'member';
                        }
                        $creditID = CreditSetting::where(['name' => 'special-fundin-type', 'value' => 1, $checkCol => 1])->get()->toArray() ?? [];
                        if (! in_array($value, array_column($creditID, 'credit_id'))) {
                            return $fail(Lang::get('validation.in', ['attribute' => str_replace('_', ' ', $q)]));
                        }
                    }
                },
            ],
            'user_id' => 'required|integer',
            'card_id' => [
                'nullable',
                'integer',
                'exists:user_card,id',
                function ($q, $value, $fail) {
                    $cardRes = UserCard::with([
                        'store' => function ($q) {
                            $q->select('store_id', 'name');
                        },
                    ])->find($value);

                    if ($cardRes->status == UserCard::$status['active']) {
                        $type = 'check_game_credit';
                        $curlParams = [
                            'storeId' => $cardRes->store_id,
                            'cardId' => $cardRes->card_id,
                        ];
                        $result = Traits\SonicTrait::post($curlParams, $type);
                        if (! isset($result['status']) || $result['status'] == false) {
                            return $fail('Store is offline');
                        }
                    }
                },
            ],
            'amount' => 'required|string',
            'attachment' => 'required|string',
            'reference_no' => [
                Rule::requiredIf($referenceNoRequired),
                'nullable',
                'string',
                'max:50',
            ],
            'remark' => 'nullable|string|max:255',
            'deposit_type' => 'required|string|in:manual-bank,thb-manual-bank',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Deposit::uploadBankReceipt($validator->validated());

        abort(200, json_encode(['data' => $res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Deposit List
     *
     * @module = admin
     *
     * @path = credit/get-deposit-list
     *
     * @method = POST
     *
     * @description = To get all user deposit request.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = username|<username>|string|optional|Username|username filter.
     * @body = phone_no|<phone_no>|string|optional|***********|User Phone filter.
     * @body = store_id|<store_id>|integer|optional|27|Store. (dropdown: store)
     * @body = status|<status>|string|optional|status|Create status filter.
     * @body = type|<type>|string|optional|manual|Create type filter
     * @body = transaction_id|<transaction_id>|string|optional|9eDtjG38eVuNJwYx|transaction unique id filter
     * @body = reference_no|<reference_no>|string|optional|***********|User input reference no filter
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = updated_from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = updated_to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = country|<country>|integer|optional|100|Country filter. (Dropdown: country)
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = export_data|<export_data>|array|optional|{"data": {"created_at": "Date"}|Data need to export value => header. Required for export
     * @body = file_name|<file_name>|string|optional|withdrawal-list|name of the export file. Required for export
     *
     * @response = {"data":{"list":[{"id":152,"created_at":"25\/07\/2023 14:43:22","transaction_id":"XcVF6Zz7o9MyPtBG","payment_type":"manual-bank","payment_type_display":"manual-bank","from_address":"-","receivable_amount":"3000.00","currency":"MYR","tx_id":"-","status":"approved","attachment":"production\/2023\/07\/1690267400_8232","reference_no":"00","user_remark":"top up","admin_remark":null,"status_display":"Approved","username":"60**********","member_id":"*********","phone_no":"60-**********","payment_id":0,"code":"XcVF6Zz7o9MyPtBG","credit_type":"myr-credit","credit_type_display":"myr-credit","country":"malaysia","country_display":"Malaysia","amount":"3000.00","rate":"1.00","converted_amount":"3000.00","charges":"0.00","approved_at":"25\/07\/2023 14:46:03","updater":"bovm"},{"id":150,"created_at":"25\/07\/2023 13:53:12","transaction_id":"Z4oT2W4Pvbm11uMs","payment_type":"manual-bank","payment_type_display":"manual-bank","from_address":"-","receivable_amount":"3000.00","currency":"MYR","tx_id":"-","status":"rejected","attachment":"production\/2023\/07\/1690264391_1980","reference_no":"00","user_remark":"top up","admin_remark":"duplicate slip , pls upload 1.23pm slip","status_display":"Rejected","username":"60**********","member_id":"*********","phone_no":"60-**********","payment_id":0,"code":"Z4oT2W4Pvbm11uMs","credit_type":"myr-credit","credit_type_display":"myr-credit","country":"malaysia","country_display":"Malaysia","amount":"3000.00","rate":"1.00","converted_amount":"3000.00","charges":"0.00","approved_at":"25\/07\/2023 14:36:47","updater":"bovm"},{"id":140,"created_at":"24\/07\/2023 19:14:24","transaction_id":"se1zre1t52fZ5XVh","payment_type":"manual-bank","payment_type_display":"manual-bank","from_address":"-","receivable_amount":"2000.00","currency":"MYR","tx_id":"-","status":"approved","attachment":"production\/2023\/07\/1690197262_0708","reference_no":"00","user_remark":"top up","admin_remark":null,"status_display":"Approved","username":"60**********","member_id":"*********","phone_no":"60-**********","payment_id":0,"code":"se1zre1t52fZ5XVh","credit_type":"myr-credit","credit_type_display":"myr-credit","country":"malaysia","country_display":"Malaysia","amount":"2000.00","rate":"1.00","converted_amount":"2000.00","charges":"0.00","approved_at":"24\/07\/2023 20:29:43","updater":"bovm"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":3,"total":3},"meta":null,"summary":{"total_amount":"8000.00","total_pending_amount":0,"total_approved_amount":"5000.00","total_rejected_amount":"3000.00","total_cancelled_amount":0},"table_total":{"total_amount":"8000.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************.trqVWmoLzpT3P3pyWBjenopESeLdBIUZ6iFF3LkIjCY","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"df2763c1-67c0-417c-be39-8950506bf989"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Deposit List
     *
     * @module = user,app
     *
     * @path = credit/get-deposit-list
     *
     * @method = POST
     *
     * @description = To get user deposit request.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = credit_id|<credit_id>|integer|required|1|Credit ID
     * @body = status|<status>|string|optional|status|Create status filter.
     * @body = type|<type>|string|optional|manual|Create type filter
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.
     *
     * @response = {"data":{"list":[{"id":1,"created_at":"26\/04\/2023 15:40:18","transaction_id":"jiwm5d2fd33k8drw","payment_type":"manual-bank","payment_type_display":"manual-bank","from_address":"-","receivable_amount":"100.00","currency":"MYR","tx_id":"-","status":"approved","attachment":"asd.jpg","reference_no":"***********","user_remark":"remark test","admin_remark":"test good remark","status_display":"Approved"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************.TjXYPk5KawBCi1b4R62_gmEhj95Nda8wYtxXau_x8ew","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"8f133952-698b-4a08-9cb0-4a907aca35e5"}
     * ##docs end##
     */
    public function getDepositList(Request $request)
    {
        if (MODULE == 'admin') {
            $request->request->add(['type' => array_search(Deposit::$type['manual-bank'], Deposit::$type), 'credit_type' => 'myr-credit']);
            $adminId = auth()->user()->id;

            $adminRes = Models\Admin::with(
                [
                    'adminDetail' => function ($q) {
                        return $q->where('name', 'store');
                    },
                ],
            )
                ->find($adminId);

            $isMaster = $adminRes->is_master;
            if (! $isMaster) {
                $branchJE = $adminRes->adminDetail->first()->value ?? '[]';
                $branchDE = json_decode($branchJE);

                $request->merge(['extra_store_id' => $branchDE]);
            }
        }
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => auth()->user()->id]);
            if (! isset($request->credit_id)) {
                $request->request->add(['credit_id' => Credit::first()->id]);
            }
        }
        if (! isset($request->credit_type)) {
            $request->request->add(['credit_type' => 'myr-credit']);
        }
        if (isset($request->credit_id)) {
            $credit = Credit::find($request->credit_id);
            if (isset($credit)) {
                $request->request->add(['credit_type' => $credit->type]);
            }
        }
        $validator = Validator::make($request->all() + ['module' => MODULE], [
            'order_sort' => 'string|in:asc,desc',
            'credit_id' => 'required_if:module,user|required_if:module,app|integer|exists:credit,id',
            'credit_type' => 'required|string|exists:credit,type',
            'store_id' => 'nullable|integer',
            'user_id' => 'int',
            'username' => 'string',
            'member_id' => 'string',
            'phone_no' => 'string',
            'transaction_id' => 'string',
            'reference_no' => 'string',
            'extra_store_id' => 'nullable',
            'status' => 'string|in:'.implode(',', array_keys(Deposit::$status)),
            'type' => 'string|in:'.implode(',', array_keys(Deposit::$type)),
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d',
            'updated_from_date' => 'required_with:updated_from_date|string|date_format:Y-m-d',
            'updated_to_date' => 'required_with:updated_to_date|string|date_format:Y-m-d',
            'country' => 'integer',
            'see_all' => 'integer|in:1,0',
            'limit' => 'integer',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string',
            'file_name' => 'required_if:export,==,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $getDeposit = Deposit::getList($validator->validated());
        $data['data'] = $getDeposit;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = FPAY Deposit List
     *
     * @module = admin
     *
     * @path = credit/get-fpay-deposit-list
     *
     * @method = POST
     *
     * @description = To get all user fpay deposit request.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = username|<username>|string|optional|Username|username filter.
     * @body = phone_no|<phone_no>|string|optional|***********|User Phone filter.
     * @body = store_id|<store_id>|integer|optional|27|Store. (dropdown: store)
     * @body = status|<status>|string|optional|status|Create status filter.
     * @body = type|<type>|string|optional|manual|Create type filter
     * @body = transaction_id|<transaction_id>|string|optional|9eDtjG38eVuNJwYx|transaction unique id filter
     * @body = reference_no|<reference_no>|string|optional|***********|User input reference no filter
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = updated_from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = updated_to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = country|<country>|integer|optional|100|Country filter. (Dropdown: country)
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = export_data|<export_data>|array|optional|{"data": {"created_at": "Date"}|Data need to export value => header. Required for export
     * @body = file_name|<file_name>|string|optional|withdrawal-list|name of the export file. Required for export
     *
     * @response = {"data":{"list":[{"id":152,"created_at":"25\/07\/2023 14:43:22","transaction_id":"XcVF6Zz7o9MyPtBG","payment_type":"manual-bank","payment_type_display":"manual-bank","from_address":"-","receivable_amount":"3000.00","currency":"MYR","tx_id":"-","status":"approved","attachment":"production\/2023\/07\/1690267400_8232","reference_no":"00","user_remark":"top up","admin_remark":null,"status_display":"Approved","username":"60**********","member_id":"*********","phone_no":"60-**********","payment_id":0,"code":"XcVF6Zz7o9MyPtBG","credit_type":"myr-credit","credit_type_display":"myr-credit","country":"malaysia","country_display":"Malaysia","amount":"3000.00","rate":"1.00","converted_amount":"3000.00","charges":"0.00","approved_at":"25\/07\/2023 14:46:03","updater":"bovm"},{"id":150,"created_at":"25\/07\/2023 13:53:12","transaction_id":"Z4oT2W4Pvbm11uMs","payment_type":"manual-bank","payment_type_display":"manual-bank","from_address":"-","receivable_amount":"3000.00","currency":"MYR","tx_id":"-","status":"rejected","attachment":"production\/2023\/07\/1690264391_1980","reference_no":"00","user_remark":"top up","admin_remark":"duplicate slip , pls upload 1.23pm slip","status_display":"Rejected","username":"60**********","member_id":"*********","phone_no":"60-**********","payment_id":0,"code":"Z4oT2W4Pvbm11uMs","credit_type":"myr-credit","credit_type_display":"myr-credit","country":"malaysia","country_display":"Malaysia","amount":"3000.00","rate":"1.00","converted_amount":"3000.00","charges":"0.00","approved_at":"25\/07\/2023 14:36:47","updater":"bovm"},{"id":140,"created_at":"24\/07\/2023 19:14:24","transaction_id":"se1zre1t52fZ5XVh","payment_type":"manual-bank","payment_type_display":"manual-bank","from_address":"-","receivable_amount":"2000.00","currency":"MYR","tx_id":"-","status":"approved","attachment":"production\/2023\/07\/1690197262_0708","reference_no":"00","user_remark":"top up","admin_remark":null,"status_display":"Approved","username":"60**********","member_id":"*********","phone_no":"60-**********","payment_id":0,"code":"se1zre1t52fZ5XVh","credit_type":"myr-credit","credit_type_display":"myr-credit","country":"malaysia","country_display":"Malaysia","amount":"2000.00","rate":"1.00","converted_amount":"2000.00","charges":"0.00","approved_at":"24\/07\/2023 20:29:43","updater":"bovm"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":3,"total":3},"meta":null,"summary":{"total_amount":"8000.00","total_pending_amount":0,"total_approved_amount":"5000.00","total_rejected_amount":"3000.00","total_cancelled_amount":0},"table_total":{"total_amount":"8000.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************.trqVWmoLzpT3P3pyWBjenopESeLdBIUZ6iFF3LkIjCY","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"df2763c1-67c0-417c-be39-8950506bf989"}
     * ##docs end##
     */
    public function getFPayDepositList(Request $request)
    {
        $typeArrray = [
            Deposit::$type['online-bank'],
            Deposit::$type['onepay-online-bank'],
            Deposit::$type['rapidpay-online-bank'],
            Deposit::$type['wepay-online-bank'],
        ];

        $request->request->add([
            'type' => $typeArrray,
            'credit_type' => 'myr-credit',
        ]);
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => auth()->user()->id]);
        }

        if (in_array(MODULE, ['admin'])) {
            $adminId = auth()->user()->id;
            $adminRes = Models\Admin::with(
                [
                    'adminDetail' => function ($q) {
                        return $q->where('name', 'store');
                    },
                ],
            )
                ->find($adminId);

            $isMaster = $adminRes->is_master;
            if (! $isMaster) {
                $branchJE = $adminRes->adminDetail->first()->value ?? '[]';
                $branchDE = json_decode($branchJE);

                $request->merge(['extra_store_id' => $branchDE]);
            }
        }

        $validator = Validator::make($request->all(), [
            'order_sort' => 'string|in:asc,desc',
            'credit_type' => 'required|string|exists:credit,type',
            'user_id' => 'int',
            'username' => 'string',
            'member_id' => 'string',
            'store_id' => 'nullable|integer',
            'phone_no' => 'string',
            'transaction_id' => 'string',
            'reference_no' => 'string',
            'extra_store_id' => 'nullable',
            'status' => 'string|in:'.implode(',', array_keys(Deposit::$status)),
            'type' => 'array|in:'.implode(',', array_values(Deposit::$type)),
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d',
            'updated_from_date' => 'required_with:updated_from_date|string|date_format:Y-m-d',
            'updated_to_date' => 'required_with:updated_to_date|string|date_format:Y-m-d',
            'country' => 'integer',
            'see_all' => 'integer|in:1,0',
            'limit' => 'integer',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string',
            'file_name' => 'required_if:export,==,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $getDeposit = Deposit::getList($validator->validated());
        $data['data'] = $getDeposit;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = FPAY Telco Deposit List
     *
     * @module = admin
     *
     * @path = credit/get-fpay-telco-deposit-list
     *
     * @method = POST
     *
     * @description = To get all user fpay telco deposit request.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = username|<username>|string|optional|Username|username filter.
     * @body = phone_no|<phone_no>|string|optional|***********|User Phone filter.
     * @body = status|<status>|string|optional|status|Create status filter.
     * @body = type|<type>|string|optional|manual|Create type filter
     * @body = transaction_id|<transaction_id>|string|optional|9eDtjG38eVuNJwYx|transaction unique id filter
     * @body = reference_no|<reference_no>|string|optional|***********|User input reference no filter
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = updated_from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = updated_to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = country|<country>|integer|optional|100|Country filter. (Dropdown: country)
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = export_data|<export_data>|array|optional|{"data": {"created_at": "Date"}|Data need to export value => header. Required for export
     * @body = file_name|<file_name>|string|optional|withdrawal-list|name of the export file. Required for export
     *
     * @response = {"data":{"list":[{"id":152,"created_at":"25\/07\/2023 14:43:22","transaction_id":"XcVF6Zz7o9MyPtBG","payment_type":"manual-bank","payment_type_display":"manual-bank","from_address":"-","receivable_amount":"3000.00","currency":"MYR","tx_id":"-","status":"approved","attachment":"production\/2023\/07\/1690267400_8232","reference_no":"00","user_remark":"top up","admin_remark":null,"status_display":"Approved","username":"60**********","member_id":"*********","phone_no":"60-**********","payment_id":0,"code":"XcVF6Zz7o9MyPtBG","credit_type":"myr-credit","credit_type_display":"myr-credit","country":"malaysia","country_display":"Malaysia","amount":"3000.00","rate":"1.00","converted_amount":"3000.00","charges":"0.00","approved_at":"25\/07\/2023 14:46:03","updater":"bovm"},{"id":150,"created_at":"25\/07\/2023 13:53:12","transaction_id":"Z4oT2W4Pvbm11uMs","payment_type":"manual-bank","payment_type_display":"manual-bank","from_address":"-","receivable_amount":"3000.00","currency":"MYR","tx_id":"-","status":"rejected","attachment":"production\/2023\/07\/1690264391_1980","reference_no":"00","user_remark":"top up","admin_remark":"duplicate slip , pls upload 1.23pm slip","status_display":"Rejected","username":"60**********","member_id":"*********","phone_no":"60-**********","payment_id":0,"code":"Z4oT2W4Pvbm11uMs","credit_type":"myr-credit","credit_type_display":"myr-credit","country":"malaysia","country_display":"Malaysia","amount":"3000.00","rate":"1.00","converted_amount":"3000.00","charges":"0.00","approved_at":"25\/07\/2023 14:36:47","updater":"bovm"},{"id":140,"created_at":"24\/07\/2023 19:14:24","transaction_id":"se1zre1t52fZ5XVh","payment_type":"manual-bank","payment_type_display":"manual-bank","from_address":"-","receivable_amount":"2000.00","currency":"MYR","tx_id":"-","status":"approved","attachment":"production\/2023\/07\/1690197262_0708","reference_no":"00","user_remark":"top up","admin_remark":null,"status_display":"Approved","username":"60**********","member_id":"*********","phone_no":"60-**********","payment_id":0,"code":"se1zre1t52fZ5XVh","credit_type":"myr-credit","credit_type_display":"myr-credit","country":"malaysia","country_display":"Malaysia","amount":"2000.00","rate":"1.00","converted_amount":"2000.00","charges":"0.00","approved_at":"24\/07\/2023 20:29:43","updater":"bovm"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":3,"total":3},"meta":null,"summary":{"total_amount":"8000.00","total_pending_amount":0,"total_approved_amount":"5000.00","total_rejected_amount":"3000.00","total_cancelled_amount":0},"table_total":{"total_amount":"8000.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************.trqVWmoLzpT3P3pyWBjenopESeLdBIUZ6iFF3LkIjCY","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"df2763c1-67c0-417c-be39-8950506bf989"}
     * ##docs end##
     */
    public function getFPayTelcoDepositList(Request $request)
    {
        $request->request->add(['type' => array_search(Deposit::$type['online-bank-telco'], Deposit::$type), 'credit_type' => 'myr-credit']);
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => auth()->user()->id]);
        }
        $validator = Validator::make($request->all(), [
            'order_sort' => 'string|in:asc,desc',
            'credit_type' => 'required|string|exists:credit,type',
            'user_id' => 'int',
            'username' => 'string',
            'member_id' => 'string',
            'phone_no' => 'string',
            'transaction_id' => 'string',
            'reference_no' => 'string',
            'status' => 'string|in:'.implode(',', array_keys(Deposit::$status)),
            'type' => 'string|in:'.implode(',', array_keys(Deposit::$type)),
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d',
            'updated_from_date' => 'required_with:updated_from_date|string|date_format:Y-m-d',
            'updated_to_date' => 'required_with:updated_to_date|string|date_format:Y-m-d',
            'country' => 'integer',
            'see_all' => 'integer|in:1,0',
            'limit' => 'integer',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string',
            'file_name' => 'required_if:export,==,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $getDeposit = Deposit::getList($validator->validated());
        $data['data'] = $getDeposit;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Platform FPAY Deposit List
     *
     * @module = admin
     *
     * @path = credit/get-platform-fpay-deposit-list
     *
     * @method = POST
     *
     * @description = To get all user platform fpay deposit request.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = member_id|<member_id>|string|optional|Member ID|member_id filter.
     * @body = username|<username>|string|optional|Username|username filter.
     * @body = phone_no|<phone_no>|string|optional|***********|User Phone filter.
     * @body = status|<status>|string|optional|status|Create status filter.
     * @body = transaction_id|<transaction_id>|string|optional|9eDtjG38eVuNJwYx|transaction unique id filter
     * @body = reference_no|<reference_no>|string|optional|***********|User input reference no filter
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = updated_from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = updated_to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = country|<country>|integer|optional|100|Country filter. (Dropdown: country)
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = export_data|<export_data>|array|optional|{"data": {"created_at": "Date"}|Data need to export value => header. Required for export
     * @body = file_name|<file_name>|string|optional|withdrawal-list|name of the export file. Required for export
     *
     * @response = {"data":{"list":[{"id":685,"created_at":"03/04/2024 14:28:57","updated_at":"03/04/2024 14:30:39","transaction_id":"GOCBCkfGSJUuJTtFA","payment_type":"online-bank-tk8th","payment_type_display":"online-bank-tk8th","from_address":"-","receivable_amount":"10.00","currency":"MYR","tx_id":"-","tx_id_hyperlink":"-","status":"approved","attachment":null,"reference_no":null,"user_remark":null,"admin_remark":null,"status_display":"Approved","username":"**********","member_id":"*********","phone_no":"**********","payment_id":0,"code":"GOCBCkfGSJUuJTtFA","credit_type":"myr-credit","credit_type_display":"MYR Wallet","amount":"10.00","rate":"1.00","converted_amount":"10.00","charges":"0.00","approved_at":"03/04/2024 14:30:39","updater":null}],"pagination":{"current_page":1,"from":1,"last_page":3,"per_page":30,"to":30,"total":67},"meta":null,"summary":{"total_amount":"670.00","total_pending_amount":"380.00","total_approved_amount":"290.00","total_rejected_amount":0,"total_cancelled_amount":0},"table_total":{"total_amount":"300.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************.JJ8iUAwzIJXzMsQOh4u1RuU3SEpN_CyFv65rQwMYzEk","token_type":"bearer","timezone":"Asia/Kuala_Lumpur","environment":"local","execution_duration":"0.10259199142456 sec","log_id":"a5222a57-e8d6-4c4f-bff9-e47210e41c45"}
     * ##docs end##
     */
    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Platform THB FPAY Deposit List
     *
     * @module = admin
     *
     * @path = credit/get-thb-platform-fpay-deposit-list
     *
     * @method = POST
     *
     * @description = To get all user platform THB fpay deposit request.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = member_id|<member_id>|string|optional|Member ID|member_id filter.
     * @body = username|<username>|string|optional|Username|username filter.
     * @body = phone_no|<phone_no>|string|optional|***********|User Phone filter.
     * @body = status|<status>|string|optional|status|Create status filter.
     * @body = transaction_id|<transaction_id>|string|optional|9eDtjG38eVuNJwYx|transaction unique id filter
     * @body = reference_no|<reference_no>|string|optional|***********|User input reference no filter
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = updated_from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = updated_to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = country|<country>|integer|optional|100|Country filter. (Dropdown: country)
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = export_data|<export_data>|array|optional|{"data": {"created_at": "Date"}|Data need to export value => header. Required for export
     * @body = file_name|<file_name>|string|optional|withdrawal-list|name of the export file. Required for export
     *
     * @response = {"data":{"list":[{"id":738,"created_at":"03/04/2024 19:47:47","updated_at":"03/04/2024 19:47:47","transaction_id":"A0004","payment_type":"online-bank-tk8th","payment_type_display":"online-bank-tk8th","from_address":"-","receivable_amount":"10.00","currency":"THB","tx_id":"-","tx_id_hyperlink":"-","status":"approved","attachment":null,"reference_no":null,"user_remark":null,"admin_remark":null,"status_display":"Approved","username":"**********","member_id":"*********","phone_no":"**********","payment_id":0,"code":"A0004","credit_type":"thb-credit","credit_type_display":"thb-credit","amount":"10.00","rate":"1.00","converted_amount":"10.00","charges":"0.00","approved_at":"03/04/2024 19:47:47","updater":null}],"pagination":{"current_page":1,"from":1,"last_page":3,"per_page":30,"to":30,"total":62},"meta":null,"summary":{"total_amount":"485.00","total_pending_amount":"51.00","total_approved_amount":"434.00","total_rejected_amount":0,"total_cancelled_amount":0},"table_total":{"total_amount":"165.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************.ogAWqiAmdel_M0SUZ7asbwMzi4Agoobg8b-6nhVC_PE","token_type":"bearer","timezone":"Asia/Kuala_Lumpur","environment":"local","execution_duration":"0.*************** sec","log_id":"51645e85-0b30-43e5-b375-2c109ee5c68f"}
     * ##docs end##
     */
    public function getPlatformFPayDepositList(Request $request)
    {
        switch ($request->path()) {
            case 'credit/get-platform-fpay-deposit-list':
                $typeArrray = [
                    Deposit::$type['online-bank-tk8'],
                    Deposit::$type['online-bank-tk8th'],
                ];
                $request->merge(['type' => $typeArrray, 'credit_type' => 'myr-credit']);
                break;
            case 'credit/get-thb-platform-fpay-deposit-list':
                $typeArrray = [
                    Deposit::$type['online-bank-tk8'],
                    Deposit::$type['online-bank-tk8th'],
                ];
                $request->merge(['type' => $typeArrray, 'credit_type' => 'thb-credit']);
                break;
        }

        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => auth()->user()->id]);
        }
        $validator = Validator::make($request->all(), [
            'order_sort' => 'string|in:asc,desc',
            'credit_type' => 'required|string|exists:credit,type',
            'user_id' => 'int',
            'username' => 'string',
            'member_id' => 'string',
            'phone_no' => 'string',
            'transaction_id' => 'string',
            'reference_no' => 'string',
            'status' => 'string|in:'.implode(',', array_keys(Deposit::$status)),
            'type' => 'array|in:'.implode(',', array_values(Deposit::$type)),
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d',
            'updated_from_date' => 'required_with:updated_from_date|string|date_format:Y-m-d',
            'updated_to_date' => 'required_with:updated_to_date|string|date_format:Y-m-d',
            'country' => 'integer',
            'see_all' => 'integer|in:1,0',
            'limit' => 'integer',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string',
            'file_name' => 'required_if:export,==,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $getDeposit = Deposit::getList($validator->validated());
        $data['data'] = $getDeposit;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Ewallet Deposit List
     *
     * @module = admin
     *
     * @path = credit/get-ewallet-deposit-list
     *
     * @method = POST
     *
     * @description = To get all user ewallet deposit request.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = username|<username>|string|optional|Username|username filter.
     * @body = member_id|<member_id>|string|optional|member_id|Member ID filter.
     * @body = store_id|<store_id>|integer|optional|27|Store. (dropdown: store)
     * @body = phone_no|<phone_no>|string|optional|***********|User Phone filter.
     * @body = status|<status>|string|optional|status|Create status filter.
     * @body = type|<type>|string|optional|manual|Create type filter
     * @body = transaction_id|<transaction_id>|string|optional|9eDtjG38eVuNJwYx|transaction unique id filter
     * @body = reference_no|<reference_no>|string|optional|***********|User input reference no filter
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = updated_from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = updated_to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = country|<country>|integer|optional|100|Country filter. (Dropdown: country)
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = export_data|<export_data>|array|optional|{"data": {"created_at": "Date"}|Data need to export value => header. Required for export
     * @body = file_name|<file_name>|string|optional|withdrawal-list|name of the export file. Required for export
     *
     * @response = {"data":{"list":[{"id":467,"created_at":"25\/09\/2023 12:56:38","transaction_id":"KC75oPfYhafHTJGN","payment_type":"ewallet","payment_type_display":"ewallet","from_address":"-","receivable_amount":"10.00","currency":"MYR","tx_id":"-","status":"rejected","attachment":null,"reference_no":null,"user_remark":null,"admin_remark":null,"status_display":"Rejected","username":"60159357258","member_id":"791959146","phone_no":"60159357258","payment_id":0,"code":"KC75oPfYhafHTJGN","credit_type":"myr-credit","credit_type_display":"myr-credit","country":"malaysia","country_display":"Malaysia","amount":"10.00","rate":"1.00","converted_amount":"10.00","charges":"0.00","approved_at":"25\/09\/2023 12:57:02","updater":null}],"pagination":{"current_page":1,"from":1,"last_page":38,"per_page":1,"to":1,"total":38},"meta":null,"summary":{"total_amount":"9220.00","total_pending_amount":"8670.00","total_approved_amount":"510.00","total_rejected_amount":"40.00","total_cancelled_amount":0},"table_total":{"total_amount":"10.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2NyZWRpdC9nZXQtZXdhbGxldC1kZXBvc2l0LWxpc3QiLCJpYXQiOjE2OTU4MDM0OTMsImV4cCI6MTY5NTgwNzM3NSwibmJmIjoxNjk1ODAzNzc1LCJqdGkiOiJUb2F6aUljR1U0RGZUbERZIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.JVPW0C6gJiyl8qpsO50t6LONcgcuKaPBCGBT3mpOvkM","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.15858197212219 sec","log_id":"e3baa3cb-9155-4400-94ea-63b0065b5aa5"}
     * ##docs end##
     */
    public function getEwalletDepositList(Request $request)
    {
        $typeArrray = [
            Deposit::$type['ewallet'],
            Deposit::$type['onepay-ewallet'],
        ];

        $request->request->add([
            'type' => $typeArrray,
            'credit_type' => 'myr-credit',
        ]);
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => auth()->user()->id]);
        }

        if (in_array(MODULE, ['admin'])) {
            $adminId = auth()->user()->id;
            $adminRes = Models\Admin::with(
                [
                    'adminDetail' => function ($q) {
                        return $q->where('name', 'store');
                    },
                ],
            )
                ->find($adminId);

            $isMaster = $adminRes->is_master;
            if (! $isMaster) {
                $branchJE = $adminRes->adminDetail->first()->value ?? '[]';
                $branchDE = json_decode($branchJE);

                $request->merge(['extra_store_id' => $branchDE]);
            }
        }

        $validator = Validator::make($request->all(), [
            'order_sort' => 'string|in:asc,desc',
            'credit_type' => 'required|string|exists:credit,type',
            'user_id' => 'int',
            'username' => 'string',
            'member_id' => 'string',
            'store_id' => 'nullable|integer',
            'phone_no' => 'string',
            'transaction_id' => 'string',
            'reference_no' => 'string',
            'extra_store_id' => 'nullable',
            'status' => 'string|in:'.implode(',', array_keys(Deposit::$status)),
            'type' => 'array|in:'.implode(',', array_values(Deposit::$type)),
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d',
            'updated_from_date' => 'required_with:updated_from_date|string|date_format:Y-m-d',
            'updated_to_date' => 'required_with:updated_to_date|string|date_format:Y-m-d',
            'country' => 'integer',
            'see_all' => 'integer|in:1,0',
            'limit' => 'integer',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string',
            'file_name' => 'required_if:export,==,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $getDeposit = Deposit::getList($validator->validated());
        $data['data'] = $getDeposit;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = THB Ewallet Deposit List
     *
     * @module = admin
     *
     * @path = credit/get-thb-ewallet-deposit-list
     *
     * @method = POST
     *
     * @description = To get all user thb deposit request.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = member_id|<member_id>|string|optional|128390123|Create Member ID filter.
     * @body = transaction_id|<transaction_id>|string|optional|9eDtjG38eVuNJwYx|transaction unique id filter
     * @body = phone_no|<phone_no>|string|optional|***********|User Phone filter.
     * @body = status|<status>|string|optional|status|Create status filter.
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = export_data|<export_data>|array|optional|{"data": {"created_at": "Date"}|Data need to export value => header. Required for export
     * @body = file_name|<file_name>|string|optional|withdrawal-list|name of the export file. Required for export
     *
     * @response = {"data":{"list":[{"id":152,"created_at":"25\/07\/2023 14:43:22","transaction_id":"XcVF6Zz7o9MyPtBG","payment_type":"manual-bank","payment_type_display":"manual-bank","from_address":"-","receivable_amount":"3000.00","currency":"MYR","tx_id":"-","status":"approved","attachment":"production\/2023\/07\/1690267400_8232","reference_no":"00","user_remark":"top up","admin_remark":null,"status_display":"Approved","username":"60**********","member_id":"*********","phone_no":"60-**********","payment_id":0,"code":"XcVF6Zz7o9MyPtBG","credit_type":"myr-credit","credit_type_display":"myr-credit","country":"malaysia","country_display":"Malaysia","amount":"3000.00","rate":"1.00","converted_amount":"3000.00","charges":"0.00","approved_at":"25\/07\/2023 14:46:03","updater":"bovm"},{"id":150,"created_at":"25\/07\/2023 13:53:12","transaction_id":"Z4oT2W4Pvbm11uMs","payment_type":"manual-bank","payment_type_display":"manual-bank","from_address":"-","receivable_amount":"3000.00","currency":"MYR","tx_id":"-","status":"rejected","attachment":"production\/2023\/07\/1690264391_1980","reference_no":"00","user_remark":"top up","admin_remark":"duplicate slip , pls upload 1.23pm slip","status_display":"Rejected","username":"60**********","member_id":"*********","phone_no":"60-**********","payment_id":0,"code":"Z4oT2W4Pvbm11uMs","credit_type":"myr-credit","credit_type_display":"myr-credit","country":"malaysia","country_display":"Malaysia","amount":"3000.00","rate":"1.00","converted_amount":"3000.00","charges":"0.00","approved_at":"25\/07\/2023 14:36:47","updater":"bovm"},{"id":140,"created_at":"24\/07\/2023 19:14:24","transaction_id":"se1zre1t52fZ5XVh","payment_type":"manual-bank","payment_type_display":"manual-bank","from_address":"-","receivable_amount":"2000.00","currency":"MYR","tx_id":"-","status":"approved","attachment":"production\/2023\/07\/1690197262_0708","reference_no":"00","user_remark":"top up","admin_remark":null,"status_display":"Approved","username":"60**********","member_id":"*********","phone_no":"60-**********","payment_id":0,"code":"se1zre1t52fZ5XVh","credit_type":"myr-credit","credit_type_display":"myr-credit","country":"malaysia","country_display":"Malaysia","amount":"2000.00","rate":"1.00","converted_amount":"2000.00","charges":"0.00","approved_at":"24\/07\/2023 20:29:43","updater":"bovm"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":3,"total":3},"meta":null,"summary":{"total_amount":"8000.00","total_pending_amount":0,"total_approved_amount":"5000.00","total_rejected_amount":"3000.00","total_cancelled_amount":0},"table_total":{"total_amount":"8000.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************.trqVWmoLzpT3P3pyWBjenopESeLdBIUZ6iFF3LkIjCY","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"df2763c1-67c0-417c-be39-8950506bf989"}
     * ##docs end##
     */
    public function getThbEwalletDepositList(Request $request)
    {
        $request->request->add(['credit_type' => 'thb-credit']);
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => auth()->user()->id]);
        }
        $validator = Validator::make($request->all(), [
            'order_sort' => 'string|in:asc,desc',
            'credit_type' => 'required|string|exists:credit,type',
            'user_id' => 'int',
            'username' => 'string',
            'member_id' => 'string',
            'phone_no' => 'string',
            'transaction_id' => 'string',
            'reference_no' => 'string',
            'status' => 'string|in:'.implode(',', array_keys(Deposit::$status)),
            'type' => 'string|in:'.implode(',', array_keys(Deposit::$type)),
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d',
            'updated_from_date' => 'required_with:updated_from_date|string|date_format:Y-m-d',
            'updated_to_date' => 'required_with:updated_to_date|string|date_format:Y-m-d',
            'country' => 'integer',
            'see_all' => 'integer|in:1,0',
            'limit' => 'integer',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string',
            'file_name' => 'required_if:export,==,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $getDeposit = Deposit::getList($validator->validated());
        $data['data'] = $getDeposit;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = THB Manual Deposit List
     *
     * @module = admin
     *
     * @path = credit/get-thb-manual-deposit-list
     *
     * @method = POST
     *
     * @description = To get all user thb manual deposit request.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = member_id|<member_id>|string|optional|128390123|Create Member ID filter.
     * @body = transaction_id|<transaction_id>|string|optional|9eDtjG38eVuNJwYx|transaction unique id filter
     * @body = phone_no|<phone_no>|string|optional|***********|User Phone filter.
     * @body = status|<status>|string|optional|status|Create status filter.
     * @body = reference_no|<reference_no>|string|optional|***********|User input reference no filter
     * @body = updated_from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = updated_to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = export_data|<export_data>|array|optional|{"data": {"created_at": "Date"}|Data need to export value => header. Required for export
     * @body = file_name|<file_name>|string|optional|withdrawal-list|name of the export file. Required for export
     *
     * @response = {"data":{"list":[{"id":646,"created_at":"13\/03\/2024 17:02:27","updated_at":"13\/03\/2024 17:02:27","transaction_id":"GTDvPxZQzyf62DGv","payment_type":"thb-manual-bank","payment_type_display":"thb-manual-bank","from_address":"-","receivable_amount":"100.00","currency":"THB","tx_id":"-","tx_id_hyperlink":"-","status":"pending","attachment":"abv","reference_no":"haha","user_remark":"test remark","admin_remark":null,"status_display":"Pending","username":"************","member_id":"*********","phone_no":"************","payment_id":0,"code":"GTDvPxZQzyf62DGv","credit_type":"thb-credit","credit_type_display":"thb-credit","country":"malaysia","country_display":"Malaysia","amount":"100.00","rate":"1.00","converted_amount":"100.00","charges":"0.00","approved_at":false,"updater":null}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null,"summary":{"total_amount":"100.00","total_pending_amount":"100.00","total_approved_amount":0,"total_rejected_amount":0,"total_cancelled_amount":0},"table_total":{"total_amount":"100.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************.mkSfmLNDuVHJa9iuRG6FXBimnV271BCf7OhZfYRmyIc","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"5cfb88ad-b11d-494c-977c-aff6488f7ec7"}
     * ##docs end##
     */
    public function getThbManualDepositList(Request $request)
    {
        $request->request->add(['credit_type' => 'thb-credit', 'type' => 'thb-manual-bank']);
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => auth()->user()->id]);
        }
        $validator = Validator::make($request->all(), [
            'order_sort' => 'string|in:asc,desc',
            'credit_type' => 'required|string|exists:credit,type',
            'user_id' => 'int',
            'username' => 'string',
            'member_id' => 'string',
            'phone_no' => 'string',
            'transaction_id' => 'string',
            'reference_no' => 'string',
            'status' => 'string|in:'.implode(',', array_keys(Deposit::$status)),
            'type' => 'string|in:'.implode(',', array_keys(Deposit::$type)),
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d',
            'updated_from_date' => 'required_with:updated_from_date|string|date_format:Y-m-d',
            'updated_to_date' => 'required_with:updated_to_date|string|date_format:Y-m-d',
            'country' => 'integer',
            'see_all' => 'integer|in:1,0',
            'limit' => 'integer',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string',
            'file_name' => 'required_if:export,==,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $getDeposit = Deposit::getList($validator->validated());
        $data['data'] = $getDeposit;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = USDT Deposit List
     *
     * @module = admin
     *
     * @path = credit/get-usdt-deposit-list
     *
     * @method = POST
     *
     * @description = To get all user ewallet deposit request.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = tx_id|<tx_id>|string|optional|tx_id|Tx ID filter.
     * @body = member_id|<member_id>|string|optional|member_id|Member ID filter.
     * @body = phone_no|<phone_no>|string|optional|***********|User Phone filter.
     * @body = status|<status>|string|optional|status|Create status filter.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = export_data|<export_data>|array|optional|{"data": {"created_at": "Date"}|Data need to export value => header. Required for export
     * @body = file_name|<file_name>|string|optional|withdrawal-list|name of the export file. Required for export
     *
     * @response = {"data":{"list":[{"id":597,"created_at":"07\/02\/2024 14:33:07","updated_at":"07\/02\/2024 14:33:07","transaction_id":"gL2pt2SUXD3KVHkR","payment_type":"crypto","payment_type_display":"Crypto","from_address":"TVF2Mp9QY7FEGTnr3DBpFLobA6jguHyMvi","receivable_amount":"50000.00","currency":"USDT","tx_id":"9ef553fee4e250a09291ef6491d755109ec4efdae592ecea8516d2f31938131c","tx_id_hyperlink":"https:\/\/nile.tronscan.org\/#\/transaction\/9ef553fee4e250a09291ef6491d755109ec4efdae592ecea8516d2f31938131c","status":"approved","attachment":null,"reference_no":null,"user_remark":null,"admin_remark":null,"status_display":"Approved","username":"60189742481","member_id":"170526055","phone_no":"60189742481","payment_id":3,"code":"gL2pt2SUXD3KVHkR","credit_type":"usdt-credit","credit_type_display":"usdt-credit","country":"malaysia","country_display":"Malaysia","amount":"50000.00","rate":"1.00","converted_amount":"50000.00","charges":"0.00","approved_at":"07\/02\/2024 14:33:07","updater":null}],"pagination":{"current_page":1,"from":1,"last_page":3,"per_page":1,"to":1,"total":3},"meta":null,"summary":{"total_amount":"51010.00","total_pending_amount":0,"total_approved_amount":"51010.00","total_rejected_amount":0,"total_cancelled_amount":0},"table_total":{"total_amount":"50000.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2NyZWRpdC9nZXQtdXNkdC1kZXBvc2l0LWxpc3QiLCJpYXQiOjE3MDc5NzY2NjEsImV4cCI6MTcwNzk4MDI4NSwibmJmIjoxNzA3OTc2Njg1LCJqdGkiOiI4bXZjV3BvMTE1N0t6eWkyIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.cPDA-KR2oeil8-WEOZ4UkzB3o-i5ydVPNZ12mhn3d1M","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.12498593330383 sec","log_id":"f08c7667-2d2d-4627-8cbe-b7fe9276ca2e"}
     * ##docs end##
     */
    public function getUsdtEwalletDepositList(Request $request)
    {
        $request->request->add(['credit_type' => 'usdt-credit']);
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => auth()->user()->id]);
        }
        $validator = Validator::make($request->all(), [
            'order_sort' => 'string|in:asc,desc',
            'credit_type' => 'required|string|exists:credit,type',
            'tx_id' => 'nullable|string',
            'user_id' => 'int',
            'username' => 'string',
            'member_id' => 'string',
            'phone_no' => 'string',
            'transaction_id' => 'string',
            'reference_no' => 'string',
            'status' => 'string|in:'.implode(',', array_keys(Deposit::$status)),
            'type' => 'string|in:'.implode(',', array_keys(Deposit::$type)),
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d',
            'updated_from_date' => 'required_with:updated_from_date|string|date_format:Y-m-d',
            'updated_to_date' => 'required_with:updated_to_date|string|date_format:Y-m-d',
            'country' => 'integer',
            'see_all' => 'integer|in:1,0',
            'limit' => 'integer',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string',
            'file_name' => 'required_if:export,==,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $getDeposit = Deposit::getList($validator->validated());
        $data['data'] = $getDeposit;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = credit/get-deposit-data
     *
     * @module = admin
     *
     * @path = credit/get-deposit-data
     *
     * @method = post
     *
     * @description = To get deposit data.
     *
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
     *
     * @body = user_id|<user_id>|integer|required|1000000|User's id.
     * @body = credit_id|<credit_id>|integer|required|1000|Credit's id.

     *
     * @response = {"data":{"fiat":{"rate":"27156.********","iso":"VND"},"crypto":{"rate":"1.********","iso":"USDT"},"payment_detail":[{"type":"crypto","detail":"TMVc7pyDmYUG7GSzCXPtQCUfcG3i8S4PuY","currency":"USDT"},{"type":"bank","detail":{"bank_name":"Asia Commercial Bank","bank_account_name":"Bien Dong E-Trade Co","bank_account_number":"********"},"currency":"VND"}]},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************.C7k3hhQaeKebO-CfcdZPDBYXCmAW4oQNyyLzXK9ScsE","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.*************** sec","log_id":"76cdeccf-82c2-435f-bb33-4c3fc602af42"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = credit/get-deposit-data
     *
     * @module = user,app
     *
     * @path = credit/get-deposit-data
     *
     * @method = post
     *
     * @description = To get deposit data.
     *
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
     *
     * @body = credit_id|<credit_id>|integer|required|1000|Credit's id.

     *
     * @response = {"data":{"option":{"online-bank":{"currency_list":[{"currency":"MYR","min":"10.000000","max":"10000.000000"}],"option_display":"Online Banking"},"manual-bank":{"bank_name":"fw Sdn Bhd","bank_account_name":"Maybank Berhad","bank_account_number":"1234 7890 4567","option_display":"Cash Deposit"},"ewallet":{"currency_list":[{"currency":"MYR","min":"10.000000","max":"10000.000000"}],"option_display":"E-Wallet"}},"min-fundin":"1.00","fundin-charge":"0.00","balance":"9999979.********"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************.a34jY60pW0rgVEQnFZ-DheEmCz8LMnXpQJIcsQxTl9o","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"staging","execution_duration":"0.************** sec","log_id":"1f4e17b0-c40f-4a97-8ab7-e638a766af8d"}
     * ##docs end##
     */
    public function getDepositData(Request $request)
    {
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => auth()->user()->id]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'int|required|exists:users,id',
            'credit_id' => [
                'int',
                Rule::requiredIf(function () {
                    $creditCount = CreditSetting::where(['name' => ['is-fundinable'], 'value' => 1])->count();

                    return $creditCount > 1;
                }),
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        try {
            $res = Deposit::getDepositData($validator->validated());
        } catch (\Exception $e) {
            abort($e->getCode(), json_encode($e->getMessage()));
        }

        abort(200, json_encode($res));
    }

    public function addDeposit(Request $request)
    {
        $userId = Auth::user()->id ?? null;
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => $userId]);
        }
        if (! isset($request->credit_id)) {
            $creditId = Credit::where('name', 'myr-credit')->first()->id ?? null;
            $request->request->add(['credit_id' => $creditId]);
        }

        $referenceNoRequired = 0;
        $specialCreditTypeChecking = 0;

        // switch ($request->path()) {
        //     case 'credit/help-centre-deposit':
        //         $request->request->add(['deposit_type' => 'thb-manual-bank']);
        //         $referenceNoRequired = 1;
        //         $specialCreditTypeChecking = 1;
        //         break;
        //
        //     default:
        //         $request->request->add(['deposit_type' => 'manual-bank']);
        //         break;
        // }
        // $request->request->add(['type' => Deposit::$type['ewallet']]);
        $depositAmount = $request->amount ?? 0;

        $validator = Validator::make($request->all(), [
            'payment_method_id' => 'required|exists:payment_methods,id',
            'promotion_id' => [
                'nullable',
                'exists:promotions,id',
                function ($q, $value, $fail) use ($userId, $depositAmount) {
                    if (! is_null($value)) {
                        $promotion = Promotion::find($value);
                        // $todayUserPromotionCount = UserPromotion::getTodayUserPromotionCountByUserIdAndPromotionId($userId, $value);
                        $userPromotionCount = UserPromotion::getTodayUserPromotionCountByUserIdAndPromotionId($userId, $value);
                        $isOneTime = $promotion->is_one_time;
                        $minDeposit = $promotion->min_deposit;
                        $maxDeposit = $promotion->max_deposit;
                        $userPromotion = UserPromotion::getUserPromotionByUserId($userId);

                        if ($userPromotion) {
                            return $fail('Oops! You already have an active promotion. Please complete your current promotion before selecting a new one.');
                        }

                        if ($isOneTime && $userPromotionCount > 0) {
                            return $fail('This promotion can only be claimed once');
                        }

                        // if ($todayUserPromotionCount > 0) {
                        //     return $fail('Promotion is claimed today');
                        // }

                        if ($depositAmount < $minDeposit) {
                            return $fail('Minimum deposit amount is RM'.strval($minDeposit));
                        }

                        if ($depositAmount > $maxDeposit) {
                            return $fail('Maximum deposit amount is RM'.strval($maxDeposit));
                        }

                        if (isset($promotion->daily_claim_limit) && $userPromotionCount >= $promotion->daily_claim_limit) {
                            $fail('Only can claim '.$promotion->daily_claim_limit.' time(s) per day.');
                        }

                        if (isset($promotion->start_time) && isset($promotion->end_time)) {
                            $currentTime = time();
                            if ($currentTime < strtotime($promotion->start_time) || $currentTime > strtotime($promotion->end_time)) {
                                $fail('User promotion not yet started');
                            }
                        }
                    }
                },
            ],
            'credit_id' => [
                'required',
                'exists:credit,id',
                function ($q, $value, $fail) use (&$creditSetting) {
                    $creditSetting = CreditSetting::where(['credit_id' => $value])->whereIn('name', ['is-fundinable', 'fundin-type', 'min-fundin'])->get();
                    // $creditSetting->map(function ($q) use (&$fail, $request) {
                    //     switch ($q->name) {
                    //         case 'is-fundinable':
                    //             if ($q->value != 1 || $q->member != 1) {
                    //                 return $fail(Lang::get('lang.invalid-credit'));
                    //             }
                    //             break;
                    //
                    //         case 'fundin-type':
                    //             $validDepositType = [];
                    //             if (isset($q->reference)) {
                    //                 $validDepositType = explode(',', $q->reference);
                    //             }
                    //
                    //             if (!isset($request->type) || !in_array($request->type, $validDepositType)) {
                    //                 return $fail(Lang::get('lang.invalid-credit'));
                    //             }
                    //             break;
                    //     }
                    // });
                },
            ],
            // "credit_id" => [
            //     "required",
            //     "exists:credit,id",
            //     function ($q, $value, $fail) use ($specialCreditTypeChecking) {
            //         if ($specialCreditTypeChecking) {
            //             $checkCol = MODULE;
            //             if (in_array(MODULE, ['app', 'user'])) $checkCol = 'member';
            //             $creditID = CreditSetting::where(['name' => 'special-fundin-type', 'value' => 1, $checkCol => 1])->get()->toArray() ?? [];
            //             if (!in_array($value, array_column($creditID, 'credit_id'))) return $fail(Lang::get('validation.in', ["attribute" => str_replace('_', ' ', $q)]));
            //         }
            //     }
            // ],
            'user_id' => 'required|integer',
            // "card_id" => "nullable|integer|exists:user_card,id",
            'card_id' => [
                'nullable',
                'integer',
                'exists:user_card,id',
                function ($q, $value, $fail) {
                    $cardRes = UserCard::with([
                        'store' => function ($q) {
                            $q->select('store_id', 'name');
                        },
                    ])->find($value);

                    if ($cardRes->status == UserCard::$status['active']) {
                        $type = 'check_game_credit';
                        $curlParams = [
                            'storeId' => $cardRes->store_id,
                            'cardId' => $cardRes->card_id,
                        ];
                        $result = Traits\SonicTrait::post($curlParams, $type);
                        if (! isset($result['status']) || $result['status'] == false) {
                            return $fail('Store is offline');
                        }
                    }
                },
            ],
            'bank_id' => 'nullable|integer',
            'amount' => [
                'required',
                'string',
                function ($q, $value, $fail) use ($request, &$creditSetting) {
                    if (isset($creditSetting)) {
                        $creditSetting->map(function ($q) use (&$fail, $request) {
                            switch ($q->name) {
                                case 'min-fundin':
                                    if ($request->amount < $q->value) {
                                        return $fail(Lang::get('lang.amount-gte-numeric', ['value' => $q->value]));
                                    }
                                    break;
                            }
                        });
                    }
                },
            ],
            'attachment' => 'nullable|string',
            // "reference_no" => [
            //     Rule::requiredIf($referenceNoRequired),
            //     "nullable",
            //     "string",
            //     "max:50"
            // ],
            'remark' => 'nullable|string|max:255',
            // "deposit_type" => "required|string|in:manual-bank,thb-manual-bank,ewallet",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Deposit::createDeposit($validator->validated());

        abort(200, json_encode(['data' => $res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = credit/update-deposit
     *
     * @module = admin
     *
     * @path = credit/update-deposit
     *
     * @method = POST
     *
     * @description = To update user deposit.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|int|required|1|deposit id.
     * @body = status|<status>|string|required|rejected|User Bank In Status.(get from dropdown: deposit)
     * @body = remark|<remark>|string|optional|testing|admin remark (required if status is rejected).
     *
     * @response = {"data":{"list":[{"id":2,"created_at":"2022-09-14T05:41:48.000000Z","nickname":"director","register_name":"sam","nric":"001009-23-9043","type":"passport","status":0,"remarks":null,"updater":null,"approved_at":null},{"id":1,"created_at":"2022-09-14T05:41:10.000000Z","nickname":"director","register_name":"sam","nric":"001009-23-9043","type":"passport","status":"Rejected","remarks":null,"updater":null,"approved_at":null}],"pagination":{"current_page":1,"first_page_url":"http:\/\/local-api-admin.martin.com\/kyc\/get-user-kyc?page=1","from":1,"last_page":1,"last_page_url":"http:\/\/local-api-admin.martin.com\/kyc\/get-user-kyc?page=1","next_page_url":null,"path":"http:\/\/local-api-admin.martin.com\/kyc\/get-user-kyc","per_page":15,"prev_page_url":null,"to":2,"total":2},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20va3ljL2dldC11c2VyLWt5YyIsImlhdCI6MTY2MzE0MjM5MiwiZXhwIjoxNjYzMTQ2MDk3LCJuYmYiOjE2NjMxNDI0OTcsImp0aSI6ImVIZVkwQU5FWTZTMUFKR3giLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.Qq7Oa4ZiFpS0WOOfNjfg5c1kGJSXrQ0duawpBBie6vM","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.011851072311401 sec","log_id":"55ab9ff7-3402-4a0d-b0fa-67303fefd3ad"}
     * ##docs end##
     */
    public function updateDeposit(Request $request)
    {
        if (MODULE != 'admin') {
            abort(400, json_encode(['Invalid Action!!!']));
        }
        $validator = Validator::make($request->all(), [
            'id' => [
                'required',
                'int',
                Rule::exists('deposit', 'id')->where('status', Deposit::$status['pending']),
            ],
            'status' => 'required|string|in:'.implode(',', array_keys(Arr::only(Deposit::$status, ['approved', 'rejected']))),
            'remark' => 'required_if:status,==,rejected',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        try {
            Deposit::updateDeposit($validator->validated());
        } catch (\Exception $e) {
            abort($e->getCode(), json_encode($e->getMessage()));
        }

        abort(200, Lang::get('lang.admin-update-success'));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Deposit Detail
     *
     * @module = admin
     *
     * @path = credit/get-deposit-det
     *
     * @method = POST
     *
     * @description = To get user deposit detail.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|int|required|1|Deposit table id.
     *
     * @response = {"data":{"id":3,"name":"fwUser0012","username":"60*********","member_id":"*********","phone_no":"60-*********","payment_type":"manual-bank","payment_type_display":"manual-bank","payment_id":0,"payment_number":null,"code":"GOle6uZAv3bZKQEs","type":"1","type_display":"1","credit_type":"myr-credit","credit_type_display":"myr-credit","currency":"MYR","status":"pending","status_display":"Pending","amount":"100.00","rate":"1.00","converted_amount":"100.00","charges":"0.00","receivable_amount":"100.00","approved_at":null,"created_at":"08\/05\/2023 09:21:46","updater":null,"attachment":"https:\/\/fw-pvt.s3.ap-southeast-1.amazonaws.com\/the%20url?response-content-disposition=inline%3B%20filename%3D%22the%20url%22&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=FwoGZXIvYXdzEBUaDMUv1aQM57zSA8xp0yKxAdJscf8wIz1HiuiAZHARW9tiDTM2wN5jm4bSYyMk3gdK6OhUG6v9R0xJUZIVvuvNlCUVgKhN%2BvA5AbEzlzhst1P6kTYAVcOT8FuigyIXQsxt%2B245V6nMB6nODsNRAJwXZytXDfgap%2FhuhZKpGIe71hdlPJGeubSOliT3AF3xuQ%2FBWCCx%2Bv4FtWZ2z8xBKFA7JZtqCM5RZtZrS7VBoT84kTqes%2F01mAqLXibZuQNpPVrJ7ijXyO6kBjItWJGb9qmfiYJI18UcsLaz28sR%2BGY6DtJpylr8Vvq6Y5Et3NBUJAK72Ie1lKf7&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIA43LUKFW2W4YGQZ5F%2F20230628%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20230628T030921Z&X-Amz-SignedHeaders=host&X-Amz-Expires=900&X-Amz-Signature=2d07223b5b7dc75e3e6227a39e30e16e66599880c8b4a62fa0bb57d65bbcd533","reference_no":"ytascdgasda","user_remark":"optional remark","admin_remark":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2NyZWRpdC9nZXQtZGVwb3NpdC1kZXQiLCJpYXQiOjE2ODc5MjEwODAsImV4cCI6MTY4NzkyNTM2MSwibmJmIjoxNjg3OTIxNzYxLCJqdGkiOiJqaDVrSjBhWGRBRUh4TGZLIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.DkTGDOwBaSMTG8Xme0ny6540MmLv4j4xQjx-umojbrw","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.07548713684082 sec","log_id":"cb86f6de-d88d-42bc-b166-98f32bc08ca2"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Deposit Detail
     *
     * @module = user,app
     *
     * @path = credit/get-deposit-det
     *
     * @method = POST
     *
     * @description = To get deposit detail.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|int|required|1|Deposit table id.
     *
     * @response = {"data":{"id":1,"username":"ghostmate","payment_type":"manual-bank","payment_type_display":"manual-bank","payment_id":0,"payment_number":null,"code":"2D0Omldexw6EazEz","type":"1","type_display":"1","credit_type":"myr-credit","credit_type_display":"myr-credit","currency":"MYR","status":"approved","status_display":"Approved","amount":"100.00","rate":"1.00","converted_amount":"100.00","charges":"0.00","receivable_amount":"100.00","approved_at":"26\/04\/2023 15:41:15","created_at":"26\/04\/2023 15:40:18","updater":"maskman","attachment":"asd.jpg","reference_no":"***********","user_remark":"remark test","admin_remark":"test good remark"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************.NpwakExeA7Wt72MxbPrJHzxmC2G2O3IQ6OqRrXXUXbU","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.11445116996765 sec","log_id":"d700562a-0f7b-4f03-bb67-26bcb5cc8a58"}
     * ##docs end##
     */
    public function getDepositDet(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => [
                'required',
                'int',
                Rule::exists('deposit', 'id')->where(function ($q) {
                    if (MODULE == 'user' || MODULE == 'app') {
                        return $q->where('user_id', auth()->user()->id);
                    }
                }),
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $getDepositDet = Deposit::getDepositDet($validator->validated());
        $data['data'] = $getDepositDet;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Balance List
     *
     * @module = admin
     *
     * @path = credit/balance-list
     *
     * @method = post
     *
     * @description = To get balance list.

     *
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     *
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = credit_id|<credit_id>|integer|required|1001|Credit's id.
     * @body = member_id|<member_id>|string|optional|12345|member_id filter.
     * @body = username|<username>|string|optional|sam|username filter.
     * @body = phone_no|<phone_no>|string|optional|60-123456789|Member Phone filter.
     * @body = store_id|<store_id>|integer|optional|27|Store. (dropdown: store)
     * @body = updated_from_date|<updated_from_date>|string|optional|2022-11-01|Updated Date filter.
     * @body = updated_to_date|<updated_to_date>|string|optional|2022-11-01|Updated Date filter.
     * @body = see_all|<see_all>|integer|optional|1|Use for export purpose.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = export_data|<export_data>|array|option|{"data": {"last_update": "Last Update","username": "Username","member_id": "Member ID","balance": "Balance"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|balance-report|name of the export file. Required for export


     * @response = {"data":{"list":[{"user_id":1000000,"member_id":"256921312","username":"tkNotes","name":"tktrst","balance":"0.01","last_update":"15\/02\/2024 05:58:23"}],"pagination":{"current_page":1,"from":1,"last_page":302,"per_page":1,"to":1,"total":302},"meta":null,"grand_total":"0.01","summary":{"balance":"584034.08"},"credit_code":"Exp"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2NyZWRpdC9iYWxhbmNlLWxpc3QiLCJpYXQiOjE3MTA4MzgzNzAsImV4cCI6MTcxMDg0MjE3NSwibmJmIjoxNzEwODM4NTc1LCJqdGkiOiJ0bFh5blhhQzVaTE9HZ2JoIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.8rnObhcYr55lW5MYO8U7lFfq2kBRzfc221Bf0_qAchE","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.061456918716431 sec","log_id":"946e1803-270f-4464-9fa1-522dde347650"}
     * ##docs end##
     */
    public function balanceList(Request $request)
    {
        if (MODULE == 'user') {
            abort(400, json_encode('Invalid Access.'));
        }

        if (in_array(MODULE, ['admin'])) {
            $adminId = auth()->user()->id;
            $adminRes = Models\Admin::with(
                [
                    'adminDetail' => function ($q) {
                        return $q->where('name', 'store');
                    },
                ],
            )
                ->find($adminId);

            $isMaster = $adminRes->is_master;
            if (! $isMaster) {
                $branchJE = $adminRes->adminDetail->first()->value ?? '[]';
                $branchDE = json_decode($branchJE);

                $request->merge(['extra_store_id' => $branchDE]);
            }
        }

        $validator = Validator::make($request->all(), [
            'credit_id' => 'required|int|exists:credit,id',
            'store_id' => 'nullable|integer',
            'phone_no' => 'nullable|string',
            'see_all' => 'integer',
            'limit' => 'integer',
            'page' => 'integer',
            'member_id' => 'string',
            'username' => 'string',
            'extra_store_id' => 'nullable',
            'updated_from_date' => 'required_with:updated_to_date|string|date_format:Y-m-d',
            'updated_to_date' => 'required_with:updated_from_date|string|date_format:Y-m-d|after_or_equal:updated_from_date',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string',
            'file_name' => 'required_if:export,==,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = User::walletBalanceList($validator->validated());

        abort(200, json_encode(['data' => $res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Add Withdrawal Channel
     *
     * @module = user,app
     *
     * @path = credit/add-withdrawal-channel
     *
     * @method = POST
     *
     * @description = To add withdrawal channel.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = step|<1 / 2>|integer|required|1| Step 1 = Get Bank Data, Step 2 = Confirmation
     * @body = bank_id|<bank_id>|integer|required|1|Bank's id.  (dropdown: bank)
     * @body = bank_account_holder|<bank_account_holder>|string|required|Name|Account Holder for bank
     * @body = bank_account_number|<bank_account_number>|string|required|Name|Account Number for bank
     * @body = is_primary|<0 / 1>|integer|optional|1| Choose to set primary.

     *
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************.D0Whu1lNabZyBt8edza4mG9fpOK2o5pLwPclz9TOqi0","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"cd3364e7-9dc7-4c1e-ad5f-f8b26687a594"}
     * ##docs end##
     */
    public function addWithdrawalChannel(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $request->request->add(['user_id' => auth()->user()->id]);
        }

        $userDetail = null;
        $withdrawalChannel = [];
        $validator = Validator::make($request->all(), [
            'user_id' => [
                'required',
                'integer',
                function ($q, $value, $fail) use (&$userDetail, &$withdrawalChannel) {
                    $userDetail = User::with(['userDetail'])->where('id', $value)->whereIN('user_type', [User::$userType['user-account']])->first();
                    if (empty($userDetail)) {
                        $fail(Lang::get('validation.exists'));

                        return;
                    }

                    // Get System Setting
                    $sysSetting = SystemSetting::selectRaw('name, type, value, reference')->where('type', 'withdrawal-channel')->get()->keyBy('name')->toArray();

                    // Get User withdrawal Channel
                    $withdrawalChannel = UserBank::selectRaw('bank_id')->where('user_id', $value)->where('status', UserBank::$status['active'])->get()->pluck('bank_id')->toArray() ?? [];
                    if ((count($withdrawalChannel) + 1) > ($sysSetting['channel-limit']['value'] ?? 0)) {
                        $fail('Withdrawal Channel is exceeded limit. Maximum - '.($sysSetting['channel-limit']['value'] ?? 0));

                        return;
                    }
                },
            ],
            'step' => 'required|integer|in:1,2',
            'bank_id' => [
                'required_if:step,==,2',
                'integer',
                function ($q, $value, $fail) use (&$userDetail, &$withdrawalChannel) {
                    $checkBank = Bank::where('id', $value)->where('country_id', ($userDetail->country_id ?? null))->where('status', Bank::$status['active'])->first();
                    if (empty($checkBank)) {
                        $fail(Lang::get('validation.exists'));

                        return;
                    }

                    if (in_array($value, $withdrawalChannel)) {
                        $fail(Lang::get('validation.unique'));

                        return;
                    }
                },
            ],
            'bank_account_holder' => 'required_if:step,==,2|string|nullable',
            'bank_account_number' => [
                'required_if:step,==,2',
                'string',
                'regex:/^\d+$/',
                'nullable',
            ],
            'is_primary' => 'nullable|integer|in:0,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if (auth()->user()->restricted == 1) {
            abort(400, 'User Account Restricted.');
        }

        if ($request->step == 2) {
            UserBank::add($validator->validated());
        }

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Withdrawal Channel List
     *
     * @module = user,app
     *
     * @path = credit/withdrawal-channel-list
     *
     * @method = POST
     *
     * @description = To get withdrawal channel list.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.

     *
     * @response = {"data":{"verified_email":0,"payment_type":{"bank":{"bank_list":[{"id":2,"bank_id":5,"bank_name":"Bank Simpanan Nasional (BSN)","account_name":"Bank 2","account_no":"**********","branch":null,"swift_code":null,"status_display":"Active","is_primary":1},{"id":1,"bank_id":7,"bank_name":"Citibank","account_name":"Bank 1","account_no":"**********","branch":null,"swift_code":null,"status_display":"Active","is_primary":0}],"bank_list_limit":"4","bank_list_available":2}}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************.xmn_hwmJjL7eYDzKSdNMkFPsnZe8Ju1gx7qWugUCCrw","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"c1474e7c-db9f-4d2a-b75d-505ee061ac67"}
     * ##docs end##
     */
    public function getWithdrawalChannelList(Request $request)
    {

        if (in_array(MODULE, ['user', 'app'])) {
            $request->request->add(['userID' => Auth::user()->id]);
        }

        $validator = Validator::make($request->all(), [
            'userID' => [
                'integer',
                Rule::exists('users', 'id')->whereIN('user_type', [User::$userType['user-account']]),
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if (auth()->user()->restricted == 1) {
            abort(400, 'User Account Restricted.');
        }

        $res = Credit::getWithdrawalChannelList($validator->validated());

        abort(200, json_encode(['data' => $res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Update Withdrawal Channel
     *
     * @module = user,app
     *
     * @path = credit/update-withdrawal-channel
     *
     * @method = POST
     *
     * @description = To update withdrawal channel status/primary.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id.*|<id.*>|array|required|[1,2]|Withdrawal channel's id array.
     * @body = id|<id>|integer|required|1|Withdrawal channel's id.
     * @body = status|<status>|string|optional|inactive|update status, in:'inactive'.
     * @body = is_primary|<is_primary>|integer|optional|0|update is primary, in:'0,1'.

     *
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS9jcmVkaXQvdXBkYXRlLXdpdGhkcmF3YWwtY2hhbm5lbCIsImlhdCI6MTY4MzUyNTcwOSwiZXhwIjoxNjgzNTMxOTIzLCJuYmYiOjE2ODM1MjgzMjMsImp0aSI6Ilp0T1V0cEJ3TXZSTEV0c1ciLCJzdWIiOiIxMDAwMDAxIiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.Y5mBNg15nwfS-CjEpf5wjU1knkPgKcRZXeepSl3RD7E","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************* sec","log_id":"53cc1c8e-1371-4e17-b98d-d7a108e5a28a"}
     * ##docs end##
     */
    public function updateWithdrawalChannel(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'id' => 'required|array',
            'id.*' => [
                'required',
                'integer',
                function ($q, $value, $fail) {
                    $checkBank = UserBank::where('id', $value)->when(in_array(MODULE, ['user', 'app']), function ($q) {
                        return $q->where('user_id', auth()->user()->id);
                    })->where('status', UserBank::$status['active'])->whereNULL('deleted_at')->first();

                    if (empty($checkBank)) {
                        $fail(__('validation.exists'));

                        return;
                    }
                },
            ],
            'is_primary' => [
                Rule::when((in_array(MODULE, ['user', 'app'])), function ($q) {
                    return $q = 'required_without:status';
                }),
                function ($q, $value, $fail) use ($request) {
                    if (count($request->id) > 1 && ($value >= 1)) {
                        $fail('Cannot set 2 channel as primary');

                        return;
                    }
                },
                'integer',
                'in:0,1',
            ],
            'status' => [
                Rule::when(in_array(MODULE, ['user', 'app']), function ($q) {
                    return $q = 'required_without:is_primary';
                }, ['required']),
                'string',
                'in:inactive',
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        UserBank::updateWithdrawalChannel($validator->validated());

        abort(200);
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Add Withdrawal
     * @module = user,app
     * @path = credit/add-withdrawal
     * @method = post
     * @description = To withdraw credit.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = step|<step>|integer|required|1|Step 1 = Get Wallet Data & Channel List, Step 2 = Verification, Step 3 = Confirmation.
     * @body = credit_id|<credit_id>|int|required|1000|Credit Unique Id for credit want to withdrawal (Step 1 Required).
     * @body = amount|<amount>|int|required|100|Credit Amount want to withdraw (Step 2 Required).
     * @body = channel_id|<channel_id>|integer|required|1|Withdrawal channel's id. (Step 2 Required).
     * @body = pin|<pin>|int|required|123456|transction PIN for user perform transfer. (Step 3 Required)
     *
     * @response = {"data":{"transaction_id":"WB454314","bank_name":"Bank Simpanan Nasional (BSN)","account_holder_name":"**********","account_holder_number":"Bank 2","amount":"100","charges":"1.00","receivable_amount":99,"date_time":"08\/05\/2023 16:23:49"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************.7cHwsBk5Yx2lch2c80j3u-XhIRZDycZjPwnJII9zmAo","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"6a7e72d8-c32f-482a-95a3-34fc293481a1"}
     * ##docs end##
     */

    public function withdrawConfirmation(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $user = Auth::user();
            $user_id = $user?->id ?? null;
            $request->request->add(['uid' => $user_id]);
            $validateCol = 'member';

            if (! isset($request->credit_id)) {
                $creditId = Credit::where('name', 'myr-credit')->first()->id ?? null;
                $request->request->add(['credit_id' => $creditId]);
            }

            $userPromotion = UserPromotion::getUserPromotionByUserId($user_id);
            if ($userPromotion && $request->step != 1) {
                abort(400, json_encode([
                    'msg' => [
                        'Turnover amount not fulfilled. You need to complete the turnover requirement before withdrawal.',
                    ],
                ]));
            }
        } else {
            $validateCol = 'admin';
            abort(400, json_encode('Invalid Access'));
        }
        $userDetail = null;
        $walletData = null;
        $summaryData = null;
        $currencyData = null;

        $validator = Validator::make($request->all() + ['module' => MODULE], [
            'step' => 'required|integer|in:1,2,3',
            'uid' => [
                'required',
                'integer',
                function ($q, $value, $fail) use (&$userDetail) {
                    $checkUser = User::with(['userDetail'])->where('id', $value)->whereIN('user_type', [User::$userType['user-account']])->get()->map(function ($q) use (&$userDetail) {
                        foreach ($q->userDetail->keyBy('name') as $name => $value) {
                            $userDetail[$name] = $value->value;
                        }

                        return $q;
                    })->first();
                    if (empty($checkUser)) {
                        $fail(Lang::get('validation.exists'));

                        return;
                    }

                    /* $pendingWithdrawal = Withdrawal::where(['user_id' => $value])->whereIn('status', [Withdrawal::$status['pending'], Withdrawal::$status['waiting-approval']])->whereNull('deleted_at')->first();
                        if (isset($pendingWithdrawal)) {
                            $fail(Lang::get("lang.withdrawal-pending-withdrawal-exist"));
                            return;
                        } */
                },
            ],
            'credit_id' => [
                'required',
                'integer',
                'exists:credit,id',
                function ($q, $value, $fail) use ($request, $validateCol, &$walletData, &$currencyData) {
                    $creditFunction = CreditSetting::with([
                        'credit' => function ($q) {
                            $q->with([
                                'currency' => function ($q) {
                                    return $q->select('id', 'iso', 'symbol');
                                },
                            ]);
                        },
                    ])
                        ->where([
                            'credit_id' => $value ?? 0,
                            'name' => 'is-withdrawable',
                            'value' => '1',
                            $validateCol => '1',
                        ])->first();

                    if (empty($creditFunction)) {
                        $fail(Lang::get('lang.function-unavailable'));

                        return;
                    }

                    $walletAmt = Credit::getBalance($request->uid, $creditFunction->credit->type);

                    $currencyData = CurrencySetting::with(['toCurrency', 'toCurrency.country' => function ($q) {
                        return $q->whereIn('name', Config('general.valid_exchange_country'));
                    }, 'fromCurrency'])
                        ->where('type', CurrencySetting::$type['fiat'])
                        ->where('disabled', 0)
                        ->whereRelation('toCurrency', 'iso', 'MYR')
                        ->whereRelation('fromCurrency', 'iso', 'MYR')
                        ->get()
                        ->map(function ($q) {
                            return [
                                'id' => $q->to_currency_id,
                                'from_currency_id' => $q->fromCurrency->id,
                                'from_currency_iso' => $q->fromCurrency->iso,
                                'currency_iso' => $q->toCurrency->iso,
                                'country_iso_code' => $q->toCurrency->country->iso_code2,
                                'rate' => $q->withdrawal_rate,
                                'processing_fee' => $q->withdrawal_fee,
                                'min_order' => $q->withdrawal_min_amount,
                                'max_order' => $q->withdrawal_max_amount,
                                'max_request_per_day' => $q->withdrawal_max_request_daily,
                            ];
                        })->first();

                    $walletData = [
                        'credit_id' => $value,
                        'credit_name' => $creditFunction->credit->name,
                        'credit_type' => $creditFunction->credit->type,
                        'credit_display' => Lang::has('lang.'.$creditFunction->credit->name) ? Lang::get('lang.'.$creditFunction->credit->name) : $creditFunction->credit->name,
                        'balance' => $walletAmt,
                        'processing_fee' => $currencyData['processing_fee'],
                        'min_order' => $currencyData['min_order'],
                        'max_order' => $currencyData['max_order'],
                        'max_request_per_day' => $currencyData['max_request_per_day'],
                        'rate' => $currencyData['rate'],
                        'currency_id' => $currencyData['id'],
                        'currency_iso' => $creditFunction->credit->currency->iso,
                        'currency_symbol' => $creditFunction->credit->currency->symbol,
                    ];
                },
            ],
            'amount' => [
                Rule::requiredIf(($request->step >= 2)),
                'required_if:step,==2',
                'string',
                'regex:/^(\\d+\\.?\\d{0,2})$/',
                function ($q, $value, $fail) use ($request, $user, &$walletData, &$currencyData, &$summaryData) {
                    $processFee = $currencyData['processing_fee'] ?? 0;
                    $minOrder = $currencyData['min_order'] ?? 0;
                    $maxOrder = $currencyData['max_order'] ?? 0;
                    $maxRequest = $currencyData['max_request_per_day'] ?? 0;

                    if ($minOrder > $value) {
                        $fail(Lang::get('lang.min-order-error', ['minOrder' => Traits\DecimalTrait::setDecimal($minOrder)]));

                        return;
                    }

                    if ($maxOrder < $value) {
                        $fail(Lang::get('lang.max-order-error', ['maxOrder' => Traits\DecimalTrait::setDecimal($maxOrder)]));

                        return;
                    }

                    $checkStatus = Arr::only(Withdrawal::$status, ['pending', 'waiting-approval', 'approved']);
                    $today = date('Y-m-d');

                    $todayWithdrawalCount = Withdrawal::where('user_id', $request->uid)->whereIn('status', $checkStatus)
                        ->where(DB::raw('DATE(created_at)'), $today)->whereNull('deleted_at')->count();
                    if ($todayWithdrawalCount >= $maxRequest) {
                        $fail(Lang::get('lang.reach-withdrawal-limit-error'));

                        return;
                    }

                    if (! $user->is_agent) {
                        $turnover = TransferTurnover::updateUserTurnoverStatus($user);
                        if ($turnover['status'] == false) {
                            $fail('Must Hit At Least Turnover '.Traits\DecimalTrait::setDecimal($turnover['target_turnover']).' to activate transfer');
                        }
                    }

                    $balance = $walletData['balance'];
                    if ($balance < ($value + $processFee)) {
                        $fail(Lang::get('lang.credit-insufficient-balance'));

                        return;
                    }

                    $summaryData = [
                        'withdrawal_amount' => Traits\DecimalTrait::setDecimal(($value)),
                        'received_amount' => Traits\DecimalTrait::setDecimal(($value + $processFee)), // payable_amount
                        'processing_fee' => Traits\DecimalTrait::setDecimal($processFee),
                        'from_iso' => $currencyData['from_currency_iso'],
                        'to_iso' => $currencyData['currency_iso'],
                    ];
                },
            ],
            'channel_id' => [
                Rule::requiredIf(($request->step >= 2)),
                'integer',
                'exists:user_bank,id,user_id,'.($request->uid ?? null).',status,'.UserBank::$status['active'],
            ],
            'pin' => [
                Rule::requiredIf(function () use ($request) {
                    return in_array(MODULE, ['user', 'app']) && ($request->step == 3);
                }),
                function ($q, $value, $fail) use (&$request, &$userDetail) {
                    $tempPass = str_pad($value, 6, '0', STR_PAD_LEFT);
                    if (isset($userDetail['transaction_password']) && Hash::check($tempPass, $userDetail['transaction_password']) != true) {
                        $fail(Lang::get('lang.invalid-pin'));

                        return;
                    }
                },
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if (empty($userDetail['transaction_password'])) {
            abort(400, json_encode(['redirectFlag' => 1, 'errMessage' => Lang::get('lang.no-transaction-pin-error')]));
        }

        if (auth()->user()->restricted == 1) {
            abort(400, 'User Account Restricted.');
        }

        // Schedule Checking
        Schedule::checkSchedule(['withdrawal']);

        if ($request->step == 1) {
            $res = Credit::getWithdrawalChannelList(['userID' => $validator->validated()['uid']]);
            abort(200, json_encode(['data' => array_merge(['wallet_data' => $walletData], $res['payment_type'])]));
        } elseif ($request->step == 2) {
            abort(200, json_encode(['data' => $summaryData]));
        } elseif ($request->step == 3) {
            $res = Withdrawal::add($validator->validated() + [
                'wallet_data' => $walletData,
            ]);
            abort(200, json_encode(['data' => $res]));
        }

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Withdraw List
     *
     * @module = admin
     *
     * @path = credit/withdraw-list
     *
     * @method = post
     *
     * @description = To get withdrawal list.

     *
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     *
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = username|<username>|string|optional|sam| username filter.
     * @body = member_id|<member_id>|string|optional|123123| member_id filter.
     * @body = status|<status>|string|optional|cancel|status filter (dropdown: withdrawal_status)
     * @body = phone_no|<phone_no>|string|optional|60-12345678| phone_no filter.
     * @body = withdrawal_reference|<withdrawal_reference>|string|optional|M122112|Withdrawal ID filter.
     * @body = store_id|<store_id>|integer|optional|27|Store. (dropdown: store)
     * @body = from_date|<from_date>|string|optional|2022-11-01|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-11-01|Create Date filter.
     * @body = updated_from|<updated_from>|string|optional|2022-11-01|Updated Date filter.
     * @body = updated_to|<updated_to>|string|optional|2022-11-01|Updated Date filter.
     * @body = user_from|<user_from>|string|optional|fw|Platform Name Filter. (dropdown: platform_list)
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = export_data|<export_data>|array|option|{"data":{"Key":"Column Name"}}|Data need to export value => header. Required for export
     * @body = file_name|<file_name>|string|optional|withdrawal-list|name of the export file. Required for export
     *
     * @response = {"data":{"list":[{"id":2,"withdrawal_reference":"WB362654","created_at":"23\/08\/2024 11:01:08","username":"***********","member_id":"*********","phone_no":"***********","withdrawal_amount":"100.00","net_withdrawal_amount":"100.00","processing_fee":"0.00","receivable_amount":"100.00","updated_at":"23\/08\/2024 11:01:08","updated_by":"***********","status":"waiting-approval","status_display":"Pending Approval","is_editable":1,"bank_display":"Hong Leong Bank","account_holder":"superdog","account_number":"123","user_from":"VM"},{"id":1,"withdrawal_reference":"WB261580","created_at":"23\/08\/2024 10:45:28","username":"***********","member_id":"*********","phone_no":"***********","withdrawal_amount":"100.00","net_withdrawal_amount":"100.00","processing_fee":"0.00","receivable_amount":"100.00","updated_at":"23\/08\/2024 10:49:59","updated_by":"***********","status":"cancel","status_display":"Cancel","is_editable":0,"bank_display":"Hong Leong Bank","account_holder":"superdog","account_number":"123","user_from":"VM"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":2,"total":2},"meta":null,"summary":{"total_withdrawal":200,"total_processing_fee":0,"total_receivable_amount":200,"total_net_withdrawal_amount":200,"total_amount_approved":0,"total_amount_pending":0,"total_amount_rejected":0,"total_amount_cancel":0},"totalTable":{"total_amount":200,"total_processing_fee":0,"total_receivable_amount":200,"total_net_withdrawal_amount":200,"total_amount_approved":0,"total_amount_pending":0,"total_amount_rejected":0,"total_amount_cancel":100}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************.Ve2_ENMlxr0e_hII6oFjqOEiYUW-LaPpxI5Iw5wI8vA","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.074253797531128 sec","log_id":"57f3efb5-c99b-4cd6-90a2-793b41ac52a2","valid_version":false}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Withdraw List
     *
     * @module = user,app
     *
     * @path = credit/withdraw-list
     *
     * @method = post
     *
     * @description = To get withdrawal list.

     *
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     *
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.

     *
     * @response = {"data":{"list":[{"id":1,"reference":"WB261580","withdrawal_amount":"100.00","net_withdrawal_amount":"100.00","currency_iso":"MYR","bank_display":"Hong Leong Bank","account_holder":"superdog","account_number":"123","created_at":"23\/08\/2024 10:45:28","status":"waiting-approval","status_display":"Pending Approval","is_editable":1}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************.SSAHfpBDmg_NGPBR-yytxVTnWygMUQdS_SiB1fQ6PFc","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.*************** sec","log_id":"fb1f9006-965f-47c9-9e27-5afc236934cf","valid_version":false}
     * ##docs end##
     */
    public function getWithdrawalList(Request $request)
    {
        if (in_array(MODULE, ['admin'])) {
            $adminId = auth()->user()->id;
            $adminRes = Models\Admin::with(
                [
                    'adminDetail' => function ($q) {
                        return $q->where('name', 'store');
                    },
                ],
            )
                ->find($adminId);

            $isMaster = $adminRes->is_master;
            if (! $isMaster) {
                $branchJE = $adminRes->adminDetail->first()->value ?? '[]';
                $branchDE = json_decode($branchJE);

                $request->merge(['extra_store_id' => $branchDE]);
            }
        }
        $validator = Validator::make($request->all(), [
            'limit' => 'integer',
            'page' => 'integer',
            'username' => 'string',
            'member_id' => 'string',
            'phone_no' => 'string',
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d',
            'updated_from' => 'required_with:updated_to|string|date_format:Y-m-d',
            'updated_to' => 'required_with:updated_from|string|date_format:Y-m-d',
            'approval_from_date' => 'required_with:approval_to_date|string|date_format:Y-m-d',
            'store_id' => 'nullable|integer',
            'user_from' => 'string',
            'phone_no' => 'string',
            'extra_store_id' => 'nullable',
            'status' => 'string|in:'.implode(',', array_keys(Withdrawal::$status)),
            'withdrawal_reference' => 'string',
            'type' => 'string|in:'.implode(',', array_keys(Withdrawal::$withdrawalType)),
            'see_all' => 'integer|in:1,0',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string',
            'file_name' => 'required_if:export,==,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Withdrawal::list($validator->validated());

        abort(200, json_encode(['data' => $res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Withdraw Update
     *
     * @module = admin
     *
     * @path = credit/withdraw-update
     *
     * @method = POST
     *
     * @description = To approve/reject/progressing user withdrawal.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = withdrawal_id_ary|<withdrawal_id_ary>|array|required|[1]|Withdrawal channel's id in pending status only.
     * @body = withdrawal_id_ary.*|<withdrawal_id_ary.*>|integer|required|1|Withdrawal channel's id in pending status only.
     * @body = action|<action>|string|required|approved| update action (dropdown: update_withdrawal_status)
     * @body = remark|<remark>|string|required if action = rejected|This is rejected| write remark.

     *
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************.oH6QWndyU7aG2lCCEet9l7XFU-bC97hkllT7Qg8Zea8","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.10791993141174 sec","log_id":"2f4dad1d-2a79-40d5-ac67-31f7959b3a0e"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Withdraw Update
     *
     * @module = user,app
     *
     * @path = credit/withdraw-update
     *
     * @method = POST
     *
     * @description = To cancel withdrawal.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = withdrawal_id_ary|<withdrawal_id_ary>|array|required|[1]|Withdrawal channel's id in pending status only.
     * @body = withdrawal_id_ary.*|<withdrawal_id_ary.*>|integer|required|1|Withdrawal channel's id in pending status only.
     * @body = action|<action>|string|required|cancel| update action, in:'cancel'.

     *
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS9jcmVkaXQvd2l0aGRyYXctdXBkYXRlIiwiaWF0IjoxNjgzNTMwNzY3LCJleHAiOjE2ODM1NDM0MjEsIm5iZiI6MTY4MzUzOTgyMSwianRpIjoiM3hub0VxSGx0aHljNWlINCIsInN1YiI6IjEwMDAwMDEiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.01xJrcwqZsUxCeIPbmFKF-OmLOUIfEMIe4HQIAGbpDE","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.20319890975952 sec","log_id":"d70e0688-13d4-42dd-93b8-a038b3775a1a"}
     * ##docs end##
     */
    public function withdrawUpdate(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $request->request->add(['uid' => Auth::user()->id]);
        }

        $validator = Validator::make($request->all() + ['module' => MODULE], [
            'withdrawal_id_ary' => 'required|array',
            'withdrawal_id_ary.*' => [
                'required',
                'integer',
                function ($q, $value, $fail) use (&$request) {
                    $status = Withdrawal::$status[($request->action ?? null)] ?? null;
                    $checkWithdrawal = Withdrawal::where('id', $value)
                        ->when(in_array(MODULE, ['app', 'user']), function ($q) {
                            return $q->where('user_id', auth()->user()->id);
                        })->when(($status == Withdrawal::$status['pending']), function ($q) {
                            return $q->whereIn('status', Arr::only(Withdrawal::$status, ['waiting-approval']));
                        })->when(($status == Withdrawal::$status['approved']), function ($q) {
                            return $q->whereIn('status', Arr::only(Withdrawal::$status, ['pending']));
                        })->when(($status == Withdrawal::$status['rejected']), function ($q) {
                            return $q->whereIn('status', Arr::only(Withdrawal::$status, ['waiting-approval', 'pending']));
                        })->when(($status == Withdrawal::$status['cancel']), function ($q) {
                            return $q->whereIn('status', Arr::only(Withdrawal::$status, ['waiting-approval']));
                        })->when(($status == Withdrawal::$status['waiting-approval']), function ($q) {
                            return $q->whereIn('status', Arr::only(Withdrawal::$status, ['waiting-approval']));
                        })
                        ->first();
                    if (empty($checkWithdrawal)) {
                        $fail(__('validation.exists', ['attribute' => str_replace('_', ' ', $q)]));

                        return;
                    }
                },
            ],
            'action' => [
                'required',
                'string',
                Rule::when((in_array(MODULE, ['user', 'app'])), function ($q) {
                    return $q = 'in:'.implode(',', array_keys(Arr::only(Withdrawal::$status, ['cancel'])));
                }),
                Rule::when((MODULE == 'admin'), function ($q) {
                    return $q = 'in:'.implode(',', array_keys(Arr::only(Withdrawal::$status, ['pending', 'approved', 'rejected'])));
                }),
            ],
            'remark' => 'required_if:action,=,rejected|string|max:255|nullable',
            'is_payout' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            // abort(400, json_encode($validator->errors()));
            $errors = [];
            foreach ($validator->errors()->toArray() as $err => $dt) {
                if (Str::contains($err, 'withdrawal_id_ary')) {
                    $errors['withdrawal_id_ary'][] = (__('lang.withdrawal-id-invalid'));
                } else {
                    $errors[$err][] = $dt;
                }
            }
            abort(400, json_encode($errors));
        }

        $res = Withdrawal::updateStatus($validator->validated());

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Withdraw Detail
     *
     * @module = admin
     *
     * @path = credit/withdraw-det
     *
     * @method = POST
     *
     * @description = To get user withdrawal detail.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|int|required|1|Withdrawal table id.
     *
     * @response = {"data":{"id":7,"withdrawal_reference":"WB578977","created_at":"08\/05\/2023 17:56:49","username":"*********","member_id":"*********","phone_no":"60-*********","withdrawal_amount":"100.********","net_withdrawal_amount":"105.50","processing_fee":"1.********","receivable_amount":"99.********","bank_display":"Bank Simpanan Nasional (BSN)","account_holder":"Bank 2","account_number":"**********","status":"cancel","status_display":"Cancel","remark":"TEST approve","updated_at":"08\/05\/2023 17:57:01","updated_by":"*********","is_editable":0},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************.RVk4Q1wd14mNqcD5PoUbE8gayZv-RgpuVqHKlz-jDYY","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"087fa90a-6eb6-41ed-b226-6f2ba7d021ff"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Withdraw Detail
     *
     * @module = user,app
     *
     * @path = credit/withdraw-det
     *
     * @method = POST
     *
     * @description = To get withdrawal detail.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|int|required|1|Withdrawal table id.
     *
     * @response = {"data":{"id":1,"withdrawal_amount":"100.00","net_withdrawal_amount":"100.00","currency_iso":"MYR","processing_fee":"0.00","receivable_amount":"100.00","bank_display":"Hong Leong Bank","account_holder":"superdog","account_number":"123","transaction_id":"WB261580","status":"waiting-approval","status_display":"Pending Approval","remark":"-","created_at":"23\/08\/2024 10:45:28","updated_at":"23\/08\/2024 10:45:28","is_editable":1},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************.4Hu2lhaBUtmbPaj6QX2FWig3lruQ0qV95nYQbBBPzGs","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.*************** sec","log_id":"e02e20c1-d07d-4d7b-9f3e-898c8b411bda","valid_version":false}
     * ##docs end##
     */
    public function getWithdrawalDet(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => [
                'required',
                'int',
                Rule::exists('withdrawal', 'id')->where(function ($q) {
                    if (in_array(MODULE, ['user', 'app'])) {
                        return $q->where('user_id', auth()->user()->id);
                    }
                }),
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $getWithdrawalDet = Withdrawal::getWithdrawalDet($validator->validated());
        $data['data'] = $getWithdrawalDet;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Withdrawal Bank List
     *
     * @module = admin
     *
     * @path = credit/withdrawal-bank-list
     *
     * @method = post
     *
     * @description = To get withdrawal bank list.

     *
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     *
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = username|<username>|string|optional|sam|username filter.
     * @body = member_id|<member_id>|string|optional|123123|member_id filter.
     * @body = phone_no|<phone_no>|string|optional|123123|phone_no filter.
     * @body = store_id|<store_id>|integer|optional|27|Store. (dropdown: store)
     * @body = status|<status>|string|optional|active|Status of the Channel added. {"dropdown": "user_bank_status"}
     * @body = from_date|<from_date>|string|optional|2022-11-01|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-11-01|Create Date filter.
     * @body = updated_from_date|<updated_from_date>|string|optional|2022-11-01|Updated Date filter.
     * @body = updated_to_date|<updated_to_date>|string|optional|2022-11-01|Updated Date filter.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = export_data|<export_data>|array|option|{"data":{"Key":"Column Name"}}|Data need to export value => header. Required for export
     * @body = file_name|<file_name>|string|optional|withdrawal-list|name of the export file. Required for export

     *
     * @response = {"data":{"list":[{"id":2,"username":"*********","member_id":"*********","phone_no":"60-*********","bank_name":"Bank Simpanan Nasional (BSN)","account_name":"Bank 2","account_no":"**********","status":"active","status_display":"Active","created_at":"08\/05\/2023 12:33:54","updated_at":"08\/05\/2023 14:43:38"},{"id":1,"username":"*********","member_id":"*********","phone_no":"60-*********","bank_name":"Citibank","account_name":"Bank 1","account_no":"**********","status":"Default","status_display":"Default","created_at":"08\/05\/2023 12:32:20","updated_at":"08\/05\/2023 14:45:23"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":2,"total":2},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************.iRIkLF4ewmi2N6iy4y6K6I1u8SLEdIB4umXrnxS6IxE","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"b8870ef2-265b-44e8-9503-2fe25eb638d2"}
     * ##docs end##
     */
    public function withdrawalBankList(Request $request)
    {
        if (in_array(MODULE, ['admin'])) {
            $adminId = auth()->user()->id;
            $adminRes = Models\Admin::with(
                [
                    'adminDetail' => function ($q) {
                        return $q->where('name', 'store');
                    },
                ],
            )
                ->find($adminId);

            $isMaster = $adminRes->is_master;
            if (! $isMaster) {
                $branchJE = $adminRes->adminDetail->first()->value ?? '[]';
                $branchDE = json_decode($branchJE);

                $request->merge(['extra_store_id' => $branchDE]);
            }
        }

        $validator = Validator::make($request->all(), [
            'limit' => 'integer',
            'page' => 'integer',
            'username' => 'string',
            'member_id' => 'string',
            'store_id' => 'nullable|integer',
            'phone_no' => 'string',
            'status' => 'string|in:'.implode(',', array_keys(UserBank::$status)),
            'extra_store_id' => 'nullable',
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d',
            'updated_from_date' => 'required_with:updated_to_date|string|date_format:Y-m-d',
            'updated_to_date' => 'required_with:updated_from_date|string|date_format:Y-m-d',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string',
            'file_name' => 'required_if:export,==,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = UserBank::list($validator->validated());

        abort(200, json_encode(['data' => $res]));
    }

    public function withdrawalBankDetail(Request $request)
    {
        if (in_array(MODULE, ['admin'])) {
            $adminId = auth()->user()->id;
            $adminRes = Models\Admin::with(
                [
                    'adminDetail' => function ($q) {
                        return $q->where('name', 'store');
                    },
                ],
            )
                ->find($adminId);

            $isMaster = $adminRes->is_master;
            if (! $isMaster) {
                $branchJE = $adminRes->adminDetail->first()->value ?? '[]';
                $branchDE = json_decode($branchJE);

                $request->merge(['extra_store_id' => $branchDE]);
            }
        }

        $validator = Validator::make($request->all(), [
            'id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = UserBank::withdrawalBankDetail($validator->validated());

        abort(200, json_encode(['data' => $res]));
    }

    public function updateWithdrawalBankDetail(Request $request)
    {
        if (in_array(MODULE, ['admin'])) {
            $adminId = auth()->user()->id;
            $adminRes = Models\Admin::with(
                [
                    'adminDetail' => function ($q) {
                        return $q->where('name', 'store');
                    },
                ],
            )
                ->find($adminId);

            $isMaster = $adminRes->is_master;
            if (! $isMaster) {
                $branchJE = $adminRes->adminDetail->first()->value ?? '[]';
                $branchDE = json_decode($branchJE);

                $request->merge(['extra_store_id' => $branchDE]);
            }
        }

        $validator = Validator::make($request->all(), [
            'id' => 'required|integer',
            'account_no' => 'required|string',
            'account_holder' => 'required|string',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = UserBank::updateWithdrawalBankDetail($validator->validated());

        abort(200, json_encode(['data' => $res]));
    }

    public function deleteWithdrawalBankDetail(Request $request)
    {
        if (in_array(MODULE, ['admin'])) {
            $adminId = auth()->user()->id;
            $adminRes = Models\Admin::with(
                [
                    'adminDetail' => function ($q) {
                        return $q->where('name', 'store');
                    },
                ],
            )
                ->find($adminId);

            $isMaster = $adminRes->is_master;
            if (! $isMaster) {
                $branchJE = $adminRes->adminDetail->first()->value ?? '[]';
                $branchDE = json_decode($branchJE);

                $request->merge(['extra_store_id' => $branchDE]);
            }
        }

        $validator = Validator::make($request->all(), [
            'id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = UserBank::deleteWithdrawalBankDetail($validator->validated());

        abort(200, json_encode(['data' => $res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = credit/online-bank-deposit
     *
     * @module = user,app
     *
     * @path = credit/online-bank-deposit
     *
     * @method = POST
     *
     * @description = To upload bank transfer receipt for deposit.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = amount|<amount>|integer|required|50|amount want to top up
     * @body = credit_id|<credit_id>|integer|optional|1000|wallet to top up. Default: myr-credit
     * @body = type|<type>|integer|optional|1|Depoist Type Default: online-bank. Get From get-deposit-data API
     *
     * @response = {"data":{"transaction_id":"jr33gTT1s82YxirS","reference_no":null,"remark":null,"created_at":"04\/07\/2023 11:47:30","deposit_type":"online-bank","amount":"100.00","fpay_url":"https:\/\/sandbox.fpay.support\/ext_info?i=22769&t=********************************"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.R9ktZQgwyF7liuUX8gL9OwBocN8hHGgQK9v8mONYvy0","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"1.************* sec","log_id":"0bba9f6a-e87b-49f6-a0f4-4a0fc9069821"}
     * ##docs end##
     */
    public function onlineBankDeposit(Request $request)
    {
        $userId = Auth::user()->id ?? null;
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => $userId]);
        }
        if (! isset($request->credit_id)) {
            $creditId = Credit::where('name', 'myr-credit')->first()->id ?? null;
            $request->request->add(['credit_id' => $creditId]);
            $request->request->add(['type' => Deposit::$type['online-bank']]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer',
            'credit_id' => [
                'required',
                'exists:credit,id',
                function ($q, $value, $fail) use ($request, &$creditSetting) {
                    $creditSetting = CreditSetting::where(['credit_id' => $value])->whereIn('name', ['is-fundinable', 'fundin-type', 'min-fundin'])->get();
                    $creditSetting->map(function ($q) use (&$fail, $request) {
                        switch ($q->name) {
                            case 'is-fundinable':
                                if ($q->value != 1 || $q->member != 1) {
                                    return $fail(Lang::get('lang.invalid-credit'));
                                }
                                break;

                            case 'fundin-type':
                                $validDepositType = [];
                                if (isset($q->reference)) {
                                    $validDepositType = explode(',', $q->reference);
                                }

                                if (! isset($request->type) || ! in_array($request->type, $validDepositType)) {
                                    return $fail(Lang::get('lang.invalid-credit'));
                                }
                                break;
                        }
                    });
                },
            ],
            'type' => 'required|in:'.implode(',', array_values(Arr::only(Deposit::$type, ['online-bank', 'online-bank-thai', 'online-bank-telco']))),
            'amount' => [
                'required',
                'string',
                function ($q, $value, $fail) use ($request, &$creditSetting) {
                    if (isset($creditSetting)) {
                        $creditSetting->map(function ($q) use (&$fail, $request) {
                            switch ($q->name) {
                                case 'min-fundin':
                                    if ($request->amount < $q->value) {
                                        return $fail(Lang::get('lang.amount-gte-numeric', ['value' => $q->value]));
                                    }
                                    break;
                            }
                        });
                    }
                },
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Deposit::addOnlineBankDeposit($validator->validated());

        abort(200, json_encode(['data' => $res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Top Up By E-Wallet
     *
     * @module = user,app
     *
     * @path = credit/ewallet-deposit
     *
     * @method = POST
     *
     * @description = To deposit by ewallet.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = amount|<amount>|integer|required|50|amount want to top up
     * @body = credit_id|<credit_id>|integer|optional|1000|wallet to top up. Default: myr-credit
     * @body = type|<type>|integer|optional|4|E wallet Type Default: e-wallet. Get From get-deposit-data API
     *
     * @response = {"data":{"transaction_id":"OzB1LgUsBixDLLv1","reference_no":null,"remark":null,"created_at":"06\/09\/2023 12:52:10","deposit_type":"ewallet","amount":"10.00","fpay_url":"https:\/\/sandbox.fpay.support\/ext_info?i=23556&t=********************************","fpay_redirect_url":"?order_id=OzB1LgUsBixDLLv1"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwuYXBwLnRrYXNoLmNvbS9jcmVkaXQvZXdhbGxldC1kZXBvc2l0IiwiaWF0IjoxNjkzOTc0NzY2LCJleHAiOjE2OTM5Nzk1MzEsIm5iZiI6MTY5Mzk3NTkzMSwianRpIjoiSjM0S3pFeFl4a1V0SWRNcyIsInN1YiI6IjEwMDAxNTgiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.HulaN9m-shbx16w2mGomf3Zk-m3_dLtxLnjKExQMAMY","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"1.2754759788513 sec","log_id":"ed385741-18ac-492c-9101-38931b93be3a"}
     * ##docs end##
     */
    public function eWalletDeposit(Request $request)
    {
        $userId = Auth::user()->id ?? null;
        if (MODULE == 'user' || MODULE == 'app') {
            $request->request->add(['user_id' => $userId]);
        }
        if (! isset($request->credit_id)) {
            $creditId = Credit::where('name', 'myr-credit')->first()->id ?? null;
            $request->request->add(['credit_id' => $creditId]);
            // $request->request->add(['type' => Deposit::$type['ewallet']]);
        }
        $request->request->add(['type' => Deposit::$type['ewallet']]);

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer',
            'credit_id' => [
                'required',
                'exists:credit,id',
                function ($q, $value, $fail) use ($request) {
                    CreditSetting::where(['credit_id' => $value])->whereIn('name', ['is-fundinable', 'fundin-type'])->get()->map(function ($q) use (&$fail, $request) {
                        switch ($q->name) {
                            case 'is-fundinable':
                                if ($q->value != 1 || $q->member != 1) {
                                    return $fail(Lang::get('lang.invalid-credit'));
                                }
                                break;

                            case 'fundin-type':
                                $validDepositType = [];
                                if (isset($q->reference)) {
                                    $validDepositType = explode(',', $q->reference);
                                }

                                if (! isset($request->type) || ! in_array($request->type, $validDepositType)) {
                                    return $fail(Lang::get('lang.invalid-credit'));
                                }
                                break;
                        }
                    });
                },
            ],
            'type' => 'required|in:'.implode(',', array_values(Arr::only(Deposit::$type, ['ewallet']))),
            'amount' => 'required|string',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Deposit::addEWalletDeposit($validator->validated());

        abort(200, json_encode(['data' => $res]));
    }

    public static function fpayCallBack(Request $request)
    {
        $res = Deposit::fpayDepositCallBack($request->all());
        abort(200);
    }

    public static function onepayCallBack(Request $request)
    {
        $res = Deposit::onepayDepositCallBack($request->all());
        abort(200);
    }

    public static function rapidpayCallback(Request $request)
    {
        $res = Deposit::rapidpayDepositCallback($request->all());
        abort(200);
    }

    public static function wepayCallback(Request $request)
    {
        $res = Deposit::wepayDepositCallback($request->all());
        abort(200);
    }

    public static function wepayPayoutCallback(Request $request)
    {
        abort(200);
    }

    public function tronUMXCallback(Request $request)
    {
        TransactionLogs::tronUMXCallback($request->all());
        abort(200);
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = MYR Transfer Transaction List
     * @module = admin
     * @path = credit/myr-transfer-transaction-list
     * @method = post
     * @description = Get myr transfer transaction list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = user_id|<user_id>|integer|optional|1|User's Id.
     * @body = from_username|<username>|string|optional|60120000001|User's username.
     * @body = to_username|<username>|string|optional|60120000001|User's username.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = export_data|<export_data>|array|option|{"data":{"Key":"Column Name"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|sponsor-bonus|name of the export file. Required for export


     * @response = {"data":{"list":[{"created_at":"15/04/2024 16:51:57","from_username":"***********","to_username":"661667803566","from_user":"testuser51","to_user":"66-1667803566","amount":"500.00","remark":null,"credit_name":"myr-credit","credit_name_display":"MYR Wallet"},{"created_at":"14/11/2023 04:23:54","from_username":"601120606582","to_username":"60122990744","from_user":"sjjs","to_user":"VS test","amount":"520520.00","remark":"haha","credit_name":"myr-credit","credit_name_display":"MYR Wallet"}],"pagination":{"current_page":1,"from":1,"last_page":6,"per_page":30,"to":30,"total":155},"meta":null,"table_total":{"amount":"17756088.00"},"summary":{"total_transfer":88881021.12,"total_charge":0}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2NyZWRpdC9teXItdHJhbnNmZXItdHJhbnNhY3Rpb24tbGlzdCIsImlhdCI6MTcxMzIzMjcwOCwiZXhwIjoxNzEzMjM2NjE2LCJuYmYiOjE3MTMyMzMwMTYsImp0aSI6ImJST29tRnJPc2E5TEFKSkYiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.ohefSDQHLjugyZXogERvX5tyQdXEglLE3RXolImQc-Y","token_type":"bearer","timezone":"Asia/Kuala_Lumpur","environment":"local","execution_duration":"0.26974487304688 sec","log_id":"d17fea27-391a-4500-b092-de037fb30749"}
     * ##docs end##
     */
    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = THB Transfer Transaction List
     * @module = admin
     * @path = credit/thb-transfer-transaction-list
     * @method = post
     * @description = Get thb transfer transaction list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = user_id|<user_id>|integer|optional|1|User's Id.
     * @body = from_username|<username>|string|optional|60120000001|User's username.
     * @body = to_username|<username>|string|optional|60120000001|User's username.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = export_data|<export_data>|array|option|{"data":{"Key":"Column Name"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|sponsor-bonus|name of the export file. Required for export


     * @response = {"data":{"list":[{"created_at":"15\/04\/2024 16:51:43","from_username":"***********","to_username":"661667803566","from_user":"testuser51","to_user":"66-1667803566","amount":"500.00","remark":null,"credit_name":"thb-credit","credit_name_display":"thb-credit"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null,"table_total":{"amount":"500.00"},"summary":{"total_transfer":500,"total_charge":0}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2NyZWRpdC90aGItdHJhbnNmZXItdHJhbnNhY3Rpb24tbGlzdCIsImlhdCI6MTcxMzIzMjcwOCwiZXhwIjoxNzEzMjM2NzU4LCJuYmYiOjE3MTMyMzMxNTgsImp0aSI6Ims4dDN0V0trWnBRU2pJdHkiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.J7jTGqybvvM4hAlcszRvtLTByAC3e8ysO6vhBfGfNOc","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.15201091766357 sec","log_id":"187bea43-6126-41bd-8007-b986c56b5020"}
     * ##docs end##
     */
    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = USDT Transfer Transaction List
     * @module = admin
     * @path = credit/usdt-transfer-transaction-list
     * @method = post
     * @description = Get usdt transfer transaction list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = user_id|<user_id>|integer|optional|1|User's Id.
     * @body = from_username|<username>|string|optional|60120000001|User's username.
     * @body = to_username|<username>|string|optional|60120000001|User's username.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = export_data|<export_data>|array|option|{"data":{"Key":"Column Name"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|sponsor-bonus|name of the export file. Required for export


     * @response = {"data":{"list":[{"created_at":"15\/04\/2024 16:51:43","from_username":"***********","to_username":"661667803566","from_user":"testuser51","to_user":"66-1667803566","amount":"500.00","remark":null,"credit_name":"thb-credit","credit_name_display":"thb-credit"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null,"table_total":{"amount":"500.00"},"summary":{"total_transfer":500,"total_charge":0}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2NyZWRpdC90aGItdHJhbnNmZXItdHJhbnNhY3Rpb24tbGlzdCIsImlhdCI6MTcxMzIzMjcwOCwiZXhwIjoxNzEzMjM2NzU4LCJuYmYiOjE3MTMyMzMxNTgsImp0aSI6Ims4dDN0V0trWnBRU2pJdHkiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.J7jTGqybvvM4hAlcszRvtLTByAC3e8ysO6vhBfGfNOc","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.15201091766357 sec","log_id":"187bea43-6126-41bd-8007-b986c56b5020"}
     * ##docs end##
     */

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Transfer Transaction List
     * @module = admin,user
     * @path = credit/transfer-transaction-list
     * @method = post
     * @description = Get all transfer transaction list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = credit_id|<credit_id>|integer|optional|1000|Credit's Id.
     * @body = user_id|<user_id>|integer|optional|1|User's Id.
     * @body = from_username|<username>|string|optional|60120000001|User's username.
     * @body = to_username|<username>|string|optional|60120000001|User's username.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
    //  * @body = status|<status>|string|optional|xxx|
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.
    //  * @body = country|<country>|integer|optional|100|Country filter. (Dropdown: country)
    //  * @body = order_by|<order_by>|string|optional|xxx|
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = export_data|<export_data>|array|option|{"data":{"Key":"Column Name"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|sponsor-bonus|name of the export file. Required for export


     * @response = {"data":{"list":[{"created_at":"02\/01\/2024 17:41:06","from_username":"60333456702","to_username":"60333456703","from_user":"VIP002","to_user":"VIP003","amount":"5000000.00","remark":"hello2"},{"created_at":"02\/01\/2024 17:25:00","from_username":"60333456702","to_username":"60333456703","from_user":"VIP002","to_user":"VIP003","amount":"2000000.00","remark":null},{"created_at":"02\/01\/2024 17:15:52","from_username":"60333456701","to_username":"60333456703","from_user":"VIP001","to_user":"VIP003","amount":"50000.00","remark":null}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":3,"total":3},"meta":null,"table_total":{"amount":"7050000.00"},"summary":{"total_transfer":7050000,"total_charge":0}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2NyZWRpdC90cmFuc2Zlci10cmFuc2FjdGlvbi1saXN0IiwiaWF0IjoxNzA1NDY1NTcyLCJleHAiOjE3MDU0Njk3MzAsIm5iZiI6MTcwNTQ2NjEzMCwianRpIjoiWXVsQ0pZcm16SEFKWU0ydyIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.xvRafNrW4deGaTDcEsSKPt-ej6ifBryyiJkErRC4v44","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.11920189857483 sec","log_id":"d1a09cfc-3e70-4cdb-bcc6-71435cb6b681"}
     * ##docs end##
     */

    public function allTransferTransactionList(Request $request)
    {
        $request->request->add(['subject_id_ary' => array_values(Arr::only(config('subject'), ['transfer-in', 'transfer-out']))]);

        $creditType = null;
        switch ($request->path()) {
            case 'credit/myr-transfer-transaction-list':
                $creditType = 'myr-credit';
                break;
            case 'credit/usdt-transfer-transaction-list':
                $creditType = 'usdt-credit';
                break;
            case 'credit/thb-transfer-transaction-list':
                $creditType = 'thb-credit';
                break;
        }

        if (isset($creditType)) {
            $request->request->add(['credit_id' => Credit::where('type', $creditType)->first()->id ?? null]);
        }

        if (! isset($request->credit_id)) {
            $request->request->add(['credit_id' => Credit::first()->id]);
        }

        $validator = Validator::make($request->all(), [
            'limit' => 'integer',
            'page' => 'integer',
            'credit_id' => 'required|integer|exists:credit,id',
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|after_or_equal:from_date|string|date_format:Y-m-d',
            'username' => 'string',
            'reference_name' => 'string',
            'subject_id' => 'integer|in:'.implode(',', array_values(Arr::only(config('subject'), ['transfer-in', 'transfer-out']))),
            'subject_id_ary' => 'array',
            'subject_id_ary.*' => 'integer|in:'.implode(',', array_values(config('subject'))),
            'see_all' => 'integer|in:1,0',
            'show_all' => 'integer|in:1',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string',
            'file_name' => 'required_if:export,==,1',
            'user_id' => 'string',
            'from_username' => 'string',
            'to_username' => 'string',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if (in_array(MODULE, ['user', 'app'])) {
            $res = CreditTransaction::getTransactionList($validator->validated());
        } else {
            $res = Transfer::list($validator->validated());
        }

        abort(200, json_encode(['data' => $res]));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Deposit Method
     * @module = admin
     * @path = credit/deposit-method
     * @method = post
     * @description = Get Deposit Method.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = setting_date|<setting_date>|string|optional|2023-10-11|Setting On the date. Default is today setting.

     * @response = {"data":{"manual-bank":{"name":"manual-bank","display":"manual-bank","status":"on","status_value":"1","status_display":"on","bank_detail":{"bank_name":"MBB","bank_account_name":"fw","bank_account_number":"****************"}},"online-bank":{"name":"online-bank","display":"online-bank","status":"on","status_value":"1","status_display":"on"},"ewallet":{"name":"ewallet","display":"ewallet","status":"on","status_value":"1","status_display":"on"},"crypto":{"name":"crypto","display":"Crypto","status":"on","status_value":"1","status_display":"on"},"online-bank-thai":{"name":"online-bank-thai","display":"online-bank-thai","status":"on","status_value":"1","status_display":"on"},"online-bank-telco":{"name":"online-bank-telco","display":"online-bank-telco","status":"on","status_value":"1","status_display":"on"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************.jk2ohGPONCyvqU2lexznj14h2w5btVask_Ziw11iiyU","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.*************** sec","log_id":"00e2c75d-db64-4512-bd76-33e3ccb06045"}
     * ##docs end##
     */

    public function getDepositMethod(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'setting_date' => 'nullable|string|date_format:Y-m-d|after_or_equal:today',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = CreditSetting::getDepositMethodSetting($validator->validated());
        abort(200, json_encode(['data' => $res]));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Set Deposit Method
     * @module = admin
     * @path = credit/set-deposit-method
     * @method = post
     * @description = Set Deposit Method.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = active_date|<active_date>|string|required|2023-10-11|Setting Start Date
     * @body = manual-bank|<manual-bank>|string|required|1/0|Manual Banking Status. (dropdown: deposit_method_status)
     * @body = online-bank|<online-bank>|string|required|1/0|Fpay Status. (dropdown: deposit_method_status)
     * @body = ewallet|<ewallet>|string|required|1/0|. (dropdown: deposit_method_status)
     * @body = bank_details|<bank_details>|array|required|{"bank_name":"asd","bank_account_name":"asd","bank_account_number":"*********"}| Required When manual bank is 1.
     * @body = bank_details.bank_name|<bank_name>|string|required|asd| Required When manual bank is 1.
     * @body = bank_details.bank_account_name|<bank_account_name>|string|required|asd| Required When manual bank is 1.
     * @body = bank_details.bank_account_name|<bank_account_name>|string|required|************| Required When manual bank is 1.

     * @response = {"data":{"list":[{"created_at":"07\/09\/2023 10:50:37","username":"***********","amount":"50.00","subject":"Transfer In","remark":"hahs","reference":null,"type":"in","transaction_type":"Receive from","toFrom_name":"testuser51","toFrom":"***********","balance":"251.66"},{"created_at":"05\/09\/2023 15:58:04","username":"***********","amount":"100.00","subject":"Transfer In","remark":null,"reference":null,"type":"in","transaction_type":"Receive from","toFrom_name":"testuser51","toFrom":"***********","balance":"201.66"},{"created_at":"05\/09\/2023 15:44:01","username":"***********","amount":"100.00","subject":"Transfer In","remark":null,"reference":null,"type":"in","transaction_type":"Receive from","toFrom_name":"testuser51","toFrom":"***********","balance":"101.66"},{"created_at":"01\/08\/2023 15:32:41","username":"***********","amount":"10.00","subject":"Transfer Out","remark":"kdkkekf","reference":null,"type":"out","transaction_type":"Transfer to","toFrom_name":"testuser51","toFrom":"***********","balance":"1.66"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":4,"total":4},"meta":null,"table_total":{"amount":"240.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vYXBpLWFkbWluLnRrYXNoLnh5ei9jcmVkaXQvdHJhbnNmZXItdHJhbnNhY3Rpb24tbGlzdCIsImlhdCI6MTY5NDA2NjkyOSwiZXhwIjoxNjk0MDcwNjMzLCJuYmYiOjE2OTQwNjcwMzMsImp0aSI6IjNXbDRHaERBVmltZTRCOHUiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.9kaTIr5wfjaFl48z_g5JNePCY-IVfmJ-CKLjXygfyeg","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"staging","execution_duration":"0.************** sec","log_id":"********-81ae-41fc-917d-c7d167cf8eb3"}
     * ##docs end##
     */

    public function setDepositMethod(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'active_date' => 'required|string|date_format:Y-m-d|after_or_equal:today',
            'manual-bank' => 'required|in:'.implode(',', array_values(SystemSettingsAdmin::$depositMethodStatus)),
            'online-bank' => 'required|in:'.implode(',', array_values(SystemSettingsAdmin::$depositMethodStatus)),
            // "online-bank-onepay" => "required|in:" . implode(",", array_values(SystemSettingsAdmin::$depositMethodStatus)),
            'ewallet' => 'required|in:'.implode(',', array_values(SystemSettingsAdmin::$depositMethodStatus)),
            //  "online-bank-telco" => "required|in:".implode(",", array_values(SystemSettingsAdmin::$depositMethodStatus)),
            'bank_details' => 'required_if:manual-bank,==,'.SystemSettingsAdmin::$depositMethodStatus['on'].'|array',
            'bank_details.bank_name' => 'required_if:manual-bank,==,'.SystemSettingsAdmin::$depositMethodStatus['on'].'|string',
            'bank_details.bank_account_name' => 'required_if:manual-bank,==,'.SystemSettingsAdmin::$depositMethodStatus['on'].'|string',
            'bank_details.bank_account_number' => 'required_if:manual-bank,==,'.SystemSettingsAdmin::$depositMethodStatus['on'].'|string',
        ], [

            'bank_details' => Lang::get('lang.input-field-required-error'),
            'bank_details.*' => Lang::get('lang.input-field-required-error'),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = CreditSetting::setDepositMethodSetting($validator->validated());
        abort(200);
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Deposit Method History
     * @module = admin
     * @path = credit/get-deposit-method-listing
     * @method = post
     * @description = Get Deposit Method History .

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.

     * @response = {"data":{"list":[{"ref_id":**********,"manual_bank_status":"off","manual_bank_status_display":"off","bank_details":null,"online_bank_status":"on","online_bank_status_display":"on","ewallet_status":"on","ewallet_status_display":"on","telco_status":"on","telco_status_display":"on","active_at":"15\/05\/2024","created_at":"15\/05\/2024 12:10:38","created_by":"tkMins"},{"ref_id":**********,"manual_bank_status":"off","manual_bank_status_display":"off","bank_details":null,"online_bank_status":"on","online_bank_status_display":"on","ewallet_status":"on","ewallet_status_display":"on","telco_status":"off","telco_status_display":"off","active_at":"15\/05\/2024","created_at":"15\/05\/2024 12:09:45","created_by":"tkMins"},{"ref_id":**********,"manual_bank_status":"on","manual_bank_status_display":"on","bank_details":{"bank_name":"MBB","bank_account_name":"fw","bank_account_number":"****************"},"online_bank_status":"on","online_bank_status_display":"on","ewallet_status":"on","ewallet_status_display":"on","telco_status":"on","telco_status_display":"on","active_at":"26\/12\/2023","created_at":"27\/12\/2023 03:20:28","created_by":"tkMins"},{"ref_id":**********,"manual_bank_status":"on","manual_bank_status_display":"on","bank_details":{"bank_name":"Cimb","bank_account_name":"fw","bank_account_number":"************"},"online_bank_status":"on","online_bank_status_display":"on","ewallet_status":"on","ewallet_status_display":"on","telco_status":"off","telco_status_display":"off","active_at":"08\/11\/2023","created_at":"09\/11\/2023 10:08:29","created_by":"tkMins"},{"ref_id":**********,"manual_bank_status":"off","manual_bank_status_display":"off","bank_details":null,"online_bank_status":"on","online_bank_status_display":"on","ewallet_status":"on","ewallet_status_display":"on","telco_status":"off","telco_status_display":"off","active_at":"28\/10\/2023","created_at":"24\/10\/2023 06:34:19","created_by":"tkMins"},{"ref_id":**********,"manual_bank_status":"on","manual_bank_status_display":"on","bank_details":{"bank_name":"Pokemon Poke","bank_account_name":"Pikachu","bank_account_number":"************"},"online_bank_status":"off","online_bank_status_display":"off","ewallet_status":"off","ewallet_status_display":"off","telco_status":"off","telco_status_display":"off","active_at":"27\/10\/2023","created_at":"24\/10\/2023 03:10:28","created_by":"tkMins"},{"ref_id":**********,"manual_bank_status":"on","manual_bank_status_display":"on","bank_details":{"bank_name":"Pokemon GO","bank_account_name":"Pikachu Test","bank_account_number":"*********"},"online_bank_status":"off","online_bank_status_display":"off","ewallet_status":"off","ewallet_status_display":"off","telco_status":"off","telco_status_display":"off","active_at":"27\/10\/2023","created_at":"24\/10\/2023 03:09:11","created_by":"tkMins"},{"ref_id":**********,"manual_bank_status":"off","manual_bank_status_display":"off","bank_details":null,"online_bank_status":"on","online_bank_status_display":"on","ewallet_status":"off","ewallet_status_display":"off","telco_status":"off","telco_status_display":"off","active_at":"26\/10\/2023","created_at":"24\/10\/2023 03:10:04","created_by":"tkMins"},{"ref_id":**********,"manual_bank_status":"off","manual_bank_status_display":"off","bank_details":null,"online_bank_status":"off","online_bank_status_display":"off","ewallet_status":"off","ewallet_status_display":"off","telco_status":"off","telco_status_display":"off","active_at":"26\/10\/2023","created_at":"24\/10\/2023 03:08:32","created_by":"tkMins"},{"ref_id":**********,"manual_bank_status":"off","manual_bank_status_display":"off","bank_details":null,"online_bank_status":"off","online_bank_status_display":"off","ewallet_status":"on","ewallet_status_display":"on","telco_status":"off","telco_status_display":"off","active_at":"26\/10\/2023","created_at":"24\/10\/2023 03:08:01","created_by":"tkMins"},{"ref_id":**********,"manual_bank_status":"off","manual_bank_status_display":"off","bank_details":null,"online_bank_status":"off","online_bank_status_display":"off","ewallet_status":"on","ewallet_status_display":"on","telco_status":"off","telco_status_display":"off","active_at":"25\/10\/2023","created_at":"24\/10\/2023 03:09:57","created_by":"tkMins"},{"ref_id":**********,"manual_bank_status":"on","manual_bank_status_display":"on","bank_details":{"bank_name":"fw Sdn Bhd","bank_account_name":"Maybank Berhad","bank_account_number":"1234 7890 4567"},"online_bank_status":"off","online_bank_status_display":"off","ewallet_status":"off","ewallet_status_display":"off","telco_status":"off","telco_status_display":"off","active_at":"25\/10\/2023","created_at":"24\/10\/2023 03:08:14","created_by":"tkMins"},{"ref_id":**********,"manual_bank_status":"off","manual_bank_status_display":"off","bank_details":null,"online_bank_status":"on","online_bank_status_display":"on","ewallet_status":"on","ewallet_status_display":"on","telco_status":"off","telco_status_display":"off","active_at":"24\/10\/2023","created_at":"24\/10\/2023 03:09:33","created_by":"tkMins"},{"ref_id":**********,"manual_bank_status":"on","manual_bank_status_display":"on","bank_details":{"bank_name":"fw Sdn Bhd","bank_account_name":"Maybank Berhad","bank_account_number":"1234 7890 4567"},"online_bank_status":"on","online_bank_status_display":"on","ewallet_status":"on","ewallet_status_display":"on","telco_status":"off","telco_status_display":"off","active_at":"24\/10\/2023","created_at":"24\/10\/2023 03:07:50","created_by":"tkMins"},{"ref_id":**********,"manual_bank_status":"on","manual_bank_status_display":"on","bank_details":{"bank_name":"fw Sdn Bhd","bank_account_name":"Maybank Berhad","bank_account_number":"1234 7890 4567"},"online_bank_status":"off","online_bank_status_display":"off","ewallet_status":"off","ewallet_status_display":"off","telco_status":"off","telco_status_display":"off","active_at":"24\/10\/2023","created_at":"24\/10\/2023 02:55:20","created_by":"tkMins"},{"ref_id":**********,"manual_bank_status":"off","manual_bank_status_display":"off","bank_details":null,"online_bank_status":"on","online_bank_status_display":"on","ewallet_status":"on","ewallet_status_display":"on","telco_status":"off","telco_status_display":"off","active_at":"24\/10\/2023","created_at":"24\/10\/2023 02:55:03","created_by":"tkMins"},{"ref_id":**********,"manual_bank_status":"on","manual_bank_status_display":"on","bank_details":{"bank_name":"fw Sdn Bhd","bank_account_name":"CIMB Berhad","bank_account_number":"************"},"online_bank_status":"on","online_bank_status_display":"on","ewallet_status":"on","ewallet_status_display":"on","telco_status":"off","telco_status_display":"off","active_at":"23\/10\/2023","created_at":"24\/10\/2023 08:38:29","created_by":"testadm"},{"ref_id":**********,"manual_bank_status":"on","manual_bank_status_display":"on","bank_details":{"bank_name":"fw Sdn Bhd","bank_account_name":"CIMB Berhad","bank_account_number":"************"},"online_bank_status":"off","online_bank_status_display":"off","ewallet_status":"on","ewallet_status_display":"on","telco_status":"off","telco_status_display":"off","active_at":"23\/10\/2023","created_at":"24\/10\/2023 08:37:59","created_by":"testadm"},{"ref_id":**********,"manual_bank_status":"on","manual_bank_status_display":"on","bank_details":{"bank_name":"fw Sdn Bhd","bank_account_name":"CIMB Berhad","bank_account_number":"1234 7890 4567"},"online_bank_status":"off","online_bank_status_display":"off","ewallet_status":"on","ewallet_status_display":"on","telco_status":"off","telco_status_display":"off","active_at":"23\/10\/2023","created_at":"24\/10\/2023 08:37:34","created_by":"testadm"},{"ref_id":**********,"manual_bank_status":"on","manual_bank_status_display":"on","bank_details":{"bank_name":"fw Sdn Bhd","bank_account_name":"CIMB Berhad","bank_account_number":"1234 7890 4567"},"online_bank_status":"off","online_bank_status_display":"off","ewallet_status":"on","ewallet_status_display":"on","telco_status":"off","telco_status_display":"off","active_at":"23\/10\/2023","created_at":"24\/10\/2023 07:37:29","created_by":"testadm"},{"ref_id":**********,"manual_bank_status":"on","manual_bank_status_display":"on","bank_details":{"bank_name":"fw Sdn Bhd","bank_account_name":"CIMB Berhad","bank_account_number":"1234 7890 4567"},"online_bank_status":"on","online_bank_status_display":"on","ewallet_status":"on","ewallet_status_display":"on","telco_status":"off","telco_status_display":"off","active_at":"23\/10\/2023","created_at":"24\/10\/2023 07:37:09","created_by":"testadm"},{"ref_id":**********,"manual_bank_status":"on","manual_bank_status_display":"on","bank_details":{"bank_name":"fw Sdn Bhd","bank_account_name":"CIMB Berhad","bank_account_number":"1234 7890 4567"},"online_bank_status":"on","online_bank_status_display":"on","ewallet_status":"on","ewallet_status_display":"on","telco_status":"off","telco_status_display":"off","active_at":"23\/10\/2023","created_at":"24\/10\/2023 07:36:46","created_by":"testadm"},{"ref_id":**********,"manual_bank_status":"off","manual_bank_status_display":"off","bank_details":null,"online_bank_status":"on","online_bank_status_display":"on","ewallet_status":"on","ewallet_status_display":"on","telco_status":"off","telco_status_display":"off","active_at":"23\/10\/2023","created_at":"24\/10\/2023 07:36:27","created_by":"testadm"},{"ref_id":0,"manual_bank_status":"on","manual_bank_status_display":"on","bank_details":null,"online_bank_status":"on","online_bank_status_display":"on","ewallet_status":"on","ewallet_status_display":"on","telco_status":"off","telco_status_display":"off","active_at":"06\/01\/2023","created_at":"06\/01\/2023 08:47:20","created_by":"System"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":24,"total":24},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************.uVoWoTDatWGIA1xKjvBy2kc1A7sX-skVF8bv6b3Dz1k","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.*************** sec","log_id":"63e1611b-96e2-4946-a50c-2ef9c7f3d625"}
     * ##docs end##
     */

    public function getDepositMethodListing(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'limit' => 'integer',
            'page' => 'integer',
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|after_or_equal:from_date|string|date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = CreditSetting::depositMethodHistory($validator->validated());

        abort(200, json_encode(['data' => $res]));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Exp Transaction List
     * @module = user,app
     * @path = credit/exp-transaction-list
     * @method = post
     * @description = To exp transaction list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter. (app_transaction_date)
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter. (app_transaction_date)

     * @response = {"data":{"list":[{"from_username":"0123482615","from_phone":"60123482615","source":"oc-bet","source_display":"oc-bet","type":"personal","type_display":"personal","created_at":"13\/12\/2023 12:27:52","point":"19.49","amount":"389.90","trxn_type":"in"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS9jcmVkaXQvZXhwLXRyYW5zYWN0aW9uLWxpc3QiLCJpYXQiOjE3MDI2MDYxMTcsImV4cCI6MTcwMjYwOTg0NywibmJmIjoxNzAyNjA2MjQ3LCJqdGkiOiJzeUFIV2lMOThNOG0zMTZFIiwic3ViIjoiMTAwNjU2MCIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.b9ZgKZwUZJzaWC4CnxjuZkqdix7wUXP-x1d4JK-LIO8","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.29408812522888 sec","log_id":"932188a5-6393-4e1f-b6a2-7f8793391a82"}
     * ##docs end##
     */

    public function getExpTransactionList(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $request->request->add(['user_id' => Auth::user()->id]);
        }
        $request->request->add(['credit_id' => Credit::where('name', 'exp')->first()->id]);

        $validator = Validator::make($request->all(), [
            'limit' => 'integer',
            'page' => 'integer',
            'user_id' => [
                'required',
                'integer',
                Rule::exists('users', 'id')->whereIN('user_type', [User::$userType['user-account']]),
            ],
            'credit_id' => 'required_without:credit_type|integer|exists:credit,id',
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d|after_or_equal:from_date',
            'subject_id' => 'integer',
            'see_all' => 'integer|in:1,0',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = CreditTransaction::getExpTransactionList($request->all());

        abort(200, json_encode(['data' => $res]));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = External Transaction Refund
     * @module = admin
     * @path = credit/ex-transfer-refund
     * @method = post
     * @description = To manually refund for External Transaction

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = ex_transfer_id|<ex_transfer_id>|integer|required|1| External transfer unique id (ex_transfer_id)

     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2NyZWRpdC9leC10cmFuc2Zlci1yZWZ1bmQiLCJpYXQiOjE3MDcxMDYwNjgsImV4cCI6MTcwNzExMjkyMCwibmJmIjoxNzA3MTA5MzIwLCJqdGkiOiJEd0FZWmpBSkFMVEdzOE1zIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.3SCWw2lC9hFjL-cHrj76UaqpHKjTGidVl5jRWM-cvRM","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.13434505462646 sec","log_id":"9df262b8-ae89-4efa-bb21-77ad0e47012b"}
     * ##docs end##
     */

    public function exTransferRefund(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ex_transfer_id' => [
                'required',
                'integer',
                function ($q, $value, $fail) {
                    $checkExTransfer = ExTransfer::where([
                        'id' => $value,
                        'status' => ExTransfer::$status['failed'],
                        'type' => ExTransfer::$type['in'],
                    ])
                        ->first();

                    if (empty($checkExTransfer)) {
                        $fail(__('validation.exists'));

                        return;
                    }
                },
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        ExTransfer::refundConfirmation($validator->validated());
        abort(200);
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Recall i8 callback
     * @module = admin
     * @path = credit/ex-transfer-recall-i8
     * @method = post
     * @description = To manually recall for i8 callback

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = ex_transfer_id|<ex_transfer_id>|integer|required|1| External transfer unique id (ex_transfer_id)

     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2NyZWRpdC9leC10cmFuc2Zlci1yZWNhbGwtaTgiLCJpYXQiOjE3MTQ5OTA5NjMsImV4cCI6MTcxNDk5NTY3NiwibmJmIjoxNzE0OTkyMDc2LCJqdGkiOiJkVTdhcHlIQ05TZWpLMnF3Iiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.5acMtc3UUP_U7xOy43PqMniZCCgeKN4o3Sw2IASAFFI","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.033761978149414 sec","log_id":"16399073-0dc4-4f1a-b34b-602f415ce96a"}
     * ##docs end##
     */

    public function exTransferRecallOC(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ex_transfer_id' => [
                'required',
                'integer',
                function ($q, $value, $fail) use (&$request) {
                    $checkExTransfer = ExTransfer::where([
                        'id' => $value,
                        'status' => ExTransfer::$status['failed'],
                        'type' => ExTransfer::$type['in'],
                    ])
                        ->first();

                    if (! isset($checkExTransfer)) {
                        $fail(__('validation.exists'));

                        return;
                    }

                    $depositData = Deposit::with([
                        'tpWebHook' => function ($q) {
                            // return $q->where('table_type', TpWebHook::$tableType['deposit']);
                            return $q->where('type', TpWebHook::$type['callback/deposit'])
                                ->orderBy('id', 'desc')->first();
                        },
                    ])->where('code', $checkExTransfer->reference)->first();

                    if (! isset($depositData)) {
                        $fail(__('validation.exists'));

                        return;
                    }

                    $webHookId = $depositData->tpWebHook->first()->id ?? null;
                    if (empty($webHookId)) {
                        $fail(__('validation.exists'));

                        return;
                    }

                    $request->merge(['web_hook_id' => $webHookId]);
                },
            ],
            'web_hook_id' => 'nullable',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $validator->setData(array_merge($validator->validated(), ['web_hook_id' => $request->web_hook_id]));
        ExTransfer::recallOCConfirmation($validator->validated());
        abort(200);
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = External Transaction Credit
     * @module = admin
     * @path = credit/ex-transfer-credit
     * @method = post
     * @description = To manually credit for External Transaction

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = ex_transfer_id|<ex_transfer_id>|integer|required|1| External transfer unique id (ex_transfer_id)

     * @response = {"status":true,"message":"lang.granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2NyZWRpdC9leC10cmFuc2Zlci1jcmVkaXQiLCJpYXQiOjE3MDkwMjYxNjUsImV4cCI6MTcwOTAzMDc0MywibmJmIjoxNzA5MDI3MTQzLCJqdGkiOiJ1d2V2OFFqc2sxV0hPdlRvIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.3aGs9AoK8jF5ELalIi6lNHNGYaZs1KdswPdmoJrOIY4","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.066671848297119 sec","log_id":"3b8faf8d-076e-47f1-a387-bf75adace4eb"}
     * ##docs end##
     */

    public function exTransferCredit(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ex_transfer_id' => [
                'required',
                'integer',
                function ($q, $value, $fail) {
                    $checkExTransfer = ExTransfer::where([
                        'id' => $value,
                        'status' => ExTransfer::$status['failed'],
                        'type' => ExTransfer::$type['out'],
                    ])
                        ->first();

                    if (empty($checkExTransfer)) {
                        $fail(__('validation.exists'));

                        return;
                    }
                },
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        ExTransfer::creditConfirmation($validator->validated());
        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Withdraw Third Party List
     *
     * @module = admin
     *
     * @path = credit/withdraw-third-party-list
     *
     * @method = post
     *
     * @description = To get withdrawal third party list.

     *
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     *
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = username|<username>|string|optional|sam| username filter.
     * @body = member_id|<member_id>|string|optional|123123| member_id filter.
     * @body = status|<status>|string|optional|cancel|status filter (dropdown: withdrawal_third_party_status)
     * @body = phone_no|<phone_no>|string|optional|60-12345678| phone_no filter.
     * @body = withdrawal_reference|<withdrawal_reference>|string|optional|M122112|Withdrawal ID filter.
     * @body = txn_id|<txn_id>|string|optional|M122112|Tarnsaction ID filter.
     * @body = platform|<platform>|integer|optional|M122112|Platform ID filter. (dropdown: withdrawal_platform)
     * @body = from_date|<from_date>|string|optional|2022-11-01|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-11-01|Create Date filter.
     * @body = updated_from|<updated_from>|string|optional|2022-11-01|Updated Date filter.
     * @body = updated_to|<updated_to>|string|optional|2022-11-01|Updated Date filter.
     * @body = user_from|<user_from>|string|optional|fw|Platform Name Filter. (dropdown: platform_list)
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = export_data|<export_data>|array|option|{"data":{"Key":"Column Name"}}|Data need to export value => header. Required for export
     * @body = file_name|<file_name>|string|optional|withdrawal-list|name of the export file. Required for export
     *
     * @response = {"data":{"list":[{"id":7,"withdrawal_reference":"WB815540","created_at":"09\/05\/2024 10:32:55","username":"**********","member_id":"*********","phone_no":"**********","withdrawal_amount":"1.00","net_withdrawal_amount":"1.00","processing_fee":"0.00","receivable_amount":"1.00","updated_at":"09\/05\/2024 10:33:42","updated_by":"tkMins","status":"rejected","status_display":"Rejected","is_editable":0,"bank_name":"bank_name","account_holder":"account_holder","account_number":"account_number","txn_id":"9000004","platform_display":"TK8TH (MYR) ","user_from":"fw"}],"pagination":{"current_page":1,"from":1,"last_page":7,"per_page":1,"to":1,"total":7},"meta":null,"totalTable":{"total_amount":1,"total_processing_fee":0,"total_receivable_amount":1,"total_net_withdrawal_amount":1}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.mMgo2-SmKsuYkm72mqSkeZtO6IYqNGt3b-qRZ-U_F60","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.*************** sec","log_id":"343b8d35-5b7e-42f0-9d6c-d7228e8931f1"}
     * ##docs end##
     */
    // Member ID	Phone No. 	Transaction ID	Ref ID	Status	Platform
    public function getWithdrawalThirdPartyList(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'limit' => 'integer',
            'page' => 'integer',
            'username' => 'string',
            'member_id' => 'string',
            'phone_no' => 'string',
            'txn_id' => 'string',
            'platform' => 'integer|exists:product,id',
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d',
            'updated_from' => 'required_with:updated_to|string|date_format:Y-m-d',
            'updated_to' => 'required_with:updated_from|string|date_format:Y-m-d',
            'approval_from_date' => 'required_with:approval_to_date|string|date_format:Y-m-d',
            'approval_to_date' => 'required_with:approval_from_date|string|date_format:Y-m-d',
            'status' => 'string|in:'.implode(',', array_keys(Withdrawal::$status)),
            'withdrawal_reference' => 'string',
            'type' => 'string|in:'.implode(',', array_keys(Withdrawal::$withdrawalType)),
            'user_from' => 'string',
            'see_all' => 'integer|in:1,0',
            'export' => 'in:1,0|nullable',
            'export_data' => 'required_if:export,==,1',
            'list_key' => 'string',
            'file_name' => 'required_if:export,==,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = WithdrawalThirdParty::list($validator->validated());

        abort(200, json_encode(['data' => $res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Withdraw Third Party Update
     *
     * @module = admin
     *
     * @path = credit/withdraw-third-party-update
     *
     * @method = POST
     *
     * @description = To approve/reject/progressing user withdrawal third party.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = withdrawal_id_ary|<withdrawal_id_ary>|array|required|[1]|Withdrawal channel's id in pending status only.
     * @body = withdrawal_id_ary.*|<withdrawal_id_ary.*>|integer|required|1|Withdrawal channel's id in pending status only.
     * @body = action|<action>|string|required|approved| update action (dropdown: update_withdrawal_third_party_status)
     * @body = remark|<remark>|string|required if action = rejected|This is rejected| write remark.

     *
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************.oH6QWndyU7aG2lCCEet9l7XFU-bC97hkllT7Qg8Zea8","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.10791993141174 sec","log_id":"2f4dad1d-2a79-40d5-ac67-31f7959b3a0e"}
     * ##docs end##
     */
    public function withdrawThirdPartyUpdate(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $request->request->add(['uid' => Auth::user()->id]);
        }

        $validator = Validator::make($request->all() + ['module' => MODULE], [
            'withdrawal_id_ary' => 'required|array',
            'withdrawal_id_ary.*' => [
                'required',
                'integer',
                function ($q, $value, $fail) use (&$request) {
                    $status = WithdrawalThirdParty::$status[($request->action ?? null)] ?? null;
                    $checkWithdrawal = WithdrawalThirdParty::where('id', $value)
                        ->when(in_array(MODULE, ['app', 'user']), function ($q) {
                            return $q->where('user_id', auth()->user()->id);
                            // })->when(($status == Withdrawal::$status['pending']),function ($q){
                            //     return $q->whereIn('status', Arr::only(Withdrawal::$status,['waiting-approval']));
                        })->when(($status == Withdrawal::$status['approved']), function ($q) {
                            return $q->whereIn('status', Arr::only(Withdrawal::$status, ['waiting-approval', 'pending']));
                        })->when(($status == Withdrawal::$status['rejected']), function ($q) {
                            return $q->whereIn('status', Arr::only(Withdrawal::$status, ['waiting-approval', 'pending']));
                        })->when(($status == Withdrawal::$status['cancel']), function ($q) {
                            return $q->whereIn('status', Arr::only(Withdrawal::$status, ['waiting-approval']));
                        })->when(($status == Withdrawal::$status['waiting-approval']), function ($q) {
                            return $q->whereIn('status', Arr::only(Withdrawal::$status, ['waiting-approval']));
                        })
                        ->first();
                    if (empty($checkWithdrawal)) {
                        $fail(__('validation.exists', ['attribute' => str_replace('_', ' ', $q)]));

                        return;
                    }
                },
            ],
            'action' => [
                'required',
                'string',
                Rule::when((in_array(MODULE, ['user', 'app'])), function ($q) {
                    return $q = 'in:'.implode(',', array_keys(Arr::only(Withdrawal::$status, ['cancel'])));
                }),
                Rule::when((MODULE == 'admin'), function ($q) {
                    return $q = 'in:'.implode(',', array_keys(Arr::only(Withdrawal::$status, ['pending', 'approved', 'rejected'])));
                }),
            ],
            'remark' => 'required_if:action,=,rejected|string|max:255|nullable',
        ]);

        if ($validator->fails()) {
            // abort(400, json_encode($validator->errors()));
            $errors = [];
            foreach ($validator->errors()->toArray() as $err => $dt) {
                if (Str::contains($err, 'withdrawal_id_ary')) {
                    $errors['withdrawal_id_ary'][] = (__('lang.withdrawal-id-invalid'));
                } else {
                    $errors[$err][] = $dt;
                }
            }
            abort(400, json_encode($errors));
        }

        $res = WithdrawalThirdParty::updateStatus($validator->validated());

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Withdraw Third Party Detail
     *
     * @module = admin
     *
     * @path = credit/withdraw-third-party-det
     *
     * @method = POST
     *
     * @description = To get user withdrawal third party detail.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|int|required|1|Withdrawal table id.
     *
     * @response = {"data":{"id":1,"withdrawal_reference":"WB706506","created_at":"04\/04\/2024 15:48:23","username":"**********","member_id":"*********","phone_no":"66-********","withdrawal_amount":"1.00","net_withdrawal_amount":"3.00","processing_fee":"2.00","receivable_amount":"1.00","bank_name":"bank_name","account_holder":"account_holder","account_number":"account_number","txn_id":"9000000","status":"approved","status_display":"Approved","remark":"-","updated_at":"04\/04\/2024 17:39:21","updated_by":"tkMins","is_editable":0,"platform_display":"TK8TH (MYR) "},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************.WnsV2lqqKCeUz-PuCRlJo9Jx-H6dxiXriaDw2cVRSCY","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.*************** sec","log_id":"2bd7d91b-afc5-416c-a60f-edee45dcd8ca"}
     * ##docs end##
     */
    public function getWithdrawalThirdPartyDet(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => [
                'required',
                'int',
                Rule::exists('withdrawal_third_party', 'id')->where(function ($q) {
                    if (in_array(MODULE, ['user', 'app'])) {
                        return $q->where('user_id', auth()->user()->id);
                    }
                }),
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $getWithdrawalDet = WithdrawalThirdParty::getWithdrawalDet($validator->validated());
        $data['data'] = $getWithdrawalDet;
        abort(200, json_encode($data));
    }

    public function generateWithdrawalOrder(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'account_holder' => 'required',
            'account_no' => 'required',
            'bank_id' => 'required',
            'amount' => 'required',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $order = Withdrawal::generateWithdrawalOrder($validator->validated());
        $data['data'] = $order;
        abort(200, json_encode($data));
    }
}
