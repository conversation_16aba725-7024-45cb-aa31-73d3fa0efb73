<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Lang;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use App\Models;
use App\Models\Announcement;

class AnnouncementController extends Controller
{
    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = announcement/add-announcement
     * @module = admin
     * @path = announcement/add-announcement
     * @permissionName = Add Announcement
     * @menuType = api
     * @parent = Announcement
     * @method = POST
     * @description = To add announcement.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = announcement_data|<array>|array|required|[{"subject":"TEST Announcement 3","language_type":"en","description":"TEST Description","image_data":[{"image_name":"image1.jpg","image_type":"image\/jpg"}],"attachment_data":[{"attachment_name":"test.pdf","attachment_type":"application\/pdf"}]}]|Announcement's Data. 
     * @body = announcement_data.*.author|<announcement_data.*.author>|string|optional|CS|Announcement's author.
     * @body = announcement_data.*.subject|<subject>|string|required|test subject|Announcement's Subject.
     * @body = announcement_data.*.short_description|<short_description>|string|optional|test short description|Announcement's Short Description.
     * @body = announcement_data.*.description|<description>|string|required|test description|Announcement's Description.
     * @body = announcement_data.*.language_type|<language_type>|string|required|en, cn, my|Announcement's Language.
     * @body = announcement_data.*.image_data|<array>|array|required|[{'image_name' : 'test/2022/09/1663906995_0715_test.png'},{'image_type' : 'image/png'}]|Announcement's Image.
     * @body = announcement_data.*.image_data.*.image_name|<image_name>|string|required|test/2022/09/1663906995_0715_test.png|Announcement's Image (Public Type).
     * @body = announcement_data.*.image_data.*.image_type|<image_type>|string|required|image/jpg, image/jpeg, image/png|Announcement's Image type.
     * @body = start_date|<start_date>|string|optional|2022-08-28|Announcement's start date.
     * @body = end_date|<end_date>|string|optional|2022-08-28|Announcement's end date.
     * @body = status|<status>|string|optional|active / inactive|Announcement's status. (dropdown: announcement_status)
     * 
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20vZG9jdW1lbnQvYWRkLW1lbW8iLCJpYXQiOjE2NjQxNjY1NjcsImV4cCI6MTY2NDE4NDYyMywibmJmIjoxNjY0MTgxMDIzLCJqdGkiOiI1RUpyajNkNldZVFNURE9MIiwic3ViIjoiMiIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.VrXHrJ6x6cOB03nZK8YGxuAy_VfAfnqyZHmkI2lqCvU","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.0040340423583984 sec","log_id":"7518ad4b-dc0c-484b-9a47-5e2893e28256"}
     * ##docs end##
     */
    public function addAnnouncement(Request $request){
        $validator = Validator::make($request->all(), [
            "announcement_data" => "required|array",
            "announcement_data.*.language_type" => "required|string|in:".implode(',',array_keys(config('language'))),
            'announcement_data.*.author' => 'nullable|string',
            "announcement_data.*.subject" => "required|string",
            "announcement_data.*.short_description" => "nullable|string|max:50",
            "announcement_data.*.description" => "required|string",

            "announcement_data.*.image_data" => "required|array",
            "announcement_data.*.image_data.*.image_name" => "required|string",
            "announcement_data.*.image_data.*.image_type" => "required|in:image/jpg,image/jpeg,image/png",

            "announcement_data.*.attachment_data" => "array",
            "announcement_data.*.attachment_data.*.attachment_name" => "required|string",
            "announcement_data.*.attachment_data.*.attachment_type" => "required|in:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,application/msword,application/vnd.ms-powerpoint,text/plain,application/pdf",

            "start_date" => "required_with:end_date|nullable|string|date_format:Y-m-d|after_or_equal:today",
            "end_date" => "required_with:start_date|nullable|string|date_format:Y-m-d|after_or_equal:start_date",
            "status" => "string|in:".implode(",",array_keys(Models\Announcement::$status))
        ],[
            'announcement_data.*.language_type.required' => Lang::get('lang.input-field-required-error'),
            'announcement_data.*.subject.required' => Lang::get('lang.input-field-required-error'),
            'announcement_data.*.description.required' => Lang::get('lang.input-field-required-error'),
            'announcement_data.*.image_data.*.image_name.required' => Lang::get('lang.input-field-required-error'),
            'announcement_data.*.image_data.*.image_type.required' => '',
            'start_date.required_with' => Lang::get('lang.input-field-required-error'),
            'end_date.required_with' => Lang::get('lang.input-field-required-error'),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }
        $res = Models\Announcement::addAnnouncement($validator->validated());

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = announcement/announcement-list
     * @module = admin
     * @path = announcement/announcement-list
     * @permissionName = Announcement
     * @menuType = sub_menu
     * @parent = Document
     * @method = POST
     * @description = To get announcement list.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = posted_date|<posted_date>|string|optional|2022-08-28|Posted Date filter.
     * @body = subject|<subject>|string|optional|Title keyword|News title keyword filter.
     * @body = author|<author>|string|optional|Author|Author filter.
     * @body = status|<status>|string|optional|active / inactive|Status filter. Dropdown: announcement_status.

     * @response = {"list":[{"id":1,"author":"asd","subject":"TEST Announcement 123","short_description":"asd","image_data":[{"image_name":"image1.jpg","image_type":"image\/jpg","image_name_display":"image1.jpg"}],"start_date":"28\/04\/2023","end_date":null,"created_at":"28\/04\/2023 17:32:01","status":"active","status_display":"Active","updated_by":"maskman","updated_at":"28\/04\/2023 17:32:18"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null,"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL2Fubm91bmNlbWVudC9hbm5vdW5jZW1lbnQtbGlzdCIsImlhdCI6MTY4MjY3MjY3MiwiZXhwIjoxNjgyNjc4NDI0LCJuYmYiOjE2ODI2NzQ4MjQsImp0aSI6IjBlY3BFTm5udTIzRlFKMGUiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.7aeiZ_MJ4dmXcLoFMFDHSQ4QKpVTEHxsJr41O1Sb2yM","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.17068481445312 sec","log_id":"1845d98a-f753-4848-a414-c124ab87e67a"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = announcement/announcement-list
     * @module = user,app
     * @path = announcement/announcement-list
     * @method = POST
     * @description = To get announcement list.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = posted_date|<posted_date>|string|optional|2022-08-28|Create posted date filter.
     * @body = subject|<subject>|string|optional|Title keyword|News title keyword filter.
     * @body = author|<author>|string|optional|Cino|Author filter.
     * @body = status|<status>|integer|optional|Active|Status filter. (dropdown: announcement_status)

     * @response = {"list":[{"id":1,"author":"asd","subject":"TEST Announcement 123","short_description":"asd","image_data":[{"image_name":"image1.jpg","image_type":"image\/jpg","image_name_display":"image1.jpg"}],"start_date":"28\/04\/2023","end_date":null}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null,"trending":[{"id":4,"author":"asd","subject":"TEST Announcement 2","short_description":"asd","image_data":[{"image_name":"image1.jpg","image_type":"image\/jpg","image_name_display":"image1.jpg"}],"start_date":"28\/04\/2023","end_date":null},{"id":3,"author":"asd","subject":"TEST Announcement 2","short_description":"asd","image_data":[{"image_name":"image1.jpg","image_type":"image\/jpg","image_name_display":"image1.jpg"}],"start_date":"28\/04\/2023","end_date":null},{"id":2,"author":"asd","subject":"TEST Announcement 2","short_description":"asd","image_data":[{"image_name":"image1.jpg","image_type":"image\/jpg","image_name_display":"image1.jpg"}],"start_date":"28\/04\/2023","end_date":null}],"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLXVzZXIudGtzaC5jb20vYW5ub3VuY2VtZW50L2Fubm91bmNlbWVudC1saXN0IiwiaWF0IjoxNjgyNjc1NTIwLCJleHAiOjE2ODI2ODAwMDEsIm5iZiI6MTY4MjY3NjQwMSwianRpIjoiblZKVENHN2lvdEV6ZFI5VCIsInN1YiI6IjEwMDAwMDAiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.0IkVO-20MFhkliMKHedwQais_tuM3zhyFlPdmL-1o0M","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.17466306686401 sec","log_id":"800f8200-8203-4cf1-9dae-c97c6e1ec130"}
     * ##docs end##
     */
    public function getAnnouncementList(Request $request){

        $validator = Validator::make($request->all(), [
            "limit" => "integer",
            "page" => "integer",
            "from_date" => "required_with:to_date|string|date_format:Y-m-d",
            "to_date" => "required_with:from_date|string|date_format:Y-m-d|after_or_equal:from_date",
            'posted_from_date' => 'required_with:posted_to_date|string|date_format:Y-m-d',
            'posted_to_date' => 'required_with:posted_from_date|string|date_format:Y-m-d|after_or_equal:posted_from_date',
            'subject' => 'string',
            'author' => 'string',
            'skip_trending' => 'string',
            'status' => 'integer|in:'.implode(',',array_values(Models\Announcement::$status)),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\Announcement::getAnnouncementList($validator->validated());

        abort(200,json_encode($res));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = announcement/announcement-detail
     * @module = admin
     * @path = announcement/announcement-detail
     * @permissionName = Announcement Detail
     * @menuType = api
     * @parent = Announcement
     * @method = POST
     * @description = To get announcement detail.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|required|1|Announcement's id.

     * @response = {"data":{"id":2,"start_date":"27\/03\/2023","end_date":"12\/12\/2023","status":"active","status_display":"Active","announcement_data":[{"author":"samchang1","subject":"wahaha1","short_description":"wow let smile together2","description":"12345","language_type":"en","image_data":[{"image_name":"image_3.jpg","image_type":"image\/jpg","image_name_display":"image_3.jpg"}],"attachment_data":[],"language_type_display":"English"},{"author":"samchang","subject":"\u592a\u597d\u7b11\u4e86","short_description":"\u770b\u83ef\u8a9e\u768450\u500b\u5b57\u9650\u5236\u6703\u4e0d\u6703\u89f8\u6cd5\u9084\u662f\u76f4\u63a5\u7121\u8996\u6389\u7562\u7adf\u83ef\u8a9e\u7684\u5f88\u9ebb\u7169\u54c8\u54c8\u54c8\u54c8\u54c8\u54c8\u54c8\u54c8\u54c8\u54c8\u54c8\u54c8\u54c8\u54c8\u54c8\u54c8\u54c8\u54c8\u54c8\u54c8","description":"123","language_type":"cn","image_data":[{"image_name":"image_2.jpg","image_type":"image\/jpg","image_name_display":"image_2.jpg"}],"attachment_data":[],"language_type_display":"Chinese"}]},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLXN0YWZmLnB1LWhvc3BpdGFsLmNvbS9hbm5vdW5jZW1lbnQvYW5ub3VuY2VtZW50LWRldGFpbCIsImlhdCI6MTY3OTkwMDgzMSwiZXhwIjoxNjc5OTA0NDQzLCJuYmYiOjE2Nzk5MDA4NDMsImp0aSI6IjhTdExCdEVCRzkwRWRPNTkiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.Na4x9rQ_6BDBABLbsZB3BN9OgcOhhX0bN_wYHOfWRfI","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.070008993148804 sec","log_id":"26b45cd4-6cdb-4896-a732-96bdf48c2b80"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = announcement/announcement-detail
     * @module = user,app
     * @path = announcement/announcement-detail
     * @method = POST
     * @description = To get announcement detail.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|optional|1|Announcement's id.

     * @response = {"data":{"author":"asd","subject":"TEST Announcement 123","short_description":"asd","description":"TEST Description","language_type":"en","image_data":[{"image_name":"image1.jpg","image_type":"image\/jpg","image_name_display":"image1.jpg"}],"attachment_data":[{"attachment_name":"test.pdf","attachment_type":"application\/pdf","attachment_name_display":"test.pdf"}],"language_type_display":"English","start_date":"28\/04\/2023","end_date":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLXVzZXIudGtzaC5jb20vYW5ub3VuY2VtZW50L2Fubm91bmNlbWVudC1kZXRhaWwiLCJpYXQiOjE2ODI2NzU1MjAsImV4cCI6MTY4MjY4MDA0NiwibmJmIjoxNjgyNjc2NDQ2LCJqdGkiOiI5eWNFSXg4UWxQM3R2MUgzIiwic3ViIjoiMTAwMDAwMCIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.WTd8bLm1JFzRGQKQXHyxcpQdxoEAbP1FsyfCXAAsr6E","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.15285301208496 sec","log_id":"2b5a6279-39ae-4a74-8eae-28835763142b"}
     * ##docs end##
     */
    public function getAnnouncementDetail(Request $request){

        $validator = Validator::make($request->all(), [
            "id" => "nullable|integer|exists:announcement,id",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = ["data" => null];
        if(isset($validator->validated()['id'])) $res = Models\Announcement::getAnnouncementDetail($validator->validated());

        abort(200,json_encode($res));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = announcement/edit-announcement
     * @module = admin
     * @path = announcement/edit-announcement
     * @permissionName = Edit Announcement
     * @menuType = api
     * @parent = Announcement
     * @method = POST
     * @description = To edit Announcement.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|required|1|Announcement's id.
     * @body = announcement_data|<array>|array|required|[{"subject":"TEST Memo 1","language_type":"en","description":"TEST Description","upload":[{"upload_name":"image1.jpg","upload_type":"image\/jpg"}]}]|Announcement's Data. 
     * @body = announcement_data.*.author|<announcement_data.*.author>|string|optional|CS|Announcement's author.
     * @body = announcement_data.*.subject|<subject>|string|required|test subject|Announcement's Subject.
     * @body = announcement_data.*.short_description|<short_description>|string|optional|test short description|Announcement's Short Description.
     * @body = announcement_data.*.description|<description>|string|required|test description|Announcement's Description.
     * @body = announcement_data.*.language_type|<language_type>|string|required|en, cn, my|Announcement's Language.
     * @body = announcement_data.*.image_data|<array>|array|required|[{'image_name' : 'test/2022/09/1663906995_0715_test.png'},{'image_type' : 'image/png'}]|Announcement's Image.
     * @body = announcement_data.*.image_data.*.image_name|<image_name>|string|required|test/2022/09/1663906995_0715_test.png|Announcement's Image (Public Type).
     * @body = announcement_data.*.image_data.*.image_type|<image_type>|string|required|image/jpg, image/jpeg, image/png|Announcement's Image type.
     * @body = announcement_data.*.attachment_data|<array>|array|optional|[{'attachment_name' : 'test/2022/09/1663906995_0715_test.png'},{'attachment_type' : 'image/png'}]|Announcement's Image.
     * @body = announcement_data.*.attachment_data.*.attachment_name|<attachment_name>|string|optional|test/2022/09/1663906995_0715_test.png|Announcement's Image (Public Type).
     * @body = announcement_data.*.attachment_data.*.attachment_type|<attachment_type>|string|optional|image/jpg, image/jpeg, image/png|Announcement's Image type.
     * @body = start_date|<start_date>|string|required|2022-08-28|Announcement's start date.
     * @body = end_date|<end_date>|string|optional|2022-08-28|Announcement's end date.
     * @body = status|<status>|string|required|active / inactive|Announcement's status. (dropdown: announcement_status)

     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20vYW5ub3VuY2VtZW50L2VkaXQtYW5ub3VuY2VtZW50IiwiaWF0IjoxNjY0MjQwMjk4LCJleHAiOjE2NjQyNDUyODEsIm5iZiI6MTY2NDI0MTY4MSwianRpIjoielhheTZ5Uk9SQzl2ejJEWCIsInN1YiI6IjIiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.ZUss2PkgpKosmvQggpSIXhtnsh2Ictq82iX-t5Dm43c","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.0063800811767578 sec","log_id":"82e3ff72-9ac0-4574-8e17-8c0939208579"}
     * ##docs end##
     */
    public function editAnnouncement(Request $request){
        $validator = Validator::make($request->all(), [
            "id" => "required|integer|exists:announcement,id",
            "announcement_data" => "required|array",
            "announcement_data.*.language_type" => "required|string|in:".implode(',',array_keys(config('language'))),
            'announcement_data.*.author' => 'nullable|string',
            "announcement_data.*.subject" => "required|string",
            "announcement_data.*.short_description" => "nullable|string|max:50",
            "announcement_data.*.description" => "required|string",

            "announcement_data.*.image_data" => "required|array",
            "announcement_data.*.image_data.*.image_name" => "required|string",
            "announcement_data.*.image_data.*.image_type" => "required|in:image/jpg,image/jpeg,image/png",

            "announcement_data.*.attachment_data" => "array",
            "announcement_data.*.attachment_data.*.attachment_name" => "required_with:announcement_data.*.attachment_data|string",
            "announcement_data.*.attachment_data.*.attachment_type" => "required_with:announcement_data.*.attachment_data|in:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,application/msword,application/vnd.ms-powerpoint,text/plain,application/pdf",

            "start_date" => "required_with:end_date|nullable|string|date_format:Y-m-d",
            "end_date" => "required_with:start_date|nullable|string|date_format:Y-m-d|after_or_equal:start_date",
            "status" => "required|string|in:".implode(",",array_keys(Models\Announcement::$status))
            
        ],[
            'id.required' => Lang::get('lang.input-field-required-error'),
            'announcement_data.*.language_type.required' => Lang::get('lang.input-field-required-error'),
            'announcement_data.*.subject.required' => Lang::get('lang.input-field-required-error'),
            'announcement_data.*.description.required' => Lang::get('lang.input-field-required-error'),
            'announcement_data.*.image_data.*.image_name.required' => Lang::get('lang.input-field-required-error'),
            'announcement_data.*.image_data.*.image_type.required' => '',
            'start_date.string' => Lang::get('lang.input-field-required-error'),
            'end_date.string' => Lang::get('lang.input-field-required-error'),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\Announcement::editAnnouncement($validator->validated());

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Delete Announcement
     * @module = admin
     * @path = announcement/delete-announcement
     * @method = POST
     * @description = To delete announcement.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = id|<id>|integer|required|1|Announcement's id.
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmNxcS5jb20vbWVtby9kZWxldGUtbWVtbyIsImlhdCI6MTY3MjM4MjgyMCwiZXhwIjoxNjcyMzg5MzY2LCJuYmYiOjE2NzIzODU3NjYsImp0aSI6InZxRWw5blRXWHhzdmtkN0wiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.xNoEdJMlRTRWiwnbCTBgTjhjF-Ku_6MSQjEXerDpQ5I","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.075712919235229 sec","log_id":"727e7bff-a16f-482e-88c1-b78b7cce2c6d"}
     * ##docs end##
     */
    public function deleteAnnouncement(Request $request){

        $validator = Validator::make($request->all(), [
            "id" => "required|integer|exists:announcement,id,deleted_at,NULL,status," . Models\Announcement::$status['inactive'],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\Announcement::deleteAnnouncement($validator->validated());

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = annonucement/get-news
     * @module = app
     * @path = announcement/get-news
     * @method = POST
     * @description = Get News.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @response = {"data":[{"news_url":"https:\/\/www.fxstreet.com\/news\/usd-cad-bears-attack-13300-as-oil-grinds-higher-us-dollar-dribbles-ahead-of-fed-verdict-202306140358","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/n\/h\/f24-143968.jpg","title":"USD\/CAD bears attack 1.3300 as Oil grinds higher, US Dollar dribbles ahead of Fed verdict","text":"USD\/CAD sellers occupy the driver's seat, despite the sluggish US Dollar, as markets brace for the Federal Reserve Interest Rate Decision on early Wednesday. In doing so, the Loonie pair cheers recovery in Canada's main export item, namely WTI crude oil, as well as hopes of witnessing no rate hike from the US central bank, while printing mild losses near 1.3300 at the latest.","source_name":"FX Street","date":"Tue, 13 Jun 2023 23:58:46 -0400","topics":["Oil","USA","Canada"],"sentiment":"Negative","type":"Article","currency":["USD-CAD"]},{"news_url":"https:\/\/www.fxstreet.com\/news\/usd-jpy-price-analysis-previous-resistance-line-feds-hawkish-halt-keeps-yen-bears-hopeful-past-13990-************","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/r\/u\/f21-143966.jpg","title":"USD\/JPY Price Analysis: Previous resistance line, Fed's hawkish halt keeps Yen bears hopeful past 139.90","text":"USD\/JPY bears struggle to keep the reins amid early Wednesday in Europe, despite snapping a three-day uptrend by retreating from a one-week high to around 140.00 at the latest. In doing so, the Yen pair portrays the market's cautious mood ahead of the all-important Federal Open Market Committee (FOMC) monetary policy meeting by poking the resistance-turned-support line stretched from early May.","source_name":"FX Street","date":"Tue, 13 Jun 2023 23:40:47 -0400","topics":["Japan","USA"],"sentiment":"Positive","type":"Article","currency":["USD-JPY"]},{"news_url":"https:\/\/www.fxempire.com\/forecasts\/article\/eur-usd-and-a-run-at-1-09-hinged-on-a-dovish-fed-pause-1354238","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/y\/k\/euro-999-2-143965.jpg","title":"EUR\/USD and a Run at $1.09 Hinged on a Dovish Fed Pause","text":"It is a busy day ahead for the EUR\/USD. While euro area stats will influence, the focus will be on the Fed as investors bet on a July interest rate hike.","source_name":"FXEmpire","date":"Tue, 13 Jun 2023 23:35:08 -0400","topics":["USA","Europe"],"sentiment":"Negative","type":"Article","currency":["EUR-USD"]},{"news_url":"https:\/\/www.fxstreet.com\/news\/silver-price-analysis-xag-usd-bulls-flirt-with-200-hour-sma-remains-below-2400-mark-202306140329","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/q\/c\/f20-143964.jpg","title":"Silver Price Analysis: XAG\/USD flirts with 200-hour SMA support-turned-resistance","text":"Silver regains positive traction during the Asian session on Wednesday, snapping a two-day losing streak and stalling the overnight sharp retracement slide from the vicinity of mid-$24.00s. The white metal is currently placed near the top end of its intraday trading range, around the $23.80 region, though seems to struggle to capitalize on the strength beyond the 200-hour Simple Moving Average (SMA).","source_name":"FX Street","date":"Tue, 13 Jun 2023 23:32:30 -0400","topics":["USA","Silver"],"sentiment":"Neutral","type":"Article","currency":["XAG-USD"]},{"news_url":"https:\/\/www.fxempire.com\/forecasts\/article\/usd-jpy-return-to-141-hinged-on-a-hawkish-fed-pause-1354231","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/w\/g\/yen-1-1-143961.jpg","title":"USD\/JPY Return to 141 Hinged on a Hawkish Fed Pause","text":"The USD\/JPY saw red this morning as investors responded to the US CPI Report. The bets are on a Fed pause.","source_name":"FXEmpire","date":"Tue, 13 Jun 2023 23:12:44 -0400","topics":["Japan","USA"],"sentiment":"Positive","type":"Article","currency":["USD-JPY"]},{"news_url":"https:\/\/www.dailyfx.com\/news\/cooling-us-inflation-provides-room-for-fed-s-wait-and-see-silver-hang-seng-index-gbp-usd-20230614.html","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/t\/w\/f19-143963.jpg","title":"Cooling US inflation provides room for Fed's wait-and-see: Silver, Hang Seng Index, GBP\/USD","text":"Further cooling in US inflation reflects some degree of success in policy moves thus far and provides room for the Fed to put on some wait-and-see at the upcoming FOMC meeting.","source_name":"DailyFX","date":"Tue, 13 Jun 2023 23:00:00 -0400","topics":["USA","Silver","United Kingdom"],"sentiment":"Neutral","type":"Article","currency":["GBP-USD","XAG-USD"]},{"news_url":"https:\/\/www.fxstreet.com\/news\/usd-cnh-price-analysis-yuan-bears-take-a-breather-at-65-month-low-within-channel-pattern-fed-eyed-202306140228","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/k\/h\/f12-143956.jpg","title":"USD\/CNH Price Analysis: Yuan bears take a breather at 6.5-month low within channel pattern, Fed eyed","text":"USD\/CNH struggles to extend a three-day uptrend as it retreats from the highest levels in nearly eight months on the key Federal Open Market Committee (FOMC) monetary policy meeting day. That said, the offshore Chinese Yuan (CNH) pair prints mild losses near 7.1720 by the press time, after rising to the highest levels since April 2022 with the latest peak of 7.1788.","source_name":"FX Street","date":"Tue, 13 Jun 2023 22:31:24 -0400","topics":["China","USA"],"sentiment":"Neutral","type":"Article","currency":["USD-CNH"]},{"news_url":"https:\/\/www.fxstreet.com\/news\/gbp-jpy-trades-just-below-its-highest-level-since-december-2015-ahead-of-uk-macro-data-202306140226","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/2\/5\/f13-143957.jpg","title":"GBP\/JPY trades just below its highest level since December 2015 ahead of UK macro data","text":"The GBP\/JPY cross eases from its highest level since December 2015 touched on Wednesday and trades with a mild negative bias through the first half of the Asian session, though lacks follow-through. The cross currently trades around the 176.65-176.70 area and seems poised to prolong its recent upward trajectory witnessed over the past month or so.","source_name":"FX Street","date":"Tue, 13 Jun 2023 22:27:33 -0400","topics":["Japan","United Kingdom"],"sentiment":"Neutral","type":"Article","currency":["GBP-JPY"]},{"news_url":"https:\/\/www.fxstreet.com\/news\/usd-chf-remains-on-the-defensive-below-mid-09000s-focus-remains-glued-to-fed-decision-202306140149","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/d\/m\/f10-143954.jpg","title":"USD\/CHF remains on the defensive below mid-0.9000s, Fed decision awaited","text":"The USD\/CHF pair lacks any firm intraday directional bias on Wednesday and oscillates in a narrow trading band, just below mid-0.9000s through the Asian session.","source_name":"FX Street","date":"Tue, 13 Jun 2023 21:51:20 -0400","topics":["USA","Switzerland"],"sentiment":"Negative","type":"Article","currency":["USD-CHF"]},{"news_url":"https:\/\/www.fxstreet.com\/news\/gold-price-forecast-xau-usd-************","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/s\/z\/f9-143953.jpg","title":"Gold Price Forecast: XAU\/USD bulls and bears go head to head in the build up to the Fed","text":"Gold price is firm in Tokyo as the focus stays on the Federal Reserve later today and following the US inflation report that gave something for both the bears and bulls. At the time of writing, XAU\/USD is trading higher by some 0.13% and has risen from a low of $1,942.31 to reach a high of $1,946.89 so far.","source_name":"FX Street","date":"Tue, 13 Jun 2023 21:47:23 -0400","topics":["Gold","USA"],"sentiment":"Neutral","type":"Article","currency":["XAU-USD"]},{"news_url":"https:\/\/www.fxstreet.com\/news\/eur-usd-price-analysis-200-ema-caps-immediate-upside-as-fed-interest-rate-decision-looms-202306140140","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/7\/n\/f8-143952.jpg","title":"EUR\/USD Price Analysis: 200-EMA caps immediate upside as Fed Interest Rate Decision looms","text":"EUR\/USD treads water around 1.0800 as it struggles to extend the two-day winning streak near the highest levels since late May amid early Wednesday. In doing so, the Euro pair aptly portrays the market's cautious mood ahead of the Federal Open Market Committee (FOMC) monetary policy meeting.","source_name":"FX Street","date":"Tue, 13 Jun 2023 21:41:21 -0400","topics":["USA","Europe"],"sentiment":"Neutral","type":"Article","currency":["EUR-USD"]},{"news_url":"https:\/\/www.fxstreet.com\/news\/pound-sterling-price-news-and-forecast-gbp-usd-seesaws-around-the-highest-levels-in-over-a-month-202306140125","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/o\/p\/f6-143950.jpg","title":"Pound Sterling Price News and Forecast: GBP\/USD seesaws around the highest levels in over a month","text":"GBP\/USD aptly portrays the Fed day anxiety as it makes rounds to 1.2610 during early Wednesday. In doing so, the Cable pair fades upside momentum as the traders recheck the hawkish signals flashes via the US inflation and the UK employment data the previous day.","source_name":"FX Street","date":"Tue, 13 Jun 2023 21:25:18 -0400","topics":["USA","United Kingdom"],"sentiment":"Positive","type":"Article","currency":["GBP-USD"]},{"news_url":"https:\/\/www.fxstreet.com\/news\/gbp-usd-grinds-below-12625-hurdle-ahead-of-uk-data-dump-fomc-202306140121","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/4\/b\/f5-143949.jpg","title":"GBP\/USD grinds below 1.2625 hurdle ahead of UK data dump, FOMC","text":"GBP\/USD aptly portrays the Fed day anxiety as it makes rounds to 1.2610 during early Wednesday. In doing so, the Cable pair fades upside momentum as the traders recheck the hawkish signals flashes via the US inflation and the UK employment data the previous day.","source_name":"FX Street","date":"Tue, 13 Jun 2023 21:21:53 -0400","topics":["USA","United Kingdom"],"sentiment":"Negative","type":"Article","currency":["GBP-USD"]},{"news_url":"https:\/\/www.fxstreet.com\/news\/usd-cny-fix-************","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/o\/6\/f3-143947.jpg","title":"USD\/CNY fix: 7.1566 vs. closing price of 7.1659","text":"In recent trade today, the People's Bank of China (PBOC) set the yuan at  7.1566 vs. the estimate at 7.1550 and the closing price of 7.1659.","source_name":"FX Street","date":"Tue, 13 Jun 2023 21:16:39 -0400","topics":["USA"],"sentiment":"Negative","type":"Article","currency":["USD-CNY"]},{"news_url":"https:\/\/www.fxstreet.com\/news\/the-usd-cad-pair-struggles-to-capitalize-on-the-ov-************","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/b\/h\/f4-143948.jpg","title":"USD\/CAD consolidates in a range above 1.3300 as traders keenly await FOMC decision","text":"The USD\/CAD pair struggles to capitalize on the overnight late rebound from the 1.3285 region, or a three-month low and remains on the defensive through the Asian session on Wednesday. Spot prices, however, manage to hold above the 1.3300 mark as traders keenly await the outcome of the high-anticipated FOMC monetary policy meeting before placing fresh directional bets.","source_name":"FX Street","date":"Tue, 13 Jun 2023 21:15:07 -0400","topics":["USA","Canada"],"sentiment":"Neutral","type":"Article","currency":["USD-CAD"]},{"news_url":"https:\/\/www.fxstreet.com\/news\/aud-usd-price-analysis-aussie-bulls-occupy-drivers-seat-above-06730-support-fed-eyed-202306140103","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/q\/j\/f2-143946.jpg","title":"AUD\/USD Price Analysis: Aussie bulls occupy driver's seat above 0.6730 support, Fed eyed","text":"AUD\/USD struggles to extend the latest upside momentum despite posting mild gains near 0.6770 amid the mid-Asian session on Wednesday.","source_name":"FX Street","date":"Tue, 13 Jun 2023 21:05:29 -0400","topics":["Australia","USA"],"sentiment":"Positive","type":"Article","currency":["AUD-USD"]},{"news_url":"https:\/\/www.dailyfx.com\/news\/gold-price-peels-lower-despite-soft-us-dollar-ahead-of-the-fed-lower-xau-usd-20230614.html","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/m\/e\/f7-143951.jpg","title":"Gold Price Peels Lower Despite Soft US Dollar Ahead of the Fed. Lower XAU\/USD?","text":"The gold price is struggling to gain ground in the aftermath of US CPI data that dented the US Dollar. If the Federal Reserve keeps its target rate the same, will it help or hinder gold?","source_name":"DailyFX","date":"Tue, 13 Jun 2023 21:00:00 -0400","topics":["Gold","USA"],"sentiment":"Negative","type":"Article","currency":["XAU-USD"]},{"news_url":"https:\/\/www.fxstreet.com\/news\/the-usd-jpy-pair-is-seen-oscillating-in-a-narrow-t-202306140045","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/v\/v\/f36-143940.jpg","title":"USD\/JPY trades with mild negative bias around 140.00, downside seems limited ahead of Fed","text":"The USD\/JPY pair is seen oscillating in a narrow trading band during the Asian session on Wednesday and consolidating its gains registered over the past three days. The pair is currently placed just above the 140.00 psychological mark as traders now seem to have moved to the sidelines and keenly await the outcome of the highly-anticipated FOMC policy meeting.","source_name":"FX Street","date":"Tue, 13 Jun 2023 20:45:39 -0400","topics":["Japan","USA"],"sentiment":"Negative","type":"Article","currency":["USD-JPY"]},{"news_url":"https:\/\/www.fxstreet.com\/news\/nzd-usd-the-kiwi-is-up-a-touch-this-morning-and-202306140032","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/z\/h\/f37-143941.jpg","title":"NZD\/USD bulls step in ahead of the Fed","text":"NZD\/USD is 0.17% higher at the time of writing after moving up from a low of 0.6144 to a fresh high of 0.6161 in Tokyo trade. The focus is on the Federal Reserve today following Tuesday's inflation report that points to sticky core inflation.","source_name":"FX Street","date":"Tue, 13 Jun 2023 20:43:21 -0400","topics":["USA","New Zealand"],"sentiment":"Neutral","type":"Article","currency":["NZD-USD"]},{"news_url":"https:\/\/www.fxstreet.com\/news\/usd-mxn-price-news-options-market-signals-prod-mexican-peso-buyers-at-seven-year-top-202306140003","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/y\/o\/f40-143944.jpg","title":"USD\/MXN Price News: Options market signals prod Mexican Peso buyers at seven-year top","text":"USD\/MXN portrays a corrective bounce from the lowest levels since May 2016 as it prints mild gains around 17.25 amid early hours of Wednesday's trading.","source_name":"FX Street","date":"Tue, 13 Jun 2023 20:04:02 -0400","topics":["USA","Mexico"],"sentiment":"Positive","type":"Article","currency":["USD-MXN"]}],"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLXVzZXIudGtzaC5jb20vYW5ub3VuY2VtZW50L2dldC1uZXdzIiwiaWF0IjoxNjg2NzIwMTM4LCJleHAiOjE2ODY3Mjc5MzQsIm5iZiI6MTY4NjcyNDMzNCwianRpIjoiQVlHYjdPRDNuUVZvNjBQVCIsInN1YiI6IjEwMDAwMDAiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.zQ_3r6oR8XK--OzOhSSEOV_iTmAaTov9nO9_-zM93io","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.11027908325195 sec","log_id":"76248c44-1276-4c02-bc1a-8b74ad43366c"}
     * ##docs end##
     */
    public function getNews(Request $request)
    {
        $validator = Validator::make($request->all(), [
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Announcement::getNews($validator->validated());

        abort(200, json_encode(["data" => $res]));
    }
}
