<?php

namespace App\Http\Controllers;

use App\Models\GameCategory;
use App\Models\GameCategoryProvider;
use App\Models\Services;
use App\Models\Store;
use App\Models\UserProduct;
use App\Services\GameLog\WWJGameLogService;
use App\Services\GameProvider\JK;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;

class GameController extends Controller
{
    public function getDashboardGameList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'nullable|exists:game_categories,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $hot_games = null;
        $special_games = null;

        if (!isset($request->id)) {
            $hot_games = [
                'id' => 0,
                'name' => 'Hot Games',
                'display' => 'Hot Games',
                'icon' => 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/games/game_type/hot_game.gif',
                'is_game_list' => true,
                'list' => Services::getHotServices(),
            ];

            $randvideo = rand(1, 7);
            $special_games = [
                'name' => '  Aljin  ',
                'display' => '  Aljin  ',
                'icon' => 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/games/game_type/aljin.png',
                'is_game_list' => false,
                'info' => [
                    'video' => 'https://pub-fe3dae3bdb1c4d3c858a4557ea6443c7.r2.dev/AljinPromo/' . $randvideo . '.mp4',
                    'image' => 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/2024/12/1735286891_4870.png',
                    'bg_image' => 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/2024/12/image_95.png',
                    'service_id' => env('APP_ENV') == 'production' ? 1342 : 1202,
                    'product_id' => 2004,
                ],
                'list' => []
            ];
        }

        $categories = GameCategory::with([
            'game_category_providers' => function ($q) use ($request) {
                $q->where('game_category_id', $request->id)
                    ->where('status', 1);
            },
            'game_category_providers.game_provider',
            'game_category_providers.services' => function ($q) {
                $q->where('status', 1);
            }
        ])
            ->where('status', true)
            ->orderBy('priority', 'asc')
            ->when(isset($request->id), function ($q) use ($request) {
                return $q->where('id', $request->id);
            })
            ->get()
            ->map(function ($q) {
                return [
                    'id' => $q->id,
                    'name' => $q->name,
                    'disply' => $q->name,
                    'icon' => isset($q->icon) ? $q->icon : null,
                    'is_game_list' => false,
                    'list' => $q->game_category_providers->map(function ($q) {
                        return [
                            'id' => $q->id,
                            "icon" => isset($q->game_provider->icon) ? $q->game_provider->icon : env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $q->icon,
                            'name' => $q->game_provider->name,
                            'is_game' => $q->is_game,
                            'list' => $q->services->map(function ($q) {
                                $status = array_search($q->status, Services::$status) ?? null;

                                return [
                                    "service_id" => $q->id,
                                    "name" => $q->name,
                                    "display" => Lang::has('langCustom.service-' . $q->name) ? Lang::get('langCustom.service-' . $q->name) : $q->name,
                                    "url" => $q->url,
                                    "icon" => isset($q->icon) ? ((filter_var($q->icon, FILTER_VALIDATE_URL) || substr($q->icon, 0, 5) == 'https') ? str_replace(' ', '%20', $q->icon) : env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $q->icon) : null,
                                    'status' => $status,
                                    'isTK8' => 0,
                                    'showCashInOutWarning' => 0,
                                    'product_id' => (int)$q->product_id,
                                    'product_name' => $q->product->name ?? null,
                                    'filter_type' => $q->filter_type,
                                    'is_deeplink' => $q->is_deeplink,
                                    'is_game' => 1,
                                ];
                            })
                        ];
                    })
                ];
            });

        $others = !isset($request->id) ? [$hot_games, $special_games] : [];

        if (!isset($request->id)) {
            $data['data'] = [
                'list' => array_merge($others, $categories->toArray()),
            ];
        } else {
            $data['data'] = [
                'list' => $categories[0]['list']
            ];
        }

        abort(200, json_encode($data));
    }

    public function getGameListByCategory(Request $request)
    {
        $hot_games = [
            'id' => 0,
            'name' => 'Hot Games',
            'display' => 'Hot Games',
            'is_game_list' => true,
            'list' => Services::getHotServices()
        ];

        $game_list = GameCategory::getListByCategory();

        $data['data']['game_list'] = array_values(array_merge([$hot_games], $game_list->toArray() ?? []));

        abort(200, json_encode($data));
    }

    public function getGameListbyCategoryId(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:game_categories,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $storeIsKilled = Store::where('store_id', auth()->user()->store_id)->where('is_disable', '0')->get(); // Compare user store id if disable will hide game

        $list = $storeIsKilled->isEmpty() ? [] : GameCategoryProvider::with([
            'services' => function ($query) {
                $query->whereIn('status', Arr::except(Services::$status, ['inactive']));
                $query->orderBy('priority', 'ASC');
            },
            'services.product',
            'game_provider'
        ])
            ->where('game_category_id', $request->id)
            ->orderBy('priority', 'ASC')
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'icon' => $item->game_provider->icon,
                    'category_id' => $item->game_category_id,
                    'provider_id' => $item->game_provider_id,
                    'service_list' => $item->services->map(function ($q) {
                        $status = array_search($q->status, Services::$status) ?? null;

                        return [
                            "service_id" => $q->id,
                            "name" => $q->name,
                            "display" => Lang::has('langCustom.service-' . $q->name) ? Lang::get('langCustom.service-' . $q->name) : $q->name,
                            "url" => $q->url,
                            "icon" => isset($q->icon) ? ((filter_var($q->icon, FILTER_VALIDATE_URL) || substr($q->icon, 0, 5) == 'https') ? str_replace(' ', '%20', $q->icon) : env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $q->icon) : null,
                            'status' => $status,
                            'isTK8' => 0,
                            'showCashInOutWarning' => 0,
                            'product_id' => (int)$q->product_id,
                            'product_name' => $q->product->name ?? null,
                            'filter_type' => $q->filter_type,
                            'is_deeplink' => $q->is_deeplink,
                            'is_game' => 1,
                        ];
                    })
                ];
            });

        $filters = [];
        foreach (Services::$filterType as $key => $value) {
            $filters[$key] = [
                'name' => Lang::has('lang.' . $key) ? Lang::get('lang.' . $key) : $key,
                'value' => $value,
            ];
        }

        $data['data'] = [
            'filters' => $filters,
            'list' => $list
        ];
        abort(200, json_encode($data));
    }

    public function getBetTransaction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "page" => "integer",
            "limit" => "integer",
            "username" => "string",
            "account" => "string",
            "phone_no" => "string",
            "from_date" => "nullable|date|date_format:Y-m-d H:i:s",
            "to_date" => "nullable|date|date_format:Y-m-d H:i:s|after_or_equal:from_date",
            "see_all" => "integer|in:1,0",
            "export" => "in:1,0|nullable",
            "export_data" => "required_if:export,==,1",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        // $data = $this->getBetTransactionWWJ($validator->validated());
        $data = (new WWJGameLogService())->getBetTransaction($validator->validated());

        abort(200, json_encode($data));
    }

    public function getAccountBetTransaction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "store_id" => "integer",
            "page" => "integer",
            "limit" => "integer",
            "account" => "string",
            "from_date" => "nullable|date|date_format:Y-m-d H:i:s",
            "to_date" => "nullable|date|date_format:Y-m-d H:i:s|after_or_equal:from_date",
            "see_all" => "integer|in:1,0",
            "export" => "in:1,0|nullable",
            "export_data" => "required_if:export,==,1",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data = (new WWJGameLogService())->getAccountBetTransaction($validator->validated());

        abort(200, json_encode($data));
    }

    public function getBetTransactionWWJ($params = [])
    {
        if (!isset($params['from_date']) || empty($params['from_date'])) {
            $fromDate = date('Y-m-d\T00:00:00.000\Z');
        } else {
            $fromDate = date('Y-m-d\TH:i:s.000\Z', strtotime($params['from_date']));
        }

        if (!isset($params['to_date']) || empty($validator['to_date'])) {
            $toDate = date('Y-m-d\T23:59:59.999\Z');
        } else {
            $toDate = date('Y-m-d\TH:i:s.999\Z', strtotime($params['to_date']));
        }

        $store = Store::find($params['store_id']) ?? null;
        if (empty($store)) abort(400, 'Store ID Not Found');

        $limit = config('app.pagination_rows');
        $list = [];
        $hasNext = false;
        $page = $params['page'] ?? 1;

        if (isset($params['export']) && ($params['export'] == 1)) {
            $jobData = $params;
            $jobData["model"] = get_class() ?? null;
            $jobData["function"] = __FUNCTION__;
            $jobData["module"] = MODULE;
            $jobData["creator_type"] = MODULE;
            $jobData["creator_id"] = auth()->user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, "Export Successfully");
        }

        $jk = resolve(JK::class);
        return $result = $jk->getBetTransaction($fromDate, $toDate, $store->jk_agent_id);

        if ($result['Data']['List']) {
            $hasNext = $result['Data']['Count'] >= $limit;
            if ($hasNext) {
                array_pop($result['Data']['List']);
            }

            foreach ($result['Data']['List'] as $eachData) {
                $list[] = [
                    'sn' => $eachData['Sn'] ?? null,
                    'agent_id' => $eachData['AgentId'] ?? null,
                    'account' => $eachData['Account'] ?? null,
                    'create_time' => $eachData['CreateTime'] ?? null,
                    'turn_over' => $eachData['TurnOver'] ?? null,
                    'bet' => $eachData['Bet'] ?? null,
                    'return' => $eachData['Return'] ?? null,
                    'win_loss' => $eachData['WinLoss'] ?? null,
                    'status' => $eachData['Status'] ?? null,
                ];
            }
        }

        $itemFrom = ($page - 1) * $limit;
        $data['list'] = $list;
        $itemTo = $itemFrom + count($list);
        $data['pagination'] = array(
            "current_page" => $page,
            "from" => $itemFrom,
            "last_page" => $hasNext ? $page + 1 : $page,
            "per_page" => (int) $limit,
            "to" => $itemTo,
            "total" => $itemTo
        );

        return $data;
    }
}
