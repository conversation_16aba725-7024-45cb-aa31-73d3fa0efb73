<?php

namespace App\Http\Controllers;

use App\Models\AngpauEvent;
use App\Models\Deposit;
use App\Models\User;
use App\Models\UserAngpau;
use App\Models\UserTicketReward;
use App\Traits\DecimalTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;

class UserAngpauController extends Controller
{
    public function claimAngpau(Request $request)
    {
        $request->request->add(['user_id' => auth()->user()?->id ?? null]);

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();
        $angpauEvent = AngpauEvent::getCurrentAngpauEvent();
        if (isset($angpauEvent) && $angpauEvent['start_date'] > now()) {
            abort(400, json_encode('Angpau event is not started yet'));
        }

        if (isset($angpauEvent) && $angpauEvent['end_date'] < now()) {
            abort(400, json_encode('Angpau event is ended'));
        }

        $angpauEventId = $angpauEvent['id'];
        $amount = UserAngpau::claimAngpauByUserId($params['user_id'], $angpauEventId);

        $data = [
            'can_claim' => false,
            'inactive_image' => 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/angpau/inactive_angpau.png',
            'amount' => DecimalTrait::setDecimal($amount),
        ];

        abort(200, json_encode(['data' => $data]));
    }

    public function getLeaderboard(Request $request)
    {
        $request->request->add(['user_id' => auth()->user()?->id ?? null]);

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();

        $angpauEvent = AngpauEvent::getCurrentAngpauEvent();
        $angpauEventId = $angpauEvent['id'];

        $limit = 10;
        $angpauList = UserAngpau::getTopUserAngpauList($angpauEvent, $params['user_id'], $limit);
        $currentRank = UserAngpau::getAngpauRankByUserId($params['user_id'], $angpauEventId);

        $data = [
            'angpau_list' => $angpauList,
            'current_rank' => $currentRank,
        ];

        abort(200, json_encode(['data' => $data]));
    }

    public function getFuDaiAngPao(Request $request)
    {
        $request->request->add(['user_id' => auth()->user()?->id ?? null]);

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
        ]);

        $user = auth()->user();

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $angpauEventData = AngpauEvent::getCurrentAngpauEvent();
        if (!isset($angpauEventData)) {
            abort(400, json_encode('Event is ended'));
        }

        if (isset($angpauEventData) && $angpauEventData['start_date'] > now()) {
            abort(400, json_encode('Event is not started yet'));
        }

        $fullEvent = AngpauEvent::find($angpauEventData['id']);
        if (!$fullEvent) {
            abort(400, json_encode('Event not found'));
        }

        $angpauEventId = $angpauEventData['id'];
        $userTicketReward = UserTicketReward::where('user_id', $request->user_id)
            ->where('ref_event_id', $angpauEventId)
            ->first();
        if (!isset($userTicketReward)) {
            $userTicketReward = UserTicketReward::create([
                'ref_event_id' => $angpauEventId,
                'user_id' => $user->id,
                'store_id' => $user->store_id,
                'name' => $user->username,
                'total_token' => 0,
                'total_deposit' => 0,
                'is_dummy' => false,
                'status' => true,
            ]);
        }

        $previousAngpauEvent = AngpauEvent::getPreviousAngpauEvent();
        $previousUserAngpau = UserAngpau::where('user_id', $request->user_id)
            ->where('angpau_event_id', $previousAngpauEvent['id'])
            ->first();

        $is_win = null;
        // if (isset($previousUserAngpau) && $previousUserAngpau['end_date'] < now()) {
        //     $is_win = $previousUserAngpau?->is_selected == true ? null : (bool)$previousUserAngpau;
        // }
        if (isset($previousAngpauEvent) && $previousAngpauEvent['end_date'] <= now()) {
            $previousUserTicketReward = UserTicketReward::where('user_id', $request->user_id)
                ->where('ref_event_id', $previousAngpauEvent['id'])
                ->first();

            if (isset($previousUserTicketReward)) {
                $is_win = $previousUserTicketReward->is_read == true ? null : $previousUserTicketReward->is_selected;
            }
        }

        # Use event's max_amount if more than 5 tickets, otherwise use min_amount
        $ticketPrice = $userTicketReward->total_token >= 5 ? $fullEvent->max_amount : $fullEvent->min_amount;
        // Remove decimal part for display
        $ticketPriceDisplay = (int)$ticketPrice;

        $data = [
            'current_date' => date('Y-m-d H:i:s'),
            'event_start_date' => $angpauEventData['start_date'],
            'event_draw_date' => $angpauEventData['end_date'],
            'total_current_participants' => $angpauEventData['total_participants'],
            'current_ticket' => $userTicketReward?->total_token ?? 0,
            'min_ticket' => $angpauEventData['min_ticket'],
            'subtitle' => $fullEvent->max_winners . ' winners daily',
            'requirement' => 'Every RM50 get 1 ticket',
            'join_status' => (bool)$userTicketReward->is_joined,
            'is_win' => $is_win,
            'result_desc' => is_null($is_win) ? null : ($is_win == true ? "You had got the $ticketPriceDisplay credit." : 'Wish you good luck next time :)'),
            'terms' => Lang::has('langReward.fortune-event-terms') ? Lang::get('langReward.fortune-event-terms') : '',
            'free_credit' => (string)(int)($fullEvent->max_amount * $fullEvent->max_winners),

        ];

        abort(200, json_encode(['data' => $data]));
    }

    public function joinFuDaiAngPao(Request $request)
    {
        $request->request->add(['user_id' => auth()->user()?->id ?? null]);

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $user = auth()->user();
        $angpauEvent = AngpauEvent::getCurrentAngpauEvent();
        $angpauEventId = $angpauEvent['id'];
        $userTicketReward = UserTicketReward::where('user_id', $request->user_id)
            ->where('ref_event_id', $angpauEventId)
            ->first();
        if ($userTicketReward->is_joined) {
            abort(400, json_encode('You have already joined'));
        }


        if (!isset($userTicketReward)) {
            UserTicketReward::create([
                'user_id' => $user->id,
                'store_id' => $user->store_id,
                'name' => $user->username,
                'ref_event_id' => $angpauEventId,
                'total_token' => 0,
                'is_joined' => true,
            ]);
        } else {
            $userTicketReward->update([
                'is_joined' => true,
            ]);
        }


        // UserAngpau::updateOrCreate(
        //     [
        //         'angpau_event_id' => $angpauEventId,
        //         'user_id' => $user->id,
        //     ],
        //     [
        //         'name' => $user->username,
        //         'ticket' => $userTicketReward?->total_token ?? 0,
        //         'amount' => 0,
        //         'is_selected' => false,
        //         'is_dummy' => false,
        //         'status' => 0,
        //     ]
        // );

        $participants_count = rand(1, 20) + 1;
        AngpauEvent::where('id', $angpauEventId)
            ->increment('total_participants', $participants_count);

        abort(200, json_encode(['data' => [
            'status' => true,
            'title' =>  'Successfully Joined!',
            'message' => 'Will announce winner. Stay Tuned!',
            'total_current_participants' => $participants_count + $angpauEvent['total_participants'],
        ]]));
    }

    public function getAngpauLeaderboard(Request $request)
    {
        $request->request->add(['user_id' => auth()->user()?->id ?? null]);

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();

        $angpauEvent = AngpauEvent::getPreviousAngpauEvent();
        $angpauEventId = $angpauEvent['id'];

        $previousUserAngpau = UserTicketReward::where('user_id', $params['user_id'])
            ->where('ref_event_id', $angpauEventId)
            ->first();
        if ($previousUserAngpau) {
            $previousUserAngpau->update([
                'is_read' => true, // is read status for winner and unlucky
            ]);
        }

        $limit = $angpauEvent['max_winners'];
        $angpauList = UserAngpau::getTopUserAngpauList($angpauEvent, $params['user_id'], $limit);
        $currentRank = UserAngpau::getAngpauRankByUserId($params['user_id'], $angpauEventId);

        $data = [
            'angpau_list' => $angpauList,
            'current_rank' => $currentRank,
            'claim_date' => date_format($previousUserAngpau?->created_at ?? now(), 'Y-m-d'),
        ];

        abort(200, json_encode(['data' => $data]));
    }
}
