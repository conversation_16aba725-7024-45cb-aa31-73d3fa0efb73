<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Lang;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use App\Models;

class MemoController extends Controller
{
    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = memo/add-memo
     * @module = admin
     * @path = memo/add-memo
     * @permissionName = Add Memo
     * @menuType = api
     * @parent = Memo
     * @method = POST
     * @description = To add memo.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = subject|<subject>|string|required|test subject|Memo's Subject.
     * @body = description|<description>|string|required|test description|Memo's Description.
     * @body = memo_data|<array>|array|required|[{"subject":"TEST Memo 1","language_type":"en","description":"TEST Description","upload":{"upload_name":"image1.jpg","upload_type":"image\/jpg"}}]|Memo's Data. 
     * @body = memo_data.*.language_type|<language_type>|string|required|en, cn, my|Memo's Language.
     * @body = memo_data.*.upload|<array>|array|required|{"upload_name":"image1.jpg","upload_type":"image\/jpg"}|Memo's Image.
     * @body = memo_data.*.upload.upload_name|<upload_name>|string|required|test/2022/09/1663906995_0715_test.png|Memo's Image (Public Type).
     * @body = memo_data.*.upload.upload_type|<upload_type>|string|required|image/jpg, image/jpeg, image/png|Memo's Image type.
     * @body = start_date|<start_date>|string|optional|2022-08-28|Memo's activate date. Required with end date.
     * @body = end_date|<end_date>|string|optional|2022-08-28|Memo's activate date. Required with start date.
     * @body = status|<status>|string|optional|active|Memo's status. (dropdown: memo_status)

     * @response = {"data":{"subject":"This is a Pop Up Announcement 3","description":"This is to inform that next two month is February.","start_date":"2022-12-30","end_date":"2023-01-30","memo_data":[{"language_type":"en","upload":{"upload_name":"image_3.jpg","upload_type":"image\/jpg"}},{"language_type":"cn","upload":{"upload_name":"image_4.jpg","upload_type":"image\/jpg"}}],"valid_country":[129]},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmNxcS5jb20vbWVtby9hZGQtbWVtbyIsImlhdCI6MTY3MjM4MjgyMCwiZXhwIjoxNjcyMzkxNTM1LCJuYmYiOjE2NzIzODc5MzUsImp0aSI6IjlZUFJxQWxra3RNbFNVekEiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.2k6yD8mqTlW6UQA8fCpLAr1wv5FlOtpxmI-rU8-y91I","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.061602115631104 sec","log_id":"d34eac8f-571d-4ac1-b58d-d4f03ed5a028"}
     * ##docs end##
     */
    public function addMemo(Request $request){
        if(MODULE == 'user'){
            abort(400, json_encode('Invalid Access'));
        }

        $validator = Validator::make($request->all(), [
            "subject" => "required|string",
            "description" => "required|string",

            "memo_data" => "required|array",
            "memo_data.*.language_type" => "required|string|distinct|in:".implode(',',array_keys(config('language'))),

            "memo_data.*.upload" => "required|array",
            "memo_data.*.upload.upload_name" => "required|string",
            "memo_data.*.upload.upload_type" => "required|in:image/jpg,image/jpeg,image/png",

            "valid_country" => "array",
            "valid_country.*" => "required|integer|exists:country,id,status,1",

            "memo_url" => "array",
            "memo_url.url" => "nullable|string",
            "memo_url.internal_flag" => "required_with:memo_url.url|integer|in:1,0",

            "start_date" => "required_with:end_date|nullable|string|date_format:Y-m-d|after_or_equal:today",
            "end_date" => "required_with:start_date|nullable|string|date_format:Y-m-d|after_or_equal:start_date",
            "status" => "string|in:".implode(",",array_keys(Models\Memo::$status))
        ],[
            'subject.required' => Lang::get('lang.input-field-required-error'),
            'description.required' => Lang::get('lang.input-field-required-error'),
            'memo_data.required' => Lang::get('lang.input-field-required-error'),
            'memo_data.*.language_type.required' => Lang::get('lang.input-field-required-error'),
            'memo_data.*.upload.required' => Lang::get('lang.input-field-required-error'),
            'memo_data.*.upload.upload_name.required' => Lang::get('lang.input-field-required-error'),
            'memo_data.*.upload.upload_type.required' => Lang::get('lang.input-field-required-error'),
            'valid_country.required' => Lang::get('lang.input-field-required-error'),
            'valid_country.*.required' => Lang::get('lang.input-field-required-error'),
            'memo_url.internal_flag.required_with' => Lang::get('lang.input-field-required-error'),
            'start_date.required_with' => Lang::get('lang.input-field-required-error'),
            'end_date.required_with' => Lang::get('lang.input-field-required-error'),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\Memo::addMemo($validator->validated());

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = memo/memo-list
     * @module = admin
     * @path = memo/memo-list
     * @permissionName = Memo
     * @menuType = sub_menu
     * @parent = Document
     * @method = POST
     * @description = To get memo list.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = title|<title>|string|optional|Announcement|Announcement title filter.
     * @body = status|<status>|string|optional|Memo|Memo status filter. (Dropdown: memo_status)
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.

     * @response = {"list":[{"id":3,"subject":"This is a Pop Up Announcement 3","description":"This is to inform that next two month is February.","start_date":"2022-12-30","end_date":"2023-01-30","status":"active","status_display":"Active","creator":"maskman","created_at":"2022-12-30 15:29:50","updated_at":"2022-12-30 15:29:50"},{"id":2,"subject":"This is a Pop Up Announcement 2","description":"This is to inform that next month is January.","start_date":null,"end_date":null,"status":"active","status_display":"Active","creator":"maskman","created_at":"2022-12-30 15:16:36","updated_at":"2022-12-30 15:16:36"},{"id":1,"subject":"This is a Pop Up Announcement","description":"This is to inform that this month is December.","start_date":null,"end_date":null,"status":"active","status_display":"Active","creator":"maskman","created_at":"2022-12-30 15:16:11","updated_at":"2022-12-30 15:16:11"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":3,"total":3},"meta":null,"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmNxcS5jb20vbWVtby9tZW1vLWxpc3QiLCJpYXQiOjE2NzIzODI4MjAsImV4cCI6MTY3MjM4ODk5NSwibmJmIjoxNjcyMzg1Mzk1LCJqdGkiOiJxcGtFa1FrUjJ3aTRRTmEzIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.Q6AnQdQ7hdbLsjdBurzaMyTD6leK7YPmqZENMpSd0as","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.13066601753235 sec","log_id":"c19c4225-670b-4d58-81bb-0f6a1d1344f7"}
     * ##docs end##
     */
    public function getMemoList(Request $request){

        $validator = Validator::make($request->all(), [
            "limit" => "integer",
            "page" => "integer",
            "title" => "string",
            'status' => 'string|in:'.implode(',',array_keys(Models\Memo::$status)),
            "from_date" => "required_with:to_date|string|date_format:Y-m-d",
            "to_date" => "required_with:from_date|string|date_format:Y-m-d|after_or_equal:from_date",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\Memo::getMemoList($validator->validated());

        abort(200,json_encode($res));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = memo/memo-detail
     * @module = admin
     * @path = memo/memo-detail
     * @permissionName = Memo Detail
     * @menuType = api
     * @parent = Memo
     * @method = POST
     * @description = To get memo detail.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|optional|1|Memo's id.

     * @response = {"data":{"id":1,"subject":"Testing","description":"Testing Only","start_date":"2023-01-13","end_date":"2023-01-13","status":"active","status_display":"active","memo_data":[{"language_type":"en","upload_data":{"upload_name":"staging\/2023\/01\/1673599099_7540","upload_type":"image\/png","upload_name_display":"https:\/\/c1q2-pub.s3.ap-southeast-1.amazonaws.com\/staging\/2023\/01\/1673599099_7540"},"language_type_display":"en"}],"valid_country":[129],"memo_url":{"url":"testing","internal_flag":0}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwuYWRtaW4uY3FxLmNvbS9tZW1vL21lbW8tZGV0YWlsIiwiaWF0IjoxNjczNTk5NzU1LCJleHAiOjE2NzM2MDM4NDMsIm5iZiI6MTY3MzYwMDI0MywianRpIjoiaDNvUm1aRlY3Z05MNkhwaSIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.npOFNL05Q2lbkeDj9bKXVxlvuV-mH9MQTs6ENTwxVXE","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.12850403785706 sec","log_id":"986f0ae7-2f12-4d82-9177-7a597c8fc764"}
     * ##docs end##
     */
    public function getMemoDetail(Request $request){

        $validator = Validator::make($request->all(), [
            "id" => "integer|exists:memo,id,deleted_at,NULL",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = ["data" => null];
        if (isset($validator->validated()['id'])) $res = Models\Memo::getMemoDetail($validator->validated());

        abort(200,json_encode($res));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = memo/edit-memo
     * @module = admin
     * @path = memo/edit-memo
     * @permissionName = Edit Memo
     * @menuType = api
     * @parent = Memo
     * @method = POST
     * @description = To edit memo.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|required|1|Memo's id.
     * @body = subject|<subject>|string|required|test subject|Memo's Subject.
     * @body = description|<description>|string|required|test description|Memo's Description.
     * @body = memo_data|<array>|array|required|[{"subject":"TEST Memo 1","language_type":"en","description":"TEST Description","upload":[{"upload_name":"image1.jpg","upload_type":"image\/jpg"}]}]|Memo's Data. 
     * @body = memo_data.*.language_type|<language_type>|string|required|en, cn, my|Memo's Language.
     * @body = memo_data.*.upload|<array>|array|required|{"upload_name":"image1.jpg","upload_type":"image\/jpg"}|Memo's Image.
     * @body = memo_data.*.upload.upload_name|<upload_name>|string|required|test/2022/09/1663906995_0715_test.png|Memo's Image (Public Type).
     * @body = memo_data.*.upload.upload_type|<upload_type>|string|required|image/jpg, image/jpeg, image/png|Memo's Image type.
     * 
     * @body = start_date|<start_date>|string|optional|2022-08-28|Memo's activate date. Required with end date.
     * @body = end_date|<end_date>|string|optional|2022-08-28|Memo's activate date. Required with start date.
     * @body = status|<status>|string|optional|active|Memo's status. (dropdown: memo_status)

     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmNxcS5jb20vbWVtby9lZGl0LW1lbW8iLCJpYXQiOjE2NzIzODI4MjAsImV4cCI6MTY3MjM4OTE0NywibmJmIjoxNjcyMzg1NTQ3LCJqdGkiOiJpSlc0WloxRlR6bk5TempzIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.B5QPa4-gnJOs98rnPR6-CY6ad12DkYXQyxe97LkE57o","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.15055394172668 sec","log_id":"a3af5c98-f335-4fe7-8f65-bf627eb75246"}
     * ##docs end##
     */
    public function editMemo(Request $request){
        if(MODULE == 'user'){
            abort(400, json_encode('Invalid Access'));
        }

        $validator = Validator::make($request->all(), [
            "id" => "required|integer|exists:memo,id,deleted_at,NULL",
            "subject" => "required|string",
            "description" => "required|string",

            "memo_data" => "required|array",
            "memo_data.*.language_type" => "required|string|distinct|in:".implode(',',array_keys(config('language'))),

            "memo_data.*.upload" => "required|array",
            "memo_data.*.upload.upload_name" => "required|string",
            "memo_data.*.upload.upload_type" => "required|in:image/jpg,image/jpeg,image/png",

            "valid_country" => "array",
            "valid_country.*" => "required|integer|exists:country,id,status,1",

            "memo_url" => "array",
            "memo_url.url" => "nullable|string",
            "memo_url.internal_flag" => "required_with:required_with:memo_url.url|integer|in:1,0",

            "start_date" => "required_with:end_date|nullable|string|date_format:Y-m-d",
            "end_date" => "required_with:start_date|nullable|string|date_format:Y-m-d|after_or_equal:start_date",
            "status" => "required|string|in:".implode(",",array_keys(Models\Memo::$status))
        ],[
            'id.required' => Lang::get('lang.input-field-required-error'),
            'subject.required' => Lang::get('lang.input-field-required-error'),
            'description.required' => Lang::get('lang.input-field-required-error'),
            'memo_data.required' => Lang::get('lang.input-field-required-error'),
            'memo_data.*.language_type.required' => Lang::get('lang.input-field-required-error'),
            'memo_data.*.upload.required' => Lang::get('lang.input-field-required-error'),
            'memo_data.*.upload.upload_name.required' => Lang::get('lang.input-field-required-error'),
            'memo_data.*.upload.upload_type.required' => Lang::get('lang.input-field-required-error'),
            'valid_country.required' => Lang::get('lang.input-field-required-error'),
            'valid_country.*.required' => Lang::get('lang.input-field-required-error'),
            'memo_url.internal_flag.required_with' => Lang::get('lang.input-field-required-error'),
            'start_date.required_with' => Lang::get('lang.input-field-required-error'),
            'end_date.required_with' => Lang::get('lang.input-field-required-error'),
            'status.required' => Lang::get('lang.input-field-required-error'),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\Memo::editMemo($validator->validated());

        abort(200);
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = memo/delete-memo
     * @module = admin
     * @path = memo/delete-memo
     * @permissionName = Delete Memo
     * @menuType = api
     * @parent = Memo
     * @method = POST
     * @description = To delete memo.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|required|1|Memo's id.

     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmNxcS5jb20vbWVtby9kZWxldGUtbWVtbyIsImlhdCI6MTY3MjM4MjgyMCwiZXhwIjoxNjcyMzg5MzY2LCJuYmYiOjE2NzIzODU3NjYsImp0aSI6InZxRWw5blRXWHhzdmtkN0wiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.xNoEdJMlRTRWiwnbCTBgTjhjF-Ku_6MSQjEXerDpQ5I","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.075712919235229 sec","log_id":"727e7bff-a16f-482e-88c1-b78b7cce2c6d"}
     * ##docs end##
     */
    public function deleteMemo(Request $request){

        $validator = Validator::make($request->all(), [
            "id" => "required|integer|exists:memo,id,deleted_at,NULL",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\Memo::deleteMemo($validator->validated());

        abort(200);
    }
}
