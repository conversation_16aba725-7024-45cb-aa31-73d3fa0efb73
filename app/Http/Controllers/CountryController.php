<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Country;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

use Validator;
use DB;
use Illuminate\Support\Facades\Lang;

class CountryController extends Controller
{

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = country/get
     * @module = admin,user
     * @path = country/get
     * @method = post
     * @description = To get country detail
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @response = {"data":[{"id":129,"dial_code":"60","name":"malaysia","country_display":"Malaysia","iso_code":"MY","state":[{"id":1,"name":"johor","state_display":"johor","zone":{"id":1,"name":"west","zone_display":"west"}},{"id":2,"name":"malacca","state_display":"malacca","zone":{"id":1,"name":"west","zone_display":"west"}},{"id":3,"name":"kedah","state_display":"kedah","zone":{"id":1,"name":"west","zone_display":"west"}},{"id":4,"name":"kelantan","state_display":"kelantan","zone":{"id":1,"name":"west","zone_display":"west"}},{"id":5,"name":"kuala-lumpur","state_display":"kuala-lumpur","zone":{"id":1,"name":"west","zone_display":"west"}},{"id":6,"name":"negeri-sembilan","state_display":"negeri-sembilan","zone":{"id":1,"name":"west","zone_display":"west"}},{"id":7,"name":"pahang","state_display":"pahang","zone":{"id":1,"name":"west","zone_display":"west"}},{"id":8,"name":"penang","state_display":"penang","zone":{"id":1,"name":"west","zone_display":"west"}},{"id":9,"name":"perak","state_display":"perak","zone":{"id":1,"name":"west","zone_display":"west"}},{"id":10,"name":"perlis","state_display":"perlis","zone":{"id":1,"name":"west","zone_display":"west"}},{"id":11,"name":"putrajaya","state_display":"putrajaya","zone":{"id":1,"name":"west","zone_display":"west"}},{"id":12,"name":"selangor","state_display":"selangor","zone":{"id":1,"name":"west","zone_display":"west"}},{"id":13,"name":"terengganu","state_display":"terengganu","zone":{"id":1,"name":"west","zone_display":"west"}},{"id":14,"name":"sarawak","state_display":"sarawak","zone":{"id":2,"name":"east","zone_display":"east"}},{"id":15,"name":"sabah","state_display":"sabah","zone":{"id":2,"name":"east","zone_display":"east"}}],"zone":[{"id":1,"name":"west","zone_display":"west"},{"id":2,"name":"east","zone_display":"east"}],"currency":{"id":5,"iso":"MYR","name":"Malaysian ringgit","currency_display":"Malaysian ringgit"}}],"status":true,"timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.10976600646973 sec","log_id":"43c887f2-4d5a-4bda-9afa-15d29ebcaeb7"}
     * ##docs end##
     */
    public static function getCountry(Request $request)
    {
        $countryList = Country::getList([], true);
        abort(200, json_encode($countryList));
    }
}
