<?php

namespace App\Http\Controllers;

use App\Jobs\RewardCredit;
use Illuminate\Http\Request;

use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Lang;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use App\Models;
use App\Models\Store;
use App\Traits;
use Illuminate\Support\Str;

class CardController extends Controller
{
    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = New Card List
     * @module = user,app
     * @path = card/new-card-list
     * @permissionName = Get new card list
     * @menuType = api
     * @method = POST
     * @description = To get new card list.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = store_id|<store_id>|integer|required|2|Store Id.
     * @response = {"data":{"store_name":"test","card_list":[{"member_card_id":"000405","member_card_no":"2634796074","card_display_name":"Card0"},{"member_card_id":"312","member_card_no":"2634796074","card_display_name":"Card1"},{"member_card_id":"000405211","member_card_no":"3122232","card_display_name":"Card2"},{"member_card_id":"009","member_card_no":"3212","card_display_name":"Card3"},{"member_card_id":"098","member_card_no":"87","card_display_name":"Card4"},{"member_card_id":"789","member_card_no":"2634000458","card_display_name":"Card5"}]},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC5mdy5jb20vY2FyZC9uZXctY2FyZC1saXN0IiwiaWF0IjoxNzI0MTQxNTM5LCJleHAiOjE3NTU3MDQ1OTUsIm5iZiI6MTcyNDE0NDU5NSwianRpIjoiczJSUGlCOFdTbkdQVjZNeSIsInN1YiI6IjEwMDAwMDgiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.51nRmSX1BfJiTyRGsmmbZCWIZC_Vek0J9J2NyqFqroE","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.33172202110291 sec","log_id":"e79b6f88-ad5b-4013-a696-2893de15389e","valid_version":false}
     * ##docs end##
     */
    public function getNewCardList(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $request->request->add(['user_id' => auth()->user()->id]);
            $request->request->add(['phone_no' => auth()->user()->phone_no]);
        }

        $validator = Validator::make($request->all(), [
            'store_id' => [
                'required',
                function ($q, $value, $fail) use ($request) {
                    $store = Models\Store::where('store_id', $value)->first();
                    if (!isset($store)) {
                        $fail(Lang::get("validation.exists"));
                    }
                    if (in_array(MODULE, ['user', 'app']) && auth()->user()->store_id != $value) {
                        $phoneNo = auth()->user()->phone_no;
                        $selectedUser = Models\User::where('phone_no', $phoneNo)->where('store_id', $value)->first();

                        $register = false;
                        $registerUrl = null;
                        $switch = false;
                        if (!isset($selectedUser)) {
                            $register = true;
                            $registerUrl = env('APP_URL_USER') . '?si=' . $value;
                        } else {
                            $switch = true;
                        }

                        $returndata = [
                            'data' => [
                                'register' => $register,
                                'register_url' => $registerUrl,
                                'switch' => $switch,
                            ],
                        ];
                        abort(200, json_encode($returndata));
                    }
                },
            ],
            'user_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\UserCard::getNewCardList($validator->validated());
        abort(200, json_encode($res));
    }


    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Add New Card
     * @module = user,app
     * @path = card/add-new-card
     * @permissionName = Add new card
     * @menuType = api
     * @method = POST
     * @description = To add new card.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = store_id|<store_id>|integer|required|1|Store Id.
     * @body = card_list.*|<card_list.*>|required|array||card list
     * @body = card_list.*.member_card_no|<card_list.*.member_card_no>|required|string||card card no
     * @body = card_list.*.card_display_name|<card_list.*.card_display_name>|required|string||card name
     * @response = {"data":{"store_name":"test","card_list":[{"member_card_id":"000405","member_card_no":"2634796074","card_display_name":"Card0"},{"member_card_id":"312","member_card_no":"2634796074","card_display_name":"Card1"},{"member_card_id":"000405211","member_card_no":"3122232","card_display_name":"Card2"},{"member_card_id":"009","member_card_no":"3212","card_display_name":"Card3"},{"member_card_id":"098","member_card_no":"87","card_display_name":"Card4"},{"member_card_id":"789","member_card_no":"2634000458","card_display_name":"Card5"}]},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC5mdy5jb20vY2FyZC9uZXctY2FyZC1saXN0IiwiaWF0IjoxNzI0MTQxNTM5LCJleHAiOjE3NTU3MDQ1OTUsIm5iZiI6MTcyNDE0NDU5NSwianRpIjoiczJSUGlCOFdTbkdQVjZNeSIsInN1YiI6IjEwMDAwMDgiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.51nRmSX1BfJiTyRGsmmbZCWIZC_Vek0J9J2NyqFqroE","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.33172202110291 sec","log_id":"e79b6f88-ad5b-4013-a696-2893de15389e","valid_version":false}
     * ##docs end##
     */
    public function addNewCard(Request $request, bool $response = true)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $request->request->add(['user_id' => auth()->user()->id]);
        }

        $validator = Validator::make($request->all(), [
            'store_id' => [
                'required',
                function ($q, $value, $fail) use ($request) {
                    if (in_array(MODULE, ['user', 'app']) && auth()->user()->store_id != $value) {
                        $fail(Lang::get("validation.exists"));
                    }
                    // $store = Models\Store::where('store_id', $value)->first();
                    // if(!isset($store)){
                    //     Models\Store::refreshStore();
                    // }
                    $store = Models\Store::where('store_id', $value)->first();
                    if (!isset($store)) {
                        $fail(Lang::get("validation.exists"));
                    }
                },
            ],
            // 'phone_no' => 'required|string',
            'user_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\UserCard::getNewCardList($validator->validated(), true);

        $cardList = $res['data']['card_list'] ?? [];

        $validator = Validator::make($request->all(), [
            'store_id' => ['required'],
            'user_id' => ['required'],
            'card_list' => 'nullable|array',
            'card_list.*.member_card_no' => [
                'required',
                function ($q, $value, $fail) use ($cardList, &$finalCardList) {
                    $cardList = collect($cardList);
                    $exist = $cardList->where('member_card_no', $value)->toArray();
                    if (empty($exist)) {
                        $fail(Lang::get("validation.exists"));
                    }

                    $exist = Models\UserCard::where('card_serial_no', $value)->where('status', Models\UserCard::$status['active'])->first();
                    if ($exist) {
                        $fail(Lang::get("validation.exists"));
                    }
                }
            ],
            'card_list.*.card_display_name' => [
                'required',
                Rule::unique('user_card', 'card_name')->where(function ($q) use ($request) {
                    return $q->where('user_id', $request->user_id);
                }),
                'distinct',
            ],
        ]);
        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        Models\UserCard::addNewCard($validator->validated() + ['card_data' => $cardList, 'card_list' => $cardList]);

        $res = null;

        if ($response) {
            abort(200, json_encode($res));
        }
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Card List
     * @module = admin
     * @path = card/card-list
     * @permissionName = Get card list
     * @menuType = api
     * @method = POST
     * @description = To get card list.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = member_id|<member_id>|string|optional|11|Member Id.
     * @body = username|<username>|string|optional|11|Username.
     * @body = serial_no|<serial_no>|string|optional|11|Serial No.
     * @body = store_id|<store_id>|string|optional|11|Store Id.
     * @body = phone_no|<phone_no>|string|optional|011111111|User's phone_no.
     * @body = status|<status>|integer|nullable|1|Status (dropdown: user_card_status).
     * @response = {"data":{"store_name":"test","card_list":[{"member_card_id":"000405","member_card_no":"2634796074","card_display_name":"Card0"},{"member_card_id":"312","member_card_no":"2634796074","card_display_name":"Card1"},{"member_card_id":"000405211","member_card_no":"3122232","card_display_name":"Card2"},{"member_card_id":"009","member_card_no":"3212","card_display_name":"Card3"},{"member_card_id":"098","member_card_no":"87","card_display_name":"Card4"},{"member_card_id":"789","member_card_no":"2634000458","card_display_name":"Card5"}]},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC5mdy5jb20vY2FyZC9uZXctY2FyZC1saXN0IiwiaWF0IjoxNzI0MTQxNTM5LCJleHAiOjE3NTU3MDQ1OTUsIm5iZiI6MTcyNDE0NDU5NSwianRpIjoiczJSUGlCOFdTbkdQVjZNeSIsInN1YiI6IjEwMDAwMDgiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.51nRmSX1BfJiTyRGsmmbZCWIZC_Vek0J9J2NyqFqroE","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.33172202110291 sec","log_id":"e79b6f88-ad5b-4013-a696-2893de15389e","valid_version":false}
     * ##docs end##
     */
    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Card List
     * @module = user,app
     * @path = card/card-list
     * @permissionName = Get card list
     * @menuType = api
     * @method = POST
     * @description = To get card list.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = status|<status>|integer|nullable|1|Status (dropdown: user_card_status).
     * @body = ex_transfer_trnx_limit|<ex_transfer_trnx_limit>|integer|nullable|1|Ex Transfer limit.
     * @response = {"list":[{"store_name":"test","card_id":83,"card_name":"Card01155","card_serial_no":"2625296035","updated_at":"05\/09\/2024 14:52:41","card_balance":"0.00","status":0,"status_display":"Inactive"},{"store_name":"test","card_id":84,"card_name":"Card0","card_serial_no":"2634796074","updated_at":"05\/09\/2024 14:43:40","card_balance":"95183.00","status":1,"status_display":"Active"},{"store_name":"test","card_id":85,"card_name":"Card1","card_serial_no":"2634796074","updated_at":"05\/09\/2024 14:43:40","card_balance":"95183.00","status":1,"status_display":"Active"},{"store_name":"test","card_id":86,"card_name":"Card2","card_serial_no":"3122232","updated_at":"05\/09\/2024 14:43:40","card_balance":"68960.00","status":1,"status_display":"Active"},{"store_name":"test","card_id":87,"card_name":"Card3","card_serial_no":"3212","updated_at":"05\/09\/2024 14:43:40","card_balance":"0.00","status":1,"status_display":"Active"},{"store_name":"test","card_id":88,"card_name":"Card4","card_serial_no":"87","updated_at":"05\/09\/2024 14:43:40","card_balance":"0.00","status":1,"status_display":"Active"},{"store_name":"test","card_id":89,"card_name":"Card5","card_serial_no":"2634000458","updated_at":"05\/09\/2024 14:43:40","card_balance":"0.00","status":1,"status_display":"Active"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":7,"total":7},"meta":null,"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC5mdy5jb20vY2FyZC9jYXJkLWxpc3QiLCJpYXQiOjE3MjU1MTg1NjMsImV4cCI6MTc1NzA3OTgyOSwibmJmIjoxNzI1NTE5ODI5LCJqdGkiOiJoSTFSMWM5Y0haQUluTElSIiwic3ViIjoiMTAwMDAwOSIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.3syW6Nd56a_50OMjN9xHiTH2cARkBtfNyJrDrCwkSlU","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"dev","execution_duration":"0.048673868179321 sec","log_id":"29d6d465-7399-43fa-b1b5-220934062372","valid_version":false}
     * ##docs end##
     */
    public function getCardList(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $request->request->add(['user_id' => auth()->user()->id]);
        }

        if (in_array(MODULE, ['admin'])) {
            $adminId = auth()->user()->id;
            $adminRes = Models\Admin::with(
                [
                    'adminDetail' =>
                    function ($q) {
                        return $q->where('name', 'store');
                    }
                ],
            )
                ->find($adminId);

            $isMaster = $adminRes->is_master;
            if (!$isMaster) {
                $branchJE = $adminRes->adminDetail->first()->value ?? "[]";
                $branchDE = json_decode($branchJE);

                $request->merge(['extra_store_id' => $branchDE]);
            }
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'nullable|exists:users,id',
            'ex_transfer_trnx_limit' => 'nullable|numeric',

            'member_id' => 'nullable',
            'username' => 'nullable',
            'serial_no' => 'nullable',
            'phone_no' => 'nullable|string',
            'store_id' => 'nullable',
            'extra_store_id' => 'nullable',
            'status' => 'nullable',

            "limit" => "int",
            'see_all' => 'integer|in:1,0',
            "export" => "in:1,0|nullable",
            "export_data" => "required_if:export,==,1",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if (in_array(MODULE, ['user', 'app'])) {
            $newCardList = $this->addNewCard(new Request([
                'store_id' => $request->store_id ?? auth()->user()->store_id,
                'user_id' => $request->user_id ?? auth()->user()->id,
            ]), false);

            // RewardCredit::dispatch([
            //     'user_id' => $request->user_id ?? auth()->user()->id,
            //     'phone_no' => auth()->user()->phone_no,
            // ]);
        }

        $res = Models\UserCard::getCardList($validator->validated());
        abort(200, json_encode($res));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Card Detail
     * @module = admin
     * @path = card/card-detail
     * @permissionName = Get card detail
     * @menuType = api
     * @method = POST
     * @description = To get card detail.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = card_id|<card_id>|integer|required|2|Card Id.
     * @response = {"data":{"store_name":"test","card_id":11,"card_name":"halo2","card_serial_no":"2634796074","card_balance":"63000","updated_at":"22\/08\/2024 11:36:05"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC5mdy5jb20vY2FyZC9jYXJkLWRldGFpbCIsImlhdCI6MTcyNDIzMzI3NSwiZXhwIjoxNzU1ODU3NzY1LCJuYmYiOjE3MjQyOTc3NjUsImp0aSI6InNhd3dKampEQ21RTnpycjEiLCJzdWIiOiIxMDAwMDAxIiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.plTh3T7FKsetCGwuVILyDSmcuQzL4v0XxIyzt2cxifE","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.069216966629028 sec","log_id":"a01ca888-cf23-4a7f-838c-fb62bb31c08b","valid_version":false}
     * ##docs end##
     */
    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Card Detail
     * @module = user,app
     * @path = card/card-detail
     * @permissionName = Get card detail
     * @menuType = api
     * @method = POST
     * @description = To get card detail.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = card_id|<card_id>|integer|required|2|Card Id.
     * @body = ex_transfer_data|<ex_transfer_data>|integer|optional|1|If need then pass 1
     * @response = {"data":{"store_name":"test","card_id":83,"card_name":"Card01155","card_serial_no":"2625296035","card_balance":"0.00","updated_at":"05\/09\/2024 15:09:08","phone_no":"Tester001","status":0,"status_display":"Inactive","ex_transfer_data":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC5mdy5jb20vY2FyZC9jYXJkLWRldGFpbCIsImlhdCI6MTcyNTUxOTkxMiwiZXhwIjoxNzU3MDgwMTQ4LCJuYmYiOjE3MjU1MjAxNDgsImp0aSI6Im1ya0xQTUZUVWZXa1lMazgiLCJzdWIiOiIxMDAwMDA5IiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.kisBuRKm6l5T-IDZBwdWlv-pyVWr_z7H8-jw94xK0Qc","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"dev","execution_duration":"0.086501836776733 sec","log_id":"264eff16-e83b-4847-a77d-153ffdbae580","valid_version":false}
     * ##docs end##
     */
    public function getCardDetail(Request $request)
    {
        if (in_array(MODULE, ['admin'])) {

            $adminId = auth()->user()->id;
            $adminRes = Models\Admin::with(
                [
                    'adminDetail' =>
                    function ($q) {
                        return $q->where('name', 'store');
                    }
                ],
            )
                ->find($adminId);

            $isMaster = $adminRes->is_master;
            if (!$isMaster) {
                $branchJE = $adminRes->adminDetail->first()->value ?? "[]";
                $branchDE = json_decode($branchJE);
                $cardStoreId = Models\Store::whereIn('id', $branchDE)->get()->pluck('store_id');
                $validator = Validator::make($request->all(), [
                    'card_id' => [
                        'required',
                        Rule::exists('user_card', 'id')->whereIn('store_id', $cardStoreId)
                    ],
                ]);
            } else {
                $validator = Validator::make($request->all(), [
                    'card_id' => 'required|exists:user_card,id',
                ]);
            }
        }
        if (in_array(MODULE, ['user', 'app'])) {
            $request->request->add(['user_id' => auth()->user()->id]);
            $validator = Validator::make($request->all(), [
                'user_id' => 'required|exists:users,id',
                'card_id' => 'required|exists:user_card,id,user_id,' . ($request->user_id ?? ''),
                'ex_transfer_data' => 'nullable|in:1',
            ]);
        }

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\UserCard::getCardDetail($validator->validated());
        abort(200, json_encode($res));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Card Transaction List
     * @module = admin
     * @path = card/card-transaction-list
     * @method = post
     * @description = To get card-transaction list (menu).

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
     *
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = transaction_id|<transaction_id>|string|optional|GFX12312312|Transaction's Id.
     * @body = transfer_type|<transfer_type>|interger|optional|1|Transfer's Type. (Get from dropdown : ex_transfer_type)
     * @body = store_id|<store_id>|interger|optional|1|Store filter. (Get from dropdown : store)
     * @body = username|<username>|string|optional|super|Username filter
     * @body = member_id|<member_id>|string|optional|member_id|Member ID filter.
     * @body = phone_no|<phone_no>|string|optional|60123456789|User Phone filter.
     * @body = card_serial_no|<card_serial_no>|string|optional|super|Card Serial No filter
     *
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * 
     * @body = export|<1/0>|integer|optional|1|export the listing or not. 
     * @body = export_data|<export_data>|array|option|{"data": {"Data KEY": "Column Display"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|goldmine-bonus|name of the export file. Required for export

     * @response = {"data":{"list":[{"ex_transfer_id":45,"created_at":"23\/08\/2024 18:12:50","transaction_id":"FW317721","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"23\/08\/2024 18:12:50","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":44,"created_at":"23\/08\/2024 16:50:05","transaction_id":"FW781281","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"23\/08\/2024 16:50:05","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":43,"created_at":"22\/08\/2024 17:46:09","transaction_id":"FW879272","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"confirmed","status_display":"Confirmed","amount":"10.00","updated_at":"22\/08\/2024 17:46:10","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":42,"created_at":"22\/08\/2024 17:41:28","transaction_id":"FW299408","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"confirmed","status_display":"Confirmed","amount":"10.00","updated_at":"22\/08\/2024 17:41:29","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":41,"created_at":"22\/08\/2024 17:39:37","transaction_id":"FW603673","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:39:37","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":40,"created_at":"22\/08\/2024 17:38:38","transaction_id":"FW584175","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:38:38","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":39,"created_at":"22\/08\/2024 17:38:17","transaction_id":"FW435991","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:38:17","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":38,"created_at":"22\/08\/2024 17:36:42","transaction_id":"FW625069","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"confirmed","status_display":"Confirmed","amount":"10.00","updated_at":"22\/08\/2024 17:36:44","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":37,"created_at":"22\/08\/2024 17:32:21","transaction_id":"FW356793","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:32:21","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":36,"created_at":"22\/08\/2024 17:30:33","transaction_id":"FW890394","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:30:33","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":35,"created_at":"22\/08\/2024 17:30:01","transaction_id":"FW748371","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:30:01","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":34,"created_at":"22\/08\/2024 17:29:11","transaction_id":"FW513572","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:29:11","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":33,"created_at":"22\/08\/2024 17:23:47","transaction_id":"FW885575","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:23:47","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":32,"created_at":"22\/08\/2024 17:23:37","transaction_id":"FW412826","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:23:37","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":31,"created_at":"22\/08\/2024 17:23:35","transaction_id":"FW397177","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:23:35","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":30,"created_at":"22\/08\/2024 17:21:06","transaction_id":"FW638962","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:21:06","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":29,"created_at":"22\/08\/2024 17:20:53","transaction_id":"FW371727","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:20:53","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":28,"created_at":"22\/08\/2024 17:20:24","transaction_id":"FW777817","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:20:24","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":27,"created_at":"22\/08\/2024 17:20:16","transaction_id":"FW784709","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:20:16","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":26,"created_at":"22\/08\/2024 17:20:04","transaction_id":"FW293854","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:20:04","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":25,"created_at":"22\/08\/2024 17:19:59","transaction_id":"FW814042","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:19:59","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":24,"created_at":"22\/08\/2024 17:19:54","transaction_id":"FW980951","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:19:54","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":23,"created_at":"22\/08\/2024 17:19:43","transaction_id":"FW029944","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:19:43","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":22,"created_at":"22\/08\/2024 17:19:34","transaction_id":"FW001025","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:19:34","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":21,"created_at":"22\/08\/2024 17:19:24","transaction_id":"FW901363","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:19:24","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":20,"created_at":"22\/08\/2024 17:18:54","transaction_id":"FW062417","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:18:54","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":19,"created_at":"22\/08\/2024 17:18:25","transaction_id":"FW462205","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:18:25","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":18,"created_at":"22\/08\/2024 17:06:57","transaction_id":"FW264359","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:06:57","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":17,"created_at":"22\/08\/2024 17:05:32","transaction_id":"FW629038","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:05:32","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":16,"created_at":"22\/08\/2024 17:03:49","transaction_id":"FW162481","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:03:49","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null}],"pagination":{"current_page":1,"from":1,"last_page":2,"per_page":30,"to":30,"total":44},"meta":null,"grand_total":{"grand_amount":"183.00"},"summary_data":{"transfer_amount":"203.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZ3LmNvbS9jYXJkL2NhcmQtdHJhbnNhY3Rpb24iLCJpYXQiOjE3MjQ2NDQ2OTEsImV4cCI6MTc1NjIxMzI4NSwibmJmIjoxNzI0NjUzMjg1LCJqdGkiOiJCV2ZlRGlIeXdWejNTY216Iiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.yXcdg-cV1aQMoHLZ51XdaxgAe5hmwgypCOuvJDf_I0E","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.13327693939209 sec","log_id":"1993b864-8cc8-4a03-b82d-c0d78aa1c4b7","valid_version":false}
     * ##docs end##
     */
    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Card Transaction
     * @module = admin
     * @path = card/card-transaction
     * @method = post
     * @description = To get card-transaction list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
    
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = card_id|<card_id>|interger|optional|1|Card Id
     * 
     * @body = export|<1/0>|integer|optional|1|export the listing or not. 
     * @body = export_data|<export_data>|array|option|{"data": {"Data KEY": "Column Display"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|goldmine-bonus|name of the export file. Required for export

     * @response = {"data":{"list":[{"ex_transfer_id":45,"created_at":"23\/08\/2024 18:12:50","transaction_id":"FW317721","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"23\/08\/2024 18:12:50","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":44,"created_at":"23\/08\/2024 16:50:05","transaction_id":"FW781281","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"23\/08\/2024 16:50:05","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":43,"created_at":"22\/08\/2024 17:46:09","transaction_id":"FW879272","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"confirmed","status_display":"Confirmed","amount":"10.00","updated_at":"22\/08\/2024 17:46:10","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":42,"created_at":"22\/08\/2024 17:41:28","transaction_id":"FW299408","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"confirmed","status_display":"Confirmed","amount":"10.00","updated_at":"22\/08\/2024 17:41:29","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":41,"created_at":"22\/08\/2024 17:39:37","transaction_id":"FW603673","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:39:37","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":40,"created_at":"22\/08\/2024 17:38:38","transaction_id":"FW584175","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:38:38","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":39,"created_at":"22\/08\/2024 17:38:17","transaction_id":"FW435991","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:38:17","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":38,"created_at":"22\/08\/2024 17:36:42","transaction_id":"FW625069","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"confirmed","status_display":"Confirmed","amount":"10.00","updated_at":"22\/08\/2024 17:36:44","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":37,"created_at":"22\/08\/2024 17:32:21","transaction_id":"FW356793","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:32:21","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":36,"created_at":"22\/08\/2024 17:30:33","transaction_id":"FW890394","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:30:33","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":35,"created_at":"22\/08\/2024 17:30:01","transaction_id":"FW748371","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:30:01","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":34,"created_at":"22\/08\/2024 17:29:11","transaction_id":"FW513572","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:29:11","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":33,"created_at":"22\/08\/2024 17:23:47","transaction_id":"FW885575","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:23:47","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":32,"created_at":"22\/08\/2024 17:23:37","transaction_id":"FW412826","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:23:37","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":31,"created_at":"22\/08\/2024 17:23:35","transaction_id":"FW397177","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:23:35","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":30,"created_at":"22\/08\/2024 17:21:06","transaction_id":"FW638962","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:21:06","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":29,"created_at":"22\/08\/2024 17:20:53","transaction_id":"FW371727","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:20:53","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":28,"created_at":"22\/08\/2024 17:20:24","transaction_id":"FW777817","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:20:24","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":27,"created_at":"22\/08\/2024 17:20:16","transaction_id":"FW784709","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:20:16","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":26,"created_at":"22\/08\/2024 17:20:04","transaction_id":"FW293854","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:20:04","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":25,"created_at":"22\/08\/2024 17:19:59","transaction_id":"FW814042","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:19:59","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":24,"created_at":"22\/08\/2024 17:19:54","transaction_id":"FW980951","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:19:54","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":23,"created_at":"22\/08\/2024 17:19:43","transaction_id":"FW029944","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:19:43","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":22,"created_at":"22\/08\/2024 17:19:34","transaction_id":"FW001025","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:19:34","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":21,"created_at":"22\/08\/2024 17:19:24","transaction_id":"FW901363","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:19:24","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":20,"created_at":"22\/08\/2024 17:18:54","transaction_id":"FW062417","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:18:54","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":19,"created_at":"22\/08\/2024 17:18:25","transaction_id":"FW462205","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:18:25","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":18,"created_at":"22\/08\/2024 17:06:57","transaction_id":"FW264359","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:06:57","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":17,"created_at":"22\/08\/2024 17:05:32","transaction_id":"FW629038","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:05:32","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":16,"created_at":"22\/08\/2024 17:03:49","transaction_id":"FW162481","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:03:49","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null}],"pagination":{"current_page":1,"from":1,"last_page":2,"per_page":30,"to":30,"total":44},"meta":null,"grand_total":{"grand_amount":"183.00"},"summary_data":{"transfer_amount":"203.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZ3LmNvbS9jYXJkL2NhcmQtdHJhbnNhY3Rpb24iLCJpYXQiOjE3MjQ2NDQ2OTEsImV4cCI6MTc1NjIxMzI4NSwibmJmIjoxNzI0NjUzMjg1LCJqdGkiOiJCV2ZlRGlIeXdWejNTY216Iiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.yXcdg-cV1aQMoHLZ51XdaxgAe5hmwgypCOuvJDf_I0E","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.13327693939209 sec","log_id":"1993b864-8cc8-4a03-b82d-c0d78aa1c4b7","valid_version":false}
     * ##docs end##
     */
    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = External Transfer List
     * @module = admin
     * @path = card/ex-transfer-list
     * @method = post
     * @description = To get ex-transfer list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
    
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = username|<username>|string|optional|test_user|Username.
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = transaction_id|<transaction_id>|string|optional|GFX12312312|Transaction's Id.
     * @body = transfer_type|<transfer_type>|interger|optional|1|Transfer's Type. (Get from dropdown : ex_transfer_type)
     * 
     * @body = export|<1/0>|integer|optional|1|export the listing or not. 
     * @body = export_data|<export_data>|array|option|{"data": {"Data KEY": "Column Display"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|goldmine-bonus|name of the export file. Required for export

     * @response = {"data":{"list":[{"ex_transfer_id":45,"created_at":"23\/08\/2024 18:12:50","transaction_id":"FW317721","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"23\/08\/2024 18:12:50","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":44,"created_at":"23\/08\/2024 16:50:05","transaction_id":"FW781281","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"23\/08\/2024 16:50:05","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":43,"created_at":"22\/08\/2024 17:46:09","transaction_id":"FW879272","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"confirmed","status_display":"Confirmed","amount":"10.00","updated_at":"22\/08\/2024 17:46:10","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":42,"created_at":"22\/08\/2024 17:41:28","transaction_id":"FW299408","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"confirmed","status_display":"Confirmed","amount":"10.00","updated_at":"22\/08\/2024 17:41:29","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":41,"created_at":"22\/08\/2024 17:39:37","transaction_id":"FW603673","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:39:37","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":40,"created_at":"22\/08\/2024 17:38:38","transaction_id":"FW584175","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:38:38","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":39,"created_at":"22\/08\/2024 17:38:17","transaction_id":"FW435991","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:38:17","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":38,"created_at":"22\/08\/2024 17:36:42","transaction_id":"FW625069","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"confirmed","status_display":"Confirmed","amount":"10.00","updated_at":"22\/08\/2024 17:36:44","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":37,"created_at":"22\/08\/2024 17:32:21","transaction_id":"FW356793","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:32:21","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":36,"created_at":"22\/08\/2024 17:30:33","transaction_id":"FW890394","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:30:33","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":35,"created_at":"22\/08\/2024 17:30:01","transaction_id":"FW748371","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:30:01","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":34,"created_at":"22\/08\/2024 17:29:11","transaction_id":"FW513572","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:29:11","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":33,"created_at":"22\/08\/2024 17:23:47","transaction_id":"FW885575","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:23:47","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":32,"created_at":"22\/08\/2024 17:23:37","transaction_id":"FW412826","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:23:37","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":31,"created_at":"22\/08\/2024 17:23:35","transaction_id":"FW397177","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:23:35","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":30,"created_at":"22\/08\/2024 17:21:06","transaction_id":"FW638962","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:21:06","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":29,"created_at":"22\/08\/2024 17:20:53","transaction_id":"FW371727","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"10.00","updated_at":"22\/08\/2024 17:20:53","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":28,"created_at":"22\/08\/2024 17:20:24","transaction_id":"FW777817","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:20:24","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":27,"created_at":"22\/08\/2024 17:20:16","transaction_id":"FW784709","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:20:16","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":26,"created_at":"22\/08\/2024 17:20:04","transaction_id":"FW293854","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:20:04","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":25,"created_at":"22\/08\/2024 17:19:59","transaction_id":"FW814042","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:19:59","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":24,"created_at":"22\/08\/2024 17:19:54","transaction_id":"FW980951","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:19:54","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":23,"created_at":"22\/08\/2024 17:19:43","transaction_id":"FW029944","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:19:43","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":22,"created_at":"22\/08\/2024 17:19:34","transaction_id":"FW001025","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:19:34","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":21,"created_at":"22\/08\/2024 17:19:24","transaction_id":"FW901363","reference":null,"username":"60142441456","type":"in","type_display":"in","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:19:24","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":20,"created_at":"22\/08\/2024 17:18:54","transaction_id":"FW062417","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:18:54","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":19,"created_at":"22\/08\/2024 17:18:25","transaction_id":"FW462205","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:18:25","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":18,"created_at":"22\/08\/2024 17:06:57","transaction_id":"FW264359","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:06:57","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":17,"created_at":"22\/08\/2024 17:05:32","transaction_id":"FW629038","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:05:32","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null},{"ex_transfer_id":16,"created_at":"22\/08\/2024 17:03:49","transaction_id":"FW162481","reference":null,"username":"60142441456","type":"out","type_display":"out","status":"processing","status_display":"Processing","amount":"1.00","updated_at":"22\/08\/2024 17:03:49","updated_by":"-","refundable":0,"creditable":0,"recall_available":0,"res_data":null}],"pagination":{"current_page":1,"from":1,"last_page":2,"per_page":30,"to":30,"total":44},"meta":null,"grand_total":{"grand_amount":"183.00"},"summary_data":{"transfer_amount":"203.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZ3LmNvbS9jYXJkL2NhcmQtdHJhbnNhY3Rpb24iLCJpYXQiOjE3MjQ2NDQ2OTEsImV4cCI6MTc1NjIxMzI4NSwibmJmIjoxNzI0NjUzMjg1LCJqdGkiOiJCV2ZlRGlIeXdWejNTY216Iiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.yXcdg-cV1aQMoHLZ51XdaxgAe5hmwgypCOuvJDf_I0E","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.13327693939209 sec","log_id":"1993b864-8cc8-4a03-b82d-c0d78aa1c4b7","valid_version":false}
     * ##docs end##
     */
    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = External Transfer List
     * @module = user,app
     * @path = card/ex-transfer-list
     * @method = post
     * @description = To get ex-transfer list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
    
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * 
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = transaction_id|<transaction_id>|string|optional|GFX12312312|Transaction's Id.
     * @body = transfer_type|<transfer_type>|interger|optional|1|Transfer's Type. (Get from dropdown : ex_transfer_type)

     * @response = {"data":{"list":[{"type":"in","type_display":"in","status":"confirmed","status_display":"Confirmed","amount":"4.00","created_at":"20\/08\/2024 19:34:23","trnx_id":"FW1"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC5mdy5jb20vY2FyZC9leC10cmFuc2Zlci1saXN0IiwiaWF0IjoxNzI0MjMzMjc1LCJleHAiOjE3NTU3OTM2MzgsIm5iZiI6MTcyNDIzMzYzOCwianRpIjoicHBEUG42OTloWkJmSEE2VyIsInN1YiI6IjEwMDAwMDEiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.9ZPQk7aK8AU86X-xHIWPB8e8xSRhvAI9Te-QGpD_FfA","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.090863943099976 sec","log_id":"57d5e1ba-679c-4f6d-8e74-df16ac8f3260","valid_version":false}
     * ##docs end##
     */
    public function getExTransferList(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $user_id = auth()->user()->id ?? NUll;
            $request->request->add(['user_id' => $user_id]);
        }

        if (in_array(MODULE, ['admin'])) {
            $adminId = auth()->user()->id;
            $adminRes = Models\Admin::with(
                [
                    'adminDetail' =>
                    function ($q) {
                        return $q->where('name', 'store');
                    }
                ],
            )
                ->find($adminId);

            $isMaster = $adminRes->is_master;
            if (!$isMaster) {
                $branchJE = $adminRes->adminDetail->first()->value ?? "[]";
                $branchDE = json_decode($branchJE);

                $request->merge(['extra_store_id' => $branchDE]);
            }
        }

        $validator = Validator::make($request->all(), [
            "order_sort" => "string|in:asc,desc",
            "user_id" => "integer",
            "username" => "string",
            "card_id" => "integer",
            "transaction_id" => "string",
            "phone_no" => "nullable|string",
            "member_id" => "nullable|integer",
            "transfer_type" => "integer",
            "from_date" => "required_with:to_date|string|date_format:Y-m-d",
            "to_date" => "required_with:from_date|string|date_format:Y-m-d|after_or_equal:from_date",
            "card_serial_no" => "string",
            "store_id" => "nullable",
            'extra_store_id' => 'nullable',
            "limit" => "int",
            'see_all' => 'integer|in:1,0',
            "export" => "in:1,0|nullable",
            "export_data" => "required_if:export,==,1",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $list = Models\ExTransfer::getExTransferList($request->all());
        $data["data"] = $list;
        abort(200, json_encode($data));
    }


    public function addExTransferPos(Request $request)
    {
        $nowTime = date('H:i:s');
        $walletData = [];
        $productData = [];
        $userDetail = [];
        $exMemberId = null;
        if (in_array(MODULE, ['user', 'app'])) {
            $user_id = auth()->user()->id ?? NUll;
            $request->request->add(['user_id' => $user_id]);
            $validateCol = "member";
        } else {
            $validateCol = "admin";
        }


        // Special Handle for Page without Credit ID
        if (!isset($request->credit_id)) {
            $creditFunction = Models\CreditSetting::where([
                'name' => 'is-ex-transferable',
                'value' => '1',
                $validateCol => '1',
            ])->first();

            if (empty($creditFunction)) {
                abort(400, json_encode(Lang::get('lang.function-unavailable')));
            }

            $request->request->add(['credit_id' => ($creditFunction->credit_id ?? 0)]);
        }

        if (isset($request->amount)) {
            $request->request->add(['amount' => (string) floatval(str_replace(',', '', $request->amount))]);
        }
        if (isset($request->password)) {
            $request->request->add(['password' => Str::upper($request->password)]);
        }

        $validator = Validator::make($request->all() + ['module' => MODULE], [
            'step' => 'integer|required|in:1,2',
            'user_id' => [
                'required',
                'integer',
                function ($q, $value, $fail) use ($request, &$userDetail) {

                    $checkUser = Models\User::with(['userDetail'])->where('id', $value)->whereIN('user_type', [Models\User::$userType['user-account']])->get()->map(function ($q) use (&$userDetail) {
                        foreach ($q->userDetail->keyBy('name') as $name => $value) {
                            $userDetail[$name] = $value->value;
                        }
                        return $q;
                    })->first();
                    if (empty($checkUser)) {
                        $fail(Lang::get('validation.exists'));
                        return;
                    }

                    $isShopOwner = Models\Store::where('store_id', $checkUser->store_id)->where('user_id', $checkUser->id)->exists();
                    if (!$isShopOwner) {
                        $fail(Lang::get('Action denied: You must be the owner of the store to perform this transfer'));
                        return;
                    }
                }
            ],
            // 'user_id' => 'required|exists:users,id',
            'card_id' => [
                'required',
                // 'exists:user_card,id,user_id,'.($request->user_id ?? ''),
                function ($q, $value, $fail) use ($request, &$cardRes, &$walletData) {
                    $authUser = auth()->user();
                    $activeCard = Models\UserCard::where('status', Models\UserCard::$status['active'])
                        ->find($value) ?? null;
                    $cardRes = $activeCard;

                    if (!isset($cardRes)) {
                        $fail(Lang::get('lang.error-invalid-card-id'));
                        return;
                    }

                    $user = Models\User::find($activeCard->user_id);
                    $storeId = $user->store_id;
                    // $result = Models\UserCard::getAllCardFrom3rd($storeId, $user); // Need to add back

                    $isSameStore = Models\User::where('id', $user->id)
                        ->where('store_id', $authUser->store_id) // Ensure the user is in the same store
                        ->first() ?? null;

                    $isSameUser = $user->id == $authUser->id;

                    $isCardSameStore = $activeCard->store_id == $authUser->store_id;

                    // dd(json_encode($walletData));

                    $walletData = [
                        'card_serial_no' => $activeCard->card_serial_no,
                        'card_name' => $activeCard->card_name,
                        'name' => $user->name,
                        'phone_number' => $activeCard->phone_no
                    ];


                    if (empty($isSameStore) || !$isCardSameStore) {
                        $fail(Lang::get('The user or card is invalid or does not belong to the same store.'));
                        return;
                    }

                    if ($isSameUser) {
                        $fail(Lang::get('You cannot transfer to yourself.'));
                        return;
                    }




                    // $type = 'check_game_credit';
                    // $curlParams = [
                    //     'storeId' => $cardRes->store_id,
                    //     'cardId' => $cardRes->card_id,
                    // ];
                    // $result = Traits\SonicTrait::post($curlParams,$type);
                    // if(!isset($result['status']) || $result['status'] == false){
                    //     return $result;
                    // }

                    // $productData['balance'] = $result['data']['credit'] ?? 0;
                }
            ],
            // 'card_id' => [
            //     'required',
            //     function ($q, $value, $fail) use ($request) {

            //         $card = Models\UserCard::find($value);
            //         if(!isset($card)){
            //             $fail(Lang::get("validation.exists"));
            //         }
            //     },
            // ],
            'credit_id' => [
                'required',
                'integer',
                'exists:credit,id',
                function ($q, $value, $fail) use ($request, $validateCol, &$walletData, &$isExTransferable, &$transferableProduct, &$exTransferableMinAmt, &$exTransferableMultiplier) {
                    $isExTransferable =  false;
                    $transferableProduct =  [];
                    $exTransferableMinAmt =  null;
                    $exTransferableMultiplier =  null;

                    $credit = Models\Credit::with([
                        'creditSetting' => function ($q) use ($validateCol) {
                            $q->whereIn('name', ['is-ex-transferable', 'ex-transfer-min-amt', 'ex-transfer-multiplier']);
                            $q->where($validateCol, '1');
                        },
                    ])
                        ->where('id', $request->credit_id ?? 0)
                        ->get();
                    $credit->map(function ($q) use (&$fail, $request, &$isExTransferable, &$transferableProduct, &$exTransferableMinAmt, &$exTransferableMultiplier) {
                        $q->creditSetting->map(function ($credit_setting) use (&$fail, $request, &$isExTransferable, &$transferableProduct, &$exTransferableMinAmt, &$exTransferableMultiplier) {
                            switch ($credit_setting->name) {
                                case 'is-ex-transferable':
                                    if ($credit_setting->value == 1) {
                                        $transferableProduct = json_decode($credit_setting->type, true);
                                        $isExTransferable = true;
                                    }
                                    break;

                                case 'ex-transfer-min-amt':
                                    $exTransferableMinAmt = $credit_setting->value;
                                    break;

                                case 'ex-transfer-multiplier':
                                    $exTransferableMultiplier = $credit_setting->value;
                                    break;
                            }
                        });
                    });
                    if (!$isExTransferable) {
                        $fail(Lang::get('lang.function-unavailable'));
                        return;
                    }

                    $credit = $credit->first();
                    $creditType = $credit->type ?? NULL;

                    $walletAmt = Models\Credit::getBalance($request->user_id, $creditType);
                    $walletData['credit_id'] = $value;
                    $walletData['credit_name'] = $credit->name;
                    $walletData['credit_type'] = $credit->type;
                    $walletData['credit_display'] = Lang::has('lang.' . $credit->name) ? Lang::get('lang.' . $credit->name) : $credit->name;
                    $walletData['balance'] = $walletAmt;
                    // $walletData = [
                    //     'credit_id' => $value,
                    //     'credit_name' => $credit->name,
                    //     'credit_type' => $credit->type,
                    //     'credit_display' => Lang::has('lang.' . $credit->name) ? Lang::get('lang.' . $credit->name) : $credit->name,
                    //     'balance' => $walletAmt,
                    // ];

                }
            ],
            'type' => [
                Rule::requiredIf(($request->step >= 1)),
                'integer',
                'in:' . implode(',', Models\ExTransfer::$type)
            ],

        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $validator = Validator::make($request->all(), [
            'step' => ['required'],
            'user_id' => ['required'],
            'card_id' => ['required'],
            'credit_id' => ['required'],
            'type' => ['required'],
            'amount' => [
                Rule::requiredIf(($request->step == 2) && ($request->type == Models\ExTransfer::$type['in'])),
                'string',
                'gt:0',
                'regex:/^(\\d+\\.?\\d{0,2})$/',
                function ($q, &$value, $fail) use ($request, &$walletData, &$cardRes, &$exTransferableMinAmt, &$exTransferableMultiplier) {

                    $balance = $walletData['balance'] ?? 0;

                    $type = array_search($request->type, Models\ExTransfer::$type);

                    switch ($type) {
                        case 'in':
                            if (isset($exTransferableMinAmt)) {
                                if ($value < ($exTransferableMinAmt)) {
                                    $fail(Lang::get('lang.min-card-reload-error', ['value' => $exTransferableMinAmt]));
                                    return;
                                }
                            }

                            if (isset($exTransferableMultiplier)) {
                                $cmpValue = bcdiv($value, $exTransferableMultiplier);
                                if ($cmpValue != (int)($cmpValue)) {
                                    $fail(Lang::get('lang.amount-not-in-multiplier'));
                                    return;
                                }
                            }

                            if ($balance < ($value)) {
                                $fail(Lang::get('lang.credit-insufficient-balance'));
                                return;
                            }
                            break;

                        case 'out':
                            $type = 'check_game_credit';

                            if (isset($exTransferableMinAmt)) {
                                if ($value < ($exTransferableMinAmt)) {
                                    $fail(Lang::get('lang.min-card-withdraw-error', ['value' => $exTransferableMinAmt]));
                                    return;
                                }
                            }

                            $curlParams = [
                                'storeId' => $cardRes->store_id,
                                'cardId' => $cardRes->card_id,
                            ];
                            $result = Traits\SonicTrait::post($curlParams, $type);
                            if (!isset($result['status']) || $result['status'] == false) {
                                return $result;
                            }

                            $prodBalance = $result['data']['credit'] ?? 0;

                            if ($prodBalance < ($value)) {
                                // if($prodBalance <= 0){
                                $fail(Lang::get('lang.credit-insufficient-balance'));
                                return;
                            }
                            break;
                    }
                }
            ],
            // 'password' => [
            //     Rule::requiredIf(($request->step >= 2)),
            //     function ($q, &$value, $fail) use($request, &$walletData, &$cardRes, &$exTransferableMinAmt, &$exTransferableMultiplier){
            //         $type = 'validate_user';
            //         $curlParams = [
            //             'storeId' => $cardRes->store_id,
            //             'cardId' => $cardRes->card_id,
            //         ];

            //         $result = Traits\SonicTrait::post($curlParams,$type);
            //         if(!isset($result['status']) || $result['status'] == false || empty($result['data'])){
            //             $fail(Lang::get('validation.exists'));
            //             return;
            //         }

            //         $password = $result['data'][0]['member_password'];
            //         if($password != $value){
            //             $fail(Lang::get('invalid-credentials'));
            //             return;
            //         }
            //     },
            // ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if ($validator->validated()['step'] == 1) {
            $returnData = [
                "to_card_name" => $walletData['card_name'],
                "to_card_serial_no" => $walletData['card_serial_no'],
                "to_name" => $walletData['name'],
                "to_phone_number" => $walletData['phone_number'],
                "from_balance" => $walletData['balance'],
            ];

            abort(200, json_encode($returnData));
        }

        if ($validator->validated()['step'] == 2) {
            $exists = Models\ApiStatus::where('user_id', $request->user_id)->where('api_path', $request->path())->where('status', Models\ApiStatus::$status['pending'])->first();
            if ($exists) {
                abort(400, Lang::get('lang.duplicate-api-detected'));
            } else {
                $apiCreated = Models\ApiStatus::create([
                    'user_id' => $request->user_id,
                    'api_path' => $request->path(),
                    'status' => Models\ApiStatus::$status['pending'],
                ]);
            }
            try {
                $res = Models\ExTransfer::add($validator->validated() + ["wallet_data" => $walletData, "wallet_type" => $request->wallet_type]);
            } catch (\Throwable $e) {
                Models\ApiStatus::find($apiCreated->id)->update(['status' => Models\ApiStatus::$status['failed']]);
                throw ($e);
            }
            Models\ApiStatus::find($apiCreated->id)->update(['status' => Models\ApiStatus::$status['success']]);
        }
        // $res = null;
        abort(200, (isset($res) ? json_encode(["data" => $res]) : ""));
    }

    /* This is needed to generate API docs
        * ##docs start##
        * @postmanName = External Transfer (Deposit & Withdraw)
        * @module = user,app
        * @path = card/add-ex-transfer
        * @method = post
        * @description = To transfer credit to sonic.

        * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
        * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
        * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

        * @body = step|<step>|int|required|1,2|Step 1 = Validate Input Data, Step 2 = Confirmation.
        * @body = card_id|<card_id>|int|optional|1000|Card Id to tranfer (Step 1 required).
        * @body = type|<type>|int|required|1,2|In or Out (Step 1 required)(Get From Dropdown : ex_transfer_type).
        * @body = amount|<amount>|int|required|100|Credit Amount want to transfer (Step 1 required).

        * @response = {"data":{"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS9wcm9kdWN0L2FkZC1leC10cmFuc2ZlciIsImlhdCI6MTY4NzMxMDgxNiwiZXhwIjoxNjg3MzE1Mjg1LCJuYmYiOjE2ODczMTE2ODUsImp0aSI6Imc0UHZ4RHI1WlNJMDZVTTgiLCJzdWIiOiIxMDAwMDUzIiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.6Oz270H0L4-qROjK4m_jHaef-1aYjO1NE0p94gQLfuI","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.14557218551636 sec","log_id":"1bbc1d44-59db-4a29-9b23-61f70aaf766c"}
        * ##docs end##
    */
    // * @body = credit_id|<credit_id>|int|optional|1000|Credit Unique Id for credit want to tranfer (Step 1 required).
    public function addExTransfer(Request $request)
    {
        $nowTime = date('H:i:s');
        $walletData = [];
        $productData = [];
        $userDetail = [];
        $exMemberId = null;
        if (in_array(MODULE, ['user', 'app'])) {
            $user_id = auth()->user()->id ?? NUll;
            $request->request->add(['user_id' => $user_id]);
            $validateCol = "member";
        } else {
            $validateCol = "admin";
        }

        // Special Handle for Page without Credit ID
        if (!isset($request->credit_id)) {
            $creditFunction = Models\CreditSetting::where([
                'name' => 'is-ex-transferable',
                'value' => '1',
                $validateCol => '1',
            ])->first();

            if (empty($creditFunction)) {
                abort(400, json_encode(Lang::get('lang.function-unavailable')));
            }

            $request->request->add(['credit_id' => ($creditFunction->credit_id ?? 0)]);
        }

        $user = auth()->user();
        $settings = Store::where('store_id', $user->store_id)->first();
        if ($settings->is_allow_withdraw == 0) {
            abort(400, json_encode(Lang::get('lang.function-unavailable')));
        }

        if (isset($request->amount)) {
            $request->request->add(['amount' => (string) floatval(str_replace(',', '', $request->amount))]);
        }
        if (isset($request->password)) {
            $request->request->add(['password' => Str::upper($request->password)]);
        }

        $validator = Validator::make($request->all() + ['module' => MODULE], [
            'step' => 'integer|required|in:1,2',
            'user_id' => [
                'required',
                'integer',
                function ($q, $value, $fail) use ($request, &$userDetail) {
                    $checkUser = Models\User::with(['userDetail'])->where('id', $value)->whereIN('user_type', [Models\User::$userType['user-account']])->get()->map(function ($q) use (&$userDetail) {
                        foreach ($q->userDetail->keyBy('name') as $name => $value) {
                            $userDetail[$name] = $value->value;
                        }
                        return $q;
                    })->first();
                    if (empty($checkUser)) {
                        $fail(Lang::get('validation.exists'));
                        return;
                    }
                }
            ],
            // 'user_id' => 'required|exists:users,id',
            'card_id' => [
                'required',
                // 'exists:user_card,id,user_id,'.($request->user_id ?? ''),
                function ($q, $value, $fail) use ($request, &$cardRes) {
                    $user = auth()->user();
                    $storeId = $user->store_id;
                    $result = Models\UserCard::getAllCardFrom3rd($storeId, $user);
                    $cardRes = Models\UserCard::where('status', Models\UserCard::$status['active'])->find($value);

                    if (!isset($cardRes)) {
                        $fail(Lang::get('lang.error-invalid-card-id'));
                        return;
                    }

                    // $type = 'check_game_credit';
                    // $curlParams = [
                    //     'storeId' => $cardRes->store_id,
                    //     'cardId' => $cardRes->card_id,
                    // ];
                    // $result = Traits\SonicTrait::post($curlParams,$type);
                    // if(!isset($result['status']) || $result['status'] == false){
                    //     return $result;
                    // }

                    // $productData['balance'] = $result['data']['credit'] ?? 0;
                }
            ],
            // 'card_id' => [
            //     'required',
            //     function ($q, $value, $fail) use ($request) {

            //         $card = Models\UserCard::find($value);
            //         if(!isset($card)){
            //             $fail(Lang::get("validation.exists"));
            //         }
            //     },
            // ],
            'credit_id' => [
                'required',
                'integer',
                'exists:credit,id',
                function ($q, $value, $fail) use ($request, $validateCol, &$walletData, &$isExTransferable, &$transferableProduct, &$exTransferableMinAmt, &$exTransferableMultiplier) {
                    $isExTransferable =  false;
                    $transferableProduct =  [];
                    $exTransferableMinAmt =  null;
                    $exTransferableMultiplier =  null;

                    $credit = Models\Credit::with([
                        'creditSetting' => function ($q) use ($validateCol) {
                            $q->whereIn('name', ['is-ex-transferable', 'ex-transfer-min-amt', 'ex-transfer-multiplier']);
                            $q->where($validateCol, '1');
                        },
                    ])
                        ->where('id', $request->credit_id ?? 0)
                        ->get();
                    $credit->map(function ($q) use (&$fail, $request, &$isExTransferable, &$transferableProduct, &$exTransferableMinAmt, &$exTransferableMultiplier) {
                        $q->creditSetting->map(function ($credit_setting) use (&$fail, $request, &$isExTransferable, &$transferableProduct, &$exTransferableMinAmt, &$exTransferableMultiplier) {
                            switch ($credit_setting->name) {
                                case 'is-ex-transferable':
                                    if ($credit_setting->value == 1) {
                                        $transferableProduct = json_decode($credit_setting->type, true);
                                        $isExTransferable = true;
                                    }
                                    break;

                                case 'ex-transfer-min-amt':
                                    $exTransferableMinAmt = $credit_setting->value;
                                    break;

                                case 'ex-transfer-multiplier':
                                    $exTransferableMultiplier = $credit_setting->value;
                                    break;
                            }
                        });
                    });
                    if (!$isExTransferable) {
                        $fail(Lang::get('lang.function-unavailable'));
                        return;
                    }

                    $credit = $credit->first();
                    $creditType = $credit->type ?? NULL;

                    $walletAmt = Models\Credit::getBalance($request->user_id, $creditType, null, null);
                    if ($walletAmt > 0) {
                        // Check Last Transaction config('subject')['free-credit'] = 101;
                        $lastTransaction = Models\CreditTransaction::where('user_id', $request->user_id)->orderBy('created_at', 'desc')->first();
                        if (isset($lastTransaction->subject_type) && ($lastTransaction->subject_type == config('subject')['free-credit'])) {
                            $walletAmt = $walletAmt - $lastTransaction->amount;
                        }
                    }

                    $walletData = [
                        'credit_id' => $value,
                        'credit_name' => $credit->name,
                        'credit_type' => $credit->type,
                        'credit_display' => Lang::has('lang.' . $credit->name) ? Lang::get('lang.' . $credit->name) : $credit->name,
                        'balance' => $walletAmt,
                    ];
                }
            ],
            'type' => [
                Rule::requiredIf(($request->step >= 1)),
                'integer',
                'in:' . implode(',', Models\ExTransfer::$type)
            ],

        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $validator = Validator::make($request->all(), [
            'step' => ['required'],
            'user_id' => ['required'],
            'card_id' => ['required'],
            'credit_id' => ['required'],
            'type' => ['required'],
            'amount' => [
                Rule::requiredIf(($request->step >= 1) && ($request->type == Models\ExTransfer::$type['in'])),
                'string',
                'gt:0',
                'regex:/^(\\d+\\.?\\d{0,2})$/',
                function ($q, &$value, $fail) use ($request, &$walletData, &$cardRes, &$exTransferableMinAmt, &$exTransferableMultiplier) {
                    $balance = $walletData['balance'] ?? 0;

                    $type = array_search($request->type, Models\ExTransfer::$type);

                    switch ($type) {
                        case 'in':
                            if (isset($exTransferableMinAmt)) {
                                if ($value < ($exTransferableMinAmt)) {
                                    $fail(Lang::get('lang.min-card-reload-error', ['value' => $exTransferableMinAmt]));
                                    return;
                                }
                            }

                            if (isset($exTransferableMultiplier)) {
                                $cmpValue = bcdiv($value, $exTransferableMultiplier);
                                if ($cmpValue != (int)($cmpValue)) {
                                    $fail(Lang::get('lang.amount-not-in-multiplier'));
                                    return;
                                }
                            }

                            if ($balance < ($value)) {
                                $fail(Lang::get('lang.credit-insufficient-balance'));
                                return;
                            }
                            break;

                        case 'out':
                            $type = 'check_game_credit';

                            if (isset($exTransferableMinAmt)) {
                                if ($value < ($exTransferableMinAmt)) {
                                    $fail(Lang::get('lang.min-card-withdraw-error', ['value' => $exTransferableMinAmt]));
                                    return;
                                }
                            }

                            $curlParams = [
                                'storeId' => $cardRes->store_id,
                                'cardId' => $cardRes->card_id,
                            ];
                            $result = Traits\SonicTrait::post($curlParams, $type);
                            if (!isset($result['status']) || $result['status'] == false) {
                                return $result;
                            }

                            $prodBalance = $result['data']['credit'] ?? 0;

                            if ($prodBalance < ($value)) {
                                // if($prodBalance <= 0){
                                $fail(Lang::get('lang.credit-insufficient-balance'));
                                return;
                            }
                            break;
                    }
                }
            ],
            // 'password' => [
            //     Rule::requiredIf(($request->step >= 2)),
            //     function ($q, &$value, $fail) use($request, &$walletData, &$cardRes, &$exTransferableMinAmt, &$exTransferableMultiplier){
            //         $type = 'validate_user';
            //         $curlParams = [
            //             'storeId' => $cardRes->store_id,
            //             'cardId' => $cardRes->card_id,
            //         ];

            //         $result = Traits\SonicTrait::post($curlParams,$type);
            //         if(!isset($result['status']) || $result['status'] == false || empty($result['data'])){
            //             $fail(Lang::get('validation.exists'));
            //             return;
            //         }

            //         $password = $result['data'][0]['member_password'];
            //         if($password != $value){
            //             $fail(Lang::get('invalid-credentials'));
            //             return;
            //         }
            //     },
            // ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        if ($validator->validated()['step'] == 2) {
            $exists = Models\ApiStatus::where('user_id', $request->user_id)->where('api_path', $request->path())->where('status', Models\ApiStatus::$status['pending'])->first();
            if ($exists) {
                abort(400, Lang::get('lang.duplicate-api-detected'));
            } else {
                $apiCreated = Models\ApiStatus::create([
                    'user_id' => $request->user_id,
                    'api_path' => $request->path(),
                    'status' => Models\ApiStatus::$status['pending'],
                ]);
            }
            try {
                $res = Models\ExTransfer::add($validator->validated() + ["wallet_data" => $walletData, "wallet_type" => $request->wallet_type]);
            } catch (\Throwable $e) {
                Models\ApiStatus::find($apiCreated->id)->update(['status' => Models\ApiStatus::$status['failed']]);
                throw ($e);
            }
            Models\ApiStatus::find($apiCreated->id)->update(['status' => Models\ApiStatus::$status['success']]);
        }

        // $res = null;
        abort(200, (isset($res) ? json_encode(["data" => $res]) : ""));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = card/get-ex-transfer-data
     * @module = user,app
     * @path = card/get-ex-transfer-data
     * @method = post
     * @description = To get ex transfer data.
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
     * @body = credit_id|<credit_id>|integer|required|1000|Credit's id.

     * @response = {"data":{"option":{"online-bank":{"currency_list":[{"currency":"MYR","min":"10.000000","max":"10000.000000"}],"option_display":"Online Banking"},"manual-bank":{"bank_name":"fw Sdn Bhd","bank_account_name":"Maybank Berhad","bank_account_number":"1234 7890 4567","option_display":"Cash Deposit"},"ewallet":{"currency_list":[{"currency":"MYR","min":"10.000000","max":"10000.000000"}],"option_display":"E-Wallet"}},"min-fundin":"1.00","fundin-charge":"0.00","balance":"9999979.********"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************.a34jY60pW0rgVEQnFZ-DheEmCz8LMnXpQJIcsQxTl9o","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"staging","execution_duration":"0.************** sec","log_id":"1f4e17b0-c40f-4a97-8ab7-e638a766af8d"}
     * ##docs end##
     */
    public function getExTransferData(Request $request)
    {
        if (MODULE == 'user' || MODULE == 'app') $request->request->add(['user_id' => auth()->user()->id]);

        if (!isset($request->credit_id)) {
            $creditId = Models\Credit::where('name', 'myr-credit')->first()->id ?? null;
            $request->request->add(['credit_id' => $creditId]);
        }

        $validator = Validator::make($request->all(), [
            "user_id" => "int|required|exists:users,id",
            "credit_id" => [
                'int',
                Rule::requiredIf(function () use ($request) {
                    $creditCount = Models\CreditSetting::where(["name" => array("is-fundinable"), "value" => 1])->count();
                    return $creditCount > 1;
                }),
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        try {
            $res = Models\ExTransfer::getExTransferData($validator->validated());
        } catch (\Exception $e) {
            abort($e->getCode(), json_encode($e->getMessage()));
        }

        abort(200, json_encode($res));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Store List
     * @module = admin
     * @path = card/store-list
     * @permissionName = Get store list
     * @menuType = api
     * @method = POST
     * @description = To get store list.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = username|<username>|string|optional|609937401199|User name.
     * @body = store_name|<store_name>|string|optional|superdog|Store name.
     * 
     * @response = {"list":[{"store_name":"StoreName1","username":"609929127999","store_id":1},{"store_name":"test","username":"609937401199","store_id":2},{"store_name":"les","username":"609974968099","store_id":3},{"store_name":"locl","username":"609996797399","store_id":4}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":4,"total":4},"meta":null,"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZ3LmNvbS9jYXJkL3N0b3JlLWxpc3QiLCJpYXQiOjE3MjQ5MDQyNjcsImV4cCI6MTc1NjQ2NDMxMCwibmJmIjoxNzI0OTA0MzEwLCJqdGkiOiJCY1RlaW9lcDRmQ25RNGdzIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.MxTv_7sS8qskDViCHbmOJD1hS6owoB7O2h-zyNy-stE","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.025635004043579 sec","log_id":"df90dab0-740f-45e0-a588-4cacb55abac6","valid_version":false}
     * ##docs end##
     */
    public function getStoreList(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'store_name' => ['nullable'],
            'username' => ['nullable'],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\Store::getList($validator->validated());
        abort(200, json_encode($res));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Store Refresh
     * @module = admin
     * @path = card/store-refresh
     * @permissionName = Store Refresh
     * @menuType = api
     * @method = POST
     * @description = To get store refresh.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @response = {"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZ3LmNvbS9jYXJkL3N0b3JlLXJlZnJlc2giLCJpYXQiOjE3MjQ4MjcyMTYsImV4cCI6MTc1NjM4NzQxOSwibmJmIjoxNzI0ODI3NDE5LCJqdGkiOiJlaU5TOHdzank3aE50NEJRIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.9Dz6qV1K7tP7ZokHZ1bP7onzVlRprhaYmP0SRXlwYzU","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.62796211242676 sec","log_id":"f520d20a-66bb-4b3b-b091-0cb92bfa800d","valid_version":false}
     * ##docs end##
     */
    public function refreshStore(Request $request)
    {
        Models\Store::refreshStore();
        $res = null;
        abort(200, json_encode($res));
    }
}
