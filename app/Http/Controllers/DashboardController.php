<?php

namespace App\Http\Controllers;

use App\Models\AccountBalance;
use App\Models\AngpauEvent;
use App\Models\Announcement;
use App\Models\Banner;
use App\Models\Credit;
use App\Models\CurrencyRateHourly;
use App\Models\Deposit;
use App\Models\ExTransfer;
use App\Models\GameCategory;
use App\Models\Memo;
use App\Models\Product;
use App\Models\Services;
use App\Models\Store;
use App\Models\SystemSetting;
use App\Models\User;
use App\Models\UserLevel;
use App\Models\UserNotification;
use App\Models\UserProduct;
use App\Models\UserPromotion;
use App\Models\UserTicketReward;
use App\Models\WWJAccountBetTransaction;
use App\Services\GameProvider\GSC;
use App\Services\GameProvider\JILI;
use App\Services\GameProvider\JK;
use App\Services\GameProvider\MT;
use App\Traits;
use App\Traits\DateTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class DashboardController extends Controller
{
    use DateTrait;

    public function dashboardSwitcher(Request $request)
    {
        $isDashboardV2 = false;

        if (defined('MODULE') && MODULE == 'app') {
            $v2Users = json_decode(SystemSetting::where('name', 'v2AuthorizedUser')->whereNull('deleted_at')->first()['value'] ?? '') ?? [];
            if (in_array(auth()->user()->username ?? '', $v2Users)) {
                $isDashboardV2 = true;
            }
        }

        if ($isDashboardV2) {
            return $this->getDashboardV2($request);
        } else {
            return $this->getDashboard($request);
        }
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Get Dashboard
     *
     * @module = user,app
     *
     * @path = get-dashboard
     *
     * @method = POST
     *
     * @description = To get dashboard details for user.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @response = {"data":{"user_detail":{"id":1000055,"member_id":"337335291","name":"test200","username":"60124000074","phone_no":"60-124000074","country":"malaysia","country_display":"Malaysia"},"referral_link":"https:\/\/member.fw.com\/\/?r=y0eona","referral_link_percentage":"0.00","wallet_balance":[{"id":1000,"name":"myr-credit","display":"MYR Wallet","balance":"10.00","iso":"MYR","credit_setting":[{"credit_id":1000,"name":"is-wallet","value":"1","member":1,"reference":null},{"credit_id":1000,"name":"show-transaction-history","value":"1","member":1,"reference":null},{"credit_id":1000,"name":"is-transferable","value":"1","member":1,"reference":null},{"credit_id":1000,"name":"is-fundinable","value":"1","member":1,"reference":null},{"credit_id":1000,"name":"is-withdrawable","value":"1","member":1,"reference":null},{"credit_id":1000,"name":"is-convertible","value":"0","member":0,"reference":null},{"credit_id":1000,"name":"is-exchangable","value":"1","member":1,"reference":null},{"credit_id":1000,"name":"is-ex-transferable","value":"1","member":1,"reference":"1,2"}]}],"service_list":[{"name":"game","display":"Game","url":null,"icon":"https:\/\/fw-s3-pub.s3.ap-southeast-1.amazonaws.com\/staging\/2024\/07\/1721284821_4947","status":"active","isTK8":0,"showCashInOutWarning":0,"product_id":2001,"product_name":"BO42"},{"name":"support","display":"Support","url":"https:\/\/wa.me\/60123228192","icon":"https:\/\/fw-s3-pub.s3.ap-southeast-1.amazonaws.com\/staging\/2024\/07\/1721285575_9784","status":"active","isTK8":0,"showCashInOutWarning":0},{"name":"entertainment2","display":"Entertainment2","url":"https:\/\/www.youtube.com","icon":"https:\/\/fw-s3-pub.s3.ap-southeast-1.amazonaws.com\/staging\/2024\/07\/1721286048_8568","status":"active","isTK8":0,"showCashInOutWarning":0},{"name":"test-xuan","display":"test xuan","url":"https:\/\/www.google.com","icon":"https:\/\/fw-s3-pub.s3.ap-southeast-1.amazonaws.com\/staging\/2024\/08\/1723455396_2554","status":"active","isTK8":0,"showCashInOutWarning":1,"product_id":2005,"product_name":"711LOT"},{"name":"checktest","display":"checktest","url":"https:\/\/www.wikipedia.org\/","icon":"https:\/\/fw-s3-pub.s3.ap-southeast-1.amazonaws.com\/staging\/2024\/08\/1723467170_4664","status":"coming-soon","isTK8":0,"showCashInOutWarning":1,"product_id":2005,"product_name":"711LOT"}],"rank_requirement":[{"id":2,"name":"level-0","display":"Level 0","min_deposit":"1"},{"id":3,"name":"level-1","display":"Level 1","min_deposit":"1000"},{"id":4,"name":"level-2","display":"Level 2","min_deposit":"10000"},{"id":5,"name":"level-3","display":"Level 3","min_deposit":"30000"}],"total_deposit":"0","rank":{"id":8,"name":"vip-0","display":"VIP 0","is_completed":0,"status":"in-progress","status_display":"In Progress"},"entitled_sp_rank":0,"sp_rank":{"label":null},"entitled_agent_rank":0,"agent_rank":null,"banner":[],"announcement":[],"memo":[],"notification_count":0},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vYXBpLWFwcC52bW9zZS5jb20vZ2V0LWRhc2hib2FyZCIsImlhdCI6MTcyMzgwMDEyMiwiZXhwIjoxNzU1MzYwMTI4LCJuYmYiOjE3MjM4MDAxMjgsImp0aSI6IklnenVBMTZNSzZZaFpjYzYiLCJzdWIiOiIxMDAwMDU1IiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.h91czT6Zw5yoSxK1bvSkLhp9qcQ3G9Vc_kqnWmkIVkI","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"staging","execution_duration":"0.34453105926514 sec","log_id":"d8d2017a-e2fe-445d-9ece-34e1c8de5226","valid_version":false}
     * ##docs end##
     */
    public function getDashboard(Request $request)
    {
        $user = auth()->user() ?? null;
        $user_id = $user?->id ?? null;
        $isAgent = $user->is_agent;
        $dashboardType = isset($user_id) ? 'dashboard' : 'homepage';
        $currentDate = date('Y-m-d');

        if ($dashboardType == 'dashboard') {
            // ############### User Detail ################
            $userDetail = User::getProfile($user_id);
            $data['data']['user_detail'] = Arr::only($userDetail, ['id', 'member_id', 'name', 'username', 'phone_no', 'country', 'country_display', 'store']);
        }

        $balance = Credit::getBalance($user_id, Credit::first()->type);
        AccountBalance::updateBalance($user_id, $balance);

        // ############### Store Data ################
        $data['data']['store'] = auth()->user()->store;

        $data['data']['service_ui'] = [
            'is_allow_my_card' => 1,
            'is_allow_deposit' => $data['data']['store']->is_allow_deposit,
            'is_allow_withdraw' => $data['data']['store']->is_allow_withdraw,
            'is_allow_transfer' => $data['data']['store']->is_wallet_transferable,
            'is_allow_rebate' => 1,
            'is_allow_promotion' => 1,
            'is_allow_referral' => 1,
            'is_allow_vip' => 1,
        ];

        // ############### Wallet Data ################
        $creditWallet = Credit::getWalletList(true);
        $data['data']['wallet_balance'] = array_reverse(array_values($creditWallet['wallet_data']->toArray()));

        // ############### Services Data ################
        $byPassProduct = Product::where('code', '711LOT')->first();
        $cashInOutWarningProduct = $byPassProduct;
        $servicesList = Services::whereIn('status', Arr::except(Services::$status, ['inactive']))
            ->where('is_homepage', 1)
            ->orderBy('priority', 'ASC')
            ->get()
            ->map(function ($q) use ($user_id, $byPassProduct, $cashInOutWarningProduct) {
                $status = array_search($q->status, Services::$status) ?? null;

                $productID = null;
                $productName = null;

                $isTK8 = 0;
                $showCashInOutWarning = 0;
                if ($q->name == 'entertainment') {
                    $userProductID = UserProduct::whereRelation('product', 'name', 'TK8')->with(['product:id,name,image_url'])->select(['id', 'product_id', 'member_id', 'balance', 'updated_at'])->where('user_id', ($user_id ?? 0))->where('status', UserProduct::$status['success'])->first()->id ?? null;
                    if (! isset($userProductID)) {
                        return false;
                    }
                    $isTK8 = 1;
                }

                if (isset($q->product_id)) {
                    if ($q->product_id == ($byPassProduct->id ?? '')) {
                        $productID = $q->product_id;
                        $productName = $byPassProduct->name;
                    } else {
                        // $userProduct = UserProduct::whereRelation('product', 'id', $q->product_id)->with(['product:id,name,image_url'])->select(['id','product_id','member_id','balance','updated_at'])->where('user_id', ($user_id ?? 0))->where('status', UserProduct::$status['success'])->first() ?? null;
                        $userProduct = UserProduct::whereRelation('product', 'id', $q->product_id)
                            ->with(['product:id,name,image_url'])
                            ->select(['id', 'product_id', 'member_id', 'balance', 'updated_at'])
                            ->where('user_id', ($user_id ?? 0))
                            // ->where('status', UserProduct::$status['success'])
                            ->first() ?? null;

                        if (! isset($userProduct)) {
                            return false;
                        }
                        $productID = $q->product_id;
                        $productName = $userProduct->product->name;
                    }

                    if ($q->product_id == ($cashInOutWarningProduct->id ?? '')) {
                        $showCashInOutWarning = 1;
                    }
                }

                $data = [
                    'service_id' => $q->id,
                    'name' => $q->name,
                    'display' => Lang::has('langCustom.service-' . $q->name) ? Lang::get('langCustom.service-' . $q->name) : $q->name,
                    'url' => $q->url,
                    'icon' => isset($q->icon) ? env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $q->icon : null,
                    'status' => $status,
                    'isTK8' => $isTK8,
                    'showCashInOutWarning' => $showCashInOutWarning,
                ];
                if (isset($productID)) {
                    $data['product_id'] = (int) $productID;
                }
                if (isset($productName)) {
                    $data['product_name'] = $productName;
                }

                return $data;
            })->reject(function ($value) {
                return $value === false;
            });
        $data['data']['service_list'] = array_values($servicesList->toArray());

        // ############### Game Category ################

        $hot_games = [
            'id' => 0,
            'name' => 'Hot Games',
            'display' => 'Hot Games',
            'is_game_list' => true,
            'list' => Services::getHotServices(),
        ];

        $storeIsKilled = Store::where('store_id', auth()->user()->store_id)->where('is_disable', '0')->get(); // Compare user store id if disable will hide game
        $game_list = null;

        if (auth()->user()->restricted == 0 && ! $storeIsKilled->isEmpty()) {
            $game_list = GameCategory::getListByCategory();
        }

        $currentRank = null;
        $store_id = auth()->user()->store_id ?? null;
        $allTransactionId = WWJAccountBetTransaction::with('user')
            ->whereHas('user', function ($q) use ($store_id) {
                $q->where('store_id', $store_id);
            })
            ->orderByDesc('total_bet')
            ->select('user_id')
            ->get()
            ->toArray();

        foreach (array_values($allTransactionId) as $i => $value) {
            if ($value['user_id'] == auth()->user()->id) {
                $currentRank = $i + 1;
            }
        }

        $userTicketReward = UserTicketReward::where('user_id', $user_id)->first();
        $totalDeposit = $userTicketReward?->total_deposit ?? 0;
        $totalTicket = $userTicketReward?->total_token ?? 0;
        // $total_turnover = WWJAccountBetTransaction::where('user_id', auth()->user()->id)->first()?->total_bet ?? 0;
        $user = auth()->user();
        $drawDate = Store::getDrawDateByStoreId($user->store_id);

        $randvideo = rand(1, 7);
        $data['data']['special_game'] = [
            'name' => 'Aljin',
            'display' => 'Aljin',
            'video' => 'https://pub-fe3dae3bdb1c4d3c858a4557ea6443c7.r2.dev/AljinPromo/' . $randvideo . '.mp4',
            'image' => 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/2024/12/1735286891_4870.png',
            'bg_image' => 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/2024/12/image_95.png',
            'service_id' => env('APP_ENV') == 'production' ? 1342 : 1202,
            'product_id' => 2004,
            'turn_over' => [
                'current_rank' => (string) ($currentRank ?? '-'),
                'current_turnover' => (string) Traits\DecimalTrait::setDecimal($total_turnover ?? 0),
            ],
            'top_deposit' => [
                'total_deposit' => (string) $totalDeposit,
                'total_ticket' => $totalTicket,
            ],
            'status' => true,
            // 'status' => ! is_null($drawDate),
            'date_range' => $drawDate,
        ];

        if (auth()->user()->restricted == 1) {
            $data['data']['game_list'] = [];
        } else {
            $data['data']['game_list'] = $storeIsKilled->isEmpty() ? [] : array_values(array_merge([$hot_games], $game_list->toArray() ?? []));
        }

        $depositAmount = Deposit::select(DB::raw('SUM(receivable_amount) as receivable_amount'))->where('user_id', $user_id)->where('status', Deposit::$status['approved'])->first();
        $data['data']['total_deposit'] = $depositAmount['receivable_amount'] ?? '0';

        // ############### Banner ################
        $data['data']['banner'] = Banner::getDashboardBanner();

        // ############### News ################
        $data['data']['announcement'] = Announcement::getAnnouncementList(['limit' => 5, 'dashboard' => true])['list'];

        // ############### Memo ################
        $data['data']['memo'] = Memo::getLoginMemo(auth()->user());

        // ############### Notification ################
        $data['data']['notification_count'] = UserNotification::selectRaw('COUNT(id) AS count')->where('user_id', $user_id)->where('read', 0)->first()->count ?? 0;

        $data['data']['live_url'] = 'https://luckydraw.funwallet.co/section?id=32';

        if (auth()->user()->restricted == 0) {
            if (auth()->user()->store->is_disable == 0) {
                if (in_array(auth()->user()->store->store_id, [29, 30, 31, 6, 7, 8, 12, 20001, 20002, 20003, 20004, 20005, 20006, 20007, 20008, 20009, 20010, 20011])) {
                    $data['data']['popup_banner'] = [
                        [
                            'name' => 'Aljin',
                            'service_id' => null,
                            'product_id' => null,
                            'image' => 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/reward/slot_game_pop_up_202506.jpg',
                        ],
                    ];
                } else {
                    $data['data']['popup_banner'] = [
                        [
                            'name' => 'Aljin',
                            // 'service_id' => 1342,
                            // 'product_id' => 2004,
                            'service_id' => null,
                            'product_id' => null,
                            'image' => 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/reward/slot_game_pop_up_202506.jpg',
                        ],
                    ];
                }
            }
        }

        // $this->restoreProductBalance($user_id);

        // ############### VIP ################
        $data['data']['daily_point'] = UserLevel::claimDailyVipPoint($user_id);
        $data['data']['vip'] = UserLevel::getVipByUserIdOrCreateIfNull($user_id);

        // $userPromotion = UserPromotion::where('user_id', $user_id)
        //     ->where('promotion_id', 1000)
        //     ->whereDate('updated_at', today())
        //     ->first();

        // if ($store_id == 12) {
        //     $data['data']['promotion'] = false;
        // } else {
        //     $data['data']['promotion'] = $userPromotion ? false : true;
        // }

        $angpauEvent = AngpauEvent::getCurrentAngpauEvent();
        $data['data']['promotion'] = (bool) $angpauEvent;
        $data['data']['ls_flag'] = true;
        $data['data']['marquee'] = "Welcome Bonus Free 20 , Happy Hour Promotion 10% ,Daily Rebate up to RM888"; //Lang::has('langReward.dashboard_marquee') ? Lang::get('langReward.dashboard_marquee') : null;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     *
     * @postmanName = Get Dashboard V2
     *
     * @module = user,app
     *
     * @path = get-dashboard
     *
     * @method = POST
     *
     * @description = To get dashboard V2 details for user.
     *
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @response = {"data":{"user_detail":{"id":1000000,"member_id":"324874366","name":"tkNotes","username":"tkNotes","phone_no":"60-1160905696","country":"malaysia","country_display":"Malaysia"},"referral_link":"\/?r=tkNotes","sp_rank":{"rank_id":7,"rank_priority":2,"percentage":"0.2","adminSetRank":1},"entitled_sp_rank":1,"banner":[{"title":"Convert currencies with just a few clicks","web_image":"https:\/\/fw-pub.s3.ap-southeast-1.amazonaws.com\/banner\/800600\/02.jpg","mobile_image":"https:\/\/fw-pub.s3.ap-southeast-1.amazonaws.com\/banner\/2000600\/02.jpg","url":null},{"title":"Keep update with latest rate","web_image":"https:\/\/fw-pub.s3.ap-southeast-1.amazonaws.com\/banner\/800600\/03.jpg","mobile_image":"https:\/\/fw-pub.s3.ap-southeast-1.amazonaws.com\/banner\/2000600\/03.jpg","url":null}],"currency":[{"from":"MYR","from_iso":"MY","to":"GNF","to_iso":"GN","rate":"1835.36893000"},{"from":"MYR","from_iso":"MY","to":"GTQ","to_iso":"GT","rate":"1.68013000"},{"from":"MYR","from_iso":"MY","to":"GYD","to_iso":"GY","rate":"45.42383000"},{"from":"MYR","from_iso":"MY","to":"HKD","to_iso":"HK","rate":"1.67900000"},{"from":"MYR","from_iso":"MY","to":"HNL","to_iso":"HN","rate":"5.27407000"}],"announcement":[{"news_url":"https:\/\/www.fxstreet.com\/news\/eur-usd-maintains-strength-above-11000-as-us-dollar-extends-downside-ahead-of-us-inflation-202307120505","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/2\/y\/f28-148592.jpg","title":"EUR\/USD maintains strength above 1.1000 as US Dollar extends downside ahead of US Inflation","text":"The EUR\/USD pair has confidently shifted above the psychological resistance of 1.1000 in the Asian session. The major currency pair is in a bullish trajectory as the US Dollar Index (DXY) has extended its losses to 101.37 ahead of the United States Consumer Price Index (DXY).","source_name":"FX Street","date":"Wed, 12 Jul 2023 01:05:01","topics":["USA","Europe"],"sentiment":"Positive","type":"Article","currency":["EUR-USD"]},{"news_url":"https:\/\/www.fxstreet.com\/news\/eur-usd-hits-over-two-month-top-around-11030-area-as-usd-selling-remains-unabated-202307120152","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/f\/w\/f8-148572.jpg","title":"EUR\/USD hits over two-month top, around 1.1030 area as USD selling remains unabated","text":"The EUR\/USD pair trades with a positive bias for the fifth successive day on Wednesday and climbs to the 1.1030 region, or its highest level since May 8 during the Asian session.","source_name":"FX Street","date":"Tue, 11 Jul 2023 21:52:38","topics":["USA","Europe"],"sentiment":"Positive","type":"Article","currency":["EUR-USD"]},{"news_url":"https:\/\/www.fxstreet.com\/news\/eur-usd-price-analysis-bears-move-in-at-key-resistance-ahead-of-us-cpi-202307111858","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/e\/6\/f22-148545.jpg","title":"EUR\/USD Price Analysis: Bears move in at key resistance ahead of US CPI","text":"EUR\/USD is flat after its intraday breakout above June's 1.1012 peak as traders get set for the US Consumer Pricfe Index inflaiton data on Wednesday morning. The following ilustrates the market structure into the data and prospects of a correction towards 1.0900.","source_name":"FX Street","date":"Tue, 11 Jul 2023 14:58:57","topics":["USA","Europe"],"sentiment":"Neutral","type":"Article","currency":["EUR-USD"]},{"news_url":"https:\/\/www.fxempire.com\/forecasts\/article\/eur-usd-gbp-usd-usd-cad-usd-jpy-u-s-dollar-is-losing-ground-ahead-of-tomorrows-inflation-reports-1360526","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/9\/h\/dxy-3-1-148533.jpg","title":"EUR\/USD, GBP\/USD, USD\/CAD, USD\/JPY \u2013 U.S. Dollar Is Losing Ground Ahead Of Tomorrow's Inflation Reports","text":"U.S. Dollar Index settled below the important support at the 102 level.","source_name":"FXEmpire","date":"Tue, 11 Jul 2023 12:38:59","topics":["Japan","USA","Canada","United Kingdom","Europe"],"sentiment":"Negative","type":"Article","currency":["EUR-USD","GBP-USD","USD-CAD","USD-JPY"]},{"news_url":"https:\/\/www.fxempire.com\/forecasts\/article\/eur-usd-forecast-euro-gives-up-early-gain-2-1360498","image_url":"https:\/\/forexnewsapi.snapi.dev\/images\/v1\/z\/f\/eur-6-1-148518.jpg","title":"EUR\/USD Forecast \u2013 Euro Gives Up Early Gains","text":"The euro initially tried to rally during the trading session on Tuesday but seems to be at the top of a significant consolidation area.","source_name":"FXEmpire","date":"Tue, 11 Jul 2023 09:58:08","topics":["USA","Europe"],"sentiment":"Negative","type":"Article","currency":["EUR-USD"]}]},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS9nZXQtZGFzaGJvYXJkIiwiaWF0IjoxNjkwNDI1OTE4LCJleHAiOjE2OTA0MzAwMjQsIm5iZiI6MTY5MDQyNjQyNCwianRpIjoic2ZHaWJPbDdWQlJWYlFrWiIsInN1YiI6IjEwMDAwMDAiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.Di689rzM2yBYAQQZibytt5y7rkBysrf9TUFoPVpZCYM","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.48143696784973 sec","log_id":"c8286cbf-8eba-4377-9e66-4b9e756c72f7"}
     * ##docs end##
     */
    public function getDashboardV2(Request $request)
    {
        $user_id = auth()->user()->id ?? null;
        $dashboardType = isset($user_id) ? 'dashboard' : 'homepage';
        $currentDate = date('Y-m-d');

        if ($dashboardType == 'dashboard') {
            // ############### User Detail ################
            $userDetail = User::getProfile($user_id);
            $data['data']['user_detail'] = Arr::only($userDetail, ['id', 'member_id', 'name', 'username', 'phone_no', 'country', 'country_display']);
        }

        // ############### Banner ################
        $data['data']['banner'] = array_slice(Banner::getBanner() ?? [], 0, 5);

        // ############### Currency Rate ################
        $data['data']['currency'] = array_slice(CurrencyRateHourly::getCurrency()['currency']->toArray() ?? [], 0, 5);

        // ############### News ################
        $data['data']['announcement'] = array_slice(Announcement::getNews() ?? [], 0, 5);

        // ############### Notification ################
        $data['data']['notification_count'] = UserNotification::selectRaw('COUNT(id) AS count')->where('user_id', $user_id)->where('read', 0)->first()->count ?? 0;

        abort(200, json_encode($data));
    }

    private function restoreProductBalance($user_id)
    {
        $total_balance = 0;
        UserProduct::with('product', 'user')
            ->where('user_id', $user_id)
            ->get()
            ->map(function ($q) {

                switch ($q->name) {
                    case 'TK8':
                        $prodBalRes = Traits\OCTK8Trait::postOC(['account' => $q->member_id], 'check_balance');
                        break;
                    case 'MT':
                        $prodBalRes = resolve(MT::class)->checkBalance($q->member_id);
                        break;
                    case 'JK':
                        // $prodBalRes = resolve(JK::class)->getWithdraw($q->user->username);
                        $prodBalRes = resolve(JK::class)->getWithdraw($q->member_id, $q->user->store->jk_agent_id);
                        break;
                    case 'JILI':
                        $prodBalRes = resolve(JILI::class)->withdraw($q->member_id);
                        break;
                    case 'GSC':
                        $prodBalRes = resolve(GSC::class)->withdraw($q->member_id);
                        break;
                }

                $mainWallet = 0;
                $gameWallet = 0;
                $lotteryWallet = 0;
                if (isset($prodBalRes['data']) && ! empty($prodBalRes['data'])) {
                    $mainWallet = isset($prodBalRes['data']['main_wallet']) && is_numeric($prodBalRes['data']['main_wallet']) ? $prodBalRes['data']['main_wallet'] : 0;
                    $gameWallet = isset($prodBalRes['data']['game_wallet']) && is_numeric($prodBalRes['data']['game_wallet']) ? $prodBalRes['data']['game_wallet'] : 0;
                    $lotteryWallet = isset($prodBalRes['data']['lottery_wallet']) && is_numeric($prodBalRes['data']['lottery_wallet']) ? $prodBalRes['data']['lottery_wallet'] : 0;
                }
                $prodBal = $mainWallet + $gameWallet;
                $prodLotteryBal = $lotteryWallet;

                if (isset($prodBalRes)) {
                    AccountBalance::updateBalance($q->user_id, $prodBal);
                }

                $params = array_merge([
                    'amount' => $prodBal,
                    'credit_id' => 1000,
                    'product_name' => $q->product->name,
                    'product_id' => $q->product_id,
                    'user_id' => $q->user_id,
                    'credit_type' => Credit::first()->type,
                ]);

                ExTransfer::transferOut($params + [
                    'product_data' => $q->product,
                    'wallet_data' => [],
                    'exMemberId' => $q->member_id,
                    'wallet_type' => 'Main',
                ]);
            });

        $data['message'] = 'Success';
    }
}
