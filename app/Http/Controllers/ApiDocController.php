<?php

namespace App\Http\Controllers;

class ApiDocController
{
    public $postman_title;
    public $api_version;
    public $api_endpoint;
    public $modules;
    public $controllers = [ 
        'ActivityLogController',
        'AdminController',
        'AnnouncementController',
        'AnnouncementBarController',
        'Auth<PERSON>ontroller',
        'BankController',
        'BannerController',
        'CountryController',
        'CreditController',
        'CurrencyController',
        'DashboardController',
        'ThirdPartyController',
        'ExchangeController',
        'FingerprintController',
        'FileController',
        'KycController',
        'MemoController',
        'PermissionController',
        'ProductController',
        'ReportController',
        'ServiceController',
        'ScheduleController',
        'TreeController',
        'TTransferController',
        'UserController',
        'DocumentController',
        'CardController',
    ];

    public $items = [];

    public function __construct(){
        define('CUSTOM_RESPONSE_OUTPUT', 1);
    }

    public function apiDocs()
    {
        $this->postman_title = config('app.name') . ' API';
        $this->api_name = config('app.name') . " API";
        $this->api_version = "1.0.0";
        $this->api_endpoint = [
            "staging" => "https://api.default.xyz/",
            "live" => "",
            "development" => "http://local-api.fw.com/"
        ];
        
        if(env('APP_ENV') == 'staging') $this->api_endpoint["staging"] = env('APP_URL_API_USER');
        elseif(env('APP_ENV') == 'production') $this->api_endpoint["live"] = env('APP_URL_API_USER');

        $this->modules = 'api';
        $this->generateDocs();

        return view('api-docs', [
            'data' => [
                'postman_download' => url("api-docs-postman"),
                'api_name' => $this->api_name,
                'api_version' => $this->api_version,
                'api_endpoint' => $this->api_endpoint,
                'items' => $this->items
            ]
        ]);
    }

    public function adminApiDocs()
    {
        $this->postman_title = config('app.name') . ' ADMIN';
        $this->api_name = config('app.name') . " Admin API";
        $this->api_version = "1.0.0";
        $this->api_endpoint = [
            "staging" => "https://api-admin.fw.xyz/",
            "live" => "",
            "development" => "http://local-api-admin.fw.com/"
        ];

        if(env('APP_ENV') == 'staging') $this->api_endpoint["staging"] = env('APP_URL_API_ADMIN');
        elseif(env('APP_ENV') == 'production') $this->api_endpoint["live"] = env('APP_URL_API_ADMIN');

        $this->modules = 'admin';
        $this->generateDocs();

        return view('api-docs', [
            'data' => [
                'postman_download' => url("api-docs-postman"),
                'api_name' => $this->api_name,
                'api_version' => $this->api_version,
                'api_endpoint' => $this->api_endpoint,
                'items' => $this->items
            ]
        ]);
    }

    public function userApiDocs()
    {
        $this->postman_title = config('app.name') . ' USER';
        $this->api_name = config('app.name') . " User API";
        $this->api_version = "1.0.0";
        $this->api_endpoint = [
            "staging" => "https://api-user.fw.xyz/",
            "live" => "",
            "development" => "http://local-api-user.fw.com/"
        ];

        if(env('APP_ENV') == 'staging') $this->api_endpoint["staging"] = env('APP_URL_API_USER');
        elseif(env('APP_ENV') == 'production') $this->api_endpoint["live"] = env('APP_URL_API_USER');

        $this->modules = 'user';
        $this->generateDocs();

        return view('api-docs', [
            'data' => [
                'postman_download' => url("api-docs-postman"),
                'api_name' => $this->api_name,
                'api_version' => $this->api_version,
                'api_endpoint' => $this->api_endpoint,
                'items' => $this->items
            ]
        ]);
    }

    public function appApiDocs()
    {
        $this->postman_title = config('app.name') . ' APP';
        $this->api_name = config('app.name') . " APP API";
        $this->api_version = "1.0.0";
        $this->api_endpoint = [
            "staging" => "https://api-app.fw.xyz/",
            "live" => "",
            "development" => "http://local-api-user.fw.com/"
        ];

        if(env('APP_ENV') == 'staging') $this->api_endpoint["staging"] = env('APP_URL_API_APP');
        elseif(env('APP_ENV') == 'production') $this->api_endpoint["live"] = env('APP_URL_API_APP');

        $this->modules = 'app';
        $this->generateDocs();

        return view('api-docs', [
            'data' => [
                'postman_download' => url("api-docs-postman"),
                'api_name' => $this->api_name,
                'api_version' => $this->api_version,
                'api_endpoint' => $this->api_endpoint,
                'items' => $this->items
            ]
        ]);
    }

    protected function generateDocs()
    {
        foreach ((array)$this->controllers as $c) {
            $fname = __DIR__ . "/$c.php";
            $content = file_get_contents($fname);

            preg_match_all('/##docs start##(.*?)\##docs end##/is', $content, $matches);

            if (isset($matches[1]) && !empty($matches[1])) {
                foreach ((array)$matches[1] as $node) {
                    $nodes = explode("\n", $node);
                    $arr = [
                        'headers' => [],
                        'body' => []
                    ];
                    foreach ((array)$nodes as $k => $nodeClean) {
                        $nodeClean = trim(preg_replace('/\s+/', ' ', $nodeClean));
                        if ($nodeClean === '' || $nodeClean === '*') {
                            continue;
                        }
                        $nodeClean = substr($nodeClean, 2);
                        $item = explode('=', $nodeClean);
                        $item[0] = trim($item[0]);
                        $itemRest = [];
                        for ($i = 1; $i < count($item); $i++) {
                            $itemRest[] = $item[$i];
                        }
                        $item[1] = trim(implode('', $itemRest));
                        if ($item[0] == '@module') {
                            $l = explode(',', $item[1]);
                            $f = false;
                            foreach ((array)$l as $moduleName) {
                                if ($this->modules == $moduleName) {
                                    $f = true;
                                }
                            }
                            if (!$f) {
                                break;
                            }
                        } else {
                            if ($item[0] == '@postmanName') {
                                $arr['name'] = $item[1];
                            } else {
                                if ($item[0] == '@path') {
                                    $arr['path'] = $item[1];
                                } else {
                                    if ($item[0] == '@method') {
                                        $arr['method'] = strtoupper($item[1]);
                                    } else {
                                        if ($item[0] == '@description') {
                                            $arr['description'] = $item[1];
                                        } else {
                                            if ($item[0] == '@response') {
                                                $arr['response'] = json_decode($item[1], true);
                                                if(isset($arr['response']['access_token'])){
                                                    $arr['response']['access_token'] = 'bearer token ....';
                                                }
                                                $arr['response'] = json_encode($arr['response']);
                                            } else {
                                                if ($item[0] == '@header') {
                                                    $z = explode('|', $item[1]);
                                                    if (isset($z[6]) && $z[6] != $this->modules) {
                                                        continue;
                                                    }
                                                    $arr['headers'][] = [
                                                        'key' => $z[0] ?? '',
                                                        'value' => $z[1] ?? '',
                                                        'type' => $z[2] ?? '',
                                                        'optional' => $z[3] == 'required' ? 'no' : 'yes',
                                                        'sample' => $z[4],
                                                        'description' => $z[5]
                                                    ];
                                                } else {
                                                    if ($item[0] == '@body') {
                                                        $z = explode('|', $item[1]);
                                                        if (isset($z[6]) && $z[6] != $this->modules) {
                                                            continue;
                                                        }
                                                        $arr['body'][] = [
                                                            'key' => $z[0] ?? '',
                                                            'value' => $z[1] ?? '',
                                                            'type' => $z[2] ?? '',
                                                            'optional' => $z[3] == 'required' ? 'no' : 'yes',
                                                            'sample' => $z[4],
                                                            'description' => $z[5]
                                                        ];
                                                    } else {
                                                        if ($item[0] == '@param') {
                                                            $z = explode('|', $item[1]);
                                                            if (isset($z[6]) && $z[6] != $this->modules) {
                                                                continue;
                                                            }
                                                            $arr['param'][] = [
                                                                'key' => $z[0] ?? '',
                                                                'value' => $z[1] ?? '',
                                                                'type' => $z[2] ?? '',
                                                                'optional' => $z[3] == 'required' ? 'no' : 'yes',
                                                                'sample' => $z[4],
                                                                'description' => $z[5]
                                                            ];
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (!empty($arr) && !empty($arr['method'])) {
                        $this->items[$c][] = $arr;
                    }
                }
            }
        }
    } 

    public function generatePostmanJson()
    {
        $items = [];
        foreach ((array)$this->controllers as $c) {
            $fname = __DIR__ . "/$c.php";
            $content = file_get_contents($fname);
            preg_match_all('/##docs start##(.*?)\##docs end##/is', $content, $matches);
            if (isset($matches[1]) && !empty($matches[1])) {
                foreach ((array)$matches[1] as $node) {
                    $nodes = explode("\n", $node);
                    $arr = [];
                    foreach ((array)$nodes as $k => $nodeClean) {
                        $nodeClean = trim(preg_replace('/\s+/', ' ', $nodeClean));
                        if ($nodeClean === '' || $nodeClean === '*') {
                            continue;
                        }
                        $nodeClean = substr($nodeClean, 2);
                        $item = explode('=', $nodeClean);
                        $item[0] = trim($item[0]);
                        $itemRest = [];
                        for ($i = 1; $i < count($item); $i++) {
                            $itemRest[] = $item[$i];
                        }
                        $item[1] = trim(implode('', $itemRest));
                        if ($item[0] == '@module') {
                            $l = explode(',', $item[1]);
                            $f = false;
                            foreach ((array)$l as $moduleName) {
                                if ($this->modules == $moduleName) {
                                    $f = true;
                                }
                            }
                            if (!$f) {
                                break;
                            }
                        } else {
                            if ($item[0] == '@postmanName') {
                                $arr = [
                                    'name' => $item[1],
                                    'request' => [
                                        'method' => "",
                                        'header' => [],
                                        'body' => [
                                            'mode' => 'raw',
                                            'raw' => [],
                                            'options' => [
                                                'raw' => [
                                                    'language' => 'json'
                                                ]
                                            ]
                                        ],
                                        'url' => [
                                            'raw' => "",
                                            'host' => [],
                                            'path' => []
                                        ],
                                    ],
                                    'response' => []
                                ];
                            } else {
                                if ($item[0] == '@method') {
                                    $arr['request']['method'] = strtoupper($item[1]);
                                } else {
                                    if ($item[0] == '@body') {
                                        $z = explode('|', $item[1]);
                                        $arr['request']['body']['raw'][$z[0]] = $z[1];
                                    } else {
                                        if ($item[0] == '@path') {
                                            $arr['request']['url']['raw'] = secure_url($item[1]);
                                            $arr['request']['url']['host'][] = secure_url("");
                                            $arr['request']['url']['path'] = explode('/', substr($item[1], 1));
                                        } else {
                                            if ($item[0] == '@header') {
                                                $z = explode('|', $item[1]);
                                                if ($z[0] == 'Authorization') {
                                                    $arr['request']['auth'] = [
                                                        'type' => 'bearer',
                                                        'bearer' => [
                                                            0 => [
                                                                'key' => 'token',
                                                                'value' => $z[1],
                                                                'type' => 'string'
                                                            ]
                                                        ]
                                                    ];
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (!empty($arr['request']['method'])) {
                        $arr['request']['body']['raw'] = json_encode($arr['request']['body']['raw']);
                        $items[] = $arr;
                    }
                }
            }
        }

        $ret = [
            'info' => [
                '_postman_id' => 'bd3ad674-2ea7-4303-9c42-a0fad11cf231',
                'name' => $this->postman_title,
                'schema' => 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
            ],
            'item' => $items
        ];
        return $ret;
    }

    public function downloadAdminPostmanJson()
    {
        $this->modules = 'admin';
        $ret = $this->generatePostmanJson();
        $headers = [
            'Content-type' => 'application/json',
            'Content-Disposition' => 'attachment; filename="postman.json"',
        ];
        return response()->make($ret, 200, $headers);
    }

    public function downloadPostmanJson()
    {
        $this->modules = 'api';
        $ret = $this->generatePostmanJson();
        $headers = [
            'Content-type' => 'application/json',
            'Content-Disposition' => 'attachment; filename="postman.json"',
        ];
        return response()->make($ret, 200, $headers);
    }

    public function downloadUserPostmanJson()
    {
        $this->modules = 'user';
        $ret = $this->generatePostmanJson();
        $headers = [
            'Content-type' => 'application/json',
            'Content-Disposition' => 'attachment; filename="postman.json"',
        ];
        return response()->make($ret, 200, $headers);
    }
}
