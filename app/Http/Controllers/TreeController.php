<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\TreeSponsor;
use App\Models\User;
use App\Models\UserSalesCache;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class TreeController extends Controller
{
    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Tree Sponsor
     * @module = admin
     * @path = tree/get-tree-sponsor
     * @method = POST
     * @description = To get tree diagram.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = user_id|<user_id>|integer|required|1000000|User ID.
     * @body = real_user_id|<real_user_id>|integer|required|1000000|Real User ID.
     * @body = username|<username>|string|optional|Username|Username.
     * @response = {"data":{"summary":[],"breadcrumb":[{"id":1000000,"name":"tkuser","username":"tkuser"}],"sponsor":{"id":1000000,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"tkuser","name":"tkuser","created_at":"21\/04\/2023 01:38:29","totalDirectDownline":30,"totalDownline":31,"own_sales":"0.********","group_sales":"0.********"},"downlinesLevel":[{"id":1000001,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser*********","created_at":"25\/04\/2023 19:46:46","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000002,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser0002","created_at":"26\/04\/2023 09:06:17","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000003,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"***********","name":"fwUser0003","created_at":"26\/04\/2023 09:10:01","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000004,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser0004","created_at":"26\/04\/2023 09:14:39","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000005,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser0005","created_at":"26\/04\/2023 09:19:07","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000006,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser0006","created_at":"26\/04\/2023 09:19:41","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000007,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"***********","name":"fwUser0007","created_at":"26\/04\/2023 09:22:47","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000008,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser0008","created_at":"26\/04\/2023 09:24:39","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000009,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser0009","created_at":"26\/04\/2023 09:25:40","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000010,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser*********","created_at":"26\/04\/2023 18:44:03","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000011,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser0011","created_at":"26\/04\/2023 19:07:09","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000012,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser0012","created_at":"26\/04\/2023 19:10:41","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000013,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser0013","created_at":"26\/04\/2023 19:11:05","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000014,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser0014","created_at":"26\/04\/2023 19:12:17","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000015,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser0015","created_at":"26\/04\/2023 19:13:19","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000016,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser0016","created_at":"26\/04\/2023 19:18:27","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000017,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser0017","created_at":"26\/04\/2023 19:34:14","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000018,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser0018","created_at":"26\/04\/2023 19:40:13","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000019,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser0019","created_at":"26\/04\/2023 19:44:35","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000020,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser0020","created_at":"26\/04\/2023 19:49:06","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000021,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser0022","created_at":"26\/04\/2023 19:56:18","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000022,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser0023","created_at":"26\/04\/2023 22:21:30","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000023,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser25","created_at":"26\/04\/2023 22:38:31","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000024,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser27","created_at":"26\/04\/2023 22:46:00","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000025,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"fwUser30","created_at":"27\/04\/2023 00:25:58","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000026,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"lele","created_at":"27\/04\/2023 23:24:28","totalDirectDownline":1,"totalDownline":1,"own_sales":"0.********","group_sales":"0.********"},{"id":1000027,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"60*********","name":"lele","created_at":"28\/04\/2023 00:09:11","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000028,"user_type":"user-account","phone_no":"60-************","member_id":"*********","username":"60************","name":"Test001","created_at":"02\/05\/2023 20:27:01","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000029,"user_type":"user-account","phone_no":"60-**********","member_id":"*********","username":"60**********","name":"Test 002","created_at":"02\/05\/2023 22:47:17","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"},{"id":1000030,"user_type":"user-account","phone_no":"60-*********","member_id":"*********","username":"***********","name":"Alice","created_at":"05\/05\/2023 21:37:34","totalDirectDownline":0,"totalDownline":0,"own_sales":"0.********","group_sales":"0.********"}],"uplineLevel":0},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************.gF2HVxDzQg48lvj2Kxar3obFD_0ftbFP_Yyfm7NNTV0","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.*************** sec","log_id":"58f984b5-166c-4977-a90a-0273da441f63"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Tree Sponsor
     * @module = user,app
     * @path = tree/get-tree-sponsor
     * @method = POST
     * @description = To get tree diagram.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = user_id|<user_id>|integer|required|1000000|User ID.
     * @body = username|<username>|string|optional|Username|Username.
     * @response = {"data":{"summary":[],"breadcrumb":[{"id":1000000,"name":"tkuser","username":"tkuser"},{"id":1000036,"name":"testreg01","username":"604"}],"sponsor":{"id":1000036,"user_type":"user-account","phone_no":"60-4","member_id":"*********","username":"604","name":"testreg01","created_at":"12\/05\/2023 09:37:25","totalDirectDownline":1,"totalDownline":3,"own_sales":"0.********","group_sales":"0.********","rank_id":3,"rank_name":"level-1","rank_display":"lang.level-1"},"downlinesLevel":[{"id":1000037,"user_type":"user-account","phone_no":"60-5","member_id":"*********","username":"605","name":"testreg02","created_at":"15\/05\/2023 09:40:57","totalDirectDownline":1,"totalDownline":2,"own_sales":"0.********","group_sales":"0.********","rank_id":4,"rank_name":"level-2","rank_display":"lang.level-2"}],"uplineLevel":1},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************.QrwA02lT4lOEyR2HM-Z3-x_rZ7OHejnU-ywkAQhzi6w","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"6d0fdd44-d1d9-48d1-9f37-de7c80a9ab34"}
     * ##docs end##
     */
    public function getTreeSponsor(Request $request)
    {
        if(in_array(MODULE,['user','app'])) $request->merge(["real_user_id" => auth()->user()->id]);
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'real_user_id' => 'required|integer|exists:users,id',
            'username' => 'string|exists:users,username',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = TreeSponsor::getTreeSponsor($validator->validated());

        abort(200, json_encode($res));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Tree Sponsor Vertical
     * @module = admin
     * @path = tree/get-tree-sponsor-vertical
     * @permissionName = Tree Sponsor List
     * @menuType = api
     * @parent = User
     * @method = POST
     * @description = To get tree vertical diagram.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = user_id|<user_id>|integer|required|1000000|User ID.
     * @body = real_user_id|<real_user_id>|integer|required|1000000|Real User ID.
     * @body = username|<username>|string|optional|Username|Username.
     * @response = {"data":{"target":{"attr":{"id":1000001,"member_id":"127487387","username":"127487387","name":"ky test","created_at":"20\/04\/2023 12:50:45","downlineCount":2,"own_sales":"0.********","group_sales":"0.********"}},"downline":[{"attr":{"id":1000002,"member_id":"301556388","username":"301556388","name":"ky test2","created_at":"20\/04\/2023 12:52:13","downlineCount":0,"own_sales":"0.********","group_sales":"0.********","disabled":"No","suspended":"No"}},{"attr":{"id":1000003,"member_id":"888199097","username":"888199097","name":"ky test3","created_at":"20\/04\/2023 12:52:48","downlineCount":0,"own_sales":"0.********","group_sales":"0.********","disabled":"No","suspended":"No"}}],"targetID":"1000000"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS90cmVlL2dldC10cmVlLXNwb25zb3ItdmVydGljYWwiLCJpYXQiOjE2ODE5NzA1MjAsImV4cCI6MTY4MTk3NjI1MiwibmJmIjoxNjgxOTcyNjUyLCJqdGkiOiJXOW1IbXNLZ0tkWEdDUmt0Iiwic3ViIjoiMTAwMDAwMSIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.wwQNIALLpaS5B6_wjt7XJfGma9kzj8gSQ9SX0GmdEpw","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"5708a58d-41dd-497a-bcea-5430baf52d4c"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Tree Sponsor Vertical
     * @module = user,app
     * @path = tree/get-tree-sponsor-vertical
     * @permissionName = Tree Sponsor List
     * @menuType = api
     * @parent = User
     * @method = POST
     * @description = To get tree vertical diagram.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = user_id|<user_id>|integer|required|1000000|User ID.
     * @body = username|<username>|string|optional|Username|Username.
     * @response = {"data":{"target":{"attr":{"id":1000001,"member_id":"127487387","username":"127487387","name":"ky test","created_at":"20\/04\/2023 12:50:45","downlineCount":2,"own_sales":"0.********","group_sales":"0.********"}},"downline":[{"attr":{"id":1000002,"member_id":"301556388","username":"301556388","name":"ky test2","created_at":"20\/04\/2023 12:52:13","downlineCount":0,"own_sales":"0.********","group_sales":"0.********","disabled":"No","suspended":"No"}},{"attr":{"id":1000003,"member_id":"888199097","username":"888199097","name":"ky test3","created_at":"20\/04\/2023 12:52:48","downlineCount":0,"own_sales":"0.********","group_sales":"0.********","disabled":"No","suspended":"No"}}],"targetID":"1000000"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFwcC50a3NoLmNvbS90cmVlL2dldC10cmVlLXNwb25zb3ItdmVydGljYWwiLCJpYXQiOjE2ODE5NzA1MjAsImV4cCI6MTY4MTk3NjI1MiwibmJmIjoxNjgxOTcyNjUyLCJqdGkiOiJXOW1IbXNLZ0tkWEdDUmt0Iiwic3ViIjoiMTAwMDAwMSIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.wwQNIALLpaS5B6_wjt7XJfGma9kzj8gSQ9SX0GmdEpw","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"5708a58d-41dd-497a-bcea-5430baf52d4c"}
     * ##docs end##
     */
    public function getTreeSponsorVertical(Request $request)
    {
        if(in_array(MODULE,['user','app'])) $request->merge(["real_user_id" => auth()->user()->id]);
        $validator = Validator::make($request->all(), [
            'real_user_id' => 'required|integer|exists:users,id,user_type,'.User::$userType['user-account'],
            'user_id' => 'required|integer|exists:users,id,user_type,'.User::$userType['user-account'],
            'username' => 'string|exists:users,username,user_type,'.User::$userType['user-account'],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = TreeSponsor::getTreeSponsorVertical($validator->validated());

        abort(200, json_encode($res));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = tree/change-referral
     * @module = admin
     * @path = tree/change-referral
     * @permissionName = Change Referral
     * @menuType = api
     * @parent = User
     * @method = POST
     * @description = To get tree diagram.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = user_id|<user_id>|integer|required|1000000|User ID.
     * @body = new_referral_username|<new_referral_username>|string|required|TestReferral|New Referral Username.
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLm1hcnRpbi5jb20vdHJlZS9jaGFuZ2UtcmVmZXJyYWwiLCJpYXQiOjE2NjMzODU2MjEsImV4cCI6MTY2MzM5MTgzNiwibmJmIjoxNjYzMzg4MjM2LCJqdGkiOiJvUld0M1ZVc0k5OFp6Y1VEIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.JkiahEVKz1dNB5cXrZZbvJ3JBLgJUn0IIb_BqnuE6Yw","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.**************** sec","log_id":"c61d62db-1c13-4b42-8237-3c98dd6df809"}
     * ##docs end##
     */
    public function changeReferral(Request $request)
    {
        $returnParam = [];

        $validator = Validator::make($request->all(), [
            "user_id" => [
                "required",
                "integer",
                Rule::exists('users', 'id')->whereIN("user_type", [User::$userType['user-account']])->where('activated', 1)
            ],
            "new_referral_username" => [
                "required",
                "string",
                function ($q, $value, $fail) use ($request, &$returnParam) {
                    $tgtUserDetail = User::with('treeSponsor')->where('id', $request->user_id)->whereIN("user_type", [User::$userType['user-account']])->first();
                    $newReflDetail = User::with('treeSponsor')->where('username', $value)->whereIN("user_type", [User::$userType['user-account']])->first();

                    //Check Referral exist
                    if (empty($newReflDetail) || empty($tgtUserDetail)) {
                        $fail(__('validation.in', ["attribute" => str_replace('_', ' ', $q)]));
                        return;
                    }

                    $newRefTraceKey = explode("/", $newReflDetail->treeSponsor->trace_key);

                    //Check Referral is same with target user
                    if ($newReflDetail->id == $tgtUserDetail->id) {
                        $fail("New referral username cannot be ownself");
                        return;
                    }

                    //Check Referral is same person with old referral
                    if ($newReflDetail->id == $tgtUserDetail->sponsor_id) {
                        $fail("New referral username cannot be same person");
                        return;
                    }

                    //Check New Referral is under target user
                    if (in_array($tgtUserDetail->id, $newRefTraceKey)) {
                        $fail("New referral username cannot be under target user");
                        return;
                    }

                    $returnParam["old_referral_id"] = $tgtUserDetail->sponsor_id;
                    $returnParam["new_referral_id"] = $newReflDetail->id;
                },
            ]
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = TreeSponsor::changeReferral(array_merge($validator->validated(), $returnParam));

        abort(200);
    }
}
