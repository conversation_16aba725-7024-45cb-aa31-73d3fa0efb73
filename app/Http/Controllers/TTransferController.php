<?php

namespace App\Http\Controllers;

use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;
use App\Models;
use App\Models\FavouriteBank;
use App\Models\TelexTransfer;
use App\Traits;

class TTransferController extends Controller
{
    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Add Telegraphic Transfer
     * @module = user,app
     * @path = tt-transfer/add-telex-transfer
     * @method = POST
     * @description = To add Telegraphic Transfer.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = step|<step>|string|required|1,2,3,4,5|Step 1 Get Credit Data, Step 2 Get Transfer Setting Data, Step 3 Get Bank List, Step 4 Get Summart Data, Step 5 Comfirmation.
     * @body = credit_id|<credit_id>|integer|required|1|Credit Id (Step 1 Required).
     * @body = type|<type>|integer|required|1|Transfer Type (Step 2 Required, Get From dropdown : tt_transfer_type_list).
     * @body = currency_id|<currency_id>|integer|required|1|Currency Id (Step 3 Required, Get From Step 2 Returned Data).
     * @body = amount|<amount>|decimal|required|100.50|Credit Amount want to transfer. (Step 3 Required, 2 Decimal)
     * @body = favourite_id|<favourite_id>|integer|Optional|1|Received Favaourite Bank's Account Id. (Step 4 Optional, Get From step 3 Returned Data)
     * @body = bank_id|<bank_id>|integer|Optional|1|Received Bank's Id. (Step 4 Required if favourite_id did not exists, Get From step 3 Returned Data)
     * @body = account_holder|<account_holder>|string|Optional|XXX|Received Bank's Account Holder Name. (Step 4 Required if favourite_id did not exists)
     * @body = account_number|<account_number>|string|Optional|XXX|Received Bank's Account Number. (Step 4 Required if favourite_id did not exists)
     * @body = branch_code|<branch_code>|string|optional|XXX|Received Bank's Branch Code. (Step 4 Required if Currency is INR and favourite_id did not exists, else optional)
     * @body = is_favourite|<is_favourite>|integer|Optional|1/0|To Set Favaourite Bank's Account. (Step 4 Required if favourite_id did not exists)
     * @body = pin|<pin>|int|required|123456|transction PIN for user perform transfer. (Step 5 Required)
     * @response = {"summary_data":{"received_amount":"1.00","converted_received_amount":"0.22","currency":"USD","country_iso":"US","amount":"1.00","from_currency":"MYR","from_country_iso":"MY","processing_fee":"1.00","bank_display":"Affin Bank","transfer_type":"Normal Transfer","created_at":"08\/05\/2023 11:03:38","account_holder":"AAA","account_number":"1234123"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************.zEJchgfsYRbYuFwK1vg4X97dnCN_OepMJs5ph3SJEmc","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"d2fe5944-ef08-4379-9b95-30be3b57524e"}
     * ##docs end##
     */
    public function addTelexTransfer(Request $request)
    {
        $request->request->add(['user_id' => auth()->user()->id]);
        $userDetail = null;
        $walletData = null;
        $telexTransferData = null;
        $bankList = [];
        $favouriteBankList = [];
        $checkSchedule = null;
        $exParams = [];

        if(isset($request->user_id)){
            $totalTransferAry = TelexTransfer::selectRaw('currency_id, SUM(converted_amount) AS totalTransferAmt')
                ->where('user_id',$request->user_id)
                ->where('is_daily_limit', 1)
                ->whereIn('status',Arr::except(TelexTransfer::$status,['rejected','cancel']))
                ->whereDATE('created_at',date('Y-m-d'))
                ->groupBy('currency_id')
                ->get()
                ->pluck('totalTransferAmt', 'currency_id')
                ->toArray();

            $telexTransferData = Models\CurrencySetting::with(['toCurrency','toCurrency.country'=> function ($q) {
                return $q->whereIn('name',Config('general.valid_exchange_country'));
            },'fromCurrency','fromCurrency.country'=> function ($q){
                return $q->whereIn('name',Config('general.valid_exchange_country'));
            }])
            ->where('type',Models\CurrencySetting::$type['fiat'])
            // ->where($column,0)
            ->where('disabled',0)
            ->whereRelation('toCurrency','disabled',Models\Currency::$disabled['active'])
            ->whereRelation('fromCurrency','iso','MYR')
            ->get()
            ->map(function ($q) use ($totalTransferAry){
                $totalTransferAmt = Traits\DecimalTrait::setDecimal(0);
                if (isset($totalTransferAry[$q->to_currency_id])) {
                    $totalTransferAmt = Traits\DecimalTrait::setDecimal($totalTransferAry[$q->to_currency_id]);
                }
                $normalRate = ($q->normal_rate > 0) ? $q->normal_rate : null;
                $instantRate = ($q->instant_rate > 0) ? $q->instant_rate : null;
                $dailyLimitBalance = Traits\DecimalTrait::setDecimal(($q->daily_limit - $totalTransferAmt) ?? 0);

                return [
                    "id" => $q->to_currency_id,
                    "country_id" => $q->toCurrency->country->id,
                    "from_currency_id" => $q->fromCurrency->id,
                    "from_currency_iso" => $q->fromCurrency->iso,
                    "from_country_iso_code" => $q->fromCurrency->country->iso_code2,
                    "currency_iso" => $q->toCurrency->iso,
                    "country_iso_code" => $q->toCurrency->country->iso_code2,
                    "country_name" => $q->toCurrency->country->name,
                    "country_name_display" => Lang::has('lang.'.$q->toCurrency->country->name) ? Lang::get('lang.'.$q->toCurrency->country->name) : $q->toCurrency->country->name,
                    "normal_rate" => $normalRate ?? $q->sell_rate,
                    "instant_rate" => $instantRate ?? $q->sell_rate,
                    "processing_fee" => $q->fee,
                    "daily_limit" => $q->daily_limit,
                    "daily_rate" => $q->daily_rate,
                    "min_order" => $q->min_amount,
                    "total_transfer_amt" => $totalTransferAmt,
                    "daily_limit_balance" => $dailyLimitBalance,
                    "is_show_out" => (($dailyLimitBalance > 0) && ($dailyLimitBalance >= $q->min_amount)) ? 1 : 0,
                    "normal_disabled" => $q->normal_disabled,
                    "instant_disabled" => $q->instant_disabled
                ];
            })->keyBy('id')->toArray();
        }

        $validator = Validator::make($request->all(), [
            'step' => 'required|integer|in:1,2,3,4,5',
            'user_id' => [
                'required',
                'integer',
                function ($q, $value, $fail) use($request, &$userDetail){
                    $checkUser = Models\User::with(['userDetail'])->where('id', $value)->whereIN('user_type', [Models\User::$userType['user-account']])->get()->map(function ($q) use (&$userDetail){
                        foreach ($q->userDetail->keyBy('name') as $name => $value) {
                            $userDetail[$name] = $value->value;
                        }
                        return $q;
                    })->first();
                    if (empty($checkUser)) {
                        $fail(Lang::get("validation.exists"));
                        return;
                    }
                }
            ],
            "credit_id" => [
                "required",
                "integer",
                function ($q, $value, $fail) use($request, &$walletData){
                    $credit = Models\Credit::where(['id' => $value])->first();
                    if(empty($credit)){
                        $fail(Lang::get("validation.exists"));
                        return;
                    }

                    $walletAmt = Models\Credit::getBalance($request->user_id, $credit->type);

                    $walletData = [
                       "credit_id" => $value,
                       "credit_name" => $credit->name,
                       "credit_type" => $credit->type,
                       "credit_display" => Lang::has('lang.'.$credit->name) ? Lang::get('lang.'.$credit->name) : $credit->name,
                       "balance" => $walletAmt,
                    ];
                }
            ],
            "type" => [
                Rule::requiredIf(($request->step >= 3)),
                "integer",
                "in:".implode(',',Models\TelexTransfer::$type),
                function ($q, $value, $fail) use($request, &$telexTransferData, &$checkSchedule){
                    switch (array_search($value,Models\TelexTransfer::$type)) {
                        case 'normal-transfer':
                            $column = 'normal_disabled';
                            $checkSchedule = 'tt-normal-transfer';
                            break;
        
                        case 'instant-transfer':
                            $column = 'instant_disabled';
                            $checkSchedule = 'tt-instant-transfer';
                            break;

                        default:
                            return;
                            break;
                    }

                    foreach ($telexTransferData as &$ttranferData) {
                        $ttranferData['rate'] = ($column == 'normal_disabled') ? ($ttranferData['normal_rate']) : ($ttranferData['instant_rate']);
                    }
                }
            ],
            "currency_id" => [
                Rule::requiredIf(($request->step >= 3)),
                "integer",
                function ($q, $value, $fail) use($request, &$telexTransferData,&$bankList,&$favouriteBankList){
                    $validCurrencyID = array_keys($telexTransferData);
                    if(!in_array($value,$validCurrencyID)){
                        $fail(__('validation.exists'));
                        return;
                    }
                    if ($request->type == Models\TelexTransfer::$type['normal-transfer'] && $telexTransferData[$value]['normal_disabled'] == 1) {
                        $fail(__('lang.ttransfer-currency-normal-not-available'));
                        return;
                    } else if ($request->type == Models\TelexTransfer::$type['instant-transfer'] && $telexTransferData[$value]['instant_disabled'] == 1) {
                        $fail(__('lang.ttransfer-currency-instant-not-available'));
                        return;
                    }

                    $countryId = $telexTransferData[$value]['country_id'];
                    $bankList =  Models\Bank::where('country_id',$countryId)
                                    ->where('status',Models\Bank::$status['active'])
                                    ->where('transfer_status',Models\Bank::$transferStatus['active'])
                                    ->get()->map(function ($q){
                                        return [
                                            "id" => $q->id,
                                            "name" => $q->name,
                                            "display" => Lang::has('lang.'.$q->name) ? Lang::get('lang.'.$q->name) : $q->name ,
                                        ];
                                    })->keyBy('id')->toArray();

                    $favouriteBankList = Models\FavouriteBank::with('bank')->selectRaw('id, bank_id, account_no, account_holder, branch_code')
                                        ->where('user_id',$request->user_id)
                                        ->where('status',Models\FavouriteBank::$status['active'])
                                        ->whereIn('bank_id',array_keys($bankList))
                                        ->get()->map(function ($q){
                                            return [
                                                "id" => $q->id,
                                                "bank_id" => $q->bank_id,
                                                "bank_display" => Lang::has('lang.'.$q->bank->translation_code) ? Lang::get('lang.'.$q->bank->translation_code) : $q->bank->name,
                                                "account_no" => $q->account_no,
                                                "account_holder" => $q->account_holder,
                                                "branch_code" => $q->branch_code,
                                            ];
                                        })->keyBy('id')->toArray();
                }
            ],
            "amount" => [
                Rule::requiredIf(($request->step >= 3)),
                "string",
                "gt:0",
                "regex:/^(\\d+\\.?\\d{0,2})$/",
                function ($q, $value, $fail) use($request, &$walletData, &$telexTransferData){
                    $totalTransferAmt = $telexTransferData[($request->currency_id ?? null)]['total_transfer_amt'] ?? 0;
                    $dailyLimit = $telexTransferData[($request->currency_id ?? null)]['daily_limit'] ?? 0;
                    $processFee = $telexTransferData[($request->currency_id ?? null)]['processing_fee'] ?? 0;
                    $minOrder = $telexTransferData[($request->currency_id ?? null)]['min_order'] ?? 0;
                    $iso = $telexTransferData[($request->currency_id ?? null)]['from_currency_iso'] ?? '';
                    $currencyISO = $telexTransferData[($request->currency_id ?? null)]['currency_iso'] ?? '';
                    $dailyLimitBalance = $telexTransferData[($request->currency_id ?? null)]['daily_limit_balance'] ?? 0;

                    // Daily limit checking
                    $isDailyRateFlag = 1;
                    $rate = $telexTransferData[($request->currency_id ?? null)]['rate'] ?? 0;

                    if ($value > $dailyLimitBalance) {
                        // If order amount greater than daily limit, use rate based on transfer type
                        $rate = $telexTransferData[($request->currency_id ?? null)]['rate'] ?? 0;
                        $isDailyRateFlag = 0;

                    } else if ((!empty($telexTransferData[($request->currency_id ?? null)]['daily_rate']))) {
                        // If today order amount + current order amount is less than daily limit, then use daily limit rate
                        $rate = $telexTransferData[($request->currency_id ?? null)]['daily_rate'];
                        $isDailyRateFlag = 1;
                    }

                    // Final rate
                    $telexTransferData[($request->currency_id ?? null)]['rate'] = $rate;
                    $telexTransferData[($request->currency_id ?? null)]['is_daily_limit'] = $isDailyRateFlag;

                    $amount = Traits\DecimalTrait::setDecimal($value / $rate, null, true);
                    if($minOrder > $value){
                        // Amount less than min order
                        $fail(Lang::get('lang.min-order-error',['minOrder'=>Traits\DecimalTrait::setDecimal($minOrder)]));
                        return;
                    }
                  
                    // if(($totalTransferAmt < $dailyLimit) && (($totalTransferAmt + $value) > $dailyLimit)){
                    /*if (($isDailyRateFlag == 1) && ($dailyLimitBalance >= $minOrder) && (($totalTransferAmt + $value) > $dailyLimit)) {
                        $fail(Lang::get('lang.daily-limit-error', ['dailyLimit' => Traits\DecimalTrait::setDecimal($dailyLimit) . ' ' . $currencyISO]));
                        return;
                    }*/

                    $balance = $walletData['balance'];
                    if($balance < ($amount + $processFee)){
                        $fail(Lang::get("lang.credit-insufficient-balance"));
                        return;
                    }          
                }
            ],
            "favourite_id" => [
                "integer",
                function ($q, $value, $fail) use(&$request, &$exParams,&$favouriteBankList){
                    if(empty($favouriteBankList[$value])){
                        $fail(__('validation.exists'));
                        return;
                    }
                    $exParams = [
                        "bank_id" => $favouriteBankList[$value]['bank_id'],
                        "account_holder" => $favouriteBankList[$value]['account_holder'],
                        "account_number" => $favouriteBankList[$value]['account_no'],
                        "branch_code" => $favouriteBankList[$value]['branch_code']
                    ];
                }
            ],
            "bank_id" => [
                Rule::requiredIf(($request->step >= 4) && (empty($request->favourite_id))),
                "integer",
                function ($q, $value, $fail) use($request, &$bankList){
                    $validBankID = array_keys($bankList);
                    if(!in_array($value,$validBankID)){
                        $fail(__('validation.exists'));
                        return;
                    }
                }
            ],
            "account_holder" => [
                Rule::requiredIf(($request->step >= 4) && (empty($request->favourite_id))),
                "string"
            ],
            "account_number" => [
                Rule::requiredIf(($request->step >= 4) && (empty($request->favourite_id))),
                "string",
                "regex:/^[0-9]*$/",
                "max:50"
            ],
            "branch_code" => [
                "nullable",
                Rule::requiredIf((($request->step >= 4) && (empty($request->favourite_id)) && (in_array($request->currency_id, Models\Currency::whereIn('iso', Models\TelexTransfer::$reqBranchCode)->get()->pluck('id')->toArray())))),
                "string",
            ],
            "is_favourite" => [
                Rule::requiredIf(($request->step >= 4) && (empty($request->favourite_id))),
                "integer",
                "in:1,0"
            ],
            "pin" => [
                Rule::requiredIf(function () use ($request){
                    return (in_array(MODULE,['user','app']) && ($request->step == 5));
                }),
                function ($q, $value, $fail) use (&$request,&$userDetail) {
                    $tempPass = str_pad($value, 6, "0", STR_PAD_LEFT);
                    if(isset($userDetail['transaction_password']) && Hash::check($tempPass, $userDetail['transaction_password']) != true){
                        $fail(Lang::get("lang.invalid-pin"));
                        return;
                    }
                }
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        // Temporary Hide
        if(empty($userDetail['transaction_password']) && ($validator->validated()['step'] >= 3)){
            abort(400, json_encode(['redirectFlag'=>1, 'errMessage'=>Lang::get('lang.no-transaction-pin-error')]));
        }

        // Schedule Checking
        Models\Schedule::checkSchedule(['tt-transfer',($checkSchedule ?? null)]);

        if(isset($exParams)){
            $validator->setData(array_merge($validator->validated(),$exParams));
        }

        if($validator->validated()['step'] == 1){
            $returnData = [
                "wallet_data" => $walletData
            ];
            abort(200,json_encode($returnData));
        }elseif($validator->validated()['step'] == 2){
            $returnData = [
                "telex_transfer_data" => array_values($telexTransferData)
            ];
            abort(200,json_encode($returnData));
        }elseif($validator->validated()['step'] == 3){
            $returnData = [
                "favourite_bank" => array_values($favouriteBankList),
                "bank_list" => array_values($bankList),
                "required_branch_code" => in_array($request->currency_id, Models\Currency::whereIn('iso', Models\TelexTransfer::$reqBranchCode)->get()->pluck('id')->toArray()) ? "1" : "0",
            ];
            abort(200,json_encode($returnData));
        }else{
            $res = Models\TelexTransfer::addTelexTransfer(array_merge($validator->validated(),[
                "wallet_data" => $walletData,
                "telex_transfer_data" => $telexTransferData[$validator->validated()['currency_id']],
                "bank_list" =>$bankList[$validator->validated()['bank_id']]
            ]));
        }

        abort(200,is_array($res) ? json_encode($res) : $res);
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Telegraphic List
     * @module = admin
     * @path = tt-transfer/get-telex-transfer-list
     * @method = post
     * @description = To get telegrahic list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
    
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = member_id|<member_id>|string|optional|1234567|Member's Id.
     * @body = phone_no|<phone_no>|integer|optional|011111111|User's phone_no.
     * @body = transaction_id|<transaction_id>|string|optional|TT123456|Transfer Transaction's Id.
     * @body = status|<status>|integer|optional|1|Transfer Transaction's status (Get From Dropdown : telex_transfer_status_list).
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = updated_from|<updated_from>|string|optional|2022-08-28|Updated Date filter.
     * @body = updated_to|<updated_to>|string|optional|2022-08-28|Updated Date filter.
     * @body = currency|<currency>|integer|optional|USD|Currency filter. (dropdown: currency_filter)
     * @body = export|<1/0>|integer|optional|1|export the listing or not. 
     * @body = export_data|<export_data>|array|option|{"data":{"Key":"Column Name"}}|Data need to export value => header. Required for export
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|sponsor-bonus|name of the export file. Required for export
     * @body = bank_export|<1/0>|integer|optional|1|export the listing or not. 
     * @body = bank_export_file_name|<bank_export_file_name>|s|option|{"data":{"Key":"Column Name"}}|Data need to export value => header. Required for export

     * @response = {"data":{"list":[{"id":99,"transaction_id":"TT692189","created_at":"28\/06\/2023 16:30:35","member_id":"*********","phone_no":"60-*********","amount":"110.00","charges":"10.00","transfer_amount":"100.00","currency_iso":"PKR","currency_rate":"61.********","received_amount":"6184.62","bank_name":"lang.national-bank-of-pakistan","bank_account_name":"Lin Hun R","bank_account_number":"*****************","branch_code":null,"status":"pending","status_display":"pending","updated_by":null,"updated_at":"28\/06\/2023 16:30:35"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null,"table_total":{"amount":"100.00","charges":"10.00","received_amount":"6184.62","transfer_amount":"100.00"},"summary":{"total_transfer_requested":"26672.19","total_transfer":"100.00","total_charge":"10.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.r3iSRrdwV3qF0NdOs8GtPRe-xQSitCUDu24gCzZvbP8","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"095b5f57-6384-48b8-bebb-30db8049ffae"}
     * ##docs end##
     */

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Telegraphic List
     * @module = user,app
     * @path = tt-transfer/get-telex-transfer-list
     * @method = post
     * @description = To get telegrahic list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
    
     * @body = isLeader|<isLeader>|string|optional|0/1|able to see all downlines transactions
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = transaction_id|<transaction_id>|string|optional|TT123456|Transfer Transaction's Id.
     * @body = name|<name>|string|optional|asd|Member's name.
     * @body = phone_no|<phone_no>|integer|optional|011111111|User's phone_no.
     * @body = status|<status>|integer|optional|1|Transfer Transaction's status (Get From Dropdown : telex_transfer_status_list).
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = updated_from|<updated_from>|string|optional|2022-08-28|Updated Date filter.
     * @body = updated_to|<updated_to>|string|optional|2022-08-28|Updated Date filter.

     * @response = {"data":{"list":[{"id":12,"transfer_amount":"1000.00","from_currency_iso":"MYR","currency_iso":"USD","status":"cancel","status_display":"Cancel","bank_account_name":"ABC","created_at":"03\/05\/2023 19:19:37","updated_at":"03\/05\/2023 19:50:46"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null,"table_total":{"amount":"1000.00"}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************************.-ASRlWpFLnZZcjDdfJCi69zzUwKfmVjrx6GuxAXlCH0","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"b6b131ad-f9e3-464e-99a8-60d5b5af8c17"}
     * ##docs end##
     */

    public function getTelexTransferList(Request $request){
         $validator = Validator::make($request->all(), [
            "isLeader" => "integer",
            "limit" => "integer",
            "page" => "integer",
            "username" => "string",
            "name" => "string",
            "member_id" => "string",
            "phone_no" => "string",
            "transaction_id" => "string",
            "status" => "integer",
            "from_date" => "required_with:to_date|string|date_format:Y-m-d",
            "to_date" => "required_with:from_date|string|date_format:Y-m-d|after_or_equal:from_date",
            "updated_from" => "required_with:updated_to|string|date_format:Y-m-d",
            "updated_to" => "required_with:updated_from|string|date_format:Y-m-d|after_or_equal:updated_from",
            "currency" => "integer",
            "subject_id" => "integer",
            "see_all" => "integer|in:1,0",
            "export" => "in:1,0|nullable",
            "export_data" => "required_if:export,==,1",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
            "bank_export" => "in:1,0|nullable",
            "bank_export_file_name" => "required_if:bank_export,==,1",
         ]);
 
         if ($validator->fails()) {
             abort(400, json_encode($validator->errors()));
         }
 
         $res = TelexTransfer::getTelexTransferList($validator->validated());
 
         abort(200,json_encode(["data"=>$res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Telegraphic Transfer Detail
     * @module = admin
     * @path = tt-transfer/get-telex-transfer-detail
     * @method = POST
     * @description = To get telegraphic transfer detail.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|interger|required|1|Telegraphic Transfer's id.
     * 
     * @response = {"data":{"id":7,"transaction_id":"TT935029","created_at":"02\/05\/2023 19:17:26","member_id":"*********","name":"ky test","phone_no":"60-*********","bank_name":"Malayan Bank","bank_account_name":"ABC","bank_account_number":"123456","branch_code":null,"amount":"100.00","processing_fee":"0.00","transfer_amount":"100.00","currency_iso":"VND","currency_rate":"5257.********","received_amount":"525714.63","status":"pending","status_display":"Pending","type":"normal-transfer","type_display":"Normal Transfer","remark":null,"updated_by":null,"updated_at":"02\/05\/2023 19:17:26"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************.kGk3fd4Hj_BbWRG1483p1r1NCuQizVkACaHbplsi1AI","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"385bb642-a3ea-4e2a-85fb-2d33e3e0c797"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Telegraphic Transfer Detail
     * @module = user,app
     * @path = tt-transfer/get-telex-transfer-detail
     * @method = POST
     * @description = To get telegraphic transfer detail.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|interger|required|1|Telegraphic Transfer's id.
     * 
     * @response = {"data":{"id":11,"received_amount":"224.20","transfer_amount":"1000.00","processing_fee":"1.00","total_deduct":"1001.00","bank_name":"Affin Bank","bank_account_name":"ABC","bank_account_number":"123","branch_code":null,"currency_iso":"USD","from_currency_iso":"MYR","transaction_id":"TT498051","status":"cancel","status_display":"Cancel","type":"normal-transfer","type_display":"normal-transfer","created_at":"03\/05\/2023 19:18:27","updated_at":"03\/05\/2023 19:20:44"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************.G17sh0gjNMILF7QST33svBdBXxy0ohWbJp2y85cccPo","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"676fef2d-20ae-4699-895e-9efbc8fae5fb"}
     * ##docs end##
     */
    public function getTelexTransferDetail(Request $request){

        $validator = Validator::make($request->all(), [
            'id'=> 'required|exists:telex_transfer,id',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $report = Models\TelexTransfer::getTelexTransferDetail($validator->validated());

        $data['data'] = $report;
        abort(200,json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Edit Telex Transfer
     * @module = admin
     * @path = tt-transfer/edit-telex-transfer
     * @method = POST
     * @description = To edit telegraphic transfer.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|required|1|Telegraphic Transfer's id.
     * @body = status|<status>|integer|optional|1|Telegraphic Transfer's Status (Get From dropdown : telex_transfer_edit_status_list). 
     * @body = remark|<remark>|string|optional|remark|Telegraphic Transfer's remark. 
     * 
     * @response = {"status":true,"message":"Update Successfully","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL3R0LXRyYW5zZmVyL2VkaXQtdGVsZXgtdHJhbnNmZXIiLCJpYXQiOjE2ODMwODExMjUsImV4cCI6MTY4MzA5MTI1NSwibmJmIjoxNjgzMDg3NjU1LCJqdGkiOiJVNVYzVTZvTmJrOXV0R1NmIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.4QMim31LoWHPljT7xSZG3IcUc-D6SOXyHNoJ6nm5GY8","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"b0fa9af1-46e7-4042-864c-bedecd469ebc"}
     * ##docs end##
     */

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Edit Telex Transfer
     * @module = user,app
     * @path = tt-transfer/edit-telex-transfer
     * @method = POST
     * @description = To edit telegraphic transfer.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|required|1|Telegraphic Transfer's id.
     * @body = status|<status>|integer|optional|1|Telegraphic Transfer's Status (Get From dropdown : telex_transfer_edit_status_list). 
     * 
     * @response = {"status":true,"message":"Update Successfully","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL3R0LXRyYW5zZmVyL2VkaXQtdGVsZXgtdHJhbnNmZXIiLCJpYXQiOjE2ODMwODExMjUsImV4cCI6MTY4MzA5MTI1NSwibmJmIjoxNjgzMDg3NjU1LCJqdGkiOiJVNVYzVTZvTmJrOXV0R1NmIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.4QMim31LoWHPljT7xSZG3IcUc-D6SOXyHNoJ6nm5GY8","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"b0fa9af1-46e7-4042-864c-bedecd469ebc"}
     * ##docs end##
     */
    public function editTelexTransfer(Request $request){
        // User and Apps - Only Cancel
        if(MODULE != 'admin') $request->merge(['status' => Models\TelexTransfer::$status['cancel']]);
        
        $validator = Validator::make($request->all(), [
            'id'=> [
                'required',
                'integer',
                function ($q, $value, $fail) use (&$slug,&$curBank,&$request) {
                    $telexTransferRes = Models\TelexTransfer::where('id',$value)
                                        ->when(in_array(MODULE,['app','user']),function ($q){
                                            return $q->where('user_id',auth()->user()->id);
                                        })->when(($request->status == Models\TelexTransfer::$status['in-progress']),function ($q){
                                            return $q->whereIn('status', Arr::only(Models\TelexTransfer::$status,['pending']));
                                        })->when(($request->status == Models\TelexTransfer::$status['approved']),function ($q){
                                            return $q->whereIn('status', Arr::only(Models\TelexTransfer::$status,['in-progress']));
                                        })->when(($request->status == Models\TelexTransfer::$status['rejected']),function ($q){
                                            return $q->whereIn('status', Arr::only(Models\TelexTransfer::$status,['pending','in-progress']));
                                        })->when(($request->status == Models\TelexTransfer::$status['cancel']),function ($q){
                                            return $q->whereIn('status', Arr::only(Models\TelexTransfer::$status,['pending']));
                                        })->when(($request->status == Models\TelexTransfer::$status['pending']),function ($q){
                                            return $q->whereIn('status', Arr::only(Models\TelexTransfer::$status,['pending']));
                                        })
                                        ->first();
                    if(empty($telexTransferRes)){
                        switch (array_search($request->status,Models\TelexTransfer::$status)) {
                            case 'in-progress':
                                $fail(__('lang.tt-transfer-status-progress-error'));
                                break;
                            
                            case 'approved':
                                $fail(__('lang.tt-transfer-status-approved-error'));
                                break;

                            case 'rejected':
                                $fail(__('lang.tt-transfer-status-rejected-error'));
                                break;

                            case 'cancel':
                                $fail(__('lang.tt-transfer-status-cancel-error'));
                                break;
                            
                            default:
                                $fail(__('validation.exists', ["attribute" => str_replace('_', ' ', $q)]));
                                break;
                        }
                        return;
                    }
                }
            ],
            'status' => [
                'required',
                'integer',
                function ($q, $value, $fail){
                    $editStatusList = Arr::only(Models\TelexTransfer::$status,['cancel']);
                    if(MODULE == 'admin') $editStatusList = Arr::except(Models\TelexTransfer::$status,['cancel']);

                    if(!in_array($value,$editStatusList)){
                        $fail(__('validation.in', ["attribute" => str_replace('_', ' ', $q)]));
                        return;
                    }
                }
            ],
            "remark" => [
                "required_if:status,==,".Models\TelexTransfer::$status['rejected'],
                "string",
            ]
        ],[
            "remark.required_if" => __('validation.required_if',["value"=>'rejected'])
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $updateRes = Models\TelexTransfer::editTelexTransfer($validator->validated());

        abort(200, $updateRes);
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Favourite Bank Detail
     * @module = user
     * @path = tt-transfer/get-favourite-bank-detail
     * @method = POST
     * @description = To get favourite bank details.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = id|<id>|integer|required|1|Favourite Bank's id.
     * @response = {"data":{"id":2,"bank_id":3,"account_number":"*********","account_holder":"Test Bank Account","branch_code":null,"status":"active","status_display":"Active"},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************.R0pikKggNhmTgZzRzM2wiuQujemD-CYzdgHijfEgKBM","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.*************** sec","log_id":"c875f670-85c1-4a31-9515-ed5fd559237e"}
     * ##docs end##
     */
    public function getFavouriteBankDetail(Request $request){
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer|exists:favourite_bank,id,user_id,'.(auth()->user()->id ?? null)
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $favouriteBank = FavouriteBank::getDetail($validator->validated());
        $data["data"] = $favouriteBank;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Edit Favourite Bank
     * @module = user
     * @path = tt-transfer/edit-favourite-bank
     * @method = POST
     * @description = To edit favourite bank.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = id|<id>|integer|required|1|Favourite Bank's id.
     * @body = bank_id|<bank_id>|integer|Optional|1|Bank's Id.
     * @body = account_holder|<account_holder>|string|Optional|XXX|Bank's Account Holder Name.
     * @body = account_number|<account_number>|string|Optional|XXX|Bank's Account Number.
     * @body = branch_code|<branch_code>|string|optional|XXX|Bank's Branch Code.
     * @body = is_favourite|<is_favourite>|integer|Optional|1/0|To Set Favaourite Bank's Account.
     * @response = {"status":true,"message":"Granted","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.UmKOmyST_I9U3IY-Ml3fqAjmesfp4cnSHqDlIWhzgOQ","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************* sec","log_id":"d0f98454-d00d-4fa2-bc5a-1514a8581a31"}
     * ##docs end##
     */
    public function editFavouriteBank(Request $request){
        $bankList = [];
    
        $validator = Validator::make($request->all(), [
            'id' => [
                'required',
                'integer',
                function ($q, $value, $fail) use($request, &$bankList){
                    $favouriteBank = Models\FavouriteBank::with('bank')->where('id',$value)->where('user_id',(auth()->user()->id ?? null))->first();
                    $countryId = $favouriteBank->bank->country_id;
                    $bankList =  Models\Bank::where('country_id',$countryId)
                        ->where('status',Models\Bank::$status['active'])
                        ->where('transfer_status',Models\Bank::$transferStatus['active'])
                        ->get()->pluck('id')->toArray();
                }
            ],
            "bank_id" => [
                "integer",
                function ($q, $value, $fail) use($request, &$bankList){
                    if(!in_array($value,$bankList)){
                        $fail(__('validation.exists'));
                        return;
                    }
                }
            ],
            "account_holder" => [
                "string"
            ],
            "account_number" => [
                "string",
                "regex:/^[0-9]*$/",
                "max:50"
            ],
            "branch_code" => [
                "nullable",
                "string",
            ],
            "is_favourite" => [
                "integer",
                "in:1,0"
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        FavouriteBank::edit($validator->validated());
        abort(200);
    }
}
