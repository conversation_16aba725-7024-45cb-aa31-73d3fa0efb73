<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Models\Providers\MtUser;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class MtUserController extends Controller
{
    public function __construct() {}

    public function getUserList(Request $request)
    {
        $users = MtUser::all();

        abort(200, json_encode(['data' => $users]));
    }

    public function getUserDetail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:mt_users,id,deleted_at,NULL',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $user = MtUser::find($request->id);

        if (!$user) {
            abort(400, 'MT User not found.');
        }

        abort(200, json_encode(['data' => $user]));
    }

    public function storeUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "username" => "required|string|unique:mt_users",
            "password" => "required|string",
            "is_online" => "required|boolean",
            "store_id" => "required|exists:store,id,deleted_at,NULL",
            "user_id" => "required|exists:userss,id,deleted_at,NULL",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $user = MtUser::create([
            "username" => $request->username,
            "password" => Hash::make($request->password),
            "is_online" => $request->is_online,
            "store_id" => $request->store_id,
            "user_id" => $request->user_id,
        ]);

        abort(200, json_encode(['data' => $user]));
    }

    public function updateUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "id" => "required|exists:mt_users,id,deleted_at,NULL",
            "username" => ['required', 'string', Rule::unique('mt_users')->ignore($request->id)],
            "password" => "nullable|string",
            "is_online" => "required|boolean",
            "store_id" => "required|exists:store,id,deleted_at,NULL",
            "user_id" => "required|exists:userss,id,deleted_at,NULL",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $user = MtUser::find($request->id);

        if (!$user) {
            abort(400, 'MT User not found.');
        }

        $data = [
            "username" => $request->username,
            "is_online" => $request->is_online,
            "store_id" => $request->store_id,
            "user_id" => $request->user_id,
        ];

        if ($password = $request->password) {
            $data['password'] = Hash::make($password);
        }

        $user->update($data);

        abort(200, json_encode(['data' => $user]));
    }

    public function deleteUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "id" => "required|exists:mt_users,id,deleted_at,NULL",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $user = MtUser::find($request->id);

        if (!$user) {
            abort(400, 'MT User not found.');
        }

        $user->delete();

        abort(200, json_encode(['data' => []]));
    }
}
