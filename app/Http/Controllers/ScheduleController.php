<?php

namespace App\Http\Controllers;

use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;
use App\Models;
use App\Models\Schedule;
use App\Models\UserApi;
use App\Traits;

class ScheduleController extends Controller
{
    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Schedule API
     * @module = admin
     * @path = schedule/get-schedule-api
     * @method = post
     * @description = To get schedule api.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @response = {"data":[{"id":1,"parent_id":0,"name":"tt-transfer","name_display":"tt-transfer","status":1,"status_display":"Active"},{"id":4,"parent_id":0,"name":"withdrawal","name_display":"withdrawal","status":1,"status_display":"Active"},{"id":2,"parent_id":1,"name":"tt-instant-transfer","name_display":"Instant TT Transfer","status":1,"status_display":"Active"},{"id":3,"parent_id":1,"name":"tt-normal-transfer","name_display":"TT Transfer","status":1,"status_display":"Active"}],"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL3NjaGVkdWxlL2dldC1zY2hlZHVsZS1hcGkiLCJpYXQiOjE2ODM2MTUxMDYsImV4cCI6MTY4MzYxODcxOCwibmJmIjoxNjgzNjE1MTE4LCJqdGkiOiI1THIzNUNsY3Zscm9GV3hSIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.jDGyePUdT29alhvwInxRfdZvfQ8V4pklB7zbNronkyU","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.080785989761353 sec","log_id":"c69fa22b-92b0-4722-bfaf-0f5d687a83b0"}
     * ##docs end##
     */
    public function getScheduleAPI(Request $request){
        $validator = Validator::make($request->all(), [
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = UserApi::getScheduleAPI($validator->validated());

        abort(200,json_encode(["data"=>$res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Add Schedule
     * @module = admin
     * @path = schedule/add-schedule
     * @method = POST
     * @description = To add schedule.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * 
     * @body = api_id|<api_id>|array|required|[1,2,3,4,5]|API's id.
     * @body = api_id.*|<api_id.*>|integer|required|1|API's id. (Get From : schedule/get-schedule-api)
     * @body = start_date|<start_date>|string|required|2022-08-28|Schedule Start Date.
     * @body = end_date|<end_date>|string|required|2022-08-28|Schedule End Date.
     * @body = start_time|<start_time>|string|required|00:00:00|Schedule Start Time.
     * @body = end_time|<end_time>|string|required|12:00:00|Schedule End Time.
     * @body = status|<status>|integer|required|1| Schedule Status. (Dropdown: schedule_status)
     * @body = remark|<remark>|string|required|Remark|Schedule Remark.
     * 
     * @response = {"status":true,"message":"Successful","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL3NjaGVkdWxlL2FkZC1zY2hlZHVsZSIsImlhdCI6MTY4MzYxNTEwNiwiZXhwIjoxNjgzNjE5MDkxLCJuYmYiOjE2ODM2MTU0OTEsImp0aSI6ImptTUJyTVRqY1JPTVVKMkEiLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.9FOWMiL7H9-NGyu2DV6azdLuBI4ROtqZBkYcfEJfAZc","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.081587076187134 sec","log_id":"965a262a-7b52-4793-9caa-b790e6052c40"}
     * ##docs end##
     */
    public function addSchedule(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "api_id" => "required|array",
            "api_id.*" => [
                "required",
                "distinct",
                "exists:user_api,id,status,".Models\UserApi::$status['active']
            ],
            "start_date" => "required|string|date_format:Y-m-d",
            "end_date" => "required|string|date_format:Y-m-d|after_or_equal:start_date",
            "start_time" => "required|string|date_format:H:i:s",
            "end_time" => "required|string|date_format:H:i:s|after:start_time",
            "status" => "required|in:".implode(",",Models\Schedule::$status),
            "remark" => "nullable|string|max:255"
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Models\Schedule::addSchedule($validator->validated());

        abort(200,is_array($res) ? json_encode($res) : $res);
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = Schedule List
     * @module = admin
     * @path = schedule/gget-schedule-list
     * @method = post
     * @description = To get schedule list.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
    
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = status|<status>|integer|optional|1| Schedule Status. (Dropdown: schedule_status)
     * @body = api_id|<api_id>|integer|optional|1|API's id. (Get From : schedule/get-schedule-api)
     * @body = start_date|<start_date>|string|optional|2022-11-01|Start DAte filter.
     * @body = end_date|<end_date>|string|optional|2022-11-01|End Date filter.

     * @response = {"data":{"list":[{"id":7,"start_date":"10\/05\/2023","end_date":"10\/05\/2023","start_time":"00:00:00","end_time":"10:55:00","function":"withdrawal","function_display":"withdrawal","status":"active","status_display":"Active","remark":"TEEST","updated_by":null,"updated_at":"10\/05\/2023 10:54:48"}],"pagination":{"current_page":1,"from":1,"last_page":7,"per_page":1,"to":1,"total":7},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL3NjaGVkdWxlL2dldC1zY2hlZHVsZS1saXN0IiwiaWF0IjoxNjgzNjg1OTE2LCJleHAiOjE2ODM2OTA5MjgsIm5iZiI6MTY4MzY4NzMyOCwianRpIjoibFhabWY1Rk1nR2pLOGVoYiIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.BtkR-zAmWpbKWPRqFThT8YH0MqKrEg1I8O_2B7uSAKc","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.071294784545898 sec","log_id":"6982d043-6c26-4422-a22f-d997049c57bc"}
     * ##docs end##
     */

    public function getScheduleList(Request $request){
         $validator = Validator::make($request->all(), [
             "limit" => "integer",
             "page" => "integer",
             "status" => "integer",
             "api_id" => "integer",
             "api_id" => "integer",
             "start_date" => "required_with:end_date|string|date_format:Y-m-d",
             "end_date" => "required_with:start_date|string|date_format:Y-m-d",
             "see_all" => "integer|in:1,0",
         ]);
 
         if ($validator->fails()) {
             abort(400, json_encode($validator->errors()));
         }
 
         $res = Schedule::getScheduleList($validator->validated());
 
         abort(200,json_encode(["data"=>$res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Edit Schedule
     * @module = admin
     * @path = schedule/edit-schedule
     * @method = POST
     * @description = To edit schedule.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     *
     * @body = id|<id>|integer|required|1|Schedule's Id
     * @body = status|<status>|integer|required|1| Schedule Status. (Dropdown: schedule_status)
     * 
     * @response = {"status":true,"message":"Update Successfully","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRrc2guY29tL3NjaGVkdWxlL2VkaXQtc2NoZWR1bGUiLCJpYXQiOjE2ODM2MTUxMDYsImV4cCI6MTY4MzYyMTI4NSwibmJmIjoxNjgzNjE3Njg1LCJqdGkiOiJONEdoemIyZzgwZWFMYjZQIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.wrP93gxUQ_FK1LczRAAN3zpi8us5cJAh34aG3xgINaQ","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.088824987411499 sec","log_id":"acdf80a3-6eba-4650-bf08-02ef37aa829d"}
     * ##docs end##
     */
    public function editSchedule(Request $request){
        $validator = Validator::make($request->all(), [
            'id'=> [
                'required',
                'integer',
                'exists:schedule,id'
            ],
            'status' => [
                'required',
                'integer',
                'in:'.implode(",",Models\Schedule::$status),
            ]
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $updateRes = Models\Schedule::editSchedule($validator->validated());

        abort(200, $updateRes);
    }
}
