<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models;

class MachineController extends Controller
{
    public function addMachine(Request $request): void
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'description' => 'required|string',
            "store_id" => [
                "required",
                "integer",
                function ($q, $value, $fail) {
                    $store = Models\Store::where('store_id', $value)->first();
                    if (empty($store)) {
                        abort(400, json_encode(['Invalid Store']));
                    }
                },
            ],
            "service_id" => [
                "required",
                "integer",
                function ($q, $value, $fail) {
                    $service = Models\Services::find($value);
                    if (empty($service)) {
                        abort(400, json_encode(['Invalid Service']));
                    }
                },
            ],
            "status" => "integer|in:".implode(",", Models\Machine::$status),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data = $validator->validated();

        $store = Models\Store::where("store_id", $data["store_id"])->first();
        if (!$store) {
            abort(500);
        }

        $data["store_id"] = $store->id;

        Models\Machine::addMachine($data);

        abort(200);
    }

    public function getMachineList(Request $request): void
    {
        $validator = Validator::make($request->all(), [
            'status' => 'integer|in:'.implode(',', Models\Machine::$status),
            'order_by' => 'string',
            'order_sort' => 'string|in:asc,desc',
            'limit' => 'integer',
            'page' => 'integer',
            'see_all' => 'integer|in:0,1',
            'created_from' => 'required_with:created_to|string|date_format:Y-m-d',
            'created_to' => 'required_with:created_from|string|date_format:Y-m-d|after_or_equal:created_from',
            'updated_from' => 'required_with:updated_to|string|date_format:Y-m-d',
            'updated_to' => 'required_with:updated_from|string|date_format:Y-m-d|after_or_equal:updated_from',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $report = Models\Machine::getMachineList($validator->validated());

        $data = ['data' => $report];
        abort(200, json_encode($data));
    }

    public function getMachineDetail(Request $request): void
    {
        $validator = Validator::make($request->all(), [
          'id' => [
              'required',
              'string',
              function ($q, $value, $fail) {
                  $machine = Models\Machine::find($value);
                  if (empty($machine)) {
                      abort(400, json_encode(['Invalid Machine']));
                  }
              },
          ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $report = Models\Machine::getMachineDetail($validator->validated());

        $data['data'] = $report;
        abort(200, json_encode($data));
    }

    public function editMachine(Request $request): void
    {
        $validator = Validator::make($request->all(), [
            'id' => [
                'required',
                'string',
                function ($q, $value, $fail) {
                    $machine = Models\Machine::find($value);
                    if (empty($machine)) {
                        abort(400, json_encode(['Invalid Machine']));
                    }
                },
            ],
            'name' => 'required|string',
            'description' => 'required|string',
            "store_id" => [
                "required",
                "integer",
                function ($q, $value, $fail) {
                    $store = Models\Store::where('store_id', $value)->first();
                    if (empty($store)) {
                        abort(400, json_encode(['Invalid Store']));
                    }
                },
            ],
            "service_id" => [
                "required",
                "integer",
                function ($q, $value, $fail) {
                    $service = Models\Services::find($value);
                    if (empty($service)) {
                        abort(400, json_encode(['Invalid Service']));
                    }
                },
            ],
            'status' => 'integer|in:'.implode(',', Models\Machine::$status),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data = $validator->validated();

        $store = Models\Store::where("store_id", $data["store_id"])->first();
        if (!$store) {
            abort(500);
        }

        $data["store_id"] = $store->id;

        $updateres = models\Machine::editmachine($data);

        abort(200, json_encode(['data' => $updateres]));
    }

    public function deleteMachine(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => [
                'required',
                'string',
                function ($q, $value, $fail) {
                    $machine = Models\Machine::find($value);
                    if (empty($machine)) {
                        abort(400, json_encode(['Invalid Machine']));
                    }
                },
            ],
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }


        $deleteres = models\machine::deleteMachine($validator->validated());

        abort(200, json_encode(['data' => $deleteres]));
    }


}
