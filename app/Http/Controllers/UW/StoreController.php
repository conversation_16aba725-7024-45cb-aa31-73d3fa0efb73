<?php

namespace App\Http\Controllers\UW;

use App\Models\Store;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;

class StoreController extends Controller
{
    public function all(Request $request)
    {
        $storeIds = $request->store_ids;

        $stores = Store::select(DB::raw('store_id AS id'), 'name')
            ->when($storeIds, function ($query, $storeIds) {
                $query->whereIn('store_id', $storeIds);
            })
            ->active()
            ->get();

        abort(200, json_encode([
            'data' => $stores,
        ]));
    }
}
