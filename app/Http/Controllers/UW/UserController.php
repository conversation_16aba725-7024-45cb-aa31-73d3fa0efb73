<?php

namespace App\Http\Controllers\UW;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class UserController extends Controller
{
    public function all(Request $request)
    {
        $storeId = $request->store_id;
        $last_user_id = $request->last_user_id;

        $users = User::where('store_id', $storeId)
            ->where('id', '>', $last_user_id)
            ->user()
            ->active()
            ->get()
            ->map(function ($user) {
                return [
                    'user_id' => $user->id,
                    'username' => $user->username,
                    'account' => $user->uuid,
                    'phone_no' => $user->phone_no,
                    'store_id' => $user->store_id,
                ];
            });

        abort(200, json_encode(['data' => $users]));
    }
}
