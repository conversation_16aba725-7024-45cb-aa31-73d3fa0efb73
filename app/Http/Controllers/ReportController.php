<?php

namespace App\Http\Controllers;

use App\Models\MaintenanceSales;
use App\Models\Credit;
use App\Models\User;
use App\Models\UserCard;
use App\Models\UserSales;
use App\Models\SMSLog;
use App\Models\ExportReport;
use App\Models;
use App\Models\CreditTransaction;
use App\Models\ExTransfer;
use App\Models\Deposit;
use App\Models\WithdrawalThirdParty;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use App\Traits;

class ReportController extends Controller
{
    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = report/otp-sent-history
     * @module = admin
     * @path = report/otp-sent-history
     * @permissionName = OTP Sent History
     * @menuType = menu
     * @method = post
     * @description = To get OTP Sent History.
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
     * @body = from_date|<from_date>|string|optional|2022-07-06|To filter listing by created date column
     * @body = to_date|<to_date>|string|optional|2022-07-07|To filter listing by created date column
     * @body = phone_no|<phone_no>|string|optional|0123456789|To filter listing by phone number column
     * @body = name|<name>|string|optional|ghostmate|To filter listing by name column
     * @body = member_id|<member_id>|integer|optional|12345|To filter listing by member_id column
     * @body = email|<email>|string|optional|<EMAIL>|To filter listing by email column
     * @body = otp|<otp>|string|optional|ghostmate|To filter listing by code column
     * @body = request_type|<request_type>|string|optional|register-otp|To filter listing by request type column
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = country|<country>|integer|optional|100|Country filter. (Dropdown: country)
     * @response = {"data":{"list":[{"created_at":"2023-02-27","name":null,"phone_no":"***********","provider":"GS","type":"lang.login-otp","code":"449297","status":"Successful","country":"malaysia","country_display":"Malaysia","description":"[Pet Universe] Your login OTP code is 449297. This OTP is valid for 2 minutes only.","sent_on":"2023-02-27 11:40:07 AM"},{"created_at":"2023-02-27","name":null,"phone_no":"***********","provider":null,"type":"lang.login-otp","code":"463110","status":"Successful","country":"malaysia","country_display":"Malaysia","description":"[Pet Universe] Your login OTP code is 463110. This OTP is valid for 2 minutes only.","sent_on":"2023-02-27 11:37:45 AM"},{"created_at":"2023-02-27","name":null,"phone_no":"***********","provider":"GS","type":"lang.login-otp","code":"886548","status":"Successful","country":"malaysia","country_display":"Malaysia","description":"[Pet Universe] Your login OTP code is 886548. This OTP is valid for 2 minutes only.","sent_on":"2023-02-27 11:35:57 AM"},{"created_at":"2023-02-27","name":null,"phone_no":"***********","provider":"GS","type":"lang.login-otp","code":"242805","status":"Successful","country":"malaysia","country_display":"Malaysia","description":"[Pet Universe] Your login OTP code is 242805. This OTP is valid for 2 minutes only.","sent_on":"2023-02-27 11:28:24 AM"},{"created_at":"2023-02-27","name":null,"phone_no":"***********","provider":"GS","type":"Register","code":"588267","status":"Successful","country":"malaysia","country_display":"Malaysia","description":"[Pet Universe] Your OTP code for account registration is 588267. This OTP is valid for 2 minutes only.","sent_on":"2023-02-27 09:58:18 AM"}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":5,"total":5},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.ONUn4pPKhtIv1tUaFXb_GyXCMc3eWoNj_ChWGBGP3v4","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.************** sec","log_id":"3ab3fceb-b448-4fd9-a9dc-9552ccd3bc50"}
     * ##docs end##
     */

    public function getOTPSentReport(Request $request)
    {
        $report = [];
        $validator = Validator::make($request->all(), [
            'order_by' => 'nullable|string',
            'order_sort' => 'nullable|string|in:asc,desc',
            "from_date" => "nullable|date|date_format:Y-m-d",
            "to_date" => "nullable|date|date_format:Y-m-d|after_or_equal:from_date",
            "phone_no" => 'nullable|string',
            "name" => 'nullable|string',
            "member_id" => "nullable|int",
            "email" => "nullable|string",
            "otp" => 'integer',
            "provider" => 'string|in:' . implode(",", array_keys(SMSLog::$provider)),
            'request_type' => 'string|in:' . implode(",", array_keys(SMSLog::$type)),
            "country" => "integer",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $report = SMSLog::list($validator->validated());
        $data['data'] = $report;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Get Export Report
     * @module = admin
     * @path = /report/export-report
     * @method = POST
     * @description = To Get Export Report.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = name|<name>|string|optional|address-listing|Report Name (Get From Dropdown - export_report_name).
     * @body = order_by|<order_by>|string|optional|created_at|Order listing by column. (created_at)
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @response = {"success":true,"message":"Granted","data":[{"id":1,"created_at":"2023-01-03 11:30:53","report_name":"address-listing","file_name":"address_listing_20230103113053","completed":"completed","deleted":"No"}],"pagination":{"current_page":1,"from":1,"last_page":1,"path":"http:\/\/local-api-admin.tronumx.com\/v1\/report\/export-report","per_page":30,"to":1,"total":1,"links":{"first":"http:\/\/local-api-admin.tronumx.com\/v1\/report\/export-report?page=1","last":"http:\/\/local-api-admin.tronumx.com\/v1\/report\/export-report?page=1","next":null,"prev":null}},"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRyb251bXguY29tL3YxL3JlcG9ydC9leHBvcnQtcmVwb3J0IiwiaWF0IjoxNjcyNzE3MzM0LCJleHAiOjE2NzI3MjE3MzUsIm5iZiI6MTY3MjcxODEzNSwianRpIjoiZUxmVGxwd3liSUlaeDVDSCIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.t-G4MHCUwdZlhNynESZq6u_F2x_XWzvBVgR-uyM3F0Q","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.14558887481689 sec","log_id":"30ad598e-f4e8-47f5-aedb-448f5173fd6d"}
     * ##docs end##
     */
    public function getExportReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'string',
            'order_by' => 'string',
            'order_sort' => 'string|in:asc,desc',
            'limit' => 'integer',
            'page' => 'integer',
            'see_all' => 'integer|in:0,1',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $list = ExportReport::get($validator->validated());
        abort(200, json_encode($list));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Download Export Report
     * @module = admin
     * @path = /report/download-export-report
     * @method = POST
     * @description = To Download Export Report.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = id|<id>|integer|required|1|Export Report's Id.
     * @response = {"success":true,"message":"Granted","data":"data:application\/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,SFRUUC8xLjAgMjAwIE9LDQowOiAgICAgICAgICAgICAgICAgICBDb250ZW50LVR5cGU6IGFwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5zcHJlYWRzaGVldG1sLnNoZWV0DQpDYWNoZS1Db250cm9sOiAgICAgICBwdWJsaWMNCkNvbnRlbnQtRGlzcG9zaXRpb246IGF0dGFjaG1lbnQ7IGZpbGVuYW1lPWRlbW8ueGxzeA0KRGF0ZTogICAgICAgICAgICAgICAgVHVlLCAwMyBKYW4gMjAyMyAwNDowNjo1OSBHTVQNCkxhc3QtTW9kaWZpZWQ6ICAgICAgIFR1ZSwgMDMgSmFuIDIwMjMgMDM6MzA6NTQgR01UDQoNCg==","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLnRyb251bXguY29tL3YxL3JlcG9ydC9kb3dubG9hZC1leHBvcnQtcmVwb3J0IiwiaWF0IjoxNjcyNzE3MzM0LCJleHAiOjE2NzI3MjI0MTksIm5iZiI6MTY3MjcxODgxOSwianRpIjoiRWlTbVdtQW9Ga3NiME5lcyIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.PMq9DmuEWzcTrzM-6K7agSvLOQfHOGkjwhK5zPo1AQA","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.15116214752197 sec","log_id":"a757bd49-5ed7-4dce-bdfa-770294f07a7b"}
     * ##docs end##
     */
    public function downloadExportReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:export_report,id,deleted,0',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data = ExportReport::download($validator->validated());
        abort(200, json_encode(['data' => $data]));
    }

    /* This is needed to generate API docs
     * ##docs start##
     * @postmanName = report/sales-summary-report
     * @module = admin
     * @path = report/sales-summary-report
     * @permissionName = Sales Summary Report
     * @menuType = menu
     * @method = post
     * @description = To get sales summary report.
     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.
     * @body = leader_username|<leader_username>|string|optional|leaderusername|Filter by leader username.
     * @body = member_id|<member_id>|string|optional|member_id|Filter by user id.
     * @body = phone_no|<phone_no>|string|optional|leaderusername|Filter by phone no.
     * @body = from_date|<from_date>|string|optional|2022-07-06|To filter listing by date column
     * @body = to_date|<to_date>|string|optional|2022-07-07|To filter listing by date column
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|sponsor-bonus|name of the export file. Required for export
     * @response = {"data":{"list":[{"date":"01\/10\/2023","register":"0.00","telex_transfer":"0.00","transfer":"0.00","withdrawal":"0.00","deposit":{"manual-bank":"200.00","online-bank":"0.00","crypto":"0.00","ewallet":"0.00","total":"200.00"}}],"table_total":{"register":"0.00","telex_transfer":"0.00","transfer":"0.00","withdrawal":"0.00","deposit":{"manual-bank":"200.00","online-bank":"0.00","crypto":"0.00","ewallet":"0.00","total":"200.00"}},"summary":{"register":"0.00","telex_transfer":0,"transfer":0,"withdrawal":"0.00","deposit":{"manual-bank":"200.00","online-bank":"0.00","crypto":"0.00","ewallet":"0.00","total":"200.00"}},"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************.t0BjI2-SUJ202aBLMbSUVXNwpwb40E_Ae8v5yI1aByI","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"staging","execution_duration":"0.************** sec","log_id":"0aeab53a-010e-4ab5-8f22-672b0eaf4fcc"}
     * ##docs end##
     */

    public function getSalesSummaryReport(Request $request)
    {
        $report = [];
        $validator = Validator::make($request->all(), [
            'order_by' => 'string',
            'order_sort' => 'string|in:asc,desc',
            'leader_username' => 'string',
            'member_id' => 'string',
            'phone_no' => 'string',
            "from_date" => "required_with:to_date|date|date_format:Y-m-d",
            "to_date" => "required_with:from_date|date|date_format:Y-m-d|after_or_equal:from_date|before_or_equal:" . date("Y-m-d"),
            "export" => "in:1,0|nullable",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
            "store_id" => "integer",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $report = UserSales::getSalesSummary($request->all());
        $data['data'] = $report;
        abort(200, json_encode($data));
    }

    public function getMachineSummaryReport(Request $request)
    {
        $report = [];
        $validator = Validator::make($request->all(), [
            'order_by' => 'string',
            'order_sort' => 'string|in:asc,desc',
            'leader_username' => 'string',
            'member_id' => 'string',
            'phone_no' => 'string',
            "from_date" => "required_with:to_date|date|date_format:Y-m-d",
            "to_date" => "required_with:from_date|date|date_format:Y-m-d|after_or_equal:from_date|before_or_equal:" . date("Y-m-d"),
            "export" => "in:1,0|nullable",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
            "store_id" => "integer",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $report = UserSales::getMachineSummary($request->all());
        $data['data'] = $report;
        abort(200, json_encode($data));
    }

    public function getMachineSummaryReportGroupByMachineId(Request $request)
    {
        $report = [];
        $validator = Validator::make($request->all(), [
            'order_by' => 'string',
            'order_sort' => 'string|in:asc,desc',
            'leader_username' => 'string',
            'member_id' => 'string',
            'phone_no' => 'string',
            "from_date" => "required_with:to_date|date|date_format:Y-m-d",
            "to_date" => "required_with:from_date|date|date_format:Y-m-d|after_or_equal:from_date|before_or_equal:" . date("Y-m-d"),
            "export" => "in:1,0|nullable",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
            "store_id" => [
                "required",
                "integer",
                function ($q, $value, $fail) {
                    $store = Models\Store::where('store_id', $value)->first();
                    if (empty($store)) {
                        abort(400, json_encode(['Invalid Store']));
                    }
                },
            ],
            'machine_id' => 'integer',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $report = UserSales::getMachineSummaryGroupByMachineId($request->all());
        $data['data'] = $report;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = Wallet Balance Report
     * @module = admin
     * @path = report/wallet-balance-report
     * @method = post
     * @description = To get balance report.

     * @header = Content-Type|application/json|String|required|application/json|content type to be send to server
     * @header = lang|<lang>|en / cn|yes|en|Language used. Default : en
     * @header = Authorization|Bearer <access_token>|String|required|Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRw...|Bearer token generated during login.

     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = member_id|<member_id>|string|optional|12345|member_id filter.
     * @body = username|<username>|string|optional|sam|username filter.
     * @body = date|<date>|string|optional|2022-11-01|Date filter.
     * @body = see_all|<see_all>|integer|optional|1|Use for export purpose.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = file_name|<file_name>|string|optional|balance-report|name of the export file. Required for export


     * @response = {"data":{"list":[{"user_id":1000282,"member_id":"720251222","username":"60127971002","name":"Danta","balance":{"myr-credit":"1610.00","exp":"210.00","thb-credit":"0.00","usdt-credit":"0.00"}},{"user_id":1000281,"member_id":"323197843","username":"60125738388","name":"yantest1","balance":{"myr-credit":"0.00","exp":"0.00","thb-credit":"0.00","usdt-credit":"0.00"}},{"user_id":1000280,"member_id":"475302691","username":"60123238191","name":"C0011","balance":{"myr-credit":"0.00","exp":"77350.00","thb-credit":"0.00","usdt-credit":"0.00"}},{"user_id":1000279,"member_id":"616713430","username":"60123238190","name":"C0022","balance":{"myr-credit":"0.00","exp":"77350.00","thb-credit":"0.00","usdt-credit":"0.00"}},{"user_id":1000278,"member_id":"593317249","username":"60123338191","name":"T002","balance":{"myr-credit":"0.00","exp":"0.00","thb-credit":"0.00","usdt-credit":"0.00"}},{"user_id":1000277,"member_id":"734985398","username":"60123338190","name":"T001","balance":{"myr-credit":"0.00","exp":"0.00","thb-credit":"0.00","usdt-credit":"0.00"}},{"user_id":1000276,"member_id":"410785195","username":"60189742485","name":"C003","balance":{"myr-credit":"2495.00","exp":"125.25","thb-credit":"0.00","usdt-credit":"0.00"}},{"user_id":1000275,"member_id":"707742198","username":"60189742483","name":"C002","balance":{"myr-credit":"0.00","exp":"1550.00","thb-credit":"0.00","usdt-credit":"0.00"}},{"user_id":1000274,"member_id":"558353108","username":"60189742482","name":"C001","balance":{"myr-credit":"0.00","exp":"2660.00","thb-credit":"0.00","usdt-credit":"0.00"}},{"user_id":1000273,"member_id":"176980295","username":"60123238194","name":"test003","balance":{"myr-credit":"0.00","exp":"250.00","thb-credit":"0.00","usdt-credit":"0.00"}}],"pagination":{"current_page":1,"from":1,"last_page":29,"per_page":10,"to":10,"total":283},"meta":null,"grand_total":{"myr-credit":"4105.00","exp":"159495.25","thb-credit":"0.00","usdt-credit":"0.00"},"summary":{"balance":{"myr-credit":"401856289.44","exp":"525622.75","thb-credit":"14568.00","usdt-credit":"444298.67"}}},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vYXBpLWFkbWluLnRrYXNoLnh5ei9yZXBvcnQvd2FsbGV0LWJhbGFuY2UtcmVwb3J0IiwiaWF0IjoxNzA3OTc3MTkwLCJleHAiOjE3MDc5ODA4NzAsIm5iZiI6MTcwNzk3NzI3MCwianRpIjoib041SXJqN2hQYk9KZjlwaSIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.DrOracePKbGIT3GQzJdsXFFaVCQl2SaWFduVv61YmyA","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"staging","execution_duration":"0.1868109703064 sec","log_id":"cd494085-4dc5-43c6-b613-a307d0aee548"}
     * ##docs end##
     */

    public function getWalletBalanceReport(Request $request)
    {
        if (MODULE == 'user') {
            abort(400, json_encode('Invalid Access.'));
        }

        $validator = Validator::make($request->all(), [
            "see_all" => "integer",
            "limit" => "integer",
            "page" => "integer",
            "member_id" => "string",
            "username" => "string",
            "date" => "string|date_format:Y-m-d",
            "export" => "in:1,0|nullable",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = User::walletBalanceReport($validator->validated());

        abort(200, json_encode(["data" => $res]));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = API Deposit List
     * @module = admin
     * @path = report/get-api-deposit-list
     * @method = POST
     * @description = To get all user deposit api request.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = status|<status>|string|optional|approved|Create status filter. (dropdown: deposit)
     * @body = transaction_id|<transaction_id>|string|optional|9eDtjG38eVuNJwYx|transaction unique id filter
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = export_data|<export_data>|array|optional|{"data": {"created_at": "Date"}|Data need to export value => header. Required for export
     * @body = file_name|<file_name>|string|optional|withdrawal-list|name of the export file. Required for export
     *
     * @response = {"data":{"list":[{"id":805,"username":"66********","transaction_id":"A0006","status":"approved","status_display":"Approved","fmt":[{"type":"balance-promoid","req_data":"{\"language\":\"en\",\"company\":\"TK8\",\"order_id\":\"A0006\",\"amount\":\"10.********\",\"account\":\"********\"}","res_data":"{\"status\":true,\"message\":\"success\",\"data\":{\"status\":true,\"amount\":10,\"promo_id\":1}}","created_at":"06\/05\/2024 11:32:28"}],"tk8":[{"type":"callback\/deposit","req_data":"{\"millis\":**********,\"secret\":\"29454bd52a8b6a821229e60851993881f71b29f6\",\"language\":\"en\",\"company\":null,\"transaction_id\":\"A0006\",\"status\":\"SUCCESS\",\"merchant_reference\":\"TK8_689354\",\"transaction_time\":\"2024-05-06 11:32:28\",\"promo_id\":1,\"signature\":\"1972433dfdd453002b6fd0925e5d540a873620919e5cc798576405fceb6e514a\",\"account\":\"********\",\"amount\":\"10.00\",\"remark\":\"TK8_689354\"}","res_data":"{\"status\":\"SUCCESS\",\"message\":\"Deposit Success!\",\"result\": []}","created_at":"06\/05\/2024 11:32:29"}],"tk8th":[]}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************.NnzGnP5vr23bGjsQLaXmTdBnEiuCwWkVUJqY7MBID5Y","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.*************** sec","log_id":"42cdbce5-db08-43d2-9f9c-dbb585f27dba"}
     * ##docs end##
     */
    public function getAPIDepositReport(Request $request)
    {
        $report = [];
        $validator = Validator::make($request->all(), [
            "order_sort" => "string|in:asc,desc",
            "page" => "integer",
            'transaction_id' => 'string',
            'status' => 'string|in:' . implode(",", array_keys(Deposit::$status)),
            "from_date" => "required_with:to_date|string|date_format:Y-m-d",
            "to_date" => "required_with:from_date|string|date_format:Y-m-d",
            "see_all" => "integer|in:1,0",
            "limit" => "integer",
            "export" => "in:1,0|nullable",
            "export_data" => "required_if:export,==,1",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $report = Deposit::apiList($validator->validated());
        $data['data'] = $report;
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = API Withdrawal List
     * @module = admin
     * @path = report/get-api-withdrawal-list
     * @method = POST
     * @description = To get all user withdrawal api request.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = status|<status>|string|optional|approved|Create status filter. (dropdown: withdrawal_third_party_status)
     * @body = withdrawal_reference|<withdrawal_reference>|string|optional|WB957636|withdrawal_reference
     * @body = from_date|<from_date>|string|optional|2022-08-28|Create Date filter.
     * @body = to_date|<to_date>|string|optional|2022-08-28|Create Date filter.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = export_data|<export_data>|array|optional|{"data": {"created_at": "Date"}|Data need to export value => header. Required for export
     * @body = file_name|<file_name>|string|optional|withdrawal-list|name of the export file. Required for export
     *
     * @response = {"data":{"list":[{"id":5,"withdrawal_reference":"WB957636","created_at":"08\/04\/2024 18:10:38","username":"66********","status":"rejected","status_display":"Rejected","fmt":[],"tk8":[],"tk8th":[{"type":"callback\/withdraw","req_data":"{\"millis\":**********,\"secret\":\"84a08c853107e3260aac84ad937cb3265f0fad92\",\"language\":\"en\",\"company\":null,\"transaction_id\":\"9000002\",\"account\":\"********\",\"amount\":\"1.********\",\"status\":\"REJECTED\",\"merchant_ref\":\"WB957636\",\"transaction_time\":\"2024-05-06 11:48:22\",\"signature\":\"9718b54bf4badf110dcc87e42c27a14c0b40c15b4aee0feae9df8f8871ea5ba6\"}","res_data":"{\"status\":\"SUCCESS\",\"message\":\"Deposit Success!\",\"result\": []}","created_at":"06\/05\/2024 11:48:22"}]}],"pagination":{"current_page":1,"from":1,"last_page":1,"per_page":30,"to":1,"total":1},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.BhBMNZu5TKAtmzqYJL0xDVwbrb3CQtiQ3dK70-vB7oY","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"local","execution_duration":"0.*************** sec","log_id":"c37c98e4-abb7-42d7-bca6-9ca3fa848be1"}
     * ##docs end##
     */

    public function getAPIWithdrawalReport(Request $request)
    {
        $report = [];
        $validator = Validator::make($request->all(), [
            'order_by' => 'nullable|string',
            'order_sort' => 'nullable|string|in:asc,desc',
            "withdrawal_reference" => 'nullable|string',
            'status' => 'string|in:' . implode(",", array_keys(WithdrawalThirdParty::$status)),
            "from_date" => "nullable|date|date_format:Y-m-d",
            "to_date" => "nullable|date|date_format:Y-m-d|after_or_equal:from_date",
            "see_all" => "integer|in:1,0",
            "limit" => "integer",
            "export" => "in:1,0|nullable",
            "export_data" => "required_if:export,==,1",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $report = WithdrawalThirdParty::apiList($validator->validated());
        $data['data'] = $report;
        abort(200, json_encode($data));
    }

    public function getMachineSummary(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "store_id" => "integer",
            "page" => "integer",
            "limit" => "integer",
            "from_date" => "nullable|date|date_format:Y-m-d H:i:s",
            "to_date" => "nullable|date|date_format:Y-m-d H:i:s|after_or_equal:from_date",
            "see_all" => "integer|in:1,0",
            "export" => "in:1,0|nullable",
            "export_data" => "required_if:export,==,1",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data = UserCard::getMachineSummary($validator->validated());
        abort(200, json_encode($data));
    }

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = POS report record
     * @module = admin
     * @path = report/get-pos-report-record
     * @method = POST
     * @description = To get pos report record
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = store_id|<store_id>|integer|required|1|Store ID. (dropdown : store)
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = export_data|<export_data>|array|optional|{"data": {"created_at": "Date"}|Data need to export value => header. Required for export
     * @body = file_name|<file_name>|string|optional|withdrawal-list|name of the export file. Required for export
     *
     * @response = {"list":[{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"02\/08\/2024 13:56:24","initial_balance":"19160.00","amount_used":"100.00","balance":"19060.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"02\/08\/2024 13:10:37","initial_balance":"20780.00","amount_used":"1620.00","balance":"19160.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"02\/08\/2024 13:02:19","initial_balance":"20880.00","amount_used":"100.00","balance":"20780.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"02\/08\/2024 12:50:06","initial_balance":"20900.00","amount_used":"20.00","balance":"20880.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"02\/08\/2024 12:49:15","initial_balance":"21000.00","amount_used":"100.00","balance":"20900.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"02\/08\/2024 12:49:06","initial_balance":"21020.00","amount_used":"20.00","balance":"21000.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"02\/08\/2024 11:49:00","initial_balance":"21120.00","amount_used":"100.00","balance":"21020.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 18:14:27","initial_balance":"21620.00","amount_used":"500.00","balance":"21120.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 18:11:41","initial_balance":"21720.00","amount_used":"100.00","balance":"21620.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 18:06:22","initial_balance":"21820.00","amount_used":"100.00","balance":"21720.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 16:53:28","initial_balance":"21920.00","amount_used":"100.00","balance":"21820.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 16:53:11","initial_balance":"21930.00","amount_used":"10.00","balance":"21920.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 16:53:06","initial_balance":"21940.00","amount_used":"10.00","balance":"21930.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 16:48:41","initial_balance":"21950.00","amount_used":"10.00","balance":"21940.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 16:45:31","initial_balance":"21960.00","amount_used":"10.00","balance":"21950.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 16:38:52","initial_balance":"22460.00","amount_used":"500.00","balance":"21960.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 16:37:50","initial_balance":"22560.00","amount_used":"100.00","balance":"22460.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 15:39:23","initial_balance":"2356.00","amount_used":"100.00","balance":"2256.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 15:37:15","initial_balance":"2366.00","amount_used":"10.00","balance":"2356.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 15:34:37","initial_balance":"3366.00","amount_used":"1000.00","balance":"2366.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 15:08:49","initial_balance":"376.00","amount_used":"10.00","balance":"366.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 15:01:08","initial_balance":"386.00","amount_used":"10.00","balance":"376.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 14:59:56","initial_balance":"396.00","amount_used":"10.00","balance":"386.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 14:59:51","initial_balance":"406.00","amount_used":"10.00","balance":"396.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 14:52:32","initial_balance":"806.00","amount_used":"400.00","balance":"406.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 14:52:20","initial_balance":"906.00","amount_used":"100.00","balance":"806.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 14:51:29","initial_balance":"2006.00","amount_used":"1100.00","balance":"906.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 14:48:32","initial_balance":"26.00","amount_used":"20.00","balance":"6.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 14:48:08","initial_balance":"326.00","amount_used":"300.00","balance":"26.00"},{"terminal_serial":"7501","terminal_address":null,"member_card_id":"000405","member_card_serial_no":"2634796074","phone_no":"60142441456","transaction_type":"\u5237\u5361\u6295\u5e01","transaction_date":"01\/08\/2024 14:45:07","initial_balance":"426.00","amount_used":"100.00","balance":"326.00"}],"pagination":{"current_page":1,"from":0,"last_page":2,"per_page":30,"to":30,"total":30},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZ3LmNvbS9yZXBvcnQvZ2V0LXBvcy1yZXBvcnQtcmVjb3JkIiwiaWF0IjoxNzI1OTUyMDEwLCJleHAiOjE3NTc1MTYyMzcsIm5iZiI6MTcyNTk1NjIzNywianRpIjoidEFDZ042djdGNFBGdGhLZSIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.lx7QgwsT0qLGQ5P5vd3cq0YrSrXR28pIoAsXFT6aA_w","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"dev","execution_duration":"0.************** sec","log_id":"99455923-9cb8-4639-ace5-57894cc21f41","valid_version":false}
     * ##docs end##
     */
    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = POS report reload
     * @module = admin
     * @path = report/get-pos-report-reload
     * @method = POST
     * @description = To get pos report reload
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = store_id|<store_id>|integer|required|1|Store ID. (dropdown : store)
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = export_data|<export_data>|array|optional|{"data": {"created_at": "Date"}|Data need to export value => header. Required for export
     * @body = file_name|<file_name>|string|optional|withdrawal-list|name of the export file. Required for export
     *
     * @response = {"list":[{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000414","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"10\/09\/2024 15:41:16","initial_balance":"100.00","amount_used":"10.00","balance":"110.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000416","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"10\/09\/2024 15:26:37","initial_balance":"70.00","amount_used":"10.00","balance":"80.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000414","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"10\/09\/2024 15:05:03","initial_balance":"70.00","amount_used":"30.00","balance":"100.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000416","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"10\/09\/2024 15:04:35","initial_balance":"30.00","amount_used":"40.00","balance":"70.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000416","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"10\/09\/2024 15:01:47","initial_balance":"0.00","amount_used":"50.00","balance":"50.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000414","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"10\/09\/2024 15:01:35","initial_balance":"40.00","amount_used":"30.00","balance":"70.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000408","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"04\/09\/2024 16:23:08","initial_balance":"150.00","amount_used":"50.00","balance":"200.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000415","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"04\/09\/2024 11:10:04","initial_balance":"10.00","amount_used":"20.00","balance":"30.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000415","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"03\/09\/2024 19:36:42","initial_balance":"40.00","amount_used":"50.00","balance":"90.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000411","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"03\/09\/2024 17:32:37","initial_balance":"40.00","amount_used":"10.00","balance":"50.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000411","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"03\/09\/2024 17:30:09","initial_balance":"40.00","amount_used":"10.00","balance":"50.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000405","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"03\/09\/2024 16:39:12","initial_balance":"95184.00","amount_used":"1.00","balance":"95185.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000414","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"03\/09\/2024 16:07:47","initial_balance":"0.00","amount_used":"50.00","balance":"50.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000410","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"02\/09\/2024 17:14:46","initial_balance":"110.00","amount_used":"10.00","balance":"100.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000411","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"02\/09\/2024 16:05:42","initial_balance":"30.00","amount_used":"10.00","balance":"40.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000413","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"02\/09\/2024 11:58:45","initial_balance":"20.00","amount_used":"20.00","balance":"0.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000413","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"02\/09\/2024 11:58:29","initial_balance":"0.00","amount_used":"20.00","balance":"20.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000408","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"02\/09\/2024 11:48:06","initial_balance":"200.00","amount_used":"50.00","balance":"150.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000411","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"30\/08\/2024 19:16:20","initial_balance":"20.00","amount_used":"10.00","balance":"30.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000410","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"30\/08\/2024 18:52:22","initial_balance":"120.00","amount_used":"10.00","balance":"110.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000410","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"30\/08\/2024 18:50:31","initial_balance":"140.00","amount_used":"20.00","balance":"120.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000410","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"30\/08\/2024 18:50:04","initial_balance":"90.00","amount_used":"50.00","balance":"140.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000410","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"30\/08\/2024 18:33:30","initial_balance":"100.00","amount_used":"10.00","balance":"90.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000410","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"30\/08\/2024 18:33:12","initial_balance":"80.00","amount_used":"20.00","balance":"100.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000411","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"30\/08\/2024 18:21:51","initial_balance":"10.00","amount_used":"10.00","balance":"20.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000411","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"30\/08\/2024 18:17:58","initial_balance":"20.00","amount_used":"10.00","balance":"10.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000411","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"30\/08\/2024 18:17:05","initial_balance":"10.00","amount_used":"10.00","balance":"20.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000412","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"30\/08\/2024 18:16:22","initial_balance":"100.00","amount_used":"10.00","balance":"110.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000410","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"30\/08\/2024 15:57:00","initial_balance":"90.00","amount_used":"10.00","balance":"80.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000410","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u5145\u503c","transaction_date":"30\/08\/2024 15:56:57","initial_balance":"100.00","amount_used":"10.00","balance":"90.00"}],"pagination":{"current_page":1,"from":0,"last_page":2,"per_page":30,"to":30,"total":30},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZ3LmNvbS9yZXBvcnQvZ2V0LXBvcy1yZXBvcnQtcmVsb2FkIiwiaWF0IjoxNzI1OTUyMDEwLCJleHAiOjE3NTc1MTYyMzQsIm5iZiI6MTcyNTk1NjIzNCwianRpIjoiMUZhZ2I4anNnOFRIa1RoYyIsInN1YiI6IjEiLCJwcnYiOiJkZjg4M2RiOTdiZDA1ZWY4ZmY4NTA4MmQ2ODZjNDVlODMyZTU5M2E5In0.rIrYP8nvwlB9JVj7Q-CTvylxZOjII2lOq7rFPjxtR08","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"dev","execution_duration":"0.79903483390808 sec","log_id":"570dc7cd-8db1-439f-96df-6f11734a425a","valid_version":false}
     * ##docs end##
     */
    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = POS report withdraw
     * @module = admin
     * @path = report/get-pos-report-withdraw
     * @method = POST
     * @description = To get pos report withdraw
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = store_id|<store_id>|integer|required|1|Store ID. (dropdown : store)
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = export|<1/0>|integer|optional|1|export the listing or not.
     * @body = list_key|<list_key>|string|optional|list|key for the API listing return. Default is "list"
     * @body = export_data|<export_data>|array|optional|{"data": {"created_at": "Date"}|Data need to export value => header. Required for export
     * @body = file_name|<file_name>|string|optional|withdrawal-list|name of the export file. Required for export
     *
     * @response = {"list":[{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000416","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u9500\u503c","transaction_date":"10\/09\/2024 15:04:25","initial_balance":"50.00","amount_used":"20.00","balance":"30.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000408","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u9500\u503c","transaction_date":"04\/09\/2024 16:23:21","initial_balance":"200.00","amount_used":"40.00","balance":"160.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000415","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u9500\u503c","transaction_date":"04\/09\/2024 14:43:38","initial_balance":"30.00","amount_used":"30.00","balance":"0.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000415","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u9500\u503c","transaction_date":"04\/09\/2024 11:09:40","initial_balance":"60.00","amount_used":"50.00","balance":"10.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000411","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u9500\u503c","transaction_date":"03\/09\/2024 17:32:47","initial_balance":"50.00","amount_used":"10.00","balance":"40.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000411","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u9500\u503c","transaction_date":"03\/09\/2024 17:30:24","initial_balance":"50.00","amount_used":"10.00","balance":"40.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000405","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u9500\u503c","transaction_date":"03\/09\/2024 16:53:40","initial_balance":"95184.00","amount_used":"1.00","balance":"95183.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000405","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u9500\u503c","transaction_date":"03\/09\/2024 16:53:34","initial_balance":"95185.00","amount_used":"1.00","balance":"95184.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000414","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u9500\u503c","transaction_date":"03\/09\/2024 16:07:58","initial_balance":"50.00","amount_used":"10.00","balance":"40.00"},{"terminal_serial":null,"terminal_address":"Online Payment","member_card_id":"000405","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u9500\u503c","transaction_date":"03\/09\/2024 11:59:51","initial_balance":"95185.00","amount_used":"1.00","balance":"95184.00"},{"terminal_serial":null,"terminal_address":"\u6536\u94f6\u53f01","member_card_id":"000405","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u9500\u503c","transaction_date":"02\/09\/2024 10:37:51","initial_balance":"95197.00","amount_used":"12.00","balance":"95185.00"},{"terminal_serial":null,"terminal_address":"\u6536\u94f6\u53f01","member_card_id":"009","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u9500\u503c","transaction_date":"14\/08\/2024 16:29:17","initial_balance":"412.00","amount_used":"12.00","balance":"400.00"},{"terminal_serial":null,"terminal_address":"\u6536\u94f6\u53f01","member_card_id":"009","member_card_serial_no":null,"phone_no":null,"transaction_type":"\u9500\u503c","transaction_date":"14\/08\/2024 16:26:35","initial_balance":"500.00","amount_used":"88.00","balance":"412.00"}],"pagination":{"current_page":1,"from":0,"last_page":1,"per_page":30,"to":13,"total":13},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwtYXBpLWFkbWluLmZ3LmNvbS9yZXBvcnQvZ2V0LXBvcy1yZXBvcnQtd2l0aGRyYXciLCJpYXQiOjE3MjU5NTIwMTAsImV4cCI6MTc1NzUxNjI5NCwibmJmIjoxNzI1OTU2Mjk0LCJqdGkiOiJYZlowSXdmMWo4ZW5zMWRKIiwic3ViIjoiMSIsInBydiI6ImRmODgzZGI5N2JkMDVlZjhmZjg1MDgyZDY4NmM0NWU4MzJlNTkzYTkifQ.lmMIyN4lkWSelGkQotyqoO8CX1P_2sinYj8lNAcdAoM","token_type":"bearer","timezone":"Asia\/Kuala_Lumpur","environment":"dev","execution_duration":"0.************* sec","log_id":"3315ba1c-5e5b-4955-91d4-bfd828ebfc64","valid_version":false}
     * ##docs end##
     */
    public function getPosReport(Request $request)
    {
        switch ($request->path()) {
            case 'report/get-pos-report-record':
                $request->merge(['type' => 'pos_record']);
                // $request->merge(['operationType'=>1]);
                break;
            case 'report/get-pos-report-reload':
                $request->merge(['type' => 'pos_reload']);
                $request->merge(['operationType' => 1]);
                break;
            case 'report/get-pos-report-withdraw':
                $request->merge(['type' => 'pos_withdraw']);
                $request->merge(['operationType' => 2]);
                break;
        }
        if (in_array(MODULE, ['admin'])) {
            $adminId = auth()->user()->id;
            $adminRes = Models\Admin::with(
                [
                    'adminDetail' =>
                    function ($q) {
                        return $q->where('name', 'store');
                    }
                ],
            )
                ->find($adminId);

            $isMaster = $adminRes->is_master;
            if (!$isMaster) {
                $branchJE = $adminRes->adminDetail->first()->value ?? "[]";
                $branchDE = json_decode($branchJE);

                $cardStoreId = Models\Store::whereIn('id', $branchDE)->get()->pluck('store_id');
                $extraValidation = [
                    'store_id' => [
                        'required',
                        Rule::exists('store', 'id')->whereIn('store_id', $cardStoreId),
                    ],
                ];
            } else {
                $extraValidation = [
                    'store_id' => [
                        'required',
                        Rule::exists('store', 'id'),
                    ],
                ];
            }
        }

        $validator = Validator::make($request->all(), array_merge([
            "operationType" => "nullable|in:1,2",
            "type" => "required",
            "page" => "integer",
            "see_all" => "integer",
            "limit" => "integer",
            "export" => "in:1,0|nullable",
            "export_data" => "required_if:export,==,1",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
            "from_date" => "nullable|date|date_format:Y-m-d H:i:s",
            "to_date" => "nullable|date|date_format:Y-m-d H:i:s|after_or_equal:from_date",
            "isIncludeOnline" => 'boolean'
        ], $extraValidation));

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data = UserCard::getPosReport($validator->validated());
        abort(200, json_encode($data));
    }

    public function getPosStatus(Request $request)
    {
        $data = UserCard::getPosStatus();
        abort(200, json_encode($data));
    }

    public function getCurrencyDetail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "store_id" => "integer",
            "page" => "integer",
            "limit" => "integer",
            "from_date" => "nullable|date|date_format:Y-m-d H:i:s",
            "to_date" => "nullable|date|date_format:Y-m-d H:i:s|after_or_equal:from_date",
            "see_all" => "integer|in:1,0",
            "export" => "in:1,0|nullable",
            "export_data" => "required_if:export,==,1",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data = UserCard::getCurrencyDetail($validator->validated());
        abort(200, json_encode($data));
    }

    public function getSummaryProfitLossReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "store_id" => "integer",
            "page" => "integer",
            "limit" => "integer",
            "from_date" => "nullable|date|date_format:Y-m-d",
            "to_date" => "nullable|date|date_format:Y-m-d|after_or_equal:from_date",
            "product_id" => "nullable|integer",
            "see_all" => "integer|in:1,0",
            "export" => "in:1,0|nullable",
            "export_data" => "required_if:export,==,1",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data = ExTransfer::getSummaryProfitLossReport($validator->validated());
        abort(200, json_encode($data));
    }

    public function getSummaryProfitLossMonthlyReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "store_id" => "integer",
            "page" => "integer",
            "limit" => "integer",
            "from_date" => "nullable|date|date_format:Y-m-d",
            "to_date" => "nullable|date|date_format:Y-m-d|after_or_equal:from_date",
            "product_id" => "nullable|integer",
            "see_all" => "integer|in:1,0",
            "export" => "in:1,0|nullable",
            "export_data" => "required_if:export,==,1",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data = ExTransfer::getSummaryProfitLossMonthlyReport($validator->validated());
        abort(200, json_encode($data));
    }

    public function getStoreSummaryProfitLossReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "store_ids" => "nullable|array",
            "store_id" => "nullable",
            "page" => "integer",
            "limit" => "integer",
            "from_date" => "nullable|date|date_format:Y-m-d",
            "to_date" => "nullable|date|date_format:Y-m-d|after_or_equal:from_date",
            "product_id" => "nullable|integer",
            "see_all" => "integer|in:1,0",
            "export" => "in:1,0|nullable",
            "export_data" => "required_if:export,==,1",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
            "is_exclude_dummy" => "boolean"
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data = ExTransfer::getStoreSummaryProfitLossReport($validator->validated());
        abort(200, json_encode($data));
    }

    public function getSummaryStoreReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "store_ids" => "nullable|array",
            "store_id" => "nullable",
            "page" => "integer",
            "limit" => "integer",
            "from_date" => "nullable|date|date_format:Y-m-d",
            "to_date" => "nullable|date|date_format:Y-m-d|after_or_equal:from_date",
            "product_id" => "nullable|integer",
            "see_all" => "integer|in:1,0",
            "export" => "in:1,0|nullable",
            "export_data" => "required_if:export,==,1",
            "list_key" => "string",
            "file_name" => "required_if:export,==,1",
            "is_exclude_dummy" => "boolean"
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $data = UserSales::getStoreSummaryReport($validator->validated());
        abort(200, json_encode($data));
    }
}
