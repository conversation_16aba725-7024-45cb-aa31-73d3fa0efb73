<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Validator;

class ActivityLogController extends Controller
{
    protected $guardName = 'admin';

    /** This is needed to generate API docs
     * ##docs start##
     * @postmanName = activity-log/list
     * @module = admin
     * @path = activity-log/list
     * @permissionName = Activity Logs
     * @menuType = menu
     * @method = POST
     * @description = To get admin activity log list.
     * @header = Content-Type|application/json|string|required|application/json|Send request in JSON format.
     * @header = Authorization|Bearer {{Token}}|string|required|Bearer {{Token}}|To authenticate user.
     * @body = from|<from>|string|optional|Y-m-d H:i:s|Filter listing by creation time.
     * @body = to|<to>|string|optional|Y-m-d H:i:s|Filter listing by creation time.
     * @body = username|<username>|string|optional|Admin|Filter listing by username.
     * @body = module|<module>|string|optional|Admin|Filter listing by module.
     * @body = created_by|<created_by>|string|optional|admin|Filter listing by created by.
     * @body = page|<page>|integer|optional|1|Current page to be display.
     * @body = limit|<limit>|integer|optional|15|Numbers of rows to be display. Default 15.
     * @body = order_sort|<order_sort>|string|optional|asc|Order listing by desc or asc.
     * @body = order_by|<order_by>|string|optional|email|Order listing by column. (created_at)
     * @body = see_all|<1/0>|integer|optional|1|See All Listing.
     * @response = {"data":{"list":[{"created_at":"2022-11-15 18:12:18","username":"ctesto1001","module":"Update Insurance Claim","action":"Insurance claim for member ctesto1001 with date 2022-11-09, cycle 1 and amount 635.00 has been updated from Submitted to Approved","created_by":"admin@maskman"},{"created_at":"2022-11-15 18:06:23","username":"ctesto1001","module":"Appealed Insurance Claim","action":"Member ctesto1001 has appealed insurance claim with date 2022-11-09, cycle 1 and amount 635.00","created_by":"user@ctesto1001"},{"created_at":"2022-11-15 18:01:04","username":"ctesto1001","module":"Update Insurance Claim","action":"Insurance claim for member ctesto1001 with date 2022-11-09, cycle 1 and amount 635.00 has been updated from Submitted to Rejected","created_by":"admin@maskman"},{"created_at":"2022-11-15 17:56:24","username":"ctesto1001","module":"Submit Insurance Claim","action":"Member ctesto1001 has appealed insurance claim with date 2022-11-09, cycle 1 and amount 635.00","created_by":"user@ctesto1001"},{"created_at":"2022-11-15 17:25:55","username":"ctesto1001","module":"Change Password","action":"Changed password for ctesto1001.","created_by":"user@ctesto1001"},{"created_at":"2022-11-15 17:20:23","username":"ctesto1001","module":"Change Password","action":"Changed password for ctesto1001.","created_by":"user@ctesto1001"},{"created_at":"2022-11-15 15:22:21","username":"ctesto1001","module":"Update Member","action":"Member ctesto1001 is updated with name from ctesto100 to ctesto1001, phone number from 60-123456789 to 60-123456780, status from Inactive to Active","created_by":"admin@maskman"},{"created_at":"2022-11-15 15:07:06","username":"ctesto1001","module":"Admin Set User Protection","action":"Member ctesto1001 is set from Protected to Unprotected.","created_by":"admin@maskman"},{"created_at":"2022-11-15 14:56:08","username":"ctesto1001","module":"Admin Set User Protection","action":"Member ctesto1001 is set from Unprotected to Protected.","created_by":"admin@maskman"},{"created_at":"2022-11-15 14:26:53","username":"ctesto1001","module":"Admin Set User Rank","action":"Member ctesto1001 is set to rank Agent.","created_by":"System"},{"created_at":"2022-11-15 14:26:45","username":"ctesto1001","module":"Admin Set User Rank","action":"Member ctesto1001 is set to rank Partner.","created_by":"admin@maskman"},{"created_at":"2022-11-15 11:11:16","username":"ctesto1001","module":"Admin Set User Rank","action":"Member ctesto1001 is set to rank Agent.","created_by":"admin@maskman"},{"created_at":"2022-11-15 11:11:16","username":"ctesto1001","module":"Admin Set User Rank","action":"Member ctesto1001 is set to rank Team Manager.","created_by":"admin@maskman"},{"created_at":"2022-11-15 09:50:37","username":null,"module":"Update Admin","action":"Admin testadmin2 is updated with status from Active to Inactive","created_by":"admin@maskman"},{"created_at":"2022-11-15 09:50:26","username":null,"module":"Update Admin","action":"Admin testadmin2 is updated with name from testadmin3 to testadmin2, username from testadmin3 to testadmin2, <NAME_EMAIL> to <EMAIL>,","created_by":"admin@maskman"},{"created_at":"2022-11-15 09:39:37","username":null,"module":"Create Admin","action":"Admin with username testadmin2 is created.","created_by":"admin@maskman"},{"created_at":"2022-11-15 09:32:24","username":"ctesto1001","module":"Crypto Deposit","action":"Crypto deposit with payment number T000217 has been updated from Pending to Approved.","created_by":"System"},{"created_at":"2022-11-15 09:32:24","username":"ctesto1001","module":"Deposit","action":"Deposit with payment number T000217 has been updated from Pending to Approved.","created_by":"System"},{"created_at":"2022-11-15 09:32:24","username":"ctesto1001","module":"Deposit","action":"Requested deposit 6.00 into MI Wallet by Crypto.","created_by":"System"},{"created_at":"2022-11-15 09:32:24","username":"ctesto1001","module":"Insurance","action":"Insurance with reference number MI495406 has been updated from Pending to Active.","created_by":"System"},{"created_at":"2022-11-15 09:32:24","username":"ctesto1001","module":"Insurance","action":"Insurance with reference number MI680070 has been updated from Active to Renewed.","created_by":"System"},{"created_at":"2022-11-15 09:32:18","username":"ctesto1001","module":"Purchase Insurance","action":"Member ctesto1001 purchased 15 Days insurance using Crypto.","created_by":"admin@maskman"},{"created_at":"2022-11-15 09:30:07","username":"ctesto1001","module":"Deposit","action":"Deposit with payment number T000216 has been updated from Pending to Approved.","created_by":"System"},{"created_at":"2022-11-15 09:30:07","username":"ctesto1001","module":"Deposit","action":"Requested deposit 128.00 into MI Wallet by Crypto.","created_by":"System"},{"created_at":"2022-11-15 09:30:02","username":"ctesto1001","module":"Purchase Insurance","action":"Member ctesto1001 purchased 15 Days insurance using Bank.","created_by":"admin@maskman"},{"created_at":"2022-11-15 09:11:19","username":"ctesto1001","module":"Deposit","action":"Deposit with payment number T000215 has been updated from Pending to Approved.","created_by":"System"},{"created_at":"2022-11-15 09:11:19","username":"ctesto1001","module":"Deposit","action":"Requested deposit 128.00 into MI Wallet by Crypto.","created_by":"System"},{"created_at":"2022-11-15 08:53:25","username":"ctesto1001","module":"Change Transaction Password","action":"Changed transaction password for ctesto1001.","created_by":"admin@maskman"},{"created_at":"2022-11-15 08:51:47","username":"ctesto1001","module":"Change Password","action":"Changed password for ctesto1001.","created_by":"admin@maskman"},{"created_at":"2022-11-14 16:41:25","username":"ctesto1001","module":"Manual Deposit","action":"Manual deposit with payment number B000354 has been updated from Pending to Cancel.","created_by":"user@ctesto1001"}],"pagination":{"current_page":1,"from":1,"last_page":2,"per_page":30,"to":30,"total":50},"meta":null},"status":true,"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWwuYWRtaW4ubWFydGluLmNvbS9hY3Rpdml0eS1sb2cvbGlzdCIsImlhdCI6MTY2ODY1MjU4MCwiZXhwIjoxNjY4NjU3MjA3LCJuYmYiOjE2Njg2NTM2MDcsImp0aSI6IndNOG1yNW1rdUxNUXl3ak0iLCJzdWIiOiIxIiwicHJ2IjoiZGY4ODNkYjk3YmQwNWVmOGZmODUwODJkNjg2YzQ1ZTgzMmU1OTNhOSJ9.VGS9N8qR3NH971Aeg1SdMR80Cytw6ZYU2vUf-TRNeBo","token_type":"bearer","timezone":"Asia\/Ho_Chi_Minh","environment":"local","execution_duration":"0.22990989685059 sec","log_id":"eefa086f-e7a0-4d95-be00-7c6f49352aa6"}
     * ##docs end##
     */
    public function get(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'from' => 'required_with:to|date_format:Y-m-d H:i:s',
            'to' => 'required_with:from|date_format:Y-m-d H:i:s|after_or_equal:from',
            'username' => 'string',
            'module' => 'string',
            'created_by' => 'string',
            'order_sort' => [
                'filled',
                Rule::in(['asc', 'desc'])
            ],
            'order_by' => ['filled'],
            'page' => [
                'filled',
                'numeric',
            ],
            'limit' => [
                'filled',
                'numeric',
            ],
            'see_all' => 'integer|in:1,0',
        ]);

        $activityLog = ActivityLog::get($validator->validated());

        $data = [];
        $data['data'] = $activityLog;

        abort(200,json_encode($data));
    }
}
