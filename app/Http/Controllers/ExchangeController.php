<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

use App\Models\ExchangeRate;
use App\Models\ExchangeSetting;

use Validator;
use DB;
use App\Traits\FingerPrintTrait;

class ExchangeController extends Controller {

    protected $guardName = '_exchange_rate';

    public static function getLatestRate(Request $request){
        FingerPrintTrait::fingerPrint($request);
        $res = ExchangeSetting::getLatestRate($request->all());
        abort(200,json_encode($res));
    }
}
