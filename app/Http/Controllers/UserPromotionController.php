<?php

namespace App\Http\Controllers;

use App\Models\UserPromotion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class UserPromotionController extends Controller
{
    public function getUserPromotionList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'string',
            'phone_no' => 'string',
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d',
            'limit' => 'integer',
            'page' => 'integer',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = UserPromotion::getList($validator->validated());

        abort(200, json_encode($res));
    }

    public function updateUserPromotion()
    {
        $validator = Validator::make(request()->all(), [
            'id' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = UserPromotion::updateStatus($validator->validated());

        abort(200, json_encode($res));
    }

    public function addUserPromotion(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string',
            'promotion_id' => 'nullable|numeric',
            'user_id' => 'nullable|numeric',
            'target_turnover' => 'nullable|numeric',
            'max_withdrawal_amount' => 'nullable|numeric',
            'bonus_amount' => 'nullable|numeric',
            'type' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = UserPromotion::add($validator->validated());

        abort(200, json_encode($res));
    }

    public function getUserPromotionSummary(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'from_date' => 'required_with:to_date|string|date_format:Y-m-d',
            'to_date' => 'required_with:from_date|string|date_format:Y-m-d',
            'store_id' => 'numeric',
            'status' => 'numeric',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $summary = UserPromotion::getUserPromotionSummary($validator->validated());
        $data = ['data' => $summary];

        abort(200, json_encode($data));
    }
}
