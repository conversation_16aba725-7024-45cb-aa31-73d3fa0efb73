<?php

namespace App\Http\Controllers;

use App\Http\Resources\ItemsCollection;
use App\Models\LangReward;
use App\Models\Promotion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class PromotionController extends Controller
{
    public function getPromotionList(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $user_id = auth()->user()->id ?? null;
            $request->request->add(['user_id' => $user_id]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'integer',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Promotion::getUserPromotionList($validator->validated());

        abort(200, json_encode([
            'data' => $res,
        ]));
    }

    public function getPromotionDetail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer',
        ]);

        $res = Promotion::getPromotionDetail($request->id);

        abort(200, json_encode([
            'data' => $res,
        ]));
    }

    public function getPromotions(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'integer',
            'limit' => 'integer',
            'order_by' => 'string',
            'order_sort' => 'string',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $params = $validator->validated();

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        $limit = $params['limit'] ?? config('app.pagination_rows');

        $items = Promotion::query()
            ->orderBy($order_by, $order_sort)
            ->paginate($limit);

        $items->getCollection()->transform(function ($e) {
            $desc = LangReward::where('slug', $e->description)->first();
            $terms = LangReward::where('slug', $e->terms)->first();

            return array_merge($e->toArray(), [
                'description' => [
                    'en' => optional($desc)->en,
                    'cn' => optional($desc)->cn,
                    'my' => optional($desc)->my,
                ],
                'terms' => [
                    'en' => optional($terms)->en,
                    'cn' => optional($terms)->cn,
                    'my' => optional($terms)->my,
                ],
            ]);

            return (object) $res;
        });

        abort(200, json_encode([
            'data' => (new ItemsCollection($items))->toArray(),
        ]));
    }

    public function addPromotion(Request $request)
    {
        $request->request->add(['product_id' => '2004']);
        $request->request->add(['service_id' => '1342']);
        $request->request->add(['is_deposit' => true]);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'image' => 'required|string',
            'promotion_data.*.description' => 'required|string',
            'promotion_data.*.terms' => 'required|string',
            'promotion_data.*.language_type' => "required|string|distinct|in:" . implode(',', array_keys(config('language'))),
            'product_id' => 'required|integer',
            'service_id' => 'required|integer',
            'user_list' => 'nullable|string',
            'is_deposit' => 'required|boolean',
            'max_withdraw_amount' => 'nullable|numeric',
            'max_withdrawal_multiplier' => 'nullable|integer',
            'is_bonus_flexible' => 'required|boolean',
            'bonus_multiplier' => 'nullable|numeric',
            'bonus_amount' => 'nullable|numeric',
            'is_bonus_multiplier' => 'required|boolean',
            'max_bonus_amount' => 'nullable|numeric',
            'turnover_multipler' => 'nullable|integer',
            'turnover_amount' => 'nullable|numeric',
            'is_turnover_multiplier' => 'required|boolean',
            'is_withdrawal_multiplier' => 'required|boolean',
            'status' => 'required',
        ], [
            'name.required' => Lang::get('lang.input-field-required-error'),
            'image.required' => Lang::get('lang.input-field-required-error'),
            'promotion_data.*.description.required' => Lang::get('lang.input-field-required-error'),
            'promotion_data.*.terms.required' => Lang::get('lang.input-field-required-error'),
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        DB::transaction(function () use ($request) {
            $slug = Str::slug($request->name);
            $descSlug = Str::slug($request->name . ' ' . 'description');
            $termsSlug = Str::slug($request->name . ' ' . 'terms');

            $promotionCount = Promotion::where('slug', $slug)->count();
            $descCount = LangReward::where('slug', $descSlug)->count();
            $termsCount = LangReward::where('slug', $termsSlug)->count();

            if ($promotionCount > 0 || $descCount > 0 || $termsCount > 0) {
                abort(400, 'Promotion name already in use');
            }

            foreach ($request->promotion_data as $item) {
                LangReward::updateOrCreate(
                    ['slug' => $descSlug],
                    [$item['language_type'] => $item['description']]
                );

                LangReward::updateOrCreate(
                    ['slug' => $termsSlug],
                    [$item['language_type'] => $item['terms']]
                );
            }

            if ($request->image) {
                $request->merge(['image' => env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $request->image]);
            }

            Promotion::create(array_merge($request->all(), [
                'slug' => $slug,
                'description' => $descSlug,
                'terms' => $termsSlug,
                'is_deposit' => 1,
                'turnover_multipler' => $request->turnover_multipler ?? 0,
            ]));
        });

        abort(200, 'Promotion created successfully');
    }

    public function updatePromotion(Request $request)
    {
        $request->request->add(['product_id' => '2004']);
        $request->request->add(['service_id' => '1342']);
        $request->request->add(['is_deposit' => true]);

        $validator = Validator::make($request->all(), [
            'id' => 'required|integer|exists:promotions,id',
            'name' => 'required|string',
            'image' => 'required|string',
            'promotion_data.*.description' => 'required|string',
            'promotion_data.*.terms' => 'required|string',
            'promotion_data.*.language_type' => "required|string|distinct|in:" . implode(',', array_keys(config('language'))),
            'product_id' => 'required|integer',
            'service_id' => 'required|integer',
            'user_list' => 'nullable|string',
            'is_deposit' => 'required|boolean',
            'max_withdraw_amount' => 'nullable|numeric',
            'max_withdrawal_multiplier' => 'nullable|integer',
            'is_bonus_flexible' => 'required|boolean',
            'bonus_multiplier' => 'nullable|numeric',
            'bonus_amount' => 'nullable|numeric',
            'is_bonus_multiplier' => 'required|boolean',
            'max_bonus_amount' => 'nullable|numeric',
            'turnover_multipler' => 'nullable|integer',
            'turnover_amount' => 'nullable|numeric',
            'is_turnover_multiplier' => 'required|boolean',
            'is_withdrawal_multiplier' => 'required|boolean',
            'status' => 'required',
        ], [
            [
                'name.required' => Lang::get('lang.input-field-required-error'),
                'image.required' => Lang::get('lang.input-field-required-error'),
                'promotion_data.*.description.required' => Lang::get('lang.input-field-required-error'),
                'promotion_data.*.terms.required' => Lang::get('lang.input-field-required-error'),
            ]
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        DB::transaction(function () use ($request) {
            $promotion = Promotion::find($request->id);
            $slug = Str::slug($request->name);
            $descSlug = Str::slug($request->name . ' description');
            $termsSlug = Str::slug($request->name . ' terms');

            $duplicatePromotion = Promotion::where('slug', $slug)
                ->where('id', '!=', $promotion->id)
                ->first();

            if ($duplicatePromotion) {
                abort(400, 'Promotion name already in use. Please pick another name.');
            }

            foreach ($request->promotion_data as $item) {
                LangReward::updateOrCreate(
                    ['slug' => $descSlug],
                    [$item['language_type'] => $item['description']]
                );
    
                LangReward::updateOrCreate(
                    ['slug' => $termsSlug],
                    [$item['language_type'] => $item['terms']]
                );
            }

            if ($request->image && !Str::startsWith($request->image, 'http')) {
                $request->merge(['image' => env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $request->image]);
            }
    
            $promotion->update(array_merge(
                $request->except('id', 'promotion_data'),
                [
                    'slug' => $slug,
                    'description' => $descSlug,
                    'terms' => $termsSlug,
                    'turnover_multipler' => $request->turnover_multipler ?? 0,
                ]
            ));
        });

        abort(200, 'Promotion updated successfully');
    }

    public function claimBuayaPromotion(Request $request)
    {
        if (in_array(MODULE, ['user', 'app'])) {
            $user_id = auth()->user()->id ?? null;
            $request->request->add(['user_id' => $user_id]);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'integer',
        ]);

        if ($validator->fails()) {
            abort(400, json_encode($validator->errors()));
        }

        $res = Promotion::claimBuayaPromotion($request->all());

        abort(200, json_encode([
            'data' => $res,
        ]));
    }

    public function editPromotion(Request $request) {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer',
        ]);

        $promotion = Promotion::where('id', $request->id)->first() ?? null;

        if (!$promotion) abort(200, json_encode(['data' => null]));

        $descriptionData = LangReward::where('slug', $promotion->description)->first();
        $termsData = LangReward::where('slug', $promotion->terms)->first();

        $promotionData = collect(array_keys(config('language')))
            ->map(function ($lang) use ($descriptionData, $termsData) {
                $description = $descriptionData[$lang] ?? null;
                $terms = $termsData[$lang] ?? null;

                if (empty($description) && empty($terms)) {
                    return null;
                }

                return [
                    'language_type' => $lang,
                    'description' => $description ?? '',
                    'terms' => $terms ?? '',
                ];
            })
            ->filter()
            ->values();

        $res = [
                'id' => $promotion->id,
                'image' => $promotion->image,
                'product_id' => $promotion->product_id,
                'service_id' => $promotion->service_id,
                'max_withdraw_amount' => $promotion->max_withdraw_amount,
                'max_withdrawal_multiplier' => $promotion->max_withdrawal_multiplier,
                'is_bonus_flexible' => $promotion->is_bonus_flexible,
                'bonus_amount' => $promotion->bonus_amount,
                'turnover_amount' => $promotion->turnover_amount,
                'is_withdrawal_multiplier' => $promotion->is_withdrawal_multiplier,
                'name' => $promotion->name,
                'is_bonus_multiplier' => $promotion->is_bonus_multiplier,
                'max_bonus_amount' => $promotion->max_bonus_amount,
                'bonus_multiplier' => $promotion->bonus_multiplier,
                'is_turnover_multiplier' => $promotion->is_turnover_multiplier,
                'turnover_multipler' => $promotion->turnover_multipler,
                'status' => $promotion->status,
                'promotion_data' => $promotionData,
            ];

        abort(200, json_encode([
            'data' => $res,
        ]));
    }
}
