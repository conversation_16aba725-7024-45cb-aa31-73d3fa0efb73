<?php

namespace App\Http\Webhooks;

use DefStudio\Telegraph\Models\TelegraphBot;
use DefStudio\Telegraph\Models\TelegraphChat;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class TelegramHandler extends \DefStudio\Telegraph\Handlers\WebhookHandler
{
    public function start(): void
    {
        $chat = TelegraphChat::where('chat_id', $this->chat->info()['id'])->first();
        if (is_null($chat)) {

            $chat = TelegraphChat::create([
                'chat_id' => $this->chat->info()['id'],
                'name' => $this->chat->info()['first_name'],
                'telegrah_bot_id' => TelegraphBot::first()->id,
            ]);
        }
    }

    // protected function onFailure(Throwable $throwable): void
    // {
    //     if ($throwable instanceof NotFoundHttpException) {
    //         throw $throwable;
    //     }

    //     report($throwable);

    //     $this->reply('Oops, no commands found!');
    // }
}
