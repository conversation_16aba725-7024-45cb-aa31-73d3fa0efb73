<?php

namespace App\Http\Middleware;

use App\Http\Resources;
use App\Models;
use App\Traits;
use App\Models\Credit;
use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Arr;

class DropdownList
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);
        $content = json_decode($response->content(), true);
        $lang = config('app.locale');
        $userDetail = null;

        if (!in_array($request->path(), ['country/get']) && in_array(MODULE, ['user', 'app'])) $userDetail = User::with('country')->find(auth()->user()->id);
        if (MODULE == 'admin' && isset($request->user_id)) $userDetail = User::with('country')->find($request->user_id);

        if (isset($userDetail) && !in_array($userDetail->country->name, config('users')['default_country_currency'])) {
            $currency = Models\Currency::whereIn('iso', ['USD', $userDetail->country->currency_code, 'USDT'])->select('id')->pluck('id', 'id')->toArray();
        } else {
            $currency = Models\Currency::whereIn('iso', ['USD', config('users')['default_currency'], 'USDT'])->select('id')->pluck('id', 'id')->toArray();
        }

        if ($request->has('dropdown') && $response->status() == 200) {
            $dropdowns = $request->input('dropdown');

            if (! is_array($dropdowns)) {
                $dropdowns = [$dropdowns];
            }

            $content['dropdown'] = [];
            foreach ($dropdowns as $dropdown) {
                switch ($dropdown) {
                    case 'country':
                        $countryList = Models\Country::getList([], false, true);
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'country' => $countryList['data'],
                        ]);
                        break;

                    case 'all_country':
                        $countryList = Models\Country::getList([], false, false);
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'all_country' => $countryList['data'],
                        ]);
                        break;

                    case 'admin_status':
                        $statusRes = Models\Admin::$activatedDisplay;
                        $statusAry = [];
                        foreach ($statusRes as $statusKey => $statusRow) {
                            $temp = [];
                            $temp['id'] = $statusRow;
                            $temp['name'] = $statusKey;
                            $temp['display'] = Lang::has('lang.' . $statusKey) ? Lang::get('lang.' . $statusKey) : $statusKey;
                            $statusAry[] = $temp;
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'admin_status' => $statusAry,
                        ]);
                        break;

                    case 'country_state':
                        $countryList = Models\Country::getList([], true);
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'country_state' => $countryList['data'],
                        ]);
                        break;

                    case 'admin_role_status':
                        $statusRes = Models\AdminRoles::$status;
                        $statusAry = [];
                        foreach ($statusRes as $statusKey => $statusRow) {
                            $temp = [];
                            $temp['name'] = $statusKey;
                            $temp['display'] = Lang::has('lang.' . $statusKey) ? Lang::get('lang.' . $statusKey) : $statusKey;
                            $statusAry[] = $temp;
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'admin_role_status' => $statusAry,
                        ]);
                        break;

                    case 'admin_roles':
                        $roles = Models\AdminRoles::where('status', Models\AdminRoles::$status['active'])->get()->pluck('name', 'id');
                        $roleList = [];
                        foreach ($roles as $roleID => $role) {
                            $temp = [];
                            $temp['id'] = $roleID;
                            $temp['name'] = $role;
                            $roleList[] = $temp;
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'admin_roles' => $roleList,
                        ]);
                        break;

                    case 'activity_log_module':
                        $module = Models\ActivityLog::groupBy('action')
                            ->get(['action'])
                            ->map(function ($q) {
                                $res = [
                                    'name' => $q->action,
                                    'display' => Lang::has('lang.act-log-title-' . $q->action) ? Lang::get('lang.act-log-title-' . $q->action) : $q->action,
                                ];
                                return $res;
                            });
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'activity_log_module' => array_values($module->toArray()),
                        ]);
                        break;

                    case 'credit_list':
                        $module = Models\Credit::get()
                            ->map(function ($q) {
                                $res = [
                                    'id' => $q->id,
                                    'name' => $q->name,
                                    'display' => Lang::has('lang.' . $q->name) ? Lang::get('lang.' . $q->name) : $q->name,
                                ];
                                return $res;
                            });
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'credit_list' => array_values($module->toArray()),
                        ]);
                        break;

                    case 'transaction_subject':
                        if (isset($request->credit_id)) {
                            $credit = Credit::where(['id' => $request->credit_id])->first();
                        }

                        foreach (config('subject') as $key => $value) {
                            $data['id'] = $value;
                            $data['name'] = $key;
                            $data['display'] = Lang::has('lang.' . $key) ? Lang::get('lang.' . $key) : $key;
                            $module[] = $data;
                        }

                        $content['dropdown'] = array_merge($content['dropdown'], ['transaction_subject' => array_values($module)]);
                        break;

                    case 'exp_transaction_subject':
                        $expTrxnSubject = Arr::only(config('subject'), ['adjustment-in', 'adjustment-out', 'gain-exp', 'loss-exp']);

                        foreach ($expTrxnSubject as $key => $value) {
                            $data['id'] = $value;
                            $data['name'] = $key;
                            $data['display'] = Lang::has('lang.' . $key) ? Lang::get('lang.' . $key) : $key;
                            $module[] = $data;
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['exp_transaction_subject' => array_values($module)]);
                        break;

                    case 'transfer_transaction_subject':
                        foreach (Arr::only(config('subject'), ['transfer-in', 'transfer-out']) as $key => $value) {
                            $data['id'] = $value;
                            $data['name'] = $key;
                            $data['display'] = Lang::has('lang.' . $key) ? Lang::get('lang.' . $key) : $key;
                            switch ($key) {
                                case 'transfer-out':
                                    $data['transaction_type'] = Lang::has('lang.transfer-to') ? Lang::get('lang.transfer-to') : "Transfer to";
                                    break;

                                case 'transfer-in':
                                    $data['transaction_type'] = Lang::has('lang.received-from') ? Lang::get('lang.received-from') : "Receive from";
                                    break;
                            }
                            $module[] = $data;
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['transaction_subject' => array_values($module)]);
                        break;

                    case 'user_type':
                        $userType = Models\User::$userType;
                        $userTypeAry = [];
                        foreach ($userType as $userKey => $userRow) {
                            if ($userKey == 'internal-account') {
                                continue;
                            }
                            $temp = [];
                            $temp['id'] = $userRow;
                            $temp['name'] = $userKey;
                            $temp['display'] = Lang::has('lang.' . $userKey) ? Lang::get('lang.' . $userKey) : $userKey;
                            $userTypeAry[] = $temp;
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'user_type' => $userTypeAry,
                        ]);
                        break;

                    case 'member_status':
                        // $status = Arr::except(Models\User::$memberStatus,['suspended']);
                        $status = Models\User::$memberStatus;
                        $statusAry = [];
                        foreach ($status as $statusKey => $statusRow) {
                            $statusAry[] = [
                                "id" => $statusKey,
                                "name" => $statusKey,
                                "display" => Lang::has('lang.' . $statusKey) ? Lang::get('lang.' . $statusKey) : $statusKey,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['member_status' => $statusAry]);
                        break;

                    case 'transaction_type':
                        $type = Models\TransactionLogs::$type;
                        $typeAry = [];
                        foreach ($type as $typeKey => $typeRow) {
                            $typeAry[] = [
                                "id" => $typeRow,
                                "name" => $typeKey,
                                "display" => Lang::has('lang.' . $typeKey) ? Lang::get('lang.' . $typeKey) : $typeKey,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['transaction_type' => $typeAry]);
                        break;

                    case 'sms_otp_type':
                        $type = Arr::except(Traits\Sms::$smsType, ['forgot-username']);
                        $typeAry = [];
                        foreach ($type as $typeKey => $typeRow) {
                            $typeAry[] = [
                                "id" => $typeRow,
                                "name" => $typeKey,
                                "display" => Lang::has('lang.' . $typeKey) ? Lang::get('lang.' . $typeKey) : $typeKey,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['sms_otp_type' => $typeAry]);
                        break;

                    case 'bank':
                        $bankRes = Models\Bank::where('status', Models\Bank::$status['active'])
                            ->when(in_array(MODULE, ['user', 'app']), function ($q) use ($userDetail) {
                                return $q->where('country_id', $userDetail->country_id);
                            })
                            ->when(in_array(MODULE, ['admin']), function ($q) use ($userDetail) {
                                return $q->where('country_id', '129'); // WARNING: Hardcode country_id
                            })->get();
                        $bankAry = [];
                        foreach ($bankRes as $bankRow) {
                            $temp = [];
                            $temp['id'] = $bankRow['id'];
                            $temp['name'] = $bankRow['name'];
                            $temp['display'] = Lang::has('lang.' . $bankRow['translation_code']) ? Lang::get('lang.' . $bankRow['translation_code']) : $bankRow['name'];
                            $temp['priority'] = $bankRow['priority'];
                            $bankAry[] = $temp;
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'bank' => $bankAry,
                        ]);
                        break;

                    case 'currency':
                        $currency = Models\Currency::where('disabled', '0')->whereNotIn('iso', ['USDT'])->get();

                        $currencyAry = [];
                        foreach ($currency as $currencyKey => $currencyRow) {
                            $currencyAry[] = [
                                "id" => $currencyRow['id'],
                                "name" => $currencyRow['iso'],
                                "display" => Lang::has('lang.' . $currencyRow['iso']) ? Lang::get('lang.' . $currencyRow['iso']) : $currencyRow['iso'],
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['currency' => $currencyAry]);
                        break;

                    case 'currency_filter':
                        $currency = Models\Currency::whereNotIn('iso', ['USDT', 'MYR'])->get();

                        $currencyAry = [];
                        foreach ($currency as $currencyKey => $currencyRow) {
                            $currencyAry[] = [
                                "id" => $currencyRow['id'],
                                "name" => $currencyRow['iso'],
                                "display" => Lang::has('lang.' . $currencyRow['iso']) ? Lang::get('lang.' . $currencyRow['iso']) : $currencyRow['iso'],
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['currency_filter' => $currencyAry]);
                        break;

                    case 'currency_filter_convert_myr':
                        $dropdownName = "currency_filter_convert_myr";
                        $fromCreditID = Models\Credit::where(['name' => 'myr-credit'])->first()->id ?? 0;
                    case 'currency_filter_convert':
                        $dropdownName ??= "currency_filter_convert";
                        $availableCurrencyIds = Models\Convert::getConvertToCurrency(isset($fromCreditID) ? $fromCreditID : $request->from_credit_id ?? null);

                        $currency = Models\Currency::with([
                            "credit",
                            "country" => function ($q) {
                                return $q->whereIn('name', Config('general.valid_exchange_country'));
                            },
                        ])
                            ->whereNotIn('iso', ['USDT'])
                            ->whereIn('id', $availableCurrencyIds)
                            ->get();

                        $currencyAry = [];
                        // hardcode - if thb available, move it to first
                        $item_to_first = null;
                        foreach ($currency as $data) {
                            if ($data->iso == 'THB') {
                                $item_to_first = $data;
                            }
                        }
                        if (isset($item_to_first)) {
                            $currency = $currency->reject(function ($value) use ($item_to_first) {
                                return $value == $item_to_first;
                            })->prepend($item_to_first);
                        }
                        foreach ($currency as $currencyKey => $currencyRow) {
                            $id = $currencyRow->credit->id;
                            $iso = $currencyRow->iso;

                            $iso = Models\Convert::$outputConversion[$iso] ?? $iso;

                            $currencyAry[] = [
                                "id" => $id,
                                "name" => $iso,
                                "display" => Lang::has('lang.' . $iso) ? Lang::get('lang.' . $iso) : $iso,
                                "country_iso_code" => $currencyRow->country->iso_code2,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [$dropdownName => $currencyAry]);
                        unset($fromCreditID);
                        unset($dropdownName);
                        break;

                    case 'currency_status':
                        $status = Models\Currency::$disabled;
                        $statusAry = [];
                        foreach ($status as $statusKey => $statusRow) {
                            $statusAry[] = [
                                "id" => $statusRow,
                                "name" => $statusKey,
                                "display" => Lang::has('lang.' . $statusKey) ? Lang::get('lang.' . $statusKey) : $statusKey,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['currency_status' => $statusAry]);
                        break;

                    case 'kyc_status':
                        $status = Models\kyc::$kycStatus;
                        $statusAry = [];
                        foreach ($status as $statusKey => $statusRow) {
                            $statusAry[] = [
                                "id" => $statusRow,
                                "name" => $statusKey,
                                "display" => Lang::has('lang.kyc-' . $statusKey) ? Lang::get('lang.kyc-' . $statusKey) : $statusKey,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['kyc_status' => $statusAry]);
                        break;

                    case 'kyc_update_status':
                        $status = Models\kyc::$kycStatus;
                        $statusAry = [];
                        foreach ($status as $statusKey => $statusRow) {
                            if ($statusKey == 'pending') continue;

                            $statusAry[] = [
                                "id" => $statusRow,
                                "name" => $statusKey,
                                "display" => Lang::has('lang.kyc-update-' . $statusKey) ? Lang::get('lang.kyc-update-' . $statusKey) : $statusKey,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['kyc_update_status' => $statusAry]);
                        break;

                    case 'product':
                        $productAry = [];
                        Models\Product::with([
                            'productSetting' => function ($q) {
                                $q->where('name', 'hasWalletList');
                                $q->where('value', '1');
                            }
                        ])->where('status', 1)
                            ->whereNull('deleted_at')
                            ->get()->map(function ($p) use (&$productAry) {
                                if (!empty($p->productSetting)) {
                                    if (!empty($p->productSetting->first()->type)) {
                                        $hasWallet = true;
                                        $walletList = explode(',', $p->productSetting->first()->type);
                                    }
                                }

                                $productAry[] = [
                                    "id" => $p->id,
                                    "name" => $p->name,
                                    "display" => Lang::has('lang.' . $p->name) ? Lang::get('lang.' . $p->name) : $p->name,
                                    "option" => $walletList ?? null,
                                ];
                            });
                        $content['dropdown'] = array_merge($content['dropdown'], ['product' => $productAry]);
                        break;

                    case 'platform_list':
                        $productAry = [];
                        Models\Product::where('status', 1)
                            ->whereNull('deleted_at')
                            ->get()->map(function ($p) use (&$productAry) {
                                $productAry[] = [
                                    "id" => $p->id,
                                    "name" => $p->name,
                                    "display" => Lang::has('lang.' . $p->name) ? Lang::get('lang.' . $p->name) : $p->name,
                                ];
                            });

                        // extra - default
                        $name = "FW";
                        $productAry[] = [
                            "id" => 0,
                            "name" => $name,
                            "display" => Lang::has('lang.' . $name) ? Lang::get('lang.' . $name) : $name,
                        ];
                        $content['dropdown'] = array_merge($content['dropdown'], [$dropdown => $productAry]);
                        break;

                    case 'user_bank_status':
                        $statusRes = Models\UserBank::$status;
                        $statusAry = [];
                        foreach ($statusRes as $statusKey => $statusRow) {
                            $temp = [];
                            $temp['name'] = $statusKey;
                            $temp['display'] = Lang::has('lang.' . $statusKey) ? Lang::get('lang.' . $statusKey) : $statusKey;
                            $statusAry[] = $temp;
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'user_bank_status' => $statusAry,
                        ]);
                        break;

                    case 'announcement_status':
                        $announcementStatus = Models\Announcement::$status ?? [];

                        $statusAry = [];
                        foreach ($announcementStatus as $name => $value) {
                            $tempStatus['id'] = $value;
                            $tempStatus['name'] = $name;
                            $tempStatus['display'] = Lang::has('lang.' . $name) ? Lang::get('lang.' . $name) : $name;
                            $statusAry[] = $tempStatus;
                        }

                        $content['dropdown'] = array_merge($content['dropdown'], ['announcement_status' => $statusAry]);
                        break;

                    case 'memo_status':
                        $memoStatus = Models\Memo::$status ?? [];

                        $statusAry = [];
                        foreach ($memoStatus as $name => $value) {
                            $tempStatus['id'] = $value;
                            $tempStatus['name'] = $name;
                            $tempStatus['display'] = Lang::has('lang.' . $name) ? Lang::get('lang.' . $name) : $name;
                            $statusAry[] = $tempStatus;
                        }

                        $content['dropdown'] = array_merge($content['dropdown'], ['memo_status' => $statusAry]);
                        break;

                    case 'banner_redirect':
                        $redirects = [];
                        foreach (config('general.app_redirect.banner') as $redirect) {
                            $lang = str_replace("_", "-", $redirect);
                            $redirects[] = [
                                'data' => $redirect,
                                'display' => Lang::has('lang.' . $lang) ? Lang::get('lang.' . $lang) : ucfirst(str_replace("_", " ", $redirect))
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'banner_redirect' => $redirects,
                        ]);
                        break;

                    case 'export_report_name':
                        $report = Models\ExportReport::selectRaw('ANY_VALUE(name) AS name')->groupBy('name')->get();
                        $reportAry = [];
                        foreach ($report as $reportRow) {
                            $reportAry[] = [
                                "name" => $reportRow->name,
                                "display" => str_replace("-", "_", $reportRow->name),
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['export_report_name' => $reportAry]);
                        break;

                    case 'banner_status':
                        $bannerStatus = Models\Banner::$status ?? [];

                        $statusAry = [];
                        foreach ($bannerStatus as $name => $value) {
                            if ($name == 'deleted') continue;
                            $tempStatus['id'] = $value;
                            $tempStatus['name'] = $name;
                            $tempStatus['display'] = Lang::has('lang.' . $name) ? Lang::get('lang.' . $name) : $name;
                            $statusAry[] = $tempStatus;
                        }

                        $content['dropdown'] = array_merge($content['dropdown'], ['banner_status' => $statusAry]);
                        break;

                    case 'kyc_type':
                        $kycType = Models\Kyc::$kycType ?? [];
                        $typeAry = [];
                        foreach ($kycType as $name => $value) {
                            $tmpType['name'] = $name;
                            $tmpType['display'] = Lang::has('lang.' . $name) ? Lang::get('lang.' . $name) : ucfirst($name);
                            $typeAry[] = $tmpType;
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['kyc_type' => $typeAry]);
                        break;

                    case 'gender':
                        $genderAry = [];
                        $genderRes = config('users')['gender'];
                        foreach ($genderRes as $key => $genderRow) {
                            $temp['gender_id'] = $key;
                            $temp['name'] = $genderRow;
                            $temp['display'] = Lang::get('lang.' . $genderRow);
                            $genderAry[] = $temp;
                        }
                        $temp = [];
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'gender' => $genderAry,
                        ]);
                        break;

                    case 'race':
                        $raceAry = [];
                        $raceRes = config('users')['race'];
                        foreach ($raceRes as $key => $raceRow) {
                            $temp['race_id'] = $key;
                            $temp['name'] = $raceRow;
                            $temp['display'] = Lang::get('lang.' . $raceRow);
                            $raceAry[] = $temp;
                        }
                        $temp = [];
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'race' => $raceAry,
                        ]);
                        break;

                    case 'deposit':
                        $status = Models\Deposit::$status;
                        $statusAry = [];
                        foreach ($status as $statusKey => $statusRow) {
                            $statusAry[] = [
                                "id" => $statusRow,
                                "name" => $statusKey,
                                "display" => Lang::has('lang.' . $statusKey) ? Lang::get('lang.' . $statusKey) : $statusKey,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['deposit_status' => $statusAry]);


                        if (MODULE == "admin") {
                            $status = Arr::only(Models\Deposit::$status, ['approved', 'rejected']);
                            $statusAry = [];
                            foreach ($status as $statusKey => $statusRow) {
                                $statusAry[] = [
                                    "id" => $statusRow,
                                    "name" => $statusKey,
                                    "display" => Lang::has('lang.' . $statusKey) ? Lang::get('lang.' . $statusKey) : $statusKey,
                                ];
                            }
                            $content['dropdown'] = array_merge($content['dropdown'], ['deposit_update_status' => $statusAry]);
                        }

                        break;

                    case 'service_status_list':
                        $serviceStatusList = [];
                        foreach (Models\Services::$status as $statusName => $statusId) {
                            $serviceStatusList[] = [
                                'id' => $statusId,
                                'name' => $statusName,
                                'display' => Lang::has('lang.' . $statusName) ? Lang::get('lang.' . $statusName) : $statusName,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'service_status_list' => $serviceStatusList,
                        ]);
                        break;

                    case 'bank_transfer_status_list':
                        $bankTransferStatusList = [];
                        foreach (Models\Bank::$transferStatus as $statusName => $statusId) {
                            $bankTransferStatusList[] = [
                                'id' => $statusId,
                                'name' => $statusName,
                                'display' => Lang::has('lang.' . $statusName) ? Lang::get('lang.' . $statusName) : $statusName,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'bank_transfer_status_list' => $bankTransferStatusList,
                        ]);
                        break;

                    case 'bank_status_list':
                        $bankStatusList = [];
                        foreach (Models\Bank::$status as $statusName => $statusId) {
                            $bankStatusList[] = [
                                'id' => $statusId,
                                'name' => $statusName,
                                'display' => Lang::has('lang.' . $statusName) ? Lang::get('lang.' . $statusName) : $statusName,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'bank_status_list' => $bankStatusList,
                        ]);
                        break;

                    case 'tt_transfer_type_list':
                        $ttTransferTypeList = [];
                        foreach (Models\TelexTransfer::$type as $typeName => $typeId) {
                            $ttTransferTypeTempList = [
                                'id' => $typeId,
                                'name' => $typeName,
                                'display' => Lang::has('lang.' . $typeName) ? Lang::get('lang.' . $typeName) : $typeName,
                            ];

                            switch ($typeName) {
                                case 'normal-transfer':
                                    $ttTransferTypeTempList['period'] = "(< " . Lang::get("lang.twenty-four-hours") . ")";
                                    break;

                                case 'instant-transfer':
                                    $ttTransferTypeTempList['period'] = "(< " . Lang::get("lang.one-hour") . ")";
                                    break;

                                default:
                                    break;
                            }

                            $ttTransferTypeList[] = $ttTransferTypeTempList;
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'tt_transfer_type_list' => $ttTransferTypeList,
                        ]);
                        break;

                    case 'telex_transfer_status_list':
                        $telexTransferStatusList = [];
                        foreach (Models\TelexTransfer::$status as $statusName => $statusId) {
                            $telexTransferStatusList[] = [
                                'id' => $statusId,
                                'name' => $statusName,
                                'display' => Lang::has('lang.' . $statusName) ? Lang::get('lang.' . $statusName) : $statusName,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'telex_transfer_status_list' => $telexTransferStatusList,
                        ]);
                        break;

                    case 'telex_transfer_edit_status_list':
                        $editStatusList = Arr::only(Models\TelexTransfer::$status, ['cancel']);
                        if (MODULE == 'admin') $editStatusList = Arr::except(Models\TelexTransfer::$status, ['cancel']);
                        $telexTransferStatusList = [];
                        foreach ($editStatusList as $statusName => $statusId) {
                            $telexTransferStatusList[] = [
                                'id' => $statusId,
                                'name' => $statusName,
                                'display' => Lang::has('lang.' . $statusName) ? Lang::get('lang.' . $statusName) : $statusName,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [
                            'telex_transfer_edit_status_list' => $telexTransferStatusList,
                        ]);
                        break;

                    case 'withdrawal_status':
                        $status = Models\Withdrawal::$status;
                        $statusAry = [];
                        foreach ($status as $statusKey => $statusRow) {
                            $statusKeyDisplay = $statusKey;
                            if (in_array(MODULE, ['user', 'app']) && ($statusKeyDisplay == 'pending')) {
                                $statusKeyDisplay = 'processing';
                            }
                            $statusAry[] = [
                                "id" => $statusRow,
                                "name" => $statusKey,
                                "display" => Lang::has('lang.' . $statusKeyDisplay) ? Lang::get('lang.' . $statusKeyDisplay) : $statusKeyDisplay,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['withdrawal_status' => $statusAry]);
                        break;

                    case 'update_withdrawal_status':
                        $status = Arr::only(Models\Withdrawal::$status, ['pending', 'approved', 'rejected']);
                        $statusAry = [];
                        foreach ($status as $statusKey => $statusRow) {
                            $statusAry[] = [
                                "id" => $statusRow,
                                "name" => $statusKey,
                                "display" => Lang::has('lang.' . $statusKey) ? Lang::get('lang.' . $statusKey) : $statusKey,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['update_withdrawal_status' => $statusAry]);
                        break;

                    case 'schedule_status':
                        $status = Models\Schedule::$status;
                        $statusAry = [];
                        foreach ($status as $statusKey => $statusRow) {
                            $statusAry[] = [
                                "id" => $statusRow,
                                "name" => $statusKey,
                                "display" => Lang::has('lang.' . $statusKey) ? Lang::get('lang.' . $statusKey) : $statusKey,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['schedule_status' => $statusAry]);
                        break;

                    case 'ex_transfer_type':
                        $type = Models\ExTransfer::$type;
                        $exTransferType = [];
                        foreach ($type as $typeKey => $typeRow) {
                            $displayType = null;
                            if ($typeKey == "in") $displayType = 'cash-in';
                            if ($typeKey == "out") $displayType = 'cash-out';
                            $exTransferType[] = [
                                "id" => $typeRow,
                                "name" => $typeKey,
                                "display" => Lang::has('lang.' . $displayType) ? Lang::get('lang.' . $displayType) : $typeKey,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['ex_transfer_type' => $exTransferType]);
                        break;

                    case 'sys_language':
                        $lang = config('language');
                        $langIso = config('languageIso');
                        $sys_language = null;
                        foreach ($lang as $key => $value) {
                            $temp_lang = [];
                            $temp_lang['priority'] = $value;
                            $temp_lang['lang'] = $key;
                            $temp_lang['iso'] = isset($langIso[$key]) ? $langIso[$key] : null;
                            $temp_lang['display'] = Lang::has('lang.' . $key) ? Lang::get('lang.' . $key) : $key;
                            $sys_language[] = $temp_lang;
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['sys_language' => config('language'), 'sys_language_iso' => $sys_language]);
                        break;

                    case 'app_transaction_date':
                        $filterRange = ["today", "yesterday", "last-7-days", "last-30-days", "last-90-days"];
                        $range = [];
                        foreach ($filterRange as $filter) {
                            $temp = [];
                            $temp['display'] = Lang::has('lang.' . $filter) ? Lang::get('lang.' . $filter) : $filter;
                            $temp['to_date'] = date("Y-m-d");
                            $temp['from_date'] = date("Y-m-d"); // default value
                            switch ($filter) {
                                case 'today':
                                    $temp['from_date'] = date("Y-m-d");
                                    break;

                                case 'yesterday':
                                    $temp['from_date'] = date("Y-m-d", strtotime("-1 day"));
                                    $temp['to_date'] = date("Y-m-d", strtotime("-1 day"));
                                    break;

                                default:
                                    $filterData = explode("-", $filter);
                                    $temp['from_date'] = date("Y-m-d", strtotime("-" . $filterData[1] . " " . $filterData[2]));
                                    break;
                            }
                            $range[] = $temp;
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['app_transaction_date' => $range]);
                        break;

                    case 'deposit_method_status':
                        $status = Models\SystemSettingsAdmin::$depositMethodStatus;
                        $statusAry = [];
                        foreach ($status as $statusKey => $statusRow) {
                            $statusAry[] = [
                                "id" => $statusRow,
                                "name" => $statusKey,
                                "display" => Lang::has('lang.' . $statusKey) ? Lang::get('lang.' . $statusKey) : $statusKey,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], ['deposit_method_status' => $statusAry]);
                        break;

                    case 'currency_convert_status':
                        $status = Models\CreditSetting::$status;
                        $statusAry = [];
                        foreach ($status as $statusKey => $statusRow) {
                            $statusAry[] = [
                                "id" => $statusRow,
                                "name" => $statusKey,
                                "display" => Lang::has('lang.' . $statusKey) ? Lang::get('lang.' . $statusKey) : $statusKey,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [$dropdown => $statusAry]);
                        break;

                    case 'user_device_status':
                        $status = Models\UserDeviceInfo::$status;
                        $statusAry = [];
                        foreach ($status as $statusKey => $statusRow) {
                            $statusAry[] = [
                                "id" => $statusRow,
                                "name" => $statusKey,
                                "display" => Lang::has('lang.' . $statusKey) ? Lang::get('lang.' . $statusKey) : $statusKey,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [$dropdown => $statusAry]);
                        break;

                    case 'withdrawal_third_party_status':
                        $status = Models\WithdrawalThirdParty::$status;
                        $statusAry = [];
                        foreach ($status as $statusKey => $statusRow) {
                            $statusKeyDisplay = $statusKey;
                            if (in_array(MODULE, ['user', 'app']) && ($statusKeyDisplay == 'pending')) {
                                $statusKeyDisplay = 'processing';
                            }
                            $statusAry[] = [
                                "id" => $statusRow,
                                "name" => $statusKey,
                                "display" => Lang::has('lang.' . $statusKeyDisplay) ? Lang::get('lang.' . $statusKeyDisplay) : $statusKeyDisplay,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [$dropdown => $statusAry]);
                        break;
                    case 'update_withdrawal_third_party_status':
                        $status = Arr::only(Models\WithdrawalThirdParty::$status, ['pending', 'approved', 'rejected']);
                        $statusAry = [];
                        foreach ($status as $statusKey => $statusRow) {
                            $statusAry[] = [
                                "id" => $statusRow,
                                "name" => $statusKey,
                                "display" => Lang::has('lang.' . $statusKey) ? Lang::get('lang.' . $statusKey) : $statusKey,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [$dropdown => $statusAry]);
                        break;
                    case 'withdrawal_platform':
                        $data = Models\Product::where('type', 'oc')->get();
                        $dataAry = [];
                        foreach ($data as $dataRow) {
                            $dataAry[] = [
                                'id' => $dataRow->id ?? null,
                                'name' => $dataRow->name ?? null,
                                'display' => Lang::has('lang.' . $dataRow->name) ? Lang::get('lang.' . $dataRow->name) : $dataRow->name,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [$dropdown => $dataAry]);
                        break;
                    case 'store':
                        if (auth()->user()->is_master) {
                            $data = Models\Store::orderBy('priority')->get();
                        } else {
                            $adminId = auth()->user()->id;
                            $adminRes = Models\Admin::with(
                                [
                                    'adminDetail' =>
                                    function ($q) {
                                        return $q->where('name', 'store');
                                    }
                                ],
                            )
                                ->find($adminId);

                            $branchJE = $adminRes->adminDetail->first()->value ?? "[]";
                            $branchDE = json_decode($branchJE);

                            $data = Models\Store::whereIn('id', $branchDE)->orderBy('priority')->get();
                        }

                        $dataAry = [];
                        foreach ($data as $dataRow) {
                            $dataAry[] = [
                                'id' => $dataRow->id ?? null,
                                'name' => $dataRow->name ?? null,
                                'display' => Lang::has('lang.' . $dataRow->name) ? Lang::get('lang.' . $dataRow->name) : $dataRow->name,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [$dropdown => $dataAry]);
                        break;
                    case 'user_card_status':
                        $status = Models\UserCard::$status;
                        $statusAry = [];
                        foreach ($status as $statusKey => $statusRow) {
                            $statusAry[] = [
                                "id" => $statusRow,
                                "name" => $statusKey,
                                "display" => Lang::has('lang.' . $statusKey) ? Lang::get('lang.' . $statusKey) : $statusKey,
                            ];
                        }
                        $content['dropdown'] = array_merge($content['dropdown'], [$dropdown => $statusAry]);
                        break;
                }
            }

            return response()->json($content);
        }

        return $response;
    }
}
