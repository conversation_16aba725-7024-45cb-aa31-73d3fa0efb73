<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Tymon\JWTAuth\Exceptions\JWTException;
use Tymon\JWTAuth\Exceptions\TokenBlacklistedException;
use <PERSON><PERSON>\JWTAuth\Exceptions\TokenExpiredException;
use Tymon\JWTAuth\Exceptions\TokenInvalidException;

class JwtRefresh
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);
        $content = json_decode($response->content(), true);
        $guard = MODULE == 'admin' ? 'admin' : 'users';

        if (isset($content['status']) && $content['status'] && auth()->user()) {
            try {
                if(MODULE == 'app'){
                    $token = auth()->setTTL(env('USER_JWT_TTL'))->refresh();
                }else{
                    $token = auth()->refresh();
                }
            } catch (TokenExpiredException $e) {
                abort(500, 'Route [login] not defined.');
            } catch (TokenInvalidException $e) {
                abort(400, 'Token Invalid');
            } catch (TokenBlacklistedException $e) {
                abort(400, 'Token Blacklisted');
            } catch (JWTException $e) {
                abort(400, 'Token Absent');
            }
            $content = array_merge($content, [
                'access_token' => (string) $token,
                'token_type' => 'bearer',
            ]);

            DB::table('jwt_token')
            ->upsert([
                'guard' => $guard,
                'uid' => auth()->user()->id,
                'token' => (string) $token,
            ], ['login'],
            ['token']);

            return response()->json($content);
        }

        return $response;
    }
}
