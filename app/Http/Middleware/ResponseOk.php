<?php

namespace App\Http\Middleware;

use Closure;

/**
 * Description of ResponseOk
 *
 * this middleware should handle only when status code is 200.
 * other than this, must go through
 *
 * <AUTHOR>
 */
class ResponseOk
{
    public function handle($request, Closure $next, $guard = null)
    {
        $response = $next($request);

        // If excluded, continue
        if ($request->route() !== null) {
            $routeName = $request->route()->getName();
            if (in_array($routeName, explode(',', env('EXCLUDE_GLOBAL_MIDDLEWARE')))) {
                return $response;
            }
        }

        if (! app()->runningInConsole()) {
            if ($response->getStatusCode() == 200 /* && $routeName != 'docs' */) {
                return $this->returnOkFormat($response);
            }
        }

        return $response;
    }

    protected function returnOkFormat($response)
    {
        $content = json_decode($response->getContent(), true);
        $content['message'] = 'Granted';

        $response->setContent(json_encode($content));

        return $response;
    }
}
