<?php

namespace App\Http\Middleware;

use App\Models\UserDevice;
use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class Logging
{
    private static $oriContent;

    private static $logId;

    private static $apiStart;

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     *
     * JOB : store in logs, provide execution time for debugging purpose
     */
    public function handle($request, Closure $next)
    {
        self::$apiStart = microtime(true);

        // enable query log for debugging purpose
        if (env('APP_DEBUG') == true) {
            DB::connection()->enableQueryLog();
        }

        $response = $next($request);
        // if excluded, continue
        if ($request->route() !== null) {
            $routeName = $request->route()->getName() ?? $request->route()->uri;
            if (in_array($routeName, explode(',', env('EXCLUDE_GLOBAL_MIDDLEWARE')))) {
                return $response;
            }
            if (in_array($request->getPathInfo(), explode(',', env('EXCLUDE_GLOBAL_CALLBACK')))) {
                return $response;
            }
            if (in_array($request->getPathInfo(), ['/export-game-bet'])) {
                return $response;
            }

            // exclude telegraph route
            if (in_array($request->segment(1), ['/telegraph'])) {
                return $response;
            }
        }

        $data = [
            'ipaddr' => $request->ip(),
            'requestHeader' => [],
            'requestBody' => $request->toArray(),
            'responseBody' => [],
        ];

        $validVersion = false;
        $headers = $request->header();

        if (array_key_exists('lang', $headers)) {
            $data['requestHeader']['lang'] = $headers['lang'][0];
        }
        if (array_key_exists('device-type', $headers)) {
            $data['requestHeader']['device-type'] = $headers['device-type'][0];
        }
        if (array_key_exists('ip-web', $headers)) {
            $data['requestHeader']['ip-web'] = $headers['ip-web'][0];
            $request->server->add(['REMOTE_ADDR' => $data['requestHeader']['ip-web']]);
        }
        if (array_key_exists('user-agent', $headers)) {
            $data['requestHeader']['user-agent'] = $headers['user-agent'][0];
        }
        if (array_key_exists('app-version', $headers)) {
            $data['requestHeader']['app-version'] = $headers['app-version'][0];
            if (MODULE == 'app' && in_array($data['requestHeader']['app-version'], config('version'))) {
                $validVersion = true;
            }
        }
        if (array_key_exists('x-module', $headers)) {
            $data['requestHeader']['x-module'] = $headers['x-module'][0];
        }
        if (array_key_exists('stripe-signature', $headers)) {
            $data['requestHeader']['stripe-signature'] = $headers['stripe-signature'][0];
        }
        if (array_key_exists('platform', $headers)) {
            $data['requestHeader']['platform'] = $headers['platform'][0];
        }
        if (array_key_exists('authorization', $headers) && (getenv('APP_ENV') == 'staging')) {
            $data['requestHeader']['authorization'] = $headers['authorization'][0];
        }
        if (auth()->user()) {

            // Retrieve the authenticated user
            $user = auth()->user();
            // Update the last_active_at column for the authenticated user
            $user->update(['last_active_at' => now()]);

            $data['requestHeader']['uid'] = auth()->user()->id;
        }

        $ori = $response->original;
        $data['responseBody'] = gettype($ori) == 'string' || gettype($ori) == 'array' ? $ori : $ori?->toArray();
        self::$oriContent = $data['responseBody'];

        // hash sensitive text.
        if (getenv('APP_ENV') == 'production') {
            $sensitive = ['password', 'secret', 'password_confirmation', 'debug'];
            foreach ($data['requestBody'] as $key => &$value) {
                if (in_array($key, $sensitive)) {
                    $value = md5($value);
                }
            }
        }
        if (array_key_exists('user-agent', $headers)) {
            $checkAgent = explode('/', $data['requestHeader']['user-agent']);
        }

        if (! array_intersect(($checkAgent ?? []), ['Zabbix'])) {
            // save $data into db
            self::$logId = Str::uuid()->toString();
            $insert_log = [
                'id' => self::$logId,
                'module' => defined('MODULE') ? MODULE : 'unknown',
                'ipaddr' => $this->getIp() ?? json_encode($data['ipaddr']),
                'hostname' => $request->getHost(),
                'method' => $request->getMethod(),
                'api' => $request->getPathInfo(),
                'req_header' => json_encode($data['requestHeader']),
                'req_body' => json_encode($data['requestBody']),
                'res_body' => json_encode(['res' => 'Before route.']),
                'exec_time_sec' => microtime(true) - self::$apiStart,
            ];

            $insert_log['created'] = now();
            DB::table('logs')->insert($insert_log);
        }

        // extract response content
        $content = json_decode($response->getContent(), true);
        unset($content['ErrDetails']);

        // add query log for debugging purpose
        if (env('APP_DEBUG') == true) {
            $dbLogs = DB::getQueryLog();
            $dbTotalExecTime = 0;
            foreach ((array) $dbLogs as $v) {
                $dbTotalExecTime += $v['time'];
            }
            self::$oriContent['debug'] = [
                'queryLog' => $dbLogs,
                'queryLogTotalTime' => $dbTotalExecTime.' milisec',
            ];
        }

        if ($response->getStatusCode() != 404) {
            $content['timezone'] = env('APP_TIMEZONE');
            $content['environment'] = env('APP_ENV');
            $content['execution_duration'] = (microtime(true) - self::$apiStart).' sec';
            $content['log_id'] = self::$logId;
            $content['valid_version'] = $validVersion;
        }

        if (defined('CUSTOM_RESPONSE_OUTPUT') && CUSTOM_RESPONSE_OUTPUT == 1) {
            $response->setContent($response->getContent());
        } else {
            // store new response
            $response->setContent(json_encode($content));
        }

        if (MODULE == 'app' && Auth::check()) {
            // if ($request->is('logout')) UserDevice::updateUserDToken(['user_id' => auth()->user()->id]);
            // else UserDevice::where(['user_id' => auth()->user()->id])->update(['updated_at' => now()]);
        }

        return $response;
    }

    /**
     * Handle tasks after the response has been sent to the browser.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Illuminate\Http\Response  $response
     * @return void
     */
    public function terminate($request, $response)
    {
        if ($request->route() !== null) {
            $routeName = $request->route()->getName();
            if (in_array($routeName, explode(',', env('EXCLUDE_GLOBAL_MIDDLEWARE')))) {
                return $response;
            }
        }

        if (empty(self::$logId)) {
            return true;
        }

        $data = [];
        $data['responseBody'] = self::$oriContent;

        $checkApi = DB::table('logs')->where('id', self::$logId)->first();

        if (in_array($checkApi->api, ['/get-dashboard-personal', '/announcement/announcement-list', '/announcement/announcement-detail'])) {
            $data['responseBody'] = 'Data too long.';
        }

        DB::table('logs')->where('id', self::$logId)
            ->update([
                'res_body' => json_encode(['res' => $data['responseBody']]),
                'exec_time_sec' => microtime(true) - self::$apiStart,
            ]);
    }

    public function getIp()
    {
        foreach (['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'] as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }

        return request()->ip(); // it will return server ip when no client ip found
    }
}
