<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\UserDetail;

class StatusValidation
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $user = $request->user();
        $checkResetPassword = UserDetail::where(['user_id' => $user->id, 'name' => 'reset_password'])->whereNull('deleted_at')->first();
        if ($checkResetPassword) {
            auth()->logout();

            return redirect()->route('login');
        }

        return $next($request);
    }
}
