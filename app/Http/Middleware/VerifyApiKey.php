<?php

namespace App\Http\Middleware;

use App\Models\ApiKey;
use Closure;
use Illuminate\Http\Request;

class VerifyApiKey
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (! $request->hasHeader('api-key')) {
            throw new \Exception('Api key missing');
        }

        $apiKey = ApiKey::with('user')->firstWhere('api_key', $request->header('api-key'));

        if (! $apiKey) {
            throw new \Exception('Invalid Api Key');
        }

        $request->request->add(['user_id' => $apiKey->user_id]);

        return $next($request);
    }
}
