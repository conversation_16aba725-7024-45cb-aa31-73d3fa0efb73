<?php

namespace App\Http\Middleware;

use App\Models\ApiKey;
use Closure;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class Maintenance
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $maintainStart = env('MAINTENANCE_START') ?? null;
        $maintainEnd = env('MAINTENANCE_END') ?? null;
        $dateTime = date('Y-m-d H:i:s');
        if((strtotime($dateTime) >= strtotime($maintainStart)) && (strtotime($dateTime) <= strtotime($maintainEnd))){
            // throw new \Exception('System Under Maintenance. Please Try again later.');
            // abort(400,json_encode('System Under Maintenance. Please Try again later.'));
        }
        
        return $next($request);
    }
}
