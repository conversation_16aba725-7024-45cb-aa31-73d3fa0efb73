<?php

namespace App\Observers;

use App\Models\Convert;
use App\Models\ActivityLog;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class ConvertObserver
{
    /**
     * Handle the Convert "created" event.
     *
     * @param  \App\Models\Convert  $convert
     * @return void
     */
    public function created(Convert $convert)
    {
        $action = 'convert-add';

        $data = [];

        foreach($convert->getFillable() as $attribute) {
            $data[$attribute] = $convert->$attribute;
        }

        // Insert activity log
        ActivityLog::insertActivityLog($action,$convert->user_id,$data);
    }

    /**
     * Handle the Convert "updated" event.
     *
     * @param  \App\Models\Convert  $convert
     * @return void
     */
    public function updated(Convert $convert)
    {
        $action = 'convert-update';

        $data = [];

        foreach(Arr::only($convert->getChanges(), $convert->getFillable()) as $key => $val) {
            $data[$key] = [
                'old' => $convert->getOriginal($key),
                'new' => $val
            ];
        }

        // Insert activity log
        ActivityLog::insertActivityLog($action,$convert->user_id,$data);
    }

    /**
     * Handle the Convert "deleted" event.
     *
     * @param  \App\Models\Convert  $convert
     * @return void
     */
    public function deleted(Convert $convert)
    {
        //
    }

    /**
     * Handle the Convert "restored" event.
     *
     * @param  \App\Models\Convert  $convert
     * @return void
     */
    public function restored(Convert $convert)
    {
        //
    }

    /**
     * Handle the Convert "force deleted" event.
     *
     * @param  \App\Models\Convert  $convert
     * @return void
     */
    public function forceDeleted(Convert $convert)
    {
        //
    }
}
