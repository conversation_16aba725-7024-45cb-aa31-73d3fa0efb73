<?php

namespace App\Observers;

use App\Models\StaffRoles;
use App\Models\ActivityLog;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class StaffRolesObserver
{
    /**
     * Handle the Staff Roles "created" event.
     *
     * @param  \App\Models\StaffRoles  $staffRoles
     * @return void
     */
    public function created(StaffRoles $staffRoles)
    {
        $action = 'staff-roles-add';

        $data = [];
        foreach ($staffRoles->getFillable() as $attribute) {
            $data[$attribute] = $staffRoles->$attribute;
        }

        // Insert activity log
        ActivityLog::insertActivityLog($action, null, $data);
    }

    /**
     * Handle the Staff Roles "updated" event.
     *
     * @param  \App\Models\StaffRoles  $staffRoles
     * @return void
     */
    public function updated(StaffRoles $staffRoles)
    {
        $action = 'staff-roles-update';

        $data = [];

        foreach (Arr::only($staffRoles->getChanges(), $staffRoles->getFillable()) as $key => $val) {
            if ($key == 'updated_at') continue;
            if ($key == 'permissions_id') {
                $add = array_diff($staffRoles->getChanges(), $staffRoles->getOriginal($key));
                $remove = array_diff($staffRoles->getOriginal($key), $staffRoles->getChanges());
                $data[$key] = [
                    'old' => $remove,
                    'new' => $add,
                ];
            } else {
                $data[$key] = [
                    'old' => $staffRoles->getOriginal($key),
                    'new' => $val
                ];
            }
        }

        if (!empty($data)) {
            $data['id'] = $staffRoles->getOriginal('id');

            // Insert activity log
            ActivityLog::insertActivityLog($action, null, $data);
        }
    }

    /**
     * Handle the StaffRoles "deleted" event.
     *
     * @param  \App\Models\Staff Roles  $staffRoles
     * @return void
     */
    public function deleted(StaffRoles $staffRoles)
    {
        //
    }

    /**
     * Handle the StaffRoles "restored" event.
     *
     * @param  \App\Models\Staff Roles  $staffRoles
     * @return void
     */
    public function restored(StaffRoles $staffRoles)
    {
        //
    }

    /**
     * Handle the StaffRoles "force deleted" event.
     *
     * @param  \App\Models\Staff Roles  $staffRoles
     * @return void
     */
    public function forceDeleted(StaffRoles $staffRoles)
    {
        //
    }
}
