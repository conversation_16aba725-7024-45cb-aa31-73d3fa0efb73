<?php

namespace App\Observers;

use App\Models\CurrencyDetail;

class CurrencyDetailObserver
{
    /**
     * Handle the CurrencyDetail "created" event.
     *
     * @param  \App\Models\CurrencyDetail  $currencyDetail
     * @return void
     */
    public function created(CurrencyDetail $currencyDetail)
    {
        // Get Previous Record with same user_id, store_id and terminal_serial
        if (!isset($currencyDetail->user_id)) {
            return;
        }

        $previousRecord = CurrencyDetail::where('user_id', $currencyDetail->user_id)
            ->where('store_id', $currencyDetail->store_id)
            ->where('terminal_serial', $currencyDetail->terminal_serial)
            ->where('transaction_at', '<=', $currencyDetail->transaction_at)
            ->orderBy('transaction_at', 'DESC')
            ->first();

        /**
         * Scenario A
         * 1. User Card A Token In -> Machine A
         * 2. User Card A Token Out -> Machine A
         */
        if (
            isset($previousRecord) &&
            $previousRecord->terminal_serial == $currencyDetail->terminal_serial &&
            $previousRecord->user_card_id == $currencyDetail->user_card_id &&
            $previousRecord->operation_type_id == CurrencyDetail::$type['token_in'] &&
            $currencyDetail->operation_type_id == CurrencyDetail::$type['token_out'] &&
            $previousRecord->pnl_ref_id == null
        ) {
            $previousRecord->update([
                'pnl_amount' => $previousRecord->operation_qty - $currencyDetail->operation_qty,
                'pnl_ref_id' => $currencyDetail->id
            ]);
        }

        /**
         * Scenario B
         * 1. User Card A Token In -> Machine A
         * 2. User Card A Token In -> Machine A
         */
        if (
            isset($previousRecord) &&
            $previousRecord->terminal_serial == $currencyDetail->terminal_serial &&
            $previousRecord->user_card_id != $currencyDetail->user_card_id &&
            $previousRecord->operation_type_id == CurrencyDetail::$type['token_in'] &&
            $currencyDetail->operation_type_id == CurrencyDetail::$type['token_in'] &&
            $previousRecord->pnl_ref_id == null
        ) {
            $previousRecord->update([
                'pnl_amount' => $previousRecord->operation_qty,
                'pnl_ref_id' => $previousRecord->id
            ]);
        }

        /**
         * Scenario C
         * 1. User Card A Token In -> Machine A
         * 2. User Card B Token In -> Machine A
         */
        if (
            isset($previousRecord) &&
            $previousRecord->terminal_serial == $currencyDetail->terminal_serial &&
            $previousRecord->user_card_id == $currencyDetail->user_card_id &&
            $previousRecord->operation_type_id == CurrencyDetail::$type['token_in'] &&
            $currencyDetail->operation_type_id == CurrencyDetail::$type['token_in'] &&
            $previousRecord->pnl_ref_id == null
        ) {
            $previousRecord->update([
                'pnl_amount' => $previousRecord->operation_qty,
                'pnl_ref_id' => $previousRecord->id
            ]);
        }
    }

    /**
     * Handle the CurrencyDetail "updated" event.
     *
     * @param  \App\Models\CurrencyDetail  $currencyDetail
     * @return void
     */
    public function updated(CurrencyDetail $currencyDetail)
    {
        //
    }

    /**
     * Handle the CurrencyDetail "deleted" event.
     *
     * @param  \App\Models\CurrencyDetail  $currencyDetail
     * @return void
     */
    public function deleted(CurrencyDetail $currencyDetail)
    {
        //
    }

    /**
     * Handle the CurrencyDetail "restored" event.
     *
     * @param  \App\Models\CurrencyDetail  $currencyDetail
     * @return void
     */
    public function restored(CurrencyDetail $currencyDetail)
    {
        //
    }

    /**
     * Handle the CurrencyDetail "force deleted" event.
     *
     * @param  \App\Models\CurrencyDetail  $currencyDetail
     * @return void
     */
    public function forceDeleted(CurrencyDetail $currencyDetail)
    {
        //
    }
}
