<?php

namespace App\Observers;

use App\Models\ActivityLog;
use App\Models\ExTransfer;
use Illuminate\Support\Arr;

class ExTransferObserver
{
    /**
     * Handle the ExTransfer "created" event.
     *
     * @param  \App\Models\ExTransfer  $exTransfer
     * @return void
     */
    public function created(ExTransfer $exTransfer)
    {
        //
    }

    /**
     * Handle the ExTransfer "updated" event.
     *
     * @param  \App\Models\ExTransfer  $exTransfer
     * @return void
     */
    public function updated(ExTransfer $exTransfer)
    {
        $action = 'ex-transfer-update';

        if (request()->has('act-log')) {
            $action = request()->input('act-log');
        }

        $data = [];

        foreach(Arr::only($exTransfer->getChanges(), $exTransfer->getFillable()) as $key => $val) {
            $data[$key] = [
                'old' => $exTransfer->getOriginal($key),
                'new' => $val
            ];
        }

        $data['id'] = $exTransfer->getOriginal('id');
        

        // Insert activity log
        ActivityLog::insertActivityLog($action,$exTransfer->user_id,$data);
    }

    /**
     * Handle the ExTransfer "deleted" event.
     *
     * @param  \App\Models\ExTransfer  $exTransfer
     * @return void
     */
    public function deleted(ExTransfer $exTransfer)
    {
        //
    }

    /**
     * Handle the ExTransfer "restored" event.
     *
     * @param  \App\Models\ExTransfer  $exTransfer
     * @return void
     */
    public function restored(ExTransfer $exTransfer)
    {
        //
    }

    /**
     * Handle the ExTransfer "force deleted" event.
     *
     * @param  \App\Models\ExTransfer  $exTransfer
     * @return void
     */
    public function forceDeleted(ExTransfer $exTransfer)
    {
        //
    }
}
