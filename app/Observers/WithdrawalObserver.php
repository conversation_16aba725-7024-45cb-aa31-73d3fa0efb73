<?php

namespace App\Observers;


use App\Models\Withdrawal;
use App\Traits\DecimalTrait;

class WithdrawalObserver
{
    /**
     * Handle the Withdrawal "created" event.
     *
     * @param  \App\Models\Withdrawal  $withdrawal
     * @return void
     */
    public function created(Withdrawal $withdrawal)
    {
        //
    }

    /**
     * Handle the Withdrawal "updated" event.
     *
     * @param  \App\Models\Withdrawal  $withdrawal
     * @return void
     */
    public function updated(Withdrawal $withdrawal)
    {
        // Check if the withdrawal status was changed to approved (status = 1)
        if ($withdrawal->isDirty('status') && $withdrawal->status == 1) {
            $this->broadcastLiveTransaction($withdrawal);
        }
    }

    /**
     * Handle the Withdrawal "deleted" event.
     *
     * @param  \App\Models\Withdrawal  $withdrawal
     * @return void
     */
    public function deleted(Withdrawal $withdrawal)
    {
        //
    }

    /**
     * Handle the Withdrawal "restored" event.
     *
     * @param  \App\Models\Withdrawal  $withdrawal
     * @return void
     */
    public function restored(Withdrawal $withdrawal)
    {
        //
    }

    /**
     * Handle the Withdrawal "force deleted" event.
     *
     * @param  \App\Models\Withdrawal  $withdrawal
     * @return void
     */
    public function forceDeleted(Withdrawal $withdrawal)
    {
        //
    }

    /**
     * Broadcast the live transaction event.
     *
     * @param  \App\Models\Withdrawal  $withdrawal
     * @return void
     */
    private function broadcastLiveTransaction(Withdrawal $withdrawal)
    {
        // Load the user relationship if not already loaded
        if (!$withdrawal->relationLoaded('user')) {
            $withdrawal->load('user:id,name,username');
        }

        $transaction = [
            'type' => 'withdrawal',
            'username' => $this->maskUsername($withdrawal->user->name ?? $withdrawal->user->username ?? 'User'),
            'amount' => DecimalTrait::setDecimal($withdrawal->amount)
        ];

        \App\Jobs\BroadcastLiveTransactionJob::dispatchSync($transaction);
    }

    /**
     * Mask the username for privacy.
     *
     * @param string $username
     * @return string
     */
    private function maskUsername($username)
    {
        if (empty($username)) {
            return 'U****r';
        }

        $length = mb_strlen($username);

        if ($length <= 2) {
            return $username . str_repeat('*', 6 - $length);
        } elseif ($length <= 6) {
            $firstChar = mb_substr($username, 0, 1);
            $lastChar = mb_substr($username, -1, 1);
            $maskedPart = str_repeat('*', $length - 2);
            return $firstChar . $maskedPart . $lastChar;
        } else {
            $firstChar = mb_substr($username, 0, 1);
            $lastChar = mb_substr($username, -1, 1);
            return $firstChar . '****' . $lastChar;
        }
    }
}
