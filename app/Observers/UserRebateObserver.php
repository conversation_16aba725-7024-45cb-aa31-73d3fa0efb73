<?php

namespace App\Observers;


use App\Models\UserRebate;
use App\Traits\DecimalTrait;

class UserRebateObserver
{
    /**
     * Handle the UserRebate "created" event.
     *
     * @param  \App\Models\UserRebate  $userRebate
     * @return void
     */
    public function created(UserRebate $userRebate)
    {
        //
    }

    /**
     * Handle the UserRebate "updated" event.
     *
     * @param  \App\Models\UserRebate  $userRebate
     * @return void
     */
    public function updated(UserRebate $userRebate)
    {
        // Check if the rebate status was changed to approved (status = 1)
        if ($userRebate->isDirty('status') && $userRebate->status == 1) {
            $this->broadcastLiveTransaction($userRebate);
        }
    }

    /**
     * Handle the UserRebate "deleted" event.
     *
     * @param  \App\Models\UserRebate  $userRebate
     * @return void
     */
    public function deleted(UserRebate $userRebate)
    {
        //
    }

    /**
     * Handle the UserRebate "restored" event.
     *
     * @param  \App\Models\UserRebate  $userRebate
     * @return void
     */
    public function restored(UserRebate $userRebate)
    {
        //
    }

    /**
     * Handle the UserRebate "force deleted" event.
     *
     * @param  \App\Models\UserRebate  $userRebate
     * @return void
     */
    public function forceDeleted(UserRebate $userRebate)
    {
        //
    }

    /**
     * Broadcast the live transaction event.
     *
     * @param  \App\Models\UserRebate  $userRebate
     * @return void
     */
    private function broadcastLiveTransaction(UserRebate $userRebate)
    {
        // Load the user relationship if not already loaded
        if (!$userRebate->relationLoaded('user')) {
            $userRebate->load('user:id,name,username');
        }

        $transaction = [
            'type' => 'rebate',
            'username' => $this->maskUsername($userRebate->user->name ?? $userRebate->user->username ?? 'User'),
            'amount' => DecimalTrait::setDecimal($userRebate->amount)
        ];

        \App\Jobs\BroadcastLiveTransactionJob::dispatchSync($transaction);
    }

    /**
     * Mask the username for privacy.
     *
     * @param string $username
     * @return string
     */
    private function maskUsername($username)
    {
        if (empty($username)) {
            return 'U****r';
        }

        $length = mb_strlen($username);

        if ($length <= 2) {
            return $username . str_repeat('*', 6 - $length);
        } elseif ($length <= 6) {
            $firstChar = mb_substr($username, 0, 1);
            $lastChar = mb_substr($username, -1, 1);
            $maskedPart = str_repeat('*', $length - 2);
            return $firstChar . $maskedPart . $lastChar;
        } else {
            $firstChar = mb_substr($username, 0, 1);
            $lastChar = mb_substr($username, -1, 1);
            return $firstChar . '****' . $lastChar;
        }
    }
}
