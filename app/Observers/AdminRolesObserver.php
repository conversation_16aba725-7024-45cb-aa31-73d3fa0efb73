<?php

namespace App\Observers;

use App\Models\AdminRoles;
use App\Models\ActivityLog;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class AdminRolesObserver
{
    /**
     * Handle the Admin Roles "created" event.
     *
     * @param  \App\Models\AdminRoles  $adminRoles
     * @return void
     */
    public function created(AdminRoles $adminRoles)
    {
        $action = 'admin-roles-add';

        $data = [];
        foreach ($adminRoles->getFillable() as $attribute) {
            $data[$attribute] = $adminRoles->$attribute;
        }

        // Insert activity log
        ActivityLog::insertActivityLog($action, null, $data);
    }

    /**
     * Handle the Admin Roles "updated" event.
     *
     * @param  \App\Models\AdminRoles  $adminRoles
     * @return void
     */
    public function updated(AdminRoles $adminRoles)
    {
        $action = 'admin-roles-update';

        $data = [];

        foreach (Arr::only($adminRoles->getChanges(), $adminRoles->getFillable()) as $key => $val) {
            if ($key == 'updated_at') continue;
            if ($key == 'permissions_id') {
                $add = array_diff($adminRoles->getChanges(), $adminRoles->getOriginal($key));
                $remove = array_diff($adminRoles->getOriginal($key), $adminRoles->getChanges());
                $data[$key] = [
                    'old' => $remove,
                    'new' => $add,
                ];
            } else {
                $data[$key] = [
                    'old' => $adminRoles->getOriginal($key),
                    'new' => $val
                ];
            }
        }

        if (!empty($data)) {
            $data['id'] = $adminRoles->getOriginal('id');

            // Insert activity log
            ActivityLog::insertActivityLog($action, null, $data);
        }
    }

    /**
     * Handle the AdminRoles "deleted" event.
     *
     * @param  \App\Models\Admin Roles  $adminRoles
     * @return void
     */
    public function deleted(AdminRoles $adminRoles)
    {
        //
    }

    /**
     * Handle the AdminRoles "restored" event.
     *
     * @param  \App\Models\Admin Roles  $adminRoles
     * @return void
     */
    public function restored(AdminRoles $adminRoles)
    {
        //
    }

    /**
     * Handle the AdminRoles "force deleted" event.
     *
     * @param  \App\Models\Admin Roles  $adminRoles
     * @return void
     */
    public function forceDeleted(AdminRoles $adminRoles)
    {
        //
    }
}
