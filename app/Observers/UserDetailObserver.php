<?php

namespace App\Observers;

use App\Models\UserDetail;
use App\Models\ActivityLog;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class UserDetailObserver
{
    /**
     * Handle the UserDetail "created" event.
     *
     * @param  \App\Models\UserDetail  $userDetail
     * @return void
     */
    public function created(UserDetail $userDetail)
    {
        $action = 'add-user-detal';

        $data = [];
        if (request()->has('act-log')) {
            $action = request()->input('act-log');
        }
        
        foreach($userDetail->getFillable() as $attribute) {
            $data[$attribute] = $userDetail->$attribute;
        }

        //insert activity log
        ActivityLog::insertActivityLog($action,$userDetail->user_id,$data);
    }

    /**
     * Handle the UserDetail "updated" event.
     *
     * @param  \App\Models\UserDetail  $userDetail
     * @return void
     */
    public function updated(UserDetail $userDetail)
    {
        $action = 'update-user-detal';

        if (request()->has('act-log')) {
            $action = request()->input('act-log');
        }

        $data = [];
        foreach(Arr::only($userDetail->getChanges(), $userDetail->getFillable()) as $key => $val) {
            unset($data);
            $data[$key] = [
                'old' => $userDetail->getOriginal($key),
                'new' => $val
            ];

            //insert activity log
            ActivityLog::insertActivityLog($action,$userDetail->user_id,$data);
        }
    }

    /**
     * Handle the UserDetail "deleted" event.
     *
     * @param  \App\Models\UserDetail  $userDetail
     * @return void
     */
    public function deleted(UserDetail $userDetail)
    {
        $action = 'delete-user-detal';

        if (request()->has('act-log')) {
            $action = request()->input('act-log');
        }

        //insert activity log
        ActivityLog::insertActivityLog($action, $userDetail->user_id, ['id' => $userDetail->id]);
    }

    /**
     * Handle the UserDetail "restored" event.
     *
     * @param  \App\Models\UserDetail  $userDetail
     * @return void
     */
    public function restored(UserDetail $userDetail)
    {
        //
    }

    /**
     * Handle the UserDetail "force deleted" event.
     *
     * @param  \App\Models\UserDetail  $userDetail
     * @return void
     */
    public function forceDeleted(UserDetail $userDetail)
    {
        //
    }
}
