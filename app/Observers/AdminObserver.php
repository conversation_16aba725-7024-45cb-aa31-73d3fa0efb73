<?php

namespace App\Observers;

use App\Models\Admin;
use App\Models\ActivityLog;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class AdminObserver
{
    /**
     * Handle the Admin "created" event.
     *
     * @param  \App\Models\Admin  $admin
     * @return void
     */
    public function created(Admin $admin)
    {
        $action = 'admin-add';

        $data = [];
        foreach($admin->getFillable() as $attribute) {
            $data[$attribute] = $admin->$attribute;
        }

        // Insert activity log
        ActivityLog::insertActivityLog($action,null,$data);
    }

    /**
     * Handle the Admin "updated" event.
     *
     * @param  \App\Models\Admin  $admin
     * @return void
     */
    public function updated(Admin $admin)
    {
        $action = 'admin-update';

        $data = [];

        foreach(Arr::only($admin->getChanges(), $admin->getFillable()) as $key => $val) {
            if ($key == 'permissions_id') {
                continue;
            }
            $old = $admin->getOriginal($key);

            $data[$key] = [
                'old' => is_array($old) ? json_encode($old) : $old,
                'new' => $val
            ];
        }

        if (!empty($data)) {
            $data['id'] = $admin->getOriginal('id');

            // Insert activity log
            ActivityLog::insertActivityLog($action,null,$data);
        }
    }

    /**
     * Handle the Admin "deleted" event.
     *
     * @param  \App\Models\Admin  $admin
     * @return void
     */
    public function deleted(Admin $admin)
    {
        //
    }

    /**
     * Handle the Admin "restored" event.
     *
     * @param  \App\Models\Admin  $admin
     * @return void
     */
    public function restored(Admin $admin)
    {
        //
    }

    /**
     * Handle the Admin "force deleted" event.
     *
     * @param  \App\Models\Admin  $admin
     * @return void
     */
    public function forceDeleted(Admin $admin)
    {
        //
    }
}
