<?php

namespace App\Observers;


use App\Models\Deposit;
use App\Traits\DecimalTrait;

class DepositObserver
{
    /**
     * Handle the Deposit "created" event.
     *
     * @param  \App\Models\Deposit  $deposit
     * @return void
     */
    public function created(Deposit $deposit)
    {
        //
    }

    /**
     * Handle the Deposit "updated" event.
     *
     * @param  \App\Models\Deposit  $deposit
     * @return void
     */
    public function updated(Deposit $deposit)
    {
        // Check if the deposit status was changed to approved (status = 1)
        if ($deposit->isDirty('status') && $deposit->status == 1) {
            $this->broadcastLiveTransaction($deposit);
        }
    }

    /**
     * Handle the Deposit "deleted" event.
     *
     * @param  \App\Models\Deposit  $deposit
     * @return void
     */
    public function deleted(Deposit $deposit)
    {
        //
    }

    /**
     * Handle the Deposit "restored" event.
     *
     * @param  \App\Models\Deposit  $deposit
     * @return void
     */
    public function restored(Deposit $deposit)
    {
        //
    }

    /**
     * Handle the Deposit "force deleted" event.
     *
     * @param  \App\Models\Deposit  $deposit
     * @return void
     */
    public function forceDeleted(Deposit $deposit)
    {
        //
    }

    /**
     * Broadcast the live transaction event.
     *
     * @param  \App\Models\Deposit  $deposit
     * @return void
     */
    private function broadcastLiveTransaction(Deposit $deposit)
    {
        // Load the user relationship if not already loaded
        if (!$deposit->relationLoaded('user')) {
            $deposit->load('user:id,name,username');
        }

        $transaction = [
            'type' => 'deposit',
            'username' => $this->maskUsername($deposit->user->name ?? $deposit->user->username ?? 'User'),
            'amount' => DecimalTrait::setDecimal($deposit->amount)
        ];
        \App\Jobs\BroadcastLiveTransactionJob::dispatchSync($transaction);
    }

    /**
     * Mask the username for privacy.
     *
     * @param string $username
     * @return string
     */
    private function maskUsername($username)
    {
        if (empty($username)) {
            return 'U****r';
        }

        $length = mb_strlen($username);

        if ($length <= 2) {
            return $username . str_repeat('*', 6 - $length);
        } elseif ($length <= 6) {
            $firstChar = mb_substr($username, 0, 1);
            $lastChar = mb_substr($username, -1, 1);
            $maskedPart = str_repeat('*', $length - 2);
            return $firstChar . $maskedPart . $lastChar;
        } else {
            $firstChar = mb_substr($username, 0, 1);
            $lastChar = mb_substr($username, -1, 1);
            return $firstChar . '****' . $lastChar;
        }
    }
}
