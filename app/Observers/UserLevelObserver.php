<?php

namespace App\Observers;

use App\Models\UserLevel;
use App\Models\VipLevel;

class UserLevelObserver
{
    /**
     * Handle the UserLevel "created" event.
     *
     * @return void
     */
    public function created(UserLevel $userLevel)
    {
        //
    }

    /**
     * Handle the UserLevel "updated" event.
     *
     * @return void
     */
    public function updated(UserLevel $userLevel)
    {
        // if (! $userLevel->isDirty('current_point')) {
        //     return;
        // }
        //
        // $nextLevel = VipLevel::where('status', true)
        //     ->where('level', $userLevel->vip_level->level + 1)
        //     ->first();
        //
        // if ($nextLevel && $userLevel->current_point >= $nextLevel->target_point) {
        //     $userLevel->vip_level_id = $nextLevel->id;
        //     $userLevel->save();
        // }
    }

    /**
     * Handle the UserLevel "deleted" event.
     *
     * @return void
     */
    public function deleted(UserLevel $userLevel)
    {
        //
    }

    /**
     * Handle the UserLevel "restored" event.
     *
     * @return void
     */
    public function restored(UserLevel $userLevel)
    {
        //
    }

    /**
     * Handle the UserLevel "force deleted" event.
     *
     * @return void
     */
    public function forceDeleted(UserLevel $userLevel)
    {
        //
    }
}
