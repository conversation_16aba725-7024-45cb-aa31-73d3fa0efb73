<?php

namespace App\Observers;

use App\Models\ActivityLog;
use App\Models\Providers\MtMachine;
use Illuminate\Support\Arr;

class MachineObserver
{
    /**
     * Handle the MtMachine "created" event.
     *
     * @param  \App\Models\MtMachine  $mtMachine
     * @return void
     */
    public function created(MtMachine $mtMachine)
    {
        //
    }

    /**
     * Handle the MtMachine "updated" event.
     *
     * @param  \App\Models\MtMachine  $mtMachine
     * @return void
     */
    public function updated(MtMachine $mtMachine)
    {
        // $action = 'machine-update';

        // $data = [];

        // foreach (Arr::only($mtMachine->getChanges(), $mtMachine->getFillable()) as $key => $val) {
        //     $data[$key] = [
        //         'old' => $mtMachine->getOriginal($key),
        //         'new' => ($val != '') ? $val : 0,
        //     ];
        // }

        // $data['id'] = $mtMachine->getOriginal('id');

        // // Insert activity log
        // ActivityLog::insertActivityLog($action, auth()->user()->id, $data);
    }

    /**
     * Handle the MtMachine "deleted" event.
     *
     * @param  \App\Models\MtMachine  $mtMachine
     * @return void
     */
    public function deleted(MtMachine $mtMachine)
    {
        //
    }

    /**
     * Handle the MtMachine "restored" event.
     *
     * @param  \App\Models\MtMachine  $mtMachine
     * @return void
     */
    public function restored(MtMachine $mtMachine)
    {
        //
    }

    /**
     * Handle the MtMachine "force deleted" event.
     *
     * @param  \App\Models\MtMachine  $mtMachine
     * @return void
     */
    public function forceDeleted(MtMachine $mtMachine)
    {
        //
    }
}
