<?php

namespace App\Observers;

use Illuminate\Database\Eloquent\Model;
use App\Models\ActivityLog;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class GlobalObserver
{
    /**
     * Handle the Model "created" event.
     *
     * @param  \App\Models\Model  $model
     * @return void
     */
    public function created(Model $model)
    {   
        ################ Initial ################  
        $data = [];  
        $userID = isset($model->user_id) ? $model->user_id : null;
        $tableName = $model->getTable();
        $slug = str_replace([' ','_'], '-', $tableName);

        $action = $slug.'-add';

        if (request()->has('act-log')) {
            $action = request()->input('act-log');
        }

        ################ Conditions ################
        //table to ignore
        $ignoreAry = ['order-payment'];        

        ################ Data Handle ################
        if (!in_array($slug,$ignoreAry)) {
            foreach ($model->getFillable() as $attribute) {
                $data[$attribute] = $model->$attribute;
            }

            // Insert activity log
            ActivityLog::insertActivityLog($action,$userID,$data);
        }
    }

    /**
     * Handle the Model "updated" event.
     *
     * @param  \App\Models\Model  $model
     * @return void
     */
    public function updated(Model $model)
    {
        ################ Initial ################
        $data = [];
        $userID = isset($model->user_id) ? $model->user_id : null;
        $tableName = $model->getTable();
        $slug = str_replace([' ','_'], '-', $tableName); 

        $action = $slug.'-update';

        if (request()->has('act-log')) {
            $action = request()->input('act-log');
        }

        ################ Conditions ################
        //table to ignore
        $ignoreAry = [];  
        //`name` column to ignore
        $nameIgnoreAry = ['category','distribution-center','product','package'];

        ################ Data Handle ################
        if (!in_array($slug,$ignoreAry)) {
            foreach (Arr::only($model->getChanges(), $model->getFillable()) as $key => $val) {
                //General Exception
                if (strtotime($val) && date('Y-m-d H:i:s', strtotime($val)) == date('Y-m-d H:i:s', strtotime($model->getOriginal($key)))) continue;
                if (is_numeric($val) && floatval($model->getOriginal($key)) == floatval($val)) continue;
                if ($key == 'updated_at') continue;
                if ($key == 'name' && in_array($slug,$nameIgnoreAry)) continue;
                
                $data[$key] = [
                    'old' => $model->getOriginal($key),
                    'new' => $val
                ];
            }

            //Data Insertion
            if (!empty($data)) {
                $data['id'] = $model->getOriginal('id');

                // Insert activity log
                ActivityLog::insertActivityLog($action,$userID,$data);
            }
        }
    }

    /**
     * Handle the Model "deleted" event.
     *
     * @param  \App\Models\Model  $model
     * @return void
     */
    public function deleted(Model $model)
    {
        //
    }

    /**
     * Handle the Model "restored" event.
     *
     * @param  \App\Models\Model  $model
     * @return void
     */
    public function restored(Model $model)
    {
        //
    }

    /**
     * Handle the Model "force deleted" event.
     *
     * @param  \App\Models\Model  $model
     * @return void
     */
    public function forceDeleted(Model $model)
    {
        //
    }
}
