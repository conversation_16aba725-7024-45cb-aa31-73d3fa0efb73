<?php

namespace App\Observers;

use App\Models\UserBank;
use App\Models\ActivityLog;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class UserBankObserver
{
    /**
     * Handle the UserBank "created" event.
     *
     * @param  \App\Models\UserBank  $userBank
     * @return void
     */
    public function created(UserBank $userBank)
    {
        $action = 'user-bank-add';

        $data = [];
        
        foreach($userBank->getFillable() as $attribute) {
            $data[$attribute] = $userBank->$attribute;
        }

        // Insert activity log
        ActivityLog::insertActivityLog($action,$userBank->user_id,$data);
    }

    /**
     * Handle the UserBank "updated" event.
     *
     * @param  \App\Models\UserBank  $userBank
     * @return void
     */
    public function updated(UserBank $userBank)
    {
        $action = 'user-bank-update';

        $data = [];

        foreach(Arr::only($userBank->getChanges(), $userBank->getFillable()) as $key => $val) {
            $data[$key] = [
                'old' => $userBank->getOriginal($key),
                'new' => ($val != '') ? $val : 0,
            ];
        }

        $data['id'] = $userBank->getOriginal('id');

        // Insert activity log
        ActivityLog::insertActivityLog($action,$userBank->user_id,$data);
    }

    /**
     * Handle the UserBank "deleted" event.
     *
     * @param  \App\Models\UserBank  $userBank
     * @return void
     */
    public function deleted(UserBank $userBank)
    {
        //
    }

    /**
     * Handle the UserBank "restored" event.
     *
     * @param  \App\Models\UserBank  $userBank
     * @return void
     */
    public function restored(UserBank $userBank)
    {
        //
    }

    /**
     * Handle the UserBank "force deleted" event.
     *
     * @param  \App\Models\UserBank  $userBank
     * @return void
     */
    public function forceDeleted(UserBank $userBank)
    {
        //
    }
}
