<?php

namespace App\Observers;

use App\Models\CreditTransaction;
use App\Models\ActivityLog;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class CreditTransactionObserver
{
    public static $observeSubject = [
        'adjustment-in',
        'adjustment-out',
    ];

    /**
     * Handle the CreditTransaction "created" event.
     *
     * @param  \App\Models\CreditTransaction  $creditTransaction
     * @return void
     */
    public function created(CreditTransaction $creditTransaction)
    {
        $data = [];

        if(isset($creditTransaction->subject_type) && !empty($creditTransaction->subject_type)){
            $subjectAry = config('subject') ?? [];
            $subject = array_search($creditTransaction->subject_type, $subjectAry);

            if(in_array($subject , self::$observeSubject)){
                foreach($creditTransaction->getFillable() as $attribute) {
                    $data[$attribute] = $creditTransaction->$attribute;
                }

                // Insert activity log
                ActivityLog::insertActivityLog($subject,$creditTransaction->user_id,$data);
            }
        }
    }

    /**
     * Handle the CreditTransaction "updated" event.
     *
     * @param  \App\Models\CreditTransaction  $creditTransaction
     * @return void
     */
    public function updated(CreditTransaction $creditTransaction)
    {
        //
    }

    /**
     * Handle the CreditTransaction "deleted" event.
     *
     * @param  \App\Models\CreditTransaction  $creditTransaction
     * @return void
     */
    public function deleted(CreditTransaction $creditTransaction)
    {
        //
    }

    /**
     * Handle the CreditTransaction "restored" event.
     *
     * @param  \App\Models\CreditTransaction  $creditTransaction
     * @return void
     */
    public function restored(CreditTransaction $creditTransaction)
    {
        //
    }

    /**
     * Handle the CreditTransaction "force deleted" event.
     *
     * @param  \App\Models\CreditTransaction  $creditTransaction
     * @return void
     */
    public function forceDeleted(CreditTransaction $creditTransaction)
    {
        //
    }
}
