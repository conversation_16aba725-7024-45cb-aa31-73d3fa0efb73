<?php

namespace App\Observers;

use App\Models\Staff;
use App\Models\ActivityLog;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class StaffObserver
{
    /**
     * Handle the Staff "created" event.
     *
     * @param  \App\Models\Staff  $staff
     * @return void
     */
    public function created(Staff $staff)
    {
        $action = 'staff-add';

        $data = [];
        foreach($staff->getFillable() as $attribute) {
            $data[$attribute] = $staff->$attribute;
        }

        // Insert activity log
        ActivityLog::insertActivityLog($action,null,$data);
    }

    /**
     * Handle the Staff "updated" event.
     *
     * @param  \App\Models\Staff  $staff
     * @return void
     */
    public function updated(Staff $staff)
    {
        $action = 'staff-update';

        $data = [];

        foreach(Arr::only($staff->getChanges(), $staff->getFillable()) as $key => $val) {
            if ($key == 'permissions_id') {
                continue;
            }
            $old = $staff->getOriginal($key);

            $data[$key] = [
                'old' => is_array($old) ? json_encode($old) : $old,
                'new' => $val
            ];
        }

        if (!empty($data)) {
            $data['id'] = $staff->getOriginal('id');

            // Insert activity log
            ActivityLog::insertActivityLog($action,null,$data);
        }
    }

    /**
     * Handle the Staff "deleted" event.
     *
     * @param  \App\Models\Staff  $staff
     * @return void
     */
    public function deleted(Staff $staff)
    {
        //
    }

    /**
     * Handle the Staff "restored" event.
     *
     * @param  \App\Models\Staff  $staff
     * @return void
     */
    public function restored(Staff $staff)
    {
        //
    }

    /**
     * Handle the Staff "force deleted" event.
     *
     * @param  \App\Models\Staff  $staff
     * @return void
     */
    public function forceDeleted(Staff $staff)
    {
        //
    }
}
