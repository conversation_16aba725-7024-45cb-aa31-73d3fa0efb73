<?php

namespace App\Observers;

use App\Models\User;
use App\Models\ActivityLog;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class UserObserver
{
    /**
     * Handle the User "created" event.
     *
     * @param  \App\Models\User  $user
     * @return void
     */
    public function created(User $user)
    {
        $action = 'user-register';

        $data = [];
        foreach ($user->getFillable() as $attribute) {
            $data[$attribute] = $user->$attribute;
        }

        // $creatorID = MODULE == "user" ? $user->id : null;

        // Insert activity log
        // ActivityLog::insertActivityLog($action,$user->id,$data,$creatorID);
    }

    /**
     * Handle the User "updated" event.
     *
     * @param  \App\Models\User  $user
     * @return void
     */
    public function updated(User $user)
    {
        $action = 'user-update';

        if (request()->has('act-log')) {
            $action = request()->input('act-log');
        }

        $data = [];

        foreach (Arr::only($user->getChanges(), $user->getFillable()) as $key => $val) {
            $data[$key] = [
                'old' => $user->getOriginal($key),
                'new' => $val
            ];
        }

        // Insert activity log
        ActivityLog::insertActivityLog($action, $user->id, $data);
    }

    /**
     * Handle the User "deleted" event.
     *
     * @param  \App\Models\User  $user
     * @return void
     */
    public function deleted(User $user)
    {
        //
    }

    /**
     * Handle the User "restored" event.
     *
     * @param  \App\Models\User  $user
     * @return void
     */
    public function restored(User $user)
    {
        //
    }

    /**
     * Handle the User "force deleted" event.
     *
     * @param  \App\Models\User  $user
     * @return void
     */
    public function forceDeleted(User $user)
    {
        //
    }
}
