<?php

namespace App\Observers;

use App\Models\Transfer;
use App\Models\ActivityLog;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class TransferObserver
{
    /**
     * Handle the Transfer "created" event.
     *
     * @param  \App\Models\Transfer  $transfer
     * @return void
     */
    public function created(Transfer $transfer)
    {
        $action = 'transfer-add';

        $data = [];

        foreach($transfer->getFillable() as $attribute) {
            $data[$attribute] = $transfer->$attribute;
        }

        // Insert activity log
        ActivityLog::insertActivityLog($action,$transfer->from_id,$data);
    }

    /**
     * Handle the Transfer "updated" event.
     *
     * @param  \App\Models\Transfer  $transfer
     * @return void
     */
    public function updated(Transfer $transfer)
    {
        $action = 'transfer-update';

        $data = [];

        foreach(Arr::only($transfer->getChanges(), $transfer->getFillable()) as $key => $val) {
            $data[$key] = [
                'old' => $transfer->getOriginal($key),
                'new' => $val
            ];
        }

        // Insert activity log
        ActivityLog::insertActivityLog($action,$transfer->from_id,$data);
    }

    /**
     * Handle the Transfer "deleted" event.
     *
     * @param  \App\Models\Transfer  $transfer
     * @return void
     */
    public function deleted(Transfer $transfer)
    {
        //
    }

    /**
     * Handle the Transfer "restored" event.
     *
     * @param  \App\Models\Transfer  $transfer
     * @return void
     */
    public function restored(Transfer $transfer)
    {
        //
    }

    /**
     * Handle the Transfer "force deleted" event.
     *
     * @param  \App\Models\Transfer  $transfer
     * @return void
     */
    public function forceDeleted(Transfer $transfer)
    {
        //
    }
}
