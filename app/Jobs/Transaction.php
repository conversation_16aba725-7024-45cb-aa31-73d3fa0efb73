<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use App\Models\CreditTransaction;
use App\Models\Transfer;
use App\Models\Convert;
use App\Models\User;
use App\Models\UserDevice;
use App\Models\CreditSetting;
use App\Traits;
use App\Traits\GenerateNumberTrait;
use App\Traits\DecimalTrait;

class Transaction implements ShouldQueue {

    use Dispatchable,
        InteractsWithQueue,
        Queueable,
        SerializesModels;

    protected $params = [];

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($params = []) {
        $this->params = $params;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        $returnData = $this->params;
        if(json_decode($returnData,true)){
            $returnData = json_decode($returnData, true);
        }

        switch ($returnData['transaction_type']) {
            case 'transfer':
                $data = $this->transferOut($returnData);
                break;

            case 'convert':
                $data = $this->convertOut($returnData);
                break;
            
            default:
                
                break;
        }
    }

    private function transferOut($params){
        $datetime = date("Y-m-d H:i:s");
        $internalId = $params['internal_id'] ?? null;
        $fromId = $params['from_id'] ?? null;
        $fromUserMemberId = $params['from_user_member_id'] ?? null;
        $toId = $params['to_id'] ?? null;
        $creditName = $params['credit_name'];
        $amount = $params['amount'] ?? 0;
        $belongId = $params['belong_id'] ?? 0;
        $batchId = $params['batch_id'] ?? 0;
        $charge = $params['charge'] ?? 0;
        $transferId = $params['transfer_id'] ?? 0;

        $totalAmount = $amount + $charge; 
        $fromBalance = 0;

        try {
            if(env('APP_ENV') == 'local') {
                $fromBalance = rand(0, 5000); // testing only
            } else {
                throw new \Exception('Tuning required.');
            }

            if($fromBalance < $totalAmount){
                throw new \Exception('Insufficient Balance.');
            }

            if(env('APP_ENV') != 'local') {
                $updateParams = [
                    "order_id" => GenerateNumberTrait::GenerateGroupId(),
                    "user_id" => $fromUserMemberId,
                    "transaction_type" => 'out',
                    "amount" => $totalAmount,
                    "remarks" => 'transfer-out'
                ];
                throw new \Exception('Tuning required.');
            }

            CreditTransaction::insertTransaction($internalId, $toId, $toId, $creditName, $amount, "transfer-in", $belongId, $belongId, null, $datetime, null, null, null, $fromId, null, true);
            Transfer::where('id', $transferId)->update(['status' => Transfer::$status['successful']]);

        } catch (\Exception $e) {
            $errorCode = $e->getCode();
            $errorMsg = $e->getMessage();

            // refund credit to from user
            CreditTransaction::insertTransaction($internalId, $fromId, $fromId, $creditName, $totalAmount, "transfer-refund", $belongId, $batchId, null, $datetime, $errorMsg, null, null, $toId, null, false);
            Transfer::where('id', $transferId)->update(['status' => Transfer::$status['refund']]);
        }

        return true;
    }

    private function convertOut($params){
        $datetime = date("Y-m-d H:i:s");
        $internalId = $params['internal_id'] ?? null;
        $userId = $params['user_id'] ?? null;
        $fromCredit = $params['from_credit'];
        $toCredit = $params['to_credit'];
        $fromCurrency = $params['from_currency'];
        $toCurrency = $params['to_currency'];
        $fromAmount = $params['from_amount'] ?? 0;
        $toAmount = $params['to_amount'] ?? 0;
        $belongId = $params['belong_id'] ?? 0;
        $batchId = $params['batch_id'] ?? 0;
        $rate = $params['rate'] ?? 0;
        $convertId = $params['convert_id'] ?? 0;
        $remark = $params['remark'] ?? null;

        $user = User::find($userId);
        $userMemberId = $user->member_id;
        
        try {

            $fromCreditId = $fromCredit['id'];
            $isFromGameCredit = CreditSetting::where('name', 'is-game-wallet')->where('credit_id', $fromCreditId)->where('value', 1)->first();
            if(!empty($isFromGameCredit)){
                if(env('APP_ENV') == 'local') {
                    $userGameBalance = rand(0, 5000); // testing only
                } else {
                    throw new \Exception('Tuning required.');
                }

                if($userGameBalance < $fromAmount){
                    throw new \Exception('Insufficient Balance.');
                }

                if(env('APP_ENV') != 'local') {
                    $updateParams = [
                        "order_id" => GenerateNumberTrait::GenerateGroupId(),
                        "user_id" => $userMemberId,
                        "transaction_type" => 'out',
                        "amount" => $fromAmount,
                        "remarks" => 'convert-out'
                    ];
                    throw new \Exception('Tuning required.');
                }
            }

            $transactionId = CreditTransaction::insertTransaction($internalId, $userId, $userId, $toCredit['name'], $toAmount, "convert-in", $belongId, $belongId, $remark, $datetime, null, null, null, null, null, true);
            $updateConvert = Convert::where('id', $convertId)->first() ?? null;
            $updateConvert->update(['status' => Convert::$status['successful']]);

            $userDeviceToken = UserDevice::where('user_id', $userId)->where('status', UserDevice::$status['active'])->first();
            if (isset($userDeviceToken)) {
                $users = User::find($userId);
                $lang = $users->lang ?? 'en';

                $templateType = 'convertSuccess';
                $template = Traits\FirebaseTrait::template($templateType, $lang);

                if (isset($templateType) && isset($template)) {

                    $template['body'] = str_replace(
                        ['{{from_iso}}','{{from_amount}}','{{to_iso}}','{{to_amount}}'], 
                        [$fromCurrency['iso'],DecimalTrait::setDecimal($fromAmount),$toCurrency['iso'],DecimalTrait::setDecimal($toAmount)],$template['body']);

                    Traits\FirebaseTrait::sendNotification($userDeviceToken->token, $template, [
                        'convertId' => $transactionId,
                        'credit_id'=> $fromCreditId,
                        'type' => $templateType,
                    ], $userDeviceToken->user_id, [
                        'from_iso'=> $fromCurrency['iso'],
                        'from_amount'=> $fromAmount,
                        'to_iso'=> $toCurrency['iso'],
                        'to_amount'=> $toAmount,
                    ]);
                }

            }

        } catch (\Exception $e) {
            $errorCode = $e->getCode();
            $errorMsg = $e->getMessage();

            // refund credit to from user
            CreditTransaction::insertTransaction($internalId, $userId, $userId, $fromCredit['name'], $fromAmount, "convert-refund", $belongId, $batchId, $remark, $datetime, $errorMsg, null, null, null, null, false);
            $updateConvert = Convert::where('id', $convertId)->first() ?? null;
            $updateConvert->update(['status' => Convert::$status['refund']]);
        }

        return true;
    }
}
