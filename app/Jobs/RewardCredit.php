<?php

namespace App\Jobs;

use App\Models\Credit;
use App\Models\CreditTransaction;
use App\Models\UserCard;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RewardCredit implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $params = [];

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($params = [])
    {
        $this->params = $params;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->givePromotion($this->params);
    }

    public function givePromotion($params)
    {
        $user_id = $params->user_id;
        $phone_no = $params->phone_no;

        $user_ids = UserCard::where('phone_no', $phone_no)->get()->pluck('user_id')->toArray();
        if (isset($user_ids) && count($user_ids) == 0) {
            return;
        }

        // Check First Time Register who haven't top up 10 before or admin had give 10 before
        $count = CreditTransaction::whereIn('user_id', $user_ids)
            ->whereNull('data')
            ->where('credit_id', 1000)
            ->where('amount', 10)
            ->count();

        if ($count == 0 && $count <= 0) {

            $exist = CreditTransaction::whereIn('user_id', $user_ids)->where('remark', 'First Time Register Bonus')->first();
            if (isset($exist)) return;

            CreditTransaction::adjustment([
                'user_id' => $user_id,
                'type' => 'in',
                'credit_id' => 1000,
                'amount' => 10,
                'remark' => 'First Time Register Bonus',
            ]);
        }
    }
}
