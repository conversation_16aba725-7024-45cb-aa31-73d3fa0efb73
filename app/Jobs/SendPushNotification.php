<?php

namespace App\Jobs;

use App\Traits\FirebaseTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Contracts\Queue\ShouldBeUnique;

class SendPushNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $deviceToken;
    public $params;
    public $data;
    public $userId;
    public $references;
    public $skipLog;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($deviceToken, $params, $data = null, $userId = null, $references = null, $skipLog = false)
    {
        $this->deviceToken = $deviceToken;
        $this->params = $params;
        $this->data = $data;
        $this->userId = $userId;
        $this->references = $references;
        $this->skipLog = $skipLog;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        FirebaseTrait::sendNotification(
            $this->deviceToken,
            $this->params,
            $this->data ?? [],
            $this->userId,
            $this->references ?? [],
            $this->skipLog
        );
    }
}
