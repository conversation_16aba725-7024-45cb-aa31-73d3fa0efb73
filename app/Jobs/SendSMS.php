<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

use App\Traits\Sms;

class SendSMS implements ShouldQueue {

    use Dispatchable,
        InteractsWithQueue,
        Queueable,
        SerializesModels;

    protected $params = [];

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($params = []) {
        $this->params = $params;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        $returnData = $this->params;
        if(json_decode($returnData,true)){
            $returnData = json_decode($returnData, true);
        }
        $data = $this->sendSMS($returnData);
    }

    private function sendSMS($params){

        $smsResult = (new Sms)->sendSms($params);

        return true;
    }
}
