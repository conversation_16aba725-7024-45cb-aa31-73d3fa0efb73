<?php

namespace App\Jobs;

use App\Events\LiveTransactionEvent;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;

class BroadcastLiveTransactionJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $transaction;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($transaction)
    {
        $this->transaction = $transaction;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $lastBroadcastTime = Cache::get('last_live_transaction_broadcast_time');
        $currentTime = now();

        if ($lastBroadcastTime && $currentTime->diffInSeconds($lastBroadcastTime) < 5) {
            return;
        }
        
        Cache::put('last_live_transaction_broadcast_time', $currentTime, 10);

        // Broadcast the transaction event
        event(new LiveTransactionEvent($this->transaction));
    }

    /**
     * The unique ID of the job.
     *
     * @return string
     */
    public function uniqueId()
    {
        return 'live_transaction_broadcast';
    }
}
