<?php

namespace App\Jobs;

use App\Models\CreditTransaction;
use App\Models\PaymentMethod;
use App\Models\User;
use App\Models\UserDevice;
use App\Models\Withdrawal;
use App\Services\PaymentGateway\FPayPayout;
use App\Services\PaymentGateway\OnePay;
use App\Traits;
use App\Traits\GenerateNumberTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class UpdateWithdrawal implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $params = [];

    public function __construct($params = [])
    {
        $this->params = $params;
    }

    public function handle()
    {
        $notification = [];
        $data = $this->params;

        DB::transaction(function () use ($data, &$notification) {
            $withdrawalIDAry = $data['withdrawal_id_ary'];
            $action = $data['action'];
            $remark = $data['remark'] ?? null;
            $dateTime = $data['datetime'] ?? date('Y-m-d H:i:s');
            $updaterId = auth()->user()->id ?? 0;

            Withdrawal::with('credit', 'userBank')->whereIn('id', $withdrawalIDAry)->get()->map(function ($withdrawalData) use ($data, $action, $remark, $dateTime, $updaterId, &$notification) {
                $withdrawalStatus = isset($withdrawalData->status) ? array_search($withdrawalData->status, Withdrawal::$status) : null;

                switch ($action) {
                    case 'approved':
                        // TODO: payout services
                        // $payoutSwitch = SystemSetting::where('name', 'withdrawal.payout.switch')->pluck('value', 'name')->toArray() ?? null;
                        // if (!isset($payoutSwitch['withdrawal.payout.switch'])) {
                        //     abort(400, 'Payout service is not enabled');
                        // }

                        // if ($payoutSwitch['withdrawal.payout.switch'] == 1) {
                        if ($data['is_payout'] == true) {
                            // Check Payment Method Status
                            $payment_method = PaymentMethod::where('status', true)->where('type', PaymentMethod::$type['payout'])->first();

                            switch ($payment_method->value) {
                                case 'onepay-payout':
                                    $onepay_payout = resolve(OnePay::class);
                                    $onepay_payout->generateWithdrawOrder(
                                        $withdrawalData->receivable_amount,
                                        $withdrawalData->credit->code,
                                        $withdrawalData->userBank->bank->ref_onepay_wd_bank_id,
                                        $withdrawalData->userBank->account_holder,
                                        $withdrawalData->userBank->account_no,
                                        $withdrawalData->serial_number
                                    );
                                    break;
                                case 'fpay-payout':
                                    $fpay_payout = resolve(FPayPayout::class);
                                    $fpay_payout->generateWithdrawOrder(
                                        $withdrawalData->receivable_amount,
                                        $withdrawalData->credit->code,
                                        $withdrawalData->userBank->bank->ref_bank_id,
                                        $withdrawalData->userBank->account_holder,
                                        $withdrawalData->userBank->account_no,
                                        $withdrawalData->serial_number
                                    );
                                    break;
                                // case 'rapidpay-payout':
                                //     $fpay_payout = resolve(RapidPay::class);
                                //     $fpay_payout->generateWithdrawOrder(
                                //         $withdrawalData->receivable_amount,
                                //         $withdrawalData->userBank->bank->ref_rapidpay_bank_id,
                                //         $withdrawalData->userBank->account_holder,
                                //         $withdrawalData->userBank->account_no,
                                //         $withdrawalData->serial_number
                                //     );
                                //     break;
                                default:
                                    abort(400, 'Payout service is not enabled');
                                    break;
                            }
                        }

                        $withdrawalData->update([
                            'remark' => $remark,
                            'status' => Withdrawal::$status[$data['action']],
                            'approved_at' => $dateTime,
                            'approved_by' => $updaterId,
                            'updater_id' => $updaterId,
                        ]);
                        break;

                    case 'pending':
                    case 'waiting-approval':
                        $processingWithdrawal = [
                            'remark' => $remark,
                            'status' => Withdrawal::$status[$action],
                            'updater_id' => $updaterId,
                        ];

                        $withdrawalData->update($processingWithdrawal);
                        break;

                    case 'rejected':
                    case 'cancel':
                        $internalID = User::select('id')->where('username', 'telexTransfer')->where('user_type', User::$userType['internal-account'])->first()->id ?? null;
                        if (empty($internalID)) {
                            abort(400, 'Invalid Internal Account.');
                        }

                        $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;
                        $creditType = $withdrawalData->credit_type;

                        $refundRes = CreditTransaction::with(['creditInfo'])->selectRaw('SUM(amount) AS refundAmt, ANY_VALUE(credit_id) AS credit_id, ANY_VALUE(user_id) AS user_id')
                            ->whereIn('subject_type', Arr::only(config('subject'), ['withdrawal-out', 'withdrawal-charge']))
                            ->where('belong_id', $withdrawalData->belong_id)->groupBy('credit_id')->get()->map(function ($q) {
                                return [
                                    'refundAmt' => $q->refundAmt,
                                    'credit_type' => $q->creditInfo->name,
                                    'user_id' => $q->user_id,
                                ];
                            });

                        foreach ($refundRes as $refundRow) {
                            $creditType = $refundRow['credit_type'];
                            $refundAmt = $refundRow['refundAmt'];
                            $userID = $refundRow['user_id'];

                            if (empty($creditType) || empty($refundAmt) || empty($userID)) {
                                abort(400, 'Failed to update status.');
                            }
                            if ($refundAmt > 0) {
                                CreditTransaction::insertTransaction($internalID, $userID, $userID, $creditType, $refundAmt, 'withdrawal-refund', $belongId, $withdrawalData->belong_id, null, $dateTime, null, null, null, null, null);
                            }
                        }

                        $cancelWithdrawal = [
                            'remark' => $remark,
                            'status' => Withdrawal::$status[$action],
                            'updater_id' => $updaterId,
                        ];

                        $withdrawalData->update($cancelWithdrawal);
                        break;
                }

                if (in_array($action, ['pending', 'approved', 'rejected'])) {
                    $userID = $withdrawalData->user_id;
                    $userDeviceToken = UserDevice::where('user_id', $userID)->where('status', UserDevice::$status['active'])->first();
                    if (isset($userDeviceToken)) {
                        $users = User::find($userID);
                        $lang = $users->lang ?? 'en';
                        $templateType = null;
                        $template = null;
                        switch ($action) {
                            case 'pending':
                                $templateType = 'withdrawalProcessing';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;

                            case 'approved':
                                $templateType = 'withdrawalApproved';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;

                            case 'rejected':
                                $templateType = 'withdrawalRejected';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                        }
                        $template['body'] = str_replace('{{serial_no}}', $withdrawalData->serial_number, $template['body']);
                        $notification[] = [
                            'user_id' => $userDeviceToken->user_id,
                            'token' => $userDeviceToken->token,
                            'template' => $template,
                            'data' => ['withdrawal' => $withdrawalData->id, 'type' => $templateType, 'credit_id' => $withdrawalData->credit->id ?? ''],
                            'reference_data' => ['serial_no' => $withdrawalData->serial_number],
                        ];
                    }
                }
            });
        });
        foreach ($notification as $noti) {
            Traits\FirebaseTrait::sendNotification($noti['token'], $noti['template'], $noti['data'], $noti['user_id'], $noti['reference_data']);
        }

        return true;
    }
}
