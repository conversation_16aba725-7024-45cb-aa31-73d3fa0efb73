<?php

namespace App\Jobs;

use App\Services\GameProvider\JK;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RandomLogoutWWJ implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $account;
    protected $agentId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($account, $agentId)
    {
        $this->account = $account;
        $this->agentId = $agentId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $jk = resolve(JK::class, ['']);
        try {
            $withdrawal = $jk->getWithdraw($this->account, $this->agentId);
            return true;
        } catch (\Exception $e) {
            $this->fail($this->account . ' - Withdraw failed.');
        }
    }
}
