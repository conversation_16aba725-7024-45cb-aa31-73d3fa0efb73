<?php

namespace App\Jobs;

use App\Traits\DecimalTrait;
use App\Models;
use App\Models\AccountBalance;
use App\Models\Credit;
use App\Traits;
use App\Models\ExTransfer;
use App\Models\User;
use App\Services\GameProvider\CQ9;
use App\Services\GameProvider\MT;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

// use function PHPUnit\Framework\isJson;

class ProcessMerchant implements ShouldQueue
{

    use Dispatchable,
        InteractsWithQueue,
        Queueable,
        SerializesModels,
        DecimalTrait;

    protected $params = [];

    public $timeout = 0;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($params = [])
    {
        $this->params = $params;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $returnData = $this->params;

        if (str($returnData)->isJson()) {
            $returnData = json_decode($returnData, true);
        }
        if (isset($returnData['product_id'])) {
            $data = $this->handleThirdParty($returnData);
        } else {
            $data = $this->handleCallback($returnData);
        }
    }

    private function handleCallback($params)
    {
        $result = Traits\SonicTrait::post($params, $params['curl_type']);
        switch ($params['curl_type']) {
            case 'change_password':
                break;
            case 'check_game_credit':
                break;
            case 'get_all_card':
                break;
            case 'get_store':
                break;
            case 'validate_user':
                break;
            case 'deposit':
                $failed = false;
                if (isset($result) && ($result['status'] == true)) {
                    $update = ['status' => Models\ExTransfer::$status['confirmed']];

                    // update balance
                    $updateUserCard = [
                        'member_balance' => $result['data'][0]['latest_member_balance'] ?? 0,
                    ];
                    Models\UserCard::find($params['ucId'])->update($updateUserCard);

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    AccountBalance::deductBalance($exTransfer['user_id'], $params['amount']);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], "res_data" => $result["msg"] ?? null];
                    $exTransfer = Models\ExTransfer::with(['creditType', 'user'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    if (($result['refund'] ?? 0) == 0) {
                        $eventDescription = "Cash In Failed\n";
                        $eventDescription .= ($exTransfer->user->username ?? "-") . "\n";
                        $eventDescription .= DecimalTrait::setDecimal($exTransfer->amount ?? 0) . "\n";
                        $eventDescription .= ($exTransfer->transaction_id ?? "-") . "\n";
                        $eventDescription .= (date('Y-m-d H:i:s', strtotime($exTransfer->created_at)) ?? "-") . "\n";
                        Traits\TelegramTrait::sendTelegram(null, $eventDescription);
                    } else {
                        // To change status for refunded
                        $update = ['status' => Models\ExTransfer::$status['refunded'], "res_data" => $result["msg"] ?? null];
                        $internalId = Models\User::where('username', 'exTransfer')->where('user_type', Models\User::$userType['internal-account'])->first()->id;

                        Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $exTransfer['amount'], "ex-transfer-refund", $exTransfer['belong_id'], $exTransfer['belong_id'], null, date("Y-m-d H:i:s"), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                    }
                    $failed = true;
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                if ($failed) {
                    abort(400, json_encode($result));
                }
                break;
            case 'withdraw':
                $failed = false;
                if (isset($result) && $result['status'] == true) {
                    $update = ['status' => Models\ExTransfer::$status['confirmed']];

                    // update balance
                    $updateUserCard = [
                        'member_balance' => $result['data'][0]['latest_member_balance'] ?? 0,
                    ];
                    Models\UserCard::find($params['ucId'])->update($updateUserCard);

                    // give money
                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', Models\User::$userType['internal-account'])->first()->id;
                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $params['amount'], "ex-transfer-in", $exTransfer['belong_id'], $exTransfer['belong_id'], null, date("Y-m-d H:i:s"), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);

                    AccountBalance::addBalance($exTransfer['user_id'], $params['amount']);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], "res_data" => $result["msg"] ?? null];
                    $failed = true;
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                if ($failed) {
                    abort(400, json_encode($result));
                }
                break;
        }
        return true;
    }

    private function handleThirdParty($params)
    {
        $product = Models\Product::find($params['product_id']);
        // switch ($product['name']) {
        //     case 'TK8':
        //         // $result = Traits\OCTK8Trait::postOC($params, $params['curl_type']);
        //         break;
        // }

        switch ($product['name']) {
            case 'TK8':
                $this->TK8($params, $product);
                break;

            case 'MT':
                $this->MT($params, $product);
                break;

            case 'CQ9':
                $this->CQ9($params, $product);
                break;
        }

        return true;
    }

    private function TK8($params, $product)
    {
        $result = Traits\OCTK8Trait::postOC($params, $params['curl_type']);

        switch ($params['curl_type']) {
            case 'register':
                if (isset($result) && $result['status'] == true) {
                    $update = ['status' => Models\UserProduct::$status['success']];
                    if ($product['name'] == 'TK8' || $product['name'] == 'FMT') $update['member_id'] = $params['account'];
                } else {
                    $update = ['status' => Models\UserProduct::$status['failed']];
                }
                Models\UserProduct::where([
                    'member_id' => $params['account'],
                    'product_id' => $params['product_id'],
                    'status' => Models\UserProduct::$status['pending'],
                ])->update($update);
                break;

            case 'callback/deposit':
                if (isset($result) && ($result['status'] == true)) {
                    $dateTime = date("Y-m-d H:i:s");
                    $updateDepositParams = [
                        'id' => $params['deposit_id'] ?? 0,
                        'status' => 'approved',
                        'datetime' => $dateTime
                    ];
                    $updateDepositRes = Models\Deposit::updateDeposit($updateDepositParams);

                    $update = ['status' => Models\ExTransfer::$status['confirmed']];
                    if ($product['name'] == 'TK8') {
                        if (isset($result['data']['transaction_id'])) {
                            $update['ref_tx_id'] = $result['data']['transaction_id'];
                        }
                        // $update['reference'] = $result['data']['reference'];
                    }

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'in', $exTransfer['amount'], $result['data']['current_balance'] ?? null);

                    $datetime = date('Y-m-d H:i:s');
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', Models\User::$userType['internal-account'])->first()->id;
                    Models\CreditTransaction::insertTransaction($exTransfer['user_id'], $internalId, $exTransfer['user_id'], $exTransfer['creditType']['type'], $exTransfer['amount'], "ex-transfer-out", $exTransfer['belong_id'], $exTransfer['belong_id'], null, $datetime, $exTransfer['transaction_id'], null, null, $exTransfer['id'], null, false);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], "res_data" => $result["msg"] ?? null];
                    $exTransfer = Models\ExTransfer::with(['creditType', 'user'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    if (($result['refund'] ?? 0) == 0) {
                        $eventDescription = "Cash In Failed\n";
                        $eventDescription .= ($exTransfer->user->username ?? "-") . "\n";
                        $eventDescription .= DecimalTrait::setDecimal($exTransfer->amount ?? 0) . "\n";
                        $eventDescription .= ($exTransfer->transaction_id ?? "-") . "\n";
                        $eventDescription .= (date('Y-m-d H:i:s', strtotime($exTransfer->created_at)) ?? "-") . "\n";
                        Traits\TelegramTrait::sendTelegram(null, $eventDescription);
                    }
                    // else{
                    //     // To change status for refunded
                    //     $update = ['status' => ExTransfer::$status['refunded'], "res_data" => $result["msg"] ?? null];
                    //     $internalId = User::where('username','exTransfer')->where('user_type',User::$userType['internal-account'])->first()->id;

                    //     CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $exTransfer['amount'], "ex-transfer-refund", $exTransfer['belong_id'], $exTransfer['belong_id'], null, date("Y-m-d H:i:s"), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                    // }
                }
                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;

            case 'deposit':
                if (isset($result) && ($result['status'] == true)) {
                    $update = ['status' => Models\ExTransfer::$status['confirmed']];
                    if ($product['name'] == 'TK8') {
                        $update['ref_tx_id'] = $result['data']['transaction_id'];
                        $update['reference'] = $result['data']['reference'];
                    }

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'in', $exTransfer['amount'], $result['data']['current_balance'] ?? null);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], "res_data" => $result["msg"] ?? null];
                    $exTransfer = Models\ExTransfer::with(['creditType', 'user'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    if (($result['refund'] ?? 0) == 0) {
                        $eventDescription = "Cash In Failed\n";
                        $eventDescription .= ($exTransfer->user->username ?? "-") . "\n";
                        $eventDescription .= DecimalTrait::setDecimal($exTransfer->amount ?? 0) . "\n";
                        $eventDescription .= ($exTransfer->transaction_id ?? "-") . "\n";
                        $eventDescription .= (date('Y-m-d H:i:s', strtotime($exTransfer->created_at)) ?? "-") . "\n";
                        Traits\TelegramTrait::sendTelegram(null, $eventDescription);
                    } else {
                        // To change status for refunded
                        $update = ['status' => Models\ExTransfer::$status['refunded'], "res_data" => $result["msg"] ?? null];
                        $internalId = Models\User::where('username', 'exTransfer')->where('user_type', Models\User::$userType['internal-account'])->first()->id;

                        Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $exTransfer['amount'], "ex-transfer-refund", $exTransfer['belong_id'], $exTransfer['belong_id'], null, date("Y-m-d H:i:s"), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                    }
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;

            case 'withdraw':
                if (isset($result) && $result['status'] == true) {
                    $update = ['amount' => $result['data']['witdraw_amount'] ?? 0, 'receivable_amount' => $result['data']['witdraw_amount'] ?? 0, 'status' => ExTransfer::$status['confirmed']];
                    if ($product['name'] == 'TK8') {
                        $update['ref_tx_id'] = $result['data']['transaction_id'];
                        $update['reference'] = $result['data']['reference'];
                    }

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;
                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $result['data']['witdraw_amount'], "ex-transfer-in", $exTransfer['belong_id'], $exTransfer['belong_id'], null, date("Y-m-d H:i:s"), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);

                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'out', $result['data']['witdraw_amount'], $result['data']['current_balance'] ?? null);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], "res_data" => $result["msg"] ?? null];
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;

                //     // only FMT will call this
                // case 'balance-promoid':
                //     if (isset($result) && $result['status'] == true && isset($result['data'])) {
                //         $fullProductData = Models\Product::find($params['party_product_id']);
                //         $credit = Models\Credit::find($params['credit_id']);
                //         $walletData = [
                //             "credit_id" => $credit->id ?? null,
                //             "credit_name" => $credit->name,
                //             "credit_type" => $credit->type,
                //         ];
                //         $transferParams['deposit_id'] = $params['deposit_id'];
                //         $transferParams['product_data'] = $fullProductData;
                //         $transferParams['wallet_data'] = $walletData;
                //         $transferParams['exMemberId'] = $params['partyAccount']; // call to tk8 / tk8th, use their member_id
                //         $transferParams['type'] = '1'; // 1 = in, 2 = out
                //         $transferParams['amount'] = $params['amount'];
                //         $transferParams['user_id'] = $params['user_id'];
                //         $transferParams['credit_id'] = $credit->id ?? null;
                //         $transferParams['product_id'] = $fullProductData->id ?? null;
                //         $transferParams['promo_id'] = $result['data']['promo_id'] ?? null;
                //         $transferParams['auto_cash_in'] = true;
                //         $transferParams['order_id'] = $params['order_id'] ?? null;
                //         $transferParams['wallet_type'] = $params['wallet_type'] ?? Models\UserProduct::$walletType['Main'];
                //         Models\ExTransfer::add($transferParams);
                //     }
                //     break;
        }
    }

    private function MT($params, $product)
    {
        switch ($params['curl_type']) {
            case 'register':
                $result = resolve(MT::class)->createUser($params['user_id']);
                break;
            case 'deposit':
                $result = resolve(MT::class)->deposit($params['user_id'], $params['amount']);
                if (isset($result) && ($result['status'] == true)) {
                    $update = ['status' => Models\ExTransfer::$status['confirmed']];
                    // if ($product['name'] == 'MT') {
                    //     $update['ref_tx_id'] = $result['data']['transaction_id'] ?? null;
                    //     $update['reference'] = $result['data']['reference'] ?? null;
                    // }

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'in', $exTransfer['amount'], $result['data']['amount'] ?? null);

                    // Models\AccountBalance::updateBalance($params['user_id'], $result['data']['amount']);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], "res_data" => $result["msg"] ?? null];
                    $exTransfer = Models\ExTransfer::with(['creditType', 'user'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    if (($result['refund'] ?? 0) == 0) {
                        $eventDescription = "Cash In Failed\n";
                        $eventDescription .= ($exTransfer->user->username ?? "-") . "\n";
                        $eventDescription .= DecimalTrait::setDecimal($exTransfer->amount ?? 0) . "\n";
                        $eventDescription .= ($exTransfer->transaction_id ?? "-") . "\n";
                        $eventDescription .= (date('Y-m-d H:i:s', strtotime($exTransfer->created_at)) ?? "-") . "\n";
                        Traits\TelegramTrait::sendTelegram(null, $eventDescription);
                    } else {
                        // To change status for refunded
                        $update = ['status' => Models\ExTransfer::$status['refunded'], "res_data" => $result["msg"] ?? null];
                        $internalId = Models\User::where('username', 'exTransfer')->where('user_type', Models\User::$userType['internal-account'])->first()->id;

                        Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $exTransfer['amount'], "ex-transfer-refund", $exTransfer['belong_id'], $exTransfer['belong_id'], null, date("Y-m-d H:i:s"), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                    }
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;
            case 'withdraw':
                $result = resolve(MT::class)->withdraw($params['user_id']);
                if (isset($result) && $result['status'] == true) {
                    $update = ['amount' => $result['data']['amount'] ?? 0, 'receivable_amount' => $result['data']['amount'] ?? 0, 'status' => ExTransfer::$status['confirmed']];
                    // if ($product['name'] == 'MT') {
                    //     $update['ref_tx_id'] = $result['data']['transaction_id'];
                    //     $update['reference'] = $result['data']['reference'];
                    // }

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;
                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $result['data']['amount'], "ex-transfer-in", $exTransfer['belong_id'], $exTransfer['belong_id'], null, date("Y-m-d H:i:s"), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);

                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'out', $result['data']['amount'], $result['data']['amount'] ?? null);

                    Models\AccountBalance::updateBalance($params['user_id'], $result['data']['amount']);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], "res_data" => $result["msg"] ?? null];
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;
        }
    }

    private function CQ9($params, $product)
    {
        $user = User::find($params['user_id']);

        switch ($params['curl_type']) {
            case 'register':
                $result = resolve(CQ9::class)->createUser($user);
                break;
            case 'deposit':
                $result = resolve(CQ9::class)->deposit($user, $params['amount'], $params['remark']);
                if (isset($result) && ($result['status'] == true)) {
                    $update = ['status' => Models\ExTransfer::$status['confirmed']];

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'in', $exTransfer['amount'], $result['data']['amount'] ?? null);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], "res_data" => $result["msg"] ?? null];
                    $exTransfer = Models\ExTransfer::with(['creditType', 'user'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    if (($result['refund'] ?? 0) == 0) {
                        $eventDescription = "Cash In Failed\n";
                        $eventDescription .= ($exTransfer->user->username ?? "-") . "\n";
                        $eventDescription .= DecimalTrait::setDecimal($exTransfer->amount ?? 0) . "\n";
                        $eventDescription .= ($exTransfer->transaction_id ?? "-") . "\n";
                        $eventDescription .= (date('Y-m-d H:i:s', strtotime($exTransfer->created_at)) ?? "-") . "\n";
                        Traits\TelegramTrait::sendTelegram(null, $eventDescription);
                    } else {
                        // To change status for refunded
                        $update = ['status' => Models\ExTransfer::$status['refunded'], "res_data" => $result["msg"] ?? null];
                        $internalId = Models\User::where('username', 'exTransfer')->where('user_type', Models\User::$userType['internal-account'])->first()->id;

                        Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $exTransfer['amount'], "ex-transfer-refund", $exTransfer['belong_id'], $exTransfer['belong_id'], null, date("Y-m-d H:i:s"), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                    }
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;
            case 'withdraw':
                $result = resolve(CQ9::class)->withdraw($user, $params['remark']);
                if (isset($result) && $result['status'] == true) {
                    $update = ['amount' => $result['data']['amount'] ?? 0, 'receivable_amount' => $result['data']['amount'] ?? 0, 'status' => ExTransfer::$status['confirmed']];

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;
                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $result['data']['amount'], "ex-transfer-in", $exTransfer['belong_id'], $exTransfer['belong_id'], null, date("Y-m-d H:i:s"), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);

                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'out', $result['data']['amount'], $result['data']['amount'] ?? null);

                    Models\AccountBalance::updateBalance($params['user_id'], $result['data']['amount']);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], "res_data" => $result["msg"] ?? null];
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;
        }
    }
}
