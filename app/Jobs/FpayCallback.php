<?php

namespace App\Jobs;

use App\Models\Order;
use App\Models\OrderCryptoAddress;
use App\Models\OrderPaymentDetail;
use App\Traits\CacheTrait;
use App\Traits\CurlTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

use App\Traits\Sms;
use Illuminate\Support\Facades\DB;

use function PHPUnit\Framework\isJson;

class FpayCallback implements ShouldQueue {

    use Dispatchable,
        InteractsWithQueue,
        Queueable,
        SerializesModels,
        CurlTrait,
        CacheTrait;

    protected $params = [];

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($params = []) {
        $this->params = $params;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        $returnData = $this->params;

        if(isJson($returnData)){
            $returnData = json_decode($returnData, true);
        }
        $data = $this->handleCallback($returnData);
    }

    private function handleCallback($params){
        $data = $params ?? [];
        DB::transaction(function () use ($data) {
            // Prefix variable
            $referenceNumb = $data['order_id'] ?? Null;
            $orderStatus = $data['order_status'] ?? Null;
            $token = $data['token'] ?? Null;
            $cacheName = 'order-status-'.md5($referenceNumb);

            // Get Order Detail
            $orderDetail = OrderPaymentDetail::where('payment_number',$referenceNumb)
                ->where('status',OrderPaymentDetail::$status['processing'])
                ->select('order_id','id')->first();

            if(empty($orderDetail)){
                throw new \Exception('Invalid Order Detail.');
            }

            // Check Token
            if(md5(env('FPAY_SECRET_KEY').$referenceNumb) != $token){
                throw new \Exception('Invalid Token.');
            }

            switch (strtolower($orderStatus)) {
                case 'completed':
                    // Get Order Data
                    $orderRes = Order::where('id',$orderDetail->order_id)->where('status',Order::$status['pending'])
                                ->select('address','to_amount')->first();
                    if(empty($orderRes)){
                        throw new \Exception('Invalid Order.');
                    }

                    $endpoint = env('TRONUMX_API_URL').'/wallet/transfer';
                    $requestData = [
                        'method' => 'POST',
                        'endpoint' => $endpoint,
                        'header' => [
                            'Connection: close',
                            'Api-Key: '.env("TRONUMX_API_KEY"),
                        ],
                        'body' => [
                            'coin' => 'USDT',
                            'to_address' => (string) $orderRes->address,
                            'amount' => $orderRes->to_amount,
                        ],
                    ];

                    $res = CurlTrait::doCurl($requestData);
                    $result = json_decode($res,true);
                    $tx_id = Null;
                    if(isset($result['success']) && ($result['success'] == true)){
                        $updateOd = ['status'=>Order::$status['processing'],'updated_at' => now()];
                        $tx_id = $result['data']['txid'];
                        $cryptoStatus = OrderCryptoAddress::$status['pending'];
                    }else{
                        $updateOd = ['status'=>Order::$status['failed'],'updated_at' => now()];
                        $cryptoStatus = OrderCryptoAddress::$status['failed'];
                    }

                    // Insert Crypto Address Detail
                    $insertCryptoDetails = [
                        "order_id" => $orderDetail->order_id,
                        "address" => $orderRes->address,
                        "tx_id" => $tx_id??Null,
                        "amount" => $orderRes->to_amount,
                        "status" => $cryptoStatus??Null,
                        "request_data" => json_encode($requestData),
                        "response_data" => json_encode($result),
                    ];
                    OrderCryptoAddress::create($insertCryptoDetails);

                    // Update Order Detail to Completed
                    $updateOdDetail = ['status'=>OrderPaymentDetail::$status['completed'],'response_data'=>json_encode($data),'updated_at' => now()];
                    $updateOdDetailRes = OrderPaymentDetail::where('id',$orderDetail->id)->update($updateOdDetail);

                    // Update Order status
                    $updateOdRes = Order::where('id',$orderDetail->order_id)->where('status',Order::$status['pending'])
                            ->update($updateOd);
                    break;
                
                default:
                    $updateOdRes = Order::where('id',$orderDetail->order_id)->where('status',Order::$status['pending'])
                            ->update(['status'=>Order::$status['failed'],'updated_at' => now()]);

                    $updateOdDetailRes = OrderPaymentDetail::where('id',$orderDetail->id)->update(['status'=>OrderPaymentDetail::$status['failed'],'response_data'=>json_encode($data),'updated_at' => now()]);
                    break;
            }

            CacheTrait::clearCacheWithoutUid($cacheName);
        });

        return true;
    }
}
