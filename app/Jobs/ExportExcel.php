<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models;
use App\Traits;
use App\Models\ExportReport;
use App\Services;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Str;
use Rap2hpoutre\FastExcel\FastExcel;
use Laravel\Telescope\Telescope;
use OpenSpout\Common\Entity\Style\Style;

class ExportExcel implements ShouldQueue
{

    use Dispatchable,
        InteractsWithQueue,
        Queueable,
        SerializesModels;


    protected $params = [];

    public $timeout = 0;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($params = [])
    {
        $this->params = $params;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $returnData = $this->params;

        // Make sure returnData is an array
        if (is_string($returnData) && Str::isJson($returnData)) {
            $returnData = json_decode($returnData, true);
        }

        $this->exportExcel($returnData, $this->params['file_name']);
    }

    private function exportExcel($params, $file_name)
    {
        unset($params['see_all']);
        unset($params['export']);
        unset($params['bank_export']);
        if (isset($params['module'])) {
            if (!defined('MODULE')) define('MODULE', $params['module']);
            unset($params['module']);
        }
        $exportFileName = str_replace(['-', '/'], '_', $file_name) . '_' . date('YmdHis');
        $export = ExportReport::create([
            "name" => $file_name,
            "file_name" => $exportFileName,
            "model" => $params['model'] ?? null,
            "function" => $params['function'] ?? null,
            "list_key" => $params['list_key'] ?? "list",
            "export_data" => json_encode(($params['export_data'] ?? [])),
            "completed" => ExportReport::$completed['processing'],
            "creator_type" => $params['creator_type'] ?? null,
            "creator_id" => $params['creator_id'] ?? null,
        ]);

        $rowsStyle = (new Style())->setCellAlignment('left');
        $params['func'] = class_basename($params['model']) . '_' . $params['function'];
        try {
            if (in_array($params['func'], ['UserRank_getUserRankList','UserSales_getSalesSummary','TelexTransfer_getTelexTransferList', 'User_walletBalanceReport']) && (!isset($params['export_data']) || empty($params['export_data']))) { //eg: Order_get
                (new FastExcel(self::reportGenerator($params, true)))->withoutHeaders()->rowsStyle($rowsStyle)->export(storage_path('app') . '/public/' . $exportFileName . '.xlsx', function ($q) {
                    return $q;
                });
            } else {
                (new FastExcel(self::reportGenerator($params)))->rowsStyle($rowsStyle)->export(storage_path('app') . '/public/' . $exportFileName . '.xlsx', function ($q) {
                    return $q;
                });
            }
        } catch (\Exception $exception) {
            ExportReport::find($export->id)->update(['completed' => ExportReport::$completed['failed'], 'failed_message' => $exception->getMessage()]);
            return true;
        }

        ExportReport::find($export->id)->update(['completed' => ExportReport::$completed['completed']]);

        return true;
    }

    function reportGenerator($params, $specialHandle = false)
    {
        Telescope::stopRecording();
        $a = 1;
        $functionName = $params['function'];
        $object = new $params['model'];
        $listKey = $params['list_key'] ?? 'list';
        $modelFunction = $params['func'];
        unset($params['func']);
        while (true) {
            $params['limit'] = 1000;
            $currentPage = $a;
            Paginator::currentPageResolver(function () use ($currentPage) {
                return $currentPage;
            });

            $params['page'] = $currentPage;
            $result = $object->$functionName($params);
            if (!isset($result) || empty($result)) {
                break;
            }

            $list = json_decode(json_encode($result), true) ?? [];
            if (!isset($list[$listKey]) || empty($list[$listKey])) {
                break;
            }

            if (isset($params['export_data']) && !empty($params['export_data'])) $exportColumn = $params['export_data']['data'];
            echo " Processing Page - " . $a . " with Limit - " . $params['limit'] . "\n";
            echo round(memory_get_usage() / 1048576, 2) . '' . ' MB' . "\n";

            if ($specialHandle) {
                switch ($modelFunction) {
                    case 'UserRank_getUserRankList':
                        $header = [
                            "member_id" => "Member ID",
                            "name" => "Name",
                            "phone_no" => "Phone No",
                            "vip_rank_display" => "Current Rank",
                            "sp_rank_display" => "Special Rank",
                            "country" => "Country",
                            "last_update_at" => "Updated At",
                        ];
                        foreach ($header as $listValue) {
                            $headers[] = $listValue;
                        }
                        if ($currentPage == 1) yield $headers;
                        foreach ($list[$listKey] as $listValue) {
                            $generateReport = [];
                            foreach ($header as $key => $value) {
                                if (in_array($key, ['current', 'admin', 'system'])) {
                                    $generateReport[] = $listValue['goldmine-bonus-percentage'][$key] ?: '-';
                                } else {
                                    $generateReport[] = $listValue[$key] ?: '-';
                                }
                            }
                            yield $generateReport;
                        }
                        break;

                    case 'UserSales_getSalesSummary':
                        $header = [
                            "date" => "Date",
                            "register" => "Total Registration",
                            "telex_transfer" => "Total Sales (TTransfer)",
                            "transfer" => "M2M Transfer",
                            "withdrawal" => "Total Withdrawal",
                            "deposit" => [
                                "display" => "Deposit",
                                "data" => [
                                    'manual-bank' => 'Manual Deposit (RM)',
                                    'online-bank' => 'Fpay (RM)',
                                    'ewallet' => 'E-Wallet (RM)',
                                    'total' => 'Total Deposit (RM)',
                                ],
                            ],
                        ];

                        $generateHeader = self::twoLayerHeader($header);
                        foreach ($generateHeader as $generateHead) {
                            if ($currentPage == 1) yield $generateHead;
                        }
                        foreach($list[$listKey] as $listValue) {
                            $generateReport = [];
                            foreach($header as $dataKey => $dataValue) {
                                if (is_array($dataValue)) {
                                    foreach($dataValue['data'] as $key => $display) {
                                        if(isset($listValue[$dataKey])){
                                            $generateReport[] = $listValue[$dataKey][$key];
                                        } else {
                                            $generateReport[] = $listValue[$key];
                                        }
                                    }
                                } else {
                                    $generateReport[] = $listValue[$dataKey];
                                }
                            }
                            yield $generateReport;
                        }
                        break;

                    case 'TelexTransfer_getTelexTransferList':
                        $header = [
                            "no" => "No",
                            "transfer_category" => "Transfer Category",
                            "bank_name" => "Bank Name",
                            "bank_account_number" => "To Bank No.",
                            "recipient" => [
                                "display" => "Recipient Information (For Transfer to Other Bank SKN / Clearing)",
                                "data" => [
                                    'bank_account_name' => 'Recipient Name',
                                    "branch_code" => "Branch Code",
                                    'citizen' => 'Citizenship',
                                    'individual' => 'Recipient Type',
                                ],
                            ],
                            'received_amount' => 'Amount',
                        ];

                        $generateHeader = self::twoLayerHeader($header);
                        foreach ($generateHeader as $generateHead) {
                            if ($currentPage == 1) yield $generateHead;
                        }
                        $count = 0;
                        foreach($list[$listKey] as $listValue) {
                            $generateReport = [];
                            foreach($header as $dataKey => $dataValue) {
                                if (is_array($dataValue)) {
                                    foreach($dataValue['data'] as $key => $display) {
                                        if(isset($listValue[$dataKey])){
                                            $generateReport[] = $listValue[$dataKey][$key];
                                        } else {
                                            switch ($key) {
                                                case 'individual':
                                                    $generateReport[] = "Individual";
                                                    break;

                                                case 'citizen':
                                                    switch ($listValue['currency_iso']) {
                                                        case 'IDR':
                                                            $generateReport[] = "Indonesian";
                                                            break;

                                                        case 'SGD':
                                                            $generateReport[] = "Singaporean";
                                                            break;

                                                        case 'USD':
                                                            $generateReport[] = "American";
                                                            break;

                                                        case 'VND':
                                                            $generateReport[] = "Vietnamese";
                                                            break;

                                                        case 'INR':
                                                            $generateReport[] = "Indian";
                                                            break;

                                                        case 'BDT':
                                                            $generateReport[] = "Bangladeshi";
                                                            break;

                                                        case 'PHP':
                                                            $generateReport[] = "Filipino";
                                                            break;

                                                        case 'PKR':
                                                            $generateReport[] = "Pakistani";
                                                            break;

                                                        case 'KHR':
                                                            $generateReport[] = "Cambodian";
                                                            break;

                                                        case 'THB':
                                                            $generateReport[] = "Thai";
                                                            break;

                                                        default:
                                                            $generateReport[] = "-";
                                                            break;
                                                    }
                                                    break;

                                                default:
                                                    $generateReport[] = $listValue[$key] ?? "-";
                                                    break;
                                            }

                                        }
                                    }
                                } else {
                                    switch ($dataKey) {
                                        case 'no':
                                            $count++;
                                            $generateReport[] = $count;
                                            break;

                                        case 'transfer_category':
                                            $generateReport[] = "Transfer to Other Bank Online";
                                            break;

                                        case 'received_amount':
                                            $generateReport[] = Traits\DecimalTrait::setDecimal(($listValue[$dataKey] ?? 0), 0);
                                            break;

                                        default:
                                            $generateReport[] = $listValue[$dataKey] ?? "-";
                                            break;
                                    }
                                }
                            }
                            yield $generateReport;
                        }
                        break;

                    case 'User_walletBalanceReport':
                        $header = [
                            "member_id" => "User ID",
                            "name" => "Name",
                            "username" => "Phone No",
                            "myr-credit" => "MYR Wallet Balance",
                            "thb-credit" => "THB Wallet Balance",
                            "usdt-credit" => "USDT Wallet Balance",
                        ];
                        foreach ($header as $listValue) {
                            $headers[] = $listValue;
                        }
                        if ($currentPage == 1) yield $headers;
                        foreach ($list[$listKey] as $listValue) {
                            $generateReport = [];
                            foreach ($header as $key => $value) {
                                if (Str::contains($key, '-credit')) {
                                    $generateReport[] = $listValue['balance'][$key] ?: '-';
                                } else {
                                    $generateReport[] = $listValue[$key] ?: '-';
                                }
                            }
                            yield $generateReport;
                        }
                        break;

                    default:
                        break;
                }
            } else {
                foreach ($list[$listKey] as $listValue) {
                    $generateReport = [];
                    foreach ($exportColumn as $key => $value) {
                        // Handle boolean values properly
                        if (in_array($key, ['is_selected', 'is_joined'])) {
                            // If the value is already 'Yes' or 'No', use it as is
                            if ($listValue[$key] === 'Yes' || $listValue[$key] === 'No') {
                                $generateReport[$value] = $listValue[$key];
                            } else {
                                // Otherwise, convert boolean to 'Yes' or 'No'
                                $generateReport[$value] = $listValue[$key] ? 'Yes' : 'No';
                            }
                        } else {
                            $generateReport[$value] = $listValue[$key] ?: '-';
                        }
                    }
                    yield $generateReport;
                }
            }
            echo round(memory_get_usage() / 1048576, 2) . '' . ' MB' . "\n";
            if ($list['pagination']['last_page'] == $currentPage) break;
            $a++;
        }
    }

    function twoLayerHeader($array)
    {
        $layer = 1; // default 2 layer
        $head = [];
        foreach ($array as $ary) {
            if (is_array($ary)) {
                $currentIndex = count($head[$layer]);
                if (!isset($head[$layer - 1])) $head[$layer - 1] = [];
                $head[$layer - 1] = array_pad($head[$layer - 1], count($head[$layer]), "");
                $head[$layer - 1][$currentIndex] = $ary['display'];
                foreach ($ary['data'] as $arr) {
                    $head[$layer][] = $arr;
                }
            } else {
                $head[$layer][] = $ary;
            }
        }
        sort($head);
        return $head;
    }
}
