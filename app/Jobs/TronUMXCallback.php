<?php

namespace App\Jobs;

use App\Models\CurrencyRate;
use App\Traits\DecimalTrait;
use App\Models\Deposit;
use App\Models\SystemSettingsAdmin;
use App\Traits\GenerateNumberTrait;
use App\Models\TransactionLogs;
use App\Models\UserWalletAddress;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;

use function PHPUnit\Framework\isJson;

class TronUMXCallback implements ShouldQueue
{

    use Dispatchable,
        InteractsWithQueue,
        Queueable,
        SerializesModels,
        DecimalTrait;

    protected $params = [];

    public $timeout = 0;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($params = [])
    {
        $this->params = $params;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $returnData = $this->params;

        if (isJson($returnData)) {
            $returnData = json_decode($returnData, true);
        }
        $data = $this->handleCallback($returnData);
    }

    private function handleCallback($params)
    {
        $data = $params ?? [];

        DB::transaction(function () use ($data) {
            // Prefix Variable
            $tx_id = $data["transactionId"] ?? null;
            $to_address = $data["toAddress"] ?? null;
            $deposit_amount = $data["amount"];
            $charges = $data["charges"] ?? 0;
            $status = $data["status"] ?? null;
            $dateTime = date("Y-m-d H:i:s");
            $coin = $data["assetName"] ?? null;

            if ($coin != "USDT") {
                throw new \Exception("Invalid coin");
            }

            $userWalletAddress = UserWalletAddress::where("address", $to_address)->where("status", UserWalletAddress::$status["active"])->where("coin_type", $coin)->first();
            $userId = $userWalletAddress->user_id ?? null;
            $creditId = $userWalletAddress->credit_id ?? null;
            if (empty($userId)) {
                throw new \Exception("Invalid address");
            }

            // Double check trnx id
            $checkTxId = TransactionLogs::where("tx_id", $tx_id)->whereIN("progress", [TransactionLogs::$progress["failed"], TransactionLogs::$progress["confirmed"]])->first();
            if ($checkTxId) {
                throw new \Exception("This transaction id already failed or confirmed.");
            }

            $paymentNumber = $paymentNumber ?? GenerateNumberTrait::generatePaymentNumber("crypto");

            // Deposit charge need special handle
            $rate = CurrencyRate::getCurrencyRate($coin, $coin);
            $cryptoCharge = 0; // SystemSettingsAdmin::getCharges('crypto');
            $percentageCharge = 0;
            $minCharge = 0;
            $tempCharge = $deposit_amount - ($deposit_amount * 100 / ($percentageCharge + 100));
            $convertMinCharge = $minCharge * $rate['deposit_rate'];
            $totalCharge = $tempCharge < $convertMinCharge ? $convertMinCharge : $tempCharge;

            $checkColumn = ["tx_id" => $tx_id];

            $updateColumn = [
                "result" => json_encode($data ?? []),
                "payment_method" => TransactionLogs::$payment_method["crypto"],
                "callback_amount" => $deposit_amount,
                "platform_charge" => $charges,
                "total_charge" => $totalCharge,
                "tx_id" => $tx_id,
            ];

            switch (strtolower($status)) {
                case "processing":
                    $updateColumn["progress"] = TransactionLogs::$progress["received"];
                    $updateColumn["status"] = TransactionLogs::$status["in-progress"];
                    $updateColumn["user_id"] = $userId;
                    $updateColumn["type"] = TransactionLogs::$type["deposit"];
                    TransactionLogs::updateOrCreate($checkColumn, $updateColumn);
                    break;

                case "confirmed":
                    $updateColumn["progress"] = TransactionLogs::$progress["confirmed"];
                    $updateColumn["status"] = TransactionLogs::$status["approved"];
                    $updateColumn["user_id"] = $userId;
                    $updateColumn["type"] = TransactionLogs::$type["deposit"];
                    $trxnObj = TransactionLogs::updateOrCreate($checkColumn, $updateColumn);
                    //Top Up Module
                    $depositDefault = [
                        "deposit_type" => "crypto",
                        "deposit_status" => "approved",
                        "user_id" => $userId,
                        "datetime" => $dateTime,
                        "amount" => $deposit_amount,
                        "reference_id" => $trxnObj->id ?? 0,
                        "payment_number" => $trxnObj->payment_number ?? $paymentNumber,
                        "currency" => $coin,
                        "credit_id" => $creditId,
                    ];
                    Deposit::addDeposit($depositDefault);
                    break;

                default:
                    TransactionLogs::updateOrCreate(["tx_id" => $tx_id], [
                        "user_id" => $userId,
                        "type" => TransactionLogs::$type["deposit"],
                        "result" => json_encode($data ?? []),
                        "callback_amount" => $deposit_amount,
                        "platform_charge" => $charges,
                        "progress" => TransactionLogs::$progress["failed"],
                        "status" => TransactionLogs::$status["failed"],
                        "payment_method" => TransactionLogs::$payment_method["crypto"],
                    ]);
                    break;
            }

            // Update payment number if previous dont have
            TransactionLogs::where($checkColumn)->whereNull("payment_number")->update(["payment_number" => $paymentNumber, "rate" => $rate["deposit_rate"]]);
        });

        return true;
    }

    public static function convertFromSun(int $amount): float
    {
        return $amount / 1000000;
    }

    public static function convertToSun(float $amount): int
    {
        return $amount * 1000000;
    }
}