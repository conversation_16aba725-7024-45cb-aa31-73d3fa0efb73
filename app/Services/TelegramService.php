<?php

namespace App\Services;

use App\Models\SystemSetting;
use App\Models\User;
use DefStudio\Telegraph\Models\TelegraphChat;

class TelegramService
{
    public function __construct() {}

    public function sendMessage($message)
    {
        try {
            $sysTelegram = SystemSetting::where('name', 'telegram.chat_id.general')->first()->pluck('value', 'name');
            $chat = TelegraphChat::where('chat_id', $sysTelegram['telegram.chat_id.general'])->first();
            $chat->markdown($message)->send();
        } catch (\Throwable $th) {
            //throw $th;
        }
    }
}
