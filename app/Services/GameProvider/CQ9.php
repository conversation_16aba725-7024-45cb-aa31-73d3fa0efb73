<?php

namespace App\Services\GameProvider;

use App\Models\CurlLog;
use App\Models\Providers\MtUser;
use App\Models\Services;
use App\Models\User;
use Illuminate\Http\Client\Response as HttpResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class CQ9
{
    protected string $clientId;
    protected string $accessToken;
    protected string $baseUrl;

    protected ?string $userToken;

    public function __construct(string $baseUrl, string $clientId, string $accessToken)
    {
        $this->baseUrl = $baseUrl;
        $this->clientId = $clientId;
        $this->accessToken = $accessToken;
    }

    public function createUser($user)
    {
        $account = env('USER_ACCOUNT_PREFIX') . $user->uuid;

        if ($this->checkUserAccount($account)) {
            return [
                'status' => true,
                "data" => "Account already exists"
            ];
        } else {
            $response = $this->httpPostRequest('/gameboy/player', [
                'account' => $account,
                'password' => $user->uuid,
                'nickname' => $user->name,
            ])->throw();

            return $response->json('data');
        }
    }

    public function checkUserAccount($account): bool
    {
        $response = $this->httpGetRequest('/gameboy/player/check/' . $account)->throw();
        return $response->json('data');
    }

    protected function generateUserToken($account, $password): void
    {
        $params = [
            'account' => $account,
            'password' => $password,
        ];

        $response = $this->httpPostRequest('/gameboy/player/login', $params)->throw();

        if ($response->ok() == false) {
            abort(Response::HTTP_FAILED_DEPENDENCY, __('CQ9 Provider server error.'));
        }

        $this->userToken = $response->json('data')['usertoken'] ?? null;
    }

    public function getGameUrl($user, $service_id)
    {
        $account = env('USER_ACCOUNT_PREFIX') . $user->uuid;

        $this->generateUserToken($account, $user->uuid);

        $response = $this->httpPostRequest('/gameboy/player/gamelink', [
            'usertoken' => $this->userToken,
            'gamehall' => 'cq9',
            'gamecode' => 'GINKGO01',
            'gameplat' => 'WEB',
            'lang' => 'en',
            'app' => 'N',
            'detect' => 'N'
        ])->throw();

        $response = $response->json('data');

        if (isset($response)) {
            return [
                'status' => true,
                'data' => $response['url'] . "&tabletype=marble&tableid=MC01&nolobby=true"
            ];
        }

        return [
            'status' => false,
            'data' => null
        ];
    }

    public function checkBalance($user)
    {
        $account = env('USER_ACCOUNT_PREFIX') . $user->uuid;
        $response = $this->httpGetRequest('/gameboy/player/balance/' . $account)->throw();
        $data = $response->json('data');

        return [
            'status' => true,
            "data" => [
                'currency' => $data['currency'],
                'main_wallet' => $data['balance']
            ]
        ];
    }

    public function deposit($user, $amount, $mtcode)
    {
        try {
            if ($amount == 0) {
                return ['status' => true, "data" => 0];
            }

            $account = env('USER_ACCOUNT_PREFIX') . $user->uuid;

            // $this->createUser($user);

            $response = $this->httpPostRequest('/gameboy/player/deposit', [
                'account' => $account,
                'mtcode' => $mtcode,
                'amount' => $amount,
            ])->throw();

            $data = $response->json('data');
            return [
                'status' => true,
                "data" => [
                    'currency' => $data['currency'],
                    'balance' => $data['balance']
                ]
            ];
        } catch (\Throwable $th) {
            return [
                'status' => false,
                "data" => $th->getMessage()
            ];
        }
    }

    public function withdraw($user, $mtcode)
    {
        try {
            $balance = $this->checkBalance($user)['data'];
            if ($balance['main_wallet'] == 0) {
                return [
                    'status' => true,
                    "data" => [
                        'currency' => $balance['currency'],
                        'amount' => $balance['main_wallet']
                    ]
                ];
            }

            $account = env('USER_ACCOUNT_PREFIX') . $user->uuid;
            $response = $this->httpPostRequest('/gameboy/player/withdraw', [
                'account' => $account,
                'mtcode' => $mtcode,
                'amount' => $balance['main_wallet'],
            ])->throw();

            $data = $response->json('data');

            return [
                'status' => true,
                "data" => [
                    'currency' => $data['currency'],
                    'amount' => $balance['main_wallet']
                ]
            ];
        } catch (\Throwable $th) {
            return [
                'status' => false,
                "data" => $th->getMessage()
            ];
        }
    }

    public function logout($account)
    {
        $response = $this->httpPostRequest('/gameboy/player/logout', [
            'account' => $account
        ])->throw();

        return $response->json('data');
    }

    protected function httpPostRequest(string $path, array $params): HttpResponse
    {
        $response = Http::asForm()->withHeaders([
            'Authorization' => $this->accessToken,
        ])->post($this->baseUrl . $path, $params);

        CurlLog::create([
            'endpoint' => $path ?? '',
            'request' => isset($params) ? (is_array($params) ? json_encode($params) : $params) : '',
            'response' => $response ?? '',
            'created_at' => now(),
        ]);

        if ($response->json('code') && $response->json('code') != 1000) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, $response->json('message'));
        } else if (!$response->ok()) {
            abort(Response::HTTP_FAILED_DEPENDENCY, __('CQ9 Provider server error.'));
        }

        return $response;
    }

    protected function httpGetRequest(string $path, array $params = []): HttpResponse
    {
        $response = Http::withHeaders([
            'Authorization' => $this->accessToken
        ])->get($this->baseUrl . $path, $params);

        CurlLog::create([
            'endpoint' => $path ?? '',
            'request' => isset($params) ? (is_array($params) ? json_encode($params) : $params) : '',
            'response' => $response ?? '',
            'created_at' => now(),
        ]);

        if ($response->json('code') && $response->json('code') != 1000) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, $response->json('message'));
        } else if (!$response->ok()) {
            abort(Response::HTTP_FAILED_DEPENDENCY, __('CQ9 Provider server error.'));
        }

        return $response;
    }
}
