<?php

namespace App\Services\GameProvider;

use App\Models\CurlLog;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class MEGA
{
    protected string $baseUrl;

    protected string $sn;

    protected string $secret;

    protected string $agentId;

    public static $productId = 2013;

    private static $uri = 'lobbymegarelease://';

    public function __construct(string $baseUrl, string $sn, string $secret, string $agentId)
    {
        $this->baseUrl = $baseUrl;
        $this->sn = $sn;
        $this->secret = $secret;
        $this->agentId = $agentId;
    }

    public function createMemberAccount()
    {
        $response = $this->request('open.mega.user.create');

        $data = $response->json();

        if (! isset($data['error'])) {
            return [
                'status' => true,
                'login_id' => $data['result']['loginId'],
                'user_id' => $data['result']['userId'],
            ];
        }

        return [
            'status' => false,
            'error' => $data['error'] ?? null,
        ];
    }

    public function getBalance($loginId)
    {
        $params = ['loginId' => $loginId];
        $response = $this->request('open.mega.balance.get', $params);
        $data = $response->json();

        if (! isset($data['error'])) {
            return [
                'status' => true,
                'balance' => $data['result'],
            ];
        }

        return [
            'status' => false,
            'error' => $data['error'],
        ];
    }

    public function deposit($loginId, $amount)
    {
        $params = [
            'loginId' => $loginId,
            'amount' => $amount,
        ];
        $response = $this->request('open.mega.balance.transfer', $params);
        $data = $response->json();

        if (! isset($data['error'])) {
            return [
                'status' => true,
                'balance' => $data['result'],
            ];
        }

        return [
            'status' => false,
            'error' => $data['error'],
        ];
    }

    public function withdraw($loginId)
    {
        $params = ['loginId' => $loginId];
        $response = $this->request('open.mega.balance.auto.transfer.out', $params);
        $data = $response->json();

        if (! isset($data['error'])) {
            return [
                'status' => true,
                'balance' => $data['result'],
            ];
        }

        return [
            'status' => false,
            'error' => $data['error'],
        ];
    }

    // TODO: WIP
    public function getCredentials($loginId)
    {
        $downloadUrlResponse = $this->getAppDownloadUrl();

        if (! $downloadUrlResponse || ! $downloadUrlResponse['status']) {
            return;
        }

        $username = $loginId;
        $password = $loginId;
        $gameUrl = self::$uri.'?account='.$username.'&password='.$password;

        return [
            'username' => $username,
            'password' => $password,
            'game_url' => $gameUrl,
            'download_url' => $downloadUrlResponse['url'] ?? '',
        ];
    }

    // TODO: Get turnover
    public function queryGameRecord($loginId, $startTime = null)
    {
        $startTime = date('Y-m-d H:i:s', strtotime('-1 days'));
        $endTime = date('Y-m-d H:i:s', now()->timestamp);
        $params = [
            'loginId' => $loginId,
            'startTime' => $startTime,
            'endTime' => $endTime,
        ];
        $response = $this->request('open.mega.player.game.log.url.get', $params);
        $data = $response->json();

        return $data;

        if (! isset($data['error'])) {
            return [
                'status' => true,
                'balance' => $data['result'],
            ];
        }

        return [
            'status' => false,
            'error' => $data['error'],
        ];
    }

    public function getAppDownloadUrl()
    {
        $response = $this->request('open.mega.app.url.download');
        $data = $response->json();

        if (! isset($data['error'])) {
            return [
                'status' => true,
                'url' => $data['result'],
            ];
        }

        return [
            'status' => false,
            'error' => $data['error'],
        ];
    }

    private function generateDigest($random, $loginId, $amount)
    {
        $digest = md5($random.$this->sn.$loginId.$amount.$this->secret);

        return $digest;
    }

    protected function request(string $method, array $params = [], $ignoreLoginId = false)
    {
        $random = (string) Str::orderedUuid();
        $loginId = $ignoreLoginId ? '' : $params['loginId'] ?? '';
        $amount = $params['amount'] ?? '';
        $digest = $this->generateDigest($random, $loginId, $amount);

        $data = array_merge($params, [
            'random' => $random,
            'digest' => $digest,
            'sn' => $this->sn,
            'agentLoginId' => $this->agentId,
            'jsonrpc' => '2.0',
            'method' => $method,
            'params' => $params,
            'id' => (string) Str::orderedUuid(),
        ]);

        $response = Http::asForm()->timeout(30)->post($this->baseUrl, $data);

        CurlLog::create([
            'endpoint' => $this->baseUrl ?? '',
            'request' => isset($data) ? (is_array($data) ? json_encode($data) : $data) : '',
            'response' => $response ?? '',
            'created_at' => now(),
        ]);

        // if ($response->json('code') && $response->json('code') != 1000) {
        //     abort(Response::HTTP_UNPROCESSABLE_ENTITY, $response->json('message'));
        // } elseif (! $response->ok()) {
        //     abort(Response::HTTP_FAILED_DEPENDENCY, __('CQ9 Provider server error.'));
        // }

        return $response;
    }
}
