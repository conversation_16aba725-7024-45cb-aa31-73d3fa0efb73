<?php

namespace App\Services\GameProvider;

use App\Models\CurlLog;
use App\Models\Providers\MtUser;
use App\Models\Services;
use App\Models\User;
use App\Models\UserProduct;
use Illuminate\Http\Client\Response as HttpResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class MT
{
    protected string $clientId;
    protected string $clientSecret;
    protected string $baseUrl;

    protected ?string $accessToken;

    public static $productId = 2002;

    public function __construct(string $baseUrl, string $clientId, string $clientSecret)
    {
        $this->baseUrl = $baseUrl;
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
    }

    protected function generateAccessToken(): void
    {
        $params = [
            'grant_type' => 'client_credentials',
            'client_id' => $this->clientId,
            'client_secret' => $this->clientSecret
        ];

        $response = Http::post($this->baseUrl . '/oauth/token', $params);

        if ($response->ok() == false) {
            abort(Response::HTTP_FAILED_DEPENDENCY, __('MT Provider server error.'));
        }

        $this->accessToken = $response->json('access_token');
    }

    public function getGameUrl($userId, $gameCategory, $branchId = null)
    {
        $this->generateAccessToken();
        $user = User::find($userId);

        if (!MtUser::where('user_id', $userId)->exists()) {
            $userName = 'fw' . strtolower(preg_replace('/\s*/', '', $user->name));
            $password = Str::random(9) . rand(1, 9);

            // Create MT User
            $response = $this->httpPostRequest('create-player', [
                'username' => $userName,
                'password' => $password,
                'currency' => "MYR"
            ])->throw();

            $newMtUser = new MtUser;
            $newMtUser->username = $userName;
            $newMtUser->password = $password;
            $newMtUser->user_id = $userId;
            $newMtUser->store_id = $branchId == null ? $user->store_id : $branchId;
            $newMtUser->is_online = $branchId == null ? true : false;

            $newMtUser->save();
        } else {
            $mtUser = MtUser::where('user_id', $userId)->first();
            $userName = $mtUser->username;
            $password = $mtUser->password;
        }

        // Transfer Balance from Wallet to Game
        // TODO: Check getProductBalance API and get Amount & Save into MT Transaction
        // TODO: Write into FW Transaction Table.
        $amount = 0;

        $response = $this->httpPostRequest('make-transaction', [
            'amount' => $amount,
            'username' => $userName,
            'type' => "deposit"
        ])->throw();

        $balance = $response->json('data')['balance'];

        $response = $this->httpPostRequest('login-game', [
            'username' => $userName,
            'password' => $password,
            'game_code' => "MT",
            'lang' => "en"
        ])->throw();

        $gameUrl = $response->json('data')['game_url'];

        // TODO: Add success status message & Status code?
        return [
            'message' => "Successfully deposit all money into Game Wallet.",
            'balance' => $balance,
            'gameUrl' => $gameUrl
        ];
    }

    public function createUser($userId, $gameCategory = null, $branchId = null)
    {
        $this->generateAccessToken();

        if (!MtUser::where('user_id', $userId)->exists()) {
            $user = User::find($userId);
            $userName = 'fw' . strtolower(preg_replace('/\s*/', '', $user->name));
            $password = Str::random(9) . rand(1, 9);

            // Create MT User
            $response = $this->httpPostRequest('create-player', [
                'username' => $userName,
                'password' => $password,
                'currency' => "MYR"
            ])->throw();

            $newMtUser = new MtUser;
            $newMtUser->username = $userName;
            $newMtUser->password = $password;
            $newMtUser->user_id = $userId;
            $newMtUser->store_id = $branchId == null ? $user->store_id : $branchId;
            $newMtUser->is_online = $branchId == null ? true : false;

            $newMtUser->save();
        } else {
            $mtUser = MtUser::where('user_id', $userId)->first();
            $userName = $mtUser->username;
            $password = $mtUser->password;
        }

        return [
            'username' => $userName,
            'password' => $password
        ];
    }

    public function createMember($user)
    {
        $account = $user->uuid;
        $count = UserProduct::where('user_id', $user->id)
            ->where('product_id', self::$productId)
            ->count();

        if ($count > 0) {
            return [
                'status' => false,
                'message' => 'User product exists',
            ];
        }

        $this->generateAccessToken();

        $response = $this->httpPostRequest('create-player', [
            'username' => $account,
            'password' => $user->uuid,
            'currency' => "MYR"
        ])->throw();

        $data = $response->json('data');
        UserProduct::subscribeGame($user->id, self::$productId, $account);

        return [
            'status' => true,
            'data' => $data['data'] ?? null,
            'message' => $data['message'] ?? null,
        ];
    }

    public function getBalance($userId)
    {
        $this->generateAccessToken();
        $user = $this->createUser($userId);

        $response = $this->httpPostRequest('get-balance', [
            'username' => $user['username'],
            'password' => $user['password'],
            'currency' => "MYR"
        ])->throw();

        $balance = $response->json('data')['balance'];

        return ['status' => true, "data" => [
            'main_wallet' => $balance
        ]];
    }

    public function deposit($account, $amount)
    {
        try {
            if ($amount == 0) {
                return ['status' => true, "data" => 0];
            }

            $this->generateAccessToken();

            $response = $this->httpPostRequest('make-transaction', [
                'amount' => $amount,
                'username' => $account,
                'type' => "deposit"
            ])->throw();

            $balance = $response->json('data')['balance'];
            return [
                'status' => true,
                "data" => ['amount' => $balance]
            ];
        } catch (\Throwable $th) {
            return ['status' => false, "data" => $th->getMessage()];
        }
    }

    public function withdraw($account, $password)
    {
        try {
            $this->generateAccessToken();


            $response = $this->httpPostRequest('get-balance', [
                'username' => $account,
                'password' => $password,
                'currency' => "MYR"
            ])->throw();

            $amount = $response->json('data')['balance'];

            if ($amount == 0) {
                return [
                    'status' => true,
                    "data" => ['amount' => $amount]
                ];
            }

            $response = $this->httpPostRequest('make-transaction', [
                'amount' => $amount,
                'username' => $account,
                'type' => "withdrawal"
            ])->throw();

            return [
                'status' => true,
                "data" => [
                    'amount' => $amount
                ]
            ];
        } catch (\Throwable $th) {
            return ['status' => false, "data" => $th->getMessage()];
        }
    }

    public function getLoginUrl($account, $password)
    {
        try {
            $this->generateAccessToken();

            $response = $this->httpPostRequest('login-game', [
                'username' => $account,
                'password' => $password,
                'game_code' => "iw", //MT, 30s, iw
                'lang' => "en"
            ])->throw();

            $gameUrl = 'https://' . $response->json('data')['game_url'];

            return ['status' => true, "data" => $gameUrl];
        } catch (\Throwable $th) {
            return ['status' => false, "data" => $th->getMessage()];
        }
    }

    public function checkBalance($memberId)
    {
        $this->generateAccessToken();
        // $mtUser = MtUser::where('user_id', $userId)->first();

        // if ($mtUser == null) {
        //     abort(Response::HTTP_FAILED_DEPENDENCY, __('User not found.'));
        // }

        $response = $this->httpPostRequest('get-balance', [
            'username' => $memberId,
            'password' => $memberId,
            'currency' => "MYR"
        ])->throw();

        $amount = $response->json('data')['balance'];

        return ['status' => true, "data" => [
            'main_wallet' => $amount
        ]];
    }

    public function closeGame($userId, $branchId = null)
    {
        $this->generateAccessToken();
        $mtUser = MtUser::where('user_id', $userId)->first();

        if ($mtUser == null) {
            abort(Response::HTTP_FAILED_DEPENDENCY, __('User not found.'));
        }

        $response = $this->httpPostRequest('get-balance', [
            'username' => $mtUser->username,
            'password' => $mtUser->password,
            'currency' => "MYR"
        ])->throw();

        $amount = $response->json('data')['balance'];

        $response = $this->httpPostRequest('make-transaction', [
            'amount' => $amount,
            'username' => $mtUser->username,
            'type' => "withdrawal"
        ])->throw();

        // TODO: Write into Mt_Transactions table? or just retrive from MT_Transaction table and update Branch ID.
        // TODO: Write into FW Transaction Table.
        // TODO: Condition Checking if isOnline, then no need write branch id.

        // TODO: Add success status message & Status code?
        return [
            'message' => "Successfully withdraw all money into Main Wallet",
            'amount' => $amount,
        ];
    }

    public function getDemoGameUrl($username, $password)
    {
        try {
            // ID: min 6 digit, pass: 8 digit
            $this->generateAccessToken();

            $response = $this->httpPostRequest('create-player', [
                'username' => $username,
                'password' => $password,
                'currency' => "MYR"
            ])->throw();

            $this->generateAccessToken();

            $response = $this->httpPostRequest('login-game', [
                'username' => $username,
                'password' => $password,
                'game_code' => "iw",
                'lang' => "en"
            ])->throw();

            $gameUrl = $response->json('data')['game_url'];

            // TODO: Add success status message & Status code?
            return [
                'message' => "Successfully deposit all money into Game Wallet.",
                'data' => 'https://' . $gameUrl
            ];
        } catch (\Throwable $th) {
            $response = $this->httpPostRequest('login-game', [
                'username' => $username,
                'password' => $password,
                'game_code' => "iw",
                'lang' => "en"
            ])->throw();

            $gameUrl = $response->json('data')['game_url'];

            // TODO: Add success status message & Status code?
            return [
                'message' => "Successfully deposit all money into Game Wallet.",
                'data' => 'https://' . $gameUrl
            ];
        }
    }


    protected function httpPostRequest(string $path, array $params): HttpResponse
    {
        $url = $this->baseUrl . '/api/v1.0.0/' . $path;
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->accessToken
        ])->post($url, $params);

        CurlLog::create([
            'endpoint' => $url ?? '',
            'request' => isset($params) ? (is_array($params) ? json_encode($params) : $params) : '',
            'response' => $response ?? '',
            'created_at' => now(),
        ]);

        if ($response->json('code') && $response->json('code') != 1000) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, $response->json('message'));
        } else if (!$response->ok()) {
            abort(Response::HTTP_FAILED_DEPENDENCY, __('MT Provider server error.'));
        }

        return $response;
    }
}
