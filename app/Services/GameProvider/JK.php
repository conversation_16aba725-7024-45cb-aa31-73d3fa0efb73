<?php

namespace App\Services\GameProvider;

use App\Models\CurlLog;
use App\Models\UserPromotion;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Client\Response as HttpResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Http;

class JK
{
    protected string $baseUrl;

    protected string $agentId;

    protected string $agentKey;

    protected ?string $key;

    // WARNING: Hardcoded value
    public static $productId = 2004;

    public function __construct(string $baseUrl, string $agentId, string $agentKey)
    {
        $this->baseUrl = $baseUrl;
        $this->agentId = $agentId;
        $this->agentKey = $agentKey;
    }

    public function generateAccessToken($qString, $agentId)
    {
        $firstRandStr = random_int(100000, 999999);
        $lastRandStr = str()->random(6);
        $date = gmdate('Ymd');

        $keyG = md5($date . $agentId . $this->agentKey);
        $md5String = md5($qString . $keyG);

        return $firstRandStr . $md5String . $lastRandStr;
    }

    public function getLoginUrl($account, $cashIn, $agentId, $useDbCash = false)
    {
        // $this->logout($account);
        $isDbCash = $useDbCash ? 1 : 0;

        // $account = 'fw_ITgIdgMcEAoV';
        // $cashIn = 740.05;
        // $agentId = 'funwallet_01';
        // $isDbCash = 0;

        $token = $this->generateAccessToken('Account=' . $account . '&GameId=101&CashIn=' . $cashIn . '&UseDbCash=' . $isDbCash . '&AgentId=' . $agentId, $agentId);
        $response = $this->httpPostRequest($this->baseUrl . '/Login', [
            'Account' => $account,
            'GameId' => 101,
            'CashIn' => $cashIn,
            'UseDbCash' => $isDbCash,
            'Lang' => 'zh-CN',
            'EnableFullScreen' => 1,
            'AgentId' => $agentId,
            'Key' => $token,
        ])->throw();

        $data = $response->json();
        if ($data['Error'] == 0) {
            // dd(true, $account, $cashIn);
            return [
                'status' => true,
                'data' => $data['Data'],
            ];
        }

        return [
            'status' => false,
            'data' => null,
        ];
    }

    public function logout($account, $agentId)
    {
        $token = $this->generateAccessToken('Account=' . $account . '&GameId=101&AgentId=' . $agentId, $agentId);
        $response = $this->httpPostRequest($this->baseUrl . '/Logout', [
            'Account' => $account,
            'GameId' => 101,
            'AgentId' => $agentId,
            'Key' => $token,
        ])->throw();

        $data = $response->json();
        if ($data['Error'] == 0) {
            return [
                'status' => true,
                'data' => $data['State'],
            ];
        }

        return [
            'status' => false,
            'data' => $data['State'],
        ];
    }

    public function deposit($amount)
    {
        return [
            'status' => true,
            'data' => [
                'main_wallet' => $amount,
            ],
        ];
    }

    public function withdraw($account, $agentId)
    {
        $token = $this->generateAccessToken('Account=' . $account . '&AgentId=' . $agentId, $agentId);
        $response = $this->httpPostRequest($this->baseUrl . '/Withdraw', [
            'Account' => $account,
            'AgentId' => $agentId,
            'Key' => $token,
        ])->throw();

        return $response->json();
    }

    public function getBalance($account, $agentId)
    {
        $data = $this->withdraw($account, $agentId);

        if ($data['Error'] == 0 && $data['Result'] == 1) {
            return $data['Data']['AllCashOut'] ?? 0;
        }

        return 0;
    }

    public function refundCoin($account, $refundKey, $agentId)
    {
        $token = $this->generateAccessToken('Account=' . $account . '&RefundKey=' . $refundKey . '&AgentId=' . $agentId, $agentId);
        $response = $this->httpPostRequest($this->baseUrl . '/RefundCoins', [
            'Account' => $account,
            'RefundKey' => $refundKey,
            'AgentId' => $agentId,
            'Key' => $token,
        ])->throw();

        return $response->json();
    }

    public function getWithdraw($account, $agentId, $hasPromotion = false)
    {
        try {
            if ($hasPromotion) {
                $this->logout($account, $agentId);

                return [
                    'status' => true,
                    'data' => [
                        'main_wallet' => 0,
                    ],
                ];
            }

            $data = $this->withdraw($account, $agentId);
            if ($data['Error'] == 0 && $data['Result'] == 1) {
                $refund = $this->refundCoin($account, $data['Data']['RefundKey'], $agentId);

                if ($refund['Error'] == 0 && $refund['Result'] == 1) {
                    $this->logout($account, $agentId);

                    return [
                        'status' => true,
                        'data' => [
                            'main_wallet' => $data['Data']['AllCashOut'],
                        ],
                    ];
                }
            } else {
                $this->logout($account, $agentId);

                return [
                    'status' => true,
                    'data' => [
                        'main_wallet' => 0,
                    ],
                ];
            }
        } catch (\Throwable $th) {
            return [
                'status' => true,
                'data' => [
                    'main_wallet' => 0,
                ],
            ];
        }
    }

    public function getBetTransaction($startTime, $endTime, $agentId)
    {
        $token = $this->generateAccessToken('GameId=101&StartTime=' . $startTime . '&EndTime=' . $endTime . '&AgentId=' . $agentId, $agentId);
        $response = $this->httpPostRequest($this->baseUrl . '/GetBetTransactions', [
            'GameId' => 101,
            'StartTime' => $startTime,
            'EndTime' => $endTime,
            'AgentId' => $agentId,
            'Key' => $token,
        ])->throw();

        return $response->json();
    }

    public function getBetTransactionByAccount($account, $startTime, $endTime, $agentId)
    {
        $startDt = Carbon::parse($startTime, 'Asia/Singapore')->setTimezone('UTC');
        $startTime = $startDt->format('Y-m-d\TH:i:s.') . substr($startDt->format('u'), 0, 3) . 'Z';

        $endDt = Carbon::parse($endTime, 'Asia/Singapore')->setTimezone('UTC');
        $endTime = $endDt->format('Y-m-d\TH:i:s.') . substr($endDt->format('u'), 0, 3) . 'Z';

        $token = $this->generateAccessToken('GameId=101&Account=' . $account . '&StartTime=' . $startTime . '&EndTime=' . $endTime . '&AgentId=' . $agentId, $agentId);
        $response = $this->httpPostRequest(env('JK_BASE_URL') . '/GetBetTransactions', [
            'GameId' => 101,
            'Account' => $account,
            'StartTime' => $startTime,
            'EndTime' => $endTime,
            'AgentId' => $agentId,
            'Key' => $token,
        ])->throw();

        return $response->json();
    }

    protected function httpPostRequest(string $path, array $params): HttpResponse
    {
        $response = Http::asForm()->timeout(10)->post($path, $params);

        CurlLog::create([
            'endpoint' => $path ?? '',
            'request' => isset($params) ? (is_array($params) ? json_encode($params) : $params) : '',
            'response' => $response ?? '',
            'created_at' => now(),
        ]);

        if ($response->json('code') && $response->json('code') != 1000) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, $response->json('message'));
        } elseif (! $response->ok()) {
            abort(Response::HTTP_FAILED_DEPENDENCY, __('CQ9 Provider server error.'));
        }

        return $response;
    }

    public function checkPromotion($account, $agent_id, $user_id = null)
    {
        $userPromotion = UserPromotion::checkUserActivePromotion($user_id);
        if (isset($userPromotion)) {
            $totalTurnover = 0;
            $lastReturn = 0;
            // $dt = new DateTime($userPromotion['initial_at']);
            // $initial_at = $dt->format('Y-m-d\TH:i:s.') . substr($dt->format('u'), 0, 3) . 'Z';
            // $now = now()->format('Y-m-d\TH:i:s.') . substr(now()->format('u'), 0, 3) . 'Z';

            $initial_at = $userPromotion['initial_at'];
            $now = now();

            // check bet transaction
            $transaction = $this->getBetTransactionByAccount($account, $initial_at, $now, $agent_id);
            if ($transaction['Error'] == 0 && isset($transaction['Data']['List'])) {
                $meta = [];
                $lastReturn = 0;
                foreach ($transaction['Data']['List'] as $key => $value) {
                    $totalTurnover += $value['Bet'];
                    $lastReturn = $value['Return'];
                    $meta[] = [
                        'sn' => $value['Sn'],
                        'bet' => $value['Bet'],
                        'created_at' => $value['CreateTime'],
                        'return' => $value['Return'],
                    ];
                }

                $userPromotion['user_promotion']->update([
                    'meta_transaction' => json_encode($meta),
                ]);
            }

            return [
                'status' => true,
                'data' => [
                    'max_withdraw_amount' => $userPromotion['max_withdraw_amount'],
                    'archived_turnover' => $totalTurnover,
                    'remaining_turnover' => ($userPromotion['target_turnover'] - $totalTurnover) > 0 ? ($userPromotion['target_turnover'] - $totalTurnover) : 0,
                    'return_amount' => $lastReturn,
                ],
            ];
        }

        return null;
    }
}
