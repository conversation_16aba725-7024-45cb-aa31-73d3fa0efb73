<?php

namespace App\Services\GameProvider;

use App\Models\CurlLog;
use App\Models\UserProduct;
use Carbon\Carbon;
use Illuminate\Http\Client\Response as HttpResponse;
use Illuminate\Support\Facades\Http;

class JILI
{
    protected string $baseUrl;

    protected string $agentId;

    protected string $agentKey;

    protected ?string $key;

    // WARNING: Hardcoded value
    public static $productId = 2005;

    public static $gameCategory = [
        'slot' => 1,
        'poker' => 2,
        'lobby' => 3,
        'fishing' => 5,
        'casino' => 8,
    ];

    private static $gameCategoryProvider = [
        'slot' => 3,
        'fishing' => 5,
        'crash' => 6,
        'table' => 7,
        'casino' => 18,
    ];

    private static $gameCategoryProviderMap = [
        1 => 3,
        2 => 19,
        3 => 3,
        5 => 5,
    ];

    public static $crashGameList = [
        224,
        229,
        232,
        233,
        235,
        236,
        254,
        241,
        242,
        261,
        407,
        469,
        272,
    ];

    public function __construct(string $baseUrl, string $agentId, string $agentKey)
    {
        $this->baseUrl = $baseUrl;
        $this->agentId = $agentId;
        $this->agentKey = $agentKey;
    }

    private function generateKey($qString)
    {
        $firstRandStr = str()->random(6);
        $lastRandStr = str()->random(6);
        $date = gmdate('ymj', strtotime('-4 hours'));
        $keyG = md5($date . $this->agentId . $this->agentKey);
        $md5String = md5($qString . $keyG);

        return $firstRandStr . $md5String . $lastRandStr;
    }

    private function postRequest($endpoint, array $queries = [])
    {
        $queryString = count($queries) > 0 ? http_build_query($queries) . '&' : '';
        $key = $this->generateKey($queryString . 'AgentId=' . $this->agentId);

        return $this->httpPostRequest($this->baseUrl . $endpoint, array_merge($queries, [
            'AgentId' => $this->agentId,
            'Key' => $key,
        ]))->throw();
    }

    public function getGameList()
    {
        $response = $this->postRequest('/GetGameList');
        $data = $response->json();

        if ($data['ErrorCode'] != 0) {
            return [
                'status' => false,
                'data' => $data['Data'],
                'message' => $data['Message'],
            ];
        }

        $gameList = array_map(function ($e) {
            $gameId = sprintf('%03d', $e['GameId']);
            // $gameId = '';
            $imageUrl = 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/games/jili/300x300/JL_300x300_GameID' . $gameId . '.png';

            return array_merge($e, ['Image' => $imageUrl]);
        }, $data['Data']);

        return [
            'status' => true,
            'data' => $gameList,
        ];
    }

    public function getGameRecord($startTime, $endTime, $page, $pageLimit)
    {
        $response = $this->postRequest('/GetBetRecordByTime', [
            'StartTime' => $startTime,
            'EndTime' => $endTime,
            'Page' => $page,
            'PageLimit' => $pageLimit,
        ]);
        $data = $response->json();

        if ($data['ErrorCode'] != 0) {
            return [
                'status' => false,
                'data' => $data['Data'],
                'message' => $data['Message'],
            ];
        }

        return [
            'status' => true,
            'data' => $data['Data'],
        ];
    }

    // TODO: Get User Game Record when user apply promotion
    public function getGameRecordByUser($startTime, $endTime, $page, $pageLimit, $account)
    {
        $startDt = Carbon::parse($startTime, 'Asia/Singapore')->setTimezone('-4');
        $startTime = $startDt->format('Y-m-d\TH:i:s.') . substr($startDt->format('u'), 0, 3) . 'Z';

        $endDt = Carbon::parse($endTime, 'Asia/Singapore')->setTimezone('-4');
        $endTime = $endDt->format('Y-m-d\TH:i:s.') . substr($endDt->format('u'), 0, 3) . 'Z';

        $response = $this->postRequest('/GetUserBetRecordByTime', [
            'StartTime' => $startTime,
            'EndTime' => $endTime,
            'Page' => $page,
            'PageLimit' => $pageLimit,
            'Account' => $account,
        ]);
        $data = $response->json();

        if ($data['ErrorCode'] != 0) {
            return [
                'status' => false,
                'data' => $data['Data'],
                'message' => $data['Message'],
            ];
        }

        return [
            'status' => true,
            'data' => $data['Data'],
        ];
    }

    public function getGameUrl($account, $gameId)
    {
        $this->logout($account);

        $response = $this->postRequest('/LoginWithoutRedirect', [
            'Account' => $account,
            'GameId' => $gameId,
            'Lang' => 'en-US',
        ]);
        $data = $response->json();

        if ($data['ErrorCode'] == 0) {
            return [
                'status' => true,
                'data' => $data['Data'],
            ];
        }

        return [
            'status' => false,
            'data' => $data['Data'],
            'message' => $data['Message'],
        ];
    }

    public function logout($account)
    {
        $response = $this->postRequest('/KickMember', [
            'Account' => $account,
        ]);
        $data = $response->json();

        if ($data['ErrorCode'] == 0) {
            return [
                'status' => true,
                'data' => $data['Data'],
            ];
        }

        return [
            'status' => false,
            'data' => $data['Data'],
            'message' => $data['Message'],
        ];
    }

    public function createMember($user)
    {
        $account = env('USER_ACCOUNT_PREFIX') . $user->uuid;
        $count = UserProduct::where('user_id', $user->id)
            ->where('product_id', self::$productId)
            ->count();

        if ($count > 0) {
            return [
                'status' => false,
                'message' => 'User product exists',
            ];
        }

        $response = $this->postRequest('/CreateMember', [
            'Account' => $account,
        ]);

        $data = $response->json();

        if ($data['ErrorCode'] == 0) {
            UserProduct::subscribeGame($user->id, self::$productId, $account);

            return [
                'status' => true,
                'data' => $data['Data'],
            ];
        }

        return [
            'status' => false,
            'data' => $data['Data'],
            'message' => $data['Message'],
        ];
    }

    private function checkTransaction($transactionId)
    {
        $response = $this->postRequest('/CheckTransferByTransactionId', [
            'TransactionId' => $transactionId,
        ]);

        $data = $response->json();

        if ($data['ErrorCode'] == 0) {
            return [
                'status' => true,
                'data' => $data['Data'],
            ];
        }

        return [
            'status' => false,
            'data' => $data['Data'],
            'message' => $data['Message'],
        ];
    }

    public function deposit($account, $amount)
    {
        $transactionId = str()->random(50);
        $this->postRequest('/ExchangeTransferByAgentId', [
            'Account' => $account,
            'TransactionId' => $transactionId,
            'Amount' => $amount,
            'TransferType' => 2,
        ]);

        $response = $this->checkTransaction($transactionId);

        if ($response['status'] && $response['data']['TransferType'] == 2) {
            return [
                'status' => true,
                'data' => [
                    'main_wallet' => $response['data']['Amount'],
                ],
            ];
        }

        return ['status' => false];
    }

    public function withdraw($account)
    {
        $this->logout($account);
        $transactionId = str()->random(50);
        $this->postRequest('/ExchangeTransferByAgentId', [
            'Account' => $account,
            'TransactionId' => $transactionId,
            'Amount' => 0,
            'TransferType' => 1,
        ]);

        $response = $this->checkTransaction($transactionId);

        if ($response['status'] && $response['data']['TransferType'] == 1) {
            return [
                'status' => true,
                'data' => [
                    'main_wallet' => -$response['data']['Amount'],
                ],
            ];
        }

        return ['status' => false];
    }

    protected function httpPostRequest(string $path, array $params): HttpResponse
    {
        $response = Http::asForm()->timeout(3)->post($path, $params);

        CurlLog::create([
            'endpoint' => $path ?? '',
            'request' => isset($params) ? (is_array($params) ? json_encode($params) : $params) : '',
            'response' => $response ?? '',
            'created_at' => now(),
        ]);

        // if ($response->json('code') && $response->json('code') != 1000) {
        //     abort(Response::HTTP_UNPROCESSABLE_ENTITY, $response->json('message'));
        // } elseif (! $response->ok()) {
        //     abort(Response::HTTP_FAILED_DEPENDENCY, __('CQ9 Provider server error.'));
        // }

        return $response;
    }

    public static function getGameCategoryProviderIdByGameId($gameCategoryId, $gameId)
    {
        if ($gameCategoryId == self::$gameCategory['casino']) {
            return self::$gameCategoryProvider[in_array($gameId, self::$crashGameList) ? 'crash' : 'table'];
        }

        return self::$gameCategoryProviderMap[$gameCategoryId];
    }
}
