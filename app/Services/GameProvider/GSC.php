<?php

namespace App\Services\GameProvider;

use App\Models\CurlLog;
use App\Models\UserProduct;
use App\Services\BetLogService;
use Illuminate\Http\Client\Response as HttpResponse;
use Illuminate\Support\Facades\Http;

class GSC
{
    protected string $baseUrl;

    protected string $operatorCode;

    protected string $agentKey;

    protected ?string $key;

    // WARNING: Hardcoded value
    public static $productId = 2006;

    public static $name = 'GSC';

    // public static $gameCategory = [
    //     '0' => 0, // pop_jackpot_slot
    //     '2' => 2, // pop_slots
    //     '66' => 3, // arcade
    //     '75' => 5, // card_games
    //     '96' => 8, // fixed_odds
    //     '100' => 8, // mini_games
    //     '298' => 3, // pop_scratch_cards,
    //     '315' => 8, // progressive_slot_machines,
    //     '372' => 8, // slot_machines,
    //     '484' => 8, // table_games,
    //     '500' => 8, // video_poker,
    // ];

    // Here Base From Provider Category ID
    private static $playtechProvider = [
        'slot' => 4,
        'casino' => 10,
        'crash' => 20,
        'table' => 21,
    ];

    private static $gameCategory = [
        'sports' => 1,
        'live_casino' => 2,
        'slot' => 3,
        'table' => 4,
        'fishing' => 5,
        'crash' => 6,
        'live' => 7,
        'others' => 8,
    ];

    public static $crashGameList = [
        224,
        229,
        232,
        233,
        235,
        236,
        254,
        241,
        242,
        261,
        407,
        469,
        272,
    ];
    // private static $CQ9 = [
    //     'slot' => 4,
    //     'casino' => 10,
    //     'crash' => 20,
    //     'table' => 21,
    // ];

    // private static $microgaming = [
    //     'slot' => 4,
    //     'casino' => 10,
    //     'crash' => 20,
    //     'table' => 21,
    // ];

    // private static $bigGaming = [
    //     'slot' => 4,
    //     'casino' => 10,
    //     'crash' => 20,
    //     'table' => 21,
    // ];

    // private static $vPowerHaventGet = [
    //     'slot' => 4,
    //     'casino' => 10,
    //     'crash' => 20,
    //     'table' => 21,
    // ];

    // private static $Ace333 = [
    //     'slot' => 4,
    //     'casino' => 10,
    //     'crash' => 20,
    //     'table' => 21,
    // ];

    public function __construct(string $baseUrl, string $operatorCode, string $agentKey)
    {
        $this->baseUrl = $baseUrl;
        $this->operatorCode = $operatorCode;
        $this->agentKey = $agentKey;
    }

    private function generateKey($qString)
    {
        $containsOperatorCode = str_contains($qString, $this->operatorCode);
        $combineString = ($containsOperatorCode ? $qString : ($this->operatorCode.$qString)).$this->agentKey;
        $md5String = md5($combineString);

        return strtoupper($md5String);
    }

    private function postRequest($endpoint, array $queries = [])
    {

        $queryString = count($queries) > 0 ? http_build_query($queries).'&' : '';

        $signature = $this->generateKey($queryString);

        return $this->httpPostRequest($this->baseUrl.'/launchGames.aspx', array_merge($queries, [
            'operatorcode' => $this->operatorCode,
            'signature' => $signature,
        ]))->throw();
    }

    private function getRequest($endpoint, array $queries = [], array $queries2 = [])
    {

        $queriesString = implode('', $queries);
        $signature = $this->generateKey($queriesString);

        return $this->httpGetRequest($this->baseUrl.$endpoint, array_merge($queries, $queries2, [
            'operatorcode' => $this->operatorCode,
            'signature' => $signature,
        ]))->throw();
    }

    public function getGameList($providerCode)
    {
        $response = $this->getRequest('/getGameList.aspx', ['providercode' => $providerCode]);
        $data = $response->json();

        if ($data['errCode'] != 0) {

            return [
                'status' => false,
                'message' => $data['errMsg'],
            ];
        }

        $gameList = array_map(function ($e) use ($providerCode) {

            $gameCodeField = $this->getGameCodeKey($providerCode);
            $gameNameField = $this->getGameNameKey($providerCode, $e);
            $gameCategoryField = $this->getGameCategoryKey($providerCode);

            $gameId = sprintf('%03s', $e[$gameCodeField]);

            // lower case and space replace to "_"
            $name = str_replace(' ', '_', strtolower($gameNameField));
            if ($providerCode == 'PR') {
                $imageUrl = 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/games/pragmatic_play/'.$name.'.png';
            } else {
                $imageUrl = 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/games/gsc/'.$providerCode.'/'.$name.'.jpg';
            }

            return array_merge($e, ['imageUrl' => $imageUrl, 'game_code' => $e[$gameCodeField], 'game_name' => $gameNameField, 'game_category_id' => $this->getGameProviderCategory($gameCodeField, $e[$gameCategoryField]), 'game_category_name' => $e[$gameCategoryField]]);
        }, $this->getGameListKey($providerCode, $data));

        return [
            'status' => true,
            'data' => $gameList,
        ];
    }

    public function getGameListKey($providerCode, $data)
    {
        switch ($providerCode) {
            case 'FR':
            case 'MP':
            case 'JK':
            case 'PR':
                return json_decode($data['gamelist'], true);
            case 'CQ':
                return json_decode($data['gamelist'], true)['data'];
            case 'JA':
                return json_decode($data['gamelist'], true)['Data'];
            case 'VP':
                return json_decode($data['gamelist'], true)['GameList'];
            default:
                break;
        }
    }

    public function getGameCodeKey($providerCode)
    {
        switch ($providerCode) {
            case 'FR':
                return 'Game Code';
            case 'MP':
                return 'gameCode';
            case 'CQ':
                return 'gamecode';
            case 'JA':
                return 'GameId';
            case 'JK':
                return 'GameCode';
            case 'VP':
                return 'GameID';
            case 'PR':
                return 'gameID';
            default:
                break;
        }
    }

    public function getGameNameKey($providerCode, $data)
    {
        switch ($providerCode) {
            case 'FR':
                return $data['name_EN'];
            case 'MP':
            case 'PR':
                return $data['gameName'];
            case 'CQ':
                return $data['gamename'];
            case 'JA':
                return $data['name']['en-US'];
            case 'JK':
                return $data['GameName'];
            case 'VP':
                return $data['GameName_EN'];
            default:
                break;
        }
    }

    public function getGameCategoryKey($providerCode)
    {
        switch ($providerCode) {
            case 'FR':
                return 'Game Type';
            case 'MP':
                return 'gameCategoryName';
            case 'CQ':
                return 'gametype';
            case 'JA':
                return 'GameCategoryId';
            case 'JK':
            case 'VP':
                return 'GameType';
            case 'PR':
                return 'typeDescription';
            default:
                break;
        }
    }

    public function createMember($user, $currentProductId, $productId)
    {
        $account = $user->uuid;
        $userProduct = UserProduct::where('user_id', $user->id)
            ->where('product_id', $productId);

        if ($userProduct->count() > 0) {

            // $userProduct->update(['product_id' => $productId]);

            return [
                'status' => false,
                'message' => 'User product exists',
            ];
        }

        $response = $this->getRequest('/createMember.aspx', [
            'username' => strtolower($account),
        ]);

        // fwsaaaaf
        // fwsRH2ym
        // RH2ym5gbbCjD
        // RH2ym5gbbCjE
        // RH2ym5gbbCjF

        $data = $response->json();

        if ($data['errCode'] == 0 || ($data['errCode'] == 82 && $data['errMsg'] == 'MEMBER_EXISTED')) {
            UserProduct::subscribeGame($user->id, $productId, $account);

            return [
                'status' => true,
            ];
        }

        return [
            'status' => false,
            'message' => $data['errMsg'],
        ];
    }

    // public function changePassword($user)
    // {
    //     $account = env('USER_ACCOUNT_PREFIX').$user->uuid;

    //     $response = $this->postRequest('/changePassword.aspx', [
    //         'providercode' => 'FR',
    //         'username' => $account,
    //         'password' => '',
    //         'opassword' => '',
    //     ]);

    //     $data = $response->json();

    //     if ($data['errorcode'] == 0) {
    //         return [
    //             'status' => true,
    //         ];
    //     }

    //     return [
    //         'status' => false,
    //         'data' => $data['data'],
    //         'message' => $data['errMsg'],
    //     ];
    // }

    private function checkTransaction($referenceId)
    {
        $response = $this->getRequest('/checkTransaction.ashx', [], ['referenceid' => $referenceId]);
        $data = $response->json();

        if ($data['errCode'] == 0) {
            return [
                'status' => true,
                'data' => $data['data'],
            ];
        }

        return [
            'status' => false,
            'message' => $data['errMsg'],
        ];
    }

    public function deposit($account, $amount, $providerCode)
    {
        $transactionId = str()->random(20);
        $deposit = $this->getRequest('/makeTransfer.aspx', [
            'amount' => $amount, // 0.5,
            'operatorcode' => $this->operatorCode,
            'password' => $account, // 'Abc12345',
            'providercode' => $providerCode,
            'referenceid' => $transactionId,
            'type' => 0,
            'username' => strtolower($account), // 'thor123',

        ]);

        $data = $deposit->json();

        if ($data['errCode'] == 0) {
            return [
                'status' => true,
            ];
        }

        // $response = $this->checkTransaction($transactionId);

        // if ($response['data']['status'] == "SUCCESS" && $response['data']['type'] == 0) {

        //     return [
        //         'status' => true,
        //         'data' => [
        //             'main_wallet' => $response['data']['amount'],
        //         ],
        //     ];
        // }

        return [
            'status' => false,
            'message' => $data['errMsg'],
        ];
    }

    public function withdraw($account, $providerCode)
    {
        $balanceRes = $this->getBalance($account, $providerCode);
        if (! $balanceRes['status']) {
            return ['status' => false];
        }

        $transactionId = str()->random(20);
        $deposit = $this->getRequest('/makeTransfer.aspx', [
            'amount' => $balanceRes['balance'],
            'operatorcode' => $this->operatorCode,
            'password' => $account, // 'Abc12345',
            'providercode' => $providerCode,
            'referenceid' => $transactionId,
            'type' => 1,
            'username' => strtolower($account), // 'thor123',

        ]);

        $data = $deposit->json();

        if ($data['errCode'] == 0) {
            return [
                'status' => true,
                'data' => [
                    'main_wallet' => $balanceRes['balance'],
                ],
            ];
        }

        // $response = $this->checkTransaction($transactionId);

        // if ($response['data']['status'] == "SUCCESS" && $response['data']['type'] == 1) {
        //     return [
        //         'status' => true,
        //         'data' => [
        //             'main_wallet' => $response['data']['amount'],
        //         ],
        //     ];
        // }

        return ['status' => false, 'message' => $data['errMsg']];
    }

    public function getBalance($account, $providerCode)
    {
        $response = $this->getRequest('/getBalance.aspx', [
            'operatorcode' => $this->operatorCode,
            'password' => $account, // 'Abc12345',
            'providercode' => $providerCode,
            'username' => strtolower($account), // 'thor123',
        ]);

        $data = $response->json();

        if ($data['errCode'] == 0) {
            return [
                'status' => true,
                'balance' => $data['balance'],
            ];
        }

        return ['status' => false, 'message' => $data['errMsg']];
    }

    public function getGameUrl($account, $gameId, $type, $providerCode)
    {
        if ($providerCode == 'JD') {
            $this->logout($account, $providerCode);
        }

        $response = $this->getRequest('/launchGames.aspx', [
            'password' => $account, // 'Abc12345',
            'providercode' => $providerCode,
            'type' => $type,
            'username' => strtolower($account), // 'thor123',
        ], [
            'lang' => 'en-US',
            'html5' => '1',
            'gameid' => $gameId,
            // 'blimit' =>
        ]);

        $data = $response->json();

        if ($data['errCode'] == 0) {
            return [
                'status' => true,
                'data' => $data['gameUrl'],
            ];
        }

        return [
            'status' => false,
            'message' => $data['errMsg'],
        ];
    }

    public function logout($account, $providerCode)
    {

        $response = $this->getRequest('/kickPlayerFromProduct.ashx', [
            'password' => $account,  // Abc12345,
            'providercode' => $providerCode,
            'username' => strtolower($account), // thor123,
        ]);

        $data = $response->json();

        if ($data['errCode'] == 0) {

            return [
                'status' => true,
            ];
        }

        return [
            'status' => false,
            'message' => $data['errMsg'],
        ];
    }

    protected function httpPostRequest(string $path, array $params): HttpResponse
    {
        $response = Http::asForm()->timeout(10)->post($path, $params);

        CurlLog::create([
            'endpoint' => $path ?? '',
            'request' => isset($params) ? (is_array($params) ? json_encode($params) : $params) : '',
            'response' => $response ?? '',
            'created_at' => now(),
        ]);

        return $response;
    }

    protected function httpGetRequest(string $path, array $params): HttpResponse
    {
        $response = Http::asForm()->timeout(10)->get($path, $params);

        CurlLog::create([
            'endpoint' => $path ?? '',
            'request' => isset($params) ? (is_array($params) ? json_encode($params) : $params) : '',
            'response' => $response ?? '',
            'created_at' => now(),
        ]);

        // if ($response->json('code') && $response->json('code') != 1000) {
        //     abort(Response::HTTP_UNPROCESSABLE_ENTITY, $response->json('message'));
        // } elseif (! $response->ok()) {
        //     abort(Response::HTTP_FAILED_DEPENDENCY, __('CQ9 Provider server error.'));
        // }

        return $response;
    }

    public static function getGameProviderCategory($gameId, $gameCategoryType)
    {
        $gameCategoryType = strtoupper($gameCategoryType);
        switch ($gameCategoryType) {
            // // 1 sports
            // case 'POP JACKPOT SLOTS':
            // case 'POP SLOTS':
            // case 'PROGRESSIVE SLOT MACHINES':
            // case 'SLOT MACHINES':
            //     return self::$gameCategory['sports'];
            // 2 Live Casino
            case 'VIDEO POKERS':
            case 'LIVE DEALER':
            case 'ECASINO':
            case '2':
            case 'BLACKJACK':
            case 'BACCARAT NEW':
            case 'BACCARAT':
                return self::$gameCategory['live_casino'];
            case '8':
                return self::$gameCategory[in_array($gameId, self::$crashGameList) ? 'crash' : 'table'];
                // 3 Slot
            case 'POP JACKPOT SLOTS':
            case 'POP SLOTS':
            case 'PROGRESSIVE SLOT MACHINES':
            case 'SLOT MACHINES':
            case 'SLOTS':
            case 'SLOT':
            case '1':
            case '3':
            case 'CLASSIC SLOTS':
            case 'VIDEO SLOTS':
                return self::$gameCategory['slot'];
                // 4 Table
            case 'TABLE GAMES':
            case 'CARD GAMES':
            case 'POP SCRATCH CARDS':
            case 'MINI GAMES':
            case 'TABLE':
            case '1':
                return self::$gameCategory['table'];
                // 5 Fishing
            case 'FISH HUNTING':
            case 'FISH':
            case '5':
            case 'FISHING':
                return self::$gameCategory['fishing'];
                // 6 Crash
            case 'ARCADE':
            case 'FIXED ODDS':
            case 'QI PAI':
            case 'INTERACTIVE GAMES':
            case 'SCRATCH CARDS':
            case 'CRASH':
            case 'BINGO':
            case 'MULTIPLAYER':
                return self::$gameCategory['crash'];
                // 7 Live
            case 'VIDEO BINGO':
            case 'POP SLOTS':
            case 'PROGRESSIVE SLOT MACHINES':
            case 'SLOT MACHINES':
            case 'ROULETTE':
            case 'LIVE GAMES':
            case 'VIDEO':
                return self::$gameCategory['live'];
            default:
                return self::$gameCategory['others'];
        }
    }

    public function checkPromotionTurnoverAchieved($account, $productId, $userPromotion)
    {
        $startTime = $userPromotion->created_at;
        $endTime = now();

        $betLogService = new BetLogService;
        $transaction = $betLogService->getBetTransactionByAccount($startTime, $endTime, $account, $productId);

        if (isset($transaction['data'])) {
            $meta = $transaction['data']['meta'];
            $achievedTurnover = $transaction['data']['total_turnover'];
            $targetTurnover = $userPromotion->target_turnover;
            $isTurnoverHit = $achievedTurnover >= $targetTurnover;

            $userPromotion->update([
                'meta_transaction' => $meta,
                'achieved_turnover' => $achievedTurnover,
            ]);

            return [
                'is_turnover_hit' => $isTurnoverHit,
                'achieved_turnover' => $achievedTurnover,
            ];
        }
    }
}
