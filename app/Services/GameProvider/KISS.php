<?php

namespace App\Services\GameProvider;

use App\Models\CurlLog;
use Illuminate\Http\Client\Response as HttpResponse;
use Illuminate\Support\Facades\Http;

class KISS
{
    protected string $baseUrl;

    protected string $baseUrl2;

    protected string $code;

    protected string $secret;

    private static $uri = 'lobbykiss://lobbykiss';

    private static $downloadUrl = 'https://bit.ly/918apk1q';

    public static $productId = 2012;

    private static $usernamePrefix = '01';

    public function __construct(string $baseUrl, string $baseUrl2, string $code, string $secret)
    {
        $this->baseUrl = $baseUrl;
        $this->baseUrl2 = $baseUrl2;
        $this->code = $code;
        $this->secret = $secret;
    }

    public function getCredentials($memberId)
    {
        $username = $this->getUsername($memberId);
        $password = $memberId;
        $gameUrl = self::$uri.'?account='.$username.'&password='.$password;

        return [
            'username' => $username,
            'password' => $password,
            'game_url' => $gameUrl,
            'download_url' => self::$downloadUrl,
        ];
    }

    public function addUser($memberId)
    {
        $username = $this->getUsername($memberId);
        $response = $this->request('/ashx/account/account.ashx', [
            'action' => 'AddPlayer',
            'agent' => 'i8fe888',
            'PassWd' => $memberId,
            'pwdtype' => 1,
            'UserAreaId' => 1,
            'userName' => $username,
            'Name' => 'FWTEST',
            'Tel' => '***********',
            'UserType' => 1,
        ])->json();

        return $response;

        if ($response && ($response['code'] ?? -99) == 0) {
            return $response;

            return ['status' => true];
        }

        return [
            'status' => false,
            'message' => $response['msg'] ?? '',
        ];
    }

    private function getUserInfo($memberId)
    {
        $username = $this->getUsername($memberId);
        $response = $this->request('/ashx/account/account.ashx', [
            'action' => 'getUserInfo',
            'userName' => $username,
        ], )->json();

        return $response;
    }

    public function getBalance($memberId)
    {
        $username = $this->getUsername($memberId);
        $response = $this->request('/ashx/account/account.ashx', [
            'action' => 'getUserInfo',
            'userName' => $username,
        ])->json();

        if (isset($response) && (isset($response['MoneyNum']))) {
            return $response['MoneyNum'];
        }
    }

    public function deposit($memberId, $amount)
    {
        return $this->transfer($memberId, $amount);
    }

    public function withdraw($memberId)
    {
        $balance = $this->getBalance($memberId) ?? 0;

        return $this->transfer($memberId, $balance, true);
    }

    private function transfer($memberId, $amount, $isWithdraw = false)
    {
        $initBalance = $this->getBalance($memberId);
        if (! isset($initBalance)) {
            return ['status' => false];
        }

        $username = $this->getUsername($memberId);
        $orderId = str()->random(50);

        try {
            $response = $this->request('/ashx/account/setScore.ashx', [
                'action' => 'setServerScore',
                'orderid' => $orderId,
                'userName' => $username,
                'scoreNum' => $isWithdraw ? -$amount : $amount,
                'ActionUser' => 'API',
                'ActionIp' => '***********',
            ])->json();
        } catch (\Throwable $th) {
        }

        if (isset($response) && isset($response['money'])) {
            return [
                'status' => true,
                'amount' => $initBalance,
            ];
        }

        $finalBalance = $this->getBalance($memberId);
        if (! isset($finalBalance)) {
            return ['status' => false];
        }

        $expectedAmount = $initBalance + $amount;
        if ($expectedAmount != $finalBalance) {
            return ['status' => false];
        }

        return [
            'status' => true,
            'amount' => $initBalance,
        ];
    }

    private function getUsername($memberId)
    {
        return self::$usernamePrefix.$memberId;
    }

    public function getTurnover($memberId, $startTime, $turnoverOffset, $returnOffset)
    {
        $username = $this->getUsername($memberId);
        $achievedTurnover = 0;
        $returnAmount = 0;
        $response = $this->request('/ashx/AccountReport.ashx', [
            'pageIndex' => 'getUserInfo',
            'pageSize' => 1000,
            'userName' => $username,
            'sDate' => date_format($startTime, 'Y-m-d 00:00:00'),
            'eDate' => date_format(now(), 'Y-m-d H:i:s'),
        ], true)->json();

        if (isset($response) && isset($response['results'])) {
            foreach ($response['results'] as $result) {
                $achievedTurnover += $result['press'];
                $returnAmount += $result['press'] - $result['win'];
            }
        }

        $achievedTurnover -= $turnoverOffset;
        $returnAmount -= $returnOffset;

        return [
            'achieved_turnover' => $achievedTurnover > 0 ? $achievedTurnover : 0,
            'return_amount' => $returnAmount > 0 ? $returnAmount : 0,
        ];
    }

    private function getSigningKey($username, $time)
    {
        $key = strtoupper(md5(strtolower($this->code.$username.$time.$this->secret)));

        return $key;
    }

    private function request(string $path, array $params = [], $isUrl2 = false): HttpResponse
    {
        $username = $params['userName'];
        $time = now()->getTimestampMs();
        $sign = $this->getSigningKey($username, $time);
        $queries = http_build_query([
            'time' => $time,
            'authcode' => $this->code,
            'sign' => $sign,
        ]);
        $base = $isUrl2 ? $this->baseUrl2 : $this->baseUrl;
        $url = $base.$path.'?'.$queries;

        $response = Http::asForm()->timeout(30)->post($url, $params);

        CurlLog::create([
            'endpoint' => $url,
            'request' => isset($params) ? (is_array($params) ? json_encode($params) : $params) : '',
            'response' => $response ?? '',
            'created_at' => now(),
        ]);

        return $response;
    }
}
