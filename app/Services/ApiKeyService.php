<?php

namespace App\Services;

use App\Models\User;
use App\Traits\ApiKeyTrait;
use DB;

class ApiKeyService
{
    use ApiKeyTrait;

    public static function generateApiKey(array $params, int $userId): array
    {
        $user = User::find($userId);

        $data = [
            'uid' => $user->id,
            'timestamp' => time(),
            'rand' => rand(0, 9).rand(0, 9).rand(0, 9).rand(0, 9).rand(0, 9).rand(0, 9),
            'expired_on' => $params['expired_on'] ?? null,
        ];

        $apiKey = self::generateEncryptedApiKey(['data' => $data]);

        DB::transaction(function () use ($user, $apiKey) {
            // Soft delete
            $user->apiKeys()->delete();
            $user->apiKeys()->create([
                'api_key_iv' => $apiKey['iv'],
                'api_key' => $apiKey['api_key'],
                'created_at' => now(),
            ]);
        });

        return [
            'api_key' => $apiKey['api_key'],
        ];
    }
}
