<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Lang;
use App\Models\Permissions;
use App\Models;
use App\Models\User;
use App\Models\UserDetail;
use App\Models\SystemSetting;
use App\Models\UserDevice;
use App\Traits;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\SoftDeletes;

class AuthService
{
    use SoftDeletes;

    public static function login(array $params = [], $guard)
    {

        $column = ($guard == 'admin') ? 'username' : 'username';
        $auth = null;

        if (!empty($params['login_token'])) {
            $loginDetail = Traits\CacheTrait::getCache($params['login_token']);

            if (!empty($loginDetail)) {
                $userDetail = User::find($loginDetail->userId);
                $auth = Auth::guard($guard)->login(($userDetail ?? null));
                Traits\CacheTrait::clearCacheWithoutUid($params['login_token']);
            }
            unset($params['login_token']);
        } else {
            if (defined('MODULE') &&  MODULE == 'app') {
                $username = User::where($column, $params[$column])->first()['username'] ?? null;
                $loginParams['username'] = $username;
                $loginParams['password'] = $params['password'];
                $auth = auth($guard)->setTTL(env('USER_JWT_TTL'))->attempt($loginParams);
                // $auth = auth($guard)->setTTL(env('USER_JWT_TTL'))->attempt($params);
                // $userID = User::where($column, $params[$column])->first()['id'] ?? null;
                // $userDetail = User::find($userID) ?? null;
                // $auth = Auth::guard($guard)->setTTL(env('USER_JWT_TTL'))->login(($userDetail ?? null));
            } else {
                $auth = auth($guard)->attempt($params);
            }
        }

        if (empty($auth)) {
            // failed Login
            $userData = DB::table('users')->where($column, ($params[$column] ?? null))->first();

            DB::table('users')->where($column, ($params[$column] ?? null))->increment('fail_login', 1);
            if ($userData) {
                if ($userData->fail_login >= config('users.availableFailedLogin')) {
                    DB::table('users')->where($column, ($params[$column] ?? null))->update(['disabled' => 1]);
                }
            }

            abort(400, json_encode([$column => Lang::get('lang.invalid-credentials')]));
        }

        $userData = auth($guard)->user();

        // Update the last_active_at column
        $userData->update(['last_active_at' => now()]);
        
        if (!$userData->activated) {
            abort(400, json_encode([$column => Lang::get('lang.account-not-activate')]));
        }

        if ($userData->suspended != 0) {
            abort(400, json_encode([$column => Lang::get('lang.account-suspended')]));
        }

        if ($userData->disabled != 0) {
            abort(400, json_encode([$column => Lang::get('lang.account-disabled')]));
        }

        $previousToken = DB::table('jwt_token')
            ->select('id', 'uid', 'token')
            ->where('guard', $guard)
            ->where('uid', $userData->id)
            ->where(DB::raw('now()'), '<', DB::raw('expiry_at'))
            ->first();

        if (! empty($previousToken)) {
            Auth::guard($guard)->setToken($previousToken->token)->invalidate(true);
        }

        $tokenExpiry = auth($guard)->factory()->getTTL() * 60;

        DB::table('jwt_token')
            ->upsert(
                [
                    'guard' => $guard,
                    'uid' => $userData->id,
                    'token' => $auth,
                    'expiry_at' => DB::raw("date_add(now(),interval $tokenExpiry second)"),
                ],
                ['login'],
                ['token', 'expiry_at']
            );

        DB::table('users')->where('id', $userData->id)->update(['fail_login' => 0]);

        if ($guard == 'admin') {
            $rolePermissions = $userData->getRoles;
            $permissions = Permissions::when(
                $userData->is_master,
                function ($q) {
                    return $q->where('master_disabled', 0);
                },
                function ($q) use ($rolePermissions) {
                    $q->whereIN('id', ($rolePermissions->permissions_id ?? []));
                    $q->where('disabled', 0);
                    return $q->where('master_disabled', 0);
                }
            )
                ->select('id', 'name', 'parent_id', 'api_url', 'level')->get();
        }

        $isDashboardV2 = 0;
        if ($guard == 'users') {
            $exists = UserDetail::where(['user_id' => $userData->id, 'name' => 'reset_password'])->first();
            if (isset($exists)) $exists->delete();
            $v2Users = json_decode(SystemSetting::where('name', 'v2AuthorizedUser')->whereNull('deleted_at')->first()['value'] ?? '') ?? [];
            if (in_array($userData->username, $v2Users)) {
                $isDashboardV2 = 1;
            }
        }

        // Get user device token
        if (isset($params['firebase_fwc_token'])) {
            UserDevice::updateUserDToken([
                'user_id' => $userData->id,
                'token' => $params['firebase_fwc_token'],
            ]);
        }

        return [
            'data' => [
                'access_token' => $auth,
                'token_type' => 'bearer',
                'expires_in' => $tokenExpiry,
                'user' => $guard == 'admin' ? Models\Admin::getProfile($userData->id) : Models\User::getProfile($userData->id, true),
                // 'memo' => Models\Memo::getLoginMemo($userData),
                'is_dashboard_v2' => $isDashboardV2,
                'permission' => $permissions ?? [],
            ],
        ];
    }

    public static function logout($guard): string
    {
        UserDevice::updateUserDToken(['user_id' => auth()->user()->id]);

        auth($guard)->logout();
        return 'Logout success.';
    }
}
