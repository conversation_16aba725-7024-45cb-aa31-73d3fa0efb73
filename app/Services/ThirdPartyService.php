<?php

namespace App\Services;

use App\Models;
use App\Models\ExTransfer;
use App\Models\Product;
use App\Models\User;
use App\Models\UserProduct;
use App\Models\UserPromotion;
use App\Services\GameProvider\CQ9;
use App\Services\GameProvider\GSC;
use App\Services\GameProvider\JILI;
use App\Services\GameProvider\JK;
use App\Services\GameProvider\KISS;
use App\Services\GameProvider\MEGA;
use App\Services\GameProvider\MT;
use App\Services\GameProvider\PUSSY;
use App\Traits;
use App\Traits\DecimalTrait;

class ThirdPartyService
{
    public function __construct() {}

    public function TK8($params, $product)
    {
        $result = Traits\OCTK8Trait::postOC($params, $params['curl_type']);

        switch ($params['curl_type']) {
            case 'register':
                if (isset($result) && $result['status'] == true) {
                    $update = ['status' => Models\UserProduct::$status['success']];
                    if ($product['name'] == 'TK8' || $product['name'] == 'FMT') {
                        $update['member_id'] = $params['account'];
                    }
                } else {
                    $update = ['status' => Models\UserProduct::$status['failed']];
                }
                Models\UserProduct::where([
                    'member_id' => $params['account'],
                    'product_id' => $params['product_id'],
                    'status' => Models\UserProduct::$status['pending'],
                ])->update($update);
                break;

            case 'callback/deposit':
                if (isset($result) && ($result['status'] == true)) {
                    $dateTime = date('Y-m-d H:i:s');
                    $updateDepositParams = [
                        'id' => $params['deposit_id'] ?? 0,
                        'status' => 'approved',
                        'datetime' => $dateTime,
                    ];
                    $updateDepositRes = Models\Deposit::updateDeposit($updateDepositParams);

                    $update = ['status' => Models\ExTransfer::$status['confirmed']];
                    if ($product['name'] == 'TK8') {
                        if (isset($result['data']['transaction_id'])) {
                            $update['ref_tx_id'] = $result['data']['transaction_id'];
                        }
                        // $update['reference'] = $result['data']['reference'];
                    }

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'in', $exTransfer['amount'], $result['data']['current_balance'] ?? null);

                    $datetime = date('Y-m-d H:i:s');
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', Models\User::$userType['internal-account'])->first()->id;
                    Models\CreditTransaction::insertTransaction($exTransfer['user_id'], $internalId, $exTransfer['user_id'], $exTransfer['creditType']['type'], $exTransfer['amount'], 'ex-transfer-out', $exTransfer['belong_id'], $exTransfer['belong_id'], null, $datetime, $exTransfer['transaction_id'], null, null, $exTransfer['id'], null, false);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                    $exTransfer = Models\ExTransfer::with(['creditType', 'user'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    if (($result['refund'] ?? 0) == 0) {
                        $eventDescription = "Cash In Failed\n";
                        $eventDescription .= ($exTransfer->user->username ?? '-')."\n";
                        $eventDescription .= DecimalTrait::setDecimal($exTransfer->amount ?? 0)."\n";
                        $eventDescription .= ($exTransfer->transaction_id ?? '-')."\n";
                        $eventDescription .= (date('Y-m-d H:i:s', strtotime($exTransfer->created_at)) ?? '-')."\n";
                        Traits\TelegramTrait::sendTelegram(null, $eventDescription);
                    }
                    // else{
                    //     // To change status for refunded
                    //     $update = ['status' => ExTransfer::$status['refunded'], "res_data" => $result["msg"] ?? null];
                    //     $internalId = User::where('username','exTransfer')->where('user_type',User::$userType['internal-account'])->first()->id;

                    //     CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $exTransfer['amount'], "ex-transfer-refund", $exTransfer['belong_id'], $exTransfer['belong_id'], null, date("Y-m-d H:i:s"), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                    // }
                }
                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;

            case 'deposit':
                if (isset($result) && ($result['status'] == true)) {
                    $update = ['status' => Models\ExTransfer::$status['confirmed']];
                    if ($product['name'] == 'TK8') {
                        $update['ref_tx_id'] = $result['data']['transaction_id'];
                        $update['reference'] = $result['data']['reference'];
                    }

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'in', $exTransfer['amount'], $result['data']['current_balance'] ?? null);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                    $exTransfer = Models\ExTransfer::with(['creditType', 'user'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    if (($result['refund'] ?? 0) == 0) {
                        $eventDescription = "Cash In Failed\n";
                        $eventDescription .= ($exTransfer->user->username ?? '-')."\n";
                        $eventDescription .= DecimalTrait::setDecimal($exTransfer->amount ?? 0)."\n";
                        $eventDescription .= ($exTransfer->transaction_id ?? '-')."\n";
                        $eventDescription .= (date('Y-m-d H:i:s', strtotime($exTransfer->created_at)) ?? '-')."\n";
                        Traits\TelegramTrait::sendTelegram(null, $eventDescription);
                    } else {
                        // To change status for refunded
                        $update = ['status' => Models\ExTransfer::$status['refunded'], 'res_data' => $result['msg'] ?? null];
                        $internalId = Models\User::where('username', 'exTransfer')->where('user_type', Models\User::$userType['internal-account'])->first()->id;

                        Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $exTransfer['amount'], 'ex-transfer-refund', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                    }
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;

            case 'withdraw':
                if (isset($result) && $result['status'] == true) {
                    $update = ['amount' => $result['data']['witdraw_amount'] ?? 0, 'receivable_amount' => $result['data']['witdraw_amount'] ?? 0, 'status' => ExTransfer::$status['confirmed']];
                    if ($product['name'] == 'TK8') {
                        $update['ref_tx_id'] = $result['data']['transaction_id'];
                        $update['reference'] = $result['data']['reference'];
                    }

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;
                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $result['data']['witdraw_amount'], 'ex-transfer-in', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);

                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'out', $result['data']['witdraw_amount'], $result['data']['current_balance'] ?? null);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;
        }
    }

    public function MT($params, $product)
    {
        $user = User::find($params['user_id']);

        switch ($params['curl_type']) {
            case 'register':
                // $result = resolve(MT::class)->createUser($params['user_id']);
                break;
            case 'deposit':
                $result = resolve(MT::class)->deposit($user->uuid, $params['amount']);
                if (isset($result) && ($result['status'] == true)) {
                    $update = ['status' => Models\ExTransfer::$status['confirmed']];

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'in', $exTransfer['amount'], $result['data']['amount'] ?? null);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                    $exTransfer = Models\ExTransfer::with(['creditType', 'user'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    if (($result['refund'] ?? 0) == 0) {
                        $eventDescription = "Cash In Failed\n";
                        $eventDescription .= ($exTransfer->user->username ?? '-')."\n";
                        $eventDescription .= DecimalTrait::setDecimal($exTransfer->amount ?? 0)."\n";
                        $eventDescription .= ($exTransfer->transaction_id ?? '-')."\n";
                        $eventDescription .= (date('Y-m-d H:i:s', strtotime($exTransfer->created_at)) ?? '-')."\n";
                        Traits\TelegramTrait::sendTelegram(null, $eventDescription);
                    } else {
                        // To change status for refunded
                        $update = ['status' => Models\ExTransfer::$status['refunded'], 'res_data' => $result['msg'] ?? null];
                        $internalId = Models\User::where('username', 'exTransfer')->where('user_type', Models\User::$userType['internal-account'])->first()->id;

                        Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $exTransfer['amount'], 'ex-transfer-refund', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                    }
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;
            case 'withdraw':
                $result = resolve(MT::class)->withdraw($user->uuid, $user->uuid);
                if (isset($result) && $result['status'] == true) {
                    $update = ['amount' => $result['data']['amount'] ?? 0, 'receivable_amount' => $result['data']['amount'] ?? 0, 'status' => ExTransfer::$status['confirmed']];
                    // if ($product['name'] == 'MT') {
                    //     $update['ref_tx_id'] = $result['data']['transaction_id'];
                    //     $update['reference'] = $result['data']['reference'];
                    // }

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;
                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $result['data']['amount'], 'ex-transfer-in', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);

                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'out', $result['data']['amount'], $result['data']['amount'] ?? null);

                    Models\AccountBalance::updateBalance($params['user_id'], $result['data']['amount']);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;
        }
    }

    public function CQ9($params, $product)
    {
        $user = User::find($params['user_id']);

        switch ($params['curl_type']) {
            case 'register':
                $result = resolve(CQ9::class)->createUser($user);
                break;
            case 'deposit':
                $result = resolve(CQ9::class)->deposit($user, $params['amount'], $params['remark']);
                if (isset($result) && ($result['status'] == true)) {
                    $update = ['status' => Models\ExTransfer::$status['confirmed']];

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'in', $exTransfer['amount'], $result['data']['amount'] ?? null);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                    $exTransfer = Models\ExTransfer::with(['creditType', 'user'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    if (($result['refund'] ?? 0) == 0) {
                        $eventDescription = "Cash In Failed\n";
                        $eventDescription .= ($exTransfer->user->username ?? '-')."\n";
                        $eventDescription .= DecimalTrait::setDecimal($exTransfer->amount ?? 0)."\n";
                        $eventDescription .= ($exTransfer->transaction_id ?? '-')."\n";
                        $eventDescription .= (date('Y-m-d H:i:s', strtotime($exTransfer->created_at)) ?? '-')."\n";
                        Traits\TelegramTrait::sendTelegram(null, $eventDescription);
                    } else {
                        // To change status for refunded
                        $update = ['status' => Models\ExTransfer::$status['refunded'], 'res_data' => $result['msg'] ?? null];
                        $internalId = Models\User::where('username', 'exTransfer')->where('user_type', Models\User::$userType['internal-account'])->first()->id;

                        Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $exTransfer['amount'], 'ex-transfer-refund', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                    }
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;
            case 'withdraw':
                $result = resolve(CQ9::class)->withdraw($user, $params['remark']);
                if (isset($result) && $result['status'] == true) {
                    $update = ['amount' => $result['data']['amount'] ?? 0, 'receivable_amount' => $result['data']['amount'] ?? 0, 'status' => ExTransfer::$status['confirmed']];

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;
                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $result['data']['amount'], 'ex-transfer-in', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);

                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'out', $result['data']['amount'], $result['data']['amount'] ?? null);

                    Models\AccountBalance::updateBalance($params['user_id'], $result['data']['amount']);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;
        }
    }

    public function JK($params, $productData)
    {
        $jk = resolve(JK::class);
        $user = User::find($params['user_id']);

        switch ($params['curl_type']) {
            case 'register':
                // $result = resolve(JK::class)->createUser($user);
                break;
            case 'deposit':
                $jk->getLoginUrl($params['account'], $params['amount'], $user->store->jk_agent_id, false);
                $result = $jk->deposit($params['amount']);
                if (isset($result) && ($result['status'] == true)) {
                    $update = ['status' => Models\ExTransfer::$status['confirmed']];

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'in', $exTransfer['amount'], $result['data']['main_wallet'] ?? null);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                    $exTransfer = Models\ExTransfer::with(['creditType', 'user'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    if (($result['refund'] ?? 0) == 0) {
                        $eventDescription = "Cash In Failed\n";
                        $eventDescription .= ($exTransfer->user->username ?? '-')."\n";
                        $eventDescription .= DecimalTrait::setDecimal($exTransfer->amount ?? 0)."\n";
                        $eventDescription .= ($exTransfer->transaction_id ?? '-')."\n";
                        $eventDescription .= (date('Y-m-d H:i:s', strtotime($exTransfer->created_at)) ?? '-')."\n";
                        Traits\TelegramTrait::sendTelegram(null, $eventDescription);
                    } else {
                        // To change status for refunded
                        $update = ['status' => Models\ExTransfer::$status['refunded'], 'res_data' => $result['msg'] ?? null];
                        $internalId = Models\User::where('username', 'exTransfer')->where('user_type', Models\User::$userType['internal-account'])->first()->id;

                        Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $exTransfer['amount'], 'ex-transfer-refund', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                    }
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;
            case 'withdraw':
                $jk = resolve(JK::class);
                $activePromotion = UserPromotion::getUserPromotionByUserId($user->id, Product::$id['JK']);

                if ($activePromotion) {
                    $balance = $jk->getBalance($params['account'], $user->store->jk_agent_id);
                    $shouldWithdraw = $balance < 1 && $balance > 0;
                    $result = $jk->getWithdraw($params['account'], $user->store->jk_agent_id, ! $shouldWithdraw);

                    $promotion = $jk->checkPromotion($params['account'], $user->store->jk_agent_id, $params['user_id']);
                    if (isset($promotion)) {
                        if ($promotion['data']['remaining_turnover'] > 0) {
                            $result = $jk->getWithdraw($params['account'], $user->store->jk_agent_id, true);
                        } else {
                            $result = $jk->getWithdraw($params['account'], $user->store->jk_agent_id, false);
                        }

                        if ($promotion['data']['archived_turnover'] > 0) {
                            UserPromotion::updateUserPromotion([
                                'achieved_turnover' => $promotion['data']['archived_turnover'],
                                'game_return_amount' => $promotion['data']['return_amount'],
                                'user_id' => $params['user_id'],
                            ]);

                            // Probably don't need this
                            // Max Withdrawal Cap
                            // if ($promotion['data']['max_withdraw_amount'] > 0) {
                            //     $result['data']['main_wallet'] = $result['data']['main_wallet'] > $promotion['data']['max_withdraw_amount'] ? $promotion['data']['max_withdraw_amount'] : $result['data']['main_wallet'];
                            // }
                        }
                    }
                } else {
                    $result = $jk->getWithdraw($params['account'], $user->store->jk_agent_id);
                }

                $incompletedPromotion = UserPromotion::getIncompletedUserPromotionByUserId($user->id, Product::$id['JK']);
                $completedPromotion = UserPromotion::getCompletedUserPromotionByUserId($user->id, Product::$id['JK']);
                $cancelledPromotion = UserPromotion::getCancelledUserPromotionByUserId($user->id, Product::$id['JK']);

                // $result = $jk->getWithdraw($params['account'], $user->store->jk_agent_id);
                if (isset($result) && $result['status'] == true) {
                    $amount = $result['data']['main_wallet'] ?? 0;

                    if ($incompletedPromotion) {
                        $burnAmount = $amount;
                        $amount = 0;

                        UserPromotion::claimUserPromotion($incompletedPromotion, $burnAmount, true);
                    } elseif ($completedPromotion) {
                        $oriAmount = $amount;
                        $amount = $oriAmount > $completedPromotion->max_withdraw_amount ? $completedPromotion->max_withdraw_amount : $oriAmount;
                        $burnAmount = $oriAmount - $amount;

                        UserPromotion::claimUserPromotion($completedPromotion, $burnAmount);
                    } elseif ($cancelledPromotion) {
                        $bonusAmount = $cancelledPromotion->bonus_amount;
                        $amount -= $bonusAmount;
                        $burnAmount = $bonusAmount;

                        UserPromotion::claimUserPromotion($cancelledPromotion, $burnAmount);
                    }

                    $update = ['amount' => $amount, 'receivable_amount' => $amount, 'status' => ExTransfer::$status['confirmed']];
                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;
                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $amount, 'ex-transfer-in', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);

                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'out', $amount, $amount);

                    Models\AccountBalance::updateBalance($params['user_id'], $amount);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;
            case 'refund':
                $result = resolve(JK::class)->deposit($params['amount']);
                if (isset($result) && $result['status'] == true) {
                    $update = ['amount' => $result['data']['main_wallet'] ?? 0, 'receivable_amount' => $result['data']['main_wallet'] ?? 0, 'status' => ExTransfer::$status['confirmed']];

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;
                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $result['data']['main_wallet'], 'ex-transfer-in', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);

                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'out', $result['data']['main_wallet'], $result['data']['main_wallet'] ?? null);

                    Models\AccountBalance::updateBalance($params['user_id'], $result['data']['main_wallet']);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;
        }
    }

    public function JILI($params, $product)
    {
        $user = User::find($params['user_id']);
        $account = UserProduct::where('user_id', $user->id)
            ->where('product_id', JILI::$productId)
            ->first()
            ->member_id;

        switch ($params['curl_type']) {
            case 'register':
                $result = resolve(JILI::class)->createMember($user);
                break;
            case 'deposit':
                $result = resolve(JILI::class)->deposit($account, $params['amount']);

                if (isset($result) && ($result['status'] == true)) {
                    $update = ['status' => Models\ExTransfer::$status['confirmed']];

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'in', $exTransfer['amount'], $result['data']['main_wallet'] ?? null);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                    $exTransfer = Models\ExTransfer::with(['creditType', 'user'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    // To change status for refunded
                    $update = ['status' => Models\ExTransfer::$status['refunded'], 'res_data' => $result['msg'] ?? null];
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', Models\User::$userType['internal-account'])->first()->id;

                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $exTransfer['amount'], 'ex-transfer-refund', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;
            case 'withdraw':
                $result = resolve(JILI::class)->withdraw($account);

                if (isset($result) && $result['status']) {
                    $update = ['amount' => $result['data']['main_wallet'] ?? 0, 'receivable_amount' => $result['data']['main_wallet'] ?? 0, 'status' => ExTransfer::$status['confirmed']];

                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;
                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $result['data']['main_wallet'], 'ex-transfer-in', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);

                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'out', $result['data']['main_wallet'], $result['data']['main_wallet'] ?? null);

                    Models\AccountBalance::updateBalance($params['user_id'], $result['data']['main_wallet']);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;
        }
    }

    public function GSC($params, $product)
    {
        $userId = $params['user_id'];
        $user = User::find($userId);
        $account = UserProduct::where('user_id', $user->id)
            ->where('product_id', $product->id)
            ->first()
            ->member_id;
        $amount = $params['amount'];

        $gsc = resolve(GSC::class);

        switch ($params['curl_type']) {
            case 'register':
                $result = $gsc->createMember($user);
                break;
            case 'deposit':
                // $result = $gsc->deposit($account, $params['amount'], $product->provider_code);
                //
                // if (isset($result) && ($result['status'] == true)) {
                //     $update = ['status' => Models\ExTransfer::$status['confirmed']];
                //     $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                //     Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'in', $exTransfer['amount'], $result['data']['main_wallet'] ?? null);
                // } else {
                //     $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                //     $exTransfer = Models\ExTransfer::with(['creditType', 'user'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                //     // To change status for refunded
                //     $update = ['status' => Models\ExTransfer::$status['refunded'], 'res_data' => $result['msg'] ?? null];
                //     $internalId = Models\User::where('username', 'exTransfer')->where('user_type', Models\User::$userType['internal-account'])->first()->id;
                //
                //     Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $exTransfer['amount'], 'ex-transfer-refund', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                // }
                //
                // Models\ExTransfer::where([
                //     'id' => $params['ex_transfer_id'] ?? 0,
                // ])->update($update);

                $initBalance = $gsc->getBalance($account, $product->provider_code);
                $initBalanceErr = ! isset($initBalance) || ! $initBalance['status'];
                $expectedAmount = $initBalanceErr ? -1 : $initBalance['balance'] + $amount;

                if (! $initBalanceErr) {
                    $result = $gsc->deposit($account, $amount, $product->provider_code);
                    $finalBalance = $gsc->getBalance($account, $product->provider_code);
                }

                $resultErr = ! isset($result) || ! $result['status'];
                $finalBalanceErr = ! isset($finalBalance) || ! $finalBalance['status'];

                if ($initBalanceErr || ($resultErr && ($finalBalanceErr || $finalBalance['balance'] != $expectedAmount))) {
                    // $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                    // To change status for refunded
                    $update = ['status' => Models\ExTransfer::$status['refunded'], 'res_data' => $result['msg'] ?? null];
                    $exTransfer = Models\ExTransfer::with(['creditType', 'user'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', Models\User::$userType['internal-account'])->first()->id;
                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $exTransfer['amount'], 'ex-transfer-refund', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['confirmed']];
                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'in', $exTransfer['amount'], $result['data']['main_wallet'] ?? null);
                }

                Models\ExTransfer::where(['id' => $params['ex_transfer_id'] ?? 0])->update($update);
                break;
            case 'withdraw':
                $activePromotion = UserPromotion::getUserPromotionByUserId($user->id, $product->id);

                if ($activePromotion) {
                    $balance = $gsc->getBalance($account, $product->provider_code)['balance'];
                    $isPromotionIncomplete = $balance < 1;
                    $turnoverAchieved = $gsc->checkPromotionTurnoverAchieved($account, $product->id, $activePromotion);

                    if ($turnoverAchieved) {
                        if ($turnoverAchieved['achieved_turnover'] > 0 && ($isPromotionIncomplete || ($turnoverAchieved['is_turnover_hit'] ?? false))) {
                            $result = $gsc->withdraw($account, $product->provider_code);
                        } else {
                            $result = [
                                'status' => true,
                                'data' => ['main_wallet' => 0],
                            ];
                        }

                        UserPromotion::updateUserPromotion([
                            'achieved_turnover' => $turnoverAchieved['achieved_turnover'],
                            'game_return_amount' => $balance,
                            'user_id' => $userId,
                            'is_promotion_incomplete' => $isPromotionIncomplete,
                        ]);
                    }
                } else {
                    $result = $gsc->withdraw($account, $product->provider_code);
                }

                $incompletedPromotion = UserPromotion::getIncompletedUserPromotionByUserId($user->id, $product->id);
                $completedPromotion = UserPromotion::getCompletedUserPromotionByUserId($user->id, $product->id);
                $cancelledPromotion = UserPromotion::getCancelledUserPromotionByUserId($user->id, $product->id);

                if (isset($result) && $result['status']) {
                    $amount = $result['data']['main_wallet'] ?? 0;

                    if ($incompletedPromotion) {
                        $burnAmount = $amount;
                        $amount = 0;

                        UserPromotion::claimUserPromotion($incompletedPromotion, $burnAmount, true);
                    } elseif ($completedPromotion) {
                        $oriAmount = $amount;
                        $amount = $oriAmount > $completedPromotion->max_withdraw_amount ? $completedPromotion->max_withdraw_amount : $oriAmount;
                        $burnAmount = $oriAmount - $amount;

                        UserPromotion::claimUserPromotion($completedPromotion, $burnAmount);
                    } elseif ($cancelledPromotion) {
                        $bonusAmount = $cancelledPromotion->bonus_amount;
                        $amount -= $bonusAmount;
                        $burnAmount = $bonusAmount;

                        UserPromotion::claimUserPromotion($cancelledPromotion, $burnAmount);
                    }

                    $update = ['amount' => $amount, 'receivable_amount' => $amount, 'status' => ExTransfer::$status['confirmed']];
                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;
                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $amount, 'ex-transfer-in', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'out', $amount, $amount);

                    Models\AccountBalance::updateBalance($params['user_id'], $amount);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;
        }
    }

    public function KISS($params, $product)
    {
        $userId = $params['user_id'];
        $user = User::find($userId);
        $account = UserProduct::where('user_id', $user->id)
            ->where('product_id', $product->id)
            ->first()
            ?->member_id;
        $amount = $params['amount'];

        $kiss = resolve(KISS::class);

        switch ($params['curl_type']) {
            case 'register':
                $result = $kiss->addUser($account);
                break;
            case 'deposit':
                $result = $kiss->deposit($account, $amount);

                if (isset($result) && ($result['status'])) {
                    $update = ['status' => Models\ExTransfer::$status['confirmed']];
                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'in', $amount, $amount);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                    $exTransfer = Models\ExTransfer::with(['creditType', 'user'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    // To change status for refunded
                    $update = ['status' => Models\ExTransfer::$status['refunded'], 'res_data' => $result['msg'] ?? null];
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', Models\User::$userType['internal-account'])->first()->id;

                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $amount, 'ex-transfer-refund', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);

                Models\ExTransfer::where(['id' => $params['ex_transfer_id'] ?? 0])->update($update);
                break;
            case 'withdraw':
                // $result = $kiss->withdraw($account);
                //
                // if (isset($result) && $result['status']) {
                //     $amount = $result['amount'];
                //     $update = ['amount' => $amount, 'receivable_amount' => $amount, 'status' => ExTransfer::$status['confirmed']];
                //
                //     $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                //     $internalId = Models\User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;
                //     Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $amount, 'ex-transfer-in', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                //
                //     Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'out', $amount, $amount);
                //
                //     Models\AccountBalance::updateBalance($params['user_id'], $amount);
                // } else {
                //     $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                // }
                //
                // Models\ExTransfer::where([
                //     'id' => $params['ex_transfer_id'] ?? 0,
                // ])->update($update);
                // break;

                $activePromotion = UserPromotion::getUserPromotionByUserId($user->id, $product->id);

                if ($activePromotion) {
                    $balance = $kiss->getBalance($account);
                    $isPromotionIncomplete = $balance < 1;

                    $todayAchievedTurnover = UserPromotion::getTodayAchievedTurnoverByProduct($user->id, $product->id, $activePromotion->achieved_turnover);
                    $todayReturnAmount = UserPromotion::getTodayReturnAmountByProduct($user->id, $product->id, $activePromotion->game_return_amount);
                    $turnover = $kiss->getTurnover($account, $activePromotion->created_at, $todayAchievedTurnover, $todayReturnAmount);
                    $achievedTurnover = $turnover['achieved_turnover'];
                    $isTurnoverAchieved = $turnover['achieved_turnover'] >= $activePromotion->target_turnover;

                    if ($achievedTurnover > 0) {
                        if ($isPromotionIncomplete || $isTurnoverAchieved) {
                            $withdrew = $kiss->withdraw($account);
                        }

                        UserPromotion::updateUserPromotion([
                            'achieved_turnover' => $turnover['achieved_turnover'],
                            'game_return_amount' => $turnover['return_amount'],
                            'user_id' => $userId,
                            'is_promotion_incomplete' => $isPromotionIncomplete,
                        ]);
                    }
                } else {
                    $withdrew = $kiss->withdraw($account);
                }

                $incompletedPromotion = UserPromotion::getIncompletedUserPromotionByUserId($user->id, $product->id);
                $completedPromotion = UserPromotion::getCompletedUserPromotionByUserId($user->id, $product->id);
                $cancelledPromotion = UserPromotion::getCancelledUserPromotionByUserId($user->id, $product->id);

                if (isset($withdrew) && isset($withdrew['status'])) {
                    $amount = $withdrew['amount'] ?? 0;

                    if ($incompletedPromotion) {
                        $burnAmount = $amount;
                        $amount = 0;

                        UserPromotion::claimUserPromotion($incompletedPromotion, $burnAmount, true);
                    } elseif ($completedPromotion) {
                        $oriAmount = $amount;
                        $amount = $oriAmount > ($completedPromotion->max_withdraw_amount ?? INF) ? $completedPromotion->max_withdraw_amount : $oriAmount;
                        $burnAmount = $oriAmount - $amount;

                        UserPromotion::claimUserPromotion($completedPromotion, $burnAmount);
                    } elseif ($cancelledPromotion) {
                        $bonusAmount = $cancelledPromotion->bonus_amount;
                        $amount -= $bonusAmount;
                        $burnAmount = $bonusAmount;

                        UserPromotion::claimUserPromotion($cancelledPromotion, $burnAmount);
                    }

                    $update = ['amount' => $amount, 'receivable_amount' => $amount, 'status' => ExTransfer::$status['confirmed']];
                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;
                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $amount, 'ex-transfer-in', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'out', $amount, $amount);

                    Models\AccountBalance::updateBalance($params['user_id'], $amount);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;
        }
    }

    public function MEGA($params, $product)
    {
        $userId = $params['user_id'];
        $user = User::find($userId);
        $account = UserProduct::where('user_id', $user->id)
            ->where('product_id', $product->id)
            ->first()
            ?->member_id;
        $amount = $params['amount'];

        $mega = resolve(MEGA::class);

        // TODO: WIP
        if (! $account) {
            $res = $mega->createMemberAccount();
            $loginId = $res['login_id'] ?? '';
            $userProduct = UserProduct::create([
                'user_id' => $user->id,
                'product_id' => $product->id,
                'member_id' => $loginId,
            ]);
            $account = $userProduct->member_id;
        }

        switch ($params['curl_type']) {
            case 'register':
                // $result = $mega->createMemberAccount($account);
                break;

            case 'deposit':
                $result = $mega->deposit($account, $amount);

                if (isset($result) && ($result['status'])) {
                    $update = ['status' => Models\ExTransfer::$status['confirmed']];
                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'in', $amount, $amount);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                    $exTransfer = Models\ExTransfer::with(['creditType', 'user'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    // To change status for refunded
                    $update = ['status' => Models\ExTransfer::$status['refunded'], 'res_data' => $result['msg'] ?? null];
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', Models\User::$userType['internal-account'])->first()->id;

                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $amount, 'ex-transfer-refund', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);

                Models\ExTransfer::where(['id' => $params['ex_transfer_id'] ?? 0])->update($update);
                break;

            case 'withdraw':
                // $activePromotion = UserPromotion::getUserPromotionByUserId($user->id, $product->id);
                $balanceResponse = $mega->getBalance($account);

                if (! $balanceResponse['status']) {
                    break;
                }

                // if ($activePromotion) {
                // $balance = $balanceResponse['balance'];
                // $isPromotionIncomplete = $balance < 1;
                //
                // $todayAchievedTurnover = UserPromotion::getTodayAchievedTurnoverByProduct($user->id, $product->id, $activePromotion->achieved_turnover);
                // $todayReturnAmount = UserPromotion::getTodayReturnAmountByProduct($user->id, $product->id, $activePromotion->game_return_amount);
                // $turnover = $kiss->getTurnover($account, $activePromotion->created_at, $todayAchievedTurnover, $todayReturnAmount);
                // $achievedTurnover = $turnover['achieved_turnover'];
                // $isTurnoverAchieved = $turnover['achieved_turnover'] >= $activePromotion->target_turnover;
                //
                // if ($achievedTurnover > 0) {
                //     if ($isPromotionIncomplete || $isTurnoverAchieved) {
                //         $withdrew = $kiss->withdraw($account);
                //     }
                //
                //     UserPromotion::updateUserPromotion([
                //         'achieved_turnover' => $turnover['achieved_turnover'],
                //         'game_return_amount' => $turnover['return_amount'],
                //         'user_id' => $userId,
                //         'is_promotion_incomplete' => $isPromotionIncomplete,
                //     ]);
                // }
                // } else {
                $withdrawResponse = $mega->withdraw($account);
                // }

                // $incompletedPromotion = UserPromotion::getIncompletedUserPromotionByUserId($user->id, $product->id);
                // $completedPromotion = UserPromotion::getCompletedUserPromotionByUserId($user->id, $product->id);
                // $cancelledPromotion = UserPromotion::getCancelledUserPromotionByUserId($user->id, $product->id);
                //
                if ($withdrawResponse['status']) {
                    $amount = $withdrawResponse['balance'] ?? 0;

                    //     if ($incompletedPromotion) {
                    //         $burnAmount = $amount;
                    //         $amount = 0;
                    //
                    //         UserPromotion::claimUserPromotion($incompletedPromotion, $burnAmount, true);
                    //     } elseif ($completedPromotion) {
                    //         $oriAmount = $amount;
                    //         $amount = $oriAmount > ($completedPromotion->max_withdraw_amount ?? INF) ? $completedPromotion->max_withdraw_amount : $oriAmount;
                    //         $burnAmount = $oriAmount - $amount;
                    //
                    //         UserPromotion::claimUserPromotion($completedPromotion, $burnAmount);
                    //     } elseif ($cancelledPromotion) {
                    //         $bonusAmount = $cancelledPromotion->bonus_amount;
                    //         $amount -= $bonusAmount;
                    //         $burnAmount = $bonusAmount;
                    //
                    //         UserPromotion::claimUserPromotion($cancelledPromotion, $burnAmount);
                    //     }

                    $update = ['amount' => $amount, 'receivable_amount' => $amount, 'status' => ExTransfer::$status['confirmed']];
                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;
                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $amount, 'ex-transfer-in', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'out', $amount, $amount);

                    Models\AccountBalance::updateBalance($params['user_id'], $amount);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;
        }
    }

    public function PUSSY($params, $product)
    {
        $userId = $params['user_id'];
        $user = User::find($userId);
        $account = UserProduct::where('user_id', $user->id)
            ->where('product_id', $product->id)
            ->first()
            ?->member_id;
        $amount = $params['amount'];

        $pussy = resolve(PUSSY::class);

        switch ($params['curl_type']) {
            case 'register':
                $result = $pussy->addUser($account);
                break;
            case 'deposit':
                $result = $pussy->deposit($account, $amount);

                if (isset($result) && ($result['status'])) {
                    $update = ['status' => Models\ExTransfer::$status['confirmed']];
                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'in', $amount, $amount);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                    $exTransfer = Models\ExTransfer::with(['creditType', 'user'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    // To change status for refunded
                    $update = ['status' => Models\ExTransfer::$status['refunded'], 'res_data' => $result['msg'] ?? null];
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', Models\User::$userType['internal-account'])->first()->id;

                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $amount, 'ex-transfer-refund', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);

                Models\ExTransfer::where(['id' => $params['ex_transfer_id'] ?? 0])->update($update);
                break;
            case 'withdraw':
                // $result = $kiss->withdraw($account);
                //
                // if (isset($result) && $result['status']) {
                //     $amount = $result['amount'];
                //     $update = ['amount' => $amount, 'receivable_amount' => $amount, 'status' => ExTransfer::$status['confirmed']];
                //
                //     $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                //     $internalId = Models\User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;
                //     Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $amount, 'ex-transfer-in', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                //
                //     Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'out', $amount, $amount);
                //
                //     Models\AccountBalance::updateBalance($params['user_id'], $amount);
                // } else {
                //     $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                // }
                //
                // Models\ExTransfer::where([
                //     'id' => $params['ex_transfer_id'] ?? 0,
                // ])->update($update);
                // break;

                $activePromotion = UserPromotion::getUserPromotionByUserId($user->id, $product->id);

                if ($activePromotion) {
                    $balance = $pussy->getBalance($account);
                    $isPromotionIncomplete = $balance < 1;

                    $todayAchievedTurnover = UserPromotion::getTodayAchievedTurnoverByProduct($user->id, $product->id, $activePromotion->achieved_turnover);
                    $todayReturnAmount = UserPromotion::getTodayReturnAmountByProduct($user->id, $product->id, $activePromotion->game_return_amount);
                    $turnover = $pussy->getTurnover($account, $activePromotion->created_at, $todayAchievedTurnover, $todayReturnAmount);
                    $achievedTurnover = $turnover['achieved_turnover'];
                    $isTurnoverAchieved = $turnover['achieved_turnover'] >= $activePromotion->target_turnover;

                    if ($achievedTurnover > 0) {
                        if ($isPromotionIncomplete || $isTurnoverAchieved) {
                            $withdrew = $pussy->withdraw($account);
                        }

                        UserPromotion::updateUserPromotion([
                            'achieved_turnover' => $turnover['achieved_turnover'],
                            'game_return_amount' => $turnover['return_amount'],
                            'user_id' => $userId,
                            'is_promotion_incomplete' => $isPromotionIncomplete,
                        ]);
                    }
                } else {
                    $withdrew = $pussy->withdraw($account);
                }

                $incompletedPromotion = UserPromotion::getIncompletedUserPromotionByUserId($user->id, $product->id);
                $completedPromotion = UserPromotion::getCompletedUserPromotionByUserId($user->id, $product->id);
                $cancelledPromotion = UserPromotion::getCancelledUserPromotionByUserId($user->id, $product->id);

                if (isset($withdrew) && isset($withdrew['status'])) {
                    $amount = $withdrew['amount'] ?? 0;

                    if ($incompletedPromotion) {
                        $burnAmount = $amount;
                        $amount = 0;

                        UserPromotion::claimUserPromotion($incompletedPromotion, $burnAmount, true);
                    } elseif ($completedPromotion) {
                        $oriAmount = $amount;
                        $amount = $oriAmount > ($completedPromotion->max_withdraw_amount ?? INF) ? $completedPromotion->max_withdraw_amount : $oriAmount;
                        $burnAmount = $oriAmount - $amount;

                        UserPromotion::claimUserPromotion($completedPromotion, $burnAmount);
                    } elseif ($cancelledPromotion) {
                        $bonusAmount = $cancelledPromotion->bonus_amount;
                        $amount -= $bonusAmount;
                        $burnAmount = $bonusAmount;

                        UserPromotion::claimUserPromotion($cancelledPromotion, $burnAmount);
                    }

                    $update = ['amount' => $amount, 'receivable_amount' => $amount, 'status' => ExTransfer::$status['confirmed']];
                    $exTransfer = Models\ExTransfer::with(['creditType'])->where(['id' => $params['ex_transfer_id'] ?? 0])->first();
                    $internalId = Models\User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;
                    Models\CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $amount, 'ex-transfer-in', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);
                    Models\UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'out', $amount, $amount);

                    Models\AccountBalance::updateBalance($params['user_id'], $amount);
                } else {
                    $update = ['status' => Models\ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
                }

                Models\ExTransfer::where([
                    'id' => $params['ex_transfer_id'] ?? 0,
                ])->update($update);
                break;
        }
    }
}
