<?php

namespace App\Services;

use App\Traits;
use App\Models\Credit;
use App\Models\CreditTransaction;
use App\Models\CurrencyDetail;
use App\Models\Deposit;
use App\Models\User;
use App\Models\UserCardLog;
use App\Models\UserLevel;
use App\Models\UserLevelTransaction;
use App\Models\UserPromotion;
use App\Models\UserTurnoverSummary;
use App\Models\VipLevel;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class VipMemberService
{
    public static function addDepositPoint($params = [])
    {
        $fromDateTime = $params['from_datetime'];
        $toDateTime = $params['to_datetime'];

        Deposit::select('user_id', DB::raw('SUM(receivable_amount) as amount'))
            ->where('status', Deposit::$status['approved'])
            ->whereBetween('created_at', [$fromDateTime, $toDateTime])
            ->groupBy('user_id')
            ->get()
            ->each(function ($q) use ($fromDateTime, $toDateTime) {
                if (UserLevelTransaction::userLevelTransactionCount(
                    $q->user_id,
                    UserLevelTransaction::$type['deposit'],
                    Carbon::parse($fromDateTime)->format('Y-m-d')
                ) > 0) {
                    return;
                }

                // Give point to user if amount > 100 can get 10 pts
                if ($q->amount > 100) {
                    $points = floor($q->amount / 100) * 10;
                    $user_level = UserLevel::where('user_id', $q->user_id)->first();

                    if (!$user_level) {
                        // Create user level
                        $vipLevel = VipLevel::where('status', true)
                            ->orderBy('level')
                            ->first();

                        $user_level = UserLevel::create([
                            'user_id' => $q->user_id,
                            'vip_level_id' => $vipLevel->id,
                            'current_point' => 0,
                            'rebate' => 0
                        ]);
                    }

                    $user_level->increment('current_point', $points);

                    UserLevelTransaction::create([
                        'user_id' => $q->user_id,
                        'user_level_id' => $user_level->id,
                        'point' => $points,
                        'rebate_amount' => 0,
                        'type' => UserLevelTransaction::$type['deposit'],
                        'status' => UserLevelTransaction::$status['completed'],
                        'meta' => json_encode([
                            'from_date' => $fromDateTime,
                            'to_date' => $toDateTime,
                            'amount' => $q->amount
                        ])
                    ]);
                }
            });

        UserCardLog::select('user_id', DB::raw('SUM(operation_qty) as amount'))
            ->where('operation_type', UserCardLog::$operationType['deposit'])
            ->whereNotNull('user_id')
            ->whereBetween('transaction_at', [$fromDateTime, $toDateTime])
            ->groupBy('user_id')
            ->get()
            ->map(function ($q) use ($fromDateTime, $toDateTime) {
                if (UserLevelTransaction::userLevelTransactionCount(
                    $q->user_id,
                    UserLevelTransaction::$type['card-reload'],
                    Carbon::parse($fromDateTime)->format('Y-m-d')
                ) > 0) {
                    return;
                }

                // Give point to user if amount > 100 can get 10 pts
                if ($q->amount > 100) {
                    $points = floor($q->amount / 100) * 10;
                    $user_level = UserLevel::where('user_id', $q->user_id)->first();
                    if (!$user_level) {
                        // Create user level
                        $vipLevel = VipLevel::where('status', true)
                            ->orderBy('level')
                            ->first();

                        $user_level = UserLevel::create([
                            'user_id' => $q->user_id,
                            'vip_level_id' => $vipLevel->id,
                            'current_point' => 0,
                            'rebate' => 0
                        ]);
                    }

                    $user_level->increment('current_point', $points);

                    UserLevelTransaction::create([
                        'user_id' => $q->user_id,
                        'user_level_id' => $user_level->id,
                        'point' => $points,
                        'rebate_amount' => 0,
                        'type' => UserLevelTransaction::$type['card-reload'],
                        'status' => UserLevelTransaction::$status['completed'],
                        'meta' => json_encode([
                            'from_date' => $fromDateTime,
                            'to_date' => $toDateTime,
                            'amount' => $q->amount
                        ])
                    ]);
                }
            });
    }

    public static function addProfitLossRebate($params = [])
    {
        $fromDateTime = $params['from_datetime'];
        $toDateTime = $params['to_datetime'];

        $currencies = CurrencyDetail::select('user_id', DB::raw('SUM(pnl_amount) as total_pnl_amount'))
            ->whereBetween('transaction_at', [$fromDateTime, $toDateTime])
            ->where('store_id', $params['store_id'])
            ->where('pnl_amount', '>', 0)
            ->whereNull('user_level_transaction_id')
            ->groupBy('user_id')
            ->get();
        $currencies->each(function ($q) use ($fromDateTime) {
            $user_level = UserLevel::with('vip_level')->where('user_id', $q->user_id)->first();
            if (!$user_level) {
                // Create user level
                $vipLevel = VipLevel::where('status', true)
                    ->orderBy('level')
                    ->first();

                $user_level = UserLevel::create([
                    'user_id' => $q->user_id,
                    'vip_level_id' => $vipLevel->id,
                    'current_point' => 0,
                    'rebate' => 0
                ]);
            }

            $result = VipMemberService::distributeVipRebate([
                'user_id' => $q->user_id,
                'user_level_id' => $user_level->id,
                'rebate_amount' => $q->total_pnl_amount * $user_level->vip_level->rebate_offline,
                'type' => UserLevelTransaction::$type['token'],
                'date' => Carbon::parse($fromDateTime)->format('Y-m-d'),
                'meta' => json_encode([
                    'date_time' => $fromDateTime,
                    'amount' => $q->total_pnl_amount
                ])
            ]);

            // if ($result) {
            //     $q->user_level_transaction_id = $result->id;
            //     $q->save();
            // }
        });
    }

    public static function addTurnoverRebate($params = [])
    {
        $fromDateTime = $params['from_datetime'];
        $toDateTime = $params['to_datetime'];

        $results = UserTurnoverSummary::whereDate('transaction_at', Carbon::parse($fromDateTime)->format('Y-m-d'))
            ->get();
        $results->each(function ($q) use ($fromDateTime, $toDateTime) {
            $user_level = UserLevel::with('vip_level')->where('user_id', $q->user_id)->first();
            if (!$user_level) {
                // Create user level
                $vipLevel = VipLevel::where('status', true)
                    ->orderBy('level')
                    ->first();

                $user_level = UserLevel::create([
                    'user_id' => $q->user_id,
                    'vip_level_id' => $vipLevel->id,
                    'current_point' => 0,
                    'rebate' => 0
                ]);
            }

            $promotion = UserPromotion::where('user_id', $q->user_id)
                ->whereBetween('created_at', [$fromDateTime, $toDateTime])
                ->select(DB::raw('SUM(achieved_turnover) as turnover'))
                ->first();

            $rebate_amount = $q->turnover * $user_level->vip_level->rebate - ($promotion?->turnover ?? 0);

            $reponse = VipMemberService::distributeVipRebate([
                'user_id' => $q->user_id,
                'user_level_id' => $user_level->id,
                'rebate_amount' => $rebate_amount > 0 ? $rebate_amount : 0,
                'type' => UserLevelTransaction::$type['turnover'],
                'date' => Carbon::parse($fromDateTime)->format('Y-m-d'),
                'meta' => json_encode([
                    'date_time' => $fromDateTime,
                    'turnover' => $q->turnover,
                    'turnover_promotion' => $promotion?->turnover
                ])
            ]);

            if ($reponse) {
                $q->status = 1;
                $q->save();
            }
        });
    }

    public static function distributeVipRebate(array $params = [])
    {
        if (UserLevelTransaction::userLevelTransactionCount(
            $params['user_id'],
            $params['type'],
            $params['date']
        ) > 0) {
            return;
        }

        // Add Credit Transaction
        return DB::transaction(function () use ($params) {

            $subject = 'vip-rebate-payout';
            $user_id = $params['user_id'];
            $user_level_id = $params['user_level_id'];
            $rebate_amount = $params['rebate_amount'];
            $type = $params['type'];

            $transaction = UserLevelTransaction::create([
                'user_id' => $user_id,
                'user_level_id' => $user_level_id,
                'point' => 0,
                'rebate_amount' => $rebate_amount,
                'type' => $type,
                'status' => UserLevelTransaction::$status['completed'],
                'meta' => $params['meta'] ?? null
            ]);

            // $internalID = User::select('id')->where('username', 'rebatePayout')->where('user_type', User::$userType['internal-account'])->first()->id ?? null;
            // if (empty($internalID)) {
            //     throw new \Exception("Invalid Internal Account.", 400);
            // }
            // $belongId = Traits\GenerateNumberTrait::GenerateNewId() ?? 0;
            // $batchId = $belongId;
            // $creditName = Credit::where('type', 'myr-credit')->orderBy('priority', 'ASC')->first()->name;

            // CreditTransaction::insertTransaction($internalID, $user_id, $user_id, $creditName, $rebate_amount, $subject, $belongId, $batchId, null, date("Y-m-d H:i:s"), null, null, null, $transaction->id);

            return $transaction;
        });

        abort(200, 'User Level Transaction Created Successfully');
    }
}
