<?php

namespace App\Services\GameLog;

use App\Models\Store;
use App\Models\User;
use App\Models\WWJAccountBetTransaction;
use App\Services\GameProvider\JK;
use Illuminate\Http\Response;
use Illuminate\Http\Client\Response as HttpResponse;
use Illuminate\Support\Facades\Http;

class WWJGameLogService
{
    protected $baseUrl = 'https://gagamelog.funwallet.vip/api';

    public function getBetTransaction($params = [])
    {
        $params = [
            'account' => $params['account'] ?? null,
            'username' => $params['username'] ?? null,
            'phone_no' => $params['phone_no'] ?? null,
            'start' => $params['from_date'] ?? null,
            'end' => $params['to_date'] ?? null,
        ];

        $fromDate = $params['start'] ?? null;
        $toDate = $params['end'] ?? null;
        $username = $params['username'] ?? null;
        $phone = $params['phone_no'] ?? null;
        $account = $params['account'] ?? null;
        $agentId = null;

        $user = User::when(isset($username), function ($q) use ($username) {
            return $q->where('username', 'LIKE', "%$username%");
        })->when(isset($account), function ($q) use ($account) {
            return $q->where('uuid', $account);
        })->when(isset($phone), function ($q) use ($phone) {
            return $q->where('phone_no', 'LIKE', "%$phone%");
        })->first();
        if (empty($user)) abort(400, 'User Not Found');

        $store = Store::where('store_id', $user->store_id)->first() ?? null;
        if (empty($store)) abort(400, 'Store ID Not Found');

        $account = 'fw_' . $user->uuid;
        $agentId = $store->jk_agent_id;

        $limit = 10000; //config('app.pagination_rows');
        $list = [];
        $hasNext = false;
        $page = $params['page'] ?? 1;

        if (isset($params['export']) && ($params['export'] == 1)) {
            $jobData = $params;
            $jobData["model"] = get_class() ?? null;
            $jobData["function"] = __FUNCTION__;
            $jobData["module"] = MODULE;
            $jobData["creator_type"] = MODULE;
            $jobData["creator_id"] = auth()->user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, "Export Successfully");
        }

        // $result = $this->httpPostRequest('/wwj/all-bet-transaction', $params);
        $jk = resolve(JK::class);
        $result = $jk->getBetTransactionByAccount($account, $fromDate, $toDate, $agentId);

        if ($result['Error'] == 0 && $result['Data']['List']) {
            $hasNext = $result['Data']['Count'] >= $limit;
            if ($hasNext) {
                array_pop($result['Data']['List']);
            }

            foreach ($result['Data']['List'] as $eachData) {
                $list[] = [
                    'sn' => $eachData['Sn'] ?? null,
                    'agent_id' => $eachData['AgentId'] ?? null,
                    'account' => $eachData['Account'] ?? null,
                    'create_time' => $eachData['CreateTime'] ?? null,
                    'turn_over' => $eachData['TurnOver'] ?? null,
                    'bet' => $eachData['Bet'] ?? null,
                    'return' => $eachData['Return'] ?? null,
                    'win_loss' => $eachData['WinLoss'] ?? null,
                    'status' => $eachData['Status'] ?? null,
                ];
            }
        }

        $itemFrom = ($page - 1) * $limit;
        $data['list'] = $list;
        $itemTo = $itemFrom + count($list);
        $data['pagination'] = array(
            "current_page" => $page,
            "from" => $itemFrom,
            "last_page" => $hasNext ? $page + 1 : $page,
            "per_page" => (int) $limit,
            "to" => $itemTo,
            "total" => $itemTo
        );

        return $data;
    }

    public function getAccountBetTransaction($params = [])
    {
        // $params = [
        //     'start' => '2025-01-27 00:00:00',
        //     'end' => '2025-02-02 23:59:59',
        // ];

        $limit = config('app.pagination_rows');
        $list = [];
        $hasNext = false;
        $page = $params['page'] ?? 1;

        if (isset($params['export']) && ($params['export'] == 1)) {
            $jobData = $params;
            $jobData["model"] = get_class() ?? null;
            $jobData["function"] = __FUNCTION__;
            $jobData["module"] = MODULE;
            $jobData["creator_type"] = MODULE;
            $jobData["creator_id"] = auth()->user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, "Export Successfully");
        }

        // $result = $this->httpPostRequest('/wwj/account-bet-transaction-date', $params);
        // if ($result['status'] == true) {

        // $hasNext = count($result['data']) >= $limit;
        // if ($hasNext) {
        // array_pop($result['data']);
        // }

        // $list = $result['data'];
        // foreach ($list as $key => $value) {
        //     $user = User::where('uuid', str_replace("fw_", "", $value['account']))->first();

        //     WWJAccountBetTransaction::updateOrCreate([
        //         'account' => $value['account'],
        //     ], [
        //         'user_id' => $user->id ?? null,
        //         'total_turn_over' => $value['total_turn_over'],
        //         'total_bet' => $value['total_bet'],
        //         'total_return' => $value['total_return'],
        //         'total_win_loss' => $value['total_win_loss'],
        //     ]);
        // }

        $list = WWJAccountBetTransaction::with('user', 'user.store')
            ->when(isset($params['store_id']), function ($query) use ($params) {
                return $query->where('user.store_id', $params['store_id']);
            })
            ->get()->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->user->name ?? null,
                    'store' => $item->user->store->name ?? null,
                    'account' => $item->account,
                    'total_turn_over' => $item->total_turn_over,
                    'total_bet' => $item->total_bet,
                    'total_return' => $item->total_return,
                    'total_win_loss' => $item->total_win_loss,
                ];
            });
        // } else {
        //     abort(400, $result['message']);
        // }

        $itemFrom = ($page - 1) * $limit;
        $data['list'] = $list;
        $itemTo = $itemFrom + count($list);
        $data['pagination'] = array(
            "current_page" => $page,
            "from" => $itemFrom,
            "last_page" => $hasNext ? $page + 1 : $page,
            "per_page" => (int) $limit,
            "to" => $itemTo,
            "total" => $itemTo
        );

        return $data;
    }

    protected function httpPostRequest(string $path, array $params): HttpResponse
    {
        $response = Http::asForm()->timeout(3)->post($this->baseUrl . $path, $params);

        if ($response->json('code') && $response->json('code') != 1000) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, $response->json('message'));
        } else if (!$response->ok()) {
            abort(Response::HTTP_FAILED_DEPENDENCY, __('Game Log Provider server error.'));
        }

        return $response;
    }
}
