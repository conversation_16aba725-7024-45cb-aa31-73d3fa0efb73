<?php

namespace App\Services\PaymentGateway;

use Illuminate\Http\Client\Response as HttpResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Http;

class FPay
{
    protected string $username;
    protected string $apiKey;
    protected string $secretKey;
    protected ?string $authToken;
    protected ?string $orderId;
    protected ?string $baseApiUrl;

    public function __construct(string $username, string $apiKey, string $secretKey, string $baseApiUrl)
    {
        $this->username = $username;
        $this->apiKey = $apiKey;
        $this->secretKey = $secretKey;
        $this->baseApiUrl = $baseApiUrl;
    }

    public function getCurrencyList()
    {
        $response = Http::asMultipart()->post($this->baseApiUrl . '/merchant/currency', [
            'username' => $this->username
        ]);

        $result = $response->json();
        if ($result['status'] != true) {
            return false;
        }

        return $result['rate'] ?? null;
    }

    protected function generateAuthToken(): void
    {
        $response = $this->httpPostRequest('auth', ['api_key' => $this->apiKey]);

        $this->authToken = $response->json('auth');
        $this->orderId = $response->json('order_id');
    }

    public function generateOrder($amount, $currency, $email, $phoneNo = null, $mode = '', $referenceNumb = null)
    {
        $this->generateAuthToken();

        $response = $this->httpPostRequest('generate_orders', [
            'auth' => $this->authToken,
            'amount' => $amount,
            'currency' => $currency,
            'orderid' => $referenceNumb ?? $this->orderId,
            'email' => $email ?? env('FPAY_EMAIL'),
            'phone_number' => $phoneNo ?? env('FPAY_PHONE_NO'),
            // 'mode' => $mode,
            'redirect_url' => env('FPAY_REDIRECT_URL') . '?order_id=' . ($referenceNumb ?? $this->orderId)
        ])->throw();

        return [
            'orderId' => $referenceNumb ?? $this->orderId,
            'payUrl' => $response->json('p_url')
        ];
    }

    public function checkStatus(string $orderId)
    {
        $response = $this->httpPostRequest('check_status', [
            'id' => $orderId
        ])->throw();

        return [
            'orderId' => $orderId,
            'status' => $response->json('order_status'),
            'orderDateTime' => $response->json('order_datetime'),
            'amount' => $response->json('amount'),
            'currency' => $response->json('currency')
        ];
    }

    protected function httpPostRequest(string $path, array $params): HttpResponse
    {
        $fixedParams = [
            'username' => $this->username,
        ];

        $response = Http::asMultipart()->post($this->baseApiUrl . '/merchant/' . $path, array_merge($fixedParams, $params));

        if ($response->ok() == false) {
            abort(Response::HTTP_FAILED_DEPENDENCY, __('Payment gateway server error.'));
        } elseif ($response->json('status') == false) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, $response->json('message'));
        }

        return $response;
    }
}
