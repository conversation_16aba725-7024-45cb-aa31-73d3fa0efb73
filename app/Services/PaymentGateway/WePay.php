<?php

namespace App\Services\PaymentGateway;

use App\Models\CurlLog;
use Illuminate\Http\Client\Response as HttpResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Http;

class WePay
{
    private string $apiKey;

    private string $baseApiUrl;

    public function __construct(string $apiKey, string $baseApiUrl)
    {
        $this->apiKey = $apiKey;
        $this->baseApiUrl = $baseApiUrl;
    }

    public function generateOrder($amount, $referenceNumb, $remark = null)
    {
        $returnUrl = env('FPAY_REDIRECT_URL') . '?order_id=' . ($referenceNumb);
        $response = $this->httpPostRequest('create_bill', [
            'ref_id' => $referenceNumb,
            'amount' => $amount,
            'return_url' => $returnUrl,
            'callback_url' => env('WEPAY_CALLBACK_URL'),
            'signature' => hash('sha256', $this->apiKey . $referenceNumb . $amount . $returnUrl . env('WEPAY_CALLBACK_URL')), // TODO: Generate signature SHA256 string [secret_key + ref_id + amount + return_url + callback_url]
            'remark' => $remark,
        ])->throw();

        $data = $response->json();

        return [
            'orderId' => $data['transaction_id'],
            'payUrl' => $data['payment_link'],
        ];
    }

    public function generateWithdrawOrder($amount, $customerBankCode, $customerBankHolderName, $customerBankAccount, $uniqueId)
    {
        $response = $this->httpPostRequest('create_payout', [
            'ref_id' => $uniqueId,
            'amount' => $amount,
            'callback_url' => env('WEPAY_PAYOUT_CALLBACK_URL'),
            'customer_bank_code' => $customerBankCode,
            'customer_bank_holder_name' => $customerBankHolderName,
            'customer_bank_acc' => $customerBankAccount,
            'signature' => hash('sha256', $this->apiKey . $uniqueId . $amount . env('WEPAY_PAYOUT_CALLBACK_URL')), // TODO: Generate signature SHA256 string [secret_key + unique_id + amount + callback_url]
        ]);

        return [
            'status' => $response->json('status'),
            'message' => $response->json('message'),
        ];
    }

    public function checkStatus(string $orderId)
    {
        $response = $this->httpPostRequest('check_bill', [
            'transaction_id' => $orderId,
        ])->throw();

        $data = $response->json();

        return [
            'orderId' => $data['transaction_id'],
            'status' => $data['state'],
            'orderDateTime' => $data['transaction_date'],
            'amount' => $data('amount'),
        ];
    }

    public function checkPayout(string $payoutId)
    {
        $response = $this->httpPostRequest('check_payout', [
            'payout_id' => $payoutId,
        ])->throw();

        $data = $response->json();

        return [
            'orderId' => $data['payout_id'],
            'status' => $data['state'],
            'orderDateTime' => $data['withdrawal_date'],
            'amount' => $data('amount'),
        ];
    }

    protected function httpPostRequest(string $path, array $params): HttpResponse
    {
        $fixedParams = [
            'secret_key' => $this->apiKey,
        ];

        $response = Http::asMultipart()->post($this->baseApiUrl . '/wepay_api/' . $path, array_merge($fixedParams, $params));

        CurlLog::create([
            'endpoint' => $this->baseApiUrl . '/wepay_api/' . $path,
            'request' => json_encode($params),
            'response' => json_encode([]),
            'created_at' => now(),
        ]);

        if ($response->ok() == false) {
            if ($response->json('status') == 'ERROR') {
                abort(Response::HTTP_FAILED_DEPENDENCY, 'WEPAY API Errro: ' . $response->json('message'));
            }

            abort(Response::HTTP_FAILED_DEPENDENCY, __('WePay payment gateway server error.'));
        } elseif ($response->json('status') == false) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, 'WEPAY API Error: ' . $response->json('message'));
        }

        return $response;
    }
}
