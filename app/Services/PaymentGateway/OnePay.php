<?php

namespace App\Services\PaymentGateway;

use App\Models\CurlLog;
use Illuminate\Http\Client\Response as HttpResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OnePay
{
    protected string $username;
    protected string $apiKey;
    protected string $secretKey;
    protected ?string $authToken;
    protected ?string $orderId;
    protected ?string $baseApiUrl;

    public function __construct(string $username, string $apiKey, string $secretKey, string $baseApiUrl)
    {
        $this->username = $username;
        $this->apiKey = $apiKey;
        $this->secretKey = $secretKey;
        $this->baseApiUrl = $baseApiUrl;
    }

    public function getCurrencyList()
    {
        $response = Http::asMultipart()->post($this->baseApiUrl . '/merchant/currency', [
            'username' => $this->username
        ]);

        $result = $response->json();
        if ($result['status'] != true) {
            return false;
        }

        return $result['rate'] ?? null;
    }

    protected function generateAuthToken(): void
    {
        $response = $this->httpPostRequest('auth', ['api_key' => $this->apiKey]);

        $this->authToken = $response->json('auth');
        $this->orderId = $response->json('order_id');
    }

    public function generateOrder($amount, $currency, $email, $phoneNo = null, $mode, $referenceNumb = null, $bank_id = null)
    {
        $this->generateAuthToken();

        $response = $this->httpPostRequest('generate_orders', [
            'username' => $this->username,
            'auth' => $this->authToken,
            'amount' => $amount,
            'currency' => $currency,
            'orderid' => $referenceNumb ?? $this->orderId,
            'email' => $email ?? env('FPAY_EMAIL'),
            'phone_number' => $phoneNo ?? env('FPAY_PHONE_NO'),
            'pay_method' => $mode,
            'bank_id' => $bank_id,
            'redirect_url' => env('FPAY_REDIRECT_URL') . '?order_id=' . ($referenceNumb ?? $this->orderId)
        ])->throw();

        return [
            'orderId' => $referenceNumb ?? $this->orderId,
            'payUrl' => $response->json('p_url')
        ];
    }

    public function generateWithdrawOrder($amount, $currency, $bankId, $holderName, $accountNo, $referenceNumb = null)
    {
        $this->generateAuthToken();

        $response = $this->httpPostRequest('withdraw_orders', [
            'auth' => $this->authToken,
            'amount' => $amount,
            'currency' => $currency,
            'orderid' => $referenceNumb ?? $this->orderId,
            'bank_id' => $bankId,
            'holder_name' => $holderName,
            'account_no' => $accountNo,
        ]);

        return [
            'status' => $response->json('status'),
            'message' => $response->json('message')
        ];
    }

    public function checkStatus(string $orderId)
    {
        $response = $this->httpPostRequest('check_status', [
            'id' => $orderId
        ])->throw();

        return [
            'orderId' => $orderId,
            'status' => $response->json('order_status'),
            'orderDateTime' => $response->json('order_datetime'),
            'amount' => $response->json('amount'),
            'currency' => $response->json('currency')
        ];
    }

    protected function httpPostRequest(string $path, array $params): HttpResponse
    {
        $fixedParams = [
            'username' => $this->username,
        ];

        $response = Http::asMultipart()->post($this->baseApiUrl . '/merchant/' . $path, array_merge($fixedParams, $params));

        CurlLog::create([
            'endpoint' => $this->baseApiUrl . '/merchant/' . $path,
            'request' => json_encode(array_merge($fixedParams, $params)),
            'response' => $response,
            'created_at' => now(),
        ]);

        // Log::info('FPAY ' . $path . ' API Response: ' . $response->body());
        // Log::info('FPAY ' . $path . ' API Request: ' . json_encode(array_merge($fixedParams, $params)));

        if ($response->ok() == false) {
            abort(Response::HTTP_FAILED_DEPENDENCY, __('Payment gateway server error.'));
        } elseif ($response->json('status') == false) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, 'API Error: ' . $response->json('message'));
        }

        return $response;
    }
}
