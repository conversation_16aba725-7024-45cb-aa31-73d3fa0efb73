<?php

namespace App\Services\PaymentGateway;

use App\Models\CurlLog;
use Illuminate\Http\Client\Response as HttpResponse;
use Illuminate\Support\Facades\Http;

class RapidPay
{
    private string $apiKey;

    private string $baseApiUrl;

    public function __construct(string $apiKey, string $baseApiUrl)
    {
        $this->apiKey = $apiKey;
        $this->baseApiUrl = $baseApiUrl;
    }

    // public function getCurrencyList()
    // {
    //     $response = Http::asMultipart()->post($this->baseApiUrl.'/merchant/currency', [
    //         'username' => $this->username,
    //     ]);
    //
    //     $result = $response->json();
    //     if ($result['status'] != true) {
    //         return false;
    //     }
    //
    //     return $result['rate'] ?? null;
    // }

    public function generateOrder($amount, $referenceNumb, $remark = null)
    {
        $response = $this->httpPostRequest('create_bill', [
            'customer_code' => $referenceNumb,
            'amount' => $amount,
            'return_url' => env('FPAY_REDIRECT_URL').'?order_id='.($referenceNumb),
            'callback_url' => env('RAPIDPAY_CALLBACK_URL'),
            'remark' => $remark,
        ])->throw();

        $data = $response->json();

        return [
            'orderId' => $data['transaction_id'],
            'payUrl' => $data['payment_link'],
        ];
    }

    public function generateWithdrawOrder($amount, $customerBankCode, $customerBankHolderName, $customerBankAccount, $uniqueId)
    {
        $response = $this->httpPostRequest('create_payout', [
            'unique_id' => $uniqueId,
            'amount' => $amount,
            'callback_url' => env('RAPIDPAY_PAYOUT_CALLBACK_URL'),
            'customer_bank_code' => $customerBankCode,
            'customer_bank_holder_name' => $customerBankHolderName,
            'customer_bank_acc' => $customerBankAccount,
        ]);

        return [
            'status' => $response->json('status'),
            'message' => $response->json('message'),
        ];
    }

    public function checkStatus(string $orderId)
    {
        $response = $this->httpPostRequest('check_bill', [
            'transaction_id' => $orderId,
        ])->throw();

        $data = $response->json();

        return [
            'orderId' => $data['transaction_id'],
            'status' => $data['state'],
            'orderDateTime' => $data['transaction_date'],
            'amount' => $data('amount'),
        ];
    }

    public function checkPayout(string $payoutId)
    {
        $response = $this->httpPostRequest('check_payout', [
            'payout_id' => $payoutId,
        ])->throw();

        $data = $response->json();

        return [
            'orderId' => $data['payout_id'],
            'status' => $data['state'],
            'orderDateTime' => $data['withdrawal_date'],
            'amount' => $data('amount'),
        ];
    }

    protected function httpPostRequest(string $path, array $params): HttpResponse
    {
        $fixedParams = [
            'secret_key' => $this->apiKey,
        ];

        $response = Http::asMultipart()->post($this->baseApiUrl.'/'.$path, array_merge($fixedParams, $params));

        CurlLog::create([
            'endpoint' => $this->baseApiUrl.'/'.$path,
            'request' => json_encode(array_merge($fixedParams, $params)),
            'response' => $response,
            'created_at' => now(),
        ]);

        // if ($response->ok() == false) {
        //     abort(Response::HTTP_FAILED_DEPENDENCY, __('Payment gateway server error.'));
        // } elseif ($response->json('status') == false) {
        //     abort(Response::HTTP_UNPROCESSABLE_ENTITY, 'API Error: '.$response->json('message'));
        // }

        return $response;
    }
}
