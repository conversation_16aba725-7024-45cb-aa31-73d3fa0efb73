<?php

namespace App\Services;

use App\Models\Product;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class BetLogService
{
    public function getBetTransactionByAccount($startTime, $endTime, $account, $productId = null)
    {
        $product = Product::where('id', $productId)->first();
        if (! isset($product)) {
            throw new \Exception('Product not found');
        }

        switch ($product->name) {
            case 'GSC':
                return $this->getBetRecords($startTime, $endTime, $account, $product->provider_code);
            case 'JK':
                return $this->getBuayaBetRecords($startTime, $endTime, $account);
        }
    }

    public function getAllBetRecords($startTime, $endTime, $account)
    {
        $total_turnover = 0;

        $products = Product::where('status', Product::$status['active'])->where('name', 'GSC')->get();
        foreach ($products as $product) {
            $res = $this->getBetRecords($startTime, $endTime, $account, $product->provider_code);
            $total_turnover += $res['data']['total_turnover'];
        }

        $res = $this->getBuayaBetRecords($startTime, $endTime, $account);
        $total_turnover += $res['data']['total_turnover'];

        $response['data'] = [
            'total_turnover' => $total_turnover,
        ];

        return $response;
    }

    public function getBetRecords($startTime, $endTime, $account, $provider)
    {
        $startTime = Carbon::parse($startTime)->format('Y-m-d H:i:s');
        $endTime = Carbon::parse($endTime)->format('Y-m-d H:i:s');

        $query = 'SELECT ticket_id, site, start_time_mas, end_time_mas, turnover, bet, payout FROM gsc_player_transactions WHERE site = :site AND start_time_mas >= :start AND end_time_mas <= :end';

        if (isset($account)) {
            $query .= ' AND LOWER(member) = :member';
        }

        $db = DB::connection('clickhouse')->getClient();
        $data = $db->select($query, array_filter([
            'start' => $startTime,
            'end' => $endTime,
            'site' => $provider,
            'member' => str()->lower($account),
        ]))->rows();

        $meta = [];
        $total_turnover = 0;
        $total_bet = 0;
        $total_payout = 0;
        $total_bet_count = 0;

        foreach ($data as $key => $value) {
            $meta[] = [
                'ticket_id' => $value['ticket_id'],
                'site' => $value['site'],
                'start_time_mas' => $value['start_time_mas'],
                'end_time_mas' => $value['end_time_mas'],
                'turnover' => $value['turnover'],
                'bet' => $value['bet'],
                'payout' => $value['payout'],
            ];

            $total_bet_count += 1;
            $total_turnover += $value['turnover'];
            $total_bet += $value['bet'];
            $total_payout += $value['payout'];
        }

        $response['data'] = [
            'total_turnover' => $total_turnover,
            'total_bet' => $total_bet,
            'total_bet_count' => $total_bet_count,
            'total_payout' => $total_payout,
            'meta' => json_encode($meta),
        ];

        return $response;
    }

    private function getBuayaBetRecords($startTime, $endTime, $account)
    {
        $startTime = Carbon::parse($startTime)->format('Y/m/d H:i:s +08:00');
        $endTime = Carbon::parse($endTime)->format('Y/m/d H:i:s +08:00');

        $query = 'SELECT sn, create_time, bet, win_loss FROM player_transactions WHERE create_time >= :start AND create_time <= :end';

        if (isset($account)) {
            $query .= ' AND account = :account';
        }

        $db = DB::connection('clickhouse')->getClient();
        $data = $db->select($query, array_filter([
            'start' => $startTime,
            'end' => $endTime,
            'account' => 'fw_'.$account,
        ]))->rows();

        $meta = [];
        $total_turnover = 0;
        $total_payout = 0;

        foreach ($data as $key => $value) {
            $meta[] = [
                'sn' => $value['sn'],
                'create_time' => $value['create_time'],
                'bet' => $value['bet'],
                'payout' => $value['win_loss'],
            ];

            $total_turnover += $value['bet'];
            $total_payout += $value['win_loss'];
        }

        $response['data'] = [
            'total_turnover' => $total_turnover,
            'total_payout' => $total_payout,
            'meta' => json_encode($meta),
        ];

        return $response;
    }

    public function getYesterdayUserLossByUserId($userId)
    {
        $user = User::find($userId);
        $buayaAcc = 'fw_'.$user->uuid;
        $gscAcc = $user->uuid;

        $yesterday = strtotime('-1 day', today()->timestamp);
        $startDate = date('Y-m-d 00:00:00', $yesterday);
        $endDate = date('Y-m-d 23:59:59', $yesterday);

        $startTime = Carbon::parse($startDate)->format('Y-m-d H:i:s');
        $endTime = Carbon::parse($endDate)->format('Y-m-d H:i:s');

        $buayaQuery = 'SELECT SUM(win_loss) AS total_win_loss ';
        $buayaQuery .= 'FROM player_transactions ';
        $buayaQuery .= 'WHERE create_time >= :start ';
        $buayaQuery .= 'AND create_time <= :end ';
        $buayaQuery .= 'AND account = :account;';

        $gscQuery = 'SELECT SUM(payout) - SUM(turnover) AS total_win_loss ';
        $gscQuery .= 'FROM gsc_player_transactions ';
        $gscQuery .= 'WHERE start_time_mas >= :start ';
        $gscQuery .= 'AND start_time_mas <= :end ';
        $gscQuery .= 'AND member = :account;';

        $db = DB::connection('clickhouse')->getClient();

        $buayaData = $db->select($buayaQuery, array_filter([
            'start' => $startTime,
            'end' => $endTime,
            'account' => $buayaAcc,
        ]))->rows();

        $gscData = $db->select($gscQuery, array_filter([
            'start' => $startTime,
            'end' => $endTime,
            'account' => $gscAcc,
        ]))->rows();

        $buayaLoss = -($buayaData[0]['total_win_loss'] ?? 0);
        $gscLoss = -($gscData[0]['total_win_loss'] ?? 0);
        $totalLoss = $buayaLoss + $gscLoss;

        return $totalLoss;
    }
}
