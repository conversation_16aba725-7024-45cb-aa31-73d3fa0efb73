<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\DateTrait;
use App\Traits\S3Trait;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Arr;
use App\Models\Store;

class UserBank extends Model
{
    use S3Trait;
    use DateTrait;
    use SoftDeletes;

    protected $table = 'user_bank';

    protected $hidden = [];

    protected $fillable = [
        'user_id',
        'bank_id',
        'account_no',
        'account_holder',
        'branch',
        'swift_code',
        'status',
        'is_primary',
        'created_at',
        'deleted_at',
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
    ];

    public function user()
    {
        return $this->belongsTo(User::class, "user_id", "id");
    }

    public function bank()
    {
        return $this->belongsTo(Bank::class, "bank_id", "id");
    }

    public static function add(array $params = [])
    {
        return DB::transaction(function () use ($params) {
            $userID = $params['user_id'] ?? Auth::user()->id ?? null;
            $isPrimary = isset($params['is_primary']) ? $params['is_primary'] : 0;
            $bankID  = $params['bank_id'] ?? null;

            $checkExistPrimary = UserBank::select('id')->where('user_id', $userID)->where('status', self::$status['active'])->where('is_primary', 1)->first()->id ?? Null;
            $checkPendingWithdrawal = Withdrawal::select('id')->where('user_id', $userID)->where('user_bank_id', $checkExistPrimary)->whereIn('status', Arr::except(Withdrawal::$status, ['rejected', 'cancel', 'approved']))->first()->id ?? Null;

            if ($isPrimary == 1) {
                if (isset($checkPendingWithdrawal)) {
                    abort(400, json_encode(Lang::get('lang.withdrawal-channel-primary-error-due-pending')));
                } else if (!empty($checkExistPrimary)) {
                    UserBank::where('id', $checkExistPrimary)->update(['is_primary' => 0]);
                }
            }

            $bankCardDetail = self::query()->where('account_no', $params['bank_account_number'])->where('status', self::$status['active'])->first();

            if (isset($bankCardDetail)) {
                $params['store_id'] = auth()->user()->store_id;
                $params['account_no'] = $params['bank_account_number'];

                $bankAccountExists = Store::getBankCardList($params);
                if (isset($bankAccountExists)) {
                    abort(400, json_encode('This account number is already linked to a account in this branch. Please check your details or contact support.'));
                }
            }

            $insert = [
                'user_id' => $userID,
                'bank_id' => $bankID,
                'account_no' => $params['bank_account_number'],
                'account_holder' => $params['bank_account_holder'],
                'branch' => $params['branch'] ?? null,
                'swift_code' => $params['swift_code'] ?? null,
                'status' => self::$status['active'],
                'is_primary' => $isPrimary,
            ];

            UserBank::create($insert);

            return true;
        });
    }

    public static function list(array $params = [])
    {
        $fromDate = $params['from_date'] ?? Null;
        $toDate = $params['to_date'] ?? Null;
        $updatedFromDate = $params['updated_from_date'] ?? Null;
        $updatedToDate = $params['updated_to_date'] ?? Null;
        $username = $params['username'] ?? Null;
        $memberId = $params['member_id'] ?? Null;
        $storeId = $params['store_id'] ?? null;
        $exStoreId = $params['extra_store_id'] ?? null;
        $phoneNo = $params['phone_no'] ?? Null;
        $currencyID = $params['currency_id'] ?? Null;
        $bankName = $params['bank_name'] ?? Null;
        $bankAccountNo = $params['bank_account_no'] ?? Null;
        $status = $params['status'] ?? Null;
        $lang = config('app.locale') ?? 'en';

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        if (isset($params['export']) && ($params['export'] == 1)) {
            $params["model"] = get_class() ?? null;
            $params["function"] = __FUNCTION__;
            ExportReport::exportReport($params);
        }

        $items = self::query()
            ->with('user', 'bank')
            ->select(["id", "user_id", "bank_id", "account_no", "account_holder", "branch", "swift_code", "status", "is_primary", "created_at", "updated_at"])
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('DATE(created_at)'), ">=", $fromDate);
                return $q->where(DB::raw('DATE(created_at)'), "<=", $toDate);
            })
            ->when((isset($updatedFromDate) && isset($updatedToDate)), function ($q) use ($updatedFromDate, $updatedToDate) {
                $q->where(DB::raw('DATE(updated_at)'), ">=", $updatedFromDate);
                return $q->where(DB::raw('DATE(updated_at)'), "<=", $updatedToDate);
            })
            ->when(isset($username), function ($q) use ($username) {
                return $q->whereRelation('user', 'username', 'LIKE', "%" . $username . "%");
            })
            ->when(isset($memberId), function ($q) use ($memberId) {
                return $q->whereRelation('user', 'member_id', $memberId);
            })
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('user.store', 'store_id', $storeId);
            })
            ->when(isset($exStoreId), function ($q) use ($exStoreId) {
                return $q->whereHas('user.store', function ($q) use ($exStoreId) {
                    $q->whereIn('id', $exStoreId);
                });
            })
            ->when(isset($phoneNo), function ($q) use ($phoneNo) {
                $q->whereRelation('user', function ($q) use ($phoneNo) {
                    $phoneNo = preg_replace('/[^0-9]/', '', $phoneNo);
                    $q->where('phone_no', 'LIKE', "%$phoneNo%");
                });
            })
            ->when((isset($currencyID)), function ($q) use ($currencyID) {
                return $q->whereRelation('user', 'currency_id', $currencyID);
            })
            ->when((isset($bankName)), function ($q) use ($bankName) {
                return $q->whereRelation('bank', 'name', 'LIKE', '%' . $bankName . '%');
            })
            ->when((isset($bankAccountNo)), function ($q) use ($bankAccountNo) {
                return $q->where('account_no', 'LIKE', '%' . $bankAccountNo . '%');
            })
            ->when(isset($status), function ($q) use ($status) {
                return $q->where('status', self::$status[$status]);
            })
            ->whereNULL('deleted_at');

        if (MODULE == 'admin') {
            $items = $items->orderBy($order_by, $order_sort)->paginate($limit);

            $items->getCollection()->transform(function ($q) use ($lang) {

                $status = ($q->is_primary == 1 && $q->status == self::$status['active']) ? 'Default' : array_search($q->status, self::$status);

                $res = [
                    "id" => $q->id,
                    "username" => $q->user->username ?? null,
                    "member_id" => $q->user->member_id ?? null,
                    "phone_no" => $q->user->phone_no ?? null,
                    "bank_name" => Lang::has('lang.' . $q->bank->translation_code) ? Lang::get('lang.' . $q->bank->translation_code) : $q->bank->name,
                    "account_name" => $q->account_holder ?? null,
                    "account_no" => $q->account_no,
                    "store_name" => $q->user->store->name ?? null,
                    "status" => $status,
                    "status_display" => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                    "created_at" => DateTrait::dateFormat($q->created_at),
                    "updated_at" => DateTrait::dateFormat($q->updated_at),
                ];

                return (object) $res;
            })->toArray();

            return (new ItemsCollection($items));
        } else {
            $userID = $params['userID'];
            $usdCurrencyID = Currency::where('iso', 'USD')->first()->id;

            $items = $items->where('user_id', $userID)->orderBy('is_primary', 'DESC')->get()->map(function ($q) use ($usdCurrencyID) {
                $status = array_search($q->status, self::$status);

                $currencyRate = 0;

                $bankID = UserBank::where('id', $q->id)->first()->bank_id;
                $toCurrency = Bank::where('id', $bankID)->with('country.getCurrency')->first()->country->getCurrency ?? null;
                $toCurrencyID = $toCurrency->id ?? null;

                $currencyRateID = CurrencyRate::select(DB::raw('MAX(id) as id'))->where('from_currency_id', $usdCurrencyID)->where('to_currency_id', $toCurrencyID)->where('deleted_at', null)->first()->id ?? null;
                if (isset($currencyRateID)) $currencyRate = CurrencyRate::select('withdrawal_rate')->where('id', $currencyRateID)->first()->withdrawal_rate;

                $res = [
                    "id" => $q->id,
                    "username" => $q->user->username ?? null,
                    'currency' => $q->user->currency->iso ?? null,
                    "bank_id" => $q->bank->id,
                    "bank_country_id" => $q->bank->country_id ?? null,
                    "bank_name" => Lang::has('lang.' . $q->bank->translation_code) ? Lang::get('lang.' . $q->bank->translation_code) : $q->bank->name,
                    "account_name" => $q->account_holder ?? null,
                    "account_no" => $q->account_no,
                    "branch" => $q->branch,
                    "swift_code" => $q->swift_code,
                    "status" => $status,
                    "status_display" => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                    "is_primary" => $q->is_primary,
                    "est_symbol" => $toCurrency->iso,
                    "conversion_rate" => $currencyRate,
                    "created_at" => DateTrait::dateFormat($q->created_at),
                    "updated_at" => DateTrait::dateFormat($q->updated_at)
                ];

                return $res;
            });

            return $items;
        }
    }

    public static function withdrawalBankDetail(array $params = [])
    {

        $bankCardId = $params['id'] ?? Null;
        $username = $params['username'] ?? Null;
        $memberId = $params['member_id'] ?? Null;
        $storeId = $params['store_id'] ?? null;
        $exStoreId = $params['extra_store_id'] ?? null;
        $phoneNo = $params['phone_no'] ?? Null;
        $currencyID = $params['currency_id'] ?? Null;
        $bankName = $params['bank_name'] ?? Null;
        $bankAccountNo = $params['bank_account_no'] ?? Null;
        $status = $params['status'] ?? Null;

        $bankCardDetail = self::query()
            ->with('user', 'bank')
            ->select(["id", "user_id", "bank_id", "account_no", "account_holder", "branch", "swift_code", "status", "is_primary", "created_at", "updated_at"])
            ->when(isset($username), function ($q) use ($username) {
                return $q->whereRelation('user', 'username', 'LIKE', "%" . $username . "%");
            })
            ->when(isset($memberId), function ($q) use ($memberId) {
                return $q->whereRelation('user', 'member_id', $memberId);
            })
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('user.store', 'store_id', $storeId);
            })
            ->when(isset($exStoreId), function ($q) use ($exStoreId) {
                return $q->whereHas('user.store', function ($q) use ($exStoreId) {
                    $q->whereIn('id', $exStoreId);
                });
            })
            ->when(isset($phoneNo), function ($q) use ($phoneNo) {
                $q->whereRelation('user', function ($q) use ($phoneNo) {
                    $phoneNo = preg_replace('/[^0-9]/', '', $phoneNo);
                    $q->where('phone_no', 'LIKE', "%$phoneNo%");
                });
            })
            ->when((isset($currencyID)), function ($q) use ($currencyID) {
                return $q->whereRelation('user', 'currency_id', $currencyID);
            })
            ->when((isset($bankName)), function ($q) use ($bankName) {
                return $q->whereRelation('bank', 'name', 'LIKE', '%' . $bankName . '%');
            })
            ->when((isset($bankAccountNo)), function ($q) use ($bankAccountNo) {
                return $q->where('account_no', 'LIKE', '%' . $bankAccountNo . '%');
            })
            ->when(isset($status), function ($q) use ($status) {
                return $q->where('status', self::$status[$status]);
            })
            ->where('id', $bankCardId)
            ->whereNULL('deleted_at')->first();

        $status = ($bankCardDetail->is_primary == 1 && $bankCardDetail->status == self::$status['active']) ? 'Default' : array_search($bankCardDetail->status, self::$status);

        $res = [
            "id" => $bankCardDetail->id,
            "member_card_id" =>  $bankCardDetail->user->member_id ?? null,
            "username" => $bankCardDetail->user->username ?? null,
            "phone_no" => $bankCardDetail->user->phone_no ?? null,
            'store_name' => $bankCardDetail->user->store->name ?? null,
            'currency' => $bankCardDetail->user->currency->iso ?? null,
            "bank_id" => $bankCardDetail->bank->id,
            "bank_country_id" => $bankCardDetail->bank->country_id ?? null,
            "bank_name" => Lang::has('lang.' . $bankCardDetail->bank->translation_code) ? Lang::get('lang.' . $bankCardDetail->bank->translation_code) : $bankCardDetail->bank->name,
            "account_name" => $bankCardDetail->account_holder ?? null,
            "account_no" => $bankCardDetail->account_no,
            "branch" => $bankCardDetail->branch,
            "swift_code" => $bankCardDetail->swift_code,
            "status" => $status,
            "status_display" => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
            "is_primary" => $bankCardDetail->is_primary,
            "est_symbol" => $bankCardDetail->iso,
            "conversion_rate" => $bankCardDetail,
            "created_at" => DateTrait::dateFormat($bankCardDetail->created_at),
            "updated_at" => DateTrait::dateFormat($bankCardDetail->updated_at)
        ];

        return $res;
    }

    public static function updateWithdrawalBankDetail(array $params = [])
    {

        $bankCardId = $params['id'] ?? Null;
        $bankAccountNo = $params['account_no'] ?? Null;;
        $bnakAccountHolder = $params['account_holder'] ?? Null;

        $bankCardDetail = self::query()->find($bankCardId);

        $bankAccountDetail = self::query()
            ->with('user', 'bank')
            ->select(["id", "user_id", "bank_id", "account_no", "account_holder", "branch", "swift_code", "status", "is_primary", "created_at", "updated_at"])
            ->where('account_no', $bankAccountNo)
            ->where('status', self::$status['active'])
            ->whereNULL('deleted_at')
            ->when($bankCardId, function ($query) use ($bankCardId) {
                return $query->where('id', '!=', $bankCardId);
            })
            ->when($bankCardDetail->bank->id, function ($query) use ($bankCardDetail) {
                return $query->where('bank_id', $bankCardDetail->bank->id);
            })
            ->first();

        if (isset($bankAccountDetail)) {
            $params['store_id'] = $bankCardDetail->user->store->store_id;
            $bankAccountExists = Store::getBankCardList($params);
            if (isset($bankAccountExists)) {
                abort(400, json_encode('Account or Bank Exists in ' . $bankAccountDetail->user->store->name));
            }
        }
        $bankCardDetail->account_no = $bankAccountNo;
        $bankCardDetail->account_holder = $bnakAccountHolder;

        $bankCardDetail->save();

        return $bankCardDetail;
    }

    public static function deleteWithdrawalBankDetail(array $params = [])
    {

        $bankCardId = $params['id'] ?? Null;

        $bankCardDetail = self::query()->find($bankCardId);

        $bankCardDetail->status = self::$status['inactive'];

        $bankCardDetail->save();

        return $bankCardDetail;
    }

    public static function updateWithdrawalChannel(array $params = [])
    {
        return DB::transaction(function () use ($params) {
            $ids = $params['id'] ?? null;
            $withdrawalChannelAry = self::whereIn('id', $ids)->get();

            foreach ($withdrawalChannelAry as $withdrawalChannel) {
                $update = [
                    "status" => array_key_exists('status', $params) ? self::$status[$params['status']] : $withdrawalChannel->status,
                    "is_primary" => array_key_exists('is_primary', $params) ? $params['is_primary'] : $withdrawalChannel->is_primary,
                ];

                if (isset($params['is_primary']) && ($params['is_primary'] == 1)) {
                    UserBank::where('id', '!=', $withdrawalChannel->id)->where('user_id', $withdrawalChannel->user_id)->where('status', self::$status['active'])->where('is_primary', 1)->update(['is_primary' => 0]);
                }

                $checkPendingWithdrawal = Withdrawal::select('id')->where('user_id', $withdrawalChannel->user_id)->where('user_bank_id', $withdrawalChannel->id)->whereIn('status', Arr::except(Withdrawal::$status, ['rejected', 'cancel', 'approved']))->first()->id ?? Null;
                if (isset($checkPendingWithdrawal)) abort(400, json_encode(Lang::get('lang.withdrawal-channel-update-error-due-pending')));

                if ($update['status'] == self::$status['inactive']) $update['is_primary'] = 0;

                $withdrawalChannel->update($update);
            }

            return true;
        });
    }
}
