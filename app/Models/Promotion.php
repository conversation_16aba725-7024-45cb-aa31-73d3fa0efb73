<?php

namespace App\Models;

use App\Services\GameProvider\JK;
use App\Traits;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;

class Promotion extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'image',
        'description',
        'terms',
        'product_id',
        'service_id',
        'is_one_time',
        'min_deposit',
        'max_deposit',
        'max_withdraw_amount',
        'is_withdrawal_multiplier',
        'max_withdrawal_multiplier',
        'is_bonus_flexible',
        'is_bonus_multiplier',
        'bonus_multiplier',
        'bonus_amount',
        'max_bonus_amount',
        'is_turnover_multiplier',
        'turnover_multipler',
        'turnover_amount',
        'status',
    ];

    protected $casts = [
        'is_withdrawal_multiplier' => 'boolean',
        'status' => 'boolean',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function service()
    {
        return $this->belongsTo(Services::class);
    }

    public function userPromotions()
    {
        return $this->hasMany(UserPromotion::class);
    }

    public static function getPromotionList()
    {
        $promotions = Promotion::where('status', true)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'image' => $item->image,
                    'name' => $item->name,
                ];
            })
            ->toArray();

        return $promotions ?? [];
    }

    public static function getPromotionListForDeposit($userId)
    {
        $promotions = Promotion::where('status', true)
            ->where('is_deposit', true)
            ->get()
            ->toArray();

        $filtered = array_filter($promotions ?? [], function ($e) use ($userId) {
            $userList = $e['user_list'];
            $decoded = json_decode($userList ?? '', true);

            if (! ($userList && $decoded)) {
                return true;
            }

            return in_array($userId, array_values($decoded));
        });

        return array_map(function ($e) {
            return [
                'id' => $e['id'],
                'image' => $e['image'],
                'name' => $e['name'],
            ];
        }, $filtered);
    }

    public static function getUserPromotionList($data)
    {
        $result['active_promotion'] = null;
        $userPromotion = UserPromotion::with('promotion')->where('user_id', $data['user_id'])
            ->whereIn('status', [UserPromotion::$status['pending'], UserPromotion::$status['in-progress']])
            ->first();

        if (isset($userPromotion)) {
            $result['active_promotion'] = [
                'promotion_id' => $userPromotion->promotion->id,
                'start_at' => Carbon::parse($userPromotion->created_at)->format('Y-m-d H:i:s A'),
                'bonus_amount' => Traits\DecimalTrait::setDecimal($userPromotion->bonus_amount ?? 0),
                'max_withdrawal_amount' => Traits\DecimalTrait::setDecimal($userPromotion->max_withdraw_amount ?? 0),
                'current_turnover' => Traits\DecimalTrait::setDecimal($userPromotion->achieved_turnover ?? 0),
                'target_turnover' => Traits\DecimalTrait::setDecimal($userPromotion->target_turnover ?? 0),
                'image' => $userPromotion->promotion->image,
            ];
        }

        $promotions = Promotion::with('product', 'service')
            ->where('status', true)
            ->get()
            ->map(function ($item) {
                $description = Lang::has('langReward.'.$item->description) ? Lang::get('langReward.'.$item->description) : '';
                $terms = Lang::has('langReward.'.$item->terms) ? Lang::get('langReward.'.$item->terms) : '';

                return [
                    'id' => $item->id,
                    'image' => $item->image,
                    'name' => $item->name,
                    'description' => $description,
                    'terms' => $terms,
                ];
            })
            ->toArray();

        // $result['promotion_list'] = array_merge($promotions, [[
        //     'name' => 'claim_rebate',
        //     'image' => 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/staging/2025/01/claim_rebate.png',
        // ]]);

        $result['promotion_list'] = $promotions;

        return $result;
    }

    public static function getPromotionDetail($id)
    {
        $promotion = Promotion::where('id', $id)->where('status', true)->first() ?? null;
        $description = Lang::has('langReward.'.$promotion->description) ? Lang::get('langReward.'.$promotion->description) : '';
        $terms = Lang::has('langReward.'.$promotion->terms) ? Lang::get('langReward.'.$promotion->terms) : '';

        return [
            'id' => $promotion->id,
            'image' => $promotion->image,
            'description' => $description,
            'terms' => $terms,
            'product_id' => $promotion->product_id,
            'service_id' => $promotion->service_id,
            'max_withdraw_amount' => $promotion->max_withdraw_amount,
            'max_withdrawal_multiplier' => $promotion->max_withdrawal_multiplier,
            'is_bonus_flexible' => $promotion->is_bonus_flexible,
            'bonus_amount' => $promotion->bonus_amount,
            'turnover_amount' => $promotion->turnover_amount,
            'is_withdrawal_multiplier' => $promotion->is_withdrawal_multiplier,
        ];
    }

    public static function claimBuayaPromotion($params = [])
    {

        $bonusAmount = 100;
        $user_id = $params['user_id'];

        $promotion = Promotion::find(1000);
        if (! $promotion) {
            abort(400, json_encode(['msg' => ['Promotion not found']]));
        }

        $userPromotion = UserPromotion::where('user_id', $user_id)
            ->where('promotion_id', 1000)
            ->whereDate('updated_at', today())
            ->first();

        if (! isset($userPromotion) && empty($userPromotion)) {
            // Get Total Bet Transaction > 500
            $totalTurnover = 0;

            $user_product = UserProduct::with('user')->where('product_id', 2004)
                ->where('user_id', $user_id)
                ->first();

            $jk = resolve(JK::class);
            $transaction = $jk->getBetTransactionByAccount($user_product->member_id, today()->format('Y-m-d 00:00:00'), now(), $user_product->user->store->jk_agent_id);
            if ($transaction['Error'] == 0 && isset($transaction['Data']['List'])) {
                foreach ($transaction['Data']['List'] as $key => $value) {
                    $totalTurnover += $value['Bet'];
                }
            }

            if ($totalTurnover < 500) {
                // abort(400, json_encode(['msg' => ['Please hit Minimum Turnover 500 for claiming promotion. T&C Applied']]));
                abort(400, json_encode(['msg' => ['Minimum Turnover is 500. T&C Applied']]));
            } else {
                UserPromotion::applyPromotion(1000, $bonusAmount);
            }
        } else {
            abort(400, json_encode(['msg' => ['You had already claimed']]));
        }

        $data = [
            'service_id' => 1342,
            'name' => null,
            'display' => null,
            'url' => null,
            'icon' => null,
            'status' => 'active',
            'isTK8' => 0,
            'showCashInOutWarning' => 0,
            'product_id' => 2004,
            'product_name' => '',
        ];

        return [
            'message' => 'Promotion claimed.',
            'promotion' => array_merge(
                $promotion->toArray(),
                ['terms' => Lang::has('langReward.promotion-terms') ? Lang::get('langReward.promotion-terms') : '']
            ),
            'amount' => (string) Traits\DecimalTrait::setDecimal($bonusAmount, 2),
            'service' => $data,
        ];
    }
}
