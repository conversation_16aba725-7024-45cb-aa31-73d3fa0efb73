<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Auth;
use App\Traits\DateTrait;
use App\Traits\DecimalTrait;

class ActivityLog extends Model
{
    use DateTrait;
    use DecimalTrait;

    protected $table = 'activity_log';

    protected $hidden = [];

    protected $fillable = [
        'id',
        'user_id',
        'action',
        'data',
        'creator_type',
        'creator_id',
        'created_at',
        'updated_at',
    ];

    public static function insertActivityLog($action = "",$userID = "",$data = [],$creatorID = ""){
        $module = defined('MODULE') ? MODULE : null;
        $creator_id = $creatorID ? $creatorID : (($module == 'admin' || $module == 'user') ? Auth::id() : 0);

        if (($creator_id == null || $creator_id == 0)) {
            $creator_type = 'system';
        } else {
            $creator_type = ($module == 'admin' || $module == 'user') ? $module : 'system';
        }

        self::create([
            'user_id' => isset($userID) ? $userID : null,
            'action' => $action,
            'data' => isset($data) ? json_encode($data) : null,
            'creator_type' => $creator_type,
            'creator_id' => $creator_id,
        ]);
    }

    public static function get(array $params = [])
    {
        $from = $params['from'] ?? null;
        $to = $params['to'] ?? null;
        $username = $params['username'] ?? null;
        $module = $params['module'] ?? null;
        $createdBy = $params['created_by'] ?? null;
        $seeAll = $params['see_all'] ?? null;

        $sortableColumns = ['created_at'];

        $ignoredModule = [
            'announcement-bar-add',
            'announcement-bar-update',
            'announcement-bar-detail-add',
            'announcement-bar-detail-update',
            'announcement-bar-setting-add',
            'announcement-bar-setting-update',
            'announcement-add',
            'announcement-update',
            'announcement-detail-add',
            'announcement-detail-update',
            'memo-add',
            'memo-update',
            'memo-detail-add',
            'memo-detail-update',
            'memo-setting-add',
            'memo-setting-update',
            'banner-add',
            'banner-update',
            'banner-detail-add',
            'banner-detail-update',
            'banner-setting-add',
            'banner-setting-update',
            'add-user-setting',
            'update-user-setting',
            'document-detail-add',
            'change-profile-pic',
            'delete-profile-pic',
        ];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        $items = ActivityLog::query()
            ->with(['user','createdAdmin','createdUser'])
            ->when(isset($username), function ($query) use ($username) {
                $query->whereHas('user', function ($query) use ($username) {
                    $query->where('username', 'like', "%$username%");
                });
            })
            ->when(isset($createdBy), function ($query) use ($createdBy) {
                $query->whereHas('createdAdmin', function ($query) use ($createdBy) {
                    $query->where('username', 'like', "%$createdBy%");
                });
                $query->orWhereHas('createdUser', function ($query) use ($createdBy) {
                    $query->where('username', 'like', "%$createdBy%");
                });
            })
            ->when(isset($from), function ($query) use ($from) {
                $query->where('created_at', '>=', $from);
            })
            ->when(isset($to), function ($query) use ($to) {
                $query->where('created_at', '<=', $to);
            })
            ->when(isset($module), function ($query) use ($module) {
                $query->where('action', 'like', "%$module%");
            })
            ->when(isset($ignoredModule), function ($query) use ($ignoredModule) {
                $query->whereNotIn('action', $ignoredModule);
            })
            ->orderBy($order_by, $order_sort)
            ->when((!isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        // Product
        // $productDataAry = \App\Models\ProductDetail::query()->pluck('product_name','id')->toArray();

        // Credit
        $creditIDAry = [];
        $creditNameAry = [];
        $creditTypeAry = [];

        $credit = \App\Models\Credit::query()
            ->get()
            ->map(function($q) use (&$creditIDAry, &$creditNameAry, &$creditTypeAry) {
                $creditIDAry[$q->id] = $q->name;
                $creditNameAry[$q->name] = $q->name;
                $creditTypeAry[$q->type] = $q->name;
            }) ?? null;

        $mapFunc = function($q) use (/*$productDataAry,*/ $creditIDAry, $creditNameAry, $creditTypeAry){
            $createdBy = 'System';
            if ($q->creator_id != null) {
                $creator_type_lang = Lang::has('lang.'.$q->creator_type) ? Lang::get('lang.'.$q->creator_type) : $q->creator_type;
                if ($q->creator_type == 'admin') {
                    $createdBy = $creator_type_lang.'@'.$q->createdAdmin->username;
                } elseif ($q->creator_type == 'user') {
                    $createdBy = $creator_type_lang.'@'.$q->createdUser->username;
                }
            }

            $q->data = json_decode($q->data,true);
            $module_slug = 'act-log-title-'.$q->action;
            $module = Lang::has('lang.'.$module_slug) ? Lang::get('lang.'.$module_slug) : $module_slug;
            $user = isset($q->user->username) ? $q->user->username : null;

            $description = '';

            $description_slug = 'act-log-desc-'.$q->action;
            $description_lang = Lang::has('lang.'.$description_slug) ? Lang::get('lang.'.$description_slug) : null;

            if (isset($description_lang)) {
                switch ($q->action) {
                    case 'admin-add':
                    case 'user-register':
                        $description = str_replace(['%username%'],[$q->data['username']],$description_lang);
                        break;

                    case 'user-verify-email':
                        $username = $q->user->username ?? null;
                        $email = $q->user->email ?? null;
                        $description = str_replace(['%username%','%email%'],[$username,$email],$description_lang);
                        break;

                    case 'user-update':
                        $username = $q->user->username ?? null;
                        if (isset($username)) {
                            $description_lang = str_replace(['%username%'],[$username],$description_lang);

                            foreach($q->data as $key => $val) {
                                if (!is_array($val)) continue;

                                $oldValue = $val['old'];
                                $oldKeyword = '%old'.ucfirst($key).'%';
                                $newValue = $val['new'];
                                $newKeyword = '%new'.ucfirst($key).'%';

                                if ($key == 'suspended') {
                                    $oldValue = array_search($oldValue,\App\Models\User::$activatedDisplay);
                                    $newValue = array_search($newValue,\App\Models\User::$activatedDisplay);
                                    $oldValue = Lang::has('lang.'.$oldValue) ? Lang::get('lang.'.$oldValue) : $oldValue;
                                    $newValue = Lang::has('lang.'.$newValue) ? Lang::get('lang.'.$newValue) : $newValue;
                                }

                                $description_lang = str_replace($oldKeyword, $oldValue, $description_lang);
                                $description_lang = str_replace($newKeyword, $newValue, $description_lang);
                            }

                            $description = $description_lang;
                            $pieces = explode(" $", $description);

                            if (isset($pieces[1])) {
                                $pieces3 = explode("%", $pieces[0]);

                                if (isset($pieces3[1])) {
                                    $piecesList[] = $pieces3[1];
                                }

                                foreach (array_slice($pieces, 1) as $val) {
                                    if (str_contains($val, "%")) { 
                                        $description = str_replace($val, '', $description);
                                    }
                                }
                            }

                            $description = str_replace('$', '', $description);
                            $description = trim(preg_replace('/[\t\n\r\s]+/', ' ', $description));
                        }
                        break;

                    case 'admin-update':
                        $adminRes = isset($q->data['id']) ? \App\Models\Admin::find($q->data['id']) : null;
                        if (isset($adminRes)) {
                            $description_lang = str_replace(['%username%'],[$adminRes->username],$description_lang);

                            foreach($q->data as $key => $val) {
                                if (!is_array($val)) continue;

                                $oldValue = $val['old'];
                                $oldKeyword = '%old'.ucfirst($key).'%';
                                $newValue = $val['new'];
                                $newKeyword = '%new'.ucfirst($key).'%';

                                if ($key == 'activated') {
                                    $oldValue = array_search($oldValue,\App\Models\User::$activatedDisplay);
                                    $newValue = array_search($newValue,\App\Models\User::$activatedDisplay);
                                    $oldValue = Lang::has('lang.'.$oldValue) ? Lang::get('lang.'.$oldValue) : $oldValue;
                                    $newValue = Lang::has('lang.'.$newValue) ? Lang::get('lang.'.$newValue) : $newValue;
                                }

                                $description_lang = str_replace($oldKeyword, $oldValue, $description_lang);
                                $description_lang = str_replace($newKeyword, $newValue, $description_lang);
                            }

                            $description = $description_lang;
                            $pieces = explode(" $", $description);

                            if (isset($pieces[1])) {
                                $pieces3 = explode("%", $pieces[0]);

                                if (isset($pieces3[1])) {
                                    $piecesList[] = $pieces3[1];
                                }

                                foreach (array_slice($pieces, 1) as $val) {
                                    if (str_contains($val, "%")) { 
                                        $description = str_replace($val, '', $description);
                                    }
                                }
                            }

                            $description = str_replace('$', '', $description);
                            $description = trim(preg_replace('/[\t\n\r\s]+/', ' ', $description));
                        }
                        break;

                    case 'change-password':
                    case 'reset-password':
                    case 'change-transaction-password':
                    case 'reset-transaction-password':
                        $username = $q->user->username ?? null;
                        if (isset($username)) {
                            $description = str_replace(['%username%'],[$username],$description_lang);
                        }
                        break;

                    case 'adjustment-in':
                    case 'adjustment-out':
                        $credit = isset($creditIDAry[$q->data['credit_id']]) ? $creditIDAry[$q->data['credit_id']] : null;
                        if (isset($credit)) {
                            $credit = Lang::has('lang.'.$credit) ? Lang::get('lang.'.$credit) : $credit;
                            $amount = $q->data['amount'] ?? 0;
                            $description = str_replace(['%wallet%','%amount%'],[$credit,$amount],$description_lang);
                        }
                        break;

                    case 'transfer-add':
                        $credit = 'mi-def'; // Currently only mi-def can transfer
                        if (isset($credit)) {
                            $credit = Lang::has('lang.'.$credit) ? Lang::get('lang.'.$credit) : $credit;
                            $userIDAry = [];
                            $userIDAry = [$q->data['from_id'],$q->data['to_id']];
                            if (!empty($userIDAry)) {
                                $userDataAry = \App\Models\User::whereIn('id',$userIDAry)->pluck('username','id')->toArray();
                                $sender = isset($userDataAry[$q->data['from_id']]) ? $userDataAry[$q->data['from_id']] : null;
                                $receiver = isset($userDataAry[$q->data['to_id']]) ? $userDataAry[$q->data['to_id']] : null;
                                $amount = $q->data['amount'] ?? 0;
                                $description = str_replace(['%wallet%','%amount%','%sender%','%receiver%'],[$credit,$amount,$sender,$receiver],$description_lang);
                            }
                        }
                        break;

                    case 'convert-add':
                        $fromCredit = isset($creditIDAry[$q->data['from_credit_id']]) ? $creditIDAry[$q->data['from_credit_id']] : null;
                        $toCredit = isset($creditIDAry[$q->data['to_credit_id']]) ? $creditIDAry[$q->data['to_credit_id']] : null;
                        if (isset($fromCredit) && isset($toCredit)) {
                            $fromCredit = Lang::has('lang.'.$fromCredit) ? Lang::get('lang.'.$fromCredit) : $fromCredit;
                            $toCredit = Lang::has('lang.'.$toCredit) ? Lang::get('lang.'.$toCredit) : $toCredit;
                            $fromAmount = $q->data['from_amount'] ?? 0;
                            $toAmount = $q->data['to_amount'] ?? 0;
                            $rate = $q->data['rate'] ?? 1;
                            $description = str_replace(['%fromWallet%','%toWallet%','%fromAmount%','%toAmount%','%rate%'],[$fromCredit,$toCredit,$fromAmount,$toAmount,$rate],$description_lang);
                        }
                        break;

                    case 'convert-update':
                        $fromStatus = array_search($q->data['status']['old'],\App\Models\Convert::$status) ?? null;
                        $toStatus = array_search($q->data['status']['new'],\App\Models\Convert::$status) ?? null;
                        if (isset($fromStatus) && isset($toStatus)) {
                            $fromStatus = Lang::has('lang.'.$fromStatus) ? Lang::get('lang.'.$fromStatus) : $fromStatus;
                            $toStatus = Lang::has('lang.'.$toStatus) ? Lang::get('lang.'.$toStatus) : $toStatus;
                            $description = str_replace(['%fromStatus%','%toStatus%'],[$fromStatus,$toStatus],$description_lang);
                        }
                        break;

                    case 'kyc-add':
                        $username = $q->user->username ?? null;
                        $type = array_search($q->data['type'],\App\Models\Kyc::$kycType) ?? null;
                        if (isset($username) && isset($type)) {
                            $type = Lang::has('lang.'.$type) ? Lang::get('lang.'.$type) : $type;
                            $description = str_replace(['%username%','%type%'],[$username,$type],$description_lang);
                        }
                        break;

                    case 'kyc-update':
                        $username = $q->user->username ?? null;
                        $fromStatus = array_search($q->data['status']['old'],\App\Models\Kyc::$kycStatus) ?? null;
                        $toStatus = array_search($q->data['status']['new'],\App\Models\Kyc::$kycStatus) ?? null;
                        if (isset($username) && isset($fromStatus) && isset($toStatus)) {
                            $fromStatus = Lang::has('lang.'.$fromStatus) ? Lang::get('lang.'.$fromStatus) : $fromStatus;
                            $toStatus = Lang::has('lang.'.$toStatus) ? Lang::get('lang.'.$toStatus) : $toStatus;
                            $description = str_replace(['%username%','%fromStatus%','%toStatus%'],[$username,$fromStatus,$toStatus],$description_lang);
                        }
                        break;

                    case 'user-bank-add':
                        $username = $q->user->username ?? null;
                        $bankRes = isset($q->data['bank_id']) ? \App\Models\Bank::find($q->data['bank_id']) : null;
                        if (isset($username) && isset($bankRes)) {
                            $bank = Lang::has('lang.'.$bankRes->translation_code) ? Lang::get('lang.'.$bankRes->translation_code) : $bankRes->translation_code;
                            $description = str_replace(['%username%','%bank%'],[$username,$bank],$description_lang);
                        }
                        break;

                    case 'user-bank-update':
                        $username = $q->user->username ?? null;
                        $bankRes = isset($q->data['id']) ? \App\Models\UserBank::with('bank')->find($q->data['id']) : null;
                        if (isset($username) && isset($bankRes)) {
                            $bank = $bankRes->bank->translation_code ?? null;
                            $bank = Lang::has('lang.'.$bank) ? Lang::get('lang.'.$bank) : $bank;
                            $description_lang = str_replace(['%username%','%bank%'],[$username,$bank],$description_lang);

                            foreach($q->data as $key => $val) {
                                if (!is_array($val)) continue;

                                $oldValue = $val['old'];
                                $oldKeyword = '%old'.ucfirst($key).'%';
                                $newValue = $val['new'];
                                $newKeyword = '%new'.ucfirst($key).'%';

                                if ($key == 'status') {
                                    $oldValue = array_search($oldValue,\App\Models\UserBank::$status);
                                    $newValue = array_search($newValue,\App\Models\UserBank::$status);
                                    $oldValue = Lang::has('lang.'.$oldValue) ? Lang::get('lang.'.$oldValue) : $oldValue;
                                    $newValue = Lang::has('lang.'.$newValue) ? Lang::get('lang.'.$newValue) : $newValue;
                                }

                                $description_lang = str_replace($oldKeyword, $oldValue, $description_lang);
                                $description_lang = str_replace($newKeyword, $newValue, $description_lang);
                            }

                            $description = $description_lang;
                            $pieces = explode(" $", $description);

                            if (isset($pieces[1])) {
                                $pieces3 = explode("%", $pieces[0]);

                                if (isset($pieces3[1])) {
                                    $piecesList[] = $pieces3[1];
                                }

                                foreach (array_slice($pieces, 1) as $val) {
                                    if (str_contains($val, "%")) { 
                                        $description = str_replace($val, '', $description);
                                    }
                                }
                            }

                            $description = str_replace('$', '', $description);
                            $description = trim(preg_replace('/[\t\n\r\s]+/', ' ', $description));
                        }
                        break;

                    case 'document-add':
                        $description = str_replace(['%type%'], [array_search($q->data['type'], \App\Models\Document::$type)], $description_lang);
                        break;
                    
                    case 'document-update':
                        $ref = \App\Models\Document::with('documentDetail')->where(['id' => $q->data['id']])->first();
                        if (isset($ref)) {
                            $title = '';
                            foreach ($ref->documentDetail as $docs) {
                                $title .= ' ' . (array_search($docs['language_type'], config('language')) . ": " . $docs['title']);
                            }
                        }
                        $update = '';
                        foreach($q->data as $key => $value) {
                            if (in_array($key, ['updated_by', 'id'])) continue;
                            $status = \App\Models\Document::$status;
                            $type = \App\Models\Document::$type;
                            if (key($q->data) != $key) $update .= ", ";
                            $update .= Lang::has('lang.' . $key) ? Lang::get('lang.' . $key) : ucfirst($key);
                            switch($key) {
                                case 'status':
                                    $oldStatus = array_search($value['old'], $status);
                                    $oldStatusLang = Lang::has('lang.' . $oldStatus) ? Lang::get('lang.' . $oldStatus) : ucfirst($oldStatus);
                                    $newStatus = array_search($value['new'], $status);
                                    $newStatusLang = Lang::has('lang.' . $newStatus) ? Lang::get('lang.' . $newStatus) : ucfirst($newStatus);
                                    $update .= " from " . $oldStatusLang . " to " . $newStatusLang;
                                    break;

                                case 'type':
                                    $oldType = array_search($value['old'], $type);
                                    $oldTypeLang = Lang::has('lang.' . $oldType) ? Lang::get('lang.' . $oldType) : ucfirst($oldType);
                                    $newType = array_search($value['new'], $type);
                                    $newTypeLang = Lang::has('lang.' . $newType) ? Lang::get('lang.' . $newType) : ucfirst($newType);
                                    $update .= " from " . $oldTypeLang . " to " . $newTypeLang;
                                    break;

                                case 'start_date':
                                case 'end_date':
                                    $oldDate = DateTrait::dateFormat($value['old']);
                                    $newDate = DateTrait::dateFormat($value['new']);
                                    $update .= " from " . $oldDate . " to " . $newDate;
                                    break;
                            }
                        }
                        $update .= ".";
                        $description = str_replace(['%title%', '%update%'], [$title, $update], $description_lang);
                        break;

                    case 'document-detail-update':
                        $update = '';
                        foreach ($q->data as $key => $value) {
                            if (in_array($key, ['id'])) continue;
                            if (key($q->data) != $key) $update .= ", ";
                            $keyLang = Lang::has('lang.' . $key) ? Lang::get('lang.' . $key) : ucfirst($key);

                            switch($key) {
                                case 'attachment_data':
                                    $update .= $keyLang;
                                    break;

                                default:
                                    $update .= $keyLang . " from " . $value['old'] . " to " . $value['new'];
                                    break;
                            }
                        }
                        $update .= ".";
                        $description = str_replace(['%update%'], [$update], $description_lang);
                        break;

                    case 'currency-rate-add':
                        $iso = Currency::where('id',$q['data']['to_currency_id'])->first();
                        $description = str_replace(['%iso%', '%depositRate%', '%withdrawalRate%'], [$iso['iso'], $q['data']['deposit_rate'], $q['data']['withdrawal_rate']], $description_lang);
                        break;

                    case 'admin-roles-add':
                        $description = str_replace(["%name%"], [$q['data']['name']], $description_lang);
                        break;

                    case 'admin-roles-update':
                        $updateData = '';
                        $count = count($q['data']);
                        foreach ($q['data'] as $key => $value) {
                            $count--;
                            if (!is_array($value)) continue;
                            switch ($key) {
                                case 'status':
                                    $oldValue = array_search($value['old'], \App\Models\AdminRoles::$status);
                                    $newValue = array_search($value['new'], \App\Models\AdminRoles::$status);
                                    $oldValue = Lang::has('lang.' . $oldValue) ? Lang::get('lang.' . $oldValue) : $oldValue;
                                    $newValue = Lang::has('lang.' . $newValue) ? Lang::get('lang.' . $newValue) : $newValue;
                                    $updateData .= $key . " from " . $oldValue . " to " . $newValue;
                                    break;
                                case 'permissions_id':
                                    $updateData .= "permissions list";
                                    break;
                                default:
                                    $oldValue = $value['old'];
                                    $newValue = $value['new'];
                                    $updateData .= $key . " from " . $oldValue . " to " . $newValue;
                                    break;
                            }
                            if ($count > 1) $updateData .= ", ";
                        }
                        $updateData .= ".";
                        $description = str_replace(['%data%'], [$updateData], $description_lang);
                        break;

                    case 'banner-detail-add':
                        $description = str_replace(['%subject%'], [$q['data']['title']], $description_lang);
                        break;

                    case 'banner-update':
                    case 'banner-detail-update':
                    case 'banner-setting-update':
                        $description_lang = str_replace(['%id%'], [$q['data']['id']], $description_lang);
                        $updateData = '';
                        $ignore = ['id', 'updated_at'];
                        $ignoreContent = [];
                        switch ($q->action) {
                            case 'banner-detail-update':
                                array_push($ignoreContent, 'web_image_data', 'mobile_image_data');
                                break;
                        }
                        $count = count($q['data']);
                        foreach ($q['data'] as $updateKey => $updateValue) {
                            $count--;
                            if (in_array($updateKey, $ignore)) continue;
                            $updateKeyReplace = str_replace('_', '-', $updateKey);
                            $updateKeyLang = Lang::has('lang.' . $updateKeyReplace) ? Lang::get('lang.' . $updateKeyReplace) : $updateKeyReplace;
                            if (in_array($updateKey, $ignoreContent)) {
                                $updateData .= $updateKeyLang;
                            } else {
                                $updateData .= $updateKeyLang . ": " . $updateValue['old'] . " -> " . $updateValue['new'];
                            }
                            if ($count > 1) $updateData .= ", ";
                        }
                        $description = str_replace(['%data%'], [$updateData], $description_lang);
                        break;
                }
            }

            $res = [
                'created_at' => DateTrait::dateFormat($q->created_at),
                'username' => $q->user->username ?? null,
                'module' => $module,
                'action' => $description,
                'created_by' => $createdBy,
            ];
            return (object) $res;
        };

        if (($seeAll == 1)) {
            return ['list' => $items->get()->map($mapFunc)->toArray()];
        } else {
            $items->getCollection()->transform($mapFunc);
            return (new ItemsCollection($items))->toArray();
        }
    }

    public function createdBy()
    {
        return $this->belongsTo(Admin::class,'causer_id','id');
    }

    public function createdAdmin()
    {
        return $this->belongsTo(Admin::class,'creator_id','id');
    }

    public function createdUser()
    {
        return $this->belongsTo(User::class,'creator_id','id');
    }

    public function user()
    {
        return $this->belongsTo(User::class,'user_id','id');
    }

    public function subject()
    {
        return $this->morphTo()->withoutGlobalScope(SoftDeletingScope::class);
    }
}
