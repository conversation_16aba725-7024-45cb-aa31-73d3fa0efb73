<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Http\Resources\ItemsCollection;
use App\Traits\DateTrait;
use App\Traits\DecimalTrait;
use Ramsey\Uuid\Type\Decimal;

class TransactionLogs extends Model
{
    use DecimalTrait;
    use DateTrait;

    protected $table = 'transaction_logs';

    protected $hidden = [
    ];

    protected $fillable = [
        'user_id',
        'payment_number',
        'type',
        'payment_method',
        'portfolio_id',
        'tx_id',
        'callback_amount',
        'total_charge',
        'total_amount',
        'charge',
        'platform_charge',
        'rate',
        'result',
        'progress',
        'status',
        'created_at',
        'updater_id',
        'updated_at',
        'deleted_at',
    ];

    public static $progress = [
        'pending' => 0,
        'received' => 1,
        'confirmed' => 2,
        'failed' => 3,
    ];

    public static $status = [
        'pending' => 0,
        'approved' => 1,
        'rejected' => 2,
        'failed' => 3,
        'cancel' => 4,
        'in-progress' => 5,
    ];

    public static $type = [
        'purchase-package' => 1,
        'reentry-package' => 2,
        'deposit' => 3,
        'upline-purchase-package' => 4,
    ];

    public static $payment_method = [
        'online_banking' => 1,
        'crypto' => 2,
        'credit_card' => 3,
    ];

    public $casts = [
        "result" => "array"
    ];

    public static function get(array $params = [], $logType = null){
        $user_id = $params['user_id'] ?? null;
        $username = $params['username'] ?? null;
        $fromDate = $params['from_date'] ?? Null;
        $toDate = $params['to_date'] ?? Null;
        $status = $params['status'] ?? Null;
        $progress = $params['progress'] ?? Null;
        $fromDate = $params['from_date'] ?? null;
        $toDate = $params['to_date'] ?? null;
        $user_id = $params['user_id'] ?? null;
        $type = $params['type'] ?? null;
        $payment_id = $params['payment_id'] ?? null;
        $package_ref = $params['package_ref'] ?? null;
        $package_name = $params['package_name'] ?? null;
        $transaction_id = $params['transaction_id'] ?? null;
        $usdt_address = $params['usdt_address'] ?? null;
        $approveFromDate = $params['approve_from_date'] ?? null;
        $approveToDate = $params['approve_to_date'] ?? null;
        $country_id = $params['country'] ?? null;
        $seeAll = $params['see_all'] ?? null;

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        $tableTotal['total_amount'] = 0;

        $items = self::query()
            ->with(['userDetail','updaterDetail','userPortfolio','deposit' => function ($q){
                return $q->where('type',Deposit::$type['crypto']);
            }])
            ->when(isset($user_id), function ($query) use ($user_id) {
                $query->where('user_id',$user_id);
            })
            ->when(isset($status), function ($query) use ($status) {
                $query->where('status',$status);
            })
            ->when(isset($logType), function ($query) use ($logType) {
                $query->where('payment_method', self::$payment_method[$logType]);
            })
            ->when(isset($progress), function ($query) use ($progress) {
                $query->where('progress',$progress);
            })
            ->when(isset($username), function ($query) use ($username) {
                $query->whereRelation('userDetail','username', 'like', "%$username%");
            })
            ->when((isset($fromDate) && isset($toDate)),function ($q) use($fromDate,$toDate){
                $q->where(DB::raw('DATE(created_at)'),">=",$fromDate);
                return $q->where(DB::raw('DATE(created_at)'),"<=",$toDate);
            })
            ->when(isset($type), function ($query) use ($type) {
                $query->where('type', self::$type[$type]);
            })
            ->when(isset($payment_id), function ($query) use ($payment_id) {
                $query->where('payment_number', $payment_id);
            })
            ->when(isset($package_ref), function ($query) use ($package_ref) {
                $query->whereRelation('userPortfolio', 'reference_no', $package_ref);
            })
            ->when(isset($package_name), function ($query) use ($package_name) {
                $query->whereRelation('userPortfolio.getProductDetail', 'product_name', $package_name);
            })
            ->when(isset($transaction_id), function ($query) use ($transaction_id) {
                $query->where('tx_id', $transaction_id);
            })
            ->when(isset($usdt_address), function ($query) use ($usdt_address) {
                $query->whereRelation('userDetail.userWalletAddress', 'address', $usdt_address);
            })
            ->when(isset($country_id), function ($query) use ($country_id) {
                $query->whereRelation('userDetail','country_id',$country_id);
            })
            ->when((isset($approveFromDate) && isset($approveToDate)), function ($q) use ($approveFromDate, $approveToDate) {
                $q->where('status', self::$status['approved']);
                $q->where(DB::raw('DATE(updated_at)'), ">=", $approveFromDate);
                return $q->where(DB::raw('DATE(updated_at)'), "<=", $approveToDate);
            });

        $summary = clone $items;
        $items = $items->orderBy($order_by, $order_sort)
                    ->when((!isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                        return $q->paginate($limit);
                    });

        if (MODULE == 'admin') {
            foreach (self::$status as $statusKey => $statusValue) {
                $summaryRecord['total_' . str_replace("-", "_", $statusKey) . '_amount'] = DecimalTrait::setDecimal(0);
                $summaryRecord['total_' . str_replace("-", "_", $statusKey)] = 0;
            }
            $summaryRecord += ["total_usdt_deposit_amount" => 0, 'total_processing_fee' => 0];
            $summary = $summary->groupBy('status')->select('status', DB::raw('SUM(callback_amount) as callback_amount, SUM(total_amount) as total_amount, SUM(platform_charge) AS processing_fee'), DB::raw("COUNT(1) as total_user"))->get()->map(function ($q) use (&$summaryRecord) {
                $summaryRecord['total_usdt_deposit_amount'] += $q->callback_amount;
                $summaryRecord['total_processing_fee'] += $q->processing_fee;
                $summaryRecord['total_' . str_replace("-", "_", array_search($q->status, self::$status)) . '_amount'] = DecimalTrait::setDecimal($q->total_amount);
                $summaryRecord['total_' . str_replace("-", "_", array_search($q->status, self::$status))] = $q->total_user;
            });
            $summaryRecord['total_usdt_deposit_amount'] = DecimalTrait::setDecimal($summaryRecord['total_usdt_deposit_amount']);
            $summaryRecord['total_processing_fee'] = DecimalTrait::setDecimal($summaryRecord['total_processing_fee']);
            $summaryRecord['actual_usdt_deposit_amount'] = DecimalTrait::setDecimal($summaryRecord['total_usdt_deposit_amount'] - $summaryRecord['total_processing_fee']);
        }

        $mapFunc = function($q) use (&$tableTotal){
            $progress = array_search($q->progress,self::$progress);
            $status = array_search($q->status,self::$status);
            $approvable = false;
            $rejectable = false;
            $type = array_search($q->type,self::$type);
            $total_amount = $q->total_amount ?? 0;

            switch ($type) {
                case 'deposit':
                    if(($progress == 'confirmed') && ($status == 'pending' || $status == 'in-progress')){
                        $rejectable = true;
                        if(isset($q->userDetail->userPortfolio)) $approvable = true;
                    }
                    break;
            }

            $res = [
                "id" => $q->id,
                "payment_number" => $q->payment_number,
                'created_at' => DateTrait::dateFormat($q->created_at),
                'type' => $type,
                'type_display' => Lang::has('lang.'.$type) ? __('lang.'.$type) : $type,
                'package_no' => $q->userPortfolio->reference_no ?? null,
                'package_name' => isset($q->userPortfolio->getProductDetail->product_name) ? Lang::get('lang.'.$q->userPortfolio->getProductDetail->product_name) : null,
                'amount' => DecimalTrait::setDecimal(($total_amount),config('decimal.crypto_decimal')) ?? null,
                'tx_id' => $q->tx_id,
                'status' => $status,
                'status_display' => Lang::has('lang.'.$status) ? __('lang.'.$status) : $status,
                'is_cancelable' => ((in_array($q->type,[self::$type['purchase-package'],self::$type['reentry-package'],self::$type['upline-purchase-package']])) && ($q->status == self::$status['pending']) ? 1 : 0),
            ];

            if(MODULE == 'admin'){
                $res = array_merge($res,[
                    'username' => $q->userDetail->username,
                    'address' => $q->userDetail->userWalletAddress->address,
                    'processing_fee' => DecimalTrait::setDecimal(($q->platform_charge),config('decimal.crypto_decimal')) ?? null,
                    'callback_amount' => DecimalTrait::setDecimal(($q->callback_amount),config('decimal.crypto_decimal')) ?? null,
                    'charge' => DecimalTrait::setDecimal(($q->charge), config('decimal.crypto_decimal')) ?? null,
                    'total_charge' => DecimalTrait::setDecimal(($q->total_charge),config('decimal.crypto_decimal')) ?? null,
                    'crypto_status' => $progress,
                    'crypto_status_display' => Lang::has('lang.'.$progress) ? __('lang.'.$progress) : $progress,
                    'approvable' => $approvable,
                    'rejectable' => $rejectable,
                    'updater_by' => $q->updaterDetail->username ?? NULL,
                    'updated_at' => DateTrait::dateFormat($q->updated_at),
                    'country' => $q->userDetail->country->name,
                    'country_display' => Lang::has('lang.'.$q->userDetail->country->name) ? Lang::get('lang.'.$q->userDetail->country->name) : $q->userDetail->country->name,
                ]);

                $tableTotal['total_amount'] += $total_amount;
            }

            return (object) $res;
        };

        $tableTotal['total_amount'] = DecimalTrait::setDecimal($tableTotal['total_amount']);

        if (($seeAll == 1)) {
            return ['list' => $items->get()->map($mapFunc)->toArray()];
        } else {
            $items->getCollection()->transform($mapFunc);
            if (MODULE == 'admin') return (new ItemsCollection($items))->toArray() + ["table_total" => $tableTotal] + ["summary" => $summaryRecord];
            return (new ItemsCollection($items));
        }
    }


    public static function cancel($params = [])
    {
        $dateTime = $params['datetime'] ?? date("Y-m-d H:i:s");
        $trxnLog = self::where('id', $params['id'])->first();
        $deposit = Deposit::where(['reference_id' => $trxnLog->id, "user_id" => $trxnLog->user_id, "type" => Deposit::$type['crypto']])->first();
        $updaterId = null;
        if(isset(auth()->user()->id) && !empty(auth()->user()->id)){
            $updaterId = auth()->user()->id;
        }

        self::where('id', $params['id'])->where('status', self::$status['pending'])->update([
                'status' => self::$status['cancel'],
                'updated_at' => $dateTime,
                'updater_id' => $updaterId
            ]);

        Deposit::updateDeposit([
            'id' => $deposit->id,
            'status' => 'rejected',
            'dateTime' => $dateTime
        ]);

        return true;
    }

    public static function getDetail($data = [])
    {
        $getDownlines = 0;
        $uplineRate = 0;

        $items = self::query()
            ->with(['userDetail', 'userPortfolio', 'updaterDetail', 'deposit'])
            ->where(['id' => $data['id']])
            ->get()
            ->map(function ($q) use (&$getDownlines, &$uplineRate) {
                if (isset($q->userPortfolio)) {
                    $product_name = $q->userPortfolio->getProductDetail->product_name ?? NULL;
                    $product_lang = Lang::has("lang." . $product_name) ? Lang::get("lang." . $product_name) : $product_name;
                    $portfolio_no = $q->userPortfolio->reference_no;
                }
                $type = array_search($q->type, self::$type);

                $total_amount = $q->total_amount;
                $crypto_amount = DecimalTrait::setDecimal(($total_amount * $q->rate), config('decimal.calculation'));
                $packageRes = [];
                switch ($type) {
                    case 'deposit':
                        if (isset($q->deposit)) {
                            $crypto_amount = $q->deposit->amount;
                            $total_amount = $q->deposit->receivable_amount;
                        }
                        break;

                    case 'reentry-package':
                    case 'upline-purchase-package':
                        $packageRes = [
                            "product_amount" => isset($q->userPortfolio->product_price) ? $q->userPortfolio->product_price : null,
                            "portfolio_no" => $portfolio_no ?? NULL,
                            "product_name" => $product_lang ?? NULL,
                        ];
                        break;
                }
                $uplineRate = $q->rate;
                $res = array_merge($packageRes,[
                    "created_at" => DateTrait::dateFormat($q->created_at),
                    "type" => $type,
                    "type_display" => Lang::has("lang." . $type) ? Lang::get("lang." . $type) : $type,
                    "transaction_id" => $q->tx_id ?? NULL,
                    'address' => $q->userDetail->userWalletAddress->address,
                    "total_price" => DecimalTrait::setDecimal(($total_amount)),
                    "crypto_amount" => DecimalTrait::setDecimal(($crypto_amount)),
                    "updated_at" => isset($q->updated_at) ? DateTrait::dateFormat($q->updated_at) : null,
                    "updater_username" => $q->adminDetail->name ?? NULL,
                    "remark" => $q->remark,
                    "status_display" => Lang::has("lang." . array_search($q->status, self::$status)) ? Lang::get("lang." . array_search($q->status, self::$status)) : array_search($q->status, self::$status),
                    "payment_number" => $q->payment_number,
                    "status" => array_search($q->status, self::$status),
                ]);

                if (MODULE == 'admin') {
                    $res = array_merge($res, [
                        "id" => $q->id,
                        "username" => $q->userDetail->username,
                        "user_id" => $q->userDetail->id,
                        'processing_fee' => DecimalTrait::setDecimal(($q->platform_charge),config('decimal.crypto_decimal')) ?? null,
                        'callback_amount' => DecimalTrait::setDecimal(($q->callback_amount), config('decimal.crypto_decimal')) ?? null,
                        'charge' => DecimalTrait::setDecimal(($q->charge), config('decimal.crypto_decimal')) ?? null,
                        'total_charge' => DecimalTrait::setDecimal(($q->total_charge), config('decimal.crypto_decimal')) ?? null,
                        "is_cancelable" => ((in_array($q->type, [self::$type['purchase-package'], self::$type['reentry-package'], self::$type['upline-purchase-package']])) && ($q->status == self::$status['pending']) ? 1 : 0),
                    ]);
                }

                return $res;
            });
        $result = $items->first();

        return $result;
    }

    public static function tronUMXCallback($data = [])
    {
        $data = array_merge($data, ['type' => 'trom-umx-callback']);
        $data = json_encode($data);
        $job = new \App\Jobs\TronUMXCallback($data);
        dispatch($job)->onConnection('sync');
    }

    public function userDetail(){
        return $this->belongsTo(User::class,'user_id','id');
    }

    public function updaterDetail(){
        return $this->belongsTo(Admin::class,'updater_id','id');
    }

    public function deposit(){
        return $this->belongsTo(Deposit::class,'id','reference_id');
    }

    public function userWalletAddress(){
        return $this->belongsTo(UserWalletAddress::class,'user_id','user_id');
    }
}
