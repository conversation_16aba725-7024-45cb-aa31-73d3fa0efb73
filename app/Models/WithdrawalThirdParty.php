<?php

namespace App\Models;

use App\Traits\DecimalTrait;
use App\Traits\GenerateNumberTrait;
use App\Http\Resources\ItemsCollection;
use App\Traits\DateTrait;
use App\Traits;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Arr;

class WithdrawalThirdParty extends Model
{
    use GenerateNumberTrait;
    use DecimalTrait;

    protected $table = 'withdrawal_third_party';

    protected $hidden = [
    ];

    protected $fillable = [
        'serial_number',
        'user_id',
        'credit_type',
        'amount',
        'charges',
        'receivable_amount',
        'currency_id',
        'currency_rate',
        'converted_amount',
        'status',
        'admin_remark',
        'bank_name',
        'account_holder',
        'account_number',
        'txn_id',
        'product_id',
        'belong_id',
        'batch_id',
        'party',
        'approved_at',
        'approved_by',
        'updater_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $status = [
        'waiting-approval' => 0,
        'approved' => 1,
        // 'pending' => 2,
        'rejected' => 3,
        'cancel' => 9,
    ];

    public static $withdrawalType = [
        'usdt' => 1,
        'bank' => 2,
    ];

    public function user(){
        return $this->belongsTo(User::class,"user_id","id");
    }

    public function admin(){
        return $this->belongsTo(Admin::class,"approved_by","id");
    }

    public function updateUser()
    {
        return $this->belongsTo(User::class, "updater_id", "id");
    }

    public function updateAdmin()
    {
        return $this->belongsTo(Admin::class, "updater_id", "id");
    }
    
    public function credit()
    {
        return $this->belongsTo(Credit::class, "credit_type", "type");
    }
    
    public function product()
    {
        return $this->belongsTo(Product::class, "product_id", "id");
    }

    public function currency(){
        return $this->belongsTo(Currency::class,"currency_id","id");
    }

    public function tk8Acc()
    {
        return $this->belongsTo(UserProduct::class, "user_id", "user_id");
    }
    
    public function tk8THAcc()
    {
        return $this->belongsTo(UserProduct::class, "user_id", "user_id");
    }
    
    public function fmtAcc()
    {
        return $this->belongsTo(UserProduct::class, "user_id", "user_id");
    }
    
    public function tpWebHook()
    {
        return $this->hasMany(TpWebHook::class, "table_id", "id")->where('table_type', TpWebHook::$tableType['withdraw']);
    }

    public static function add($data = []){
        $returnData =  DB::transaction(function () use ($data) {
            $internalID = User::select('id')->where('username','withdrawal')->where('user_type',User::$userType['internal-account'])->first()->id ?? Null;
            if(empty($internalID)){
                abort(400,'Invalid Internal Account.');
            }

            $dateTime = date('Y-m-d H:i:s');
            $userID = $data['uid'] ?? 0;
            $dateTime = $data['datetime'] ?? date('Y-m-d H:i:s');
            $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;
            $refNo = GenerateNumberTrait::generateReferenceNo(self::query(),'serial_number',NULL,'bank-withdrawal');
            $amount = $data['amount'];
            $creditId = $data['credit_id'];
            $walletData = $data['wallet_data'];
            $txnId = $data['txn_id'];
            $bankName = $data['bank_name'];
            $accountHolder = $data['account_holder'];
            $accountNumber = $data['account_number'];
            $productId = $data['product_id'];

            // Transaction
            $creditType = $walletData['credit_type'];
            $charge = (empty($walletData['processing_fee']) || !isset($walletData['processing_fee'])) ? 0 : $walletData['processing_fee'];

            // Third Party Withdrawal Processing Fee will be 0 - 04/04/2024 14:33
            $charge = 0;
            
            if (isset($charge) && $charge > 0) {
                $charge = DecimalTrait::setDecimal($charge);
            }
            $currencyRate = $walletData['rate'];


            $convertAmount = $amount;
            $amountAfterCharge = $amount; // Charge separate from amount logic
            if (isset($currencyRate)) $convertAmount = $amountAfterCharge * $currencyRate;
            else abort(400, json_encode(Lang::get('lang.withdrawal-invalid-currency-rate')));

            self::create([
                'serial_number' => $refNo,
                'user_id' => $userID,
                'credit_type' => $creditType,
                'amount' => $amount,
                'charges' => $charge,
                'receivable_amount' => $amountAfterCharge,
                'currency_id' => $walletData['currency_id'],
                'currency_rate' => $currencyRate,
                'converted_amount' => $convertAmount,
                'status' => self::$status['waiting-approval'],
                'bank_name' => $bankName,
                'account_holder' => $accountHolder,
                'account_number' => $accountNumber,
                'txn_id' => $txnId,
                'product_id' => $productId,
                'belong_id' => $belongId,
                'batch_id' => $belongId,
            ]);

            return array_merge([
                'merchant_ref' => $refNo,
                "amount" => $amount,
                "charges" => $charge,
                "receivable_amount" => DecimalTrait::setDecimal($amountAfterCharge + $charge), // payable_amount
                "date_time" => DateTrait::dateFormat($dateTime)
            ],($extraParams ?? []));
        });

        return $returnData;
    }

    public static function updateStatus($data = []){
        
        DB::transaction(function () use ($data) {
        });
        return true;
    }

    public static function list(array $params = []){
        $fromDate = $params['from_date'] ?? Null;
        $toDate = $params['to_date'] ?? Null;
        $updatedFrom = $params['updated_from'] ?? Null;
        $updatedTo = $params['updated_to'] ?? Null;
        $approvalFromDate = $params['approval_from_date'] ?? Null;
        $approvalToDate = $params['approval_to_date'] ?? Null;
        $username = $params['username'] ?? Null;
        $memberId = $params['member_id'] ?? Null;
        $phoneNo = $params['phone_no'] ?? null;
        $status = $params['status'] ?? Null;
        $type = $params['type'] ?? Null;
        $platform = $params['platform'] ?? Null;
        $txnId = $params['txn_id'] ?? Null;
        $withdrawal_reference = $params['withdrawal_reference'] ?? Null;
        $userFrom = $params['user_from'] ?? Null;
        $lang = config('app.locale') ?? 'en';
        $seeAll = $params['see_all'] ?? Null;

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        if (isset($params['export']) && ($params['export'] == 1)) {
            $jobData = $params;
            $jobData["model"] = get_class() ?? null;
            $jobData["function"] = __FUNCTION__;
            $jobData["module"] = MODULE;
            $jobData["creator_type"] = MODULE;
            $jobData["creator_id"] = Auth::user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, "Export Successfully");
        }

        $items = self::query()
            ->with(['user', 'admin', 'currency', 'updateUser', 'updateAdmin',
                'credit' => function($q){
                    return $q->select('type', 'code');
                },
                'product' => function($q){
                    return $q->select('id', 'code');
                },
            ])
            ->when((isset($fromDate) && isset($toDate)),function ($q) use($fromDate,$toDate){
                $q->where(DB::raw('DATE(created_at)'),">=",$fromDate);
                return $q->where(DB::raw('DATE(created_at)'),"<=",$toDate);
            })
            ->when((isset($updatedFrom) && isset($updatedTo)),function ($q) use($updatedFrom,$updatedTo){
                $q->where(DB::raw('DATE(updated_at)'),">=",$updatedFrom);
                return $q->where(DB::raw('DATE(updated_at)'),"<=",$updatedTo);
            })
            ->when(isset($status),function ($q) use($status){
                return $q->where('status', Withdrawal::$status[$status]);
            })
            ->when(MODULE == 'admin',function ($q) use($approvalFromDate,$approvalToDate,$username,$memberId,$phoneNo,$type,$withdrawal_reference, $userFrom){
                $q->when((isset($approvalFromDate) && isset($approvalToDate)),function ($q) use($approvalFromDate,$approvalToDate){
                    $q->where(DB::raw('DATE(approved_at)'),">=",$approvalFromDate);
                    return $q->where(DB::raw('DATE(approved_at)'),"<=",$approvalToDate);
                });
                $q->when(isset($username),function ($q) use($username){
                    return $q->whereRelation('user', 'username', 'LIKE', "%".$username."%");
                });
                $q->when(isset($memberId),function ($q) use($memberId){
                    return $q->whereRelation('user', 'member_id',$memberId);
                });
                $q->when(isset($phoneNo), function ($q) use ($phoneNo) {
                    return $q->whereRelation('user', function($q) use ($phoneNo) {
                        $phoneNo = preg_replace('/[^0-9]/', '', $phoneNo);
                        $q->where('phone_no', 'LIKE', "%$phoneNo%");
                    });
                });
                $q->when(isset($userFrom), function ($q) use ($userFrom) {
                    if($userFrom == "FW"){
                        // $query->whereNull('party');
                        return $q->whereRelation('user', function ($q) use ($userFrom){
                            return $q->whereNull('party');
                        },);
                    } else {
                        // $query->where('party',$userFrom);
                        return $q->whereRelation('user', function ($q) use ($userFrom){
                            return $q->where('party', $userFrom);
                        },);
                    }
                });
                $q->when(isset($type),function ($q) use($type){
                    return $q->where('withdrawal_type',Withdrawal::$withdrawalType[$type]);
                });
                $q->when(isset($withdrawal_reference),function ($q) use($withdrawal_reference){
                    return $q->where('serial_number',$withdrawal_reference);
                });
                return $q;
            })
            ->when(in_array(MODULE,['user','app']),function ($q){
                return $q->where('user_id',auth()->user()->id);
            })
            ->when(isset($platform),function ($q) use($platform){
                return $q->where('product_id', $platform);
            })
            ->when(isset($txnId),function ($q) use($txnId){
                return $q->where('txn_id', $txnId);
            })
            ->when(isset($withdrawal_reference),function ($q) use($withdrawal_reference){
                return $q->where('serial_number', $withdrawal_reference);
            })
            ;
            
        // $sumQuery = clone $items;
        $items = $items->select(["id","user_id","amount","currency_id","status","admin_remark","approved_at","approved_by","updater_id","product_id","credit_type","receivable_amount","charges","currency_rate","converted_amount","txn_id","serial_number", "bank_name", "account_holder", "account_number","created_at",'updated_at','deleted_at'])
                ->orderBy($order_by, $order_sort)
                ->when((!isset($seeAll) || $seeAll == 0),function ($q) use ($limit){
                    return $q->paginate($limit);
                });

        // $summary = [
        //     "total_withdrawal" => 0,
        //     "total_processing_fee" => 0,
        //     "total_receivable_amount" => 0,
        //     "total_net_withdrawal_amount" => 0,
        // ];
        $totalTable = [
            "total_amount" => 0,
            "total_processing_fee" => 0,
            "total_receivable_amount" => 0,
            "total_net_withdrawal_amount" => 0,
        ];

        // $sumQuery->selectRaw('SUM(amount) AS amount, SUM(charges) AS charges, SUM(receivable_amount) AS receivable_amount, ANY_VALUE(status) AS status')->get()
        //         ->map(function ($q) use (&$summary){
        //             $summary['total_withdrawal'] += $q->amount;
        //             $summary['total_processing_fee'] += $q->charges;
        //             $summary['total_receivable_amount'] += $q->receivable_amount;
        //             $summary['total_net_withdrawal_amount'] += $q->amount + $q->charges;
        //         });

        $mapFunc = function($q) use(&$totalTable){

            $status = array_search($q->status,self::$status);
            if (in_array(MODULE,['user','app'])) {
                $isEditable = in_array($q->status, [self::$status['waiting-approval']]) ? 1 : 0;
                switch($status) {
                    case 'pending':
                        $status = 'processing';
                        break;
                }
            } else if (MODULE == 'admin') {
                $isEditable = in_array($q->status, [self::$status['waiting-approval']]) ? 1 : 0;
            }
            $toCurrency = $q->currency->iso;

            switch (MODULE) {
                case 'admin':
                    if ($q->updater_id >= 1000000) {
                        $updater = $q->updateUser ? $q->updateUser->username : null;
                    } else if ($q->updater_id == 0) {
                        $updater = 'system';
                    } else {
                        $updater = $q->updateAdmin ? $q->updateAdmin->username : null;
                    }

                    $res = [
                        'id' => $q->id,
                        "withdrawal_reference" => $q->serial_number,
                        "created_at" => DateTrait::dateFormat($q->created_at),
                        "username" => $q->user->username ?? null,
                        "member_id" => $q->user->member_id ?? null,
                        "phone_no" => $q->user->phone_no ?? null,
                        'withdrawal_amount' => DecimalTrait::setDecimal($q->amount ?? 0),
                        'net_withdrawal_amount' => DecimalTrait::setDecimal(($q->amount + $q->charges)),
                        'processing_fee' => DecimalTrait::setDecimal($q->charges ?? 0),
                        "receivable_amount" => DecimalTrait::setDecimal($q->converted_amount ?? 0),
                        "updated_at" => DateTrait::dateFormat($q->updated_at),
                        "updated_by" => $updater,
                        'status' => $status,
                        'status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                        'is_editable' => $isEditable,
                        // 'bank_display' => Lang::has('lang.'.$q->userBank->bank->translation_code) ? Lang::get('lang.'.$q->userBank->bank->translation_code) : $q->userBank->bank->translation_code,
                        "bank_name" => $q->bank_name,
                        "account_holder" => $q->account_holder,
                        "account_number" => $q->account_number,
                        "txn_id" => $q->txn_id,
                        "platform_display" => ($q->product->code ?? '')." (".($q->credit->code ?? '').") ",
                        "user_from" => $q->user->party ?? "FW",
                    ];
                    $totalTable['total_amount'] += $q->amount;
                    $totalTable['total_processing_fee'] += $q->charges;
                    $totalTable['total_receivable_amount'] += $q->converted_amount;
                    $totalTable['total_net_withdrawal_amount'] += ($q->amount + $q->charges);
                    break;
                
                case 'app':
                    $res = [];
                    break;

                case 'user':
                    $res = [];
                    break;
            }

            return (object) $res;
        };

        if(($seeAll == 1)){
            $data = $items->get()->map($mapFunc);
            
        }else{
            $items->getCollection()->transform($mapFunc);
            $data =  (new ItemsCollection($items));
        }

        if(MODULE == 'admin'){
            if(($seeAll == 1)){
                $data = ['list' => $data->toArray()] + ["totalTable" => $totalTable];
                // $data = ['list' => $data->toArray()] + ["summary" => $summary] + ["totalTable" => $totalTable];
            }else{
                // $data->additional['summary'] = $summary;
                $data->additional['totalTable'] = $totalTable;
            }
        } 

        return $data;
    }

    public static function getWithdrawalDet(array $params = [])
    {
        if(MODULE == 'user'){
           $uid = Auth::user()->id; 
        } else {
            $uid = [];
        }

        $s3Client = null;
        $items = self::query()
            ->with(['user','admin','currency','updateUser','updateAdmin',
                'credit' => function($q){
                    return $q->select('type', 'code');
                },
                'product' => function($q){
                    return $q->select('id', 'code');
                },
            ])
            ->where('id', $params['id'])
            ->when(MODULE == 'user',function ($q) use($uid){
                return $q->where('user_id',$uid);
            })
            ->get()
            ->map(function ($q) use(&$s3Client) {
                $status = array_search($q->status,self::$status);
                $toCurrency = $q->currency->iso;
                // $userBank = $q->userBank->bank->translation_code ?? null;
                // $userBankHolder = $q->userBank->account_holder ?? null;
                // $userBankNumber = $q->userBank->account_no ?? null;

                if (in_array(MODULE,['user','app'])) {
                    $isEditable = in_array($q->status, [self::$status['waiting-approval']]) ? 1 : 0;
                    switch($status) {
                        case 'pending':
                            $status = 'processing';
                            break;
                    }
                } else if (MODULE == 'admin') {
                    $isEditable = in_array($q->status, [self::$status['waiting-approval']]) ? 1 : 0;
                }

                switch (MODULE) {
                    case 'admin':
                        if ($q->updater_id >= 1000000) {
                            $updater = $q->updateUser ? $q->updateUser->username : null;
                        } else if ($q->updater_id == 0) {
                            $updater = 'system';
                        } else {
                            $updater = $q->updateAdmin ? $q->updateAdmin->username : null;
                        }
    
                        $res = [
                            'id' => $q->id,
                            "withdrawal_reference" => $q->serial_number,
                            "created_at" => DateTrait::dateFormat($q->created_at),
                            "username" => $q->user->username ?? null,
                            "member_id" => $q->user->member_id ?? null,
                            "phone_no" => $q->user->phone_no ?? null,
                            'withdrawal_amount' => DecimalTrait::setDecimal($q->amount),
                            'net_withdrawal_amount' => DecimalTrait::setDecimal(($q->amount + $q->charges)),
                            'processing_fee' => DecimalTrait::setDecimal($q->charges),
                            "receivable_amount" => DecimalTrait::setDecimal($q->converted_amount),
                            // "bank_display" => Lang::has('lang.'.$userBank) ? Lang::get('lang.'.$userBank) : $userBank,
                            "bank_name" => $q->bank_name,
                            "account_holder" => $q->account_holder,
                            "account_number" => $q->account_number,
                            "txn_id" => $q->txn_id,
                            'status' => $status,
                            'status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                            'remark' => $q->admin_remark ?: '-',
                            "updated_at" => DateTrait::dateFormat($q->updated_at),
                            "updated_by" => $updater,
                            "is_editable" => $isEditable,
                            "platform_display" => ($q->product->code ?? '')." (".($q->credit->code ?? '').") ",
                        ];
                        break;
                    
                    case 'app':
                        $res = [];
                        break;
    
                    case 'user':
                        $res = [];
                        break;
                }

                return $res;
            })
            ->first();

        return $items;
    }

    // combine with tp_webhook
    public static function apiList(array $params = []){
        $fromDate = $params['from_date'] ?? Null;
        $toDate = $params['to_date'] ?? Null;
        $updatedFrom = $params['updated_from'] ?? Null;
        $updatedTo = $params['updated_to'] ?? Null;
        $approvalFromDate = $params['approval_from_date'] ?? Null;
        $approvalToDate = $params['approval_to_date'] ?? Null;
        $username = $params['username'] ?? Null;
        $memberId = $params['member_id'] ?? Null;
        $phoneNo = $params['phone_no'] ?? null;
        $status = $params['status'] ?? Null;
        $type = $params['type'] ?? Null;
        $platform = $params['platform'] ?? Null;
        $txnId = $params['txn_id'] ?? Null;
        $withdrawal_reference = $params['withdrawal_reference'] ?? Null;
        $lang = config('app.locale') ?? 'en';
        $seeAll = $params['see_all'] ?? Null;

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        if (isset($params['export']) && ($params['export'] == 1)) {
            $jobData = $params;
            $jobData["model"] = get_class() ?? null;
            $jobData["function"] = __FUNCTION__;
            $jobData["module"] = MODULE;
            $jobData["creator_type"] = MODULE;
            $jobData["creator_id"] = Auth::user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, "Export Successfully");
        }

        $items = self::query()
            ->with(['user', 'tpWebHook',
            // ->with(['user', 'admin', 'currency', 'updateUser', 'updateAdmin',
                // 'credit' => function($q){
                //     return $q->select('type', 'code');
                // },
                // 'product' => function($q){
                //     return $q->select('id', 'code');
                // },
            ])
            ->when((isset($fromDate) && isset($toDate)),function ($q) use($fromDate,$toDate){
                $q->where(DB::raw('DATE(created_at)'),">=",$fromDate);
                return $q->where(DB::raw('DATE(created_at)'),"<=",$toDate);
            })
            ->when((isset($updatedFrom) && isset($updatedTo)),function ($q) use($updatedFrom,$updatedTo){
                $q->where(DB::raw('DATE(updated_at)'),">=",$updatedFrom);
                return $q->where(DB::raw('DATE(updated_at)'),"<=",$updatedTo);
            })
            ->when(isset($status),function ($q) use($status){
                return $q->where('status', WithdrawalThirdParty::$status[$status]);
            })
            ->when(MODULE == 'admin',function ($q) use($approvalFromDate,$approvalToDate,$username,$memberId,$phoneNo,$type,$withdrawal_reference){
                $q->when((isset($approvalFromDate) && isset($approvalToDate)),function ($q) use($approvalFromDate,$approvalToDate){
                    $q->where(DB::raw('DATE(approved_at)'),">=",$approvalFromDate);
                    return $q->where(DB::raw('DATE(approved_at)'),"<=",$approvalToDate);
                });
                $q->when(isset($username),function ($q) use($username){
                    return $q->whereRelation('user', 'username', 'LIKE', "%".$username."%");
                });
                $q->when(isset($memberId),function ($q) use($memberId){
                    return $q->whereRelation('user', 'member_id',$memberId);
                });
                $q->when(isset($phoneNo), function ($q) use ($phoneNo) {
                    return $q->whereRelation('user', function($q) use ($phoneNo) {
                        $phoneNo = preg_replace('/[^0-9]/', '', $phoneNo);
                        $q->where('phone_no', 'LIKE', "%$phoneNo%");
                    });
                });
                $q->when(isset($type),function ($q) use($type){
                    return $q->where('withdrawal_type',WithdrawalThirdParty::$withdrawalType[$type]);
                });
                $q->when(isset($withdrawal_reference),function ($q) use($withdrawal_reference){
                    return $q->where('serial_number',$withdrawal_reference);
                });
                return $q;
            })
            ->when(in_array(MODULE,['user','app']),function ($q){
                return $q->where('user_id',auth()->user()->id);
            })
            ->when(isset($platform),function ($q) use($platform){
                return $q->where('product_id', $platform);
            })
            ->when(isset($txnId),function ($q) use($txnId){
                return $q->where('txn_id', $txnId);
            })
            ->when(isset($withdrawal_reference),function ($q) use($withdrawal_reference){
                return $q->where('serial_number', $withdrawal_reference);
            });

        $items = $items
                ->orderBy($order_by, $order_sort)
                ->when((!isset($seeAll) || $seeAll == 0),function ($q) use ($limit){
                    return $q->paginate($limit);
                });

        $productCode = Product::select('code')->get();

        $mapFunc = function($q) use($productCode){

            $status = array_search($q->status,self::$status);

            switch (MODULE) {
                case 'admin':
                    $res = [
                        'id' => $q->id,
                        "withdrawal_reference" => $q->serial_number,
                        "created_at" => DateTrait::dateFormat($q->created_at),
                        "username" => $q->user->username ?? null,
                        'status' => $status,
                        'status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                    ];
                    foreach($productCode as $value){
                        $res[strtolower($value->code)] = [];
                    }

                    if(!empty($q->tpWebHook)){
                        foreach($q->tpWebHook as $hook){
                            $data = [
                                'type' => (array_search($hook->type, TpWebHook::$type) ?? null),
                                'req_data' => $hook->req_data,
                                'res_data' => $hook->res_data,
                                'created_at' => DateTrait::dateFormat($hook->created_at),
                            ];
                            $res[$hook->platform][] = $data;
                        }
                    }
                    break;
                
                case 'app':
                    $res = [];
                    break;

                case 'user':
                    $res = [];
                    break;
            }

            return (object) $res;
        };

        if(($seeAll == 1)){
            $data = $items->get()->map($mapFunc);
            
        }else{
            $items->getCollection()->transform($mapFunc);
            $data =  (new ItemsCollection($items));
        }

        if(MODULE == 'admin'){
            if(($seeAll == 1)){
                $data = ['list' => $data->toArray()];
                // $data = ['list' => $data->toArray()] + ["summary" => $summary] + ["totalTable" => $totalTable];
            }else{
                // $data->additional['summary'] = $summary;
                // $data->additional['totalTable'] = $totalTable;
            }
        } 

        return $data;
    }
}