<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use App\Traits\DateTrait;
use App\Traits\FirebaseTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Soundasleep\Html2Text;

class UserNotification extends Model
{
    use DateTrait, FirebaseTrait, SoftDeletes;

    protected $table = 'user_notification';

    protected $hidden = [];

    protected $fillable = [
        'user_id',
        'notice_type',
        'notification_id',
        'reference_data',
        'read',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected $casts = [
        'reference_data' => 'array',
    ];

    public static $noticeType = [
        'sponsorBonus' => 1,
        'ttransferProcessing' => 2,
        'ttransferApproved' => 3,
        'ttransferRejected' => 4,
        'withdrawalProcessing' => 5,
        'withdrawalApproved' => 6,
        'withdrawalRejected' => 7,
        'onlineBankingDepositApproved' => 8,
        'manualDepositApproved' => 9,
        'manualDepositRejected' => 10,
        'transferReceived' => 11,
        'specialBonus' => 12,
        'transferSended' => 13,
        'vipBonus' => 14,
        'agentBonus' => 15,
        'vipRankUp' => 16,
        'cryptoDepositApproved' => 17,
        'ewalletDepositApproved' => 18,
        'onlineBankingThaiDepositApproved' => 19,
        'cryptoDepositRejected' => 20,
        'ewalletDepositRejected' => 21,
        'onlineBankingThaiDepositRejected' => 22,
        'onlineBankingDepositRejected' => 23,
        'convertSuccess' => 24,
        'custom' => 25, // Notifications sent from admin panel
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function notification()
    {
        return $this->belongsTo(Notification::class);
    }

    public static function getList(array $params = [])
    {
        $userID = $params['user_id'] ?? null;
        $seeAll = $params['see_all'] ?? null;

        $sortableColumns = ['id', 'created_at'];

        // Order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // Sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = ($seeAll == 1) ? null : ($params['limit'] ?? config('app.pagination_rows'));

        $items = self::query()
            ->with(['user', 'notification' => function ($q) {
                $q->withTrashed();
            }])
                    // ->select(['id', 'user_id', 'notice_type', 'reference_data', 'read', 'created_at'])
            ->when(in_array(MODULE, ['app', 'user']), function ($q) use ($userID) {
                $q->where('user_id', $userID);
            })
            ->orderBy($order_by, $order_sort)
            ->when((! isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $mapFunc = function ($q) {
            $noticeType = array_search($q->notice_type, self::$noticeType) ?? null;
            $userLang = $q->user->lang ?? 'en';

            $referenceData = $q->reference_data ?? [];
            if (isset($referenceData['en']) || isset($referenceData['cn']) || isset($referenceData['my'])) {

                $langContent = $referenceData[$userLang] ?? $referenceData['en'] ?? null;

                if (!$langContent && count($referenceData) > 0) {
                    $firstLang = array_key_first($referenceData);
                    $langContent = $referenceData[$firstLang];
                }

                if ($langContent) {
                    return [
                        'id' => $q->id,
                        'notice_type' => $noticeType,
                        'title' => Html2Text::convert($langContent['title'] ?? '', ['ignore_errors' => true]),
                        'content' => Html2Text::convert($langContent['content'] ?? '', ['ignore_errors' => true]),
                        'read' => $q->read,
                        'param_data' => $q->reference_data,
                        'created_at' => DateTrait::dateFormat($q->created_at),
                    ];
                }
            }

            $keyAry = $valAry = [];
            foreach ($referenceData as $refKey => $refVal) {
                if (is_array($refVal)) continue;

                $keyAry[] = '{{'.$refKey.'}}';
                $valAry[] = $refVal;
            }

            $template = FirebaseTrait::template($noticeType, $userLang, $q?->notification);
            $content = '';
            if (is_string($template['body'] ?? null)) {
                $content = str_replace($keyAry, $valAry, $template['body']);
            }

            return [
                'id' => $q->id,
                'notice_type' => $noticeType,
                'title' => Html2Text::convert($template['title'] ?? '', ['ignore_errors' => true]),
                'content' => Html2Text::convert($content, ['ignore_errors' => true]),
                'read' => $q->read,
                'param_data' => $q->reference_data,
                'created_at' => DateTrait::dateFormat($q->created_at),
            ];
        };

        if (($seeAll == 1)) {
            return ['list' => $items->get()->map($mapFunc)->toArray()];
        } else {
            $items->getCollection()->transform($mapFunc);

            return (new ItemsCollection($items))->toArray();
        }
    }

    public static function readNotification($data = [])
    {
        DB::transaction(function () use ($data) {
            self::where('user_id', $data['user_id'])->when(isset($data['id_ary']), function ($q) use ($data) {
                return $q->whereIn('id', $data['id_ary']);
            })->update(['read' => 1]);
        });

        return true;
    }
}
