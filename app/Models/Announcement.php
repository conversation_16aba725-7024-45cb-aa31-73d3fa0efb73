<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Http\Resources\ItemsCollection;
use App\Traits\DateTrait;
use App\Traits\S3Trait;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
class Announcement extends Model
{
    use S3Trait;
    
    protected $table = 'announcement';

    protected $hidden = [
    ];

    protected $fillable = [
        "start_date",
        "end_date",
        "status",
        "creator_id",
        "creator_type",
        "created_at",
        "updated_at",
        "updater_id",
        "deleted_at",
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
    ];

    public function announcementDetail(){
        return $this->hasMany(AnnouncementDetail::class, 'announcement_id', 'id');
    }

    public function admin(){
        return $this->belongsTo(Admin::class,'creator_id','id');
    }

     public function updater() {
        return $this->belongsTo(Admin::class, 'updater_id', 'id');
    }

    public static function getNews(array $params = []) {
        // https://forexnewsapi.com/ -> place to get news (general & topics -> all currency pairs)
        $directorie = File::files(resource_path('news'));
        $res = File::get($directorie[0]);
        $arr = json_decode($res, true)['data'];
        return $arr;
    }

    public static function addAnnouncement(array $params = []){
        return DB::transaction(function () use ($params){
            $creatorId = Auth::user()->id ?? null;
            $creatorType = isset($creatorId) ? MODULE : 'system';

            $insertAnnouncement = [
                'status' => isset($params['status']) ? self::$status[$params['status']] : self::$status['active'],
                'creator_id' => $creatorId,
                'creator_type' => $creatorType
            ];

            if (isset($params['start_date'])) {
                $insertAnnouncement['start_date'] = $params['start_date'];
            }

            if (isset($params['end_date'])) {
                $insertAnnouncement['end_date'] = $params['end_date'];
            }

            $announcementId = self::create($insertAnnouncement)->id;

            // Insert Announcement Data
            foreach ($params['announcement_data'] as $announcementData) {
                AnnouncementDetail::create([
                    "announcement_id" => $announcementId,
                    "author" => $announcementData['author'] ?? null,
                    "subject" => $announcementData['subject'],
                    'short_description' => $announcementData['short_description'] ?? null,
                    "description" => $announcementData['description'],
                    "language_type" => config('language')[$announcementData['language_type']],
                    "image_data" => ($announcementData['image_data'] ?? []),
                    "attachment_data" => ($announcementData['attachment_data'] ?? []),
                ]);
            }

            return true;
        });
    }

    public static function getAnnouncementList(array $params = []){
        $fromDate = $params['from_date'] ?? Null;
        $toDate = $params['to_date'] ?? Null;
        $postedFromDate = $params['posted_from_date'] ?? null;
        $postedToDate = $params['posted_to_date'] ?? null;
        $subject = $params['subject'] ?? null;
        $author = $params['author'] ?? null;
        $status = $params['status'] ?? null;
        $lang = config('app.locale') ?? 'en';
        $dateTime = date('Y-m-d H:i:s');
        $date = date('Y-m-d',strtotime($dateTime));
        $seeAll = $params['see_all'] ?? null;
        $skipTrending = isset($params['skip_trending']) && !empty($params['skip_trending']) ? 1 : 0;

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');
        $items = self::query()
            ->with(['announcementDetail' => function($q){
                $q->orderBy('language_type','DESC');
                return $q;
            },'admin', 'updater'])
            ->select(["id","start_date","end_date","status","creator_id","creator_type","created_at","updated_at","updater_id"])
            ->when(in_array(MODULE, ['user', 'app']), function ($q) use ($date){
                $q->where(function($q) use ($date){
                    $q->where(function($q) use ($date){
                        $q->where(DB::raw('DATE(start_date)'),'<=',$date);
                        $q->where(DB::raw('DATE(end_date)'),'>=',$date);
                    });
                    $q->orWhere(function($q) use ($date){
                        $q->where(DB::raw('DATE(start_date)'),'<=',$date);
                        $q->where('end_date',null);
                    });
                    $q->orWhere(function($q) use ($date){
                        $q->where('start_date',null)->where('end_date',null);
                    });
                });
                $q->where('status', self::$status['active']);
                return $q;
            })
            ->when((isset($fromDate)),function ($q) use($fromDate){
                return $q->where(DB::raw('DATE(created_at)'),">=",$fromDate);
            })
            ->when((isset($toDate)),function ($q) use($toDate){
                return $q->where(DB::raw('DATE(created_at)'),"<=",$toDate);
            })
            ->when((isset($postedFromDate)), function ($q) use ($postedFromDate) {
                $q->where(function($query) use ($postedFromDate) {
                    return $query->where(DB::raw('DATE(start_date)'), ">=", $postedFromDate)
                    ->orWhere(function($queries) use ($postedFromDate) {
                        return $queries->Where(DB::raw('DATE(created_at)'), ">=", $postedFromDate)->whereNull('start_date');
                    });
                });
            })
            ->when((isset($postedToDate)), function ($q) use ($postedToDate) {
                $q->where(function ($query) use ($postedToDate) {
                    return $query->where(DB::raw('DATE(start_date)'), "<=", $postedToDate)
                        ->orWhere(function ($queries) use ($postedToDate) {
                            return $queries->Where(DB::raw('DATE(created_at)'), "<=", $postedToDate)->whereNull('start_date');
                        });
                });
            })
            ->when((isset($subject)),function ($q) use($subject){
                $q->whereHas('announcementDetail',function ($q) use ($subject) {
                    return $q->where('subject', 'LIKE', '%'.$subject.'%');
                });
            })
            ->when((isset($author)),function ($q) use($author){
                $q->whereHas('announcementDetail',function ($q) use ($author) {
                    return $q->where('author', 'LIKE', '%'.$author.'%');
                });
            })
            ->when((isset($status)),function ($q) use($status){
                $q->where('status', $status);
            })
            ->whereNull('deleted_at')
            ->orderBy($order_by, $order_sort);

        $mapFunc = function($q) use($lang){
            foreach ($q->announcementDetail as $key => $value) {
                $defaultSetting = $value;

                if(array_search($value['language_type'],config('language')) == $lang){
                    $defaultSetting = $value;
                    break;
                }
            }

            if (isset($defaultSetting->image_data)) {
                $imageAry = $defaultSetting->image_data;
                foreach ($imageAry as &$imageRow) {
                    $imageRow['image_name_display'] = env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN').$imageRow['image_name'];
                }
            }

            $res = [
                "id" => $q->id,
                'author' => $defaultSetting->author,
                "subject" => $defaultSetting->subject,
                'short_description' => $defaultSetting->short_description,
                'image_data' => $imageAry,
                'start_date' => $q->start_date ? DateTrait::dateformat($q->start_date, false) : (DateTrait::dateformat($q->created_at, false) ?? null),
                'end_date' => $q->end_date ? DateTrait::dateformat($q->end_date, false) : null,
            ];
            
            if(MODULE == 'admin'){
                $status = array_search($q->status, self::$status);
                $statusDisplay = Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status;

                $res = array_merge($res,[
                    'created_at' => DateTrait::dateFormat($q->created_at),
                    'status' => $status,
                    'status_display' => $statusDisplay,
                    'updated_by' => $q->updater->username ?? null,
                    'updated_at' => isset($q->updater) ? DateTrait::dateFormat($q->updated_at) : null,
                ]);
            }
            
            return (object) $res;
        };

        if ((isset($params['see_all']) && $params['see_all'] == 1)) {
            return $items->get()->map($mapFunc)->toArray();
        } else {
            if (MODULE == 'admin' || isset($params['dashboard']) || $skipTrending == 1) {
                $items = $items->paginate($limit);
                $items->getCollection()->transform($mapFunc);
                $data = (new ItemsCollection($items))->toArray();
            } else {
                $ignore = $items->take(3)->pluck('id');
                if (!isset($params['page']) || $params['page'] == 1) {
                    $trending = $items->take(3)->orderBy('id', 'desc')->get()->map($mapFunc)->toArray();
                } 
                $items = $items->whereNotIn('id', $ignore)->paginate($limit);
                $items->getCollection()->transform($mapFunc);
                $data = (new ItemsCollection($items))->toArray();
                if (isset($trending)) $data['trending'] = $trending;
            }
            return $data;
        }

        return (new ItemsCollection($items));
    }

    public static function getAnnouncementDetail(array $params = []){
        $lang = config('app.locale') ?? 'en';
        $dateTime = date('Y-m-d H:i:s');
        $date = date('Y-m-d',strtotime($dateTime));

        $items = self::query()
            ->with(['announcementDetail' => function($q){
                return $q->select('announcement_id','author','subject','short_description','description','language_type','image_data','attachment_data');
            }])
            ->where('id',$params['id'])
            ->when(in_array(MODULE, ['user', 'app']), function ($q) use ($date){
                $q->where(function($sq) use ($date){
                    $sq->where(DB::raw('DATE(start_date)'),'<=',$date)->where(DB::raw('DATE(end_date)'),'>=',$date);
                    $sq->orWhere('start_date',null)->where('end_date',null);
                    return $sq;
                });
                return $q;
            })
            ->select(["id","start_date","end_date","status","creator_id","creator_type","created_at","updated_at"])
            ->get()
            ->map(function ($q) use ($lang){                
                $status = array_search($q->status,self::$status);
                $announcement = $q->announcementDetail->toArray();

                $res = [];

                if(MODULE == 'user'){
                    foreach ($announcement as $key => $value) {
                        $announcementData = $value;
                        if(array_search($value['language_type'],config('language')) == $lang){
                            $announcementData = $value;
                            break;
                        }
                    }

                    unset($announcementData['announcement_id']);
                    $announcementData['language_type'] = array_search($announcementData['language_type'],config('language'));
                    $announcementData['language_type_display'] = Lang::has('lang.'.$announcementData['language_type']) ? Lang::get('lang.'.$announcementData['language_type']) : $announcementData['language_type'];

                    foreach ($announcementData['image_data'] as &$imageData) {
                        $imageData['image_name_display'] = env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN').$imageData['image_name'];
                    }
                    foreach ($announcementData['attachment_data'] as &$attachmentData) {
                        $attachmentData['attachment_name_display'] = env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN').$attachmentData['attachment_name'];
                    }

                    $announcementData['start_date'] = $q->start_date ? DateTrait::dateformat($q->start_date, false) : (DateTrait::dateformat($q->created_at, false) ?? null);
                    $announcementData['end_date'] = $q->end_date ? DateTrait::dateformat($q->end_date, false) : null;

                    $res = $announcementData ?? [];

                }else{
                    $announcementData = $announcement;

                    if (MODULE == 'app') {
                        $announcementData = [];
                        foreach ($announcement as $key => $value) {
                            $announcementData[] = $value;
                            if(array_search($value['language_type'],config('language')) == $lang){
                                $announcementData = [];
                                $announcementData[] = $value;
                                break;
                            }
                        }
                    }

                    foreach ($announcementData as &$announcementDetail) {
                        unset($announcementDetail['announcement_id']);
                        $announcementDetail['language_type'] = array_search($announcementDetail['language_type'],config('language'));
                        $announcementDetail['language_type_display'] = Lang::has('lang.'.$announcementDetail['language_type']) ? Lang::get('lang.'.$announcementDetail['language_type']) : $announcementDetail['language_type'];
                        foreach ($announcementDetail['image_data'] as &$imageData) {
                            $imageData['image_name_display'] = env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN').$imageData['image_name'];
                        }
                        foreach ($announcementDetail['attachment_data'] as &$attachmentData) {
                            $attachmentData['attachment_name_display'] = env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN').$attachmentData['attachment_name'];
                        }
                    }

                    $res = array_merge($res,[
                        "id" => $q->id,
                        "start_date" => isset($q->start_date) ? date("Y-m-d", strtotime($q->start_date)) : null,
                        "end_date" => isset($q->end_date) ? date("Y-m-d", strtotime($q->end_date)) : null,
                        "status" => $status,
                        "status_display" => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                        'announcement_data' => $announcementData ?? [],
                    ]);
                }

                return $res;
            })->first();

        $return ['data']  = $items ?? [];
        return $return;
    }

    public static function editAnnouncement(array $params = []){
        return DB::transaction(function () use ($params){
            $creatorId = Auth::user()->id ?? null;
            $creatorType = isset($creatorId) ? MODULE : 'system';

            $announcement = self::find($params['id']);
            
            $updateAnnouncement = [
                'status' => self::$status[$params['status']],
                'start_date' => $params['start_date'] ?? null,
                'end_date' => $params['end_date'] ?? null,
                'updater_id' => Auth::user()->id ?? null,
            ];

            $announcement->update($updateAnnouncement);
            
            // Insert Announcement Data
            foreach ($params['announcement_data'] as $announcementData) {
                $validLanguage[] = config('language')[$announcementData['language_type']];
                AnnouncementDetail::updateOrCreate(['announcement_id'=>$params['id'],'language_type'=>config('language')[$announcementData['language_type']]],[
                    'author' => $announcementData['author'],
                    "subject" => $announcementData['subject'],
                    'short_description' => $announcementData['short_description'] ?? null,
                    "description" => $announcementData['description'],
                    "attachment_data" => ($announcementData['attachment_data'] ?? []),
                    "image_data" => ($announcementData['image_data'] ?? []),
                    "deleted_at" => null
                ]);
            }

            AnnouncementDetail::where('announcement_id',$params['id'])->whereNotIn('language_type',($validLanguage ?? []))->update(['deleted_at'=>DB::raw('now()')]);

            return true;
        });
    }

    public static function deleteAnnouncement(array $params = []){
        return DB::transaction(function () use ($params){
            $creatorId = Auth::user()->id ?? null;
            $creatorType = isset($creatorId) ? MODULE : 'system';

            $announcement = Announcement::find($params['id']);
            $announcement->update(['deleted_at'=>DB::raw('now()')]);;

            return true;
        });
    }
}
