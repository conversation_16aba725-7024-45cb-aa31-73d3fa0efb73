<?php

namespace App\Models;

use App\Traits\DecimalTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class PaymentMethods extends Model
{
    use SoftDeletes;

    protected $table = 'payment_method';

    protected $hidden = [];

    protected $fillable = [
        'credit_type',
        'status',
        'min_percentage',
        'max_percentage',
        'payment_type',
        'type',
        'group_type',
        'created_at',
        'deleted_at'
    ];

    public function credit()
    {
        return $this->belongsTo(Credit::class, "credit_type", "type");
    }

    public static function getPaymentMethod($userID, $subject)
    {
        if (!isset($userID) || !isset($subject)) {
            return false;
        }

        $paymentMethodRes = self::query()
            ->select([
                'credit_type',
                'min_percentage',
                'max_percentage',
                'is_external',
            ])
            ->where('payment_type', $subject)
            ->where('status', 1)
            ->wherenull('deleted_at')
            ->get()
            ->map(function ($q) use ($userID) {
                return [
                    'creditType' => $q->credit_type,
                    'creditTypeDisplay' => Lang::has('lang.' . $q->credit_type) ? Lang::get('lang.' . $q->credit_type) : $q->credit_type,
                    'minPercentage' => DecimalTrait::setDecimal($q->min_percentage),
                    'maxPercentage' => DecimalTrait::setDecimal($q->max_percentage),
                    'isExternal' => $q->is_external,
                    'balance' => Credit::getBalance($userID, $q->credit_type),
                ];
            })
            ->keyBy('creditType')
            ->toArray();

        if (empty($paymentMethodRes)) {
            return null;
        }

        return $paymentMethodRes;
    }

    public static function paymentVerification($userID, $subject, $spendCreditAry, $payableAmount, $trxnPass = null, $isRepayment = false)
    {
        $paymentTrxnPassSwitch = SystemSetting::where('name', 'paymentTrxnPassSwitch')->first()->value ?? 1;
        $internalCount = 0;
        $externalCount = 0;
        $totalAmount = 0;
        $data = [];

        if (MODULE != 'admin') {
            if ($paymentTrxnPassSwitch == 1) {
                $checkTrxnPass = UserDetail::query()
                    ->where([
                        'user_id' => $userID,
                        'name' => 'transaction_password'
                    ])
                    ->whereNull('deleted_at')
                    ->first();

                if (((empty($checkTrxnPass)) || (Hash::check($trxnPass, $checkTrxnPass->value) != true))) {
                    abort(400, json_encode(Lang::get('lang.invalid-pin')));
                }
            }
        }

        $validatePaymentMethod = self::getPaymentMethod($userID, $subject);

        if ((!$validatePaymentMethod) || (!isset($validatePaymentMethod))) {
            abort(400, json_encode('Payment method not found.'));
        }

        foreach ($spendCreditAry as $spendCreditRow) {
            $credit = $spendCreditRow['credit'];
            $amount = $spendCreditRow['amount'];

            $validateCredit = isset($validatePaymentMethod[$credit]) ? $validatePaymentMethod[$credit] : null;

            if (!$validateCredit) {
                abort(400, json_encode([$credit => 'Invalid payment method.']));
            }

            $minPercentage = $validateCredit['minPercentage'] ?? 0;
            $maxPercentage = $validateCredit['maxPercentage'] ?? 0;
            $isExternal = $validateCredit['isExternal'] ?? 0;
            $balance = $validateCredit['balance'] ?? 0;
            $data[$credit]['amount'] = $amount;
            $data[$credit]['isExternal'] = $isExternal;

            $minPrice = DecimalTrait::setDecimal($payableAmount * ($minPercentage / 100) ?? 0);
            $maxPrice = DecimalTrait::setDecimal($payableAmount * ($maxPercentage / 100) ?? 0);

            if (($maxPrice > 0) && ($amount > $maxPrice)) {
                $descriptionLang = Lang::has('lang.payment-maximum-amount') ? Lang::get('lang.payment-maximum-amount') : null;
                $descriptionLang = str_replace(['%maxPrice%'], [$maxPrice], $descriptionLang);
                abort(400, json_encode([$credit => $descriptionLang]));
            }

            if (($minPrice > 0) && ($amount < $minPrice)) {
                $descriptionLang = Lang::has('lang.payment-minimum-amount') ? Lang::get('lang.payment-minimum-amount') : null;
                $descriptionLang = str_replace(['%minPrice%'], [$minPrice], $descriptionLang);
                abort(400, json_encode([$credit => $descriptionLang]));
            }

            if ($isExternal == 0) {
                if ($balance < $maxPrice) {
                    abort(400, json_encode([$credit => Lang::get('lang.credit-insufficient-balance')]));
                }

                $internalCount += 1;
            } else {
                $externalCount += 1;
            }

            $totalAmount += $amount;
        }

        $totalAmount = DecimalTrait::setDecimal($totalAmount);

        if (($totalAmount != $payableAmount) || ($totalAmount < 0)) {
            abort(400, json_encode('Invalid total amount.'));
        }

        if ($externalCount > 1) {
            abort(400, json_encode('Only one external payment is allowed.'));
        }

        if ($internalCount <= 0 && $isRepayment == false) {
            // abort(400, json_encode('Payment via wallet is required.'));
        }

        return $data;
    }

    public static function paymentConfirmation($userID, $subject, $spendCreditAry, $payableAmount, $trxnPass = null, $belongId)
    {

        if (!isset($belongId)) abort(400, json_encode('Failed to retrieve belong ID.'));
        $paymentVerification = PaymentMethod::paymentVerification($userID, $subject, $spendCreditAry, $payableAmount, $trxnPass);

        DB::transaction(function () use ($userID, $subject, $paymentVerification, $payableAmount, $belongId) {
            $internalID = User::select('id')->where('username', 'creditSales')->where('user_type', User::$userType['internal-account'])->first()->id ?? Null;
            $dateTime = $data['datetime'] ?? date('Y-m-d H:i:s');

            foreach ($paymentVerification as $credit => $creditRow) {
                $isExternal = $creditRow['isExternal'] ?? 0;
                $amount = $creditRow['amount'] ?? 0;

                if ($isExternal == 0) {
                    $creditType = $credit;
                    $creditDeduction = CreditTransaction::insertTransaction($userID, $internalID, $userID, $creditType, $amount, $subject, $belongId, $belongId, null, $dateTime, null, null, null, null, null, true);

                    if (!$creditDeduction) {
                        abort(400, json_encode([$credit => 'Failed to deduct credit.']));
                    }
                }
            }
        });

        return $paymentVerification;
    }
}
