<?php

namespace App\Models;

use App\Services\BetLogService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransferTurnover extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'amount',
        'target_turnover',
        'achieved_turnover',
        'achieved_at',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * TODO:
     *
     * 1. Check User is AgentID
     * 2. Check Bet Log Service Total Turnover
     * 3. Verify the Bet Log
     */
    public static function add($toUserId, $amount)
    {
        TransferTurnover::create([
            'user_id' => $toUserId,
            'amount' => $amount,
            'target_turnover' => $amount,
            'achieved_turnover' => 0,
            'status' => false,
        ]);
    }

    public static function updateUserTurnoverStatus($user)
    {
        $transferTurnover = TransferTurnover::where('status', false)
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'ASC')
            ->first();

        $totalTargetTurnover = TransferTurnover::where('status', false)
            ->where('user_id', $user->id)
            ->sum('target_turnover');

        if (! isset($transferTurnover)) {
            return [
                'status' => true,
                'target_turnover' => 0,
                'total_turnover' => 0,
            ];
        }

        $res = (new BetLogService)->getAllBetRecords($transferTurnover->created_at, now(), $user->uuid);
        if ($res) {
            $totalTurnover = $res['data']['total_turnover'];
            $remainingTurnover = $totalTurnover;

            $transferTurnover?->each(function ($e) use (&$remainingTurnover) {
                if ($remainingTurnover >= $e->target_turnover) {
                    $e->update([
                        'status' => true,
                        'achieved_at' => now(),
                        'achieved_turnover' => $e->target_turnover,
                    ]);
                    $remainingTurnover -= $e->target_turnover;
                } else {
                    $e->update([
                        'achieved_turnover' => $remainingTurnover,
                    ]);
                    $remainingTurnover = 0;
                }
            });

            if ($totalTurnover >= $totalTargetTurnover) {
                return [
                    'status' => true,
                    'target_turnover' => 0,
                    'total_turnover' => 0,
                ];
            } else {
                return [
                    'status' => false,
                    'target_turnover' => $totalTargetTurnover,
                    'total_turnover' => $totalTurnover,
                ];
            }
        }
    }
}
