<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UnitPrice extends Model
{
    protected $table = 'unit_price';

    protected $hidden = [];

    protected $fillable = [
        'unit_price',
        'type',
        'creator_id',
        'creator_type',
        'actived_date',
        'created_at',
        'deleted_at',
    ];

    public static function getLatestUnitPrice($type = null,$dateTime = null){
        if(!$type){
            return 1.00;
        }

        $unitPrice = UnitPrice::where('type',$type)
            ->when($dateTime, function ($query) use ($dateTime) {
                $query->where('actived_date','<=',$dateTime);
            })
            ->latest()
            ->first();

        $unitPrice = $unitPrice->unit_price ?? 1.00;
        
        return $unitPrice;
    }
}
