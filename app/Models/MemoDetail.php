<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Http\Resources\ItemsCollection;
use Illuminate\Support\Facades\Auth;

class MemoDetail extends Model
{
    const UPDATED_AT = null;

    const CREATED_AT = null;
    
    protected $table = 'memo_detail';

    protected $hidden = [
    ];

    protected $casts = [
        "upload_data" => "array"
    ];

    protected $fillable = [
        'memo_id',
        'subject',
        'description',
        'language_type',
        'upload_data',
        'deleted_at',
    ];
}
