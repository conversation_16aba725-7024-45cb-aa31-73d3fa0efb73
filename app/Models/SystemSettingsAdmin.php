<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SystemSettingsAdmin extends Model
{
    const UPDATED_AT = null;

    protected $table = 'system_settings_admin';

    protected $hidden = [
    ];

    protected $fillable = [
        'name',
        'type',
        'value',
        'reference',
        'ref_id',
        'status',
        'active_at',
        'created_at',
        'creator_id',
        'updated_at',
    ];

    public static $depositMethodStatus = [
        'off' => 0,
        'on' => 1,
    ];

    public function admin()
    {
        return $this->belongsTo(Admin::class, "creator_id", "id");
    }
}