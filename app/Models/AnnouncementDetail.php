<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Http\Resources\ItemsCollection;
use Illuminate\Support\Facades\Auth;

class AnnouncementDetail extends Model
{
    const UPDATED_AT = null;

    const CREATED_AT = null;
    
    protected $table = 'announcement_detail';

    protected $hidden = [
    ];

    protected $casts = [
        "image_data" => "array",
        "attachment_data" => "array",
    ];

    protected $fillable = [
        "announcement_id",
        'author',
        "subject",
        'short_description',
        "description",
        "language_type",
        "image_data",
        "attachment_data",
        "deleted_at",
    ];
}
