<?php

namespace App\Models;

use App\Traits;
use App\Models\Providers\MtMachine;
use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Store extends Model
{
    protected $table = 'store';

    protected $hidden = [];


    public static $transfer_action = [
        "shop_to_customer" => 0,
        "all" => 1,
    ];

    protected $fillable = [
        'store_id',
        'user_id',
        'name',
        'is_disable',
        'is_wallet_transferable',
        'is_allow_deposit',
        'is_allow_withdraw',
        'is_pos',
        'is_agent',
        'transfer_action',
        'is_dummy',
        'jk_agent_id',
        'priority',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected $casts = [
        'is_pos' => 'boolean',
    ];

    /* -------------------------------------------------------------------------- */
    /*                                Relationships */
    /* -------------------------------------------------------------------------- */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function mtMachines()
    {
        return $this->hasMany(MtMachine::class);
    }

    public function userBankCards()
    {
        return $this->hasManyThrough(UserBank::class, User::class, 'store_id', 'user_id', 'store_id', 'id');
    }

    /* -------------------------------------------------------------------------- */
    /*                                   Scopes                                   */
    /* -------------------------------------------------------------------------- */
    public function scopeActive(Builder $query)
    {
        return $query->where('status', true);
    }

    public function scopeInactive(Builder $query)
    {
        return $query->where('status', false);
    }

    public static function getDrawDateByStoreId($storeId)
    {
        if (env('APP_ENV') == 'staging') {
            return null;
        }

        // $inStores = [29, 30, 31, 6, 7, 8, 12, 20001, 20002, 20003, 20004, 20005, 20006, 20007, 20008, 20009, 20010, 20011];
        // if (in_array($storeId, $inStores)) {
        //     return 'Coming Soon'; //'19-03-2025';
        // }

        return null;

        // return '* 1/1/2025 - 2/2/2025';
    }

    public static function refreshStore(array $params = [])
    {
        $type = 'get_store';
        $params = null;
        $result = Traits\SonicTrait::post($params, $type);

        if (! isset($result['status']) || $result['status'] == false) {
            return;
        }

        foreach ($result['data'] as $eachData) {
            self::updateOrCreate([
                'store_id' => $eachData['id'],
            ], [
                'name' => $eachData['name'],
            ]);
        }

        self::insertDefaultUser();
    }

    public static function insertDefaultUser()
    {
        $storeRes = self::whereNull('user_id')->get();
        $countryRes = Country::where('name', 'malaysia')->first();

        foreach ($storeRes as $eachStore) {

            $phoneNo = Traits\GenerateNumberTrait::generateStorePhoneNumber(User::query(), $countryRes->country_code, '99', '99', '10');
            $password = Traits\GenerateNumberTrait::generateRandomPassword();
            $userParams = [
                'preferred_language' => 'en',
                'name' => $eachStore->name,
                'country_id' => $countryRes->id,
                'dial_code' => $countryRes->country_code,
                'phone_no' => $phoneNo,
                'password' => $password,
                'store_id' => $eachStore->store_id,
            ];
            User::addUser($userParams, true);
            $userRes = User::where('phone_no', $phoneNo)->first();
            $eachStore->update(['user_id' => $userRes->id]);
        }
    }

    public static function getList(array $params = [])
    {

        $storeName = $params['store_name'] ?? null;
        $username = $params['username'] ?? null;

        $seeAll = $params['see_all'] ?? null;

        $sortableColumns = ['store_name', 'created_at'];

        // Order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // Sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'asc';

        // limit
        $limit = ($seeAll == 1) ? null : ($params['limit'] ?? config('app.pagination_rows'));

        $items = self::query()
            ->with(['user'])
            ->when(isset($storeName), function ($q) use ($storeName) {
                return $q->where('name', $storeName);
            })
            ->when(isset($username), function ($q) use ($username) {
                return $q->whereRelation('user', 'username', $username);
            })
            ->orderBy($order_by, $order_sort)
            ->when((! isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $mapFunc = function ($q) {
            $res = [
                'store_name' => $q->name ?? '',
                'username' => $q->user->username ?? '',
                'store_id' => $q->store_id ?? '',
            ];

            return (object) $res;
        };

        if (($seeAll == 1)) {
            return ['list' => $items->get()->map($mapFunc)->toArray()];
        } else {
            $items->getCollection()->transform($mapFunc);

            return (new ItemsCollection($items))->toArray();
        }
    }

    public static function getBankCardList(array $params = [])
    {
        $storeId = $params['store_id'] ?? null;
        $accNo = $params['account_no'] ?? null;

        $bankCardDetail = self::query()->where('store_id', $storeId)->with(['userBankCards' => function ($query) use ($accNo) {
            $query->where('account_no', $accNo);
            $query->where('status', UserBank::$status['active']);
        }])->first();

        return $bankCardDetail->userBankCards->first();
    }
}
