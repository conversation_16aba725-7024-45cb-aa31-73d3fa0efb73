<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;
use App\Traits;
use Illuminate\Support\Str;

class Machine extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'mt_machines';

    protected $hidden = [];

    protected $fillable = [
        'name',
        'description',
        'store_id',
        'service_id',
        'transfer_in',
        'transfer_out',
        'profit_loss',
        'status',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected $appends = [];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
        'coming-soon' => 2,
    ];

    public function store()
    {
        return $this->belongsTo(Store::class, 'store_id', 'store_id');
    }

    public function service()
    {
        return $this->belongsTo(Services::class, 'service_id', 'id');
    }

    protected static function booted()
    {
        static::creating(function ($mtMachine) {
            $mtMachine->uuid = (string) Str::uuid();
        });
    }

    public static function addMachine(array $params = [])
    {
        $insertData = [
            'name' => $params["name"] ?? null,
            'description' => $params["description"] ?? null,
            'store_id' => $params["store_id"] ?? null,
            'service_id' => $params["service_id"] ?? null,
            'status' => $params["status"] ?? null,
        ];


        DB::transaction(function () use ($insertData) {
            $service = self::create($insertData);
            if (!$service) {
                throw new \Exception(Lang::get('lang.failed'));
            }
        });

        abort(200, Lang::get("lang.successful"));
    }

    public static function getMachineList(array $params = [])
    {
        $dateTime = date('Y-m-d H:i:s');
        $date = date('Y-m-d', strtotime($dateTime));
        $sortableColumns = ['created_at'];
        $status = $params['status'] ?? null;
        $createdFrom = $params['created_from'] ?? null;
        $createdTo = $params['created_to'] ?? null;
        $updatedFrom = $params['updated_from'] ?? null;
        $updatedTo = $params['updated_to'] ?? null;

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : 'null';
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        // see all
        $seeAll = $params['see_all'] ?? null;

        $report = self::query()->with(['store', 'service'])
            ->whereNull('deleted_at')
            ->when(isset($status), function ($q) use ($status) {
                return $q->where('status', $status);
            })
            ->when($createdFrom, function ($q) use ($createdFrom) {
                return $q->whereDATE('created_at', '>=', $createdFrom);
            })
            ->when($createdTo, function ($q) use ($createdTo) {
                return $q->whereDATE('created_at', '<=', $createdTo);
            })
            ->when($updatedFrom, function ($q) use ($updatedFrom) {
                return $q->whereDATE('updated_at', '>=', $updatedFrom);
            })
            ->when($updatedTo, function ($q) use ($updatedTo) {
                return $q->whereDATE('updated_at', '<=', $updatedTo);
            })
            ->orderBy($order_by, $order_sort)
            ->orderBy('id', $order_sort)
            ->when((!isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $mapFunc = function ($q) {
            $status = array_search($q->status, self::$status) ?? null;

            switch (MODULE) {
                case 'admin':
                    $res = [
                        'id' => $q->id,
                        'name' => $q->name,
                        'uuid' => $q->uuid,
                        'description' => $q->description,
                        'store_id' => $q->store->store_id ?? null,
                        'store_name' => $q->store->name,
                        'service_id' => $q->service->id ?? null,
                        'service_name' => $q->service->name ?? null,
                        'transfer_in' => $q->transfer_in,
                        'transfer_out' => $q->transfer_out,
                        'profit_loss' => $q->profit_loss,
                        'status' => $status,
                        'status_display' => $status,
                        'updated_at' => Traits\DateTrait::dateFormat($q->updated_at)
                    ];
                    break;

                case 'user':
                    $res = [];
                    break;
            }

            return $res;
        };

        if (($seeAll == 1)) {
            return ['list' => $report->get()->map($mapFunc)->toArray()];
        } else {
            $report->getCollection()->transform($mapFunc);
            return (new ItemsCollection($report))->toArray();
        }
    }

    public static function getMachineDetail(array $params = [])
    {
        $data = self::with(['store', 'service'])->where('id', $params['id'])->get()->map(function ($q) {
            $status = array_search($q->status, self::$status);
            $res = [
                'id' => $q->id,
                'name' => $q->name,
                'description' =>  $q->description,
                'store_id' => $q->store->store_id,
                'store_name' => $q->store->name,
                'service_id' => $q->service->id,
                'service_name' => $q->service->name,
                'transfer_in' => $q->transfer_in,
                'transfer_out' => $q->transfer_out,
                'profit_loss' => $q->profit_loss,
                'status' => $status,
                'status_display' => $status,
                'updated_at' => Traits\DateTrait::dateFormat($q->updated_at)
            ];
            return $res;
        })->first();

        if (empty($data)) {
            throw new \Exception(Lang::get('lang.failed'));
        }

        return $data;
    }

    public static function editMachine(array $params = [])
    {
        $machine = self::find($params['id']);

        $updateData = [
            'name' => $params["name"] ?? null,
            'description' => $params["description"] ?? null,
            'store_id' => $params["store_id"] ?? null,
            'service_id' => $params["service_id"] ?? null,
            'status' => $params["status"] ?? null,
        ];

        DB::transaction(function () use ($updateData, $machine) {
            $machine->update($updateData);
        });

        if ($machine->wasChanged()) {
            abort(200, Lang::get('lang.update-successfully'));
        }

        abort(200, Lang::get('lang.no-changes-detected'));
    }

    public static function deleteMachine(array $params = [])
    {
        $machine = self::find($params['id']);

        DB::transaction(function () use ($machine) {
            $machine->delete();
        });

        abort(200, Lang::get('lang.update-successfully'));
    }
}
