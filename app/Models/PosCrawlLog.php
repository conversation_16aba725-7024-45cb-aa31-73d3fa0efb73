<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PosCrawlLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'start_at',
        'end_at',
        'store_id',
        'count',
        'status'
    ];

    protected $casts = [
        'start_at' => 'datetime',
        'end_at' => 'datetime',
        'status' => 'boolean'
    ];
}
