<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmailLog extends Model
{
    use HasFactory;

    const UPDATED_AT = null;

    protected $fillable = [
        'email',
        'email_type',
        'expired_at',
        'created_at',
        'updated_at',
    ];

    public static $emailType = [
        'register-email' => 1,
        'reset-password' => 2,
        'reset-transaction-password' => 3,
        'update-email' => 4,
        'inquiry-email' => 5
    ];

    public static function scopeNonExpiredEmailRequest($query, $params = [])
    {
        return $query->select('expired_at')
            ->where([
                'email' => $params['email'],
                'email_type' => $params['email_type'],
            ])
            ->where('expired_at','>',now());
    }
}
