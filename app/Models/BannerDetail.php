<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Http\Resources\ItemsCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\SoftDeletes;

class BannerDetail extends Model
{
    use SoftDeletes;

    const UPDATED_AT = null;

    const CREATED_AT = null;

    protected $table = 'banner_detail';

    protected $hidden = [
    ];

    protected $casts = [
        "web_image_data" => "array",
        "mobile_image_data" => "array",
    ];

    protected $fillable = [
        'banner_id',
        'title',
        'web_image_data',
        'mobile_image_data',
        'language_type',
        'deleted_at',
        'terms',
        'description',
    ];
}
