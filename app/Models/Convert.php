<?php

namespace App\Models;

use App\Traits\DecimalTrait;
use App\Traits\GenerateNumberTrait;
use App\Http\Resources\ItemsCollection;
use App\Traits\DateTrait;
use App\Traits;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;

class Convert extends Model
{
    use GenerateNumberTrait;
    use DecimalTrait;
    use DateTrait;

    protected $table = 'convert';

    protected $hidden = [
    ];

    protected $fillable = [
        'user_id',
        'from_credit_id',
        'to_credit_id',
        'rate',
        'from_amount',
        'to_amount',
        'status',
        'belong_id',
        'remark',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $status = [
        'pending' => 0,
        'successful' => 1,
        'refund' => 2
    ];

    // hardcode
    public static $fromCurrency = [
        'MYR',
    ];

    // hardcode
    public static $inputConversion = [
        'USDT' => 'USD',
        'USD' => 'USD',
    ];

    // hardcode
    public static $outputConversion = [
        'USDT' => 'USDT',
        'USD' => 'USDT',
    ];

    public function user(){
        return $this->belongsTo(User::class,"user_id","id");
    }

    public function from_credit(){
        return $this->belongsTo(Credit::class,"from_credit_id","id");
    }

    public function to_credit(){
        return $this->belongsTo(Credit::class,"to_credit_id","id");
    }

    public static function add($data = []){
        return DB::transaction(function () use ($data) {
            $internalID = User::select('id')->where('username','convert')->where('user_type',User::$userType['internal-account'])->first()->id ?? Null;
            if(empty($internalID)){
                abort(400,'Invalid Internal Account.');
            }

            $userId = $data["user_id"] ?? NULL;
            $dateTime = $data['datetime'] ?? date('Y-m-d H:i:s');
            $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;

            $isValidFromCurrency = $data['isValidFromCurrency'];
            $currencySetting = CurrencySetting::
                where('from_currency_id', $isValidFromCurrency?$data['from_currency_id']:$data['to_currency_id'])
                ->where('to_currency_id', $isValidFromCurrency?$data['to_currency_id']:$data['from_currency_id'])
                ->orderBy('created_at', 'DESC')
                ->orderBy('id', 'DESC')
                ->get()->first();
            $rate = $isValidFromCurrency ? ($currencySetting->convert_out_rate ?? 0) : ($currencySetting->convert_in_rate ?? 0);
            if(!isset($rate) || $rate <= 0){
                throw new Exception("Someting Went Wrong", 500);
            }

            $remark = null;
            if(isset($data['remark']) && !empty($data['remark'])){
                $remark = $data['remark'];
            }

            if($isValidFromCurrency){
                $toAmount = $data['amount'];
                $fromAmount =  DecimalTrait::setDecimal(($toAmount / $rate), null, true);
            }else{
                $fromAmount = $data['amount'];
                $toAmount =  DecimalTrait::setDecimal(($fromAmount * $rate), null, true);
                
            }

            $fromCreditId = $data['from_credit_id'];
            $toCreditId = $data['to_credit_id'];
            $fromCurrencyId = $data['from_currency_id'];
            $toCurrencyId = $data['to_currency_id'];

            $fromCredit = Credit::find($fromCreditId)->toArray();
            $toCredit = Credit::find($toCreditId)->toArray();

            $fromCurrency = Currency::find($fromCurrencyId)->toArray();
            $toCurrency = Currency::find($toCurrencyId)->toArray();

            CreditTransaction::insertTransaction($userId, $internalID, $userId, $fromCredit['type'], $fromAmount, "convert-out", $belongId, $belongId, $remark, $dateTime, null, null, null, null, null, false); // deduct from tird party will do in queue

            $covertRecord = self::create([
                'user_id' => $userId,
                'from_credit_id' => $fromCreditId,
                'to_credit_id' => $toCreditId,
                'rate' => $rate,
                'from_amount' => $fromAmount,
                'to_amount' => $toAmount,
                'status' => self::$status['pending'],
                'belong_id' => $belongId,
                'remark' => $remark,
                'created_at' => $dateTime
            ]);

            $convertParams = [
                'transaction_type' => 'convert',
                'internal_id' => $internalID,
                'user_id' => $userId,
                'from_credit' => $fromCredit,
                'to_credit' => $toCredit,
                'from_currency' => $fromCurrency,
                'to_currency' => $toCurrency,
                'rate' => $rate,
                'from_amount' => $fromAmount,
                'to_amount' => $toAmount,
                'belong_id' => $belongId,
                'batch_id' => $belongId,
                'datetime' => $dateTime,
                'remark' => $remark,
                'convert_id' => $covertRecord->id,
            ];

            $convertParams = json_encode($convertParams);
            $transactionJob = new \App\Jobs\Transaction($convertParams);
            dispatch($transactionJob);
            /* 
            * Do transfer out will do in jobs
            * CreditTransaction::insertTransaction($internalID, $toId, $toId, $creditName, $amount, "convert-in", $belongId, $belongId, null, $dateTime, null, null, null, $fromId);
            */
        });
    }

    public static function list($params = []){
        $username = $params['username'] ?? null;
        $fromCreditId = $params['from_credit_id'] ?? null;
        $toCreditId = $params['to_credit_id'] ?? null;
        $fromDate = $params['from_date'] ?? null;
        $toDate = $params['to_date'] ?? null;
        $status = $params['status'] ?? Null;

        $seeAll = $params['see_all'] ?? Null;

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');
        
        if (isset($params['export']) && ($params['export'] == 1)) {
            $jobData = $params;
            $jobData["model"] = get_class() ?? null;
            $jobData["function"] = __FUNCTION__;
            $jobData["module"] = MODULE;
            $jobData["creator_type"] = MODULE;
            $jobData["creator_id"] = Auth::user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, "Export Successfully");
        }

        $items = self::query()
            ->with([
                'user', 
                'from_credit',
                'from_credit.currency',
                'to_credit',
                'to_credit.currency',
            ])
            ->when($username, function ($q) use ($username) {
                return $q->whereRelation('user', 'username', '=', $username);
            })
            ->when($fromCreditId, function ($q) use ($fromCreditId) {
                return $q->whereRelation('from_credit', 'id', '=',$fromCreditId);
            })
            ->when($toCreditId, function ($q) use ($toCreditId) {
                return $q->where('to_credit', 'id', '=', $toCreditId);
            })
            ->when((isset($fromDate) && isset($toDate)),function ($q) use($fromDate,$toDate){
                $q->where(DB::raw('DATE(created_at)'),">=",$fromDate);
                return $q->where(DB::raw('DATE(created_at)'),"<=",$toDate);
            })
            ->when(in_array(MODULE,['user','app']),function ($q){
                return $q->where('user_id',auth()->user()->id);
            })
            ->when(isset($status),function ($q) use($status){
                return $q->where('status', Convert::$status[$status]);
            });
        
        $sumQuery = clone $items;
        $items = $items
                ->orderBy($order_by, $order_sort)
                ->when((!isset($seeAll) || $seeAll == 0),function ($q) use ($limit){
                    return $q->paginate($limit);
                });

        if(MODULE == 'admin'){
            $sumQuery->selectRaw('from_credit_id, SUM(from_amount) AS from_amount, to_credit_id, SUM(to_amount) AS to_amount ')
                ->groupBy('from_credit_id', 'to_credit_id')
                ->get()
                ->map(function ($q) use (&$summary) {
                    $fromIso = Convert::$outputConversion[$q->from_credit->currency->iso] ?? $q->from_credit->currency->iso;
                    $info['name'] = $q->from_credit->name;
                    $info['display'] = Lang::has('lang.' . $q->from_credit->name) ? Lang::get('lang.' .$q->from_credit->name) : $q->from_credit->name;
                    $info['iso'] =  $fromIso;
                    $info['total'] = $q->from_amount;
                    $summary['convert_from'][$fromIso] = $info;

                    $toIso = Convert::$outputConversion[$q->to_credit->currency->iso] ?? $q->to_credit->currency->iso;
                    $info['name'] = $q->to_credit->name;
                    $info['display'] = Lang::has('lang.' . $q->to_credit->name) ? Lang::get('lang.' .$q->to_credit->name) : $q->to_credit->name;
                    $info['iso'] =  $toIso;
                    $info['total'] = $q->to_amount;
                    $summary['convert_to'][$toIso] = $info;    
                });
        }

        $mapFunc = function($q) {
            $fromCreditIso = Convert::$outputConversion[$q->from_credit->currency->iso] ?? $q->from_credit->currency->iso;
            $toCreditIso = Convert::$outputConversion[$q->to_credit->currency->iso] ?? $q->to_credit->currency->iso;
            $res = [
                'created_at' => DateTrait::dateFormat($q->created_at),
                'username' => $q->user->username ?? null,
                'from_credit' => Lang::has('lang.' . $q->from_credit->name) ? Lang::get('lang.' .$q->from_credit->name) : $q->from_credit->name,
                'to_credit' => Lang::has('lang.' . $q->to_credit->name) ? Lang::get('lang.' .$q->to_credit->name) : $q->to_credit->name,
                'from_credit_iso' => $fromCreditIso,
                'to_credit_iso' => $toCreditIso,
                'from_amount' => DecimalTrait::setDecimal($q->from_amount),
                'rate' => DecimalTrait::setDecimal($q->rate,3),
                'to_amount' => DecimalTrait::setDecimal($q->to_amount),
                'status' => array_search($q->status, Convert::$status),
                'status_display' => Lang::get('lang.'.array_search($q->status, Convert::$status)),
                'country' => $q->user->country->name,
                'country_display' => Lang::has('lang.'.$q->user->country->name) ? Lang::get('lang.'.$q->user->country->name) : $q->user->country->name,
                'remark' => $q->remark,
                'updated_at' => DateTrait::dateFormat($q->updated_at),
            ];
            return (object) $res;
        };

        if (($seeAll == 1)) {
            $data = $items->get()->map($mapFunc);
        } else {
            $items->getCollection()->transform($mapFunc);
            $data =  (new ItemsCollection($items));
        }

        if(MODULE == 'admin'){
            if(($seeAll == 1)){
                $data = ['list' => $data->toArray()] + ["summary" => $summary];
            }else{
                $data->additional['summary'] = $summary;
            }
        } 

        return $data;
    }

    public static function getMYRConvertToCurrency()
    {
        $fromID = Currency::where(['iso' => 'MYR', 'disabled' => 0])->first()->id ?? 0;

        $variable = CreditSetting::with(['convertToCredit'])
            // ->where('member', "1")
            ->where('name', 'convert-to')
            ->whereHas('credit', function($q) use ($fromID){
                $q->where('currency_id', $fromID);
            })
            ->get();
        foreach($variable as $set){
            $val['id'] = $set->convertToCredit->currency_id;
            $val['status'] = $set->member;
            $ids[] = $val;
        }
        return $ids ?? [];
    }

    public static function getConvertToCurrency($fromCreditID = null)
    {
        // for user only
        $variable = CreditSetting::with(['convertToCredit'])
            ->when(isset($fromCreditID),function ($q) use($fromCreditID){
                return $q->where('credit_id',$fromCreditID);
            })
            // ->where('credit_id', $fromCreditID)
            ->where('member', "1")
            ->where('name', 'convert-to')
            ->get();
        foreach($variable as $set){
            $ids[$set->convertToCredit->currency_id] = $set->convertToCredit->currency_id;
        }
        return $ids ?? [];
    }
}