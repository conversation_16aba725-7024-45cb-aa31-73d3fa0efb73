<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class GameProvider extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'url',
        'icon',
        'priority',
        'status',
    ];

    public function game_category_providers()
    {
        return $this->hasMany(GameCategoryProvider::class, 'game_provider_id', 'id');
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->priority = self::max('priority') + 1;
        });
    }
}
