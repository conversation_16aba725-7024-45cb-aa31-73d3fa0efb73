<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;
use App\Traits;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Arr;

class Services extends Model
{
    protected $table = 'services';

    protected $hidden = [];

    protected $fillable = [
        'name',
        'url',
        'icon',
        'product_id',
        'wallet_type',
        'filter_type',
        'game_provider_id',
        'game_category_id',
        'game_category_provider_id',
        'game_setting_id',
        'is_homepage',
        'is_deeplink',
        'priority',
        'status',
        'created_at',
        'updater_id',
        'updated_at',
        'deleted_at',
    ];

    protected $appends = ['is_game'];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
        'coming-soon' => 2,
    ];

    public static $filterType = [
        'all' => 0,
        'hot-games' => 1,
        'new-games' => 2,
    ];

    public function admin()
    {
        return $this->belongsTo(Admin::class, 'updater_id', 'id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public function game_setting()
    {
        return $this->belongsTo(GameSetting::class, 'game_setting_id', 'id');
    }

    public function game_category_providers()
    {
        return $this->hasMany(GameCategoryProvider::class, 'game_category_provider_id', 'id');
    }

    public function game_category()
    {
        return $this->belongsTo(GameCategory::class, 'game_category_id', 'id');
    }

    public function game_provider()
    {
        return $this->belongsTo(GameProvider::class, 'game_provider_id', 'id');
    }

    public function isGame(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->game_setting_id > 0 ? true : false
        );
    }

    public static function addService(array $params = [])
    {
        $insertData = [
            'name' => $params["name"] ?? null,
            'url' => $params["url"] ?? null,
            'icon' => $params["icon"] ?? null,
            'priority' => self::max('priority') + 1,
            'status' => $params["status"] ?? null,
            'product_id' => $params["product_id"] ?? null,
            'wallet_type' => $params["wallet_type"] ?? null,
            'updater_id' => auth()->user()->id,
        ];

        //Language
        $insertLanguage = [
            'slug' => $params["name"] ?? null,
            'type' => LangCustom::$type['service'],
        ];
        foreach (config('language') as $lang => $value) {
            $langKey = array_search($lang, array_column($params['language'], 'type'));
            if (($langKey === false)) {
                $langKey = array_search('en', array_column($params['language'], 'type'));
            }
            $insertLanguage[$lang] = $params['language'][$langKey]['name'];
        }

        DB::transaction(function () use ($insertData, $insertLanguage) {
            $service = self::create($insertData);
            if (!$service) {
                throw new \Exception(Lang::get('lang.service-create-fail'));
            }

            $cusLang = LangCustom::create($insertLanguage);
            if (!$cusLang) {
                throw new \Exception(Lang::get('lang.language-create-fail'));
            }

            Traits\CacheTrait::clearLangCache('lang-custom-');
        });

        return true;
    }

    public static function getServiceList(array $params = [])
    {
        $dateTime = date('Y-m-d H:i:s');
        $date = date('Y-m-d', strtotime($dateTime));
        $sortableColumns = ['created_at'];
        $status = $params['status'] ?? null;
        $createdFrom = $params['created_from'] ?? null;
        $createdTo = $params['created_to'] ?? null;
        $updatedFrom = $params['updated_from'] ?? null;
        $updatedTo = $params['updated_to'] ?? null;

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : 'null';
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        // see all
        $seeAll = $params['see_all'] ?? null;

        $report = self::query()->with(['admin', 'product'])
            ->when(isset($status), function ($q) use ($status) {
                return $q->where('status', $status);
            })
            ->when($createdFrom, function ($q) use ($createdFrom) {
                return $q->whereDATE('created_at', '>=', $createdFrom);
            })
            ->when($createdTo, function ($q) use ($createdTo) {
                return $q->whereDATE('created_at', '<=', $createdTo);
            })
            ->when($updatedFrom, function ($q) use ($updatedFrom) {
                return $q->whereDATE('updated_at', '>=', $updatedFrom);
            })
            ->when($updatedTo, function ($q) use ($updatedTo) {
                return $q->whereDATE('updated_at', '<=', $updatedTo);
            })
            ->orderBy($order_by, $order_sort)
            ->orderBy('id', $order_sort)
            ->when((!isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $mapFunc = function ($q) {
            $status = array_search($q->status, self::$status) ?? null;

            switch (MODULE) {
                case 'admin':
                    $res = [
                        'id' => $q->id,
                        'name' => Lang::has('langCustom.service-' . $q->name) ? Lang::get('langCustom.service-' . $q->name) : $q->name,
                        'url' => $q->url,
                        'icon' => isset($q->icon) ? env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $q->icon : null,
                        'priority' => $q->priority,
                        'product_id' => $q->product->id ?? null,
                        'product_name' => $q->product->name ?? null,
                        'product_display' => Lang::has('lang.' . ($q->product->name ?? "")) ? Lang::get('lang.' . $q->product->name) : ($q->product->name ?? ""),
                        'status' => $status,
                        'status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                        'wallet_type' => $q->wallet_type ?? null,
                        'wallet_type_display' => isset($q->wallet_type) ? (Lang::has('lang.' . $q->wallet_type) ? Lang::get('lang.' . $q->wallet_type) : $q->wallet_type) : null,
                        'updated_by' => $q->admin?->username ?? '-',
                        'updated_at' => Traits\DateTrait::dateFormat($q->updated_at)
                    ];
                    break;

                case 'user':
                    $res = [];
                    break;
            }

            return $res;
        };

        if (($seeAll == 1)) {
            return ['list' => $report->get()->map($mapFunc)->toArray()];
        } else {
            $report->getCollection()->transform($mapFunc);
            return (new ItemsCollection($report))->toArray();
        }
    }

    public static function getServiceDetail(array $params = [])
    {
        $data = self::with(['product'])->where('id', $params['id'])->get()->map(function ($q) {
            $langAry = [];
            foreach (config('language') as $lang => $row) {
                $langDisplay = Lang::has('langCustom.service-' . $q->name) ? Lang::get('langCustom.service-' . $q->name, [], $lang) : null;
                if (empty($langDisplay)) {
                    continue;
                }
                $temp['type'] = $lang;
                $temp['name'] = $langDisplay;
                $langAry[] = $temp;
            }

            $status = array_search($q->status, self::$status);
            $res = [
                'id' => $q->id,
                'language' => $langAry ?? [],
                'url' => $q->url,
                'priority' => $q->priority,
                'product_id' => $q->product->id ?? null,
                'product_name' => $q->product->name ?? null,
                'product_display' => Lang::has('lang.' . ($q->product->name ?? "")) ? Lang::get('lang.' . $q->product->name) : ($q->product->name ?? ""),
                'icon' => $q->icon ?? null,
                'icon_display' => isset($q->icon) ? env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $q->icon : null,
                'status' => $q->status,
                'status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                'wallet_type' => $q->wallet_type ?? null,
                'wallet_type_display' => isset($q->wallet_type) ? (Lang::has('lang.' . $q->wallet_type) ? Lang::get('lang.' . $q->wallet_type) : $q->wallet_type) : null,
            ];
            return $res;
        })->first();

        if (empty($data)) {
            throw new \Exception(Lang::get('lang.service-not-exist'));
        }

        return $data;
    }

    public static function editService(array $params = [])
    {
        $service = self::find($params['id']);
        if (empty($service)) {
            throw new \Exception(Lang::get('lang.service-not-exist'));
        }

        $lang = null;
        $langCusID = LangCustom::where('slug', $service->name)->where('type', LangCustom::$type['service'])->first();
        $langCusID = isset($langCusID) ? $langCusID['id'] : null;
        if (!empty($langCusID)) {
            // throw new \Exception(Lang::get('lang.lang-not-exist'));
            $lang = LangCustom::find($langCusID);
        }

        $updateData = [
            // 'name' => (array_key_exists("name", $params) && !in_array($service->name, ['entertainment'])) ? $params["name"] : $service->name,
            'name' => $service->name,
            'url' => array_key_exists("url", $params) ? $params["url"] : $service->url,
            'icon' => array_key_exists("icon", $params) ? $params["icon"] : $service->icon,
            'product_id' => $params["product_id"] ?? null,
            'wallet_type' => $params["wallet_type"] ?? null,
            'priority' => array_key_exists("priority", $params) ? $params["priority"] : $service->priority,
            'status' => array_key_exists("status", $params) ? $params["status"] : $service->status,
        ];

        //Language
        $updateLang = [];
        if (isset($lang)) {
            $updateLang = [
                'slug' => (array_key_exists("name", $params) && !in_array($service->name, ['entertainment'])) ? $params["name"] : $service->name,
            ];
            $languageAry = [];
            if (isset($params['language'])) {
                $languageAry = $params['language'];
            }
            foreach ($languageAry as $languageData) {
                $updateLang[$languageData['type']] = $languageData['name'];
            }
        }

        DB::transaction(function () use ($updateData, $updateLang, $service, $lang) {
            if ($service->priority > $updateData['priority']) {
                self::where('priority', '>=', $updateData['priority'])->update(["priority" => DB::raw("priority + 1")]);
            } elseif ($service->priority < $updateData['priority']) {
                self::where('priority', '>', $service->priority)->where('priority', '<=', $updateData['priority'])->update(["priority" => DB::raw("priority - 1")]);
            }

            $service->update($updateData);
            if (isset($lang)) {
                $lang->update($updateLang);
            }
        });

        if (isset($lang)) {
            if ($lang->wasChanged() || $service->wasChanged()) {
                Traits\CacheTrait::clearLangCache('lang-custom-');
                return Lang::get('lang.update-successfully');
            }
        }

        return Lang::get('lang.no-changes-detected');
    }

    public static function arrangeService(array $params = [])
    {
        DB::transaction(function () use ($params) {
            foreach ($params['arrange_data'] as $arrangeData) {
                self::find($arrangeData['id'])->update(Arr::only($arrangeData, ['priority']));
            }
        });

        return Lang::get('lang.update-successfully');
    }

    public static function getHotServices()
    {
        return self::with('product')
            ->where('status', 1)
            ->where('is_homepage', 1)
            ->orderBy('priority', 'asc')
            ->get()
            ->map(function ($q) {
                $status = array_search($q->status, Services::$status) ?? null;

                return [
                    "service_id" => $q->id,
                    "name" => $q->name,
                    "display" => Lang::has('langCustom.service-' . $q->name) ? Lang::get('langCustom.service-' . $q->name) : $q->name,
                    "url" => $q->url,
                    "icon" => isset($q->icon) ? ((filter_var($q->icon, FILTER_VALIDATE_URL) || substr($q->icon, 0, 5) == 'https') ? str_replace(' ', '%20', $q->icon) : env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $q->icon) : null,
                    'status' => $status,
                    'isTK8' => 0,
                    'showCashInOutWarning' => 0,
                    'product_id' => (int)$q->product_id,
                    'product_name' => $q->product->name ?? null,
                    'filter_type' => $q->filter_type,
                    'is_game' => 1,
                    'is_deeplink' => $q->is_deeplink
                ];
            });
    }
}
