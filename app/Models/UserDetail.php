<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class UserDetail extends Model
{
    use SoftDeletes;
    const CREATED_AT = null;
    const UPDATED_AT = null;

    protected $table = 'user_detail';

    protected $hidden = [
    ];

    protected $fillable = [
        'name',
        'value',
        'type',
        'reference',
        'description',
        'user_id',
    ];

    public static function createTrxnPassword(array $params = []) {
        if (!isset($params['userID']) || !isset($params['trxnPassword'])) {
            return false;
        }

        $insertData = [
            'value' => Hash::make($params['trxnPassword']),
        ];

        self::updateOrCreate([
            'user_id' => $params['userID'],
            'name' => 'transaction_password',
        ],$insertData);

        return true;
    }

    public static function changeTransactionPassword($data = []) {
        request()->merge(['act-log' => 'change-transaction-password']);
        $insertData = [
            "value" => Hash::make($data['password']),
        ];

        self::updateOrCreate(["user_id" => $data['user_id'],"name"=>"transaction_password"],$insertData);
        self::where(['user_id' => $data['user_id'], 'name' => 'reset_transaction_password'])->whereNull('deleted_at')->delete();

        return true;
    }

    public static function uploadProfilePicture($data = [])
    {
        $previousRecord = UserDetail::where(['name' => 'profile_picture', 'user_id' => $data['user_id']])->first();

        if ((!isset($previousRecord) || empty($previousRecord)) && isset($data['image'])) {
            request()->merge(['act-log' => 'change-profile-pic']);
        } else {
            request()->merge(['act-log' => 'delete-profile-pic']);
        }

        if (isset($previousRecord) && isset($data['image']) && $previousRecord->value == $data['image']) return true;
        if (isset($previousRecord)) $previousRecord->delete();

        if (isset($data['image'])) {
            UserDetail::create([
                "name" => "profile_picture",
                "value" => $data['image'],
                "user_id" => $data['user_id'],
                "created_at" => date('Y-m-d H:i:s'),
            ]);
        }

        return true;
    }
}
