<?php

namespace App\Models;

use App\Traits\Sms;
use App\Traits\DateTrait;
use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class SMSLog extends Model
{
    use SoftDeletes;
    use DateTrait;

    protected $table = 'sms_log';

    protected $hidden = [
    ];

    protected $fillable = [
        'user_id',
        'country_code',
        'username',
        'type',
        'otp_code',
        'phone_no',
        'provider',
        'provider_request',
        'expired_at',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $type = [
        'register-otp' => 1,
        'login-otp' => 2,
        'change-phone-otp' => 3,
        'email-verify-otp' => 4,
        'register-success' => 5,
    ];

    public static $provider = [
        'yunpian' => 'YP',
        'gosms' => 'GS',
        'twilio' => 'TWI',
        'skyline' => 'SKY',
        'nexmo' => 'NEX',
        'sureceive' => 'SR'
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_code', 'country_code');
    }

    public static function list($params = [])
    {
        // Search
        $from_date = $params['from_date'] ?? null;
        $to_date = $params['to_date'] ?? null;
        $phoneNo = $params['phone_no'] ?? null;
        $requestType = $params['request_type'] ?? null;
        $name = $params['name'] ?? null;
        $memberID = $params['member_id'] ?? null;
        $email = $params['email'] ?? null;
        $otp = $params['otp'] ?? null;
        $provider = $params['provider'] ?? null;
        $country_id = $params['country'] ?? null;

        $sortableColumns = ['created_at'];

        // Order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // Sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // Limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        // Query
        $items = self::query()
            ->with(['user', 'country'])
            ->when($from_date, function($q, $from_date) {
                return $q->where('created_at', '>=', date("Y-m-d 00:00:00", strtotime($from_date)));
            })
            ->when($to_date, function($q, $to_date) {
                return $q->where('created_at', '<=', date("Y-m-d 23:59:59", strtotime($to_date)));
            })
            ->when($requestType, function($q, $requestType) {
                return $q->where('type', self::$type[$requestType]);
            })
            ->when($otp, function($q, $otp) {
                return $q->where('code', $otp);
            })
            ->when($phoneNo, function($q, $phoneNo) {
                $phoneNo = preg_replace('/[^0-9]/', '', $phoneNo);
                return $q->where("phone_no", 'like', "%$phoneNo%");
            })
            ->when($provider, function($q, $provider) {
                return $q->where('phone_no', $provider);
            })
            ->when($name, function($q, $name) {
                return $q->whereRelation('user','name','LIKE', '%' . $name . '%');
            })
            ->when($memberID, function($q, $memberID) {
                return $q->whereRelation('user','member_id','LIKE', '%' . $memberID . '%');
            })
            ->when($email, function($q, $email) {
                return $q->whereRelation('user','email','LIKE', '%' . $email . '%');
            })
            ->when(isset($country_id), function ($q) use ($country_id) {
                return $q->whereRelation('country', 'id', $country_id)->orWhereRelation('user','country_id',$country_id);
            })
            ->withTrashed()
            ->orderBy($order_by, $order_sort)
            ->paginate($limit);

        // Transform
        $items->getCollection()->transform(function($q) {
            if($q->protected == 1) {
                $protectedDisplay = Lang::get('lang.protected'); 
            } else {
                $protectedDisplay = Lang::get('lang.unprotected');
            }

            $providerReq = $q->provider_request ? json_decode($q->provider_request) : [];
            $status = "pending";
            $content = null;
            $sentDateTime = null;
            foreach ($providerReq as $req) {
                $content = $req->request->text ?? null;
                $sentDateTime = $req->request_at ?? null;
                $status = $req->response->status == true ? 'successful' : 'failed';
            }

            if (isset($q->country) && !empty($q->country)) {
                $countryName = $q->country->name ?? '-';
            } else if (isset($q->user) && !empty($q->user)) {
                $countryName = $q->user->country->name ?? '-';
            } else {
                $countryName = '-';
            }

            $result = [
                'created_at' => DateTrait::dateFormat($q->created_at,false),
                'name' => $q->user->name ?? null,
                'phone_no' => $q->phone_no,
                'provider' => self::$provider[$q->provider] ?? null,
                'type' => Lang::get("lang.".array_search($q->type, Sms::$smsType)),
                'code' => $q->otp_code ?? "-",
                'status' => Lang::get("lang.".$status),
                'country' => $countryName ?? null,
                'country_display' => $countryName ? (Lang::has('lang.'.$countryName) ? Lang::get('lang.'.$countryName) : $countryName) : '-',
                'description' => $content,
                'sent_on' => $sentDateTime ? DateTrait::dateFormat($sentDateTime) : DateTrait::dateFormat($q->created_at),
                'member_id' => $q->user->member_id ?? null,
            ];
            return (object) $result;
        });

        return (new ItemsCollection($items));   
    }
}
