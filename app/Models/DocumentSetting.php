<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use App\Traits;
use App\Traits\DecimalTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class DocumentSetting extends Model
{
    use DecimalTrait, SoftDeletes;

    const UPDATED_AT = null;

    const CREATED_AT = null;

    protected $table = 'document_setting';

    protected $hidden = [
    ];

    protected $fillable = [
        'document_id',
        'name',
        'value',
        'deleted_at',
    ];
}

