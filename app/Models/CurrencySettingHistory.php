<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use App\Traits;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Traits\DateTrait;
use App\Traits\DecimalTrait;
use Illuminate\Support\Arr;

class CurrencySettingHistory extends Model
{
    const UPDATED_AT = null;

    protected $table = 'currency_setting_history';

    protected $hidden = [
    ];

    protected $fillable = [
        'setting_id',
        'sell_rate',
        'deposit_rate',
        'withdrawal_rate',
        'withdrawal_fee',
        'withdrawal_min_amount',
        'convert_out_rate',
        'convert_in_rate',
        'convert_in_daily_limit', 
        'convert_out_daily_limit', 
        'conversion_min_amount',
        'conversion_max_amount',
        'fee',
        'min_amount',
        'instant_rate',
        'normal_rate',
        'daily_rate',
        'daily_limit',
        'creator_id',
        'created_at',
        'deleted_at',
    ];

    public static function getSettingHistory(array $params = []){
        $typeId = $params['type_id'] ?? Null;
        $creatorBy = $params['creator_by'] ?? Null;
        $fromDate = $params['from_date'] ?? Null;
        $toDate = $params['to_date'] ?? Null;

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        $items = self::query()
            ->with(['creatorBy', 'currencySetting'])
            ->select([
                    "setting_id",
                    "deposit_percentage",
                    "withdrawal_percentage",
                    "creator_id",
                    "created_at"
            ])
            ->whereRelation('currencySetting.toCurrency', 'iso', $params['iso'])
            ->when(isset($creatorBy),function ($q) use($creatorBy){
                return $q->whereRelation('creatorBy', 'username', $creatorBy);
            })
            ->when((isset($fromDate) && isset($toDate)),function ($q) use($fromDate,$toDate){
                $q->where(DB::raw('DATE(created_at)'),">=",$fromDate);
                return $q->where(DB::raw('DATE(created_at)'),"<=",$toDate);
            })
            ->orderBy($order_by, $order_sort)
            ->paginate($limit);
        $items->getCollection()->transform(function($q) {
            $fromCurrency = $q->currencySetting->fromCurrency->iso;
            $toCurrency = $q->currencySetting->toCurrency->iso;

            $res = [
                'deposit_percentage' => DecimalTrait::setDecimal($q->deposit_percentage),
                'withdrawal_percentage' => DecimalTrait::setDecimal($q->withdrawal_percentage),
                'creator_by' => $q->creatorBy->username,
                'created_at' => DateTrait::dateFormat($q->created_at),
            ];

            return (object) $res;
        });
        return (new ItemsCollection($items))->toArray();
    }

    public static function insert(array $params = []){
        $insertData = Arr::only($params, ['convert_in_daily_limit', 'convert_out_daily_limit', 'setting_id', 'sell_rate', 'withdrawal_rate', 'withdrawal_fee', 'withdrawal_min_amount', 'convert_out_rate', 'convert_in_rate', 'conversion_min_amount', 'conversion_max_amount','deposit_rate', 'withdrawal_rate', 'fee', 'min_amount', 'instant_rate', 'normal_rate', 'daily_rate', 'daily_limit']);
        DB::transaction(function () use ($insertData) {
            $history = self::create($insertData);

            if(!$history) {
                throw new \Exception(Lang::get('lang.currency-setting-history-insert-fail'));
            }
        });

        return true;
    }

    public function currencySetting(){
        return $this->belongsTo(CurrencySetting::class,"setting_id","id");
    }

    public function creatorBy(){
        return $this->belongsTo(Admin::class,"creator_id","id");
    }
}

