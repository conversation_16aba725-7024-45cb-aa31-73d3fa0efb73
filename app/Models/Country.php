<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;

class Country extends Model
{
    protected $table = 'country';

    protected $hidden = [
    ];

    protected $fillable = [
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
    ];

    public static function getList(array $params = [],$isInventory = false, $activeStatus = true){
        $sortableColumns = ['name', 'priority'];

        // Order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'priority';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'priority';

        // Sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'asc';

        // limit
        $limit = $params['limit'] ?? null;

        $items = self::query()
            ->select(['id','name','country_code','iso_code2', 'currency_code'])
            ->when($isInventory, function ($q) {
                $q->with([
                    'getCurrency' => function ($q) {
                        $q->where('disabled',0);
                    },
                    'getState' => function ($q) {
                        $q->selectRaw('id, name, country_id, zone_id');
                        $q->where('status',State::$status['active']);
                        $q->whereNull('deleted_at');
                    },
                    'getState.zone' => function ($q) {
                        $q->selectRaw('id, name');
                        $q->whereNull('deleted_at');
                        $q->where('status',Zone::$status['active']);
                    },
                    'getZone' => function ($q) {
                        $q->selectRaw('id, name, country_id');
                        $q->whereNull('deleted_at');
                        $q->where('status',Zone::$status['active']);
                    },
                ]);
            })
            ->when($activeStatus == true, function($q){
                $q->where('status',self::$status['active']);    
            })
            ->orderBy($order_by, $order_sort)
            ->limit($limit)
            ->get()
            ->map(function ($q) use ($isInventory) {
                if($isInventory){
                    $stateAry = $q->getState ?? [];
                    foreach($stateAry as &$stateRow){
                        unset($stateRow['country_id'],$stateRow['zone_id']);
                        $stateRow['state_display'] = Lang::has('lang.'.$stateRow['name']) ? Lang::get('lang.'.$stateRow['name']) : $stateRow['name'];
                        $stateRow['zone']['zone_display'] = Lang::has('lang.'.$stateRow['zone']['name']) ? Lang::get('lang.'.$stateRow['zone']['name']) : $stateRow['zone']['name'];
                    }

                    $zoneAry = $q->getZone ?? [];
                    foreach($zoneAry as &$zoneRow){
                        unset($zoneRow['country_id']);
                        $zoneRow['zone_display'] = Lang::has('lang.'.$zoneRow['name']) ? Lang::get('lang.'.$zoneRow['name']) : $zoneRow['name'];
                    }

                    return [
                        'id' => $q->id,
                        'dial_code' => $q->country_code,
                        'name' => $q->name,
                        'country_display' => Lang::has('lang.'.$q->name) ? Lang::get('lang.'.$q->name) : $q->name,
                        'iso_code' => $q->iso_code2,
                        'state' => $stateAry,
                        'zone' => $zoneAry,
                        'currency' => isset($q->getCurrency) ? [
                                        'id' => $q->getCurrency->id,
                                        'iso' => $q->getCurrency->iso,
                                        'name' => $q->getCurrency->name,
                                        'currency_display' => Lang::has('lang.'.$q->getCurrency->name) ? Lang::get('lang.'.$q->getCurrency->name) : $q->getCurrency->name,
                                    ] : [],
                    ];
                }

                return [
                    'id' => $q->id,
                    'dial_code' => $q->country_code,
                    'name' => $q->name,
                    'country_display' => Lang::has('lang.'.$q->name) ? Lang::get('lang.'.$q->name) : $q->name,
                    'iso_code' => $q->iso_code2,
                ];
            });

        return [
            'data' => $items
        ];
    }

    public function getCurrency(){
        return $this->belongsTo(Currency::class, 'currency_code', 'iso');
    }

    public function getCurrencies(){
        return $this->belongsTo(Currencies::class, 'currency_code', 'iso');
    }

    public function getState(){
        return $this->hasMany(State::class, 'country_id', 'id');
    }

    public function getZone(){
        return $this->hasMany(Zone::class, 'country_id', 'id');
    }
}
