<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserCardLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'store_id',
        'user_id',
        'terminal_serial',
        'terminal_address',
        'member_card_id',
        'member_card_no',
        'member_phone',
        'operation_type',
        'date_time',
        'transaction_at',
        'member_balance',
        'operation_qty',
        'latest_member_balance',
        'value_type',
        'remark',
        'status'
    ];

    public static $valueType = [
        'online' => 8,
        'offline' => 3,
    ];

    public static $operationType = [
        'deposit' => 1,
        'withdrawal' => 2
    ];

    /* -------------------------------------------------------------------------- */
    /*                                Relationships                               */
    /* -------------------------------------------------------------------------- */
    public function store()
    {
        return $this->belongsTo(Store::class, 'store_id', 'store_id');
    }

    public function userCard()
    {
        return $this->belongsTo(UserCard::class, 'member_card_id', 'card_id');
    }

    public function users()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /* -------------------------------------------------------------------------- */
    /*                                   Scopes                                   */
    /* -------------------------------------------------------------------------- */
    public function scopeDeposit($query)
    {
        return $query->where('operation_type', self::$operationType['deposit']);
    }

    public function scopeWithdrawal($query)
    {
        return $query->where('operation_type', self::$operationType['withdrawal']);
    }
}
