<?php

namespace App\Models;

use App\Traits\DecimalTrait;
use App\Traits\GenerateNumberTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class UserReferralBonus extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'status',
        'type',
        'amount',
        'updated_by',
    ];

    public static $status = [
        'pending' => 0,
        'approved' => 1,
        'rejected' => 2,
    ];

    public static $type = [
        'rebate' => 0,
        'tier' => 1,
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public static function getUserReferralBonusByUserId($userId, $fromDate = null, $toDate = null)
    {
        $bonuses = self::where('user_id', $userId)
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('created_at'), '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where(DB::raw('created_at'), '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->get()
            ->map(function ($e) {
                return [
                    'id' => $e->id,
                    'user_id' => $e->user_id,
                    'status' => $e->status,
                    'type' => $e->type,
                    'amount' => DecimalTrait::setDecimal($e->amount),
                    'updated_by' => $e->updated_by,
                    'created_at' => date('Y-m-d', strtotime($e->created_at)),
                    'updated_at' => date('Y-m-d', strtotime($e->updated_at)),
                ];
            });

        return $bonuses;
    }

    public static function getById($id)
    {
        $bonus = self::with('user')
            ->find($id);

        return [
            'id' => $bonus->id,
            'user_id' => $bonus->user->id,
            'username' => $bonus->user->username,
            'phone_no' => $bonus->user->phone_no,
            'branch' => $bonus->user->store->name,
            'amount' => DecimalTrait::setDecimal($bonus->amount),
            'status' => $bonus->status,
            'type' => $bonus->type,
            'created_at' => $bonus->created_at,
            'updated_at' => $bonus->updated_at,
            'updated_by' => $bonus->updated_by,
        ];
    }

    public static function approve($id, $adminId)
    {
        $bonus = self::find($id);
        $amount = $bonus->amount;
        $userId = $bonus->user_id;

        DB::transaction(function () use ($bonus, $adminId, $amount, $userId) {
            $bonus->update([
                'status' => self::$status['approved'],
                'updated_by' => $adminId,
            ]);

            $internalID = User::select('id')->where('username', 'creditSales')->where('user_type', User::$userType['internal-account'])->first()->id ?? null;
            if (empty($internalID)) {
                throw new \Exception('Invalid Internal Account.', 400);
            }
            $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;
            $batchId = $belongId;
            $creditName = Credit::first()->name;

            CreditTransaction::insertTransaction($internalID, $userId, $userId, $creditName, $amount, 'referral-payout', $belongId, $batchId, null, now(), null, null, null);
        });

        return $bonus;
    }

    public static function reject($id, $adminId)
    {
        $bonus = self::find($id);

        $bonus->update([
            'status' => self::$status['rejected'],
            'updated_by' => $adminId,
        ]);

        return $bonus;
    }
}
