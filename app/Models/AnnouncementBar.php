<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Http\Resources\ItemsCollection;
use App\Traits\S3Trait;
use Illuminate\Support\Facades\Auth;
use App\Traits\DateTrait;

class AnnouncementBar extends Model
{
    use S3Trait;
    use DateTrait;
    
    protected $table = 'announcement_bar';

    protected $hidden = [
    ];

    protected $fillable = [
        'status',
        'start_date',
        'end_date',
        'creator_id',
        'creator_type',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
    ];

    public function announcementBarDetail(){
        return $this->hasMany(AnnouncementBarDetail::class, 'bar_id', 'id');
    }

    public function announcementBarSetting(){
        return $this->hasMany(AnnouncementBarSetting::class, 'bar_id', 'id');
    }

    public function admin(){
        return $this->belongsTo(Admin::class,'creator_id','id');
    }

    public static function add(array $params = []){
      
        return DB::transaction(function () use ($params){
            $creatorId = Auth::user()->id ?? null;
            $creatorType = isset($creatorId) ? MODULE : 'system';

            $insertAnnouncementBar = [
                'status' => self::$status['active'],
                'creator_id' => $creatorId,
                'creator_type' => $creatorType
            ];

            if(isset($params['start_date']) && isset($params['end_date'])){
                $insertAnnouncementBar['start_date'] = $params['start_date'];
                $insertAnnouncementBar['end_date'] = $params['end_date'];
            }

            $announcementBarId = AnnouncementBar::create($insertAnnouncementBar)->id;

            // Insert announcementBar Data
            foreach ($params['announcement_bar_data'] as $announcementBarData) {
                AnnouncementBarDetail::create([
                    "bar_id" => $announcementBarId,
                    "subject" => $announcementBarData['subject'],
                    "description" => $announcementBarData['description'],
                    "language_type" => config('language')[$announcementBarData['language_type']],
                    "upload_data" => ($announcementBarData['upload'] ?? []),
                ]);
            }

            // Insert Country Setting
            AnnouncementBarSetting::create([
                "bar_id" => $announcementBarId,
                "name" => "valid-country",
                "value" => json_encode($params['valid_country'] ?? []) ,
            ]);

            if(isset($params['announcement_bar_url'])){
                // Insert Url Setting
                AnnouncementBarSetting::create([
                    "bar_id" => $announcementBarId,
                    "name" => "announcement-bar-url",
                    "value" => json_encode($params['announcement_bar_url'] ?? []) ,
                ]);
            }

            return true;
        });
    }

    public static function getList(array $params = []){        
        $fromDate = $params['from_date'] ?? Null;
        $toDate = $params['to_date'] ?? Null;
        $lang = config('app.locale') ?? 'en';

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');
        $items = self::query()
            ->with(['announcementBarDetail' => function($q){
                $q->orderBy('language_type','DESC');
                return $q;
            },'admin'])
            ->select(["id","start_date","end_date","status","creator_id","creator_type","created_at","updated_at"])
            ->when((isset($fromDate) && isset($toDate)),function ($q) use($fromDate,$toDate){
                $q->where(DB::raw('created_at'),">=", date("Y-m-d 00:00:00", strtotime($fromDate)));
                return $q->where(DB::raw('created_at'),"<=",date("Y-m-d 23:59:59", strtotime($toDate)));
            })
            ->orderBy($order_by, $order_sort)
            ->paginate($limit);

        $items->getCollection()->transform(function($q) use($lang){
            foreach ($q->announcementBarDetail as $key => $value) {
                $defaultSetting = $value;
                if(array_search($value['language_type'],config('language')) == $lang){
                    $defaultSetting = $value;
                    break;
                }
            }

            $status = array_search($q->status,self::$status);

            $res = [
                "id" => $q->id,
                "subject" => $defaultSetting->subject,
                "description" => $defaultSetting->description,
                "start_date" =>  date("Y-m-d", strtotime($q->start_date)),
                "end_date" =>  date("Y-m-d", strtotime($q->end_date)),
                'status' => $status,
                "status_display" => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                "creator" => $q->admin->username,
                "created_at" => DateTrait::dateFormat($q->created_at),
                "updated_at" => DateTrait::dateFormat($q->updated_at),
            ];
            
            return (object) $res;
        })->toArray();
        return (new ItemsCollection($items));
    }

    public static function getDetail(array $params = []){
        $items = self::query()
            ->where('id',$params['id'])
            ->with(['announcementBarDetail' => function($q){
                return $q->select('bar_id','subject','description','language_type');
            },"announcementBarSetting" => function ($q){
                return $q->select('bar_id','name','value');
            }])
            ->select(["id","start_date","end_date","status","creator_id","creator_type","created_at","updated_at"])
            ->get()
            ->map(function ($q){
                $status = array_search($q->status,self::$status);
                $announcementBarData = $q->announcementBarDetail->toArray();

                foreach ($announcementBarData as &$announcementBarDetail) {
                    unset($announcementBarDetail['bar_id']);
                    $announcementBarDetail['language_type'] = array_search($announcementBarDetail['language_type'],config('language'));
                    $announcementBarDetail['language_type_display'] = Lang::has('lang.'.$announcementBarDetail['language_type']) ? Lang::get('lang.'.$announcementBarDetail['language_type']) : $announcementBarDetail['language_type'];
                }

                foreach ($q->announcementBarSetting as $settingData) {
                    $announcementBarSetting[$settingData->name] = json_decode($settingData->value,true);
                }

                $res = [
                    "id" => $q->id,
                    "start_date" => date("Y-m-d", strtotime($q->start_date)),
                    "end_date" => date("Y-m-d", strtotime($q->end_date)),
                    "status" => $status,
                    "status_display" => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                    'announcement_bar_data' => $announcementBarData ?? [],
                ];
                return array_merge($res,($announcementBarSetting ?? []));
            })->first();

        $return ['data']  = $items ?? [];
        return $return;
    }

    public static function edit(array $params = []){
        return DB::transaction(function () use ($params){
            $creatorId = Auth::user()->id ?? null;
            $creatorType = isset($creatorId) ? MODULE : 'system';

            $announcementBar = self::find($params['id'])->first();
            
            $updateAnnouncementBar = [
                'status' => self::$status[$params['status']],
            ];

            if(isset($params['start_date']) && isset($params['end_date'])){
                $updateAnnouncementBar['start_date'] = $params['start_date'];
                $updateAnnouncementBar['end_date'] = $params['end_date'];
            }

            $announcementBar->update($updateAnnouncementBar);

            // Insert announcementBar Data
            foreach ($params['announcement_bar_data'] as $announcementBarData) {
                $validLanguage[] = config('language')[$announcementBarData['language_type']];
                AnnouncementBarDetail::updateOrCreate(['bar_id'=>$params['id'],'language_type'=>config('language')[$announcementBarData['language_type']]],[
                    "subject" => $announcementBarData['subject'],
                    "description" => $announcementBarData['description'],
                    "upload_data" => ($announcementBarData['upload'] ?? []),
                    "deleted_at" => null
                ]);
            }

            AnnouncementBarDetail::where('bar_id',$params['id'])->whereNotIn('language_type',($validLanguage ?? []))->update(['deleted_at'=>DB::raw('now()')]);
            // Insert Country Setting
            AnnouncementBarSetting::updateOrCreate(["bar_id" => $params['id'],"name" => "valid-country"],["value" => json_encode($params['valid_country'] ?? [])]);
            // Insert URL Setting
            AnnouncementBarSetting::updateOrCreate(["bar_id" => $params['id'],"name" => "announcement-bar-url"],["value" => json_encode($params['announcement_bar_url'] ?? [])]);

            return true;
        });
    }

    public static function getAnnouncementBar($userData){
        $dateTime = date('Y-m-d H:i:s');
        $date = date('Y-m-d',strtotime($dateTime));
        $lang = config('app.locale') ?? 'en';
        $country_id = $userData->country_id ?? null;

        $items = self::query()
            ->where('status',self::$status['active'])
            ->where(function($q) use ($date){
                $q->where(function($q) use ($date){
                    $q->where(DB::raw('DATE(start_date)'),'<=',$date);
                    $q->where(DB::raw('DATE(end_date)'),'>=',$date);
                    return $q;
                })
                ->orWhere(function($q) use ($date){
                    $q->where('start_date',null)->where('end_date',null);
                    return $q;
                });
                return $q;
            })
            ->with(['announcementBarDetail' => function($q){
                $q->select('bar_id','subject','description','language_type');
                $q->orderBy('language_type','DESC');
                return $q;
            }])
            ->select(["id","start_date","end_date","status"])
            ->whereRelation('announcementBarSetting',function ($q) use ($country_id){
                $q->where('name','valid-country');
                return $q->whereJsonContains('value',[$country_id]);
            })
            ->get()
            ->map(function ($q) use ($lang){
                foreach ($q->announcementBarDetail as $key => $value) {
                    $defaultSetting = $value;
                    if(array_search($value['language_type'],config('language')) == $lang){
                        $defaultSetting = $value;
                        break;
                    }
                }

                foreach ($q->announcementBarSetting as $key => $value) {
                    if($value->name == 'valid-country') continue;
                    $announcementBarSetting[$value->name] = json_decode($value->value,true);
                }
    
                $res = [
                    "subject" => $defaultSetting->subject,
                    "description" => $defaultSetting->description,
                ];

                $res = array_merge($res,($announcementBarSetting ?? []));
                
                return (object) $res;
            });

        return $items ?? null;
    }
}
