<?php

namespace App\Models;

use Carbon\Carbon;
use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Notification extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        //
    ];

    protected static function booted(): void
    {
        static::deleting(function (Notification $notification) {
            foreach ($notification->details as $detail) {
                $detail->delete();
            }
        });

        static::restoring(function (Notification $notification) {
            $notification->details()
                ->withTrashed()
                ->restore();
        });
    }

    /* -------------------------------------------------------------------------- */
    /*                                Relationships                               */
    /* -------------------------------------------------------------------------- */
    public function userNotifications()
    {
        return $this->hasMany(UserNotification::class);
    }

    public function details()
    {
        return $this->hasMany(NotificationDetail::class);
    }

    public static function getList(array $params = [])
    {
        $lang = config('app.locale') ?? 'en';

        $sortableColumns = ['created_at'];
        $title = $params['title'] ?? null;

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : 'null';
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        // see all
        $seeAll = $params['see_all'] ?? null;

        $items = self::query()
            ->with(['details'])
            ->when(isset($title), function ($q) use ($title, $lang) {
                return $q->whereHas('details', function ($query) use ($title, $lang) {
                    $query->where('language_type', $lang)
                        ->where('title', 'like',  "%$title%");
                });
            })
            ->orderBy($order_by, $order_sort)
            ->paginate($limit);

        $items->getCollection()->transform(function ($item) use ($lang) {
            $detail = $item->details->where('language_type', $lang)->first();

            $res = [
                "id" => $item->id ?? null,
                "title" => $detail->title ?? null,
                "content" => $detail->content ?? null,
                "created_at" => $detail?->created_at
                    ? Carbon::parse($detail?->created_at)->toDateTimeString()
                    : null,
            ];

            return (object) $res;
        });

        return (new ItemsCollection($items))->toArray();
    }
}
