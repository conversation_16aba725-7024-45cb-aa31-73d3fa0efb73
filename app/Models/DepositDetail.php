<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use App\Traits;
use App\Traits\DecimalTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class DepositDetail extends Model
{
    use DecimalTrait;

    const UPDATED_AT = null;

    protected $table = 'deposit_detail';

    protected $hidden = [
        'deleted_at'
    ];

    protected $fillable = [
        'deposit_id',
        'name',
        'value',
        'type',
        'reference',
        'description',
        'created_at',
    ];

    public function deposit(){
        return $this->belongsTo(Deposit::class,"deposit_id","id");
    }
}

