<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AdminDetail extends Model
{
    use SoftDeletes;
    const CREATED_AT = null;
    const UPDATED_AT = null;

    protected $table = 'admin_detail';

    protected $hidden = [
    ];

    protected $fillable = [
        'name',
        'value',
        'type',
        'reference',
        'description',
        'admin_id',
    ];

    public static function createStore(array $params = []) {
        if (!isset($params['adminID']) || !isset($params['store_id'])) {
            return false;
        }

        $insertData = [
            'value' => json_encode($params['store_id']),
        ];

        self::updateOrCreate([
            'admin_id' => $params['adminID'],
            'name' => 'store',
        ],$insertData);

        return true;
    }
}
