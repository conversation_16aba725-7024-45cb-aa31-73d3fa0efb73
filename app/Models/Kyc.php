<?php

namespace App\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use App\Http\Resources\ItemsCollection;
use App\Traits\S3Trait;
use App\Traits\DateTrait;
use Illuminate\Support\Facades\Lang;

class Kyc extends Model
{
    use DateTrait;

    const UPDATED_AT = null;

    protected $table = 'kyc';

    protected $hidden = [];

    protected $fillable = [
        'user_id',
        'type',
        'name',
        'ic_no',
        'country_id',
        'email',
        'image_1',
        'image_2',
        'self_image',
        'document',
        'approved_at',
        'updater_id',
        'status',
        'remark'
    ];

    public static $kycStatus = [
        'pending' => 0,
        'successful' => 1,
        'declined' => 2,
    ];

    public static $kycType = [
        'ic' => 0,
        'passport' => 1,
    ];

    public static function registerKyc(array $params = [],$isDirectApproved = false)
    {
        $registerType = self::$kycType[$params['type']];
        $params['type'] = $registerType;

        if($isDirectApproved) {
            $params['status'] = self::$kycStatus['successful'];
            $params['approved_at'] = now();
            $params['updater_id'] = auth()->user()->id;
            $params['updated_at'] = now();
        }

        DB::transaction(function () use ($params) {
            self::Create($params);
        });

        return true;
    }

    public static function getOwnKyc(array $params = [])
    {
        $data = self::query()
            ->with(['country'])
            ->where(['user_id' => auth()->user()->id])
            ->orderBy('id', 'desc')
            ->take(1)
            ->get()
            ->map(function ($q) {
                $s3Client = S3Trait::getS3Client();
                $downloadImage = [];
                if (isset($q->image_1)) array_push($downloadImage, $q->image_1);
                if (isset($q->image_2)) array_push($downloadImage, $q->image_2);
                if (isset($q->self_image)) array_push($downloadImage, $q->self_image);
                if (isset($q->document)) array_push($downloadImage, $q->document);
                if (!empty($downloadImage)) $imgRes = S3Trait::awsDownload($s3Client,$downloadImage);
                $res = [
                    "user_id" => $q->user_id,
                    "name" => $q->name ?? null,
                    "ic" => $q->ic_no ?? null,
                    "email" => $q->email ?? null,
                    "country_name" => $q->country->name ?? null,
                    "country_name_display" => Lang::has("lang.".optional($q->country)->name) ? Lang::get("lang.".optional($q->country)->name) : optional($q->country)->name,
                    "type" => array_search($q->type, self::$kycType),
                    "image_1" => isset($imgRes[$q->image_1]) ? $imgRes[$q->image_1] : null,
                    "image_2" => isset($imgRes[$q->image_2]) ? $imgRes[$q->image_2] : null,
                    "self_image" => isset($imgRes[$q->self_image]) ? $imgRes[$q->self_image] : null,
                    "document" => isset($imgRes[$q->document]) ? $imgRes[$q->document] : null,
                    "status_display" => Lang::get('lang.kyc-' . array_search($q->status, self::$kycStatus)),
                    "status" => array_search($q->status, self::$kycStatus),
                    "remark" =>  $q->remark,
                ];
                return (object) $res;
            });
        $return = $data->first();
        return $return;
    }

    public static function getUserKyc(array $params = [])
    {
        $username = $params['username'] ?? Null;
        $type = $params['type'] ?? Null;
        $status = $params['status'] ?? Null;
        $email = $params['email'] ?? Null;
        $phoneNo = $params['phone_no'] ?? Null;
        $memberID = $params['member_id'] ?? Null;
        $fromDate = $params['from_date'] ?? Null;
        $toDate = $params['to_date'] ?? Null;
        $approveFromDate = $params['approve_from_date'] ?? Null;
        $approveToDate = $params['approve_to_date'] ?? Null;
        $country_id = $params['country'] ?? null;
        $icNo = $params['ic_no'] ?? null;
        $seeAll = $params['see_all'] ?? null;
        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        if (isset($params['export']) && ($params['export'] == 1)) {
            $params["model"] = get_class() ?? null;
            $params["function"] = __FUNCTION__;
            ExportReport::exportReport($params);
        }

        $items = self::query()
            ->with('userDetail', 'adminDetail', 'country')
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('DATE(created_at)'), ">=", $fromDate);
                return $q->where(DB::raw('DATE(created_at)'), "<=", $toDate);
            })
            ->when((isset($approveFromDate) && isset($approveToDate)), function ($q) use ($approveFromDate, $approveToDate) {
                $q->where(DB::raw('DATE(approved_at)'), ">=", $approveFromDate);
                return $q->where(DB::raw('DATE(approved_at)'), "<=", $approveToDate);
            })
            ->when((isset($phoneNo)), function ($q) use ($phoneNo) {
                return $q->whereRelation('userDetail', 'phone_no', $phoneNo);
            })
            ->when((isset($memberID)), function ($q) use ($memberID) {
                return $q->whereRelation('userDetail', 'member_id', $memberID);
            })
            ->when((isset($type)), function ($q) use ($type) {
                return $q->where('type', self::$kycType[$type]);
            })
            ->when((isset($username)), function ($q) use ($username) {
                return $q->whereRelation('userDetail', 'username', 'like', '%'.$username.'%');
            })
            ->when(isset($country_id), function ($q) use ($country_id) {
                return $q->where('country_id',$country_id);
            })
            ->when(isset($email), function ($q) use ($email) {
                return $q->where('email',$email);
            })
            ->when(isset($icNo), function ($q) use ($icNo) {
                return $q->where('ic_no', "like", "%".$icNo."%");
            })
            ->when(isset($status), function ($q) use ($status) {
                return $q->where('status', $status);
            })
            ->orderBy($order_by, $order_sort)
            ->when((!isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

       $mapFunc = function ($q) {
            $username = $q->userDetail->username;
            $adminUsername = $q->adminDetail->username ?? null;
            $res = [
                'id' => $q->id,
                'created_at' => DateTrait::dateFormat($q->created_at),
                'name' => $q->name ?? null,
                'member_id' => optional($q->userDetail)->member_id ?? null,
                'username' => $username,
                'ic_no' => $q->ic_no ?? null,
                'email' => $q->email ?? null,
                'type' => array_search($q->type, self::$kycType),
                "status_display" => Lang::get('lang.kyc-' . array_search($q->status, self::$kycStatus)),
                "status" => array_search($q->status, self::$kycStatus),
                "country_name" => $q->country->name ?? null,
                "country_display" => Lang::has("lang.".optional($q->country)->name) ? Lang::get("lang.".optional($q->country)->name) : optional($q->country)->name,
                'remarks' => $q->remark,
                'updater' => $adminUsername,
                'approved_at' => DateTrait::dateFormat($q->approved_at),
            ];

            return (object) $res;
        };

        if (($seeAll == 1)) {
            return ['list' => $items->get()->map($mapFunc)->toArray()];
        } else {
            $items->getCollection()->transform($mapFunc);
            return (new ItemsCollection($items))->toArray();
        }
    }

    public static function getUserKycDet(array $params = [])
    {
        $items = self::query()
            ->with('userDetail', 'country')
            ->where('id', $params['id'])
            ->get()
            ->map(function ($q) {
                $s3Client = S3Trait::getS3Client();
                $downloadImage = [];
                if (isset($q->image_1)) array_push($downloadImage, $q->image_1);
                if (isset($q->image_2)) array_push($downloadImage, $q->image_2);
                if (isset($q->self_image)) array_push($downloadImage, $q->self_image);                
                if (isset($q->document)) array_push($downloadImage, $q->document);
                if (!empty($downloadImage)) $imgRes = S3Trait::awsDownload($s3Client, $downloadImage);
                $res = [
                    'created_at' => DateTrait::dateFormat($q->created_at),
                    'type' => array_search($q->type, self::$kycType),
                    'name' => $q->name ?? null,
                    'username' => $q->userDetail->username,
                    'phone_no' => $q->userDetail->phone_no,
                    'ic_no' => $q->ic_no ?? null,
                    'email' => $q->email ?? null,
                    "country_name" => $q->country->name ?? null,
                    "country_display" => Lang::has("lang.".optional($q->country)->name) ? Lang::get("lang.".optional($q->country)->name) : optional($q->country)->name,
                    'image_1' => isset($imgRes[$q->image_1]) ? $imgRes[$q->image_1] : null,
                    'image_2' => isset($imgRes[$q->image_2]) ? $imgRes[$q->image_2] : null,
                    'self_image' => isset($imgRes[$q->self_image]) ? $imgRes[$q->self_image] : null,
                    'document' => isset($imgRes[$q->document]) ? $imgRes[$q->document] : null,
                    "status_display" => Lang::get('lang.kyc-' . array_search($q->status, self::$kycStatus)),
                    "status" => array_search($q->status, self::$kycStatus),
                    'remarks' => $q->remark,
                    'approved_at' => DateTrait::dateFormat($q->approved_at)
                ];
                return (object) $res;
            });
        $data["data"] = $items->first();

        return $data;
    }

    public static function updateKyc(array $params = [])
    {
        DB::transaction(function () use ($params) {
            $res = self::find($params['id']);

            User::where('id', $res->user_id)->update([
                "email" => $res->email,
                "ic_no" => $res->ic_no,
            ]);

            $res->update([
                'status' => self::$kycStatus[strtolower($params['status'])],
                'remark' => isset($params['remark']) ? $params['remark'] : null,
                'approved_at' => now(),
                'updater_id' => auth()->user()->id,
                'updated_at' => now(),
            ]);
        });

        return true;
    }

    public function userDetail()
    {
        return $this->belongsTo(User::class, "user_id", "id");
    }

    public function adminDetail()
    {
        return $this->belongsTo(Admin::class, "updater_id", "id");
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }
}
