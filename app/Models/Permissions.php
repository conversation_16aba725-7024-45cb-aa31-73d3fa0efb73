<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;

class Permissions extends Model
{
    const UPDATED_AT = null;

    protected $table = 'permissions';

    protected $hidden = [
    ];

    protected $fillable = [
        'parent_id',
        'name',
        'priority',
        'level',
        'api_url',
        'disabled',
        'master_disabled',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public static $level = [
        "menu" => 1,
        "sub_menu" => 2,
        "api" => 3,
    ];

    public static function getList($params = []){
        $user = Auth::user();
        $is_master = $user->is_master ?? 0;

        $list = Self::select(['id','name','parent_id','level'])
        ->when($is_master,function($q){
            return $q->where('master_disabled',0);
        },function($q){
            $q->where('disabled',0);
            return $q->where('master_disabled',0);
        })
        ->orderBy('priority','ASC')
        ->get()
        ->map(function ($q){
            $res = [
                "id" => $q->id,
                "name" => $q->name,
                "display" => Lang::has('lang.'.$q->name) ? __('lang.'.$q->name) : $q->name,
                "parent_id" => $q->parent_id,
                "level" => $q->level,
            ];
            return $res;
        });

        return $list;
    }

    public function child(){
        return $this->hasMany(Permissions::class, 'parent_id')->with(['child' => function($q) { $q->select('parent_id', 'id', 'name', 'api_url', 'disabled', 'level'); }]);
    }
}

