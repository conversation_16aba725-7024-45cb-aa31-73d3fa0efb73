<?php

namespace App\Models;

use App\Traits\DateTrait;
use App\Traits\DecimalTrait;
use App\Traits\TreeTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class TreeSponsor extends Model
{
    use DateTrait;
    use DecimalTrait;
    use TreeTrait;

    const CREATED_AT = null;

    const UPDATED_AT = null;

    protected $table = 'tree_sponsor';

    protected $hidden = [];

    protected $fillable = [
        'user_id',
        'upline_id',
        'level',
        'trace_key',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function upline()
    {
        return $this->hasMany(User::class, 'id', 'upline_id');
    }

    public function userSales()
    {
        return $this->belongsTo(UserSales::class, 'user_id', 'user_id');
    }

    public static function getTreeSponsor($params = [])
    {
        $userID = $params['real_user_id'];
        $targetUserID = $params['user_id'];
        $targetData = [];

        $user = User::with(['country'])->find($userID);
        $userCurrencyCode = $user->country->currency_code ?? null;

        $clientTree = User::with(['userSales' => function ($q) {
            return $q->select('user_id', 'downline_count', 'direct_downline_count', 'own_sales', 'group_sales', 'own_transfer', 'group_transfer', 'own_turnover', 'group_turnover');
        }, 'treeSponsor'])
            ->when(isset($params['username']), function ($q) use ($params) {
                $q->where('username', $params['username']);
            }, function ($q) use ($targetUserID) {
                $q->where('id', $targetUserID);
            })->get()->map(function ($q) use (&$targetData) {
                $targetData['level'] = $q->treeSponsor->level;
                $targetData['trace_key'] = $q->treeSponsor->trace_key;
                unset($q->treeSponsor);

                $groupTransfer = (optional($q->userSales)->own_transfer ?? 0) + (optional($q->userSales)->group_transfer ?? 0);
                $groupTurnover = (optional($q->userSales)->own_turnover ?? 0) + (optional($q->userSales)->group_turnover ?? 0);

                $res = [
                    'id' => $q->id,
                    'user_type' => array_search($q->user_type, User::$userType),
                    'phone_no' => $q->phone_no,
                    'member_id' => $q->member_id,
                    'username' => $q->username,
                    'name' => $q->name,
                    'created_at' => DateTrait::dateFormat($q->created_at, true),
                    'totalDirectDownline' => $q->userSales->direct_downline_count,
                    'totalDownline' => $q->userSales->downline_count,
                    'own_sales' => $q->userSales->own_sales,
                    'group_sales' => $q->userSales->group_sales,
                    'group_transfer' => DecimalTrait::setDecimal($groupTransfer),
                    'group_turnover' => DecimalTrait::setDecimal($groupTurnover),
                ];

                return $res;
            })->first();

        if (empty($clientTree)) {
            abort(400, json_encode(Lang::get('lang.invalid-downline-error')));
        }

        if (! in_array($userID, explode('/', $targetData['trace_key']))) {
            abort(400, json_encode(Lang::get('lang.invalid-downline-error')));
        }

        if ($userID == $targetUserID) {
            $searchLevel = $targetData['level'];
        } else {
            $realClientTree = TreeSponsor::where('user_id', $userID)->first();
            $searchLevel = $realClientTree->level;
        }

        $finalSponsorLevel = $targetData['level'] - $searchLevel;

        $downlineData = User::with(['userSales' => function ($q) {
            return $q->select('user_id', 'downline_count', 'direct_downline_count', 'own_sales', 'group_sales', 'own_transfer', 'group_transfer', 'own_turnover', 'group_turnover');
        }, 'treeSponsor'])
            ->whereRelation('treeSponsor', 'upline_id', $clientTree['id'])
            ->get()
            ->map(function ($q) {

                $groupTransfer = (optional($q->userSales)->own_transfer ?? 0) + (optional($q->userSales)->group_transfer ?? 0);
                $groupTurnover = (optional($q->userSales)->own_turnover ?? 0) + (optional($q->userSales)->group_turnover ?? 0);

                $res = [
                    'id' => $q->id,
                    'user_type' => array_search($q->user_type, User::$userType),
                    'phone_no' => $q->phone_no,
                    'member_id' => $q->member_id,
                    'username' => $q->username,
                    'name' => $q->name,
                    'created_at' => DateTrait::dateFormat($q->created_at, true),
                    'totalDirectDownline' => $q->userSales->direct_downline_count,
                    'totalDownline' => $q->userSales->downline_count ?? 0,
                    'own_sales' => $q->userSales->own_sales,
                    'group_sales' => $q->userSales->group_sales,
                    'group_transfer' => DecimalTrait::setDecimal($groupTransfer),
                    'group_turnover' => DecimalTrait::setDecimal($groupTurnover),
                ];

                return $res;
            })
            ->toArray();

        // Get breadcrumb
        $clientTraceKey = $targetData['trace_key'];
        if ($clientTraceKey) {
            $traceKey = array_filter(explode('/', $clientTraceKey), 'strlen');

            $realClientKey = array_search($userID, $traceKey);
            $clientKey = array_search($clientTree['id'], $traceKey);

            if ($realClientKey == '') {
                abort(400, json_encode(Lang::get('lang.invalid-downline-error')));
            }

            for ($i = $realClientKey; $i <= $clientKey; $i++) {
                $breadcrumbTemp[] = $traceKey[$i];
            }

            $breadcrumb = User::select('id', 'name', 'username')->whereIn('id', $breadcrumbTemp)->get()->all();
        }

        $data = [];

        $data['summary'] = [];
        $data['breadcrumb'] = $breadcrumb;
        $data['sponsor'] = $clientTree;
        $data['downlinesLevel'] = $downlineData;
        $data['uplineLevel'] = $finalSponsorLevel;

        return [
            'data' => $data,
        ];
    }

    public static function getTreeSponsorVertical($params = [])
    {
        $userID = $params['real_user_id'];
        $targetID = $params['user_id'];
        $downlineData = [];
        $targetData = [];
        $userIDAry = [];
        $targetUser = User::selectRaw('id,username,name,created_at,member_id')
            ->with(['treeSponsor', 'userSales'])
            ->when(isset($params['username']), function ($q) use ($params) {
                $q->where('username', $params['username']);
            }, function ($q) use ($targetID) {
                $q->where('id', $targetID);
            })->get()->map(function ($q) use (&$targetData, &$userIDAry) {
                $userIDAry[$q->id] = $q->id;

                $targetData['level'] = $q->treeSponsor->level;
                $targetData['trace_key'] = $q->treeSponsor->trace_key;
                unset($q->treeSponsor);

                $res = [
                    'id' => $q->id,
                    'member_id' => $q->member_id,
                    'username' => $q->username,
                    'name' => $q->name,
                    'created_at' => DateTrait::dateFormat($q->created_at, true),
                    'downlineCount' => $q->userSales->downline_count ?? 0,
                    'own_sales' => $q->userSales->own_sales,
                    'group_sales' => $q->userSales->group_sales,
                ];

                return $res;
            })->first();

        if (! in_array($userID, explode('/', $targetData['trace_key']))) {
            abort(400, json_encode(Lang::get('lang.invalid-downline-error')));
        }

        $data['target']['attr'] = $targetUser;

        $downlineData = User::with(['treeSponsor', 'userSales'])
            ->whereRelation('treeSponsor', 'level', '>', $targetData['level'])
            ->whereRelation('treeSponsor', 'level', '<=', ($targetData['level'] + 1))
            ->whereRelation('treeSponsor', 'trace_key', 'LIKE', $targetData['trace_key'].'/%')
            ->get()
            ->map(function ($q) use (&$userIDAry) {
                $userIDAry[$q->id] = $q->id;

                $res = [
                    'id' => $q->id,
                    'member_id' => $q->member_id,
                    'username' => $q->username,
                    'name' => $q->name,
                    'created_at' => DateTrait::dateFormat($q->created_at, true),
                    'downlineCount' => $q->userSales->downline_count ?? 0,
                    'own_sales' => $q->userSales->own_sales,
                    'group_sales' => $q->userSales->group_sales,
                    'disabled' => ($q->disabled == 0) ? 'No' : 'Yes',
                    'suspended' => ($q->suspended == 0) ? 'No' : 'Yes',
                ];

                return ['attr' => $res];
            })
            ->toArray();

        $data['downline'] = $downlineData;

        $data['targetID'] = (trim($params['user_id']) == trim($params['real_user_id'])) ? null : trim($params['real_user_id']);

        return ['data' => $data];
    }

    public static function getUplines($userID = '', $limit = '', $includeSelf = true)
    {
        if (! $userID) {
            return false;
        }

        $treeRow = self::where('user_id', $userID)->lockForUpdate()->first();
        $clientArray = explode('/', $treeRow->trace_key);
        if ($limit == 1) {
            $uplines[] = $clientArray[1];
        } else {
            for ($i = 0; $i < count($clientArray); $i++) {
                $uplines[] = $clientArray[$i];
            }
        }

        // Sort the array by descending because we want to loop from bottom to top
        krsort($uplines);

        if (! $includeSelf) {
            unset($uplines[array_search($userID, $uplines)]);
        }

        return $uplines;
    }

    public static function changeReferral($params = [])
    {
        try {
            DB::transaction(function () use ($params) {
                // Preset Variable
                $userID = $params['user_id'];
                $newReferralId = $params['new_referral_id'];
                $oldReferralId = $params['old_referral_id'];

                $traceKeyRes = self::whereIN('user_id', [$newReferralId, $userID])->lockForUpdate()->get()->keyBy('user_id')->toArray();
                if (empty($traceKeyRes)) {
                    throw new Exception('Invalid Trace Key');
                }

                $newRefTraceKey = $traceKeyRes[$newReferralId]['trace_key'];
                $newRefLvl = $traceKeyRes[$newReferralId]['level'];
                $oldRefTraceKey = $traceKeyRes[$userID]['trace_key'];
                $oldRefLvl = $traceKeyRes[$userID]['level'];

                $diffLevel = ($newRefLvl + 1) - $oldRefLvl;

                // Update User Sales
                UserSales::moveSales($userID, 'decrease');

                // Update User Tree
                $newPath = ($newRefTraceKey) ? $newRefTraceKey.'/'.$userID : $userID;
                $data = self::where('user_id', $userID)->lockForUpdate()->first();
                $data->update(['upline_id' => $newReferralId, 'trace_key' => DB::raw("replace(trace_key,'{$oldRefTraceKey}','{$newPath}')"), 'level' => ($newRefLvl + 1)]);

                $downlines = self::where('trace_key', 'like', "{$oldRefTraceKey}/%")->lockForUpdate()->get();
                self::where('trace_key', 'like', "{$oldRefTraceKey}/%")->update(['trace_key' => DB::raw("replace(trace_key,'{$oldRefTraceKey}','{$newPath}')"), 'level' => DB::raw('level +'.$diffLevel)]);

                // Update User Referral
                $user = User::where('id', $userID)->lockForUpdate()->first();
                $user->update(['sponsor_id' => $newReferralId]);

                $userSales = UserSales::where('user_id', $userID)->lockForUpdate()->first();
                $userSales->update(['sponsor_id' => $newReferralId]);

                UserSales::moveSales($userID, 'increase');
            });
        } catch (\Throwable $e) {
            $err = $e->getMessage();
            abort(400, $err);
        }

        return true;
    }

    public static function getTreeSponsorSummary(array $params = [])
    {
        $datas = [];
        ProductDetail::where('disabled', 0)->get()->map(function ($q) use (&$datas) {
            $datas[$q->id] = ['product_name' => Lang::has('lang.'.$q->product_name) ? Lang::get('lang.'.$q->product_name) : $q->product_name, 'category' => $q->category, 'product_price' => DecimalTrait::setDecimal($q->price), 'total_amount_usd' => 0, 'total_amount_est' => 0, 'total_user' => 0];
        });
        // $rate = CurrencyRate::with(['fromCurrencySetting', 'toCurrencySetting'])
        //     ->whereRelation('fromCurrencySetting', 'type', CurrencySetting::$type['fiat'])
        //     ->whereRelation('toCurrencySetting', 'type', CurrencySetting::$type['fiat'])
        //     ->orderBy('created_at', 'DESC')->orderBy('id', 'DESC')->get()->first();
        // $rate = $rate->rate ?? 1;

        $user = User::with(['country'])->find($params['user_id']);
        $userCurrencyCode = $user->country->currency_code ?? null;
        $rate = CurrencyRate::getCurrencyRate($userCurrencyCode, config('users.default_currency'));

        $downline = self::select(DB::raw('GROUP_CONCAT(user_id) as all_downline'))
            ->where(
                'trace_key',
                'like',
                DB::raw("'".
                    TreeSponsor::select('trace_key')
                        ->where(
                            'user_id',
                            isset($params['username']) ? User::where('username', $params['username'])->first()->id : $params['user_id']
                        )->first()->trace_key."/%'")
            )->first();

        if (! empty($downline->all_downline)) {
            UserSales::query()
                ->select('product_id', DB::raw('count(1) as total'))
                ->whereIn('user_id', explode(',', $downline->all_downline))->whereNot('product_id', 0)
                ->groupBy('product_id')->get()
                ->map(function ($q) use (&$datas) {
                    $datas[$q->product_id]['total_user'] += $q->total;
                    $datas[$q->product_id]['total_amount_usd'] += $q->total * $datas[$q->product_id]['product_price'];
                });
        }
        $totalUser = 0;
        foreach ($datas as $data) {
            $data['total_amount_usd'] = DecimalTrait::setDecimal($data['total_amount_usd']);
            $data['total_amount_est'] = DecimalTrait::setDecimal($data['total_amount_est'] * $rate['deposit_rate']);
            $totalUser += $data['total_user'];
            $output['summary'] = [
                'total_user' => $totalUser,
            ];
            $output[$data['category']][] = $data;
        }

        return ['data' => $output];
    }

    public static function getSponsorTreeUplines($userID, $limit = null, $includeSelf = true)
    {
        $clientTraceKey = self::where('user_id', $userID)->first()->trace_key ?? null;
        $uplineIDArray = explode('/', $clientTraceKey);

        if ($includeSelf != true) {
            unset($uplineIDArray[count($uplineIDArray) - 1]);
        }

        $data = [];

        if (! empty($limit)) {
            for ($count = 1; $count <= $limit; $count++) {
                if (! empty($uplineIDArray[count($uplineIDArray) - $count])) {
                    $data[] = $uplineIDArray[count($uplineIDArray) - $count];
                }
            }
        } else {
            for ($count = 1; $count <= count($uplineIDArray); $count++) {
                if (! empty($uplineIDArray[count($uplineIDArray) - $count])) {
                    $data[] = $uplineIDArray[count($uplineIDArray) - $count];
                }
            }
        }

        return $data;
    }

    public static function getDownlineUserIdsByUserId($userId)
    {
        $downlines = self::where('upline_id', $userId)
            ->get()
            ->map(function ($e) {
                return $e->user_id;
            });

        return $downlines;
    }

    public static function getUplineIdByUserId($userId)
    {
        $upline = self::where('user_id', $userId)->first()->upline_id;

        return $upline;
    }
}
