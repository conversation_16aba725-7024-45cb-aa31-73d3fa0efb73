<?php

namespace App\Models;

use App\Traits;
use App\Traits\DecimalTrait;
use App\Traits\DateTrait;
use App\Traits\GenerateNumberTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class UserRebate extends Model
{
    use HasFactory;

    protected $fillable = [
        'serial_number',
        'user_id',
        'from',
        'to',
        'amount',
        'decimal',
        'status',
        'remark',
        'updated_by',
    ];

    public static $status = [
        'pending' => 0,
        'approved' => 1,
        'rejected' => 2,
    ];

    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    private static function getPendingRebateByUserId($userId)
    {
        $pendingRebate = self::where('user_id', $userId)
            ->where('status', self::$status['pending'])
            ->first();

        return $pendingRebate;
    }

    private static function getAvailableRebateAmount($userId)
    {
        $lastDate = self::where('user_id', $userId)
            ->whereIn('status', [self::$status['approved'], self::$status['pending']])
            ->orderBy('created_at', 'DESC')
            ->first()?->to;

        $query = UserLevelTransaction::where('user_id', $userId)
            ->when(isset($lastDate), function ($q) use ($lastDate) {
                return $q->where(DB::raw('created_at'), '>', $lastDate);
            });

        if (!$query->count()) {
            $amount = 0;
        } else {
            $amount = $query->sum('rebate_amount');
        }

        return $amount;
    }

    public static function getRebatesByUserId($userId, $fromDate = null, $toDate = null)
    {
        $approvedRebate = self::where('user_id', $userId)
            ->where('status', self::$status['approved'])
            ->orderBy('created_at', 'DESC');
        $query = self::where('user_id', $userId)
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                return $q->where(DB::raw('DATE(created_at)'), '>=', $fromDate)
                    ->where(DB::raw('DATE(created_at)'), '<=', $toDate);
            })
            ->orderBy('created_at', 'DESC');
        // $pendingRebate = self::getPendingRebateByUserId($userId);
        $lastRebate = $approvedRebate->first();

        $pendingClaim = self::getAvailableRebateAmount($userId); //$pendingRebate?->amount ?? 0;
        $lastClaim = $lastRebate?->amount ?? 0;
        $totalClaim = $approvedRebate->sum('amount') ?? 0;
        $historyRebates = $query->get()->map(function ($e) {
            return [
                'date' => date_format($e->created_at, 'Y-m-d'),
                'amount' => DecimalTrait::setDecimal($e->amount),
                'status' => $e->status,
            ];
        });

        return [
            'pending_claim' => DecimalTrait::setDecimal($pendingClaim),
            'last_claim' => DecimalTrait::setDecimal($lastClaim),
            'total_claim' => DecimalTrait::setDecimal($totalClaim),
            'history_rebates' => $historyRebates,
        ];
    }

    public static function requestRebate($userId)
    {
        $lastRebate = self::where('user_id', $userId)
            ->orderBy('created_at', 'DESC')
            ->first();
        $lastDate = self::where('user_id', $userId)
            ->where('status', self::$status['approved'])
            ->orderBy('created_at', 'DESC')
            ->first()
            ?->to;

        if (($lastRebate?->status ?? -1) == self::$status['pending']) {
            abort(400, json_encode('Pending for approval'));
        }

        $query = UserLevelTransaction::where('user_id', $userId)
            ->when(isset($lastDate), function ($q) use ($lastDate) {
                return $q->where(DB::raw('DATE(created_at)'), '>', $lastDate);
            });

        if (! $query->count()) {
            abort(400, json_encode('No remaining rebate'));
        }

        $dateRange = $query->selectRaw('MIN(created_at) AS from_date, MAX(created_at) AS to_date')->first();
        $fromDate = $dateRange->from_date;
        $toDate = $dateRange->to_date;
        $totalRebate = $query->sum('rebate_amount');

        if (! $totalRebate || $totalRebate <= 0) {
            abort(400, json_encode('No remaining rebate'));
        }

        $refNo = 'RB' . str_pad(self::query()->max('id'), 9, 0, STR_PAD_LEFT);

        $created = self::create([
            'serial_number' => $refNo,
            'user_id' => $userId,
            'from' => $fromDate,
            'to' => $toDate,
            'amount' => $totalRebate,
            'status' => self::$status['pending'],
        ]);

        $approvedRebate = self::where('user_id', $userId)
            ->where('status', self::$status['approved'])
            ->orderBy('created_at', 'DESC');
        $pendingRebate = self::getPendingRebateByUserId($userId);
        $lastRebate = $approvedRebate->first();

        // $pendingClaim = $pendingRebate?->amount ?? 0;
        $pendingClaim = self::getAvailableRebateAmount($userId); //$pendingRebate?->amount ?? 0;
        $lastClaim = $lastRebate?->amount ?? 0;
        $totalClaim = $approvedRebate->sum('amount') ?? 0;
        $rebate = $created->fresh();
        $historyRebate = [
            'date' => date_format($rebate->created_at, 'Y-m-d'),
            'amount' => DecimalTrait::setDecimal($rebate->amount),
            'status' => $rebate->status,
        ];

        return [
            'pending_claim' => DecimalTrait::setDecimal($pendingClaim),
            'last_claim' => DecimalTrait::setDecimal($lastClaim),
            'total_claim' => DecimalTrait::setDecimal($totalClaim),
            'history_rebate' => $historyRebate,
        ];
    }

    public static function updateRebateStatus($userRebateId, $action)
    {
        $rebate = UserRebate::find($userRebateId);
        if (!$rebate) {
            abort(400, 'User rebate not found');
        }

        $status = UserRebate::$status[$action];

        DB::transaction(function () use ($rebate, $status) {
            if ($rebate->status == self::$status['pending'] && $status == self::$status['approved']) {
                $credit = Credit::first();
                $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;
                $batchId = $belongId;
                $dateTime = date('Y-m-d H:i:s');

                $internalID = User::select('id')->where('username', 'creditSales')->where('user_type', User::$userType['internal-account'])->first()->id ?? null;
                CreditTransaction::insertTransaction(
                    $internalID,
                    $rebate->user_id,
                    $rebate->user_id,
                    $credit->name,
                    $rebate->amount,
                    'vip-rebate-payout',
                    $belongId,
                    $batchId,
                    null,
                    $dateTime,
                    null,
                    null,
                    null
                );
            }

            $rebate->update([
                'status' => $status,
                'updated_by' => auth()->user()->id,
                'approved_at' => $status = self::$status['approved'] ? date('Y-m-d H:i:s') : $dateTime,
                'approved_by' => $status = self::$status['approved'] ? auth()->user()->id : null,
            ]);
        });

        try {
            if (in_array($action, ['pending', 'approved', 'rejected'])) {
                $userID = $rebate->user_id;
                $userDeviceToken = UserDevice::where('user_id', $userID)->where('status', UserDevice::$status['active'])->first();
                if (isset($userDeviceToken)) {
                    $users = User::find($userID);
                    $lang = $users->lang ?? 'en';
                    $templateType = null;
                    $template = null;
                    switch ($action) {
                        case 'approved':
                            $templateType = 'rebateApproved';
                            $template = Traits\FirebaseTrait::template($templateType, $lang);
                            break;

                        case 'rejected':
                            $templateType = 'rebateRejected';
                            $template = Traits\FirebaseTrait::template($templateType, $lang);
                            break;
                    }
                    $template['body'] = str_replace('{{serial_no}}', $rebate->serial_number, $template['body']);
                    $notification[] = [
                        'user_id' => $userDeviceToken->user_id,
                        'token' => $userDeviceToken->token,
                        'template' => $template,
                        'data' => ['rebate' => $rebate->id, 'type' => $templateType],
                        'reference_data' => ['serial_no' => $rebate->serial_number],
                    ];
                }
            }

            foreach ($notification as $noti) {
                try {
                    Traits\FirebaseTrait::sendNotification($noti['token'], $noti['template'], $noti['data'], $noti['user_id'], $noti['reference_data']);
                } catch (\Throwable $th) {
                    // throw $th;
                }
            }
        } catch (\Throwable $th) {
            //throw $th;
        }
    }

    public static function getUserRebateDetail(array $params = [])
    {
        $items = self::query()
            ->with('user')
            ->where('id', $params['id'])
            ->get()
            ->map(function ($q) use (&$s3Client) {
                $status = array_search($q->status, self::$status);

                $isEditable = in_array($q->status, [self::$status['pending']]) ? 1 : 0;

                if ($q->updater_id >= 1000000) {
                    $updater = $q->updateUser ? $q->updateUser->username : null;
                } elseif ($q->updater_id == 0) {
                    $updater = 'system';
                } else {
                    $updater = $q->updateAdmin ? $q->updateAdmin->username : null;
                }

                $res = [
                    'id' => $q->id,
                    'withdrawal_reference' => $q->serial_number,
                    'created_at' => DateTrait::dateFormat($q->created_at),
                    'username' => $q->user->username ?? null,
                    'member_id' => $q->user->member_id ?? null,
                    'phone_no' => $q->user->phone_no ?? null,
                    'amount' => DecimalTrait::setDecimal($q->amount),
                    'status' => $status,
                    'status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                    'remark' => $q->remark ?: '-',
                    'updated_at' => DateTrait::dateFormat($q->updated_at),
                    'updated_by' => $updater,
                    'is_editable' => $isEditable,
                ];

                return $res;
            })
            ->first();

        return $items;
    }
}
