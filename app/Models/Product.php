<?php

namespace App\Models;

use App\Traits\CacheTrait;
use App\Traits\DateTrait;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use CacheTrait;
    use DateTrait;

    protected $table = 'product';

    protected $hidden = [];

    protected $fillable = [
        'id',
        'name',
        'display_name',
        'code',
        'type',
        'priority',
        'image_url',
        'status',
        'api_url',
        'provider_code',
        'operator_code',
        'key',
        'aggregator',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
    ];

    public static $id = [
        'TK8' => 2001,
        'MT' => 2002,
        'CQ9' => 2003,
        'JK' => 2004,
        'JILI' => 2005,
        'ATLASPLAY' => 2006,
    ];

    public function productSetting()
    {
        return $this->hasMany(ProductSetting::class, 'product_id', 'id');
    }
}
