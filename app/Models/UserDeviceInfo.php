<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Http\Resources\ItemsCollection;
use App\Traits;
use App\Traits\DateTrait;


class UserDeviceInfo extends Model
{

    protected $table = 'user_device_info';

    protected $hidden = [];

    protected $fillable = [
        'user_id',
        'uuid',
        'type',
        'brand',
        'model',
        'name',
        'device_info',
        'status',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $status = [
        "inactive" => 0,
        "active" => 1,
    ];

    public function user(){
        return $this->belongsTo(User::class,"user_id","id");
    }

    public function admin(){
        return $this->belongsTo(Admin::class,'updater_id','id');
    }

    public static function updateUserDInfo(array $params = [])
    {
        $id = $params['id'] ?? null;
        $status = $params['status'] ?? null;
        $rec = self::find($id);
        $rec->update(['status' => $status, 'updater_id'=>auth()->user()->id]);
        
        if($status == self::$status['inactive']){
            $oriAuth = Auth();
            $oriToken = $oriAuth->getToken();

            $guard = "users";
            $previousToken = DB::table('jwt_token')
                ->select('id', 'uid', 'token')
                ->where('guard', $guard)
                ->where('uid', $rec->user_id)
                ->where(DB::raw('now()'), '<', DB::raw('expiry_at'))
                ->get();

            if (! empty($previousToken)) {
                foreach ($previousToken as $pt) {
                    DB::table('jwt_token')->where('id',$pt->id)->update(['expiry_at' => DB::raw('now()')]);
                    $v = Auth::guard($guard)->setToken($pt->token);
                    $v->invalidate(true);            

                    Auth::setToken($oriToken);
                }
            }
        }

        return true;
    }

    public static function insertUserDInfo(array $params = [])
    {
        $userId = $params['user_id'] ?? null;
        $uuid = $params['uuid'] ?? null;
        $type = $params['type'] ?? null;
        $brand = $params['device_brand'] ?? null;
        $model = $params['device_model'] ?? null;
        $name = $params['device_name'] ?? null;
        $status = $params['status'] ?? self::$status['active'];
        $deviceInfo = $params['device_info'] ?? null;

        $update['user_id'] = $userId;
        $update['uuid'] = $uuid;
        $update['type'] = $type;
        $update['brand'] = $brand;
        $update['model'] = $model;
        $update['name'] = $name;
        $update['status'] = $status;

        $update['device_info'] = $deviceInfo;

        $info = self::where("user_id", $userId)
                    ->where("uuid", $uuid)
                    ->where("status", $status)->get()->first();

        if(isset($info)){
            $info->update($update);
        }else{
            $rec = self::create($update);
            $rec->save();
        }
        
        return true;
    }

    public static function checkValidDeviceToLogin(array $params = [])
    {
        // one user one device
        $userId = $params['user_id'] ?? null;
        $uuid = $params['uuid'] ?? null;

        $mapUUIdUserId = [];
        $mapUserIdUUId = [];
        self::select('user_id','uuid')->where('user_id', $userId)->where('status', self::$status['active'])
                    ->orWhere('uuid', $uuid)->where('status', self::$status['active'])
                    ->get()->map(function ($q) use (&$mapUUIdUserId, &$mapUserIdUUId){
                        $mapUUIdUserId[$q->uuid] = $q->user_id;
                        $mapUserIdUUId[$q->user_id] = $q->uuid;
                    });

        if(count($mapUUIdUserId) == 0 && count($mapUserIdUUId) == 0){
            return true;
        }
        
        if(count($mapUUIdUserId) == 1 && count($mapUserIdUUId) == 1){
            $condition1 = isset($mapUUIdUserId[$uuid]) && $mapUUIdUserId[$uuid] == $userId;
            $condition2 = isset($mapUserIdUUId[$userId]) && $mapUserIdUUId[$userId] == $uuid;

            if($condition1 && $condition2){
                return true;
            }
        }

        return false;
    }

    public static function getList(array $params = [])
    {
        $memberId = $params['member_id'] ?? null;
        $phoneNo = $params['phone_no'] ?? null;
        $status = $params['status'] ?? null;
        $seeAll = $params['see_all'] ?? null;

        $sortableColumns = ['name', 'username', 'email', 'status'];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = ($seeAll == 1) ? null : ($params['limit'] ?? config('app.pagination_rows'));

        if (isset($params['export']) && ($params['export'] == 1)) {
            $params["model"] = get_class() ?? null;
            $params["function"] = __FUNCTION__;
            ExportReport::exportReport($params);
        }

        $items = self::query()->with([
                        'user' => function ($q){
                            $q->select('id', 'member_id', 'username', 'phone_no');
                        }, 
                        'admin' => function ($q){
                            $q->select('id', 'username');
                        }, 
                    ])
                    ->when(isset($memberId), function ($q) use ($memberId) {
                        $q->whereRelation('user', 'member_id', $memberId);
                    })
                    ->when(isset($phoneNo), function ($q) use ($phoneNo) {
                        $q->whereRelation('user', 'phone_no', $phoneNo);
                    })
                    ->when(isset($status), function ($q) use ($status) {
                        $q->where('status', $status);
                    })
                    ->orderBy($order_by, $order_sort)
                    ->paginate($limit);

        $mapFunc = function($q) {

            $status = $q->status;
            $statusName = array_search($q->status, self::$status);
            $statusDisplay = Lang::has('lang.'.$statusName) ? Lang::get('lang.'.$statusName) : $statusName;
            $res = [
                'id' => $q->id,
                'brand' => $q->brand,
                'model' => $q->model,
                'member_id' => $q->user->member_id ?? null,
                'phone_no' => $q->user->phone_no ?? null,
                'status' => $status,
                'statusName' => $statusName,
                'status_display' => $statusDisplay,
                'updated_at' => Traits\DateTrait::dateFormat($q->updated_at),
                'updated_by' => $q->admin->username ?? null,
                'type' => $q->type ?? null,
            ];

            $deviceInfoString = $q->device_info;
            $firstDecode = json_decode($deviceInfoString, true);
            $deviceInfoData = is_string($firstDecode) ? json_decode($firstDecode, true) : $firstDecode;

            if ($deviceInfoData && is_array($deviceInfoData)) {
                $res['brand'] = $deviceInfoData['brand'] ?? $q->brand;
                $res['model'] = $deviceInfoData['model'] ?? $q->model;
                $res['version_release'] = $deviceInfoData['version.release'] ?? $q->version_release ?? null;
                $res['version_base_os'] = $deviceInfoData['version.baseOS'] ?? $q->version_base_os ?? null;
            } else {
                $res['brand'] = $q->brand;
                $res['model'] = $q->model;
                $res['version_release'] = $q->version_release ?? null;
                $res['version_base_os'] = $q->version_base_os ?? null;
            }

            return (object) $res;
        };

        if (($seeAll == 1)) {
            $listing = $items->get()->map($mapFunc)->toArray();
            return ['list' => $listing];
        } else {
            $items->getCollection()->transform($mapFunc);
            $data = new ItemsCollection($items);
            return $data;
        }
    }

    public static function getLinkedDevice(array $params = [])
    {
        $userId = $params['user_id'] ?? null;


        $deviceInfo = self::select('model', 'brand', 'name', 'created_at')->where('user_id', $userId)
                        ->where('status', self::$status['active'])
                        ->get()->first();

        $data['linked'] = false;
        if(isset($deviceInfo)){
            $data['linked'] = true;
            $data['model'] = $deviceInfo->model ?? null;
            $data['brand'] = $deviceInfo->brand ?? null;
            $data['name'] = $deviceInfo->name ?? null;
            $data['created_at'] = isset($deviceInfo->created_at) ? DateTrait::dateFormat($deviceInfo->created_at) : null;
        }
        
        return $data;
    }
}
