<?php

namespace App\Models;

use App\Traits\DateTrait;
use App\Traits\DecimalTrait;
use App\Traits\GenerateNumberTrait;
use App\Traits\TreeTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Auth;
use App\Http\Resources\ItemsCollection;

class ProductDetail extends Model
{
    use GenerateNumberTrait;
    use DecimalTrait;

    protected $table = 'product_detail';

    protected $hidden = [
    ];

    protected $fillable = [
        'product_id',
        'product_name',
        'category',
        'price',
        'priority',
        'product_setting',
        'disabled',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected $casts = [
        "product_setting" => 'array'
    ];

    public static $purchaseWallet = '';

    public function product(){
        return $this->belongsTo(Product::class, "product_id", "id");
    }
}
