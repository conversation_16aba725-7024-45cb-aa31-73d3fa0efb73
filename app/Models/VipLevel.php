<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VipLevel extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'badge_image',
        'design_meta',
        'target_point',
        'rebate',
        'rebate_offline',
        'rebate_display',
        'status',
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
    ];

    public static function getVipLevels()
    {
        $levels = self::where('status', true)
            ->get()
            ->map(function ($item) {
                $designMeta = json_decode($item->design_meta);

                return [
                    'name' => $item->name,
                    'level' => $item->level,
                    'badge_image' => $item->badge_image,
                    'card_logo' => $designMeta->card_logo,
                    'balance_theme' => $designMeta->balance_theme,
                    'vip_card_theme' => $designMeta->vip_card_theme,
                    'target_point' => $item->target_point,
                    'rebate' => $item->rebate,
                    'rebate_display' => $item->rebate_display,
                ];
            });

        return $levels;
    }
}
