<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use App\Services\GameProvider\GSC;
use App\Services\ThirdPartyService;
use App\Traits;
use App\Traits\DateTrait;
use App\Traits\DecimalTrait;
use App\Traits\GenerateNumberTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Str;

class ExTransfer extends Model
{
    use DateTrait;

    protected $table = 'ex_transfer';

    protected $hidden = [];

    public static $type = [
        'in' => 1,
        'out' => 2,
    ];

    public static $status = [
        'processing' => 0,
        'confirmed' => 1,
        'failed' => 2,
        'refunded' => 3,
    ];

    protected $fillable = [
        'user_id',
        'transaction_id',
        'type',
        'credit_id',
        'product_id',
        'card_id',
        'amount',
        'charges',
        'receivable_amount',
        'res_data',
        'ref_tx_id',
        'reference',
        'machine_id',
        'status',
        'remark',
        'belong_id',
        'created_at',
        'updated_by',
        'updated_at',
        'deleted_at',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function admin()
    {
        return $this->belongsTo(Admin::class, 'updated_by', 'id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public function userCard()
    {
        return $this->belongsTo(UserCard::class, 'card_id', 'id');
    }

    public function creditType()
    {
        return $this->belongsTo(Credit::class, 'credit_id', 'id');
    }

    /* -------------------------------------------------------------------------- */
    /*                                   Scopes */
    /* -------------------------------------------------------------------------- */
    public function scopeIn($query)
    {
        return $query->where('type', self::$type['in']);
    }

    public function scopeOut($query)
    {
        return $query->where('type', self::$type['out']);
    }

    public function scopeConfirmed($query)
    {
        return $query->where('status', self::$status['confirmed']);
    }

    public static function add(array $params = [])
    {
        $datetime = date('Y-m-d H:i:s');
        $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;
        $userId = $params['user_id'] ?? null;
        $walletData = $params['wallet_data'] ?? null;
        $creditId = $params['credit_id'] ?? null;
        $cardId = $params['card_id'] ?? null;
        $amount = $params['amount'] ?? null;
        $type = $params['type'] ?? null;
        // $password = $params['password'] ?? null;

        $trxId = GenerateNumberTrait::generateReferenceNo(self::query(), 'transaction_id', null);

        $userCard = UserCard::find($cardId);
        $credit = Credit::find($creditId);

        $insertData = [
            'user_id' => $userId,
            'transaction_id' => $trxId,
            'credit_id' => $creditId ?? null,
            'type' => $type ?? null,
            'card_id' => $cardId ?? null,
            'amount' => DecimalTrait::setDecimal($amount, 2) ?? null,
            'receivable_amount' => $amount ?? null,
            'status' => self::$status['processing'],
            'belong_id' => $belongId,
            'created_at' => $datetime,
            // "password" => $password,
        ];

        return DB::transaction(function () use ($insertData, $walletData, $userCard, $trxId, $datetime, $credit) {
            $internalId = User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;

            $exTransfer = self::create($insertData);
            if (! $exTransfer) {
                throw new \Exception('Ex Transfer Failed');
            }

            $jobData = [
                'ex_transfer_id' => $exTransfer->id,
                'remark' => $trxId,
                'curl_type' => 'withdraw',

                'ucId' => $userCard->id,
                'storeId' => $userCard->store_id,
                'cardId' => $userCard->card_id,
                // 'password' => $insertData['password'],
                'amount' => $insertData['amount'],
            ];

            if (array_search($insertData['type'], self::$type) == 'in') {
                CreditTransaction::insertTransaction($insertData['user_id'], $internalId, $insertData['user_id'], $walletData['credit_type'], $insertData['amount'], 'ex-transfer-out', $insertData['belong_id'], $insertData['belong_id'], null, $datetime, $trxId, null, null, $exTransfer->id, null, false);
                $jobData['curl_type'] = 'deposit';
                $jobData['amount'] = $insertData['amount'];
            }

            $job = new \App\Jobs\ProcessMerchant(json_encode($jobData));
            // dispatch($job)->onQueue('merchant');
            dispatch_sync($job);

            $status = array_search($insertData['status'], self::$status);

            return [
                'status' => $status,
                'status_display' => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                'transaction_id' => $insertData['transaction_id'],
                'amount' => $insertData['amount'],
                'credit_name' => $credit->name,
                'credit_display' => Lang::has('lang.'.$credit->name) ? Lang::get('lang.'.$credit->name) : $credit->name,
                'date_time' => DateTrait::dateFormat($datetime),
            ];
        });
    }

    public static function addI8(array $params = [])
    {
        $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;
        $walletData = $params['wallet_data'] ?? null;
        $productData = $params['product_data'] ?? null;
        $exMemberId = $params['exMemberId'] ?? null;
        if (isset($params['type']) && $params['type'] == self::$type['out']) {
            $params['amount'] = $params['product_data']['balance'] ?? 0;
        }

        $datetime = date('Y-m-d H:i:s');

        $autoCashIn = false;
        if (isset($params['auto_cash_in']) && $params['auto_cash_in']) {
            $autoCashIn = true;
            $trxId = GenerateNumberTrait::generateReferenceNo(self::query(), 'transaction_id', null, $params['product_data']['name']);
        } else {
            $trxId = GenerateNumberTrait::generateReferenceNo(self::query(), 'transaction_id', null);
        }

        $insertData = [
            'user_id' => $params['user_id'],
            'transaction_id' => $trxId,
            'credit_id' => $params['credit_id'] ?? null,
            'type' => $params['type'] ?? null,
            'product_id' => $params['product_id'] ?? null,
            'wallet_type' => $params['wallet_type'] ?? null,
            'amount' => DecimalTrait::setDecimal($params['amount'], 2) ?? null,
            'receivable_amount' => $params['amount'] ?? null,
            'status' => self::$status['processing'],
            'belong_id' => $belongId,
        ];

        return DB::transaction(function () use ($insertData, $walletData, $productData, $datetime, $exMemberId, $trxId, $params, $autoCashIn) {
            $internalId = User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;

            if ($autoCashIn) {
                $insertData['reference'] = $params['order_id'];
                // $insertData['ref_tx_id'] = $params['transaction_id'];
            }

            $exTransfer = self::create($insertData);
            if (! $exTransfer) {
                throw new \Exception('Ex Transfer Failed');
            }

            $jobData = [
                'ex_transfer_id' => $exTransfer->id,
                'account' => $exMemberId,
                'amount' => $insertData['amount'],
                'remark' => $trxId,
                'product_id' => $insertData['product_id'],
                'wallet_type' => $insertData['wallet_type'] ?? null,
                'user_id' => $insertData['user_id'],
                'curl_type' => 'withdraw',
            ];

            if ($autoCashIn) {
                // for callback/deposit
                $jobData['deposit_id'] = $params['deposit_id'] ?? null;
                $jobData['transaction_id'] = $params['order_id'] ?? null;
                $jobData['status'] = 'SUCCESS' ?? null;
                $jobData['merchant_reference'] = $trxId ?? null;
                $jobData['transaction_time'] = $datetime ?? null;
                $jobData['promo_id'] = $params['promo_id'] ?? null;
            }

            if (array_search($insertData['type'], self::$type) == 'in') {
                // CreditTransaction::insertTransaction($insertData['user_id'], $internalId, $insertData['user_id'], $walletData['credit_type'], $insertData['amount'], "ex-transfer-out", $insertData['belong_id'], $insertData['belong_id'], null, $datetime, $trxId, null, null, $exTransfer->id, null, false);
                // $jobData['curl_type'] = "deposit";
                // if(isset($params['auto_cash_in']) && $params['auto_cash_in']) $jobData['curl_type'] = "callback/deposit";
                // $jobData['amount'] = $insertData['amount'];

                $jobData['curl_type'] = 'deposit';
                if (isset($params['auto_cash_in']) && $params['auto_cash_in']) {
                    $jobData['curl_type'] = 'callback/deposit';
                    $jobData['amount'] = $insertData['amount'];
                } else {
                    CreditTransaction::insertTransaction($insertData['user_id'], $internalId, $insertData['user_id'], $walletData['credit_type'], $insertData['amount'], 'ex-transfer-out', $insertData['belong_id'], $insertData['belong_id'], null, $datetime, $trxId, null, null, $exTransfer->id, null, false);
                }
            }

            $job = new \App\Jobs\ProcessMerchant(json_encode($jobData));
            dispatch($job)->onQueue('merchant');

            $status = array_search($insertData['status'], self::$status);

            return [
                'status' => $status,
                'status_display' => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                'transaction_id' => $insertData['transaction_id'],
                'product_name' => $productData['name'],
                'product_name_display' => $productData['display'],
                'amount' => $insertData['amount'],
                'datetime' => DateTrait::dateFormat(($datetime)),
            ];
        });
    }

    public static function transferIn(array $params = [])
    {
        if (in_array($params['user_id'], [1000160, 1004122, 1004295])) {
            // check ex_transfer got duplicate record
            $duplicate = ExTransfer::where('user_id', $params['user_id'])
                ->where('product_id', $params['product_id'])
                ->where('type', self::$type['in'])
                ->where('created_at', '>=', Carbon::now()->subMinutes(1))
                ->where('created_at', '<=', Carbon::now())
                ->first();
            if (isset($duplicate)) {
                abort(400, json_encode(['Please Wait 1 minute before next access']));
            }
        }

        $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;
        $walletData = $params['wallet_data'] ?? null;
        $isJKEnterGame = $params['is_jk_enter_game'] ?? false;
        $productData = $params['product_data'] ?? null;
        $exMemberId = $params['exMemberId'] ?? null;
        $datetime = date('Y-m-d H:i:s');
        $trxId = GenerateNumberTrait::generateReferenceNo(self::query(), 'transaction_id', null, $params['product_name']);

        $insertData = [
            'user_id' => $params['user_id'],
            'transaction_id' => $trxId,
            'credit_id' => $params['credit_id'] ?? null,
            'type' => 1, // WARNING: hardcode
            'product_id' => $params['product_id'] ?? null,
            'wallet_type' => $params['wallet_type'] ?? null,
            'credit_type' => $params['credit_type'] ?? null,
            'amount' => DecimalTrait::setDecimal($params['amount'], 2) ?? null,
            'receivable_amount' => $params['amount'] ?? null,
            'status' => self::$status['processing'],
            'belong_id' => $belongId,
            'machine_id' => $params['machine_id'] ?? null,
        ];

        return DB::transaction(function () use ($insertData, $productData, $datetime, $exMemberId, $trxId, $isJKEnterGame) {
            $internalId = User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;

            $exTransfer = self::create($insertData);
            if (! $exTransfer) {
                throw new \Exception('Ex Transfer Failed');
            }

            $jobData = [
                'ex_transfer_id' => $exTransfer->id,
                'account' => $exMemberId,
                'amount' => $insertData['amount'],
                'remark' => $trxId,
                'product_id' => $insertData['product_id'],
                'wallet_type' => $insertData['wallet_type'] ?? null,
                'user_id' => $insertData['user_id'],
                'curl_type' => 'deposit',
            ];

            CreditTransaction::insertTransaction(
                $insertData['user_id'],
                $internalId,
                $insertData['user_id'],
                $insertData['credit_type'],
                $insertData['amount'],
                'ex-transfer-out',
                $insertData['belong_id'],
                $insertData['belong_id'],
                null,
                $datetime,
                $trxId,
                null,
                null,
                $exTransfer->id,
                null,
                false
            );

            // $job = new \App\Jobs\ProcessMerchant(json_encode($jobData));
            // dispatch($job)->onQueue('merchant');

            if (! $isJKEnterGame) {
                switch ($productData['name']) {
                    case 'TK8':
                        (new ThirdPartyService)->TK8($jobData, $productData);
                        break;

                    case 'MT':
                        (new ThirdPartyService)->MT($jobData, $productData);
                        break;

                    case 'CQ9':
                        (new ThirdPartyService)->CQ9($jobData, $productData);
                        break;

                    case 'JK':
                        (new ThirdPartyService)->JK($jobData, $productData);
                        break;

                    case 'JILI':
                        (new ThirdPartyService)->JILI($jobData, $productData);
                        break;

                    case 'GSC':
                        (new ThirdPartyService)->GSC($jobData, $productData);
                        break;

                    case 'KISS':
                        (new ThirdPartyService)->KISS($jobData, $productData);
                        break;

                    case 'MEGA':
                        (new ThirdPartyService)->MEGA($jobData, $productData);
                        break;

                    case 'PUSSY':
                        (new ThirdPartyService)->PUSSY($jobData, $productData);
                        break;
                }
            } else {
                $update = ['status' => ExTransfer::$status['confirmed']];
                ExTransfer::where([
                    'id' => $exTransfer->id ?? 0,
                ])->update($update);
            }

            $status = array_search($insertData['status'], self::$status);

            return [
                'status' => $status,
            ];

            // return [
            //     "status" => $status,
            //     "status_display" => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
            //     "transaction_id" => $insertData['transaction_id'],
            //     "product_name" => $productData['name'],
            //     "product_name_display" => $productData['display'],
            //     "amount" => $insertData['amount'],
            //     "datetime" => DateTrait::dateFormat(($datetime))
            // ];
        });
    }

    public static function transferInWithBonus(array $params = [])
    {
        $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;
        $walletData = $params['wallet_data'] ?? null;
        $productData = $params['product_data'] ?? null;
        $exMemberId = $params['exMemberId'] ?? null;
        $datetime = date('Y-m-d H:i:s');
        $trxId = GenerateNumberTrait::generateReferenceNo(self::query(), 'transaction_id', null, $params['product_name']);
        $bonusAmount = $params['bonus_amount'] ?? null;

        $insertData = [
            'user_id' => $params['user_id'],
            'transaction_id' => $trxId,
            'credit_id' => $params['credit_id'] ?? null,
            'type' => 1, // WARNING: hardcode
            'product_id' => $params['product_id'] ?? null,
            'wallet_type' => $params['wallet_type'] ?? null,
            'credit_type' => $params['credit_type'] ?? null,
            'amount' => DecimalTrait::setDecimal($params['amount'], 2) ?? null,
            'receivable_amount' => $params['amount'] ?? null,
            'status' => self::$status['processing'],
            'belong_id' => $belongId,
            'machine_id' => $params['machine_id'] ?? null,
        ];

        return DB::transaction(function () use ($insertData, $productData, $datetime, $exMemberId, $trxId, $bonusAmount) {
            $internalId = User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;

            $exTransfer = self::create($insertData);
            if (! $exTransfer) {
                throw new \Exception('Ex Transfer Failed');
            }

            $jobData = [
                'ex_transfer_id' => $exTransfer->id,
                'account' => $exMemberId,
                'amount' => $insertData['amount'] + $bonusAmount,
                'remark' => $trxId,
                'product_id' => $insertData['product_id'],
                'wallet_type' => $insertData['wallet_type'] ?? null,
                'user_id' => $insertData['user_id'],
                'curl_type' => 'deposit',
            ];

            CreditTransaction::insertTransaction(
                $insertData['user_id'],
                $internalId,
                $insertData['user_id'],
                $insertData['credit_type'],
                $insertData['amount'],
                'ex-transfer-out',
                $insertData['belong_id'],
                $insertData['belong_id'],
                null,
                $datetime,
                null,
                null,
                null,
                $exTransfer->id,
            );

            CreditTransaction::insertTransaction(
                $insertData['user_id'],
                $internalId,
                $insertData['user_id'],
                $insertData['credit_type'],
                $bonusAmount,
                'promotion-bonus-payout',
                $insertData['belong_id'],
                $insertData['belong_id'],
                null,
                $datetime,
                null,
                null,
                null,
                $exTransfer->id,
            );

            // $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;
            //
            // CreditTransaction::insertTransaction(
            //     $insertData['user_id'],
            //     $exTransfer['user_id'],
            //     $insertData['user_id'],
            //     $insertData['credit_type'],
            //     $insertData['amount'],
            //     'ex-transfer-out',
            //     $belongId,
            //     $belongId,
            //     null,
            //     $datetime,
            //     $trxId,
            //     null,
            //     null,
            //     $exTransfer->id,
            //     null,
            //     false
            // );

            // $job = new \App\Jobs\ProcessMerchant(json_encode($jobData));
            // dispatch($job)->onQueue('merchant');

            if ($productData['aggregator'] == GSC::$name) {
                (new ThirdPartyService)->GSC($jobData, $productData);
            } else {
                switch ($productData['name']) {
                    case 'TK8':
                        (new ThirdPartyService)->TK8($jobData, $productData);
                        break;

                    case 'MT':
                        (new ThirdPartyService)->MT($jobData, $productData);
                        break;

                    case 'CQ9':
                        (new ThirdPartyService)->CQ9($jobData, $productData);
                        break;

                    case 'JK':
                        (new ThirdPartyService)->JK($jobData, $productData);
                        break;

                    case 'JILI':
                        (new ThirdPartyService)->JILI($jobData, $productData);
                        break;

                    case 'KISS':
                        (new ThirdPartyService)->KISS($jobData, $productData);
                        break;

                    case 'MEGA':
                        (new ThirdPartyService)->MEGA($jobData, $productData);
                        break;

                    case 'PUSSY':
                        (new ThirdPartyService)->PUSSY($jobData, $productData);
                        break;
                }
            }

            $status = array_search($insertData['status'], self::$status);

            return [
                'status' => $status,
            ];

            // return [
            //     "status" => $status,
            //     "status_display" => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
            //     "transaction_id" => $insertData['transaction_id'],
            //     "product_name" => $productData['name'],
            //     "product_name_display" => $productData['display'],
            //     "amount" => $insertData['amount'],
            //     "datetime" => DateTrait::dateFormat(($datetime))
            // ];
        });
    }

    public static function transferOut(array $params = [])
    {
        $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;
        $walletData = $params['wallet_data'] ?? null;
        $productData = $params['product_data'] ?? null;
        $exMemberId = $params['exMemberId'] ?? null;
        $datetime = date('Y-m-d H:i:s');
        $trxId = GenerateNumberTrait::generateReferenceNo(self::query(), 'transaction_id', null, $params['product_name']);

        $insertData = [
            'user_id' => $params['user_id'],
            'transaction_id' => $trxId,
            'credit_id' => $params['credit_id'] ?? null,
            'type' => 2 ?? null, // WARNING: hardcode
            'product_id' => $params['product_id'] ?? null,
            'wallet_type' => $params['wallet_type'] ?? null,
            'credit_type' => $params['credit_type'] ?? null,
            'amount' => DecimalTrait::setDecimal($params['amount'], 2) ?? null,
            'receivable_amount' => $params['amount'] ?? null,
            'status' => self::$status['processing'],
            'belong_id' => $belongId,
            'machine_id' => $params['machine_id'] ?? null,
        ];

        return DB::transaction(function () use ($insertData, $productData, $exMemberId, $trxId, $params) {
            $internalId = User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;

            $exTransfer = self::create($insertData);
            if (! $exTransfer) {
                throw new \Exception('Ex Transfer Failed');
            }

            $jobData = [
                'ex_transfer_id' => $exTransfer->id,
                'account' => $exMemberId,
                'amount' => $insertData['amount'],
                'remark' => $trxId,
                'product_id' => $insertData['product_id'],
                'wallet_type' => $insertData['wallet_type'] ?? null,
                'user_id' => $insertData['user_id'],
                'curl_type' => $params['curl_type'] ?? 'withdraw',
            ];

            switch ($productData['name']) {

                case 'TK8':
                    (new ThirdPartyService)->TK8($jobData, $productData);
                    break;

                case 'MT':
                    (new ThirdPartyService)->MT($jobData, $productData);
                    break;

                case 'CQ9':
                    (new ThirdPartyService)->CQ9($jobData, $productData);
                    break;

                case 'JK':
                    (new ThirdPartyService)->JK($jobData, $productData);
                    break;

                case 'JILI':
                    (new ThirdPartyService)->JILI($jobData, $productData);
                    break;

                case 'GSC':
                    (new ThirdPartyService)->GSC($jobData, $productData);
                    break;

                case 'KISS':
                    (new ThirdPartyService)->KISS($jobData, $productData);
                    break;

                case 'MEGA':
                    (new ThirdPartyService)->MEGA($jobData, $productData);
                    break;

                case 'PUSSY':
                    (new ThirdPartyService)->PUSSY($jobData, $productData);
                    break;
            }

            // $job = new \App\Jobs\ProcessMerchant(json_encode($jobData));
            // dispatch($job)->onQueue('merchant');

            $status = array_search($insertData['status'], self::$status);

            return [
                'status' => $status,
            ];

            // return [
            //     "status" => $status,
            //     "status_display" => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
            //     "transaction_id" => $insertData['transaction_id'],
            //     "product_name" => $productData['name'],
            //     "product_name_display" => $productData['display'],
            //     "amount" => $insertData['amount'],
            //     "datetime" => DateTrait::dateFormat(($datetime))
            // ];
        });
    }

    public static function getExTransferList(array $params = [])
    {
        $fromDate = $params['from_date'] ?? null;
        $toDate = $params['to_date'] ?? null;
        $username = $params['username'] ?? null;
        $productId = $params['product_id'] ?? null;
        $phoneNo = $params['phone_no'] ?? null;
        $memberId = $params['member_id'] ?? null;
        $cardId = $params['card_id'] ?? null;
        $transactionId = $params['transaction_id'] ?? null;
        $creditId = $params['credit_id'] ?? null;
        $transferType = $params['transfer_type'] ?? null;
        $userID = $params['user_id'] ?? null;
        $cardSerialNo = $params['card_serial_no'] ?? null;
        $storeId = $params['store_id'] ?? null;
        $exStoreId = $params['extra_store_id'] ?? null;

        $seeAll = $params['see_all'] ?? null;

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        if (isset($params['export']) && ($params['export'] == 1)) {
            $params['model'] = get_class() ?? null;
            $params['function'] = __FUNCTION__;
            ExportReport::exportReport($params);
        }

        $items = self::query()
            ->when(isset($userID), function ($q) use ($userID) {
                $q->where('user_id', $userID);
            })
            ->when(isset($memberId), function ($q) use ($memberId) {
                $q->whereRelation('user', 'member_id', $memberId);
            })
            ->when(isset($phoneNo), function ($q) use ($phoneNo) {
                return $q->whereRelation('user', function ($q) use ($phoneNo) {
                    $phoneNo = preg_replace('/[^0-9]/', '', $phoneNo);
                    $q->where('phone_no', 'like', '%'.$phoneNo.'%');
                });
            })
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('DATE(created_at)'), '>=', $fromDate);

                return $q->where(DB::raw('DATE(created_at)'), '<=', $toDate);
            })
            ->when(isset($username), function ($q) use ($username) {
                $q->whereRelation('user', 'username', 'LIKE', '%'.$username.'%');
            })
            ->when(isset($transactionId), function ($q) use ($transactionId) {
                $q->where('transaction_id', $transactionId);
            })
            ->when(isset($productId), function ($q) use ($productId) {
                $q->where('product_id', $productId);
            }, function ($q) {
                $q->whereNull('product_id');
            })
            ->when(isset($cardId), function ($q) use ($cardId) {
                $q->where('card_id', $cardId);
            })
            ->when(isset($transferType), function ($q) use ($transferType) {
                $q->where('type', $transferType);
            })
            ->when(isset($creditId), function ($q) use ($creditId) {
                $q->where('credit_id', $creditId);
            })
            ->when(isset($cardSerialNo), function ($q) use ($cardSerialNo) {
                $q->whereRelation('userCard', 'card_serial_no', $cardSerialNo);
            })
            ->when(isset($storeId), function ($q) use ($storeId) {
                $q->whereRelation('userCard.store', 'id', $storeId);
            })
            ->when(isset($exStoreId), function ($q) use ($exStoreId) {
                return $q->whereHas('userCard.store', function ($q) use ($exStoreId) {
                    $q->whereIn('id', $exStoreId);
                });
            });

        $summary = clone $items;
        $items = $items->with(['user', 'userCard', 'userCard.store', 'product'])->orderBy($order_by, $order_sort)
            ->when((! isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $summaryData = $summary->selectRaw('SUM(amount) AS transfer_amount')->get()->map(function ($q) {
            return [
                'transfer_amount' => DecimalTrait::setDecimal(($q->transfer_amount)),
            ];
        })->first();

        $grandTotal = [
            'grand_amount' => 0,
        ];

        $mapFunc = function ($q) use (&$grandTotal) {
            $type = array_search($q->type, self::$type);
            $displayType = null;
            if ($type == 'in') {
                $displayType = 'cash-in';
            }
            if ($type == 'out') {
                $displayType = 'cash-out';
            }
            $status = array_search($q->status, self::$status);

            switch (MODULE) {
                case 'admin':

                    // Check transaction refundable for deposit
                    $refundable = 0;
                    if ($q->status == self::$status['failed'] && $type == 'in') {
                        $refundable = 1;
                    }

                    $creditable = 0;
                    if ($q->status == self::$status['failed'] && $type == 'out') {
                        $creditable = 1;
                    }

                    // checking if from third party
                    $recallAvailable = 0;
                    $productCode = optional($q->product)->code ?? null;
                    if (! empty($productCode) && $q->status == self::$status['failed'] && $type == 'in') {
                        if (Str::startsWith($q->transaction_id ?? '', $productCode.'_')) {
                            $recallAvailable = 1;
                            $refundable = 0;
                        }
                    }

                    $res = [
                        'ex_transfer_id' => $q->id,
                        'created_at' => DateTrait::dateFormat($q->created_at),
                        'transaction_id' => $q->transaction_id,
                        'reference' => $q->reference,
                        'username' => $q->user->username,
                        'store_name' => $q->user->store->name ?? null,
                        'phone_no' => $q->user->phone_no ?? null,
                        'member_id' => $q->user->member_id ?? null,
                        'product_name' => optional($q->product)->name ?? null,
                        'product_name_display' => isset($q->product) ? (Lang::has('lang.'.$q->product->name) ? Lang::get('lang.'.$q->product->name) : $q->product->name) : null,
                        'type' => $type,
                        'type_display' => Lang::has('lang.'.$displayType) ? Lang::get('lang.'.$displayType) : $type,
                        'status' => $status,
                        'status_display' => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                        'amount' => DecimalTrait::setDecimal($q->amount),
                        'updated_at' => DateTrait::dateFormat($q->updated_at),
                        'updated_by' => $q->admin->name ?? '-',
                        'refundable' => $refundable,
                        'creditable' => $creditable,
                        'recall_available' => $recallAvailable,
                        'res_data' => $q->res_data,

                        'remark' => $q->remark,
                        'card_serial_no' => $q->userCard->card_serial_no ?? null,
                        'store' => $q->userCard->store->name ?? null,
                    ];

                    $grandTotal['grand_amount'] = DecimalTrait::setDecimal($grandTotal['grand_amount'] + $q->amount);
                    break;

                case 'user':
                case 'app':
                    if ($q->status == self::$status['failed'] && $type == 'out') {
                        $status = 'in-progress';
                    }

                    $res = [
                        'type' => $type,
                        'type_display' => Lang::has('lang.'.$displayType) ? Lang::get('lang.'.$displayType) : $type,
                        'status' => $status,
                        'status_display' => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                        'amount' => Traits\DecimalTrait::setDecimal($q->amount),
                        'created_at' => Traits\DateTrait::dateFormat($q->created_at),
                        'trnx_id' => $q->transaction_id,
                    ];
                    break;
            }

            return (object) $res;
        };

        if (($seeAll == 1)) {
            return ['list' => $items->get()->map($mapFunc)->toArray()] + (MODULE == 'admin' ? ['grand_total' => $grandTotal, 'summary_data' => $summaryData] : []);
        } else {
            $items->getCollection()->transform($mapFunc);

            return (new ItemsCollection($items))->toArray() + (MODULE == 'admin' ? ['grand_total' => $grandTotal, 'summary_data' => $summaryData] : []);
        }
    }

    public static function refundConfirmation(array $params = [])
    {

        $exTransfer = ExTransfer::with(['creditType'])
            ->where([
                'id' => $params['ex_transfer_id'],
                'status' => ExTransfer::$status['failed'],
            ])
            ->first();

        return DB::transaction(function () use ($exTransfer) {
            // Process refund manually
            $internalId = User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;
            CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $exTransfer['amount'], 'ex-transfer-refund', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);

            // Record updated by
            $updater = Auth::User()->id;
            $update = [
                'updated_by' => $updater,
                'status' => self::$status['refunded'],
            ];

            $exTransfer->update($update);

            return true;
        });
    }

    public static function getExTransferData(array $params = [])
    {
        $creditID = $params['credit_id'] ?? null;
        $credit = Credit::with(['creditSetting', 'currency'])
            ->when(isset($creditID) && ! empty($creditID), function ($q) use ($creditID) {
                return $q->where('id', $creditID);
            })->first();
        if (! isset($credit) || empty($credit)) {
            throw new \Exception('Credit Not Found', 400);
        }

        $creditSettingRow = $credit->creditSetting ?? [];
        $creditSetting = [];
        foreach ($creditSettingRow as $cs) {
            $creditSetting[$cs->name] = $cs->toArray();
        }

        if (! isset($creditSetting['is-ex-transferable']) || empty($creditSetting['is-ex-transferable'])) {
            throw new \Exception('Deposit Currently Not Available...', 400);
        } else {
            if ($creditSetting['is-ex-transferable']['value'] != 1 || $creditSetting['is-ex-transferable']['member'] != 1) {
                throw new \Exception('Deposit Currently Not Available...', 400);
            }
        }

        $data = [];
        $data['ex-transfer-min-amt'] = 0.00;
        if (isset($creditSetting['ex-transfer-min-amt']) && ! empty($creditSetting['ex-transfer-min-amt'])) {
            $data['ex-transfer-min-amt'] = isset($creditSetting['ex-transfer-min-amt']['value']) ? DecimalTrait::setDecimal($creditSetting['ex-transfer-min-amt']['value']) : 0.00;
        }

        $depositOption = [];
        if (isset($creditSetting['ex-transfer-in-option']['value'])) {
            $depositOptionAry = json_decode($creditSetting['ex-transfer-in-option']['value']);
            foreach ($depositOptionAry as $value) {

                $double = Traits\DecimalTrait::setDecimal($value, 2);
                $display = (string) $value;

                $depositOption[] = [
                    'value' => $double,
                    'display' => $display,
                ];
            }
        }
        $data['deposit-option'] = $depositOption ?? [];

        $withdrawOption = [];
        if (isset($creditSetting['ex-transfer-out-option']['value'])) {
            $withdrawOptionAry = json_decode($creditSetting['ex-transfer-out-option']['value']);
            foreach ($withdrawOptionAry as $value) {

                $double = Traits\DecimalTrait::setDecimal($value, 2);
                $display = (string) $value;

                $withdrawOption[] = [
                    'value' => $double,
                    'display' => $display,
                ];
            }
        }
        $data['withdraw-option'] = $withdrawOption ?? [];

        return ['data' => $data];
    }

    public static function recallOCConfirmation(array $params = [])
    {
        $webHook = TpWebHook::find($params['web_hook_id']);
        $reqData = json_decode($webHook->req_data);

        $product = Product::where('code', strtoupper($reqData->company))->first();

        $jobData = [
            'ex_transfer_id' => $params['ex_transfer_id'],
            'account' => $reqData->account,
            'amount' => $reqData->amount,
            'remark' => $reqData->remark,
            'product_id' => $product->id,
            'curl_type' => 'callback/deposit',
        ];

        // for callback/deposit
        $jobData['transaction_id'] = $reqData->transaction_id ?? null;
        $jobData['status'] = 'SUCCESS' ?? null;
        $jobData['merchant_reference'] = $reqData->merchant_reference ?? null;
        $jobData['transaction_time'] = $reqData->transaction_time ?? null;
        $jobData['promo_id'] = $reqData->promo_id ?? null;

        $job = new \App\Jobs\ProcessMerchant(json_encode($jobData));
        dispatch($job)->onQueue('merchant');
    }

    public static function creditConfirmation(array $params = [])
    {

        $exTransfer = ExTransfer::with(['creditType'])
            ->where([
                'id' => $params['ex_transfer_id'],
                'status' => self::$status['failed'],
                'type' => self::$type['out'],
            ])
            ->first();

        return DB::transaction(function () use ($exTransfer) {
            // Process credit manually
            $internalId = User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;
            CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $exTransfer['amount'], 'ex-transfer-refund', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);

            // Record updated by
            $updater = Auth::User()->id;
            $update = [
                'updated_by' => $updater,
                'status' => self::$status['confirmed'],
            ];

            $exTransfer->update($update);

            return true;
        });
    }

    public static function getSummaryProfitLossReport($params = [])
    {
        $store_id = $params['store_id'] ?? null;
        $product_id = $params['product_id'] ?? null;
        $excludeDummy = isset($params['is_exclude_dummy']) ? $params['is_exclude_dummy'] : false;

        if (isset($params['from_date']) && isset($params['to_date'])) {
            $fromDate = $params['from_date'];
            $toDate = $params['to_date'];
        } else {
            $fromDate = now()->format('Y-m-d');
            $toDate = now()->format('Y-m-d');
        }

        if (isset($params['export']) && ($params['export'] == 1)) {
            $jobData = $params;
            $jobData['model'] = get_class() ?? null;
            $jobData['function'] = __FUNCTION__;
            $jobData['module'] = MODULE;
            $jobData['creator_type'] = MODULE;
            $jobData['creator_id'] = Auth::user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, 'Export Successfully');
        }

        // Pagination
        $page = 1;
        $limit = 0;
        $fromRow = 0;
        $toRow = INF;
        $lastPage = 1;
        $tableTotal = 0;
        if (! isset($seeAll) || $seeAll == 0) {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? config('app.pagination_rows');
            $fromDateTS = strtotime(date('Y-m-d 00:00:00', strtotime($fromDate)));
            $toDateTS = strtotime(date('Y-m-d 23:59:59', strtotime($toDate)));
            $totalRow = ($toDateTS - $fromDateTS) / (24 * 60 * 60) + 1;
            $fromRow = (($page - 1) * $limit) + 1;
            $toRow = $page * $limit;
            $lastPage = ceil(($totalRow / $limit));
            if ($page == $lastPage) {
                $toRow = $totalRow;
            }
        }

        $onlineGroupTotal = ExTransfer::join('users', 'users.id', '=', 'ex_transfer.user_id')
            ->join('store', 'store.store_id', '=', 'users.store_id')
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                DB::raw('CASE WHEN ex_transfer.type = 1 AND ex_transfer.machine_id IS NULL THEN SUM(ex_transfer.amount) ELSE 0 END as transfer_in_online'),
                DB::raw('CASE WHEN ex_transfer.type = 1 AND ex_transfer.machine_id IS NOT NULL THEN SUM(ex_transfer.amount) ELSE 0 END as transfer_in_offline'),
                DB::raw('CASE WHEN ex_transfer.type = 2 AND ex_transfer.machine_id IS NULL THEN SUM(ex_transfer.amount) ELSE 0 END as transfer_out_online'),
                DB::raw('CASE WHEN ex_transfer.type = 2 AND ex_transfer.machine_id IS NOT NULL THEN SUM(ex_transfer.amount) ELSE 0 END as transfer_out_offline'),
                DB::raw('DATE(ex_transfer.created_at) AS transaction_date'),
            )
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('ex_transfer.created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where('ex_transfer.created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(isset($store_id), function ($q) use ($store_id) {
                return $q->where('users.store_id', $store_id);
            })
            ->whereNull('card_id')
            ->where('ex_transfer.receivable_amount', '>', 0)
            ->where('ex_transfer.status', self::$status['confirmed'])
            ->whereNotIn('ex_transfer.user_id', ['1001200', '1001201', '1001202', '1001242', '1001243', '1001244', '1001262', '1001258', '1001259', '1001260', '1000021'])
            ->when(isset($product_id), function ($q) use ($product_id) {
                return $q->where('ex_transfer.product_id', $product_id);
            })
            ->when($excludeDummy, function ($q) {
                return $q->where('users.is_dummy', false);
            })
            ->groupBy(DB::raw('DATE(ex_transfer.created_at)'), 'ex_transfer.type', 'ex_transfer.machine_id', 'store.id', 'store.name')
            ->orderBy('store.id', 'asc')
            ->get()->toArray();

        $mergedOnline = [];

        $online_user_ids = [];
        if ($fromDate < '2025-02-19' && $toDate < '2025-02-19') {
            $online_user_ids = RewardSpinLog::select('user_id')->where('reward_type', 4)
                ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                    $q->where('created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                    return $q->where('created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
                })->pluck('user_id')->toArray();
        }

        $onlinePromotion = UserPromotion::join('users', 'users.id', '=', 'user_promotions.user_id')
            ->join('store', 'store.store_id', '=', 'users.store_id')
            ->join('promotions', 'promotions.id', '=', 'user_promotions.promotion_id')
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                'user_promotions.user_id',
                'user_promotions.claim_type',
                DB::raw('SUM(user_promotions.bonus_amount) as promotion_in_online'),
                DB::raw('DATE(user_promotions.created_at) AS transaction_date'),
            )
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('user_promotions.created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where('user_promotions.created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(isset($store_id), function ($q) use ($store_id) {
                return $q->where('users.store_id', $store_id);
            })
            ->where('promotions.product_id', $product_id)
            ->where(function ($q) use ($online_user_ids) {
                if (! empty($online_user_ids)) {
                    $q->whereIn('user_promotions.user_id', $online_user_ids);
                } else {
                    $q->where('user_promotions.claim_type', '!=', UserPromotion::$claimType['offline']);
                }
            })
            ->groupBy(DB::raw('DATE(user_promotions.created_at)'), 'store.id', 'store.name', 'user_promotions.user_id', 'user_promotions.claim_type')
            ->orderBy('store.id', 'asc')
            ->get()->toArray();

        $offlinePromotion = UserPromotion::join('users', 'users.id', '=', 'user_promotions.user_id')
            ->join('store', 'store.store_id', '=', 'users.store_id')
            ->join('promotions', 'promotions.id', '=', 'user_promotions.promotion_id')
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                'user_promotions.user_id',
                'user_promotions.claim_type',
                DB::raw('SUM(user_promotions.bonus_amount) as promotion_in_offline'),
                DB::raw('DATE(user_promotions.created_at) AS transaction_date'),
            )
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('user_promotions.created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where('user_promotions.created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(isset($store_id), function ($q) use ($store_id) {
                return $q->where('users.store_id', $store_id);
            })
            ->where('promotions.product_id', $product_id)
            ->where(function ($q) use ($online_user_ids) {
                if (! empty($online_user_ids)) {
                    $q->whereNotIn('user_promotions.user_id', $online_user_ids);
                } else {
                    $q->where('user_promotions.claim_type', '=', UserPromotion::$claimType['offline']);
                }
            })
            ->groupBy(DB::raw('DATE(user_promotions.created_at)'), 'store.id', 'store.name', 'user_promotions.user_id', 'user_promotions.claim_type')
            ->orderBy('store.id', 'asc')
            ->get()->toArray();

        // $onlinePromotion = [];
        // $offlinePromotion = [];
        // $promotions->each(function ($item) use ($online_user_ids, &$onlinePromotion, &$offlinePromotion) {
        //     // Due to previous claim_type value is null
        //     if ($item->claim_type != null) {
        //         if ($item->claim_type == UserPromotion::$claimType['offline']) {
        //             $offlinePromotion[] = [
        //                 'store_id' => $item->store_id,
        //                 'store_name' => $item->store_name,
        //                 'promotion_in_offline' => $item->total_bonus_amount,
        //                 'transaction_date' => $item->transaction_date,
        //             ];
        //         } else {
        //             $onlinePromotion[] = [
        //                 'store_id' => $item->store_id,
        //                 'store_name' => $item->store_name,
        //                 'promotion_in_online' => $item->total_bonus_amount,
        //                 'transaction_date' => $item->transaction_date,
        //             ];
        //         }
        //     } else {
        //         if (in_array($item->user_id, $online_user_ids)) {
        //             $onlinePromotion[] = [
        //                 'store_id' => $item->store_id,
        //                 'store_name' => $item->store_name,
        //                 'promotion_in_online' => $item->total_bonus_amount,
        //                 'transaction_date' => $item->transaction_date,
        //             ];
        //         } else {
        //             $offlinePromotion[] = [
        //                 'store_id' => $item->store_id,
        //                 'store_name' => $item->store_name,
        //                 'promotion_in_offline' => $item->total_bonus_amount,
        //                 'transaction_date' => $item->transaction_date,
        //             ];
        //         }
        //     }
        // });

        $angpau = UserAngpau::join('users', 'users.id', '=', 'user_angpaus.user_id')
            ->join('store', 'store.store_id', '=', 'users.store_id')
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                'user_angpaus.user_id',
                DB::raw('SUM(user_angpaus.amount) as total_fudai'),
                DB::raw('DATE(user_angpaus.created_at) as transaction_date'),
            )
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('user_angpaus.created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where('user_angpaus.created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(isset($store_id), function ($q) use ($store_id) {
                return $q->where('users.store_id', $store_id);
            })
            ->where('user_angpaus.status', 1)
            ->where('user_angpaus.is_dummy', false)
            ->groupBy(DB::raw('DATE(user_angpaus.created_at)'), 'store.id', 'store.name', 'user_angpaus.user_id')
            ->orderBy('store.id', 'asc')
            ->get()->toArray();

        $luckySpin = RewardSpinLog::join('users', 'users.id', '=', 'reward_spin_logs.user_id')
            ->join('store', 'store.store_id', '=', 'users.store_id')
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                'reward_spin_logs.user_id',
                DB::raw('SUM(reward_spin_logs.value) as total_lucky_spin'),
                DB::raw('DATE(reward_spin_logs.created_at) as transaction_date'),
            )
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('reward_spin_logs.created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where('reward_spin_logs.created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(isset($store_id), function ($q) use ($store_id) {
                return $q->where('users.store_id', $store_id);
            })
            ->where('reward_spin_logs.reward_type', RewardSpin::$reward_type['cash'])
            ->where('reward_spin_logs.status', true)
            ->where('reward_spin_logs.is_dummy', false)
            ->groupBy(DB::raw('DATE(reward_spin_logs.created_at)'), 'store.id', 'store.name', 'reward_spin_logs.user_id')
            ->orderBy('store.id', 'asc')
            ->get()->toArray();

        $rebate = UserRebate::join('users', 'users.id', '=', 'user_rebates.user_id')
            ->join('store', 'store.store_id', '=', 'users.store_id')
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                'user_rebates.user_id',
                DB::raw('SUM(user_rebates.amount) as total_rebate'),
                DB::raw('DATE(user_rebates.created_at) as transaction_date'),
            )
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('user_rebates.created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where('user_rebates.created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(isset($store_id), function ($q) use ($store_id) {
                return $q->where('users.store_id', $store_id);
            })
            ->where('user_rebates.status', UserRebate::$status['approved'])
            ->groupBy(DB::raw('DATE(user_rebates.created_at)'), 'store.id', 'store.name', 'user_rebates.user_id')
            ->orderBy('store.id', 'asc')
            ->get()->toArray();

        $referralBonus = UserReferralBonus::join('users', 'users.id', '=', 'user_referral_bonuses.user_id')
            ->join('store', 'store.store_id', '=', 'users.store_id')
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                'user_referral_bonuses.user_id',
                DB::raw('SUM(user_referral_bonuses.amount) as total_referral_bonus'),
                DB::raw('DATE(user_referral_bonuses.created_at) as transaction_date'),
            )
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('user_referral_bonuses.created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where('user_referral_bonuses.created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(isset($store_id), function ($q) use ($store_id) {
                return $q->where('users.store_id', $store_id);
            })
            ->where('user_referral_bonuses.status', UserReferralBonus::$status['approved'])
            ->groupBy(DB::raw('DATE(user_referral_bonuses.created_at)'), 'store.id', 'store.name', 'user_referral_bonuses.user_id')
            ->orderBy('store.id', 'asc')
            ->get()->toArray();

        $list = array_merge($onlineGroupTotal, $onlinePromotion, $offlinePromotion, $luckySpin, $angpau, $rebate, $referralBonus);

        foreach ($list as $entry) {
            $key = $entry['store_id'].'|'.$entry['store_name'].'|'.$entry['transaction_date'];

            if (! isset($mergedOnline[$key])) {
                $mergedOnline[$key] = [
                    'store_id' => $entry['store_id'],
                    'store_name' => $entry['store_name'],
                    'transaction_date' => $entry['transaction_date'],
                    'promotion_in_online' => 0,
                    'transfer_in_online' => 0,
                    'transfer_out_online' => 0,
                    'promotion_in_offline' => 0,
                    'transfer_in_offline' => 0,
                    'transfer_out_offline' => 0,
                    'total_transfer_online' => 0,
                    'total_transfer_offline' => 0,
                    'total_profit_loss_online' => 0,
                    'total_profit_loss_offline' => 0,
                    'total_fudai' => 0,
                    'total_lucky_spin' => 0,
                    'total_rebate' => 0,
                    'total_referral_bonus' => 0,
                ];
            }

            if (isset($entry['promotion_in_online'])) {
                $mergedOnline[$key]['promotion_in_online'] += (float) $entry['promotion_in_online'];
            }

            if (isset($entry['transfer_in_online'])) {
                $mergedOnline[$key]['transfer_in_online'] += (float) $entry['transfer_in_online'];
                $mergedOnline[$key]['transfer_out_online'] += (float) $entry['transfer_out_online'];
            }

            if (isset($entry['promotion_in_offline'])) {
                $mergedOnline[$key]['promotion_in_offline'] += (float) $entry['promotion_in_offline'];
            }

            if (isset($entry['transfer_in_offline'])) {
                $mergedOnline[$key]['transfer_in_offline'] += (float) $entry['transfer_in_offline'];
                $mergedOnline[$key]['transfer_out_offline'] += (float) $entry['transfer_out_offline'];
            }

            if (isset($entry['total_fudai'])) {
                $mergedOnline[$key]['total_fudai'] += (float) $entry['total_fudai'];
            }

            if (isset($entry['total_lucky_spin'])) {
                $mergedOnline[$key]['total_lucky_spin'] += (float) $entry['total_lucky_spin'];
            }

            if (isset($entry['total_rebate'])) {
                $mergedOnline[$key]['total_rebate'] += (float) $entry['total_rebate'];
            }

            if (isset($entry['total_referral_bonus'])) {
                $mergedOnline[$key]['total_referral_bonus'] += (float) $entry['total_referral_bonus'];
            }

            $mergedOnline[$key]['total_transfer_in_online'] = (float) $mergedOnline[$key]['transfer_in_online'] + $mergedOnline[$key]['promotion_in_online'];
            $mergedOnline[$key]['total_transfer_in_offline'] = (float) $mergedOnline[$key]['transfer_in_offline'] + $mergedOnline[$key]['promotion_in_offline'];

            $mergedOnline[$key]['total_profit_loss_online'] = (float) $mergedOnline[$key]['total_transfer_in_online'] - $mergedOnline[$key]['transfer_out_online'];
            $mergedOnline[$key]['total_profit_loss_offline'] = (float) $mergedOnline[$key]['total_transfer_in_offline'] - $mergedOnline[$key]['transfer_out_offline'];
        }
        $results = array_values($mergedOnline);

        $data['list'] = $results;
        if (! isset($seeAll) || $seeAll == 0) {
            $data['table_total'] = $tableTotal;
            $data['pagination'] = [
                'current_page' => $page,
                'from' => $fromRow,
                'last_page' => $lastPage,
                'per_page' => (int) $limit,
                'to' => $toRow,
                'total' => $totalRow,
            ];
        }
        $data['meta'] = null;

        return (object) $data;
    }

    public static function getSummaryProfitLossMonthlyReport($params = [])
    {
        $product_id = $params['product_id'] ?? null;
        if (isset($params['from_date']) && isset($params['to_date'])) {
            $fromDate = $params['from_date'];
            $toDate = $params['to_date'];
        } else {
            $fromDate = now()->format('Y-m-d');
            $toDate = now()->format('Y-m-d');
        }

        if (isset($params['export']) && ($params['export'] == 1)) {
            $jobData = $params;
            $jobData['model'] = get_class() ?? null;
            $jobData['function'] = __FUNCTION__;
            $jobData['module'] = MODULE;
            $jobData['creator_type'] = MODULE;
            $jobData['creator_id'] = Auth::user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, 'Export Successfully');
        }

        // Pagination
        $page = 1;
        $limit = 0;
        $fromRow = 0;
        $toRow = INF;
        $lastPage = 1;
        $tableTotal = 0;
        if (! isset($seeAll) || $seeAll == 0) {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? config('app.pagination_rows');
            $fromDateTS = strtotime(date('Y-m-d 00:00:00', strtotime($fromDate)));
            $toDateTS = strtotime(date('Y-m-d 23:59:59', strtotime($toDate)));
            $totalRow = ($toDateTS - $fromDateTS) / (24 * 60 * 60) + 1;
            $fromRow = (($page - 1) * $limit) + 1;
            $toRow = $page * $limit;
            $lastPage = ceil(($totalRow / $limit));
            if ($page == $lastPage) {
                $toRow = $totalRow;
            }
        }

        $onlineGroupTotal = ExTransfer::join('users', 'users.id', '=', 'ex_transfer.user_id')
            ->join('store', 'store.store_id', '=', 'users.store_id')
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                DB::raw('CASE WHEN ex_transfer.type = 1 AND ex_transfer.machine_id IS NULL THEN SUM(ex_transfer.amount) ELSE 0 END as transfer_in_online'),
                DB::raw('CASE WHEN ex_transfer.type = 1 AND ex_transfer.machine_id IS NOT NULL THEN SUM(ex_transfer.amount) ELSE 0 END as transfer_in_offline'),
                DB::raw('CASE WHEN ex_transfer.type = 2 AND ex_transfer.machine_id IS NULL THEN SUM(ex_transfer.amount) ELSE 0 END as transfer_out_online'),
                DB::raw('CASE WHEN ex_transfer.type = 2 AND ex_transfer.machine_id IS NOT NULL THEN SUM(ex_transfer.amount) ELSE 0 END as transfer_out_offline'),
                DB::raw('DATE_FORMAT(ex_transfer.created_at, "%Y-%m") AS transaction_date'),
            )
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('ex_transfer.created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where('ex_transfer.created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->whereNull('card_id')
            ->where('ex_transfer.receivable_amount', '>', 0)
            ->where('ex_transfer.status', self::$status['confirmed'])
            ->whereNotIn('ex_transfer.user_id', ['1001200', '1001201', '1001202', '1001242', '1001243', '1001244', '1001262', '1001258', '1001259', '1001260', '1000021'])
            ->when(isset($product_id), function ($q) use ($product_id) {
                $q->where('ex_transfer.product_id', $product_id);
            })
            ->groupBy(DB::raw('DATE_FORMAT(ex_transfer.created_at, "%Y-%m")'), 'ex_transfer.type', 'ex_transfer.machine_id', 'store.id', 'store.name')
            ->orderBy('store.id', 'asc')
            ->get()->toArray();

        $mergedOnline = [];

        // $promotions = UserPromotion::join('users', 'users.id', '=', 'user_promotions.user_id')
        //     ->join('store', 'store.store_id', '=', 'users.store_id')
        //     ->join('promotions', 'promotions.id', '=', 'user_promotions.promotion_id')
        //     ->select(
        //         'store.id as store_id',
        //         'store.name as store_name',
        //         'user_promotions.user_id',
        //         DB::raw('SUM(user_promotions.bonus_amount) as total_bonus_amount'),
        //         DB::raw('DATE_FORMAT(user_promotions.created_at, "%Y-%m") AS transaction_date'),
        //     )
        //     ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
        //         $q->where('user_promotions.created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

        //         return $q->where('user_promotions.created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
        //     })
        //     ->where('promotions.product_id', $product_id)
        //     ->groupBy(DB::raw('DATE_FORMAT(user_promotions.created_at, "%Y-%m")'), 'store.id', 'store.name', 'user_promotions.user_id')
        //     ->orderBy('store.id', 'asc')
        //     ->get();

        $online_user_ids = [];
        if ($fromDate < '2025-02-19' && $toDate < '2025-02-19') {
            $online_user_ids = RewardSpinLog::select('user_id')->where('reward_type', 4)
                ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                    $q->where('created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                    return $q->where('created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
                })->pluck('user_id')->toArray();
        }

        $onlinePromotion = UserPromotion::join('users', 'users.id', '=', 'user_promotions.user_id')
            ->join('store', 'store.store_id', '=', 'users.store_id')
            ->join('promotions', 'promotions.id', '=', 'user_promotions.promotion_id')
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                'user_promotions.user_id',
                'user_promotions.claim_type',
                DB::raw('SUM(user_promotions.bonus_amount) as promotion_in_online'),
                DB::raw('DATE_FORMAT(user_promotions.created_at, "%Y-%m") AS transaction_date'),
            )
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('user_promotions.created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where('user_promotions.created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(isset($product_id), function ($q) use ($product_id) {
                $q->where('promotions.product_id', $product_id);
            })
            ->where(function ($q) use ($online_user_ids) {
                if (! empty($online_user_ids)) {
                    $q->whereIn('user_promotions.user_id', $online_user_ids);
                } else {
                    $q->where('user_promotions.claim_type', '!=', UserPromotion::$claimType['offline']);
                }
            })
            ->groupBy(DB::raw('DATE_FORMAT(user_promotions.created_at, "%Y-%m")'), 'store.id', 'store.name', 'user_promotions.user_id', 'user_promotions.claim_type')
            ->orderBy('store.id', 'asc')
            ->get()->toArray();

        $offlinePromotion = UserPromotion::join('users', 'users.id', '=', 'user_promotions.user_id')
            ->join('store', 'store.store_id', '=', 'users.store_id')
            ->join('promotions', 'promotions.id', '=', 'user_promotions.promotion_id')
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                'user_promotions.user_id',
                'user_promotions.claim_type',
                DB::raw('SUM(user_promotions.bonus_amount) as promotion_in_offline'),
                DB::raw('DATE_FORMAT(user_promotions.created_at, "%Y-%m") AS transaction_date'),
            )
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('user_promotions.created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where('user_promotions.created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(isset($product_id), function ($q) use ($product_id) {
                $q->where('promotions.product_id', $product_id);
            })
            ->where(function ($q) use ($online_user_ids) {
                if (! empty($online_user_ids)) {
                    $q->whereNotIn('user_promotions.user_id', $online_user_ids);
                } else {
                    $q->where('user_promotions.claim_type', '=', UserPromotion::$claimType['offline']);
                }
            })
            ->groupBy(DB::raw('DATE_FORMAT(user_promotions.created_at, "%Y-%m")'), 'store.id', 'store.name', 'user_promotions.user_id', 'user_promotions.claim_type')
            ->orderBy('store.id', 'asc')
            ->get()->toArray();

        $list = array_merge($onlineGroupTotal, $onlinePromotion, $offlinePromotion);

        foreach ($list as $entry) {
            $key = $entry['store_id'].'|'.$entry['store_name'].'|'.$entry['transaction_date'];

            if (! isset($mergedOnline[$key])) {
                $mergedOnline[$key] = [
                    'store_id' => $entry['store_id'],
                    'store_name' => $entry['store_name'],
                    'transaction_date' => $entry['transaction_date'],
                    'promotion_in_online' => 0,
                    'transfer_in_online' => 0,
                    'total_transfer_in_online' => 0,
                    'transfer_out_online' => 0,
                    'total_profit_loss_online' => 0,
                    'promotion_in_offline' => 0,
                    'transfer_in_offline' => 0,
                    'total_transfer_in_offline' => 0,
                    'transfer_out_offline' => 0,
                    'total_profit_loss_offline' => 0,
                ];
            }

            if (isset($entry['promotion_in_online'])) {
                $mergedOnline[$key]['promotion_in_online'] += (float) $entry['promotion_in_online'];
            }

            if (isset($entry['transfer_in_online'])) {
                $mergedOnline[$key]['transfer_in_online'] += (float) $entry['transfer_in_online'];
                $mergedOnline[$key]['transfer_out_online'] += (float) $entry['transfer_out_online'];
            }

            if (isset($entry['promotion_in_offline'])) {
                $mergedOnline[$key]['promotion_in_offline'] += (float) $entry['promotion_in_offline'];
            }

            if (isset($entry['transfer_in_offline'])) {
                $mergedOnline[$key]['transfer_in_offline'] += (float) $entry['transfer_in_offline'];
                $mergedOnline[$key]['transfer_out_offline'] += (float) $entry['transfer_out_offline'];
            }

            $mergedOnline[$key]['total_transfer_in_online'] = (float) $mergedOnline[$key]['transfer_in_online'] + $mergedOnline[$key]['promotion_in_online'];
            $mergedOnline[$key]['total_transfer_in_offline'] = (float) $mergedOnline[$key]['transfer_in_offline'] + $mergedOnline[$key]['promotion_in_offline'];

            $mergedOnline[$key]['total_profit_loss_online'] = (float) $mergedOnline[$key]['total_transfer_in_online'] - $mergedOnline[$key]['transfer_out_online'];
            $mergedOnline[$key]['total_profit_loss_offline'] = (float) $mergedOnline[$key]['total_transfer_in_offline'] - $mergedOnline[$key]['transfer_out_offline'];
        }
        $results = array_values($mergedOnline);

        $data['list'] = $results;
        if (! isset($seeAll) || $seeAll == 0) {
            $data['table_total'] = $tableTotal;
            $data['pagination'] = [
                'current_page' => $page,
                'from' => $fromRow,
                'last_page' => $lastPage,
                'per_page' => (int) $limit,
                'to' => $toRow,
                'total' => $totalRow,
            ];
        }
        $data['meta'] = null;

        return (object) $data;
    }

    public static function getStoreSummaryProfitLossReport($params = [])
    {
        $excludeDummy = isset($params['is_exclude_dummy']) ? $params['is_exclude_dummy'] : false;
        $product_id = $params['product_id'] ?? null;
        if (isset($params['from_date']) && isset($params['to_date'])) {
            $fromDate = $params['from_date'];
            $toDate = $params['to_date'];
        } else {
            $fromDate = now()->format('Y-m-d');
            $toDate = now()->format('Y-m-d');
        }

        if (isset($params['export']) && ($params['export'] == 1)) {
            $jobData = $params;
            $jobData['model'] = get_class() ?? null;
            $jobData['function'] = __FUNCTION__;
            $jobData['module'] = MODULE;
            $jobData['creator_type'] = MODULE;
            $jobData['creator_id'] = Auth::user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, 'Export Successfully');
        }

        // Pagination
        $page = 1;
        $limit = 0;
        $fromRow = 0;
        $toRow = INF;
        $lastPage = 1;
        $tableTotal = 0;

        $store_ids = isset($params['store_ids']) ? json_encode($params['store_ids']) : AdminDetail::where('admin_id', auth()->user()->id)->first()->value;
        $store_id = isset($params['store_id']) ? $params['store_id'] : null;

        if (! isset($seeAll) || $seeAll == 0) {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? config('app.pagination_rows');
            $fromDateTS = strtotime(date('Y-m-d 00:00:00', strtotime($fromDate)));
            $toDateTS = strtotime(date('Y-m-d 23:59:59', strtotime($toDate)));
            $totalRow = ($toDateTS - $fromDateTS) / (24 * 60 * 60) + 1;
            $fromRow = (($page - 1) * $limit) + 1;
            $toRow = $page * $limit;
            $lastPage = ceil(($totalRow / $limit));
            if ($page == $lastPage) {
                $toRow = $totalRow;
            }
        }

        $onlineGroupTotal = ExTransfer::join('users', 'users.id', '=', 'ex_transfer.user_id')
            ->join('store', 'store.store_id', '=', 'users.store_id')
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                DB::raw('CASE WHEN ex_transfer.type = 1 AND ex_transfer.machine_id IS NULL THEN SUM(ex_transfer.amount) ELSE 0 END as transfer_in_online'),
                DB::raw('CASE WHEN ex_transfer.type = 1 AND ex_transfer.machine_id IS NOT NULL THEN SUM(ex_transfer.amount) ELSE 0 END as transfer_in_offline'),
                DB::raw('CASE WHEN ex_transfer.type = 2 AND ex_transfer.machine_id IS NULL THEN SUM(ex_transfer.amount) ELSE 0 END as transfer_out_online'),
                DB::raw('CASE WHEN ex_transfer.type = 2 AND ex_transfer.machine_id IS NOT NULL THEN SUM(ex_transfer.amount) ELSE 0 END as transfer_out_offline'),
                DB::raw('DATE(ex_transfer.created_at) AS transaction_date'),
            )
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('ex_transfer.created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where('ex_transfer.created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->whereNull('card_id')
            ->where('ex_transfer.receivable_amount', '>', 0)
            ->where('ex_transfer.status', self::$status['confirmed'])
            ->whereNotIn('ex_transfer.user_id', ['1001200', '1001201', '1001202', '1001242', '1001243', '1001244', '1001262', '1001258', '1001259', '1001260', '1000021'])
            ->when(! isset($store_id), function ($q) use ($store_ids) {
                $q->whereIn('store.id', json_decode($store_ids));
            })
            ->when(isset($store_id), function ($q) use ($store_id) {
                $q->where('store.id', $store_id);
            })
            ->when(isset($product_id), function ($q) use ($product_id) {
                $q->where('ex_transfer.product_id', $product_id);
            })
            ->when($excludeDummy, function ($q) {
                $q->where('users.is_dummy', false);
            })
            ->groupBy(DB::raw('DATE(ex_transfer.created_at)'), 'ex_transfer.type', 'ex_transfer.machine_id', 'store.id', 'store.name')
            ->orderBy('store.id', 'asc')
            ->get()->toArray();

        $mergedOnline = [];

        $game_active_users = ExTransfer::join('users', 'users.id', '=', 'ex_transfer.user_id')
            ->join('store', 'store.store_id', '=', 'users.store_id')
            ->where('ex_transfer.status', self::$status['confirmed'])
            ->whereNull('card_id')
            ->where('receivable_amount', '>', 0)
            ->where('type', ExTransfer::$type['in'])
            ->when(! isset($store_id), function ($q) use ($store_ids) {
                $q->whereIn('store.id', json_decode($store_ids));
            })
            ->when(isset($store_id), function ($q) use ($store_id) {
                $q->where('store.id', $store_id);
            })
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('ex_transfer.created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where('ex_transfer.created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(isset($product_id), function ($q) use ($product_id) {
                $q->where('ex_transfer.product_id', $product_id);
            })
            ->when($excludeDummy, function ($q) {
                $q->where('users.is_dummy', false);
            })
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                DB::raw('COUNT(DISTINCT(ex_transfer.user_id)) as game_active_user'),
                DB::raw('DATE(ex_transfer.created_at) as transaction_date')
            )
            ->groupBy(DB::raw('DATE(ex_transfer.created_at)'), 'store.id', 'store.name')
            ->orderBy('store.id', 'asc')
            ->get()
            ->toArray();

        $active_users = Deposit::join('users', 'users.id', '=', 'deposit.user_id')
            ->join('store', 'store.store_id', '=', 'users.store_id')
            ->where('deposit.status', Deposit::$status['approved'])
            ->when(! isset($store_id), function ($q) use ($store_ids) {
                $q->whereIn('store.id', json_decode($store_ids));
            })
            ->when(isset($store_id), function ($q) use ($store_id) {
                $q->where('store.id', $store_id);
            })
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('approved_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where('approved_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(isset($store_id), function ($q) use ($store_id) {
                $q->where('store.id', $store_id);
            })
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                DB::raw('COUNT(DISTINCT(deposit.user_id)) as deposit_active_user'),
                DB::raw('DATE(deposit.approved_at) as transaction_date')
            )
            ->groupBy(DB::raw('DATE(deposit.approved_at)'), 'store.id', 'store.name')
            ->orderBy('store.id', 'asc')
            ->get()
            ->toArray();

        $list = array_merge($onlineGroupTotal, $active_users, $game_active_users);

        foreach ($list as $entry) {
            $key = $entry['store_id'].'|'.$entry['store_name'].'|'.$entry['transaction_date'];

            if (! isset($mergedOnline[$key])) {
                $mergedOnline[$key] = [
                    'store_id' => $entry['store_id'],
                    'store_name' => $entry['store_name'],
                    'transaction_date' => $entry['transaction_date'],
                    'deposit_active_user' => 0,
                    'game_active_user' => 0,
                    'transfer_in_online' => 0,
                    'transfer_out_online' => 0,
                    'transfer_in_offline' => 0,
                    'transfer_out_offline' => 0,
                    'total_profit_loss_online' => 0,
                    'total_profit_loss_offline' => 0,
                ];
            }

            if (isset($entry['deposit_active_user'])) {
                $mergedOnline[$key]['deposit_active_user'] += (float) $entry['deposit_active_user'];
            }

            if (isset($entry['game_active_user'])) {
                $mergedOnline[$key]['game_active_user'] += (float) $entry['game_active_user'];
            }

            if (isset($entry['transfer_in_online'])) {
                $mergedOnline[$key]['transfer_in_online'] += (float) $entry['transfer_in_online'];
                $mergedOnline[$key]['transfer_out_online'] += (float) $entry['transfer_out_online'];
            }

            if (isset($entry['transfer_in_offline'])) {
                $mergedOnline[$key]['transfer_in_offline'] += (float) $entry['transfer_in_offline'];
                $mergedOnline[$key]['transfer_out_offline'] += (float) $entry['transfer_out_offline'];
            }

            $mergedOnline[$key]['total_profit_loss_online'] = (float) $mergedOnline[$key]['transfer_in_online'] - $mergedOnline[$key]['transfer_out_online'];
            $mergedOnline[$key]['total_profit_loss_offline'] = (float) $mergedOnline[$key]['transfer_in_offline'] - $mergedOnline[$key]['transfer_out_offline'];
        }
        $results = array_values($mergedOnline);

        $data['list'] = $results;
        if (! isset($seeAll) || $seeAll == 0) {
            $data['table_total'] = $tableTotal;
            $data['pagination'] = [
                'current_page' => $page,
                'from' => $fromRow,
                'last_page' => $lastPage,
                'per_page' => (int) $limit,
                'to' => $toRow,
                'total' => $totalRow,
            ];
        }
        $data['meta'] = null;

        return (object) $data;
    }

    /*
    public static function getStoreSummaryProfitLossReport($params = [])
    {
        if (isset($params['from_date']) && isset($params['to_date'])) {
            $fromDate = $params['from_date'];
            $toDate = $params['to_date'];
        } else {
            $fromDate = now()->format('Y-m-d');
            $toDate = now()->format('Y-m-d');
        }

        if (isset($params['export']) && ($params['export'] == 1)) {
            $jobData = $params;
            $jobData['model'] = get_class() ?? null;
            $jobData['function'] = __FUNCTION__;
            $jobData['module'] = MODULE;
            $jobData['creator_type'] = MODULE;
            $jobData['creator_id'] = Auth::user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, 'Export Successfully');
        }

        // Pagination
        $page = 1;
        $limit = 0;
        $fromRow = 0;
        $toRow = INF;
        $lastPage = 1;
        $tableTotal = 0;
        if (! isset($seeAll) || $seeAll == 0) {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? config('app.pagination_rows');
            $fromDateTS = strtotime(date('Y-m-d 00:00:00', strtotime($fromDate)));
            $toDateTS = strtotime(date('Y-m-d 23:59:59', strtotime($toDate)));
            $totalRow = ($toDateTS - $fromDateTS) / (24 * 60 * 60) + 1;
            $fromRow = (($page - 1) * $limit) + 1;
            $toRow = $page * $limit;
            $lastPage = ceil(($totalRow / $limit));
            if ($page == $lastPage) {
                $toRow = $totalRow;
            }
        }

        $onlineGroupTotal = ExTransfer::join('users', 'users.id', '=', 'ex_transfer.user_id')
            ->join('store', 'store.store_id', '=', 'users.store_id')
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                DB::raw('CASE WHEN ex_transfer.type = 1 AND ex_transfer.machine_id IS NULL THEN SUM(ex_transfer.amount) ELSE 0 END as transfer_in_online'),
                DB::raw('CASE WHEN ex_transfer.type = 1 AND ex_transfer.machine_id IS NOT NULL THEN SUM(ex_transfer.amount) ELSE 0 END as transfer_in_offline'),
                DB::raw('CASE WHEN ex_transfer.type = 2 AND ex_transfer.machine_id IS NULL THEN SUM(ex_transfer.amount) ELSE 0 END as transfer_out_online'),
                DB::raw('CASE WHEN ex_transfer.type = 2 AND ex_transfer.machine_id IS NOT NULL THEN SUM(ex_transfer.amount) ELSE 0 END as transfer_out_offline'),
                DB::raw('DATE(ex_transfer.created_at) AS transaction_date'),
            )
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('ex_transfer.created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where('ex_transfer.created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->whereNull('card_id')
            ->where('ex_transfer.receivable_amount', '>', 0)
            ->where('ex_transfer.status', self::$status['confirmed'])
            ->whereNotIn('ex_transfer.user_id', ['1001200', '1001201', '1001202', '1001242', '1001243', '1001244', '1001262', '1001258', '1001259', '1001260', '1000021'])
            ->where('ex_transfer.product_id', 2004)
            ->groupBy(DB::raw('DATE(ex_transfer.created_at)'), 'ex_transfer.type', 'ex_transfer.machine_id', 'store.id', 'store.name')
            ->orderBy('store.id', 'asc')
            ->get()->toArray();

        $mergedOnline = [];

        $promotions = UserPromotion::join('users', 'users.id', '=', 'user_promotions.user_id')
            ->join('store', 'store.store_id', '=', 'users.store_id')
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                'user_promotions.user_id',
                DB::raw('SUM(user_promotions.bonus_amount) as total_bonus_amount'),
                DB::raw('DATE(user_promotions.created_at) AS transaction_date'),
            )
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('user_promotions.created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where('user_promotions.created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->groupBy(DB::raw('DATE(user_promotions.created_at)'), 'store.id', 'store.name', 'user_promotions.user_id')
            ->orderBy('store.id', 'asc')
            ->get();

        $online_user_ids = RewardSpinLog::select('user_id')->where('reward_type', 4)
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where('created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })->pluck('user_id')->toArray();

        $onlinePromotion = [];
        $offlinePromotion = [];
        $promotions->each(function ($item) use ($online_user_ids, &$onlinePromotion, &$offlinePromotion) {
            if (in_array($item->user_id, $online_user_ids)) {
                $onlinePromotion[] = [
                    'store_id' => $item->store_id,
                    'store_name' => $item->store_name,
                    'total_bonus_amount_online' => $item->total_bonus_amount,
                    'transaction_date' => $item->transaction_date,
                ];
            } else {
                $offlinePromotion[] = [
                    'store_id' => $item->store_id,
                    'store_name' => $item->store_name,
                    'total_bonus_amount_offline' => $item->total_bonus_amount,
                    'transaction_date' => $item->transaction_date,
                ];
            }
        });

        $list = array_merge($onlineGroupTotal, $onlinePromotion, $offlinePromotion);

        foreach ($list as $entry) {
            $key = $entry['store_id'] . '|' . $entry['store_name'] . '|' . $entry['transaction_date'];

            if (! isset($mergedOnline[$key])) {
                $mergedOnline[$key] = [
                    'store_id' => $entry['store_id'],
                    'store_name' => $entry['store_name'],
                    'transaction_date' => $entry['transaction_date'],
                    'transfer_in_online' => 0,
                    'transfer_out_online' => 0,
                    'transfer_in_offline' => 0,
                    'transfer_out_offline' => 0,
                    'total_transfer_online' => 0,
                    'total_transfer_offline' => 0,
                    'total_bonus_amount_online' => 0,
                    'total_bonus_amount_offline' => 0,
                ];
            }

            if (isset($entry['promotion_in_online'])) {
                $mergedOnline[$key]['promotion_in_online'] += (float) $entry['promotion_in_online'];
            }

            if (isset($entry['transfer_in_online'])) {
                $mergedOnline[$key]['transfer_in_online'] += (float) $entry['transfer_in_online'];
                $mergedOnline[$key]['transfer_out_online'] += (float) $entry['transfer_out_online'];
            }

            if (isset($entry['promotion_in_offline'])) {
                $mergedOnline[$key]['promotion_in_offline'] += (float) $entry['promotion_in_offline'];
            }

            if (isset($entry['transfer_in_offline'])) {
                $mergedOnline[$key]['transfer_in_offline'] += (float) $entry['transfer_in_offline'];
                $mergedOnline[$key]['transfer_out_offline'] += (float) $entry['transfer_out_offline'];
            }

            $mergedOnline[$key]['total_transfer_in_online'] = (float) $mergedOnline[$key]['transfer_in_online'] + $mergedOnline[$key]['promotion_in_online'];
            $mergedOnline[$key]['total_transfer_in_offline'] = (float) $mergedOnline[$key]['transfer_in_offline'] + $mergedOnline[$key]['promotion_in_offline'];

            $mergedOnline[$key]['total_profit_loss_online'] = (float) $mergedOnline[$key]['total_transfer_in_online'] - $mergedOnline[$key]['transfer_out_online'];
            $mergedOnline[$key]['total_profit_loss_offline'] = (float) $mergedOnline[$key]['total_transfer_in_offline'] - $mergedOnline[$key]['transfer_out_offline'];
        }
        $results = array_values($mergedOnline);

        $data['list'] = $results;
        if (! isset($seeAll) || $seeAll == 0) {
            $data['table_total'] = $tableTotal;
            $data['pagination'] = [
                'current_page' => $page,
                'from' => $fromRow,
                'last_page' => $lastPage,
                'per_page' => (int) $limit,
                'to' => $toRow,
                'total' => $totalRow,
            ];
        }
        $data['meta'] = null;

        return (object) $data;
    }

    public static function getSummaryProfitLossByStoreReport($params = [])
    {
        if (!isset($params['from_date']) || empty($params['from_date'])) {
            return [];
        }

        if (!isset($params['from_date']) || empty($params['from_date'])) {
            $firstUser = User::whereIn('user_type', [User::$userType['user-account']])->first();
            $fromDate = date("Y-m-d", strtotime($firstUser->created_at));
        } else {
            $fromDate = $params['from_date'];
        }

        if (!isset($params['to_date']) || empty($params['to_date'])) {
            $toDate = date("Y-m-d");
        } else {
            $toDate = $params['to_date'];
        }

        $username = $params['username'] ?? null;
        if (isset($params['export']) && ($params['export'] == 1)) {
            $params["model"] = get_class() ?? null;
            $params["function"] = __FUNCTION__;
            ExportReport::exportReport($params);
        }

        $fromDateFilter = $fromDate;
        $toDateFilter = $toDate;

        $seeAll = $params['see_all'] ?? null;
        $startDate = $toDate;
        $dateRange = [];
        $tableTotal = [];
        $leaderDownlines = null;
        $storeId = $params['store_id'] ?? null;

        if (isset($params['leader_username']) && !empty($params['leader_username'])) {
            $leaderUser = User::where('username', $params['leader_username'])->first();
            if (isset($leaderUser) && !empty($leaderUser)) {
                $leaderTree = $leaderUser->treeSponsor;
                $donwlines = TreeSponsor::where("trace_key", 'LIKE', $leaderTree->trace_key . "%")->pluck('user_id', 'id');
                if (!empty($donwlines)) {
                    $leaderDownlines = $donwlines->toArray();
                }
            }

            if (empty($donwlines)) {
                $data['list'] = $dateRange;
                if (!isset($seeAll) || $seeAll == 0) {
                    $data['pagination'] = array(
                        "current_page" => 1,
                        "from" => null,
                        "last_page" => 1,
                        "per_page" => (int) ($params['limit'] ?? config('app.pagination_rows')),
                        "to" => null,
                        "total" => 0
                    );
                }
                $data['meta'] = null;

                return (object) $data;
            }
        }

        $memberID = $params['member_id'] ?? null;
        $username = $params['phone_no'] ?? null;

        // Manual pagination
        $page = 1;
        $limit = 0;
        $fromRow = 0;
        $toRow = INF;
        $lastPage = 1;
        if (!isset($seeAll) || $seeAll == 0) {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? config('app.pagination_rows');
            $fromDateTS = strtotime(date("Y-m-d 00:00:00", strtotime($fromDate)));
            $toDateTS = strtotime(date("Y-m-d 00:00:00", strtotime($toDate)));
            $totalRow = ($toDateTS - $fromDateTS) / (24 * 60 * 60) + 1;
            $fromRow = (($page - 1) * $limit) + 1;
            $toRow = $page * $limit;
            $lastPage = ceil(($totalRow / $limit));
            if ($page == $lastPage) {
                $toRow = $totalRow;
            }
        }

        $totalCount = 0;
        $initTable = [
            "active_user" => 0,
            "telex_transfer" => DecimalTrait::setDecimal(0),
            "transfer" => DecimalTrait::setDecimal(0),
            "wallet_to_card" => DecimalTrait::setDecimal(0),
            "card_to_wallet" => DecimalTrait::setDecimal(0),
            "withdrawal" => DecimalTrait::setDecimal(0),
            "deposit" => [
                'manual-bank' => DecimalTrait::setDecimal(0),
                'online-bank' => DecimalTrait::setDecimal(0),
                'onepay-online-bank' => DecimalTrait::setDecimal(0),
                'crypto' => DecimalTrait::setDecimal(0),
                'ewallet' => DecimalTrait::setDecimal(0),
                'onepay-ewallet' => DecimalTrait::setDecimal(0),
                'total' => DecimalTrait::setDecimal(0),
            ]
        ];

        $summary = $tableTotal = $initTable;
        while (strtotime($startDate) >= strtotime($fromDate)) {
            if (strtotime($startDate) > time()) {
                $startDate = date("Y-m-d", strtotime($startDate . " -1 day"));
                $toDate = $startDate;
                continue;
            }

            $totalCount++;
            if ($totalCount < $fromRow) {
                $startDate = date("Y-m-d", strtotime($startDate . " -1 day"));
                $toDate = $startDate;
                continue;
            }

            $dateRange[$startDate]['date'] = Traits\DateTrait::dateFormat($startDate, false);
            $dateRange[$startDate] = $dateRange[$startDate] + $initTable;
            if ($totalCount >= $toRow) {
                $fromDate = $startDate;
                break;
            }
            $startDate = date("Y-m-d", strtotime($startDate . " -1 day"));
        }

        // Deposit
        $depositRow = Deposit::with('userDetail')->where('status', Deposit::$status['approved'])
            ->whereRelation('userDetail.store', 'id', '!=', 1)
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('userDetail.store', 'id', $storeId);
            });
        $summaryDeposit = clone $depositRow;
        $depositRow = $depositRow->where("status", Deposit::$status['approved'])
            ->where("approved_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDate)))
            ->where("approved_at", "<=", date("Y-m-d 23:59:59", strtotime($toDate)))
            ->groupBy("user_id", "approved_at")
            ->selectRaw('COUNT(user_id) AS total_user, DATE(approved_at) AS approved_at')
            ->get();

        foreach ($depositRow as $deposit) {
            $joinDate = date("Y-m-d", strtotime($deposit->approved_at));
            $dateRange[$joinDate]['active_user'] = DecimalTrait::setDecimal(($deposit->total_user ?? 0));
            $tableTotal['active_user'] = DecimalTrait::setDecimal($tableTotal['active_user'] + ($deposit->total_user ?? 0));
        }

        $totalDeposit = $summaryDeposit->selectRaw("COUNT(user_id) as total_user")
            ->where("approved_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDateFilter)))
            ->where("approved_at", "<=", date("Y-m-d 23:59:59", strtotime($toDateFilter)))
            ->groupBy("user_id")
            ->get();
        foreach ($totalDeposit as $tDeposit) {
            $summary['active_user'] = DecimalTrait::setDecimal(($summary['active_user']['total'] + $tDeposit->total_user));
        }
        // END - Deposit

        // Online (Transfer In)
        $exTranferInRow = ExTransfer::with('user')->where('status', ExTransfer::$status['confirmed'])
            ->where('type', ExTransfer::$type['in'])
            ->whereNotNull('product_id')
            ->whereRelation('user.store', 'id', '!=', 1)
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('user.store', 'id', $storeId);
            });
        $summaryExTransferIn = clone $exTranferInRow;
        $exTranferInRow = $exTranferInRow->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDate)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDate)))
            ->get();

        foreach ($exTranferInRow as $row) {
            $withdrawalDate = date("Y-m-d", strtotime($row->created_at));
            $dateRange[$withdrawalDate]['card_to_wallet'] = DecimalTrait::setDecimal($dateRange[$withdrawalDate]['card_to_wallet'] + ($row->amount ?? 0));
            $tableTotal['card_to_wallet'] = DecimalTrait::setDecimal($tableTotal['card_to_wallet'] + ($row->amount ?? 0));
        }
        $totalExTransferIn = $summaryExTransferIn->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDateFilter)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDateFilter)))
            ->sum('amount');
        $summary['card_to_wallet'] = DecimalTrait::setDecimal($totalExTransferIn);

        // Online (Transfer Out)
        $exTranferOutRow = ExTransfer::with('user')->where('status', ExTransfer::$status['confirmed'])
            ->where('type', ExTransfer::$type['in'])
            ->whereNotNull('card_id')->whereNull('product_id')
            ->whereRelation('user.store', 'id', '!=', 1)
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('user.store', 'id', $storeId);
            })
            ->when(isset($memberID), function ($query) use ($memberID) {
                $query->whereRelation('user', 'member_id', '=', $memberID);
            })
            ->when(isset($username), function ($query) use ($username) {
                $query->whereRelation('user', 'username', '=', $username);
            })
            ->when($leaderDownlines, function ($query) use ($leaderDownlines) {
                $query->whereIn('user_id', $leaderDownlines);
            });
        $summaryExTransferOut = clone $exTranferOutRow;
        $exTranferOutRow = $exTranferOutRow->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDate)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDate)))
            ->get();

        foreach ($exTranferOutRow as $row) {
            $withdrawalDate = date("Y-m-d", strtotime($row->created_at));
            $dateRange[$withdrawalDate]['wallet_to_card'] = DecimalTrait::setDecimal($dateRange[$withdrawalDate]['wallet_to_card'] + ($row->amount ?? 0));
            $tableTotal['wallet_to_card'] = DecimalTrait::setDecimal($tableTotal['wallet_to_card'] + ($row->amount ?? 0));
        }
        $totalExTransferOut = $summaryExTransferOut->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDateFilter)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDateFilter)))
            ->sum('amount');
        $summary['wallet_to_card'] = DecimalTrait::setDecimal($totalExTransferOut);


        // Card - Card To Wallet (Transfer In)
        $exTranferInRow = ExTransfer::with('user')->where('status', ExTransfer::$status['confirmed'])
            ->where('type', ExTransfer::$type['out'])
            ->whereNotNull('card_id')->whereNull('product_id')
            ->whereRelation('user.store', 'id', '!=', 1)
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('user.store', 'id', $storeId);
            })
            ->when(isset($memberID), function ($query) use ($memberID) {
                $query->whereRelation('user', 'member_id', '=', $memberID);
            })
            ->when(isset($username), function ($query) use ($username) {
                $query->whereRelation('user', 'username', '=', $username);
            })
            ->when($leaderDownlines, function ($query) use ($leaderDownlines) {
                $query->whereIn('user_id', $leaderDownlines);
            });
        $summaryExTransferIn = clone $exTranferInRow;
        $exTranferInRow = $exTranferInRow->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDate)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDate)))
            ->get();

        foreach ($exTranferInRow as $row) {
            $withdrawalDate = date("Y-m-d", strtotime($row->created_at));
            $dateRange[$withdrawalDate]['card_to_wallet'] = DecimalTrait::setDecimal($dateRange[$withdrawalDate]['card_to_wallet'] + ($row->amount ?? 0));
            $tableTotal['card_to_wallet'] = DecimalTrait::setDecimal($tableTotal['card_to_wallet'] + ($row->amount ?? 0));
        }
        $totalExTransferIn = $summaryExTransferIn->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDateFilter)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDateFilter)))
            ->sum('amount');
        $summary['card_to_wallet'] = DecimalTrait::setDecimal($totalExTransferIn);

        // Card - Wallet To Card (Transfer Out)
        $exTranferOutRow = ExTransfer::with('user')->where('status', ExTransfer::$status['confirmed'])
            ->where('type', ExTransfer::$type['in'])
            ->whereNotNull('card_id')->whereNull('product_id')
            ->whereRelation('user.store', 'id', '!=', 1)
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('user.store', 'id', $storeId);
            })
            ->when(isset($memberID), function ($query) use ($memberID) {
                $query->whereRelation('user', 'member_id', '=', $memberID);
            })
            ->when(isset($username), function ($query) use ($username) {
                $query->whereRelation('user', 'username', '=', $username);
            })
            ->when($leaderDownlines, function ($query) use ($leaderDownlines) {
                $query->whereIn('user_id', $leaderDownlines);
            });
        $summaryExTransferOut = clone $exTranferOutRow;
        $exTranferOutRow = $exTranferOutRow->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDate)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDate)))
            ->get();

        foreach ($exTranferOutRow as $row) {
            $withdrawalDate = date("Y-m-d", strtotime($row->created_at));
            $dateRange[$withdrawalDate]['wallet_to_card'] = DecimalTrait::setDecimal($dateRange[$withdrawalDate]['wallet_to_card'] + ($row->amount ?? 0));
            $tableTotal['wallet_to_card'] = DecimalTrait::setDecimal($tableTotal['wallet_to_card'] + ($row->amount ?? 0));
        }
        $totalExTransferOut = $summaryExTransferOut->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDateFilter)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDateFilter)))
            ->sum('amount');
        $summary['wallet_to_card'] = DecimalTrait::setDecimal($totalExTransferOut);

        $data['list'] = array_values($dateRange);
        if (!isset($seeAll) || $seeAll == 0) {
            $data['table_total'] = $tableTotal;
            $data['summary'] = $summary;
            $data['pagination'] = array(
                "current_page" => $page,
                "from" => $fromRow,
                "last_page" => $lastPage,
                "per_page" => (int) $limit,
                "to" => $toRow,
                "total" => $totalRow
            );
        }
        $data['meta'] = null;

        return (object) $data;
    }

    */
}
