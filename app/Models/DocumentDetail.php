<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use App\Traits;
use App\Traits\DecimalTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class DocumentDetail extends Model
{
    use DecimalTrait, SoftDeletes;

    const UPDATED_AT = null;

    const CREATED_AT = null;

    protected $table = 'document_detail';

    protected $hidden = [
    ];

    protected $fillable = [
        'document_id',
        'title',
        'attachment_data',
        'language_type',
        'deleted_at'
    ];
}