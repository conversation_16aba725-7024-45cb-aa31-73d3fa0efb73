<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserReward extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'total_points',
        'total_token',
        'last_claimed_at',
        'total_claimed',
        'total_spin',
        'last_deposit_at',
        'ref_deposit_id',
        'last_spin_at',
        'status'
    ];

    protected $casts = [
        'last_claimed_at' => 'datetime',
        'last_deposit_at' => 'datetime',
        'last_spin_at' => 'datetime',
        'status' => 'boolean'
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public static function depositUserReward($user_id, $deposit_id, $amount = 0)
    {
        if ($user_id == null) {
            return false;
        }

        if ($amount < 100) {
            return false;
        }

        $userReward = self::where('user_id', $user_id)->first();
        if (!isset($userReward) && $userReward == null) {
            UserReward::create([
                'user_id' => $user_id,
                'total_points' => 0,
                'total_claimed' => 0,
                'last_deposit_at' => now(),
                'ref_deposit_id' => $deposit_id ?? null,
            ]);
        }

        if (isset($userReward)) {
            $userReward->update([
                'last_deposit_at' => now(),
                'ref_deposit_id' => $deposit_id ?? null
            ]);
        }

        return true;
    }

    public static function getUserRewardList(array $params = [])
    {
        $dateTime = date('Y-m-d H:i:s');
        $date = date('Y-m-d', strtotime($dateTime));
        $sortableColumns = ['created_at'];
        $status = $params['status'] ?? null;
        $createdFrom = $params['created_from'] ?? null;
        $createdTo = $params['created_to'] ?? null;


        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : 'null';
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        // see all
        $seeAll = $params['see_all'] ?? null;

        $report = self::query()->with(['user',])
            ->when(isset($status), function ($q) use ($status) {
                return $q->where('status', $status);
            })
            ->when($createdFrom, function ($q) use ($createdFrom) {
                return $q->whereDATE('created_at', '>=', $createdFrom);
            })
            ->when($createdTo, function ($q) use ($createdTo) {
                return $q->whereDATE('created_at', '<=', $createdTo);
            })
            ->orderBy($order_by, $order_sort)
            ->orderBy('id', $order_sort)
            ->when((!isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $mapFunc = function ($item) {

            switch (MODULE) {
                case 'admin':
                    $res = [
                        'name' => $item->user->name,
                        'current_point' => $item->total_points,
                        'current_token' => $item->total_token,
                        'total_claimed' => $item->total_claimed,
                        'total_spin' => $item->total_spin,
                        'ref_deposit' => $item->ref_deposit_id,
                        'last_claimed_at' => $item->created_at->format('Y-m-d H:i:s'),
                        'last_deposit_at' => $item->created_at->format('Y-m-d H:i:s'),
                        'last_spin_at' => $item->created_at->format('Y-m-d H:i:s')
                    ];
                    break;

                case 'user':
                    $res = [];
                    break;
            }

            return $res;
        };

        if (($seeAll == 1)) {
            return ['list' => $report->get()->map($mapFunc)->toArray()];
        } else {
            $report->getCollection()->transform($mapFunc);
            return (new ItemsCollection($report))->toArray();
        }
    }

    public static function giveFirstTopUpFreeCredit($user_id, $is_online = false)
    {
        return false;

        $bonus_amount = 20;

        if ($user_id == null) {
            return false;
        }

        $user = User::where('id', $user_id)->first();
        if (!isset($user) && $user == null) {
            return false;
        }

        if ($user->is_shop_user == 1 || $user->store_id != 8) {
            return false;
        }

        if (!$is_online) {
            $countOffline = UserCardLog::where('user_id', $user_id)
                ->where('operation_type', UserCardLog::$operationType['deposit'])
                ->where('value_type', UserCardLog::$valueType['offline'])
                ->count();

            if ($countOffline === 1) {
                $offline = UserCardLog::where('user_id', $user_id)
                    ->where('operation_type', UserCardLog::$operationType['deposit'])
                    ->where('value_type', UserCardLog::$valueType['offline'])
                    ->where('operation_qty', '>=', 100)
                    ->first();

                if (!isset($offline) && empty($offline)) {
                    return false;
                }

                $credit = CreditTransaction::where('user_id', $user_id)
                    ->where('credit_id', 1000)
                    ->where('amount', $bonus_amount)
                    ->where('subject_type', config('subject')['free-credit'])
                    ->first();

                if (!isset($credit) && empty($credit) && isset($offline)) {
                    User::getFreeCredit($user_id, $bonus_amount);
                }
            }
        }

        if ($is_online) {
            $countOnline = Deposit::where('user_id', $user_id)
                ->where('status', Deposit::$status['approved'])
                ->count();

            if ($countOnline === 1) {
                $online = Deposit::where('user_id', $user_id)
                    ->where('status', Deposit::$status['approved'])
                    ->where('amount', '>=', 100)
                    ->first();

                if (!isset($online) && empty($online)) {
                    return false;
                }

                $credit = CreditTransaction::where('user_id', $user_id)
                    ->where('credit_id', 1000)
                    ->where('amount', $bonus_amount)
                    ->where('subject_type', config('subject')['free-credit'])
                    ->first();

                if (!isset($credit) && empty($credit) && isset($online)) {
                    User::getFreeCredit($user_id, $bonus_amount);
                }
            }
        }

        return true;
    }
}
