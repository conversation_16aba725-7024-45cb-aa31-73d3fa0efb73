<?php

namespace App\Models;

use App\Models\CreditTransaction as ModelsCreditTransaction;
use App\Traits\DecimalTrait;
use App\Traits\GenerateNumberTrait;
use App\Http\Resources\ItemsCollection;
use App\Traits\DateTrait;
use App\Traits;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;

class Transfer extends Model
{
    use GenerateNumberTrait;
    use DecimalTrait;
    use DateTrait;

    protected $table = 'transfer';

    protected $hidden = [];

    protected $fillable = [
        'transaction_id',
        'from_id',
        'to_id',
        'amount',
        'charges',
        'status',
        'belong_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public static $status = [
        'pending' => 0,
        'successful' => 1,
        'refund' => 2
    ];

    public function fromUser()
    {
        return $this->belongsTo(User::class, "from_id", "id");
    }

    public function toUser()
    {
        return $this->belongsTo(User::class, "to_id", "id");
    }

    public function creditTransaction()
    {
        return $this->belongsTo(CreditTransaction::class, "belong_id", "belong_id");
    }

    public static function add($data = [])
    {
        $user = Auth::user() ?? NUll;
        $credit = Credit::find($data['credit_id']);
        $trxId = GenerateNumberTrait::generateReferenceNo(self::query(), 'transaction_id', null);
        $account_balance = AccountBalance::where('user_id', $user->id)->where('credit_id', $credit->id)->first();

        DB::transaction(function () use ($data, $user, &$fromData, &$receiverData, &$fromId, &$toId, &$amount, &$transfer, &$dateTime, $credit, $trxId) {
            $internalID = User::select('id')->where('username', 'transfer')->where('user_type', User::$userType['internal-account'])->first()->id ?? Null;
            if (empty($internalID)) {
                abort(400, 'Invalid Internal Account.');
            }

            $fromId = $user->id;
            $fromData = $user;
            if (MODULE == 'admin') {
                $fromId = $data['from_uid'] ?? 0;
                $fromData = User::find($fromId);
            }

            $remark = null;
            if (isset($data['remark']) && !empty($data['remark'])) {
                $remark = $data['remark'];
            }

            $receiverData = User::where('username', $data['to_username'])->where('user_type', '!=', User::$userType['internal-account'])->first();
            $toId = $receiverData->id;
            $dateTime = $data['datetime'] ?? date('Y-m-d H:i:s');
            $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;
            $amount = $data['amount'];
            $creditId = $data['credit_id'];

            $creditName = $credit->name;
            $creditType = $credit->type;
            $charge = 0;

            CreditTransaction::insertTransaction($fromId, $internalID, $fromId, $creditType, $amount, "transfer-out", $belongId, $belongId, $remark, $dateTime, null, null, null, $toId, null, true);
            if (isset($data['charge']) && $data['charge'] > 0) {
                $charge = DecimalTrait::setDecimal($data['charge']);
                CreditTransaction::insertTransaction($fromId, $internalID, $fromId, $creditType, $charge, "transfer-charge", $belongId, $belongId, null, $dateTime, null, null, null, null, null, false);
            }

            CreditTransaction::insertTransaction($internalID, $toId, $toId, $creditName, $amount, "transfer-in", $belongId, $belongId, $remark, $dateTime, null, null, null, $fromId, null, true);
            AccountBalance::deductBalance($fromId, $amount);

            $transfer = Transfer::create([
                'transaction_id' => $trxId,
                'from_id' => $fromId,
                'to_id' => $toId,
                'amount' => $amount,
                'charges' => $charge,
                'status' => self::$status['successful'],
                'belong_id' => $belongId,
            ]);

            // Add TransferTurnover Record
            TransferTurnover::add($toId, $amount);
        });
        $userDeviceToken = UserDevice::whereIn('user_id', [$fromId, $toId])->where('status', UserDevice::$status['active'])->get()->keyBy('user_id');

        $creditId = $data['credit_id'];
        // Send to Sender
        if (isset($userDeviceToken[$fromId])) {
            $lang = $fromData->lang ?? 'en';
            $templateType = 'transferSended';
            $template = Traits\FirebaseTrait::template($templateType, $lang);
            $template['body'] = str_replace(['{{to_user}}', '{{amount}}'], [$receiverData->name, DecimalTrait::setDecimal($amount)], $template['body']);
            Traits\FirebaseTrait::sendNotification($userDeviceToken[$fromId]->token, $template, ['transfer' => $transfer->id, 'type' => $templateType, 'credit_id' => $creditId], $userDeviceToken[$fromId]->user_id, ["to_user" => $receiverData->name, "amount" => DecimalTrait::setDecimal($amount)]);
        }

        // Send to Receiver
        if (isset($userDeviceToken[$toId])) {
            $lang = $receiverData->lang ?? 'en';
            $templateType = 'transferReceived';
            $template = Traits\FirebaseTrait::template($templateType, $lang);
            $template['body'] = str_replace(['{{from_user}}', '{{amount}}'], [$fromData->name, DecimalTrait::setDecimal($amount)], $template['body']);
            Traits\FirebaseTrait::sendNotification($userDeviceToken[$toId]->token, $template, ['transfer' => $transfer->id, 'type' => $templateType, 'credit_id' => $creditId], $userDeviceToken[$toId]->user_id, ["from_user" => $fromData->name, "amount" => DecimalTrait::setDecimal($amount)]);
        }

        // $balance = DecimalTrait::setDecimal(($account_balance?->balance ?? 0 - $amount));
        $balance = AccountBalance::where("user_id", $fromId)->where("credit_id", $creditId)->first()->balance;


        try {
            if (in_array($fromId, ['1002696', '1002990'])) {
                UserTicketReward::add([
                    'user_id' => $toId,
                    'amount' => $amount,
                    'ref_id' => $transfer?->id ?? null,
                    'is_online' => true,
                ]);
            }
        } catch (\Throwable $th) {
        }

        return [
            "data" => [
                "amount" => DecimalTrait::setDecimal(($amount)),
                "iso" => $credit->code ?? '',
                "receiver" => $receiverData->name,
                "receiver_contact" => $receiverData->phone_no,
                "remark" => isset($data['remark']) ? $data['remark'] : null,
                "date_time" => DateTrait::dateFormat(($dateTime)),
                "transaction_id" => $trxId,
                "balance" => $balance
            ]
        ];
    }

    public static function getAppsList($data = [])
    {
        $user_id = $data['user_id'] ?? null;
        $fromUsername = $data['from_username'] ?? null;
        $toUsername = $data['to_username'] ?? null;
        $fromDate = $data['from_date'] ?? Null;
        $toDate = $data['to_date'] ?? null;
        $status = isset($data['status']) ? Transfer::$status[$data['status']] : null;
        $seeAll = $data['see_all'] ?? null;
        $country_id = $data['country'] ?? null;

        $sortableColumns = [];

        $tableTotal['amount'] = 0;

        // order
        $order_by = isset($data['order_by']) ? $data['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($data['order_sort']) ? $data['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $data['limit'] ?? config('app.pagination_rows');
        $items = Transfer::query()
            ->with('fromUser', 'toUser')
            ->when($fromUsername, function ($query) use ($fromUsername) {
                return $query->whereRelation('fromUser', 'username', 'like', '%' . $fromUsername . '%');
            })
            ->when($toUsername, function ($query) use ($toUsername) {
                return $query->whereRelation('toUser', 'username', 'like', '%' . $toUsername . '%');
            })
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->whereDate('created_at', ">=", $fromDate);
                return $q->whereDate('created_at', "<=", $toDate);
            })
            ->when(isset($status), function ($query) use ($status) {
                return $query->where('status', $status);
            })
            ->when(isset($country_id), function ($query) use ($country_id) {
                return $query->whereRelation('fromUser', 'country_id', $country_id);
            })->when($user_id, function ($query) use ($user_id) {
                $query->where(function ($q) use ($user_id) {
                    $q->where('from_id', $user_id);
                    return $q->orWhere('to_id', $user_id);
                });
            });

        $summaryQuery = clone $items;
        $items = $items->orderBy($order_by, $order_sort)
            ->when((!isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        if (MODULE == 'admin') {
            $summary = [
                "total_transfer" => 0,
                "total_charge" => 0
            ];

            $summaryQuery = $summaryQuery->get()->map(function ($q) use (&$summary) {
                $summary['total_transfer'] = DecimalTrait::setDecimal($summary['total_transfer']) + $q->amount;
                $summary['total_charge'] = DecimalTrait::setDecimal($summary['total_charge']) + $q->charges;
            });
        }

        $mapFunc = function ($q) use (&$tableTotal) {
            $status = array_search($q->status, Transfer::$status);

            $res = [
                'created_at' => DateTrait::dateFormat($q->created_at),
                'from_user' => $q->fromUser->username ?? null,
                'to_user' => $q->toUser->username ?? null,
                'amount' => DecimalTrait::setDecimal($q->amount),
                'charges' => DecimalTrait::setDecimal($q->charges),
                'transaction_status' => array_search($q->status, Transfer::$status),
                'transaction_status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                'country' => isset($q->fromUser) ? $q->fromUser->country->name : '-',
                'country_display' => isset($q->fromUser) ? (Lang::has('lang.' . $q->fromUser->country->name) ? Lang::get('lang.' . $q->fromUser->country->name) : $q->fromUser->country->name) : '-',
                'updated_at' => DateTrait::dateFormat($q->updated_at),
            ];
            $tableTotal['amount'] = DecimalTrait::setDecimal($tableTotal['amount']) + $q->amount;
            return (object) $res;
        };

        if (($seeAll == 1)) {
            if (MODULE == 'admin') return ['list' => $items->get()->map($mapFunc)->toArray(), 'table_total' => $tableTotal, 'summary' => $summary];
            return ['list' => $items->get()->map($mapFunc)->toArray(), 'table_total' => $tableTotal];
        } else {

            $items->getCollection()->transform($mapFunc);
            foreach ($tableTotal as $key => &$value) {
                $value = DecimalTrait::setDecimal($value);
            }
            $data = new ItemsCollection($items);

            $data->additional['table_total'] = $tableTotal;
            if (MODULE == 'admin') $data->additional['summary'] = $summary;
            return $data;
        }
    }

    public static function list($data = [])
    {
        $user_id = $data['user_id'] ?? null;
        $credit_id = $data['credit_id'] ?? null;
        $fromUsername = $data['from_username'] ?? null;
        $toUsername = $data['to_username'] ?? null;
        $fromDate = $data['from_date'] ?? Null;
        $toDate = $data['to_date'] ?? null;
        $status = isset($data['status']) ? Transfer::$status[$data['status']] : null;
        $seeAll = $data['see_all'] ?? null;
        $country_id = $data['country'] ?? null;

        $sortableColumns = [];

        $tableTotal['amount'] = 0;

        // order
        $order_by = isset($data['order_by']) ? $data['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($data['order_sort']) ? $data['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $data['limit'] ?? config('app.pagination_rows');

        if (isset($data['export']) && ($data['export'] == 1)) {
            $jobData = $data;
            $jobData["model"] = get_class() ?? null;
            $jobData["function"] = __FUNCTION__;
            $jobData["module"] = MODULE;
            $jobData["creator_type"] = MODULE;
            $jobData["creator_id"] = Auth::user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, "Export Successfully");
        }

        $items = Transfer::query()
            ->with('fromUser', 'toUser', 'creditTransaction', 'creditTransaction.creditInfo')
            ->when($fromUsername, function ($query) use ($fromUsername) {
                return $query->whereRelation('fromUser', 'username', 'like', '%' . $fromUsername . '%');
            })
            ->when($toUsername, function ($query) use ($toUsername) {
                return $query->whereRelation('toUser', 'username', 'like', '%' . $toUsername . '%');
            })
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->whereDate('created_at', ">=", $fromDate);
                return $q->whereDate('created_at', "<=", $toDate);
            })
            ->when(isset($status), function ($query) use ($status) {
                return $query->where('status', $status);
            })
            ->when(isset($country_id), function ($query) use ($country_id) {
                return $query->whereRelation('fromUser', 'country_id', $country_id);
            })->when($user_id, function ($query) use ($user_id) {
                $query->where(function ($q) use ($user_id) {
                    $q->where('from_id', $user_id);
                    return $q->orWhere('to_id', $user_id);
                });
            })
            ->when(isset($credit_id), function ($query) use ($credit_id) {
                return $query->whereRelation('creditTransaction', 'credit_id', $credit_id);
            });

        $summaryQuery = clone $items;
        $items = $items->orderBy($order_by, $order_sort)
            ->when((!isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        if (MODULE == 'admin') {
            $summary = [
                "total_transfer" => 0,
                "total_charge" => 0
            ];

            $summaryQuery = $summaryQuery->get()->map(function ($q) use (&$summary) {
                $summary['total_transfer'] = DecimalTrait::setDecimal($summary['total_transfer']) + $q->amount;
                $summary['total_charge'] = DecimalTrait::setDecimal($summary['total_charge']) + $q->charges;
            });
        }

        $mapFunc = function ($q) use (&$tableTotal) {
            $status = array_search($q->status, Transfer::$status);

            $creditName = $q->creditTransaction->creditInfo->name;
            $res = [
                'created_at' => DateTrait::dateFormat($q->created_at),
                'from_username' => $q->fromUser->username ?? null,
                'to_username' => $q->toUser->username ?? null,
                'from_user' => $q->fromUser->name ?? null,
                'to_user' => $q->toUser->name ?? null,
                'amount' => DecimalTrait::setDecimal($q->amount),
                // 'charges' => DecimalTrait::setDecimal($q->charges),
                // 'transaction_status' => array_search($q->status, Transfer::$status),
                // 'transaction_status_display' => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                // 'country' => isset($q->fromUser) ? $q->fromUser->country->name : '-' ,
                // 'country_display' => isset($q->fromUser) ? (Lang::has('lang.'.$q->fromUser->country->name) ? Lang::get('lang.'.$q->fromUser->country->name) : $q->fromUser->country->name) : '-',
                // 'updated_at' => DateTrait::dateFormat($q->updated_at),
                'remark' => $q->creditTransaction->remark,
                'credit_name' => $creditName,
                'credit_name_display' => Lang::has('lang.' . $creditName) ? Lang::get('lang.' . $creditName) : $creditName,
            ];
            $tableTotal['amount'] = DecimalTrait::setDecimal($tableTotal['amount']) + $q->amount;
            return (object) $res;
        };

        if (($seeAll == 1)) {
            if (MODULE == 'admin') return ['list' => $items->get()->map($mapFunc)->toArray(), 'table_total' => $tableTotal, 'summary' => $summary];
            return ['list' => $items->get()->map($mapFunc)->toArray(), 'table_total' => $tableTotal];
        } else {

            $items->getCollection()->transform($mapFunc);
            foreach ($tableTotal as $key => &$value) {
                $value = DecimalTrait::setDecimal($value);
            }
            $data = new ItemsCollection($items);

            $data->additional['table_total'] = $tableTotal;
            if (MODULE == 'admin') $data->additional['summary'] = $summary;
            return $data;
        }
    }
}
