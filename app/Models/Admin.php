<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use <PERSON><PERSON>\JWTAuth\Contracts\JWTSubject;
use App\Http\Resources\ItemsCollection;
use App\Traits;
use App\Traits\DecimalTrait;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class Admin extends Authenticatable implements JWTSubject
{
    protected $table = 'admin';
    protected $guard = 'admin';

    protected $hidden = [
        'password'
    ];

    protected $fillable = [
        'name',
        'username',
        'password',
        'email',
        'phone_no',
        'activated',
        'disabled',
        'is_master',
        'role_id',
        'created_at',
        'updater_id',
        'updated_at',
    ];

    public static $activatedDisplay = [
        'inactive' => 0,
        'active' => 1,
    ];

    protected $casts = [];

    public function getRoles()
    {
        return $this->belongsTo(AdminRoles::class, 'role_id', 'id');
    }

    public function updatedBy()
    {
        return $this->belongsTo(Admin::class, 'updater_id', 'id');
    }

    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [];
    }

    public function adminDetail()
    {
        return $this->hasMany(AdminDetail::class);
    }

    public static function getProfile($adminId)
    {
        $admin = self::with([
            'getRoles',
            'adminDetail' => function ($q) {
                $q->where('name', 'store');
            }
        ])->find($adminId);
        if (empty($admin)) {
            abort(400, Lang::get('lang.admin-not-exist'));
        }

        $adminPermitedPermissions = [];
        $adminPermissionsAry = $admin->getRoles->permissions_id ?? [];
        if (isset($adminPermissionsAry) && !empty($adminPermissionsAry)) {
            $adminPermissions = Permissions::whereIn('id', $adminPermissionsAry)
                ->when($admin, function ($query) use ($admin, &$adminPermitedPermissions) {
                    if ($admin->is_master) {
                        $query->where('master_disabled', 0);
                    } else {
                        $query->where('master_disabled', 0);
                        $query->where('disabled', 0);
                    }
                })->get('id');
            if (isset($adminPermissions) && !empty($adminPermissions)) {
                $tempPermissions = $adminPermissions->toArray();
                foreach ($tempPermissions as $temp) {
                    $adminPermitedPermissions[$temp['id']] = $temp['id'];
                }
            }
        }

        $storeVal = $admin->adminDetail->where('name', 'store');
        $storeJE = $storeVal[0]->value ?? json_encode([]);
        $storeJD = json_decode($storeJE);
        $storeList = $storeJD;

        $data = [
            'id' => $admin->id,
            'name' => $admin->name,
            'username' => $admin->username,
            'email' => $admin->email,
            'phone_no' => $admin->phone_no,
            'status' => $admin->disabled == 1 ? 'disabled' : array_search($admin->activated, self::$activatedDisplay),
            'status_display' => $admin->disabled == 1 ? Lang::get('lang.disabled') : (Lang::has('lang.' . array_search($admin->activated, self::$activatedDisplay)) ? Lang::get('lang.' . array_search($admin->activated, self::$activatedDisplay)) : array_search($admin->activated, self::$activatedDisplay)),
            'created_at' => Traits\DateTrait::dateFormat($admin->created_at),
            'updated_at' => Traits\DateTrait::dateFormat($admin->updated_at) ?: null,
            'permissions_id' => $adminPermitedPermissions,
            'role_id' => $admin->role_id,
            'role_name' => $admin->getRoles->name ?? null,
            'is_master' => $admin->is_master,
            'store_list' => $storeList,
        ];

        return $data;
    }

    public static function getList(array $params = [])
    {
        $name = $params['name'] ?? null;
        $username = $params['username'] ?? null;
        $email = $params['email'] ?? null;
        $phoneNo = $params['phone_no'] ?? null;
        $role = $params['role'] ?? null;
        $status = $params['status'] ?? null;
        $fromDate = $params['date_from'] ?? null;
        $toDate = $params['date_to'] ?? null;
        $updateFromDate = $params['updated_date_from'] ?? null;
        $updateToDate = $params['updated_date_to'] ?? null;

        $sortableColumns = ['name', 'username', 'email', 'status'];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        if (isset($params['export']) && ($params['export'] == 1)) {
            $params["model"] = get_class() ?? null;
            $params["function"] = __FUNCTION__;
            ExportReport::exportReport($params);
        }

        $items = self::query()
            ->with(['getRoles', 'updatedBy'])
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->whereDate('created_at', ">=", date("Y-m-d 00:00:00", strtotime($fromDate)));
                return $q->whereDate('created_at', "<=", date("Y-m-d 23:59:59", strtotime($toDate)));
            })
            ->when((isset($updateFromDate) && isset($updateToDate)), function ($q) use ($updateFromDate, $updateToDate) {
                $q->whereDate('updated_at', ">=", date("Y-m-d 00:00:00", strtotime($updateFromDate)));
                return $q->whereDate('updated_at', "<=", date("Y-m-d 23:59:59", strtotime($updateToDate)));
            })
            ->when($username, function ($query) use ($username) {
                $query->where('username', 'like', "%$username%");
            })
            ->when($name, function ($query) use ($name) {
                $query->where('name', $name);
            })
            ->when(isset($phoneNo), function ($query) use ($phoneNo) {
                $phoneNo = preg_replace('/[^0-9]/', '', $phoneNo);
                $query->where('phone_no', 'LIKE', "%$phoneNo%");
            })
            ->when($email, function ($query) use ($email) {
                $query->where('email', $email);
            })
            ->when($role, function ($query) use ($role) {
                $query->where('role_id', $role);
            })
            ->when(isset($status), function ($query) use ($status) {
                $query->where('activated', $status);
            })
            ->orderBy($order_by, $order_sort)
            ->paginate($limit);

        $items->getCollection()->transform(function ($q) {
            $res = [
                'id' => $q->id,
                'name' => $q->name,
                'username' => $q->username,
                'email' => $q->email,
                'phone_no' => preg_replace('/[^0-9]/', '', $q->phone_no) ?? null,
                'created_at' => Traits\DateTrait::dateFormat($q->created_at),
                'updated_at' => Traits\DateTrait::dateFormat($q->updated_at) ?: null,
                'role_id' => $q->role_id,
                'role_name' => $q->getRoles->name ?? null,
                'status' => $q->disabled == 1 ? 'disabled' : array_search($q->activated, self::$activatedDisplay),
                'status_display' => $q->disabled == 1 ? Lang::get('lang.disabled') : (Lang::has('lang.' . array_search($q->activated, self::$activatedDisplay)) ? Lang::get('lang.' . array_search($q->activated, self::$activatedDisplay)) : array_search($q->activated, self::$activatedDisplay)),
                'updated_by' => $q->updatedBy->username ?? null
            ];
            return (object) $res;
        });
        return (new ItemsCollection($items))->toArray();
    }

    public static function addAdmin(array $params = [])
    {

        $insertData = [
            'name' => isset($params["name"]) ? $params["name"] : "",
            'username' => isset($params["username"]) ? $params["username"] : "",
            'email' => isset($params["email"]) ? $params["email"] : "",
            'phone_no' => isset($params["phone_no"]) ? $params["phone_no"] : "",
            'password' => isset($params["password"]) ? Hash::make($params['password']) : "",
            'activated' => isset($params["status"]) ? $params["status"] : 0,
            'disabled' => isset($params["disabled"]) ? $params["disabled"] : 0,
            'role_id' => isset($params["role_id"]) ? $params["role_id"] : "",
        ];
        $insertStore = [
            'store_id' => isset($params["store_id"]) ? $params["store_id"] : [],
        ];

        DB::transaction(function () use ($insertData, $insertStore) {
            $admin = self::create($insertData);
            if (! $admin) {
                abort(400, 'Failed creating new admin');
            }

            $insertStore['adminID'] = $admin->id;
            AdminDetail::createStore($insertStore);
        });

        return true;
    }

    public static function updateProfile(array $params = [], int $adminId)
    {
        $admin = self::find($adminId);
        if (empty($admin)) {
            abort(400, Lang::get('lang.admin-not-exist'));
        }

        $updatedData = [
            'name' => isset($params["name"]) ? $params["name"] : $admin->name,
            'email' => isset($params["email"]) ? $params["email"] : $admin->email,
            'phone_no' => isset($params["phone_no"]) ? $params["phone_no"] : $admin->phone_no,
            'disabled' => isset($params["status"]) ? ($params["status"] == 1 ? 0 : 1) : 0,
            'activated' => isset($params["status"]) ? $params["status"] : $admin->activated,
            'role_id' => isset($params["role_id"]) ? $params["role_id"] : $admin->permissions_id,
        ];

        $updatedStore = [
            'store_id' => isset($params["store_id"]) ? $params["store_id"] : [],
        ];

        DB::transaction(function () use ($updatedData, $admin, $updatedStore) {
            $admin->update($updatedData);
            $updatedStore['adminID'] = $admin->id;
            AdminDetail::createStore($updatedStore);
        });

        // if ($admin->wasChanged()) {
        //     $admin->update(['updater_id'=>auth()->user()->id]);
        //     return Lang::get('lang.update-successfully');
        // }

        // return Lang::get('lang.no-changes-detected');
        return Lang::get('lang.update-successfully');
    }

    public static function changePassword($data = [])
    {

        $res = self::where('id', $data["user_id"])->first();
        $res->update([
            'password' => Hash::Make($data['password']),
        ]);

        if (!$res) {
            abort(400, Lang::get('lang.admin-not-exist'));
        }

        return true;
    }

    public static function authPermistion($data = [])
    {
        $res = self::where('id', $data["admin_id"])->first(['is_master', 'permissions_id']);
        $adminPermissionsAry = $res->permissions_id;

        if (isset($res['is_master']) && $res['is_master'] == 1) {
            $masterPermissions = Permissions::select(DB::raw('GROUP_CONCAT(id) as ids'))->where('master_disabled', 0)->first()->toArray();

            $adminPermissionsAry = explode(",", $masterPermissions['ids']);
        } else {
            $adminPermissionsAry = [];
            $adminPermissionsArray = $res->permissions_id ?? [];
            if (isset($adminPermissionsArray) && !empty($adminPermissionsArray)) {
                $adminPermissions = Permissions::whereIn('id', $adminPermissionsArray)
                    ->when($res, function ($query) use ($res, &$adminPermitedPermissions) {
                        if ($res->is_master) {
                            $query->where('master_disabled', 0);
                        } else {
                            $query->where('master_disabled', 0);
                            $query->where('disabled', 0);
                        }
                    })->get('id');

                if (isset($adminPermissions) && !empty($adminPermissions)) {
                    $tempPermissions = $adminPermissions->toArray();
                    foreach ($tempPermissions as $temp) {
                        $adminPermissionsAry[] = $temp['id'];
                    }
                }
            }
        }

        return (object) $adminPermissionsAry;
    }

    public static function updatePermistion($data = [])
    {
        $authPermissions = $data['permissions_ids'];
        $adminId = $data['admin_id'];

        $admin = self::find($adminId);
        if (empty($admin)) {
            abort(400, Lang::get('lang.admin-not-exist'));
        }

        $updatedData = [
            'permissions_id' => isset($authPermissions) ? $authPermissions : [],
        ];

        DB::transaction(function () use ($updatedData, $admin) {
            $admin->update($updatedData);
        });

        return true;
    }

    public static function dashboard(array $params = [])
    {
        if (!isset($params['from_date']) || empty($params['from_date'])) {
            $fromDate = date('Y-m-d\T00:00:00.000\Z');
        } else {
            $fromDate = date('Y-m-d\TH:i:s.000\Z', strtotime($params['from_date']));
        }

        if (!isset($params['to_date']) || empty($params['to_date'])) {
            $toDate = date('Y-m-d\T23:59:59.999\Z');
        } else {
            $toDate = date('Y-m-d\TH:i:s.999\Z', strtotime($params['to_date']));
        }

        $totalUser = User::count();
        $totalActivatedUser = User::where('activated', '1')->count();
        $totalDepositAmount = Deposit::sum('receivable_amount');
        $totalWithdrawAmount = Withdrawal::sum('receivable_amount');
        $totalW2C = ExTransfer::where('type', ExTransfer::$type['in'])->sum('receivable_amount');
        $totalC2W = ExTransfer::where('type', ExTransfer::$type['out'])->sum('receivable_amount');

        $firstDepositUsersCount = User::whereBetween('created_at', [$fromDate, $toDate])
            ->whereHas('deposits', function($query) {
                $query->select(DB::raw('user_id, MIN(created_at) AS first_deposit_date'))
                    ->groupBy('user_id');
            })
            ->orWhereHas('userCardLogs', function($query) {
                $query->select(DB::raw('user_id, MIN(transaction_at) AS first_deposit_date'))
                    ->groupBy('user_id');
            })
            ->distinct()
            ->count();

        $activeUsers = User::whereHas('deposits', function ($query) use($fromDate, $toDate){
            $query->whereBetween('created_at', [$fromDate, $toDate]);
        })
        ->orWhereHas('userCardLogs', function ($query) use($fromDate, $toDate){
            $query->whereBetween('transaction_at', [$fromDate, $toDate]);
        })
        ->distinct()
        ->count();

        $data['total_user'] = $totalUser;
        $data['total_active_user'] = $totalActivatedUser;
        $data['total_deposit_amount'] = DecimalTrait::setDecimal($totalDepositAmount);
        $data['total_withdrawal_amount'] =  DecimalTrait::setDecimal($totalWithdrawAmount);
        $data['total_wallet_to_card'] = DecimalTrait::setDecimal($totalW2C);
        $data['total_card_to_wallet'] = DecimalTrait::setDecimal($totalC2W);
        $data['currency'] = "MYR"; // Temp Hardcode
        $data['first_deposit_time'] = $firstDepositUsersCount;
        $data['active_users'] = $activeUsers;
        
        return $data;
    }
}
