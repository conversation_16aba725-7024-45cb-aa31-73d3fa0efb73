<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Http\Resources\ItemsCollection;
use App\Traits\DateTrait;
use App\Traits\DecimalTrait;

class Currency extends Model
{
    const UPDATED_AT = null;

    protected $table = 'currency';

    protected $hidden = [
    ];

    protected $fillable = [
        'iso',
        'name',
        'symbol',
        'decimal',
        'image_url',
        'disabled',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $disabled = [
        'active' => 0,
        'inactive' => 1,
    ];

    public function country()
    {
        return $this->hasOne(Country::class, 'currency_code', 'iso');
    }

    public function credit()
    {
        return $this->hasOne(Credit::class, 'currency_id', 'id');
    }
}

