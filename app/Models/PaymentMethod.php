<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentMethod extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $fillable = [
        'name',
        'value',
        'type',
        'image',
        'bank_list',
        'reference',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    public static $type = [
        'deposit' => 0,
        'payout' => 1,
    ];

    public static $paymentOption = [
        'main_wallet' => 0,
        'card' => 1,
    ];

    public static function getActiveDepositPaymentMethodById($id)
    {
        $paymentMethod = self::where('id', $id)->where('status', true)->where('type', self::$type['deposit'])->first();
        return $paymentMethod;
    }
}
