<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;

class TelexTransferDetail extends Model
{
    protected $table = 'telex_transfer_detail';

    protected $hidden = [
    ];

    protected $casts = [
        "reference" => "array"
    ];

    protected $fillable = [
        'telex_transfer_id',
        'name',
        'value',
        'type',
        'reference',
        'updated_at',
    ];
}
