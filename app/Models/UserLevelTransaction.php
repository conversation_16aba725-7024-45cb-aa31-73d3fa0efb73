<?php

namespace App\Models;

use App\Traits\DecimalTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class UserLevelTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'user_level_id',
        'type',
        'point',
        'rebate_amount',
        'remark',
        'meta',
        'status',
    ];

    public static $type = [
        'daily' => 1,
        'deposit' => 2,
        'card-reload' => 3,
        'turnover' => 4, // Online
        'token' => 5, // Offline
        'adjustment' => 6,
    ];

    public static $status = [
        'pending' => 0,
        'completed' => 1,
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function user_level()
    {
        return $this->belongsTo(UserLevel::class, 'user_level_id', 'id');
    }

    public static function userLevelTransactionCount($userId, $type, $date)
    {
        return self::where('user_id', $userId)
            ->whereDate('created_at', $date)
            ->where('type', $type)
            ->count();
    }

    public static function getRebateAmountReport(array $params = [])
    {
        $fromDate = $params['from_date'] ?? null;
        $toDate = $params['to_date'] ?? null;
        $username = $params['username'] ?? null;
        $phoneNo = $params['phone_no'] ?? null;

        if (! isset($params['from_date']) || empty($params['from_date'])) {
            $firstUser = User::whereIn('user_type', [User::$userType['user-account']])->first();
            $fromDate = date('Y-m-d', strtotime($firstUser->created_at));
        } else {
            $fromDate = $params['from_date'];
        }

        if (! isset($params['to_date']) || empty($params['to_date'])) {
            $toDate = date('Y-m-d');
        } else {
            $toDate = $params['to_date'];
        }

        // Pagination
        $tableTotal = [];
        // $page = 1;
        // $limit = 0;
        // $fromRow = 0;
        // $toRow = INF;
        // $lastPage = 1;
        // $seeAll = $params['see_all'] ?? null;

        // if (!isset($seeAll) || $seeAll == 0) {
        //     $page = $params['page'] ?? 1;
        //     $limit = $params['limit'] ?? config('app.pagination_rows');
        //     $fromDateTS = strtotime(date("Y-m-d 00:00:00", strtotime($fromDate)));
        //     $toDateTS = strtotime(date("Y-m-d 00:00:00", strtotime($toDate)));
        //     $totalRow = ($toDateTS - $fromDateTS) / (24 * 60 * 60) + 1;
        //     $fromRow = (($page - 1) * $limit) + 1;
        //     $toRow = $page * $limit;
        //     $lastPage = ceil(($totalRow / $limit));
        //     if ($page == $lastPage) {
        //         $toRow = $totalRow;
        //     }
        // }
        $initTable = [
            'total_rebate_amount' => DecimalTrait::setDecimal(0),
        ];

        $tableTotal = $initTable;

        $limit = $params['limit'] ?? config('app.pagination_rows');

        $results = self::with('user')
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('created_at'), '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where(DB::raw('created_at'), '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(isset($username), function ($q) use ($username) {
                return $q->whereRelation('user', 'username', 'like', '%'.$username.'%');
            })
            ->when(isset($phoneNo), function ($q) use ($phoneNo) {
                return $q->whereRelation('user', function ($q) use ($phoneNo) {
                    $phoneNo = preg_replace('/[^0-9]/', '', $phoneNo);
                    $q->where('phone_no', 'LIKE', "%$phoneNo%");
                });
            })
            ->groupBy('user_id')
            ->selectRaw('sum(rebate_amount) as total_rebate_amount, sum(point) as total_point, user_id')
            ->paginate($limit);

        $results->getCollection()->transform(function ($q) {
            return [
                'username' => $q->user->username,
                'phone_no' => $q->user->phone_no,
                'total_rebate_amount' => $q->total_rebate_amount,
                'total_point' => $q->total_point,
            ];
        });

        $tableTotal['total_rebate_amount'] = $results->getCollection()->sum(function ($result) {
            return DecimalTrait::setDecimal($result['total_rebate_amount']);
        });

        $data['list'] = $results->items();
        $data['pagination'] = [
            'current_page' => $results->currentPage(),
            'from' => $results->firstItem(),
            'to' => $results->lastItem(),
            'last_page' => $results->lastPage(),
            'per_page' => $results->perPage(),
            'total' => $results->total(),
        ];
        $data['table_total'] = $tableTotal;

        return $data;
    }

    public static function addUserLevelTransaction($userId, $rebateAmount)
    {
        $user = User::with('user_level')->find($userId);
        UserLevelTransaction::create([
            'user_id' => $userId,
            'user_level_id' => $user->user_level->id,
            'point' => 0,
            'rebate_amount' => $rebateAmount,
            'type' => UserLevelTransaction::$type['adjustment'],
            // 'status' => UserLevelTransaction::$status['completed'],
        ]);
    }
}
