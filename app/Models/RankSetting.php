<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class RankSetting extends Model{
    use SoftDeletes;

    protected $table = 'rank_setting';

    protected $hidden = [
    ];

    protected $fillable = [
        'rank_id',
        'name',
        'value',
        'type',
        'reference',
    ];

    public function rank()
    {
        return $this->belongsTo(Rank::class,'rank_id','id');
    }
}
