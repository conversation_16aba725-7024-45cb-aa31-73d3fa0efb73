<?php

namespace App\Models;


use App\Traits;
use App\Models;
use App\Http\Resources\ItemsCollection;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Str;

class UserCard extends Model
{
    protected $table = 'user_card';

    protected $hidden = [];

    protected $fillable = [
        'user_id',
        'store_id',
        'card_id',
        'phone_no',
        'card_name',
        'card_serial_no',
        'member_balance',
        'accumulated_balance',
        'member_status',
        'card_image',
        'branch_code',
        'status',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
    ];

    public function store()
    {
        return $this->belongsTo(Store::class, "store_id", "store_id");
    }

    public function user()
    {
        return $this->belongsTo(User::class, "user_id", "id");
    }

    public function exTransfer()
    {
        return $this->hasMany(ExTransfer::class, "card_id", "id");
    }

    /* #region New Card */
    public static function getAllCardFrom3rd($storeId, $user)
    {
        $type = 'get_all_card';
        $curlParams = [
            'storeId' => $storeId,
            'phone' => Str::replace("-", "", $user->phone_no),
        ];
        $result = Traits\SonicTrait::post($curlParams, $type);
        if (!isset($result['status']) || $result['status'] == false) {
            return $result;
        }
        $cardIds = [];
        foreach ($result['data'] as $eachResult) {
            $cardIds[] = $eachResult['member_card_id'];
        }

        // deactivate card
        UserCard::whereIn('card_id', $cardIds)->where('user_id', '!=', $user->id)->where('status', self::$status['active'])
            ->update(['status' => self::$status['inactive']]);

        UserCard::whereNotIn('card_id', $cardIds)->where('user_id', $user->id)->where('status', self::$status['active'])
            ->update(['status' => self::$status['inactive']]);

        return $result;
    }

    public static function getNewCardList(array $params = [], $internal = false)
    {

        $userId = $params['user_id'];
        $storeId = $params['store_id'];

        $user = User::find($userId);

        $result = self::getAllCardFrom3rd($storeId, $user);
        if (!isset($result['status']) || $result['status'] == false) {
            return $result;
        }

        $store = Models\Store::where('store_id', $storeId)->first();
        $existingCardList = self::where('user_id', $userId)->where('store_id', $storeId)->get();

        $count = count($existingCardList);

        $cardList = [];
        foreach ($result['data'] as $eachResult) {
            $exist = $existingCardList->where('card_serial_no', $eachResult['member_card_no'])->where('status', self::$status['active'])->first();
            if (isset($exist)) continue;

            $thisCard = [
                'member_card_no' => $eachResult['member_card_no'],
                'card_display_name' => 'Card' . $count,
            ];

            if ($internal) {
                $thisCard['member_card_id'] = $eachResult['member_card_id'];
                $thisCard['member_balance'] = $eachResult['member_balance'];
                $thisCard['accumulated_balance'] = $eachResult['accumulated_balance'];
                $thisCard['member_status'] = $eachResult['member_status'];
                $thisCard['card_image'] = $eachResult['card_image'];
                $thisCard['branch_code'] = $eachResult['branch_code'];
            }

            $cardList[] = $thisCard;
            $count += 1;
        }

        $return = [];
        $return['data'] = [
            'store_name' => $store->name ?? '',
            'card_list' => $cardList ?? [],
        ];

        return $return;
    }

    public static function addNewCard(array $params = [])
    {

        $userId = $params['user_id'];
        $storeId = $params['store_id'];
        $cardList = $params['card_list'];
        $cardData = collect($params['card_data']);

        $user = User::find($userId);

        foreach ($cardList as $eachCard) {
            $data = $cardData->where('member_card_no', $eachCard['member_card_no'])->first();

            $insertUserCard['user_id'] = $userId;
            $insertUserCard['store_id'] = $storeId;
            $insertUserCard['card_id'] = $data['member_card_id'];
            $insertUserCard['phone_no'] = $user->phone_no ?? null;
            $insertUserCard['card_name'] = $eachCard['card_display_name'];
            $insertUserCard['card_serial_no'] = $data['member_card_no'];
            $insertUserCard['member_balance'] = $data['member_balance'];
            $insertUserCard['accumulated_balance'] = $data['accumulated_balance'];
            $insertUserCard['member_status'] = $data['member_status'];
            $insertUserCard['card_image'] = $data['card_image'] ?? null;
            $insertUserCard['branch_code'] = $data['branch_code'] ?? null;
            $insertUserCard['status'] = self::$status['active'];

            self::updateOrCreate([
                "user_id" => $userId,
                "store_id" => $storeId,
                "card_id" => $data['member_card_id'],
            ], $insertUserCard);
            // self::create($insertUserCard);
        }

        $inStores = [29, 30, 31, 6, 7, 8, 12];
        if (!in_array($storeId, $inStores) || $user->created_at <= Carbon::parse('2025-02-10 18:00:00')) {
            return;
        }

        $userCards = UserCard::where('user_id', $userId)->where('store_id', $storeId)->get();
        if (isset($userCards) && count($userCards) == 1) {
            // Check Credit Transaction
            $credit = CreditTransaction::where('user_id', $userId)
                ->where('credit_id', 1000)
                ->where('amount', 20)
                ->where('subject_type', config('subject')['free-credit'])
                ->first();

            // Give FREE Credit
            if (!isset($credit) && empty($credit) && in_array($storeId, $inStores)) {
                User::getFreeCredit($userId, 20);
            }
        }

        return;
    }
    /* #endregion */

    /* #region Card */
    public static function getCardList(array $params = [])
    {
        $userId = $params['user_id'] ?? null;
        $ett_limit = $params['ex_transfer_trnx_limit'] ?? 0;
        $memberId = $params['member_id'] ?? null;
        $username = $params['username'] ?? null;
        $phoneNo = $params['phone_no'] ?? null;
        $serial_no = $params['serial_no'] ?? null;
        $storeId = $params['store_id'] ?? null;
        $exStoreId = $params['extra_store_id'] ?? null;
        $status = $params['status'] ?? null;

        $seeAll = $params['see_all'] ?? null;

        $sortableColumns = ['card_name', 'created_at'];

        // Order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // Sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'asc';

        // limit
        $limit = ($seeAll == 1) ? null : ($params['limit'] ?? config('app.pagination_rows'));

        $items = self::query()
            ->with([
                'store' => function ($q) {
                    $q->select('store_id', 'name');
                },
                'user' => function ($q) {
                    $q->select('id', 'username', 'member_id');
                },
                'exTransfer' => function ($q) use ($ett_limit) {
                    $q->select('card_id', 'type', 'amount', 'created_at', 'transaction_id');
                    $q->orderBy('created_at', 'desc');
                    $q->limit($ett_limit);
                },
            ])
            ->when(isset($userId), function ($q) use ($userId) {
                return $q->where('user_id', $userId);
            })
            ->when(isset($status), function ($q) use ($status) {
                return $q->where('status', $status);
            })
            ->when(isset($memberId), function ($q) use ($memberId) {
                return $q->whereRelation('user', 'member_id', $memberId);
            })
            ->when(isset($phoneNo), function ($q) use ($phoneNo) {
                return $q->where('phone_no', $phoneNo);
            })
            ->when(isset($username), function ($q) use ($username) {
                return $q->whereRelation('user', 'username', $username);
            })
            ->when(isset($serial_no), function ($q) use ($serial_no) {
                return $q->where('card_serial_no', $serial_no);
            })
            ->when(isset($storeId), function ($q) use ($storeId) {
                return $q->whereRelation('store', 'id', $storeId);
            })
            ->when(isset($exStoreId), function ($q) use ($exStoreId) {
                return $q->whereHas('store', function ($q) use ($exStoreId) {
                    $q->whereIn('id', $exStoreId);
                });
            })
            ->orderBy($order_by, $order_sort)
            ->when((!isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $mapFunc = function ($q) use ($ett_limit) {
            if (MODULE == 'admin') {
                $adminRes = [
                    'username' => $q->user->username ?? '',
                    'member_id' => $q->user->member_id ?? '',
                    'phone_no' => $q->phone_no ?? '',
                ];
            }

            $statusDisplay = array_search($q->status, self::$status);
            $res = [
                'store_name' => $q->store->name ?? '',
                'card_id' => $q->id,
                'card_name' => $q->card_name,
                'card_serial_no' => $q->card_serial_no,
                'updated_at' => Traits\DateTrait::dateFormat($q->updated_at),
                'card_balance' => Traits\DecimalTrait::setDecimal($q->member_balance),
                'card_serial_no' => $q->card_serial_no,
                'status' => $q->status,
                'status_name' => $statusDisplay,
                'status_display' => Lang::has('lang.' . $statusDisplay) ? Lang::get('lang.' . $statusDisplay) : $statusDisplay,
            ];

            if ($ett_limit > 0) {
                $trnxList = [];
                foreach ($q->exTransfer as $eachT) {
                    $type = array_search($eachT->type, Extransfer::$type);
                    if ($type == "in") $displayType = 'cash-in';
                    if ($type == "out") $displayType = 'cash-out';

                    $trnxList[] = [
                        'type' => $type,
                        "type_display" => Lang::has('lang.' . $displayType) ? Lang::get('lang.' . $displayType) : $type,
                        'amount' => Traits\DecimalTrait::setDecimal($eachT->amount),
                        'created_at' => Traits\DateTrait::dateFormat($eachT->created_at),
                        'trnx_id' => $eachT->transaction_id,
                    ];
                }
                $res["ett"] = $trnxList;
            }
            return (object) array_merge($res, ($adminRes ?? []));
        };

        if (($seeAll == 1)) {
            return ['list' => $items->get()->map($mapFunc)->toArray()];
        } else {
            $items->getCollection()->transform($mapFunc);
            return (new ItemsCollection($items))->toArray();
        }
    }

    public static function getCardDetail(array $params = [])
    {
        $cardId = $params['card_id'];

        $cardRes = self::with([
            'store' => function ($q) {
                $q->select('store_id', 'name');
            },
        ])->find($cardId);

        if ($cardRes->status == self::$status['active']) {
            $type = 'check_game_credit';
            $curlParams = [
                'storeId' => $cardRes->store_id,
                'cardId' => $cardRes->card_id,
            ];
            $result = Traits\SonicTrait::post($curlParams, $type);
            if (!isset($result['status']) || $result['status'] == false) {
                return $result;
            }
        }

        $balance = $result['data']['credit'] ?? 0;

        $updateUserCard = [
            'member_balance' => $balance,
        ];
        $cardRes->update($updateUserCard);

        $statusDisplay = array_search($cardRes->status, self::$status);
        $data = [
            'store_name' => $cardRes->store->name ?? '',
            'card_id' => $cardRes->id,
            'card_name' => $cardRes->card_name,
            'card_serial_no' => $cardRes->card_serial_no,
            'card_balance' => Traits\DecimalTrait::setDecimal($cardRes->member_balance),
            'updated_at' => Traits\DateTrait::dateFormat($cardRes->updated_at),
            'phone_no' => $cardRes->phone_no ?? '',
            'status' => $cardRes->status,
            'status_name' => $statusDisplay,
            'status_display' => Lang::has('lang.' . $statusDisplay) ? Lang::get('lang.' . $statusDisplay) : $statusDisplay,
        ];

        if (isset($params['ex_transfer_data'])) {
            $creditId = Models\Credit::where('name', 'myr-credit')->first()->id ?? null;
            $exParam = [
                'user_id' => $params['user_id'],
                'credit_id' => $creditId,
            ];
            $exTransferData = Models\ExTransfer::getExTransferData($exParam);
        }
        $data['ex_transfer_data'] = $exTransferData['data'] ?? null;
        $return['data'] = $data;

        return $return;
    }
    /* #endregion */

    public static function getPosReport(array $params = [])
    {
        if (!isset($params['from_date']) || empty($params['from_date'])) {
            $fromDate = date('Y-m-d\T00:00:00.000\Z');
        } else {
            $fromDate = date('Y-m-d\TH:i:s.000\Z', strtotime($params['from_date']));
        }

        if (!isset($params['to_date']) || empty($params['to_date'])) {
            $toDate = date('Y-m-d\T23:59:59.999\Z');
        } else {
            $toDate = date('Y-m-d\TH:i:s.999\Z', strtotime($params['to_date']));
        }

        $storeId = Store::find($params['store_id'])->store_id;
        $page = $params['page'] ?? 1;
        $type = $params['type'] ?? null;
        $operationType = $params['operationType'] ?? null;
        $isIncludeOnline = $params['isIncludeOnline'] ?? false;

        if (isset($params['export']) && ($params['export'] == 1)) {
            $jobData = $params;
            $jobData["model"] = get_class() ?? null;
            $jobData["function"] = __FUNCTION__;
            $jobData["module"] = MODULE;
            $jobData["creator_type"] = MODULE;
            $jobData["creator_id"] = Auth::user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, "Export Successfully");
        }

        $curlParams = [
            'storeId' => $storeId,
            'page' => $page,
            'startDate' => $fromDate,
            'endDate' => $toDate,
            'isIncludeOnline' => $isIncludeOnline
        ];

        if (isset($operationType)) {
            $curlParams['operationType'] = $operationType;
        }
        $result = Traits\SonicTrait::post($curlParams, $type);

        $limit = config('app.pagination_rows');
        $list = [];
        $grand_total = [];
        $hasNext = false;

        if ($result['status']) {
            $hasNext = count($result['data']['list']) >= $limit;
            // if ($hasNext) {
            //     array_pop($result['data']);
            // }
            foreach ($result['data']['list'] as $eachData) {
                if ($params['type'] != 'pos_record' && !$params['isIncludeOnline'] && (str_contains($eachData['terminal_address'], 'Card to Wallet') || str_contains($eachData['terminal_address'], 'Wallet to Card'))) {
                    continue;
                }

                $list[] = [
                    'terminal_serial' => $eachData['terminal_serial'] ?? null,
                    'terminal_address' => $eachData['terminal_address'] ?? null,
                    'member_card_id' => $eachData['member_card_id'] ?? null,
                    'member_card_serial_no' => $eachData['member_card_no'] ?? null,
                    'phone_no' => $eachData['member_phone'] ?? null,
                    'transaction_type' => $eachData['operation_type'] ?? null,
                    'transaction_date' => Traits\DateTrait::dateFormat($eachData['date_time']),
                    'initial_balance' => Traits\DecimalTrait::setDecimal($eachData['member_balance'] ?? 0),
                    'amount_used' => Traits\DecimalTrait::setDecimal($eachData['operation_qty'] ?? 0),
                    'balance' => Traits\DecimalTrait::setDecimal($eachData['latest_member_balance'] ?? 0),
                ];
            }

            foreach ($result['data']['sum'] as $eachData) {
                $grand_total['total_initial'] = Traits\DecimalTrait::setDecimal($eachData['total_initial'] ?? 0);
                $grand_total['total_amount'] = Traits\DecimalTrait::setDecimal($eachData['total_amount'] ?? 0);
                $grand_total['total_balance'] = Traits\DecimalTrait::setDecimal($eachData['total_balance'] ?? 0);
                $grand_total['total_all_amount'] = Traits\DecimalTrait::setDecimal($eachData['total_all_amount'] ?? 0);
            }
        }

        $itemFrom = ($page - 1) * $limit;
        $itemTo = $itemFrom + count($list);
        $data['list'] = $list;
        $data['grand_total'] = $grand_total;
        $data['pagination'] = array(
            "current_page" => $page,
            "from" => $itemFrom,
            "last_page" => $hasNext ? $page + 1 : $page,
            "per_page" => (int) $limit,
            "to" => $itemTo,
            "total" => $itemTo
        );

        return $data;
    }

    public static function getPosStatus()
    {
        $page = $params['page'] ?? 1;

        $result = Traits\SonicTrait::post(null, 'pos_status');

        $limit = config('app.pagination_rows');
        $list = [];
        $hasNext = false;

        if ($result['status']) {
            $hasNext = count($result['data']) > $limit;
            if ($hasNext) {
                array_pop($result['data']);
            }

            foreach ($result['data'] as $eachData) {
                $list[] = [
                    'id' => $eachData['id'] ?? null,
                    'name' => $eachData['name'] ?? null,
                    'status' => $eachData['onlineStatus'] ? 'Online' : 'Offline' ?? null,
                ];
            }
        }

        $itemFrom = ($page - 1) * $limit;
        $itemTo = $itemFrom + count($list);
        $data['list'] = $list;
        $data['pagination'] = array(
            "current_page" => $page,
            "from" => $itemFrom,
            "last_page" => $hasNext ? $page + 1 : $page,
            "per_page" => (int) $limit,
            "to" => $itemTo,
            "total" => $itemTo
        );

        return $data;
    }

    public static function getMachineSummary($params = [])
    {
        if (!isset($params['from_date']) || empty($params['from_date'])) {
            $fromDate = date('Y-m-d\T00:00:00.000\Z');
        } else {
            $fromDate = date('Y-m-d\TH:i:s.000\Z', strtotime($params['from_date']));
        }

        if (!isset($params['to_date']) || empty($params['to_date'])) {
            $toDate = date('Y-m-d\T23:59:59.999\Z');
        } else {
            $toDate = date('Y-m-d\TH:i:s.999\Z', strtotime($params['to_date']));
        }

        $storeId = Store::find($params['store_id'])->store_id;
        $page = $params['page'] ?? 1;
        $grandTotal = null;
        $grandTotal['token_in'] = 0;
        $grandTotal['token_out'] = 0;
        $grandTotal['total_profit'] = 0;

        if (isset($params['export']) && ($params['export'] == 1)) {
            $jobData = $params;
            $jobData["model"] = get_class() ?? null;
            $jobData["function"] = __FUNCTION__;
            $jobData["module"] = MODULE;
            $jobData["creator_type"] = MODULE;
            $jobData["creator_id"] = Auth::user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, "Export Successfully");
        }

        $curlParams = [
            'storeId' => $storeId,
            'page' => $page,
            'startDate' => $fromDate,
            'endDate' => $toDate,
        ];

        $result = Traits\SonicTrait::post($curlParams, 'machine_summary');

        $limit = 0; //config('app.pagination_rows');
        $list = [];
        $hasNext = false;

        if ($result['status']) {
            $limit = count($result['data']); // force display all
            $hasNext = count($result['data']) > $limit;
            if ($hasNext) {
                array_pop($result['data']);
            }

            foreach ($result['data'] as $eachData) {
                $list[] = [
                    "pos_id" => $eachData["终端编号"],
                    "machine_name" => $eachData["机台名称"],
                    "machine_no" => $eachData["机台编号"],
                    "token_in" => $eachData["tokenIn"] ?? 0,
                    "token_out" => $eachData["tokenOut"] ?? 0,
                    'total_profit' => Traits\DecimalTrait::setDecimal($eachData['totalprofit'] ?? 0),
                ];

                $grandTotal['token_in'] += Traits\DecimalTrait::setDecimal($eachData['tokenIn']) ?? 0;
                $grandTotal['token_out'] += Traits\DecimalTrait::setDecimal($eachData['tokenOut']) ?? 0;
                $grandTotal['total_profit'] += Traits\DecimalTrait::setDecimal($eachData['totalprofit']) ?? 0;
            }
        }

        $itemFrom = ($page - 1) * $limit;
        $itemTo = $itemFrom + count($list);
        $data['list'] = $list;
        $data['grand_total'] = [
            'token_in' => Traits\DecimalTrait::setDecimal($grandTotal['token_in'] ?? 0),
            'token_out' => Traits\DecimalTrait::setDecimal($grandTotal['token_out'] ?? 0),
            'total_profit' => Traits\DecimalTrait::setDecimal($grandTotal['total_profit'] ?? 0),
        ];
        $data['pagination'] = array(
            "current_page" => $page,
            "from" => $itemFrom,
            "last_page" => $hasNext ? $page + 1 : $page,
            "per_page" => (int) $limit,
            "to" => $itemTo,
            "total" => $itemTo
        );

        return $data;
    }

    public static function getCurrencyDetail($params)
    {
        if (!isset($params['from_date']) || empty($params['from_date'])) {
            $fromDate = date('Y-m-d\T00:00:00.000\Z');
        } else {
            $fromDate = date('Y-m-d\TH:i:s.000\Z', strtotime($params['from_date']));
        }

        if (!isset($params['to_date']) || empty($validator['to_date'])) {
            $toDate = date('Y-m-d\T23:59:59.999\Z');
        } else {
            $toDate = date('Y-m-d\TH:i:s.999\Z', strtotime($params['to_date']));
        }

        $storeId = Store::find($params['store_id'])->store_id ?? null;
        if (empty($storeId)) abort(400, 'Store ID Not Found');
        $page = $params['page'] ?? 1;
        $grandTotal = null;
        $grandTotal['total_amount'] = 0;
        $grandTotal['total_member_balance'] = 0;
        $grandTotal['total_latest_member_balance'] = 0;

        if (isset($params['export']) && ($params['export'] == 1)) {
            $jobData = $params;
            $jobData["model"] = get_class() ?? null;
            $jobData["function"] = __FUNCTION__;
            $jobData["module"] = MODULE;
            $jobData["creator_type"] = MODULE;
            $jobData["creator_id"] = Auth::user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, "Export Successfully");
        }

        $curlParams = [
            'storeId' => $storeId,
            'page' => $page,
            'startDate' => $fromDate,
            'endDate' => $toDate,
        ];

        $result = Traits\SonicTrait::post($curlParams, 'currency_detail');

        $limit = config('app.pagination_rows');
        $list = [];
        $hasNext = false;

        if ($result['status']) {
            $hasNext = count($result['data']) > $limit;
            if ($hasNext) {
                array_pop($result['data']);
            }

            foreach ($result['data'] as $eachData) {

                $list[] = [
                    // if next time update from others side just change the name 
                    "row_num" => $eachData["rowNum"],
                    "amount" => $eachData["operation_qty"],
                    "terminal_serial" => $eachData["terminal_serial"],
                    "member_card_id" => $eachData["member_card_id"],
                    "member_card_no" => $eachData["member_card_no"],
                    "phone_number" => $eachData["member_phone"],
                    "operation_type" => $eachData["operation_type"],
                    "date_time" => $eachData["date_time"],
                    "member_balance" => $eachData["member_balance"],
                    "latest_member_balance" => $eachData["latest_member_balance"],
                ];

                $grandTotal['total_amount'] += Traits\DecimalTrait::setDecimal($eachData['operation_qty']) ?? 0;
                $grandTotal['total_member_balance'] += Traits\DecimalTrait::setDecimal($eachData['member_balance']) ?? 0;
                $grandTotal['total_latest_member_balance'] += Traits\DecimalTrait::setDecimal($eachData['latest_member_balance']) ?? 0;
            }
        }

        $itemFrom = ($page - 1) * $limit;
        $data['list'] = $list;
        $data['grand_total'] = [
            'total_amount' => Traits\DecimalTrait::setDecimal($grandTotal['total_amount'] ?? 0),
            'total_member_balance' => Traits\DecimalTrait::setDecimal($grandTotal['total_member_balance'] ?? 0),
            'total_latest_member_balance' => Traits\DecimalTrait::setDecimal($grandTotal['total_latest_member_balance'] ?? 0),
        ];
        $itemTo = $itemFrom + count($list);
        $data['pagination'] = array(
            "current_page" => $page,
            "from" => $itemFrom,
            "last_page" => $hasNext ? $page + 1 : $page,
            "per_page" => (int) $limit,
            "to" => $itemTo,
            "total" => $itemTo
        );

        return $data;
    }
}
