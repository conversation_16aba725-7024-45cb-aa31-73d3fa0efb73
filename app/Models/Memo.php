<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Http\Resources\ItemsCollection;
use App\Traits\DateTrait;
use App\Traits\S3Trait;
use Illuminate\Support\Facades\Auth;

class Memo extends Model
{
    use S3Trait;
    use DateTrait;
    
    protected $table = 'memo';

    protected $hidden = [
    ];

    protected $fillable = [
        'status',
        'start_date',
        'end_date',
        'creator_id',
        'creator_type',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
    ];

    public function memoDetail(){
        return $this->hasMany(MemoDetail::class);
    }

    public function memoSetting(){
        return $this->hasMany(MemoSetting::class);
    }

    public function admin(){
        return $this->belongsTo(Admin::class,'creator_id','id');
    }

    public static function addMemo(array $params = []){
        return DB::transaction(function () use ($params){
            $creatorId = Auth::user()->id ?? null;
            $creatorType = isset($creatorId) ? MODULE : 'system';

            $insertMemo = [
                'status' => isset($params['status']) ? self::$status[$params['status']] : self::$status['active'],
                'creator_id' => $creatorId,
                'creator_type' => $creatorType
            ];

            if(isset($params['start_date']) && isset($params['end_date'])){
                $insertMemo['start_date'] = $params['start_date'];
                $insertMemo['end_date'] = $params['end_date'];
            }

            $memoId = Memo::create($insertMemo)->id;

            // Insert Memo Data
            foreach ($params['memo_data'] as $memoData) {
                MemoDetail::create([
                    "memo_id" => $memoId,
                    "subject" => $params['subject'],
                    "description" => $params['description'],
                    "language_type" => config('language')[$memoData['language_type']],
                    "upload_data" => ($memoData['upload'] ?? []),
                ]);
            }

            if(isset($params['valid_country'])){
                // Insert Country Setting
                MemoSetting::create([
                    "memo_id" => $memoId,
                    "name" => "valid-country",
                    "value" => json_encode($params['valid_country'] ?? []) ,
                ]);
            }

            if(isset($params['memo_url']['url'])){
                // Insert Url Setting
                MemoSetting::create([
                    "memo_id" => $memoId,
                    "name" => "memo-url",
                    "value" => json_encode($params['memo_url'] ?? []) ,
                ]);
            }

            return true;
        });
    }

    public static function getMemoList(array $params = []){
        $fromDate = $params['from_date'] ?? Null;
        $toDate = $params['to_date'] ?? Null;
        $title = $params['title'] ?? Null;
        $status = $params['status'] ?? null;
        $lang = config('app.locale') ?? 'en';

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');
        $items = self::query()
            ->with(['memoDetail' => function($q){
                $q->orderBy('language_type','DESC');
                return $q;
            },'admin'])
            ->select(["id","start_date","end_date","status","creator_id","status","creator_type","created_at","updated_at"])
            ->when((isset($fromDate) && isset($toDate)),function ($q) use($fromDate,$toDate){
                $q->where(DB::raw('DATE(created_at)'),">=",$fromDate);
                return $q->where(DB::raw('DATE(created_at)'),"<=",$toDate);
            })
            ->when(isset($title),function ($q) use($title){
                return $q->whereRelation("memoDetail","subject","LIKE","%".$title."%");
            })
            ->when(isset($status),function ($q) use($status){
                return $q->where('status',Memo::$status[$status]);
            })
            ->whereNULL('deleted_at')
            ->orderBy($order_by, $order_sort)
            ->paginate($limit);

        $items->getCollection()->transform(function($q) use($lang){
            foreach ($q->memoDetail as $key => $value) {
                $defaultSetting = $value;
                if(array_search($value['language_type'],config('language')) == $lang){
                    $defaultSetting = $value;
                    break;
                }
            }
            $status = array_search($q->status,self::$status);

            $res = [
                "id" => $q->id,
                "subject" => $defaultSetting->subject,
                "description" => $defaultSetting->description,
                "start_date" => isset($q->start_date) ? DateTrait::dateFormat($q->start_date,false) : null,
                "end_date" => isset($q->end_date) ? DateTrait::dateFormat($q->end_date,false) : null,
                "status" => $status,
                "status_display" => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                "creator" => $q->admin->username,
                "created_at" => DateTrait::dateFormat($q->created_at),
                "updated_at" => DateTrait::dateFormat($q->updated_at),
            ];
            
            return (object) $res;
        })->toArray();
        return (new ItemsCollection($items));
    }

    public static function getMemoDetail(array $params = []){
        $items = self::query()
            ->where('id',$params['id'])
            ->with(['memoDetail' => function($q){
                return $q->select('memo_id','subject','description','language_type','upload_data');
            },"memoSetting" => function ($q){
                return $q->select('memo_id','name','value');
            }])
            ->select(["id","start_date","end_date","status","creator_id","creator_type","created_at","updated_at"])
            ->get()
            ->map(function ($q){
                $status = array_search($q->status,self::$status);
                $memoData = $q->memoDetail->toArray();

                foreach ($memoData as &$memoDetail) {
                    unset($memoDetail['memo_id']);
                    $subject = $memoDetail['subject'];
                    $description = $memoDetail['description'];
                    $memoDetail['language_type'] = array_search($memoDetail['language_type'],config('language'));
                    $memoDetail['language_type_display'] = Lang::has('lang.'.$memoDetail['language_type']) ? Lang::get('lang.'.$memoDetail['language_type']) : $memoDetail['language_type'];
                    $memoDetail['upload_data']['upload_name_display'] = env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN').$memoDetail['upload_data']['upload_name'];
                    unset($memoDetail['subject'],$memoDetail['description']);
                    $memoDetail['upload'] = $memoDetail['upload_data'];
                    unset($memoDetail['upload_data']);
                }

                foreach ($q->memoSetting as $settingData) {
                    $memoSetting[(str_replace('-','_',$settingData->name))] = json_decode($settingData->value,true);
                }

                $res = [
                    "id" => $q->id,
                    "subject" => $subject,
                    "description" => $description,
                    "start_date" => isset($q->start_date) ? date("Y-m-d", strtotime($q->start_date)) : null,
                    "end_date" => isset($q->end_date) ? date("Y-m-d", strtotime($q->end_date)) : null,
                    "status" => $status,
                    "status_display" => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                    'memo_data' => $memoData ?? [],
                ];
                return array_merge($res,($memoSetting ?? []));
            })->first();

        $return ['data']  = $items ?? [];
        return $return;
    }

    public static function editMemo(array $params = []){
        return DB::transaction(function () use ($params){
            $creatorId = Auth::user()->id ?? null;
            $creatorType = isset($creatorId) ? MODULE : 'system';

            $memo = self::find($params['id']);
            $updateMemo = [
                'status' => self::$status[$params['status']],
            ];

            if(isset($params['start_date']) && isset($params['end_date'])){
                $updateMemo['start_date'] = $params['start_date'];
                $updateMemo['end_date'] = $params['end_date'];
            }

            $memo->update($updateMemo);

            // Insert Memo Data
            foreach ($params['memo_data'] as $memoData) {
                $validLanguage[] = config('language')[$memoData['language_type']];
                MemoDetail::updateOrCreate(['memo_id'=>$params['id'],'language_type'=>config('language')[$memoData['language_type']]],[
                    "subject" => $params['subject'],
                    "description" => $params['description'],
                    "upload_data" => ($memoData['upload'] ?? []),
                    "deleted_at" => null
                ]);
            }

            MemoDetail::where('memo_id',$params['id'])->whereNotIn('language_type',($validLanguage ?? []))->update(['deleted_at'=>DB::raw('now()')]);

            if(isset($params['valid_country'])) {
                // Insert Country Setting
                MemoSetting::updateOrCreate(["memo_id" => $params['id'],"name" => "valid-country"],["value" => json_encode($params['valid_country'] ?? [])]);
            }
            if(isset($params['memo_url'])) {
                // Insert URL Setting
                MemoSetting::updateOrCreate(["memo_id" => $params['id'],"name" => "memo-url"],["value" => json_encode($params['memo_url'] ?? [])]);
            }

            return true;
        });
    }

    public static function getLoginMemo($userData){
        $dateTime = date('Y-m-d H:i:s');
        $date = date('Y-m-d',strtotime($dateTime));
        $lang = config('app.locale') ?? 'en';

        $items = self::query()
            ->where('status',self::$status['active'])
            ->where(function($q) use ($date){
                $q->where(function($q) use ($date){
                    $q->where(DB::raw('DATE(start_date)'),'<=',$date);
                    $q->where(DB::raw('DATE(end_date)'),'>=',$date);
                    return $q;
                })
                ->orWhere(function($q) use ($date){
                    $q->where('start_date',null)->where('end_date',null);
                    return $q;
                });
                return $q;
            })
            ->whereNULL('deleted_at')
            ->with(['memoDetail' => function($q){
                $q->select('memo_id','subject','description','language_type','upload_data');
                $q->orderBy('language_type','DESC');
                return $q;
            },"memoSetting" => function ($q){
                return $q->select('memo_id','name','value')->where('name','memo-url');
            }])
            ->select(["id","start_date","end_date","status"])
            ->get()
            ->map(function ($q) use ($lang){
                foreach ($q->memoDetail as $key => $value) {
                    $defaultSetting = $value;
                    if(array_search($value['language_type'],config('language')) == $lang){
                        $defaultSetting = $value;
                        break;
                    }
                }
                $uploadData = (array)$defaultSetting->upload_data;
                $uploadData['upload_name_display'] = env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN').$uploadData['upload_name'];
                unset($uploadData['upload_name']);

                foreach ($q->memoSetting as $key => $value) {
                    $memoSetting[$value->name] = json_decode($value->value,true);
                }
    
                $res = [
                    "subject" => $defaultSetting->subject,
                    "description" => $defaultSetting->description,
                    "upload_data" => $uploadData,
                ];

                $res = array_merge($res,($memoSetting ?? []));
                
                return (object) $res;
            });

        return $items ?? null;
    }

    public static function deleteMemo(array $params = []){
        return DB::transaction(function () use ($params){
            $creatorId = Auth::user()->id ?? null;
            $creatorType = isset($creatorId) ? MODULE : 'system';

            $memo = self::find($params['id']);
            $memo->update(['deleted_at'=>DB::raw('now()')]);;

            return true;
        });
    }
}
