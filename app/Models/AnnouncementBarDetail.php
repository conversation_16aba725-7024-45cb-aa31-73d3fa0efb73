<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Http\Resources\ItemsCollection;
use Illuminate\Support\Facades\Auth;

class AnnouncementBarDetail extends Model
{
    const UPDATED_AT = null;

    const CREATED_AT = null;
    
    protected $table = 'announcement_bar_detail';

    protected $hidden = [
    ];

    protected $fillable = [
        'bar_id',
        'subject',
        'description',
        'language_type',
        'deleted_at',
    ];
}
