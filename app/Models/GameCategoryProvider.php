<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class GameCategoryProvider extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'game_category_id',
        'game_provider_id',
        'name',
        'url',
        'icon',
        'is_game',
        'service_id',
        'priority',
        'status',
    ];

    public function game_category()
    {
        return $this->belongsTo(GameCategory::class, 'game_category_id', 'id');
    }

    public function game_provider()
    {
        return $this->belongsTo(GameProvider::class, 'game_provider_id', 'id');
    }

    public function services()
    {
        return $this->hasMany(Services::class, 'game_category_provider_id', 'id');
    }

    public function service()
    {
        return $this->belongsTo(Services::class, 'service_id', 'id');
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->priority = self::max('priority') + 1;
        });

        static::addGlobalScope('status', function ($query) {
            $query->where('status', true);
        });
    }
}
