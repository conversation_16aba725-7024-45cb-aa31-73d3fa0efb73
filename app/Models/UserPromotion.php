<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use App\Services\GameProvider\GSC;
use App\Services\GameProvider\JILI;
use App\Services\GameProvider\JK;
use App\Traits;
use App\Traits\DateTrait;
use App\Traits\DecimalTrait;
use App\Traits\GenerateNumberTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class UserPromotion extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'promotion_id',
        'claim_type',
        'target_turnover',
        'achieved_turnover',
        'bonus_amount',
        'burn_amount',
        'max_withdraw_amount',
        'game_return_amount',
        'meta_transaction',
        'achieved_at',
        'machine_id',
        'status',
        'is_claimed',
    ];

    public static $status = [
        'in-progress' => 0, // once user access game then status will be in-progress
        'completed' => 1, // user had archieved target turnover
        'pending' => 2, // to know user haven't access game yet, once access then give bonus amount
        'incomplete' => 3, // the promotion is incomplete, game amount depleted
        'cancelled' => 4, // the promotion has been cancelled
    ];

    public static $claimType = [
        'manual' => 1,
        'online' => 2,
        'offline' => 3,
        'deposit' => 4,
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function promotion()
    {
        return $this->belongsTo(Promotion::class, 'promotion_id', 'id');
    }

    public static function applyPromotionForDeposit($promotion_id, $amount, $user_id = null)
    {
        $user_id = $user_id ?? auth()->user()->id;
        $user = User::find($user_id);
        $promotion = Promotion::find($promotion_id);

        if (! isset($promotion)) {
            abort(400, json_encode(['Promotion Not Found']));
        }

        $userPromotion = UserPromotion::where('user_id', $user_id)
            ->where('promotion_id', $promotion_id)
            ->whereIn('status', [self::$status['pending'], self::$status['in-progress']])
            ->first();

        if (isset($userPromotion)) {
            abort(400, json_encode(['User Promotion Already Applied']));
        }

        $productId = $promotion->product_id;
        $product = Product::with([
            'productSetting' => function ($q) {
                $q->where('name', 'hasWalletList');
                $q->where('value', '1');
            },
        ])
            ->where('id', $productId)
            ->where('status', Product::$status['active'])
            ->first();
        UserProduct::getUserProductByUserAndProduct($user, $product);

        $bonus_amount = $promotion->is_bonus_multiplier ? $amount * $promotion->bonus_multiplier : $promotion->bonus_amount;
        $target_turnover = $promotion->is_turnover_multiplier ? $amount * $promotion->turnover_multipler : $promotion->turnover_amount;
        $max_bonus_amount = $promotion->max_bonus_amount ?? 0;

        if ($max_bonus_amount > 0 && $bonus_amount > $max_bonus_amount) {
            $bonus_amount = $max_bonus_amount;
        }

        DB::transaction(function () use ($promotion, $promotion_id, $bonus_amount, $user_id, $target_turnover) {
            $upromotion = self::create([
                'user_id' => $user_id,
                'promotion_id' => $promotion_id,
                'target_turnover' => $target_turnover,
                'achieved_turnover' => 0,
                'bonus_amount' => $bonus_amount,
                'max_withdraw_amount' => $promotion->is_withdrawal_multiplier ? $promotion->max_withdrawal_multiplier * $bonus_amount : $promotion->max_withdraw_amount,
                'status' => self::$status['pending'],
                'claim_type' => self::$claimType['deposit'],
            ]);

            self::addBonusToUser($upromotion, $bonus_amount);
        });

        return [
            'bonus_amount' => $bonus_amount,
            'turnover_amount' => $target_turnover,
        ];
    }

    public static function applyPromotion($promotion_id, $bonus_amount, $machine_id = null, $user_id = null)
    {
        $user_id = auth()->user()?->id ?? $user_id;
        $user = User::find($user_id);
        $promotion = Promotion::find($promotion_id);
        if (! isset($promotion)) {
            abort(400, json_encode(['Promotion Not Found']));
        }

        $productId = $promotion->product_id;

        $userPromotion = UserPromotion::where('user_id', $user_id)
            ->where('promotion_id', $promotion_id)
            ->whereIn('status', [self::$status['pending'], self::$status['in-progress']])
            ->first();
        if (isset($userPromotion)) {
            abort(400, json_encode(['User Promotion Already Applied']));
        }

        $product = Product::with([
            'productSetting' => function ($q) {
                $q->where('name', 'hasWalletList');
                $q->where('value', '1');
            },
        ])
            ->where('id', $productId)
            ->where('status', Product::$status['active'])
            ->first() ?? null;

        // if ($productId == JILI::$productId && ! $userProduct) {
        //     resolve(JILI::class)->createMember($user);
        // } elseif ($product->aggregator == GSC::$name) {
        //     $currentProductId = UserProduct::with(['product', 'user'])->where('user_id', $user_id)->whereHas('product', function ($query) {
        //         $query->where('aggregator', 'GSC');
        //     })->first();
        //
        //     $gsc->createMember($user, $currentProductId->product_id ?? -1, $product->id);
        // }

        $userProduct = UserProduct::getUserProductByUserAndProduct($user, $product);

        $paramsData = [
            'amount' => 0,
            'credit_id' => 1000,
            'product_name' => $userProduct->product->name,
            'product_id' => $userProduct->product_id,
            'user_id' => $userProduct->user_id,
            'credit_type' => Credit::first()->type,
            'curl_type' => 'withdraw',
        ];

        ExTransfer::transferOut($paramsData + [
            'product_data' => $userProduct->product,
            'wallet_data' => [],
            'exMemberId' => $userProduct->member_id,
            'wallet_type' => 'Main',
        ]);

        return DB::transaction(function () use ($promotion, $promotion_id, $bonus_amount, $user_id, $machine_id, $userProduct) {
            $upromotion = self::create([
                'user_id' => $user_id,
                'promotion_id' => $promotion_id,
                'target_turnover' => $promotion->is_turnover_multiplier ? $promotion->turnover_multipler * $bonus_amount : $promotion->turnover_amount,
                'achieved_turnover' => 0,
                'bonus_amount' => $bonus_amount,
                'max_withdraw_amount' => $promotion->is_withdrawal_multiplier ? $promotion->max_withdrawal_multiplier * $bonus_amount : $promotion->max_withdraw_amount,
                'status' => self::$status['pending'],
                'machine_id' => $machine_id,
                'claim_type' => $machine_id ? self::$claimType['offline'] : self::$claimType['online'],
            ]);

            self::addBonusToUser($upromotion, $bonus_amount);
            self::transferBonusToProduct($userProduct, $upromotion->id, $bonus_amount);

            // $internalID = User::select('id')->where('username', 'bonusPayout')->where('user_type', User::$userType['internal-account'])->first()->id ?? null;
            // if (empty($internalID)) {
            //     throw new \Exception('Invalid Internal Account.', 400);
            // }
            // $belongId = Traits\GenerateNumberTrait::GenerateNewId() ?? 0;
            // $batchId = $belongId;
            // $creditName = Credit::where('type', 'myr-credit')->orderBy('priority', 'ASC')->first()->name;
            //
            // CreditTransaction::insertTransaction($internalID, $user_id, $user_id, $creditName, $bonus_amount, 'promotion-bonus-payout', $belongId, $batchId, null, date('Y-m-d H:i:s'), null, null, null, $upromotion->id);

            // if ($promotion->product_id == 2004) {
            //     $res = resolve(JK::class)->getLoginUrl($userProduct->member_id, $bonus_amount, $userProduct->user->store->jk_agent_id, false);
            // } elseif ($product->aggregator == GSC::$name) {
            //     $res = $gsc->deposit($userProduct->member_id, $bonus_amount, $product->provider_code);
            // }
            //
            // if (isset($res['status']) || $res['status'] == true) {
            //     self::updateUserPromotion(['achieved_turnover' => 0, 'game_return_amount' => 0]);
            //     CreditTransaction::insertTransaction($user_id, $internalID, $user_id, $creditName, $bonus_amount, 'promotion-bonus-payout', $belongId, $batchId, null, date('Y-m-d H:i:s'), null, null, null, $upromotion->id);
            // }
        });
    }

    private static function addBonusToUser($userPromotion, $amount)
    {
        $userId = $userPromotion->user_id;
        $internalID = User::select('id')->where('username', 'bonusPayout')->where('user_type', User::$userType['internal-account'])->first()->id ?? null;
        if (empty($internalID)) {
            throw new \Exception('Invalid Internal Account.', 400);
        }
        $belongId = Traits\GenerateNumberTrait::GenerateNewId() ?? 0;
        $batchId = $belongId;
        $creditName = Credit::where('type', 'myr-credit')->orderBy('priority', 'ASC')->first()->name;

        CreditTransaction::insertTransaction($internalID, $userId, $userId, $creditName, $amount, 'promotion-bonus-payout', $belongId, $batchId, null, date('Y-m-d H:i:s'), null, null, null, $userPromotion->id);
    }

    private static function transferBonusToProduct($userProduct, $userPromotionId, $amount)
    {
        $product = Product::find($userProduct->product_id);
        $internalID = User::select('id')->where('username', 'bonusPayout')->where('user_type', User::$userType['internal-account'])->first()->id ?? null;
        if (empty($internalID)) {
            throw new \Exception('Invalid Internal Account.', 400);
        }
        $belongId = Traits\GenerateNumberTrait::GenerateNewId() ?? 0;
        $batchId = $belongId;
        $creditName = Credit::where('type', 'myr-credit')->orderBy('priority', 'ASC')->first()->name;

        if ($userProduct->product_id == 2004) {
            $res = resolve(JK::class)->getLoginUrl($userProduct->member_id, $amount, $userProduct->user->store->jk_agent_id, false);
        } elseif ($product->aggregator == GSC::$name) {
            $res = resolve(GSC::class)->deposit($userProduct->member_id, $amount, $product->provider_code);
        }

        if (isset($res['status']) || $res['status'] == true) {
            self::updateUserPromotion(['achieved_turnover' => 0, 'game_return_amount' => 0, 'user_id' => $userProduct->user_id]);
            CreditTransaction::insertTransaction($userProduct->user_id, $internalID, $userProduct->user_id, $creditName, $amount, 'promotion-bonus-payout', $belongId, $batchId, null, date('Y-m-d H:i:s'), null, null, null, $userPromotionId);
        }
    }

    public static function getUserPromotionByUserId($user_id, $product_id = null)
    {
        $userPromotion = self::with('promotion')
            ->where('user_id', $user_id)
            ->whereIn('status', [self::$status['pending'], self::$status['in-progress']])
            ->when($product_id, function ($q) use ($product_id) {
                $q->whereHas('promotion', function ($query) use ($product_id) {
                    $query->where('product_id', $product_id);
                });
            })
            ->first();

        return $userPromotion;
    }

    public static function getTodayAchievedTurnoverByProduct($user_id, $product_id, $achieved_turnover)
    {
        $turnover = self::with('promotion')
            ->where('user_id', $user_id)
            ->whereHas('promotion', function ($query) use ($product_id) {
                $query->where('product_id', $product_id);
            })
            ->whereDate('created_at', today())
            ->sum('achieved_turnover');

        $turnover -= $achieved_turnover;

        return $turnover > 0 ? $turnover : 0;
    }

    public static function getTodayReturnAmountByProduct($user_id, $product_id, $return_amount)
    {
        $returnAmount = self::with('promotion')
            ->where('user_id', $user_id)
            ->whereHas('promotion', function ($query) use ($product_id) {
                $query->where('product_id', $product_id);
            })
            ->whereDate('created_at', today())
            ->sum('game_return_amount');

        $returnAmount -= $return_amount;

        return $returnAmount > 0 ? $returnAmount : 0;
    }

    public static function getIncompletedUserPromotionByUserId($user_id, $product_id = null)
    {
        $userPromotion = self::with('promotion')
            ->where('user_id', $user_id)
            ->where('status', self::$status['incomplete'])
            ->where('is_claimed', false)
            ->when($product_id, function ($q) use ($product_id) {
                $q->whereHas('promotion', function ($query) use ($product_id) {
                    $query->where('product_id', $product_id);
                });
            })
            ->orderBy('created_at', 'DESC')
            ->first();

        return $userPromotion;
    }

    public static function getCompletedUserPromotionByUserId($user_id, $product_id = null)
    {
        $userPromotion = self::with('promotion')
            ->where('user_id', $user_id)
            ->where('status', self::$status['completed'])
            ->where('is_claimed', false)
            ->when($product_id, function ($q) use ($product_id) {
                $q->whereHas('promotion', function ($query) use ($product_id) {
                    $query->where('product_id', $product_id);
                });
            })
            ->orderBy('created_at', 'DESC')
            ->first();

        return $userPromotion;
    }

    public static function getCancelledUserPromotionByUserId($user_id, $product_id = null)
    {
        $userPromotion = self::with('promotion')
            ->where('user_id', $user_id)
            ->where('status', self::$status['cancelled'])
            ->where('is_claimed', false)
            ->when($product_id, function ($q) use ($product_id) {
                $q->whereHas('promotion', function ($query) use ($product_id) {
                    $query->where('product_id', $product_id);
                });
            })
            ->orderBy('created_at', 'DESC')
            ->first();

        return $userPromotion;
    }

    public static function claimUserPromotion($promotion, $burn_amount, $is_incomplete = false)
    {
        if ($is_incomplete) {
            $promotion->update([
                'status' => self::$status['incomplete'],
                'is_claimed' => true,
                'burn_amount' => $burn_amount,
            ]);

            return;
        }

        $promotion->update([
            'is_claimed' => true,
            'burn_amount' => $burn_amount,
        ]);
    }

    public static function checkUserActivePromotion($user_id = null)
    {
        // Step 1: Check any existing promotion for the user
        $userPromotion = self::with('promotion')->where('user_id', auth()->user()?->id ?? $user_id)
            ->whereIn('status', [self::$status['pending'], self::$status['in-progress']])
            ->first();

        if (isset($userPromotion)) {
            return [
                'user_promotion' => $userPromotion,
                'user_id' => $userPromotion->user_id,
                'target_turnover' => $userPromotion->target_turnover,
                'achieved_turnover' => $userPromotion->achieved_turnover,
                'bonus_amount' => $userPromotion->bonus_amount,
                'max_withdraw_amount' => $userPromotion->max_withdraw_amount,
                'initial_at' => $userPromotion->created_at,
                'product_id' => $userPromotion->promotion->product_id,
            ];
        }

        return null;
    }

    public static function updateUserPromotion($params = [])
    {
        $userPromotion = self::where('user_id', auth()->user()?->id ?? $params['user_id'])
            ->whereIn('status', [self::$status['pending'], self::$status['in-progress']])
            ->first();

        if ($userPromotion) {
            if ($params['achieved_turnover'] >= $userPromotion->target_turnover) {
                $userPromotion->update([
                    'achieved_turnover' => $params['achieved_turnover'],
                    'game_return_amount' => $params['game_return_amount'],
                    'status' => self::$status['completed'],
                    'achieved_at' => date('Y-m-d H:i:s'),
                ]);
            } else {
                $userPromotion->update([
                    'achieved_turnover' => $params['achieved_turnover'] > 0 ? $params['achieved_turnover'] : $userPromotion->achieved_turnover,
                    'game_return_amount' => $params['game_return_amount'] > 0 ? $params['game_return_amount'] : $userPromotion->game_return_amount,
                    'status' => self::$status['in-progress'],
                ]);
            }

            if ($params['is_promotion_incomplete'] ?? false) {
                $userPromotion->update([
                    'status' => self::$status['incomplete'],
                ]);
            }
        }
    }

    public static function getList(array $params = [])
    {
        $username = $params['username'] ?? null;
        $phoneNo = $params['phone_no'] ?? null;
        $userID = $params['user_id'] ?? null;
        $fromDate = $params['from_date'] ?? null;
        $toDate = $params['to_date'] ?? null;
        $seeAll = $params['see_all'] ?? null;

        $sortableColumns = ['id', 'created_at'];

        // Order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // Sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = ($seeAll == 1) ? null : ($params['limit'] ?? config('app.pagination_rows'));

        $items = self::query()
            ->with(['user', 'promotion'])
            ->select(['id', 'user_id', 'promotion_id', 'target_turnover', 'achieved_turnover', 'bonus_amount', 'max_withdraw_amount', 'game_return_amount', 'status', 'created_at'])
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('created_at'), '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where(DB::raw('created_at'), '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(in_array(MODULE, ['app', 'user']), function ($q) use ($userID) {
                $q->where('user_id', $userID);
            })
            ->when($username, function ($query) use ($username) {
                $query->whereRelation('user', 'username', 'like', "%$username%");
            })
            ->when(isset($phoneNo), function ($query) use ($phoneNo) {
                $phoneNo = preg_replace('/[^0-9]/', '', $phoneNo);
                $query->whereRelation('user', 'phone_no', 'LIKE', "%$phoneNo%");
            })
            ->orderBy($order_by, $order_sort)
            ->when((! isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $mapFunc = function ($q) {
            return [
                'id' => $q->id,
                'username' => $q->user->username,
                'phone_no' => $q->user->phone_no,
                'store_name' => $q->user->store->name,
                'promotion' => $q->promotion->name,
                'product' => $q->promotion->product->display_name,
                'target_turnover' => $q->target_turnover,
                'archieved_turnover' => $q->achieved_turnover,
                'bonus_amount' => $q->bonus_amount,
                'max_withdraw_amount' => $q->max_withdraw_amount,
                'game_return_amount' => $q->game_return_amount,
                'status' => array_search($q->status, self::$status) ?? null,
                'created_at' => DateTrait::dateFormat($q->created_at),
            ];
        };

        if (($seeAll == 1)) {
            return ['list' => $items->get()->map($mapFunc)->toArray()];
        } else {
            $items->getCollection()->transform($mapFunc);

            return (new ItemsCollection($items))->toArray();
        }
    }

    // public static function updateStatus($params = []) {}

    public static function updateStatus($params = [])
    {
        $userPromotion = UserPromotion::find($params['id']);
        if ($userPromotion['status'] == UserPromotion::$status['completed']) {
            return abort(400, 'Promotion Already Completed');
        }

        $user_id = $userPromotion->user_id;
        $user_product = UserProduct::with('product', 'user')->where('user_id', $user_id)->where('product_id', 2004)->first();

        $jk = resolve(JK::class);
        $result = $jk->getWithdraw($user_product->member_id, $user_product->user->store->jk_agent_id, false);

        if ($result['data']['main_wallet'] > 0) {
            $userPromotion->update([
                'game_return_amount' => $result['data']['main_wallet'] ?? 0,
                'status' => UserPromotion::$status['completed'],
            ]);

            // Max Withdrawal Cap
            if ($userPromotion->max_withdraw_amount > 0) {
                $result['data']['main_wallet'] = $result['data']['main_wallet'] > $userPromotion->max_withdraw_amount ? $userPromotion->max_withdraw_amount : $result['data']['main_wallet'];
            }

            if ($userPromotion->achieved_turnover < $userPromotion->target_turnover) {
                $result['data']['main_wallet'] = 0;
            }

            if (isset($result) && $result['status'] == true) {

                $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;
                $trxId = GenerateNumberTrait::generateReferenceNo(ExTransfer::query(), 'transaction_id', null, $user_product->product->name);

                $insertData = [
                    'user_id' => $user_id,
                    'transaction_id' => $trxId,
                    'credit_id' => 1000 ?? null,
                    'type' => 2 ?? null, // WARNING: hardcode
                    'product_id' => 2004 ?? null,
                    'wallet_type' => 'Main' ?? null,
                    'credit_type' => Credit::first()->type ?? null,
                    'amount' => DecimalTrait::setDecimal($result['data']['main_wallet'] ?? 0, 2) ?? null,
                    'receivable_amount' => $result['data']['main_wallet'] ?? null,
                    'status' => ExTransfer::$status['processing'],
                    'belong_id' => $belongId,
                    'machine_id' => null,
                ];

                $exTransfer = ExTransfer::create($insertData);
                if (! $exTransfer) {
                    throw new \Exception('Ex Transfer Failed');
                }

                $update = ['amount' => $result['data']['main_wallet'] ?? 0, 'receivable_amount' => $result['data']['main_wallet'] ?? 0, 'status' => ExTransfer::$status['confirmed']];

                // $exTransfer = ExTransfer::with(['creditType'])->where(['id' => $exTransfer->id ?? 0])->first();
                $internalId = User::where('username', 'exTransfer')->where('user_type', User::$userType['internal-account'])->first()->id;
                CreditTransaction::insertTransaction($internalId, $exTransfer['user_id'], $exTransfer['user_id'], $exTransfer->creditType->name, $result['data']['main_wallet'], 'ex-transfer-in', $exTransfer['belong_id'], $exTransfer['belong_id'], null, date('Y-m-d H:i:s'), $exTransfer['transaction_id'], null, null, $exTransfer->id, null, false);

                UserProduct::updateTransferTotal($exTransfer['user_id'], $exTransfer['product_id'], 'out', $result['data']['main_wallet'], $result['data']['main_wallet'] ?? null);

                AccountBalance::updateBalance($user_id, $result['data']['main_wallet']);
            } else {
                $update = ['status' => ExTransfer::$status['failed'], 'res_data' => $result['msg'] ?? null];
            }

            ExTransfer::where([
                'id' => $params['ex_transfer_id'] ?? 0,
            ])->update($update);
        } else {
            $userPromotion->update([
                'status' => UserPromotion::$status['completed'],
            ]);
        }

        abort(200, 'Promotion Completed Successfully');
    }

    // Admin
    public static function add(array $params = [])
    {
        $username = $params['username'];
        $type = $params['type'];

        switch ($type) {
            case 1: // Check
                $user = User::when(isset($username), function ($q) use ($username) {
                    return $q->where('username', $username);
                })->first();
                if (! isset($user)) {
                    abort(400, 'User Not Found');
                }

                $userPromotion = UserPromotion::where('user_id', $user->id)
                    ->whereIn('status', [self::$status['pending'], self::$status['in-progress']])
                    ->first();
                if (isset($userPromotion)) {
                    abort(400, 'User Promotion Already Applied');
                }

                $promotions = Promotion::where('status', true)->get();

                return [
                    'data' => [
                        'user' => $user,
                        'promotion_list' => $promotions,
                    ],
                ];
                break;
            case 2: // Create
                $promotion_id = $params['promotion_id'];
                $promotion = Promotion::find($promotion_id);
                if (! isset($promotion)) {
                    abort(400, 'Promotion Not Found');
                }

                $user_id = $params['user_id'];
                $bonus_amount = $params['bonus_amount'];
                $target_turnover = $params['target_turnover'] ?? $promotion->turnover_amount;
                $max_withdraw_amount = $params['max_withdrawal_amount'] ?? $promotion->max_withdraw_amount;

                $userPromotion = UserPromotion::where('user_id', $user_id)
                    ->whereIn('status', [self::$status['pending'], self::$status['in-progress']])
                    ->first();
                if (isset($userPromotion)) {
                    abort(400, 'User Promotion Already Applied');
                }

                return DB::transaction(function () use ($promotion, $promotion_id, $bonus_amount, $user_id, $target_turnover, $max_withdraw_amount) {
                    $upromotion = self::create([
                        'user_id' => $user_id,
                        'promotion_id' => $promotion_id,
                        'target_turnover' => $target_turnover,
                        'achieved_turnover' => 0,
                        'bonus_amount' => $bonus_amount,
                        'max_withdraw_amount' => $max_withdraw_amount,
                        'status' => self::$status['pending'],
                        'claim_type' => self::$claimType['manual'],
                    ]);

                    $internalID = User::select('id')->where('username', 'bonusPayout')->where('user_type', User::$userType['internal-account'])->first()->id ?? null;
                    if (empty($internalID)) {
                        throw new \Exception('Invalid Internal Account.', 400);
                    }
                    $belongId = Traits\GenerateNumberTrait::GenerateNewId() ?? 0;
                    $batchId = $belongId;
                    $creditName = Credit::where('type', 'myr-credit')->orderBy('priority', 'ASC')->first()->name;

                    CreditTransaction::insertTransaction($internalID, $user_id, $user_id, $creditName, $bonus_amount, 'promotion-bonus-payout', $belongId, $batchId, null, date('Y-m-d H:i:s'), null, null, null, $upromotion->id);

                    if ($promotion->product_id == 2004) {
                        $userProduct = UserProduct::where('user_id', $user_id)->where('product_id', $promotion->product_id)->first();
                        $user = User::find($user_id);
                        if ($promotion->product_id == JK::$productId && ! $userProduct) {
                            UserProduct::subscribeGame($user_id, $promotion->product_id, env('USER_ACCOUNT_PREFIX').$user->uuid);
                            sleep(3);
                            $userProduct = UserProduct::where('user_id', $user_id)->where('product_id', $promotion->product_id)->first();
                        }

                        $res = resolve(JK::class)->getLoginUrl($userProduct->member_id, $bonus_amount, $userProduct->user->store->jk_agent_id, false);
                        if (isset($res['status']) || $res['status'] == true) {
                            CreditTransaction::insertTransaction($user_id, $internalID, $user_id, $creditName, $bonus_amount, 'promotion-bonus-payout', $belongId, $batchId, null, date('Y-m-d H:i:s'), null, null, null, $upromotion->id);
                        }
                    }
                });

                abort(200, 'User Promotion Created Successfully');
                break;
        }

        return null;
    }

    public static function getUserPromotionSummary(array $params = [])
    {
        $fromDate = $params['from_date'] ?? null;
        $toDate = $params['to_date'] ?? null;
        $storeId = $params['store_id'] ?? null;
        $status = $params['status'] ?? null;

        $data = self::with('user')
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('created_at'), '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where(DB::raw('created_at'), '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(isset($storeId), function ($q) use ($storeId) {
                return $q->whereRelation('user', 'storeId', $storeId);
            })
            ->when(isset($status), function ($q) use ($status) {
                return $q->where('status', $status);
            })
            ->groupBy(function ($item) {
                return $item->created_at->format('d-M-y');
            })
            ->selectRaw('sum(bonus_amount) as total_bonus_amount, sum(game_return_amount) as total_return_amount, IF(game_return_amount > max_withdraw_amount, max_withdraw_amount, game_return_amount) as actual_withdraw_amount, user_id, created_at')
            ->get()
            ->map(function ($q) {
                return [
                    'date' => $q->created_at->format('d-M-y'),
                    'total_bonus_amount' => $q->total_bonus_amount,
                    'total_return_amount' => $q->total_return_amount,
                    'actual_withdraw_amount' => $q->actual_withdraw_amount,
                ];
            })
            ->toArray();

        return $data;
    }

    public static function getTotalAchievedTurnoverByUserId($userId)
    {
        $total = self::where('user_id', $userId)
            ->sum('achieved_turnover');

        return $total;
    }

    public static function getTodayUserPromotionCountByUserIdAndPromotionId($userId, $promotionId)
    {
        $count = self::with(['promotion' => function ($q) {
            $q->where('is_deposit', true);
        }])
            ->where('user_id', $userId)
            ->whereDate('created_at', today())
            ->where('promotion_id', $promotionId)
            ->count();

        return $count;
    }

    public static function getUserPromotionCountByUserIdAndPromotionId($userId, $promotionId)
    {
        $count = self::where('user_id', $userId)
            ->where('promotion_id', $promotionId)
            ->count();

        return $count;
    }

    /**
     * Scope
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::$status['completed']);
    }
}
