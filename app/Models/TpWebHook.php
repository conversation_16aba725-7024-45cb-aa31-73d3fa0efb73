<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TpWebHook extends Model
{
    protected $table = 'tp_webhook';

    protected $hidden = [
    ];

    protected $fillable = [
        'table_type',
        'table_id',
        'type',
        'platform',
        'endpoint',
        'params',
        'req_data',
        'res_data',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $type = [
        'verify_transaction' => 1,
        'callback/deposit' => 2,
        'callback/withdraw' => 3,
        'balance-promoid' => 4,
    ];
    
    public static $tableType = [
        'deposit' => 1,
        'withdraw' => 2,
    ];
}
