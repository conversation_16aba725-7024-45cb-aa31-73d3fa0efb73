<?php

namespace App\Models;

use App\Traits\CacheTrait;
use App\Traits\DateTrait;
use App\Traits\DecimalTrait;
use App\Traits\GenerateNumberTrait;
use App\Traits\S3Trait;
use App\Traits\Sms;
use App\Traits\TreeTrait;
use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;

class ProductSetting extends Model
{
    use CacheTrait;
    use DateTrait;

    protected $table = 'product_setting';

    protected $hidden = [];

    protected $fillable = [
        'id',
        'product_id',
        'name',
        'value',
        'type',
        'reference',
        'description',
        'deleted_at',
    ];
}
