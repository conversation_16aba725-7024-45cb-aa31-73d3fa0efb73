<?php

namespace App\Models;

use App\Services\GameProvider\CQ9;
use App\Services\GameProvider\GSC;
use App\Services\GameProvider\JILI;
use App\Services\GameProvider\JK;
use App\Services\GameProvider\KISS;
use App\Services\GameProvider\MEGA;
use App\Services\GameProvider\MT;
use App\Services\GameProvider\PUSSY;
use App\Traits;
use App\Traits\DecimalTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;

class UserProduct extends Model
{
    protected $table = 'user_product';

    protected $hidden = [];

    public static $status = [
        'pending' => 0,
        'success' => 1,
        'failed' => 2,
    ];

    public static $walletType = [
        'Main' => 'Main',
    ];

    protected $fillable = [
        'user_id',
        'product_id',
        'wallet_type',
        'product_user_id',
        'member_id',
        'balance',
        'transfer_in',
        'transfer_out',
        'profit_loss',
        'status',
        'updated_at',
        'created_at',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public static function subscribe($params, $party = null)
    {
        if (! isset($params['user_id'])) {
            abort(400, json_encode(['invalid subscribe']));
        }
        $datetime = $params['datetime'] ?? date('Y-m-d H:i:s');

        // Get Maintenance Setting
        $maintenanceStg = SystemSetting::selectRaw('value AS start_time, reference AS end_time')->where('name', 'maintenanceTime')->first();

        if (isset($params['product_id'])) {
            $productIDs = Product::select(['id', 'code'])->where('id', $params['product_id'])->orderBy('priority')->orderBy('id')->get()->pluck('id', 'code')->toArray();
        } else {
            $productIDs = Product::select(['id', 'code'])->orderBy('priority')->orderBy('id')->get()->pluck('id', 'code')->toArray();
        }

        foreach ($productIDs as $productCode => $productID) {
            $sameParty = false;
            if (isset($party)) {
                if ($productCode == $party) {
                    $sameParty = true;
                }
            }
            $product = Product::where('id', $productID)->first();
            $account = ($product->aggregator == null ? env('USER_ACCOUNT_PREFIX') : '').$params['account'];

            $exists = self::where(['product_id' => $productID, 'member_id' => $account, 'wallet_type' => UserProduct::$walletType['Main']])->first();
            if (isset($exists)) {
                self::create([
                    'user_id' => $params['user_id'],
                    'product_id' => $exists->product_id,
                    'member_id' => $exists->member_id,
                    'status' => $exists->status,
                    'wallet_type' => $exists->wallet_type,
                    'created_at' => $datetime,
                ]);

                continue;
            }
            self::create([
                'user_id' => $params['user_id'],
                'product_id' => $productID,
                'member_id' => $account,
                'status' => $sameParty ? (self::$status['success']) : (self::$status['pending']),
                'wallet_type' => UserProduct::$walletType['Main'],
                'created_at' => $datetime,
            ]);

            $delayTime = null;
            if (! empty($maintenanceStg)) {
                $startTime = date('Y-m-d '.$maintenanceStg->start_time);
                $endTime = date('Y-m-d '.$maintenanceStg->end_time);
                if ((strtotime($datetime) >= strtotime($startTime)) && (strtotime($datetime) <= strtotime($endTime))) {
                    $delayTime = strtotime($endTime) - strtotime(($datetime)) + 1;
                }
            }

            // if from 3rd party A, dont call A again
            if ($sameParty) {
                continue;
            }
            // TODO: Add When Register
            $job = new \App\Jobs\ProcessMerchant(json_encode($params + [
                'company' => $party,
                'product_id' => $productID,
                'ip' => \Request::header('ip-web') ?? \Request::ip(),
                'curl_type' => 'register',
            ]));
            dispatch($job)->onQueue('merchant')->delay(now()->addSecond(($delayTime ?? 0)));
        }

        return true;
    }

    public static function subscribeGame($userId, $productId, $account)
    {
        $count = self::where('user_id', $userId)
            ->where('product_id', $productId)
            ->count();

        if ($count > 0) {
            return;
        }

        self::firstOrCreate([
            'user_id' => $userId,
            'product_id' => $productId,
            'member_id' => $account,
            'status' => self::$status['success'],
            'wallet_type' => self::$walletType['Main'],
        ]);
    }

    public static function productAutoLogin($params, $needUserProduct = true)
    {
        $userId = $params['user_id'];
        $productId = $params['product_id'];
        $serviceId = $params['service_id'];

        $user = User::find($userId);

        if (! isset($params['user_id']) || ! isset($params['product_id'])) {
            abort(400, json_encode(['Invalid Data']));
        }

        if ($needUserProduct) {
            $userProduct = self::with(['product', 'user'])->where('user_id', $params['user_id'])->where('product_id', $params['product_id'])->first();

            if (! $userProduct) {
                switch ($productId) {
                    case JILI::$productId:
                        resolve(JILI::class)->createMember($user);
                        break;

                    case GSC::$productId:
                        resolve(GSC::class)->createMember($user);
                        break;

                    case KISS::$productId:
                        resolve(KISS::class)->addUser($user->uuid);
                        break;

                    case MT::$productId:
                        resolve(MT::class)->createMember($user);
                        break;

                    case PUSSY::$productId:
                        resolve(PUSSY::class)->addUser($user->uuid);
                        break;
                }

                $userProduct = self::with(['product', 'user'])->where('user_id', $params['user_id'])->where('product_id', $params['product_id'])->first();
            }

            if (! isset($userProduct->member_id) || empty($userProduct->member_id)) {
                abort(400, json_encode(['Invalid Login']));
            }

            if (! isset($userProduct->product)) {
                abort(400, json_encode(['Invalid Product']));
            }
            $product = $userProduct->product;

            $gsc = resolve(GSC::class);
            $service = Services::with('game_setting')->where('id', $params['service_id'])->first();
            if (! isset($service)) {
                abort(400, json_encode(['Invalid Service']));
            }

            if ($product->aggregator != null) {
                switch ($product->aggregator) {
                    case 'GSC':
                        $gameId = Services::with('game_setting')->find($serviceId)->game_setting->game_id;
                        $res = $gsc->getGameUrl($userProduct->member_id, $gameId, 'SL', $product->provider_code);
                        if (! isset($res['status']) || $res['status'] != true) {
                            abort(400, json_encode(['msg' => ['Login Failed']]));
                        }
                        break;
                }
            } else {

                switch ($product->name) {
                    case 'TK8':
                        $wtList = ['WEBSITE'];
                        $walletType = $params['wallet_type'] ?? $wtList[0];
                        $walletType = strtoupper($walletType);
                        if (! in_array($walletType, $wtList)) {
                            $walletType = $wtList[0];
                        }

                        $params = [
                            'account' => $userProduct->member_id,
                            'platform' => $walletType,
                            'service_id' => $params['service_id'],
                            'is_game' => $service?->is_game ?? false,
                            'ip' => \Request::header('ip-web') ?? \Request::ip(),
                        ];

                        $res = Traits\OCTK8Trait::postOC($params, 'auto_login');
                        if (! isset($res['status']) || $res['status'] != true) {
                            abort(400, json_encode(['msg' => ['Login Failed']]));
                        }

                        sleep(1);

                        if (isset($res['data']['auth_code']) && $service->is_game) {
                            $params['auth_code'] = $res['data']['auth_code'];
                            $params['game'] = $service->game_setting->game;
                            $params['gameID'] = $service->game_setting->game_id;
                            $params['gameCategory'] = $service->game_setting->game_category;
                            $params['gameListID'] = $service->game_setting->game_list_id;

                            $res = Traits\OCTK8Trait::postOC($params, 'transfer');
                            if (! isset($res['status']) || $res['status'] != true) {
                                abort(400, json_encode(['msg' => ['Launch Game Failed']]));
                            }
                        }

                        break;
                    case 'MT':
                        $wtList = ['WEBSITE'];
                        $walletType = $params['wallet_type'] ?? $wtList[0];
                        $walletType = strtoupper($walletType);
                        if (! in_array($walletType, $wtList)) {
                            $walletType = $wtList[0];
                        }

                        $mt = resolve(MT::class);
                        $res = $mt->getLoginUrl($userProduct->member_id, $userProduct->member_id);

                        if (! isset($res['status']) || $res['status'] != true) {
                            abort(400, json_encode(['msg' => ['Login Failed']]));
                        }
                        break;

                    case 'CQ9':
                        $cq9 = resolve(CQ9::class);
                        $res = $cq9->getGameUrl($userProduct->user, $params['service_id']);
                        if (! isset($res['status']) || $res['status'] != true) {
                            abort(400, json_encode(['msg' => ['Login Failed']]));
                        }
                        break;

                    case 'JK':
                        $jk = resolve(JK::class);

                        $totalBalance = 0;
                        $jkAgentId = $userProduct->user->store->jk_agent_id;

                        $promotion = $jk->checkPromotion($userProduct->member_id, $jkAgentId);
                        if (isset($promotion) && $promotion['data']['remaining_turnover'] > 0) {
                            $res = $jk->getLoginUrl($userProduct->member_id, $totalBalance, $jkAgentId, true);
                        } else {
                            $prodBalRes = $jk->getWithdraw($userProduct->member_id, $jkAgentId);
                            $totalBalance = $prodBalRes['data']['main_wallet'] + $params['balance'];
                            $res = $jk->getLoginUrl($userProduct->member_id, $totalBalance, $jkAgentId, false);
                        }

                        if (! isset($res['status']) || $res['status'] != true) {

                            $paramsData = [
                                'amount' => $totalBalance,
                                'credit_id' => 1000,
                                'product_name' => $product->name,
                                'product_id' => $userProduct->product_id,
                                'user_id' => $userProduct->user_id,
                                'credit_type' => Credit::first()->type,
                                'curl_type' => 'refund',
                            ];

                            ExTransfer::transferOut($paramsData + [
                                'product_data' => $product,
                                'wallet_data' => [],
                                'exMemberId' => $userProduct->member_id,
                                'wallet_type' => 'Main',
                            ]);

                            if (! $res) {
                                abort(400, json_encode(['msg' => ['Transfer Failed']]));
                            }

                            abort(400, json_encode(['msg' => ['Login Failed']]));
                        }
                        break;

                    case 'JILI':
                        $gameId = Services::with('game_setting')->find($serviceId)->game_setting->game_id;
                        $res = resolve(JILI::class)->getGameUrl($userProduct->member_id, $gameId);
                        if (! isset($res['status']) || $res['status'] != true) {
                            abort(400, json_encode(['msg' => ['Login Failed']]));
                        }
                        break;

                    case 'KISS':
                        $res = resolve(KISS::class)->getCredentials($userProduct->member_id);
                        break;

                    case 'MEGA':
                        $res = resolve(MEGA::class)->getCredentials($userProduct->member_id);
                        break;

                    case 'PUSSY':
                        $res = resolve(PUSSY::class)->getCredentials($userProduct->member_id);
                        break;

                    default:
                        abort(400, json_encode(['msg' => ['Invalid Product']]));
                        break;
                }
            }
        }

        $data['login_link'] = $res['data'] ?? $res['game_url'] ?? null;
        $data['download_link'] = $res['download_url'] ?? null;
        $data['username'] = $res['username'] ?? null;
        $data['password'] = $res['password'] ?? null;
        $data['balance'] = DecimalTrait::setDecimal(0);

        return $data;
    }

    public static function getProductBalance($params)
    {
        if (! isset($params['user_id']) || ! isset($params['product_id'])) {
            abort(400, json_encode(['Invalid Data']));
        }

        $walletType = $params['wallet_type'] ?? null;

        if (isset($walletType)) {
            $userProduct = self::with(['product'])->where('user_id', $params['user_id'])->where('product_id', $params['product_id'])
                ->when(isset($walletType), function ($q) use ($walletType) {
                    return $q->where('wallet_type', $walletType);
                })
                ->first();

            if (empty($userProduct)) {
                $userProduct = self::with(['product'])->where('user_id', $params['user_id'])->where('product_id', $params['product_id'])
                    ->first();
                if (! isset($userProduct->member_id) || empty($userProduct->member_id)) {
                    abort(400, json_encode(['Invalid Login']));
                }

                $userProduct = UserProduct::create([
                    'user_id' => $userProduct->user_id,
                    'product_id' => $userProduct->product_id,
                    'status' => UserProduct::$status['success'],
                    'member_id' => $userProduct->member_id,
                    'wallet_type' => $walletType,
                ]);
            }
        } else {
            $userProduct = self::with(['product'])->where('user_id', $params['user_id'])->where('product_id', $params['product_id'])
                ->first();
        }

        if (! isset($userProduct->member_id) || empty($userProduct->member_id)) {
            abort(400, json_encode(['Invalid Login']));
        }

        if (! isset($userProduct->product)) {
            abort(400, json_encode(['Invalid Product']));
        }
        $product = $userProduct->product;
        $mainWallet = 0;
        $gameWallet = 0;
        switch ($product->name) {
            case 'TK8':
                $res = Traits\OCTK8Trait::postOC(['account' => $userProduct->member_id], 'check_balance');
                if (! isset($res['status']) || $res['status'] != true) {
                    abort(400, json_encode(['Login Failed']));
                }

                if (isset($res['data']) && ! empty($res['data'])) {
                    $mainWallet = isset($res['data']['main_wallet']) && is_numeric($res['data']['main_wallet']) ? $res['data']['main_wallet'] : 0;
                    $gameWallet = isset($res['data']['game_wallet']) && is_numeric($res['data']['game_wallet']) ? $res['data']['game_wallet'] : 0;
                    $lotteryWallet = isset($res['data']['lottery_wallet']) && is_numeric($res['data']['lottery_wallet']) ? $res['data']['lottery_wallet'] : 0;
                }

                if ($userProduct->wallet_type == UserProduct::$walletType['Main']) {
                    $userProduct->balance = $mainWallet + $gameWallet;
                } else {
                    $userProduct->balance = $lotteryWallet;
                }

                $userProduct->save();
                break;
            case 'MT':
                $res = resolve(MT::class)->getBalance($userProduct->user_id);
                if (! isset($res['status']) || $res['status'] != true) {
                    abort(400, json_encode(['Login Failed']));
                }

                if (isset($res['data']) && ! empty($res['data'])) {
                    $mainWallet = isset($res['data']['main_wallet']) && is_numeric($res['data']['main_wallet']) ? $res['data']['main_wallet'] : 0;
                    $gameWallet = isset($res['data']['game_wallet']) && is_numeric($res['data']['game_wallet']) ? $res['data']['game_wallet'] : 0;
                    $lotteryWallet = isset($res['data']['lottery_wallet']) && is_numeric($res['data']['lottery_wallet']) ? $res['data']['lottery_wallet'] : 0;
                }

                if ($userProduct->wallet_type == UserProduct::$walletType['Main']) {
                    $userProduct->balance = $mainWallet + $gameWallet;
                } else {
                    $userProduct->balance = $lotteryWallet;
                }

                $userProduct->save();
                break;

            default:
                abort(400, json_encode(['Invalid Product']));
                break;
        }

        $data['balance'] = (string) ($userProduct->balance);
        $data['wallet_currency'] = $res['data']['wallet_currency'] ?? null;
        $data['last_update'] = Traits\DateTrait::dateFormat(date('Y-m-d H:i:s'));

        return $data;
    }

    public static function getUserProductData($params)
    {
        if (! isset($params['user_id'])) {
            abort(400, json_encode(['Invalid Data']));
        }
        $dateTime = date('Y-m-d H:i:s');

        $userProducts = self::whereRelation('product', 'id', $params['product_id'])
            ->with([
                'product:id,name,image_url',
                'product.productSetting' => function ($q) {
                    $q->select('product_id', 'name', 'value', 'type', 'reference');

                    return $q;
                },
            ])->select(['id', 'product_id', 'member_id', 'balance', 'updated_at'])->where('user_id', $params['user_id'])->where('status', self::$status['success'])->first();

        if (! isset($userProducts->product) || empty($userProducts->product)) {
            $userProducts = [];
        } else {
            unset($productSetting);
            $userProducts->product->productSetting->map(function ($q) use (&$productSetting) {
                $productSetting[$q['name']]['value'] = $q['value'];
                $productSetting[$q['name']]['type'] = $q['type'];
                $productSetting[$q['name']]['reference'] = $q['reference'];

                return $q;
            });
            unset($userProducts->product->productSetting);
            $userProducts->product->product_setting = $productSetting;

            $userProducts->balance = $userProducts['balance'] ?? 0;
            $userProducts->last_update = Traits\DateTrait::dateFormat($userProducts->updated_at);
            $userProducts->product->image_url = env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN').$userProducts->product->image_url;
            $userProducts->product->display_name = Lang::has('lang.'.($userProducts->product->name ?? null)) ? Lang::get('lang.'.($userProducts->product->name ?? null)) : ($userProducts->product->name ?? null);
        }

        // hardcode: do this because only one setting:
        // if not valid, then inverse fromCurrency and toCurrency
        $defaultCredit = Credit::where('code', 'MYR')->first();

        $creditFunction = CreditSetting::with(['credit'])
            ->where([
                'name' => 'is-ex-transferable',
            ])
            ->WhereJsonContains('type', (int) $params['product_id'])
            ->first();
        $userProducts->wallet_currency = $creditFunction->credit->code;

        $isValidFromCurrency = in_array($creditFunction->credit->code, Config('general.valid_convert_from_currency'));

        $currencySetting = CurrencySetting::where('from_currency_id', $isValidFromCurrency ? $creditFunction->credit->currency_id : $defaultCredit->currency_id)
            ->where('to_currency_id', $isValidFromCurrency ? $defaultCredit->currency_id : $creditFunction->credit->currency_id)
            ->orderBy('created_at', 'DESC')
            ->orderBy('id', 'DESC')
            ->get()->first();

        $isFundinable = '0';
        if ($isValidFromCurrency) {
            $fromCurrencyIso = Convert::$outputConversion[$currencySetting->fromCurrency->iso] ?? $currencySetting->fromCurrency->iso;
            $currencyIso = Convert::$outputConversion[$currencySetting->toCurrency->iso] ?? $currencySetting->toCurrency->iso;

            $fromRate = '1';
            $toRate = $currencySetting->convert_out_rate;
        } else {
            $fromCurrencyIso = Convert::$outputConversion[$currencySetting->toCurrency->iso] ?? $currencySetting->toCurrency->iso;
            $currencyIso = Convert::$outputConversion[$currencySetting->fromCurrency->iso] ?? $currencySetting->fromCurrency->iso;

            $fromRate = '1';
            $toRate = $currencySetting->convert_in_rate;
        }

        $externalTransfer = [];
        $availableFunction = explode(',', $creditFunction['reference']);
        foreach (ExTransfer::$type as $typeName => $typeId) {
            $externalTransfer[$typeName] = 0;
            if ($creditFunction['value'] == 1 && $creditFunction['member'] == 1 && in_array($typeId, $availableFunction)) {
                $externalTransfer[$typeName] = 1;
            }
        }

        $credit['id'] = $creditFunction->credit->id ?? 0;
        $credit['name'] = $creditFunction->credit->name ?? 0;
        $credit['display'] = Lang::has('lang.'.($creditFunction->credit->name ?? null)) ? Lang::get('lang.'.($creditFunction->credit->name ?? null)) : ($creditFunction->credit->name ?? null);
        $credit['is-ex-transferable'] = $externalTransfer;
        $credit['balance'] = isset($creditFunction->credit) ? Credit::getBalance($params['user_id'], $creditFunction->credit->name) : '0.00000000';

        $data['product'] = isset($userProducts->id) ? $userProducts : null;

        $credit['from_currency_iso'] = $fromCurrencyIso;
        $credit['to_currency_iso'] = $currencyIso;
        $credit['from_rate'] = Traits\DecimalTrait::setDecimal($fromRate);
        $credit['to_rate'] = Traits\DecimalTrait::setDecimal($toRate);
        $data['credit'] = $credit;

        return $data;
    }

    public static function updateTransferTotal($userID, $productID, $type, $amount = 0, $balance = null)
    {
        if (! isset($userID) || ! isset($productID) || ! isset($type) || $amount <= 0) {
            return false;
        }

        $userProduct = self::where('user_id', $userID)->where('product_id', $productID)->where('wallet_type', UserProduct::$walletType['Main'])->first();
        switch ($type) {
            case 'in':
                $userProduct->increment('transfer_in', $amount);
                break;

            case 'out':
                $userProduct->increment('transfer_out', $amount);
                break;
        }
        if ($balance != null) {
            $userProduct->balance = $balance;
        }
        $userProduct->save();

        return true;
    }

    public static function getUserProductByUserAndProduct($user, $product)
    {
        $gsc = resolve(GSC::class);
        $productId = $product->id;
        $userId = $user->id;
        $userProduct = UserProduct::with('product')->where('user_id', $userId)->where('product_id', $productId)->first();

        if ($productId == JILI::$productId && ! $userProduct) {
            resolve(JILI::class)->createMember($user);
        } elseif ($product->aggregator == GSC::$name) {
            $currentProductId = UserProduct::with(['product', 'user'])->where('user_id', $userId)->whereHas('product', function ($query) {
                $query->where('aggregator', 'GSC');
            })->first();

            $gsc->createMember($user, $currentProductId->product_id ?? -1, $product->id);
        }

        if ($productId == JK::$productId && ! $userProduct) {
            UserProduct::subscribeGame($userId, $productId, env('USER_ACCOUNT_PREFIX').$user->uuid);
        }

        return $userProduct = UserProduct::with('product')->where('user_id', $userId)->where('product_id', $productId)->first();
    }
}
