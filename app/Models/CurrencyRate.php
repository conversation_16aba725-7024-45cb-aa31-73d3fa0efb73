<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use App\Traits;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Arr;
use App\Traits\DateTrait;
use App\Traits\DecimalTrait;
use Illuminate\Database\Eloquent\SoftDeletes;
use Stevebauman\Location\Facades\Location;

class CurrencyRate extends Model
{
    use SoftDeletes;

    protected $table = 'currency_rate';

    protected $hidden = [];

    protected $fillable = [
        'from_currency_id',
        'to_currency_id',
        'rate',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static function convertCurrencyAmount($userID, $toCurrencyID, $amount, $type = null)
    {
        if ((!isset($userID)) || (!isset($toCurrencyID)) || (!isset($amount))) {
            return false;
        }

        $userRes = User::find($userID) ?? null;

        if (!isset($userRes)) {
            return false;
        }

        $currencyID = $userRes->currency_id ?? null;

        if (!isset($currencyID)) {
            return false;
        }

        $usdCurrencyID = Currency::query()
            ->where('iso', 'USD')
            ->first()->id ?? null;

        if (!isset($usdCurrencyID)) {
            return false;
        }

        $currencyRateID = self::query()
            ->select(DB::raw('MAX(id) as id'))
            ->where('from_currency_id', $usdCurrencyID)
            ->where('to_currency_id', $currencyID)
            ->where('deleted_at', null)
            ->first()->id ?? null;

        if (!isset($currencyRateID)) {
            return false;
        }

        $currencyRateRes = self::find($currencyRateID) ?? null;

        if (!isset($currencyRateRes)) {
            return false;
        }

        switch ($type) {
            case 'withdrawal':
                $systemRate = $currencyRateRes->withdrawal_rate;
                break;

            default:
                $systemRate = $currencyRateRes->deposit_rate;
                break;
        }

        if ($currencyID == $toCurrencyID) {
            return $data = [
                "convertedAmount" => $amount,
                "systemRate" => $systemRate,
                "currencyRate" => 1,
            ];
        }

        $usdAmount = ($amount / $systemRate);

        $currencyRateID = self::query()
            ->select(DB::raw('MAX(id) as id'))
            ->where('from_currency_id', $usdCurrencyID)
            ->where('to_currency_id', $toCurrencyID)
            ->where('deleted_at', null)
            ->first()->id ?? null;

        if (!isset($currencyRateID)) {
            return false;
        }

        $currencyRateRes = self::find($currencyRateID) ?? null;

        if (!isset($currencyRateRes)) {
            return false;
        }

        $currencyRate = $currencyRateRes->deposit_rate;

        $convertedAmount = ($usdAmount * $currencyRate);
        $convertedAmount = DecimalTrait::setDecimal($convertedAmount);

        if ($convertedAmount <= 0) {
            return false;
        }

        $data = [
            "systemRate" => $systemRate,
            "currencyRate" => $currencyRate,
            "convertedAmount" => $convertedAmount,
        ];

        return $data;
    }

    public static function preset(array $params = [], $isWithdraw = false, $isConvert = false)
    {
        $iso = $params['iso'] ?? null;
        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        $myrCurrency = Currency::where('iso', 'MYR')->select('id')->first();

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        $toCurrencyIds = null;
        $toCurrencyStatusList = [];
        if ($isConvert) {
            $toCurrencys = Convert::getMYRConvertToCurrency();
            $toCurrencyIds = array_column($toCurrencys, 'id') ?? [];
            $toCurrencyStatusList = [];
            foreach ($toCurrencys as $eachCur) {
                $toCurrencyStatusList[$eachCur['id']] = $eachCur['status'];
            }
        }

        $items = self::query()
            ->with([
                'fromCurrency',
                'toCurrency',
                'updater',
                'currencySettingFiat',
                'fromCurrency.credit.creditSetting' => function ($q) {
                    $q->whereIn('name', ['convert-to']);
                },
                'toCurrency.credit.creditSetting' => function ($q) {
                    $q->whereIn('name', ['convert-to']);
                },
            ])
            ->whereHas('toCurrency', function ($q) use ($iso) {
                // $q->where('disabled', Currency::$disabled['active']);
                $q->when((isset($iso)), function ($q) use ($iso) {
                    $q->where('iso', $iso);
                });
                return $q;
            })
            ->whereHas('fromCurrency', function ($q) use ($isWithdraw, $isConvert, $toCurrencyIds) {
                // $q->where('disabled', Currency::$disabled['active']);
                $q->whereNotIn('iso', ['USDT']);
                if ($isConvert) {
                    $q->whereIn('to_currency_id', $toCurrencyIds);
                }
                return $q;
            })
            ->whereNull('deleted_at')
            ->when(isset($myrCurrency) && !empty($myrCurrency), function ($q) use ($myrCurrency) {
                $q->orderByRaw('CASE WHEN to_currency_id = "5" THEN 1 ELSE 2 END ASC');
            })
            ->orderBy($order_by, $order_sort)
            ->paginate($limit);

        $items->getCollection()->transform(function ($q) use ($isWithdraw, $isConvert, $toCurrencyStatusList) {
            $status = array_search($q->toCurrency->disabled, Currency::$disabled);
            if ($isWithdraw) {
                $res = [
                    'iso' => $q->toCurrency->iso,
                    'country' => $q->toCurrency->country->name,
                    'country_display' => Lang::has('lang.' . $q->toCurrency->country->name) ? Lang::get('lang.' . $q->toCurrency->country->name) : $q->toCurrency->country->name,
                    'rate' => DecimalTrait::setDecimal($q->rate),
                    'withdrawal_rate' => DecimalTrait::setDecimal(isset($q->currencySettingFiat->withdrawal_rate) ? $q->currencySettingFiat->withdrawal_rate : $q->rate),
                    'withdrawal_fee' => DecimalTrait::setDecimal(isset($q->currencySettingFiat->withdrawal_fee) ? $q->currencySettingFiat->withdrawal_fee : 0, false),
                    'withdrawal_min_amount' => DecimalTrait::setDecimal(isset($q->currencySettingFiat->withdrawal_min_amount) ? $q->currencySettingFiat->withdrawal_min_amount : 0, false),
                    'status_id' => $q->toCurrency->disabled,
                    'status_name' => $status,
                    'status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                    'updated_at' => DateTrait::dateFormat((isset($q->currencySettingFiat->updated_at) && ($q->currencySettingFiat->updated_at > $q->created_at)) ? $q->currencySettingFiat->updated_at : $q->created_at),
                    'updated_by' => (isset($q->currencySettingFiat->updated_at) && ($q->currencySettingFiat->updated_at > $q->created_at)) ? ($q->currencySettingFiat->updater->username ?? 'System') : ($q->updater->username ?? 'System'),
                ];
            } else if ($isConvert) {
                $curISO = $q->toCurrency->iso;

                $curISO = Convert::$outputConversion[$curISO] ?? $curISO;
                $statusId = $toCurrencyStatusList[$q->to_currency_id] ?? '0';
                $status = array_search($statusId, CreditSetting::$status);
                $statusDisplay = Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status;

                $res = [
                    'iso' => $curISO,
                    'country' => $q->toCurrency->country->name,
                    'country_display' => Lang::has('lang.' . $q->toCurrency->country->name) ? Lang::get('lang.' . $q->toCurrency->country->name) : $q->toCurrency->country->name,
                    'rate' => DecimalTrait::setDecimal($q->rate, 3),
                    'convert_in_rate' => DecimalTrait::setDecimal(isset($q->currencySettingFiat->convert_in_rate) ? $q->currencySettingFiat->convert_in_rate : $q->rate, 3),
                    'convert_out_rate' => DecimalTrait::setDecimal(isset($q->currencySettingFiat->convert_out_rate) ? $q->currencySettingFiat->convert_out_rate : $q->rate, 3),
                    'convert_in_daily_limit' => DecimalTrait::setDecimal(isset($q->currencySettingFiat->convert_in_daily_limit) ? $q->currencySettingFiat->convert_in_daily_limit : 0, false),
                    'convert_out_daily_limit' => DecimalTrait::setDecimal(isset($q->currencySettingFiat->convert_out_daily_limit) ? $q->currencySettingFiat->convert_out_daily_limit : 0, false),
                    'conversion_min_amount' => DecimalTrait::setDecimal(isset($q->currencySettingFiat->conversion_min_amount) ? $q->currencySettingFiat->conversion_min_amount : 0, false),
                    'conversion_max_amount' => DecimalTrait::setDecimal(isset($q->currencySettingFiat->conversion_max_amount) ? $q->currencySettingFiat->conversion_max_amount : 0, false),
                    'status_id' => $statusId,
                    'status_name' => $status,
                    'status_display' => $statusDisplay,
                    'updated_at' => DateTrait::dateFormat((isset($q->currencySettingFiat->updated_at) && ($q->currencySettingFiat->updated_at > $q->created_at)) ? $q->currencySettingFiat->updated_at : $q->created_at),
                    'updated_by' => (isset($q->currencySettingFiat->updated_at) && ($q->currencySettingFiat->updated_at > $q->created_at)) ? ($q->currencySettingFiat->updater->username ?? 'System') : ($q->updater->username ?? 'System'),
                ];
            } else {
                $res = [
                    'iso' => $q->toCurrency->iso,
                    'country' => $q->toCurrency->country->name,
                    'country_display' => Lang::has('lang.' . $q->toCurrency->country->name) ? Lang::get('lang.' . $q->toCurrency->country->name) : $q->toCurrency->country->name,
                    'rate' => DecimalTrait::setDecimal($q->rate, 3),
                    'sell_rate' => DecimalTrait::setDecimal(isset($q->currencySettingFiat->sell_rate) ? $q->currencySettingFiat->sell_rate : $q->rate, 3),
                    'fee' => DecimalTrait::setDecimal(isset($q->currencySettingFiat->fee) ? $q->currencySettingFiat->fee : 0, false),
                    'min_amount' => DecimalTrait::setDecimal(isset($q->currencySettingFiat->min_amount) ? $q->currencySettingFiat->min_amount : 0, false),
                    'instant_rate' => DecimalTrait::setDecimal(isset($q->currencySettingFiat->instant_rate) ? $q->currencySettingFiat->instant_rate : $q->rate, 3),
                    'instant_disabled' => isset($q->currencySettingFiat->instant_disabled) ? $q->currencySettingFiat->instant_disabled : 0,
                    'normal_rate' => DecimalTrait::setDecimal(isset($q->currencySettingFiat->normal_rate) ? $q->currencySettingFiat->normal_rate : $q->rate, 3),
                    'normal_disabled' => isset($q->currencySettingFiat->normal_disabled) ? $q->currencySettingFiat->normal_disabled : 0,
                    'daily_rate' => DecimalTrait::setDecimal(isset($q->currencySettingFiat->daily_rate) ? $q->currencySettingFiat->daily_rate : $q->rate, 3),
                    'daily_limit' => DecimalTrait::setDecimal(isset($q->currencySettingFiat->daily_limit) ? $q->currencySettingFiat->daily_limit : 0),
                    'status_id' => $q->toCurrency->disabled,
                    'status_name' => $status,
                    'status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                    'updated_at' => DateTrait::dateFormat((isset($q->currencySettingFiat->updated_at) && ($q->currencySettingFiat->updated_at > $q->created_at)) ? $q->currencySettingFiat->updated_at : $q->created_at),
                    'updated_by' => (isset($q->currencySettingFiat->updated_at) && ($q->currencySettingFiat->updated_at > $q->created_at)) ? ($q->currencySettingFiat->updater->username ?? 'System') : ($q->updater->username ?? 'System'),
                ];
            }
            return (object) $res;
        });
        return (new ItemsCollection($items))->toArray();
    }

    public static function getHistory(array $params = [])
    {
        $fromDate = $params['from_date'] ?? Null;
        $toDate = $params['to_date'] ?? Null;

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'currency_rate.created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        $items = self::query()->withTrashed()
            ->with(['toCurrency', 'updater'])
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('DATE(currency_rate.created_at)'), ">=", $fromDate);
                return $q->where(DB::raw('DATE(currency_rate.created_at)'), "<=", $toDate);
            })
            ->whereRelation('toCurrency', 'iso', $params['iso'])
            ->orderBy($order_by, $order_sort)
            ->paginate($limit);

        $items->getCollection()->transform(function ($q) {
            $res = [
                'date' => DateTrait::dateFormat($q->created_at),
                'deposit_rate' => DecimalTrait::setDecimal($q->deposit_rate),
                'withdrawal_rate' => DecimalTrait::setDecimal($q->withdrawal_rate),
                'creator' => isset($q->updater) ? $q->updater->username : null,
            ];

            return (object) $res;
        });
        return (new ItemsCollection($items))->toArray();
    }

    public static function edit(array $params = [])
    {
        $iso = $params['iso'] ?? Null;
        $depositRate = $params['deposit_rate'] ?? Null;
        $withdrawalRate = $params['withdrawal_rate'] ?? Null;

        $currency_id = Currency::where(['iso' => $iso, 'disabled' => 0])->first();
        if (!$currency_id) abort(400, Lang::get('lang.failed'));

        $checkUpdate = self::where(['to_currency_id' => $currency_id->id])->whereNull('deleted_at')->first();
        if ($checkUpdate['deposit_rate'] == $depositRate && $checkUpdate['withdrawal_rate'] == $withdrawalRate) return true;
        self::where(['to_currency_id' => $currency_id->id])->whereNull('deleted_at')->delete();
        self::create([
            'from_currency_id' => Currency::where(['iso' => 'USD', 'disabled' => 0])->first()->id ?? 0,
            'to_currency_id' => $currency_id->id,
            'deposit_rate' => $depositRate,
            'withdrawal_rate' => $withdrawalRate,
            'creator_id' => auth()->user()->id,
        ]);
        return true;
    }

    public static function getCurrencyRate($from = null, $to = null)
    {

        if (!isset($from) || !isset($to)) {
            abort(400, "Invalid Currency...");
        }

        $currencyIDs = Currency::whereIn('iso', [$from, $to])->get()->pluck('id', 'iso')->toArray();
        if (!isset($currencyIDs[$from]) || !isset($currencyIDs[$to])) {
            abort(400, "Currency Not Found...");
        }

        if ($currencyIDs[$from] == $currencyIDs[$to]) {
            $currency['iso'] = $to;
            $currency['rate'] = 1;
            $currency['deposit_rate'] = 1;
            $currency['withdrawal_rate'] = 1;
            return $currency;
        }

        $lastCurrency = self::where(['from_currency_id' => $currencyIDs[$from], 'to_currency_id' => $currencyIDs[$to]])->orderBy('id', 'desc')->first();
        if (!isset($lastCurrency) || empty($lastCurrency)) {
            abort(400, "Invalid Exchange Rates...");
        }

        $currency['iso'] = $to ?? null;
        $currency['rate'] = $lastCurrency->rate ?? 1;
        $currency['deposit_rate'] = $lastCurrency->deposit_rate ?? 1;
        $currency['withdrawal_rate'] = $lastCurrency->withdrawal_rate ?? 1;
        return $currency;
    }

    public static function getCurrency($params = null)
    {

        $maxIDAry = self::selectRaw('MAX(id) AS id')->groupBy('from_currency_id')->groupBy('to_currency_id')->get()->pluck('id')->toArray();

        $currency = null;
        $country = null;
        $validCountry = 0;

        if (!empty($maxIDAry)) {
            $currency = self::with(['toCurrency', 'fromCurrency'])
                ->whereIn('id', $maxIDAry)
                ->whereRelation('toCurrency', 'disabled', 0)
                ->whereRelation('fromCurrency', 'disabled', 0)->get()->map(function ($q) {
                    return [
                        "from" => $q->fromCurrency->iso,
                        "to" => $q->toCurrency->iso,
                        "rate" => $q->rate,
                    ];
                });
        }

        $country = Country::query()
            ->select(['id', 'name', 'country_code', 'iso_code2', 'currency_code'])
            ->with(['getCurrency'])
            ->whereIn('name', Config('general.valid_exchange_country'))
            ->whereRelation('getCurrency', 'disabled', 0)
            ->orderBy('name', 'ASC')
            ->get()
            ->map(function ($q) {
                return [
                    'name' => $q->name,
                    'country_display' => Lang::has('lang.' . $q->name) ? Lang::get('lang.' . $q->name) : $q->name,
                    'iso_code' => $q->iso_code2,
                    'currency_code' => $q->currency_code,
                ];
            });

        if (isset($params['ip_address']) && filter_var($params['ip_address'], FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            $location = Location::get($params['ip_address']);
            if (!empty($location) && ($location->countryCode == 'MY')) {
                $validCountry = 1;
            }
        }

        return [
            "currency" => $currency,
            "country" => $country,
            "validCountry" => $validCountry
        ];
    }

    public static function getMyrCurrencyRate($params = null)
    {
        $limit = $params['limit'] ?? config('app.pagination_rows');
        $maxIDAry = self::selectRaw('MAX(id) AS id')->groupBy('from_currency_id')->groupBy('to_currency_id')->get()->pluck('id')->toArray();
        $items = self::with(['toCurrency', 'fromCurrency'])
            ->whereIn('id', $maxIDAry)
            ->whereRelation('toCurrency', 'disabled', 0)
            ->whereRelation('fromCurrency', 'disabled', 0)
            ->paginate($limit);

        $items->getCollection()->transform(function ($q) {
            return [
                "from" => $q->fromCurrency->iso,
                "to" => $q->toCurrency->iso,
                "rate" => $q->rate,
            ];
        });

        return (new ItemsCollection($items))->toArray();
    }

    public static function getExchangeData($params)
    {
        $currency = null;
        $country = null;
        $userID = $params['user_id'] ?? null;

        $totalTransferAmt = TelexTransfer::selectRaw('SUM(amount) AS totalTransferAmt')
            ->where('user_id', $userID)
            ->whereIn('status', Arr::except(TelexTransfer::$status, ['rejected', 'cancel']))
            ->whereDATE('created_at', date('Y-m-d'))
            ->first()->totalTransferAmt ?? "0";

        $currency = CurrencySetting::with(['toCurrency', 'toCurrency.country' => function ($q) {
            return $q->whereIn('name', Config('general.valid_exchange_country'));
        }, 'fromCurrency'])
            ->where('type', CurrencySetting::$type['fiat'])
            // ->where('normal_disabled',0)
            ->where('disabled', 0)
            ->whereRelation('toCurrency', 'disabled', Currency::$disabled['active'])
            ->whereRelation('fromCurrency', 'iso', 'MYR')
            ->get()
            ->map(function ($q) {
                $normalRate = ($q->normal_rate > 0) ? $q->normal_rate : null;
                $instantRate = ($q->instant_rate > 0) ? $q->instant_rate : null;

                return [
                    "id" => $q->to_currency_id,
                    "country_id" => $q->toCurrency->country->id,
                    "from_currency_iso" => $q->fromCurrency->iso,
                    "currency_iso" => $q->toCurrency->iso,
                    "country_iso_code" => $q->toCurrency->country->iso_code2,
                    "country_name" => $q->toCurrency->country->name,
                    "country_name_display" => Lang::has('lang.' . $q->toCurrency->country->name) ? Lang::get('lang.' . $q->toCurrency->country->name) : $q->toCurrency->country->name,
                    "rate" => ($instantRate ?? $q->sell_rate),
                    "processing_fee" => $q->fee,
                    "daily_limit" => $q->daily_limit,
                    "daily_rate" => $q->daily_rate,
                    "min_order" => $q->min_amount,
                    "normal_disabled" => $q->normal_disabled,
                    "instant_disabled" => $q->instant_disabled
                ];
            })->toArray();

        return [
            "currency" => $currency,
            "total_transfer_amt" => $totalTransferAmt,
            "transfer_type_id" => TelexTransfer::$type['instant-transfer']
        ];
    }

    public function currencySettingFiat()
    {
        $fromID = Currency::where(['iso' => 'MYR', 'disabled' => 0])->first()->id ?? 0;
        return $this->hasOne(CurrencySetting::class, 'to_currency_id', 'to_currency_id')->where('from_currency_id', $fromID)->where('type', CurrencySetting::$type['fiat'])->whereNull('deleted_at');
    }

    public function toCurrency()
    {
        return $this->belongsTo(Currency::class, 'to_currency_id', 'id')->whereNull('deleted_at');
    }

    public function fromCurrency()
    {
        return $this->belongsTo(Currency::class, 'from_currency_id', 'id')->whereNull('deleted_at');
    }

    public function updater()
    {
        return $this->belongsTo(Admin::class, 'creator_id', 'id');
    }
}
