<?php

namespace App\Models;

use App\Jobs\ExportExcel;
use App\Traits\DecimalTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AngpauEvent extends Model
{
    use HasFactory;

    protected $fillable = [
        'promotion_id',
        'name',
        'start_date',
        'end_date',
        'total_participants',
        'pool_amount',
        'pool_amount_display',
        'current_pool_amount',
        'min_amount',
        'max_amount',
        'min_ticket',
        'max_winners',
        'is_active',
    ];

    public static function getCurrentAngpauEvent()
    {
        $angpauEvent = AngpauEvent::where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->where('is_active', true)
            ->orderBy('start_date', 'DESC')
            ->orderBy('end_date', 'DESC')
            ->first();

        if (! $angpauEvent) {
            return null;
        }

        return [
            'id' => $angpauEvent->id,
            'name' => $angpauEvent->name,
            'start_date' => $angpauEvent->start_date,
            'end_date' => $angpauEvent->end_date,
            'total_participants' => $angpauEvent->total_participants,
            'pool_amount_display' => $angpauEvent->pool_amount_display,
            'min_ticket' => $angpauEvent->min_ticket,
            'min_amount' => $angpauEvent->min_amount,
            'max_amount' => $angpauEvent->max_amount,
            'max_winners' => $angpauEvent->max_winners,
        ];
    }

    public static function getPreviousAngpauEvent()
    {
        $angpauEvent = AngpauEvent::where('is_active', true)
            ->orderBy('start_date', 'DESC')
            ->orderBy('end_date', 'DESC')
            ->skip(1)->take(1)
            ->first();

        if (! $angpauEvent) {
            return null;
        }

        return [
            'id' => $angpauEvent->id,
            'name' => $angpauEvent->name,
            'start_date' => $angpauEvent->start_date,
            'end_date' => $angpauEvent->end_date,
            'total_participants' => $angpauEvent->total_participants,
            'pool_amount_display' => $angpauEvent->pool_amount_display,
            'min_ticket' => $angpauEvent->min_ticket,
            'max_winners' => $angpauEvent->max_winners,
        ];
    }

    /**
     * Get participants list for an angpao event
     *
     * @param array $params
     * @return array
     */
    public static function getParticipantsList(array $params = [])
    {
        $event_id = $params['event_id'] ?? null;
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 10;
        $search = $params['search'] ?? '';
        $orderBy = $params['order_by'] ?? 'total_token';
        $orderSort = $params['order_sort'] ?? 'desc';
        $seeAll = $params['see_all'] ?? 0;
        $isExport = isset($params['is_export']) && $params['is_export'] === true;

        // Handle export request - only if not already in export mode
        if (!$isExport && isset($params['export']) && ($params['export'] == 1)) {
            // Make sure export_data is properly structured
            if (!isset($params['export_data']) || !isset($params['export_data']['data'])) {
                // Create default export_data structure if not provided
                $params['export_data'] = [
                    'data' => [
                        'id' => 'ID',
                        'store_name' => 'Store Name',
                        'name' => 'Name',
                        'phone' => 'Phone',
                        'total_token' => 'Total Token',
                        'total_deposit' => 'Total Deposit',
                        'is_selected' => 'Is Selected',
                        'is_joined' => 'Is Joined',
                        'created_at' => 'Created At'
                    ]
                ];
            }

            $params['see_all'] = 1;

            $jobData = $params;
            $jobData["model"] = get_class();
            $jobData["function"] = __FUNCTION__;
            $jobData["module"] = MODULE;
            $jobData["creator_type"] = MODULE;
            $jobData["creator_id"] = $params['user_id'] ?? null;
            $job = new ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, "Export Successfully");
        }

        $params['is_export'] = true;

        $query = UserTicketReward::where('ref_event_id', $event_id)
            ->with(['user:id,name,username,phone_no', 'store:store_id,name']);

        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhereHas('user', function ($userQuery) use ($search) {
                        $userQuery->where('username', 'like', "%{$search}%")
                            ->orWhere('phone_no', 'like', "%{$search}%");
                    })
                    ->orWhereHas('store', function ($storeQuery) use ($search) {
                        $storeQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        $total = $query->count();

        if ($seeAll != 1) {
            $participantsQuery = $query->orderBy($orderBy, $orderSort)
                ->skip(($page - 1) * $limit)
                ->take($limit);
        } else {
            $participantsQuery = $query->orderBy($orderBy, $orderSort);
        }

        // For regular API response, we want to keep the boolean values for frontend
        // For export, we want to convert them to 'Yes' or 'No'
        $participants = $participantsQuery->get()
            ->map(function ($participant) use ($isExport) {
                $isSelected = $isExport ? ($participant->is_selected ? 'Yes' : 'No') : $participant->is_selected;
                $isJoined = $isExport ? ($participant->is_joined ? 'Yes' : 'No') : $participant->is_joined;

                return [
                    'id' => $participant->id,
                    'user_id' => $participant->user_id,
                    'store_name' => $participant->store->name ?? '',
                    'name' => $participant->user->name ?? $participant->name,
                    'phone' => $participant->user->phone_no ?? '',
                    'total_token' => $participant->total_token,
                    'total_deposit' => DecimalTrait::setDecimal($participant->total_deposit),
                    'is_selected' => $isSelected,
                    'is_joined' => $isJoined,
                    'created_at' => $participant->created_at ? $participant->created_at->format('Y-m-d H:i:s') : '',
                ];
            });

        $event = AngpauEvent::find($event_id);

        $participantCount = UserTicketReward::where('ref_event_id', $event->id)->count();

        $meta = [
            'event' => [
                'id' => $event->id,
                'name' => $event->name,
                'start_date' => $event->start_date,
                'end_date' => $event->end_date,
                'total_participants' => $participantCount,
                'max_winners' => $event->max_winners,
            ]
        ];

        $pagination = [
            'current_page' => (int)$page,
            'from' => ($page - 1) * $limit + 1,
            'last_page' => ceil($total / $limit),
            'per_page' => (int)$limit,
            'to' => min($page * $limit, $total),
            'total' => $total
        ];

        return [
            'list' => $participants,
            'pagination' => $pagination,
            'meta' => $meta,
        ];
    }
}
