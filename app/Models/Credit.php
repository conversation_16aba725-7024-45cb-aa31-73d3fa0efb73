<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Auth;
use App\Traits\DecimalTrait;
use App\Traits;
use Illuminate\Support\Arr;
use Illuminate\Database\Eloquent\SoftDeletes;

class Credit extends Model
{
    use SoftDeletes;

    const UPDATED_AT = null;

    protected $table = 'credit';

    protected $hidden = [];

    protected $fillable = [
        'name',
        'type',
        'code',
        'description',
        'dcm',
        'rate',
        'image_name',
        'priority',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public function creditSetting()
    {
        return $this->hasMany(CreditSetting::class, "credit_id", "id");
    }

    public function accountBalance()
    {
        return $this->belongsTo(AccountBalance::class, "id", "credit_id");
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class, "currency_id", "id");
    }

    public static function getBalance($userId, $creditType, $dateTime = null, $excludeSubjectId = null)
    {
        $creditArray = Credit::where(function ($q) use ($creditType) {
            $q->where('name', $creditType);
            return $q;
        })->orderBy('priority', 'ASC')->get()->pluck('name', 'id')->toArray();

        $balance = CreditTransaction::where('user_id', $userId)->whereIN('credit_id', array_keys($creditArray))
            ->when(isset($excludeSubjectId), function ($q) use ($excludeSubjectId) {
                return $q->where('subject_type', '!=', $excludeSubjectId);
            })
            ->when(isset($dateTime), function ($q) use ($dateTime) {
                return $q->where('created_at', '<=', $dateTime);
            })
            ->selectRaw(
                'COALESCE(SUM(
                                            CASE WHEN from_id > 100 then -amount
                                            WHEN to_id > 100 then amount
                                            else 0 END)
                                            ,0
                                        ) as balance'
            )->first();

        return $balance->balance;
    }

    public static function getMultiBalance($userIdAry, $creditType, $dateTime = null)
    {
        $creditArray = Credit::where(function ($q) use ($creditType) {
            $q->where('name', $creditType);
            return $q;
        })->orderBy('priority', 'ASC')->get()->pluck('name', 'id')->toArray();

        $balance = CreditTransaction::whereIN('user_id', $userIdAry)->whereIN('credit_id', array_keys($creditArray))
            ->when(isset($dateTime), function ($q) use ($dateTime) {
                return $q->where('created_at', '<=', $dateTime);
            })
            ->select([
                'user_id',
                DB::raw('COALESCE(SUM(CASE WHEN from_id > 100 then -amount WHEN to_id > 100 then amount else 0 END),0) as balance')
            ])
            ->groupBy('user_id')->get()->pluck('balance', 'user_id');

        return $balance->toArray() ?? 0;
    }

    public static function getWalletList($isDashboard = false, $userId = null)
    {
        $validReturnStg = null;

        if (empty($userId)) {
            $userId =  Auth::user()->id ?? null;
        }

        if ($isDashboard == true) {
            $validReturnStg = ['is-wallet', 'show-transaction-history', 'is-transferable', 'is-fundinable', 'is-withdrawable', 'is-convertible', 'is-exchangable', 'is-ex-transferable'];
        }

        $walletList = Credit::with(['creditSetting' => function ($q) use ($validReturnStg) {
            $q->selectRaw('credit_id,name,value,member,reference');
            return $q->when(isset($validReturnStg), function ($q) use ($validReturnStg) {
                return $q->whereIn('name', $validReturnStg);
            });
        }, 'accountBalance' => function ($q) use ($userId) {
            return $q->where('user_id', $userId);
        }])
            ->whereRelation('creditSetting', function ($q) {
                $q->where('name', 'is-wallet');
                $q->where('value', 1);
                $q->where('member', 1);
            })->get()
            ->map(function ($q) use ($userId) {
                if ($q->name == 'exp') return false; // not display exp
                $res = [
                    "id" => $q->id,
                    "name" => $q->name,
                    "display" =>  Lang::has('lang.' . $q->name) ? Lang::get('lang.' . $q->name) : $q->name,
                    "balance" => DecimalTrait::setDecimal(($q->accountBalance->balance ?? 0)),
                    "iso" => $q->code,
                    "credit_setting" => $q->creditSetting,
                ];
                return $res;
            })
            ->reject(function ($value) {
                return $value === false;
            });

        return array_merge(["wallet_data" => $walletList], ($returnData ?? []));
    }

    public static function getUserWalletDetail($params = [])
    {
        $userDetail = User::getProfile($params['id']) ?? null;
        $rate = CurrencyRate::getCurrencyRate($userDetail['currency_code'], config('users.default_currency'));
        $credit = Credit::with(['creditSetting' => function ($q) {
            $q->where('name', 'like', 'is-%');
        }])->find($params['credit_id']);
        foreach ($credit->creditSetting as &$cs) {
            if ($cs['name'] == "is-fundinable") {
                if ($userDetail['user_type'] == 'demo-account') {
                    $cs->admin = 0;
                    $cs->value = 0;
                    $cs->member = 0;
                }
            }
            $setting[$cs->name] = $cs->admin;
        }

        $walletBalance = self::getBalance($params['id'], $credit->name);

        $userInfo = [
            "full_name" => $userDetail['name'] ?? null,
            "currency_code" => $userDetail['currency_code'] ?? null,
            "username" => $userDetail['username'] ?? null,
        ];

        return [
            "user_detail" => $userInfo,
            "walletBalance" => [
                "amount" => $walletBalance,
                "est" => $walletBalance * $rate['deposit_rate'],
            ],
            "setting" => $setting,
        ];
    }

    public static function getWithdrawalChannelList($params = [])
    {

        // $usdtList = UserWithdrawalAddress::list($params);
        $bankList = UserBank::list(array_merge($params, ['status' => 'active']));
        $creditID = $params['credit_id'] ?? null;
        $sysSetting = SystemSetting::where('type', 'withdrawal-channel')->get()->keyBy('name')->toArray() ?? [];

        if (isset($bankList)) {
            $payment_type['bank'] = [
                "bank_list" => $bankList->map(function ($q) {
                    return Arr::only($q, ['id', 'bank_id', 'bank_name', 'account_name', 'account_no', 'branch', 'swift_code', 'status_display', 'is_primary']);
                }),
                "bank_list_limit" => isset($sysSetting['channel-limit']) ? (string) ($sysSetting['channel-limit']['value']) : null,
                "bank_list_available" => isset($sysSetting['channel-limit']) ? (string) ($sysSetting['channel-limit']['value'] - count($bankList)) : null,
            ];
        }
        if (isset($usdtList)) {
            $payment_type['usdt'] = [
                "usdt_list" => $usdtList,
                "usdt_list_limit" => $maxAcc > 0 ? $maxAcc : null,
                "usdt_limit_available" => $maxAcc > 0 ? $maxAcc - count($usdtList) : null,
            ];
        }

        return [
            'verified_email' => (isset(auth()->user()->email_verified_at)) ? 1 : 0,
            "payment_type" => $payment_type
        ];
    }

    public static function getAllTransactionAmountBySubjectByDay($userId, $subjectType = null, $dateTime = null)
    {
        $dateTime ??= date('Y-m-d');

        $transactionList = [];
        $subjectList = config('subject');
        CreditTransaction::with('creditInfo')
            ->where('user_id', $userId)
            ->where('deleted_at', null)
            ->when(isset($dateTime), function ($q) use ($dateTime) {
                $q->where('created_at', '>=', $dateTime);
                return $q->where('created_at', '<=', $dateTime . " 23:59:59");
            })
            ->when(isset($subjectType), function ($q) use ($subjectType) {
                return $q->whereIn('subject_type', $subjectType);
            })
            ->select([
                DB::raw('ANY_VALUE(user_id) as user_id'),
                'credit_id',
                'subject_type',
                DB::raw('COALESCE(SUM(CASE WHEN from_id > 100 then -amount WHEN to_id > 100 then amount else 0 END),0) as balance')
            ])
            ->groupBy('subject_type', 'credit_id')
            ->get()
            ->map(function ($q) use ($subjectList, &$transactionList) {

                $subject = array_search($q->subject_type, $subjectList);
                $q->subject = $subject;
                $transactionList[$q->credit_id][$q->subject] = $q->balance;
                return $q;
            });
        return $transactionList ?? [];
    }
}
