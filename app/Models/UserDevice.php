<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserDevice extends Model
{
    use SoftDeletes;

    protected $table = 'user_device';

    protected $hidden = [];

    protected $fillable = [
        'user_id',
        'token',
        'status',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $status = [
        "inactive" => 0,
        "active" => 1,
    ];

    /* -------------------------------------------------------------------------- */
    /*                                   Scopes                                   */
    /* -------------------------------------------------------------------------- */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /* -------------------------------------------------------------------------- */
    /*                                Relationships                               */
    /* -------------------------------------------------------------------------- */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public static function updateUserDToken(array $params = [])
    {
        $dateTime = date('Y-m-d H:i:s');
        $date = date('Y-m-d', strtotime($dateTime));
        $userId = $params['user_id'] ?? null;
        $token = $params['token'] ?? null;
        $status = 'inactive';

        $update['token'] = null;
        if (isset($token)) {
            $status = 'active';
            $update['token'] = $token;
        }
        $update['status'] = self::$status[$status];

        self::updateOrCreate(
            [
                "user_id" => $userId,
            ],
            $update
        );

        return true;
    }
}
