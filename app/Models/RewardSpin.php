<?php

namespace App\Models;

use App\Services\BetLogService;
use App\Traits\DecimalTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class RewardSpin extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'type',
        'title',
        'image',
        'percent',
        'percent_deposit',
        'reward_type',
        'min_points',
        'max_points',
        'value',
        'is_first',
        'priority',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
        'is_first' => 'boolean',
    ];

    public static $type = [
        'text' => 0,
        'image' => 1,
    ];

    public static $reward_type = [
        'empty' => 0,
        'point' => 1,
        'prize' => 2,
        'cash' => 3,
        'promotion' => 4,
    ];

    public static function spin($user_id = null, $is_free_spin = false)
    {
        // $userPromotion = UserPromotion::getUserPromotionByUserId($user_id);
        // $promotionIsToday = $userPromotion?->updated_at->isToday() ?? false;
        // if (!$userPromotion || ($userPromotion && !$promotionIsToday)) {
        //     // WARNING: Hardcoded
        //     $reward = RewardSpin::find(10);
        //     $promotion = Promotion::find(1000);
        //     $bonusAmount = DecimalTrait::setDecimal(rand($reward->min_points, $reward->max_points));
        //     return [
        //         'id' => $reward['id'],
        //         'title' => $reward['title'],
        //         'image' => $reward['image'],
        //         'reward_type' => $reward['reward_type'],
        //         'value' => $bonusAmount,
        //         'promotion' => $promotion,
        //         'increment' => ($userPromotion && !$promotionIsToday) ? true : false,
        //     ];
        // }

        $prizes = self::where('status', true)->where('percent', '>', 0)->get();
        $percentage = rand(1, 100);

        $reward = null;
        foreach ($prizes as $prize) {
            if ($prize->percent >= $percentage) {
                $reward = $prize;
                break;
            }
        }

        $gotDeposit = Deposit::where('user_id', $user_id)->where('status', Deposit::$status['approved'])->whereDate('approved_at', Carbon::yesterday()->format('Y-m-d'))->exists() ?? false;
        if ($gotDeposit) {
            foreach ($prizes as $prize) {
                if ($prize->percent_deposit >= $percentage) {
                    $reward = $prize;
                    break;
                }
            }
        }

        if ($user_id) {
            $yesterdayLoss = 0; // (new BetLogService)->getYesterdayUserLossByUserId($user_id);
            $user_reward = UserReward::where('user_id', $user_id)->first();

            if ($is_free_spin && $yesterdayLoss >= 100) {
                $reward = $prizes->first(function ($item) {
                    return $item->is_first == true;
                });

                return [
                    'id' => $reward->id,
                    'title' => $reward->title,
                    'image' => isset($reward->image) ? env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $reward->image : null,
                    'reward_type' => $reward->reward_type,
                    'value' => mt_rand(5.88 * 100, 8.88 * 100) / 100,
                ];
            }

            if (isset($user_reward) && $user_reward->total_spin == 0) {
                $reward = $prizes->first(function ($item) {
                    return $item->is_first == true;
                });

                return [
                    'id' => $reward->id,
                    'title' => $reward->title,
                    'image' => isset($reward->image) ? env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $reward->image : null,
                    'reward_type' => $reward->reward_type,
                    'value' => mt_rand($reward->min_points * 100, $reward->max_points * 100) / 100,
                ];
            }
        }

        if ($reward == null) {
            // get max percentage of prize
            $reward = $prizes->sortByDesc('percent')->first();
        }

        if (! isset($reward) && $reward == null) {
            return [];
        }

        if ($reward->reward_type == self::$reward_type['point']) {
            return [
                'id' => $reward->id,
                'title' => $reward->title,
                'image' => isset($reward->image) ? env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $reward->image : null,
                'reward_type' => $reward->reward_type,
                'value' => rand($reward->min_points, $reward->max_points),
            ];
        } elseif ($reward->reward_type == self::$reward_type['cash']) {
            return [
                'id' => $reward->id,
                'title' => $reward->title,
                'image' => isset($reward->image) ? env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $reward->image : null,
                'reward_type' => $reward->reward_type,
                'value' => mt_rand($reward->min_points * 100, $reward->max_points * 100) / 100,
            ];
        } else {
            return [
                'id' => $reward->id,
                'title' => $reward->title,
                'image' => isset($reward->image) ? env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $reward->image : null,
                'reward_type' => $reward->reward_type,
                'value' => $reward->value,
            ];
        }
    }

    public static function dummySpin()
    {
        $percentage = rand(1, 100);
        $prizes = self::inRandomOrder()->where('status', true)->get();
        $zeroChancePrize = self::inRandomOrder()->where('status', true)->where('percent', 0)->first();

        $reward = null;
        foreach ($prizes as $prize) {
            if ($prize->percent >= $percentage) {
                $reward = $prize;
                break;
            }
        }

        if ($percentage <= 10) {
            $reward = $zeroChancePrize;
        }

        if ($reward == null) {
            $reward = $prizes->sortByDesc('percent')->first();
        }

        if ($reward->reward_type == self::$reward_type['point']) {
            return [
                'id' => $reward->id,
                'title' => $reward->title,
                'image' => isset($reward->image) ? env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $reward->image : null,
                'reward_type' => $reward->reward_type,
                'value' => rand($reward->min_points, $reward->max_points),
            ];
        } elseif ($reward->reward_type == self::$reward_type['cash']) {
            return [
                'id' => $reward->id,
                'title' => $reward->title,
                'image' => isset($reward->image) ? env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $reward->image : null,
                'reward_type' => $reward->reward_type,
                'value' => mt_rand($reward->min_points * 100, $reward->max_points * 100) / 100,
            ];
        } else {
            return [
                'id' => $reward->id,
                'title' => $reward->title,
                'image' => isset($reward->image) ? env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $reward->image : null,
                'reward_type' => $reward->reward_type,
                'value' => $reward->value,
            ];
        }
    }
}
