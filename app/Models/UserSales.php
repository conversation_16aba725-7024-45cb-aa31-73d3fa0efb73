<?php

namespace App\Models;

use App\Traits\DecimalTrait;
use App\Traits\TreeTrait;
use App\Traits;
use App\Models;
use App\Models\CreditTransaction;
use App\Models\TransactionLogs;
use App\Models\BankOrderLogs;
use App\Http\Resources\ItemsCollection;
use App\Models\User;
use App\Models\Credit;
use App\Models\TreeSponsor;
use App\Models\WalletTransactionLogs;
use App\Models\Deposit;
use Carbon\Carbon;
use Illuminate\Support\Facades\Lang;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserSales extends Model
{
    use TreeTrait;
    use DecimalTrait;

    protected $table = 'user_sales';

    protected $hidden = [];

    protected $fillable = [
        'user_id',
        'sponsor_id',
        'downline_count',
        'direct_downline_count',
        'own_sales',
        'group_sales',
        'direct_sales',
        'own_transfer',
        'group_transfer',
        'own_turnover',
        'group_turnover',
        'data_json',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function sponsor()
    {
        return $this->belongsTo(User::class, 'sponsor_id', 'id');
    }

    public function activeSponsor()
    {
        return $this->hasMany(self::class, 'sponsor_id', 'user_id');
    }

    public function newRecruitment()
    {
        return $this->hasMany(User::class, 'sponsor_id', 'user_id');
    }

    public static function updateSales($params = [], $saleType = null)
    {
        $dateTime = date('Y-m-d H:i:s');

        if (empty($params)) {
            return false;
        }

        if (empty($saleType)) {
            return false;
        }

        switch ($saleType) {
            case 'package':
                $ownSales = 'own_sales';
                $groupSales = 'group_sales';
                $directSales = 'direct_sales';
                break;

            case 'tt-transfer':
                $ownSales = 'own_transfer';
                $groupSales = 'group_transfer';
                break;

            case 'oc-bet':
                $ownSales = 'own_turnover';
                $groupSales = 'group_turnover';
                break;
        }
        $userID = $params['user_id'];
        $bonusValue = isset($params['bonus_value']) ? $params['bonus_value'] : 0;
        $sponsorID = isset($params['sponsor_id']) ? $params['sponsor_id'] : null;
        $moduleType = isset($params['module_type']) ? $params['module_type'] : null;

        if (!$sponsorID) {
            $user = User::find($userID);
            $sponsorID = $user->sponsor_id;

            if (!$sponsorID) {
                return false;
            }
        }

        $sales = UserSales::where('user_id', $userID)->first();

        // Update own sales
        if (empty($sales)) {
            // Create
            $insert = [
                'user_id' => $userID,
                'sponsor_id' => $sponsorID,
                $ownSales => $bonusValue,
                'product_id' => $params['product_id'] ?? 0,
            ];
            $sales = UserSales::create($insert);
        } else {
            // Update
            if (isset($params['product_id'])) {
                $sales->update(['product_id' => $params['product_id']]);
            }
            $sales->increment($ownSales, $bonusValue);
            $sales->save();
        }

        // Check direct
        $directUpline = 1;

        // Update group sales
        while ($sponsorID) {
            $uplineSales = UserSales::where('user_id', $sponsorID)->first();

            $uplineSalesID = $uplineSales->id;
            $sponsorID = $uplineSales->sponsor_id;

            switch ($moduleType) {
                case 'register':
                    $uplineSales->increment('downline_count');

                    if ($directUpline) {
                        $uplineSales->increment('direct_downline_count');
                    }
                    break;
            }

            if ($bonusValue > 0) {
                $uplineSales->increment($groupSales, $bonusValue);

                if (isset($directSales) && $directUpline) {
                    $uplineSales->increment($directSales, $bonusValue);
                }
            }

            $uplineSales->save();

            $directUpline = 0;
        }

        return true;
    }

    public static function getUserSales($userIDAry = "")
    {
        $userIDAry = isset($userIDAry) ? $userIDAry : [];
        $userSalesAry = self::select("user_id", "own_sales", "group_sales")
            ->when($userIDAry, function ($query) use ($userIDAry) {
                $query->whereIn('user_id', $userIDAry);
            })
            ->get()
            ->keyBy("user_id")
            ->toArray();
        return $userSalesAry;
    }

    public static function moveSales($userId, $moveType, $dateTime = null)
    {
        $validType = array('decrease', 'increase');
        if (!$dateTime) {
            $dateTime = date('Y-m-d H:i:s');
        }

        if (!$userId) {
            return false;
        }

        if (!in_array($moveType, $validType)) {
            return false;
        }

        $userSalesRes = self::where('user_id', $userId)->lockForUpdate()->first();

        $uplineIDArray = TreeSponsor::getUplines($userId, null, false);

        foreach ($uplineIDArray as $uplineID) {
            $uplineSales = UserSales::where('user_id', $uplineID)->lockForUpdate()->first();
            $group_sales = DecimalTrait::setDecimal((($userSalesRes->group_sales ?? 0) + ($userSalesRes->own_sales ?? 0)));
            $group_transfer = DecimalTrait::setDecimal((($userSalesRes->group_transfer ?? 0) + ($userSalesRes->own_transfer ?? 0)));
            $group_turnover = DecimalTrait::setDecimal((($userSalesRes->group_turnover ?? 0) + ($userSalesRes->own_turnover ?? 0)));

            $downline = DecimalTrait::setDecimal((($userSalesRes->downline_count ?? 0) + 1));

            switch ($moveType) {
                case 'decrease':
                    // Update Sales

                    // Group sales
                    $uplineSales->decrement('group_sales', $group_sales);

                    $uplineSales->decrement('group_transfer', $group_transfer);

                    $uplineSales->decrement('group_turnover', $group_turnover);

                    $uplineSales->decrement('downline_count', $downline);

                    if ($uplineID == $userSalesRes->sponsor_id) {

                        // Direct sales
                        $uplineSales->decrement('direct_sales', ($userSalesRes->own_sales ?? 0));

                        $uplineSales->decrement('direct_downline_count', 1);
                    }

                    break;

                case 'increase':
                    // Update Sales

                    // Group sales
                    $uplineSales->increment('group_sales', $group_sales);

                    $uplineSales->increment('group_transfer', $group_transfer);

                    $uplineSales->increment('group_turnover', $group_turnover);

                    $uplineSales->increment('downline_count', $downline);

                    if ($uplineID == $userSalesRes->sponsor_id) {

                        // Group sales
                        $uplineSales->increment('direct_sales', ($userSalesRes->own_sales ?? 0));

                        $uplineSales->increment('direct_downline_count', 1);
                    }

                    break;
            }
            $uplineSales->save();
        }
        return true;
    }

    public static function getSalesSummary($params = [])
    {
        if (!isset($params['from_date']) || empty($params['from_date'])) {
            return [];
        }

        if (!isset($params['from_date']) || empty($params['from_date'])) {
            $firstUser = User::whereIn('user_type', [User::$userType['user-account']])->first();
            $fromDate = date("Y-m-d", strtotime($firstUser->created_at));
        } else {
            $fromDate = $params['from_date'];
        }

        if (!isset($params['to_date']) || empty($params['to_date'])) {
            $toDate = date("Y-m-d");
        } else {
            $toDate = $params['to_date'];
        }

        $username = $params['username'] ?? null;
        if (isset($params['export']) && ($params['export'] == 1)) {
            $params["model"] = get_class() ?? null;
            $params["function"] = __FUNCTION__;
            ExportReport::exportReport($params);
        }

        $fromDateFilter = $fromDate;
        $toDateFilter = $toDate;

        $seeAll = $params['see_all'] ?? null;
        $startDate = $toDate;
        $dateRange = [];
        $tableTotal = [];
        $leaderDownlines = null;
        $storeId = $params['store_id'] ?? null;

        if (isset($params['leader_username']) && !empty($params['leader_username'])) {
            $leaderUser = User::where('username', $params['leader_username'])->first();
            if (isset($leaderUser) && !empty($leaderUser)) {
                $leaderTree = $leaderUser->treeSponsor;
                $donwlines = TreeSponsor::where("trace_key", 'LIKE', $leaderTree->trace_key . "%")->pluck('user_id', 'id');
                if (!empty($donwlines)) {
                    $leaderDownlines = $donwlines->toArray();
                }
            }

            if (empty($donwlines)) {
                $data['list'] = $dateRange;
                if (!isset($seeAll) || $seeAll == 0) {
                    $data['pagination'] = array(
                        "current_page" => 1,
                        "from" => null,
                        "last_page" => 1,
                        "per_page" => (int) ($params['limit'] ?? config('app.pagination_rows')),
                        "to" => null,
                        "total" => 0
                    );
                }
                $data['meta'] = null;

                return (object) $data;
            }
        }

        $memberID = $params['member_id'] ?? null;
        $username = $params['phone_no'] ?? null;

        // Manual pagination
        $page = 1;
        $limit = 0;
        $fromRow = 0;
        $toRow = INF;
        $lastPage = 1;
        if (!isset($seeAll) || $seeAll == 0) {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? config('app.pagination_rows');
            $fromDateTS = strtotime(date("Y-m-d 00:00:00", strtotime($fromDate)));
            $toDateTS = strtotime(date("Y-m-d 00:00:00", strtotime($toDate)));
            $totalRow = ($toDateTS - $fromDateTS) / (24 * 60 * 60) + 1;
            $fromRow = (($page - 1) * $limit) + 1;
            $toRow = $page * $limit;
            $lastPage = ceil(($totalRow / $limit));
            if ($page == $lastPage) {
                $toRow = $totalRow;
            }
        }

        $totalCount = 0;
        $initTable = [
            "register" => DecimalTrait::setDecimal(0),
            "telex_transfer" => DecimalTrait::setDecimal(0),
            "transfer" => DecimalTrait::setDecimal(0),
            "wallet_to_card" => DecimalTrait::setDecimal(0),
            "card_to_wallet" => DecimalTrait::setDecimal(0),
            "withdrawal" => DecimalTrait::setDecimal(0),
            "deposit" => [
                'manual-bank' => DecimalTrait::setDecimal(0),
                'online-bank' => DecimalTrait::setDecimal(0),
                'onepay-online-bank' => DecimalTrait::setDecimal(0),
                'crypto' => DecimalTrait::setDecimal(0),
                'ewallet' => DecimalTrait::setDecimal(0),
                'onepay-ewallet' => DecimalTrait::setDecimal(0),
                'rapidpay-online-bank' => DecimalTrait::setDecimal(0),
                'wepay-online-bank' => DecimalTrait::setDecimal(0),
                'total' => DecimalTrait::setDecimal(0),
            ],
            // "pos" => [
            //     'deposit' => DecimalTrait::setDecimal(0),
            //     'withdrawal' => DecimalTrait::setDecimal(0),
            // ],
        ];

        $summary = $tableTotal = $initTable;
        while (strtotime($startDate) >= strtotime($fromDate)) {
            if (strtotime($startDate) > time()) {
                $startDate = date("Y-m-d", strtotime($startDate . " -1 day"));
                $toDate = $startDate;
                continue;
            }

            $totalCount++;
            if ($totalCount < $fromRow) {
                $startDate = date("Y-m-d", strtotime($startDate . " -1 day"));
                $toDate = $startDate;
                continue;
            }

            $dateRange[$startDate]['date'] = Traits\DateTrait::dateFormat($startDate, false);
            $dateRange[$startDate] = $dateRange[$startDate] + $initTable;
            if ($totalCount >= $toRow) {
                $fromDate = $startDate;
                break;
            }
            $startDate = date("Y-m-d", strtotime($startDate . " -1 day"));
        }

        $depositRow = Deposit::with('userDetail')->where('status', Deposit::$status['approved'])
            ->whereRelation('userDetail.store', 'id', '!=', 1)
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('userDetail.store', 'id', $storeId);
            })
            ->when(isset($memberID), function ($query) use ($memberID) {
                $query->whereRelation('userDetail', 'member_id', '=', $memberID);
            })
            ->when(isset($username), function ($query) use ($username) {
                $query->whereRelation('userDetail', 'username', '=', $username);
            })
            ->when($leaderDownlines, function ($query) use ($leaderDownlines) {
                $query->whereIn('user_id', $leaderDownlines);
            });
        $summaryDeposit = clone $depositRow;
        $depositRow = $depositRow->where("status", Deposit::$status['approved'])
            ->where("approved_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDate)))
            ->where("approved_at", "<=", date("Y-m-d 23:59:59", strtotime($toDate)))
            ->get();

        foreach ($depositRow as $deposit) {
            $depositDate = date("Y-m-d", strtotime($deposit->approved_at));
            $depositType = array_search($deposit->type, Deposit::$type);
            $dateRange[$depositDate]['deposit'][$depositType] = DecimalTrait::setDecimal($dateRange[$depositDate]['deposit'][$depositType] + ($deposit->receivable_amount ?? 0));
            $tableTotal['deposit'][$depositType] = DecimalTrait::setDecimal($tableTotal['deposit'][$depositType] + ($deposit->receivable_amount ?? 0));

            $dateRange[$depositDate]['deposit']['total'] = DecimalTrait::setDecimal($dateRange[$depositDate]['deposit']['total'] + ($deposit->receivable_amount ?? 0));
            $tableTotal['deposit']['total'] = DecimalTrait::setDecimal($tableTotal['deposit']['total'] + ($deposit->receivable_amount ?? 0));
        }

        $totalDeposit = $summaryDeposit->selectRaw("type, SUM(receivable_amount) AS receivable_amount")
            ->where("approved_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDateFilter)))
            ->where("approved_at", "<=", date("Y-m-d 23:59:59", strtotime($toDateFilter)))
            ->groupBy("type")
            ->get();
        foreach ($totalDeposit as $tDeposit) {
            $depositType = array_search($tDeposit->type, Deposit::$type);
            $summary['deposit'][$depositType] = DecimalTrait::setDecimal($tDeposit->receivable_amount);
            $summary['deposit']['total'] = DecimalTrait::setDecimal(($summary['deposit']['total'] + $tDeposit->receivable_amount));
        }

        $withdrawalRow = Withdrawal::with('user')->where('status', Withdrawal::$status['approved'])
            ->whereRelation('user.store', 'id', '!=', 1)
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('user.store', 'id', $storeId);
            })
            ->when(isset($memberID), function ($query) use ($memberID) {
                $query->whereRelation('user', 'member_id', '=', $memberID);
            })
            ->when(isset($username), function ($query) use ($username) {
                $query->whereRelation('user', 'username', '=', $username);
            })
            ->when($leaderDownlines, function ($query) use ($leaderDownlines) {
                $query->whereIn('user_id', $leaderDownlines);
            });
        $summaryWithdrawal = clone $withdrawalRow;
        $withdrawalRow = $withdrawalRow->where("updated_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDate)))
            ->where("updated_at", "<=", date("Y-m-d 23:59:59", strtotime($toDate)))
            ->get();

        foreach ($withdrawalRow as $withdrawal) {
            $withdrawalDate = date("Y-m-d", strtotime($withdrawal->updated_at));
            $dateRange[$withdrawalDate]['withdrawal'] = DecimalTrait::setDecimal($dateRange[$withdrawalDate]['withdrawal'] + ($withdrawal->amount ?? 0));
            $tableTotal['withdrawal'] = DecimalTrait::setDecimal($tableTotal['withdrawal'] + ($withdrawal->amount ?? 0));
        }
        $totalWithdrawal = $summaryWithdrawal->where("updated_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDateFilter)))
            ->where("updated_at", "<=", date("Y-m-d 23:59:59", strtotime($toDateFilter)))
            ->sum('amount');
        $summary['withdrawal'] = DecimalTrait::setDecimal($totalWithdrawal);

        $userRes = User::with('store')
            ->whereRelation('store', 'id', '!=', 1)
            ->where("is_dummy", 0)
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('store', 'id', $storeId);
            })
            ->whereIn("user_type", [User::$userType['user-account']])
            ->when(isset($memberID), function ($query) use ($memberID) {
                $query->where('member_id', '=', $memberID);
            })
            ->when(isset($username), function ($query) use ($username) {
                $query->where('username', '=', $username);
            })
            ->when($leaderDownlines, function ($query) use ($leaderDownlines) {
                $query->whereIn('id', $leaderDownlines);
            });
        $summaryUserRes = clone $userRes;
        $userRes = $userRes->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDate)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDate)))
            ->groupBy("join_date")
            ->selectRaw("COUNT(id) AS total_user,  DATE(created_at) as join_date")
            ->get();

        foreach ($userRes as $userCount) {
            $joinDate = date("Y-m-d", strtotime($userCount->join_date));
            $dateRange[$joinDate]['register'] = DecimalTrait::setDecimal(($userCount->total_user ?? 0));
            $tableTotal['register'] = DecimalTrait::setDecimal($tableTotal['register'] + ($userCount->total_user ?? 0));
        }
        $totalRegister = $summaryUserRes->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDateFilter)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDateFilter)))
            ->count('id');
        $summary['register'] = DecimalTrait::setDecimal($totalRegister);

        $transferRes = Transfer::where('status', transfer::$status['successful'])
            ->when(isset($memberID), function ($query) use ($memberID) {
                $query->whereRelation('fromUser', 'member_id', '=', $memberID);
            })
            ->when(isset($username), function ($query) use ($username) {
                $query->whereRelation('fromUser', 'username', '=', $username);
            })
            ->when($leaderDownlines, function ($query) use ($leaderDownlines) {
                $query->whereIn('id', $leaderDownlines);
            });
        $summaryTransferRes = clone $transferRes;
        $transferRes = $transferRes->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDate)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDate)))
            ->selectRaw("amount, created_at")
            ->get();

        foreach ($transferRes as $transfer) {
            $transferDate = date("Y-m-d", strtotime($transfer->created_at));
            $dateRange[$transferDate]['transfer'] = DecimalTrait::setDecimal($dateRange[$transferDate]['transfer'] + ($transfer->amount ?? 0));
            $tableTotal['transfer'] = DecimalTrait::setDecimal($tableTotal['transfer'] + ($transfer->amount ?? 0));

            $packageID = $transfer->product_id ?? 0;
            if (isset($packages[$packageID])) {
                $dateRange[$transferDate]['transfer'] = DecimalTrait::setDecimal($dateRange[$transferDate]['transfer'] + ($transfer->amount ?? 0));
                $tableTotal['transfer'] = DecimalTrait::setDecimal($tableTotal['transfer'] + ($transfer->amount ?? 0));
            }
        }

        $summaryTransferRes = $summaryTransferRes->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDateFilter)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDateFilter)))
            ->sum('amount');
        $summary['transfer'] = $summaryTransferRes;

        $telexTransferRes = TelexTransfer::where('status', TelexTransfer::$status['approved'])
            ->when(isset($memberID), function ($query) use ($memberID) {
                $query->whereRelation('user', 'member_id', '=', $memberID);
            })
            ->when(isset($username), function ($query) use ($username) {
                $query->whereRelation('user', 'username', '=', $username);
            })
            ->when($leaderDownlines, function ($query) use ($leaderDownlines) {
                $query->whereIn('id', $leaderDownlines);
            });
        $summaryTelexTransferRes = clone $telexTransferRes;
        $telexTransfer = $telexTransferRes->where("approved_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDate)))
            ->where("approved_at", "<=", date("Y-m-d 23:59:59", strtotime($toDate)))
            ->selectRaw("amount, approved_at")
            ->get();

        foreach ($telexTransfer as $telexTransfer) {
            $telexTransferDate = date("Y-m-d", strtotime($telexTransfer->approved_at));
            $dateRange[$telexTransferDate]['telex_transfer'] = DecimalTrait::setDecimal($dateRange[$telexTransferDate]['telex_transfer'] + ($telexTransfer->amount ?? 0));
            $tableTotal['telex_transfer'] = DecimalTrait::setDecimal($tableTotal['telex_transfer'] + ($telexTransfer->amount ?? 0));

            $packageID = $telexTransfer->product_id ?? 0;
            if (isset($packages[$packageID])) {
                $dateRange[$TelexTransferDate]['telex_transfer'] = DecimalTrait::setDecimal($dateRange[$TelexTransferDate]['telex_transfer'] + ($TelexTransfer->amount ?? 0));
                $tableTotal['telex_transfer'] = DecimalTrait::setDecimal($tableTotal['telex_transfer'] + ($TelexTransfer->amount ?? 0));
            }
        }

        $summaryTelexTransferRes = $summaryTelexTransferRes->where("approved_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDateFilter)))
            ->where("approved_at", "<=", date("Y-m-d 23:59:59", strtotime($toDateFilter)))
            ->sum('amount');
        $summary['telex_transfer'] = $summaryTelexTransferRes;


        // Card - Card To Wallet (Transfer In)
        $exTranferInRow = ExTransfer::with('user')->where('status', ExTransfer::$status['confirmed'])
            ->where('type', ExTransfer::$type['out'])
            ->whereNotNull('card_id')->whereNull('product_id')
            ->whereRelation('user.store', 'id', '!=', 1)
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('user.store', 'id', $storeId);
            })
            ->when(isset($memberID), function ($query) use ($memberID) {
                $query->whereRelation('user', 'member_id', '=', $memberID);
            })
            ->when(isset($username), function ($query) use ($username) {
                $query->whereRelation('user', 'username', '=', $username);
            })
            ->when($leaderDownlines, function ($query) use ($leaderDownlines) {
                $query->whereIn('user_id', $leaderDownlines);
            });
        $summaryExTransferIn = clone $exTranferInRow;
        $exTranferInRow = $exTranferInRow->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDate)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDate)))
            ->get();

        foreach ($exTranferInRow as $row) {
            $withdrawalDate = date("Y-m-d", strtotime($row->created_at));
            $dateRange[$withdrawalDate]['card_to_wallet'] = DecimalTrait::setDecimal($dateRange[$withdrawalDate]['card_to_wallet'] + ($row->amount ?? 0));
            $tableTotal['card_to_wallet'] = DecimalTrait::setDecimal($tableTotal['card_to_wallet'] + ($row->amount ?? 0));
        }
        $totalExTransferIn = $summaryExTransferIn->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDateFilter)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDateFilter)))
            ->sum('amount');
        $summary['card_to_wallet'] = DecimalTrait::setDecimal($totalExTransferIn);

        // Card - Wallet To Card (Transfer Out)
        $exTranferOutRow = ExTransfer::with('user')->where('status', ExTransfer::$status['confirmed'])
            ->where('type', ExTransfer::$type['in'])
            ->whereNotNull('card_id')->whereNull('product_id')
            ->whereRelation('user.store', 'id', '!=', 1)
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('user.store', 'id', $storeId);
            })
            ->when(isset($memberID), function ($query) use ($memberID) {
                $query->whereRelation('user', 'member_id', '=', $memberID);
            })
            ->when(isset($username), function ($query) use ($username) {
                $query->whereRelation('user', 'username', '=', $username);
            })
            ->when($leaderDownlines, function ($query) use ($leaderDownlines) {
                $query->whereIn('user_id', $leaderDownlines);
            });
        $summaryExTransferOut = clone $exTranferOutRow;
        $exTranferOutRow = $exTranferOutRow->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDate)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDate)))
            ->get();

        foreach ($exTranferOutRow as $row) {
            $withdrawalDate = date("Y-m-d", strtotime($row->created_at));
            $dateRange[$withdrawalDate]['wallet_to_card'] = DecimalTrait::setDecimal($dateRange[$withdrawalDate]['wallet_to_card'] + ($row->amount ?? 0));
            $tableTotal['wallet_to_card'] = DecimalTrait::setDecimal($tableTotal['wallet_to_card'] + ($row->amount ?? 0));
        }
        $totalExTransferOut = $summaryExTransferOut->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDateFilter)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDateFilter)))
            ->sum('amount');
        $summary['wallet_to_card'] = DecimalTrait::setDecimal($totalExTransferOut);

        // // POS - Deposit
        // $userCardLogDepositRow = UserCardLog::with('userCard', 'userCard.user', 'store')
        //     ->where('value_type', UserCardLog::$valueType['offline'])
        //     ->where('operation_type', UserCardLog::$operationType['deposit'])
        //     ->when($storeId, function ($query) use ($storeId) {
        //         $query->whereRelation('store', 'id', $storeId);
        //     })->when(isset($memberID), function ($query) use ($memberID) {
        //         $query->whereRelation('userCard.user', 'member_id', '=', $memberID);
        //     })
        //     ->when(isset($username), function ($query) use ($username) {
        //         $query->whereRelation('userCard.user', 'username', '=', $username);
        //     });
        // $summaryUserCardLogDeposit = clone $userCardLogDepositRow;
        // $userCardLogDepositRow = $userCardLogDepositRow->where("transaction_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDate)))
        //     ->where("transaction_at", "<=", date("Y-m-d 23:59:59", strtotime($toDate)))
        //     ->get();

        // foreach ($userCardLogDepositRow as $row) {
        //     $withdrawalDate = date("Y-m-d", strtotime($row->transaction_at));
        //     $dateRange[$withdrawalDate]['pos']['deposit'] = DecimalTrait::setDecimal($dateRange[$withdrawalDate]['pos']['deposit'] + ($row->operation_qty ?? 0));
        //     $tableTotal['pos']['deposit'] = DecimalTrait::setDecimal($tableTotal['pos']['deposit'] + ($row->operation_qty ?? 0));
        // }
        // $totalUserCardLogDeposit = $summaryUserCardLogDeposit->where("transaction_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDateFilter)))
        //     ->where("transaction_at", "<=", date("Y-m-d 23:59:59", strtotime($toDateFilter)))
        //     ->sum('operation_qty');
        // $summary['pos']['deposit'] = DecimalTrait::setDecimal($totalUserCardLogDeposit);

        // // POS - Withdraw
        // $userCardLogWithdrawRow = UserCardLog::with('userCard', 'userCard.user', 'store')
        //     ->where('value_type', UserCardLog::$valueType['offline'])
        //     ->where('operation_type', UserCardLog::$operationType['withdrawal'])
        //     ->when($storeId, function ($query) use ($storeId) {
        //         $query->whereRelation('store', 'id', $storeId);
        //     })->when(isset($memberID), function ($query) use ($memberID) {
        //         $query->whereRelation('userCard.user', 'member_id', '=', $memberID);
        //     })
        //     ->when(isset($username), function ($query) use ($username) {
        //         $query->whereRelation('userCard.user', 'username', '=', $username);
        //     });
        // $summaryUserCardLogWithdraw = clone $userCardLogWithdrawRow;
        // $userCardLogWithdrawRow = $userCardLogWithdrawRow->where("transaction_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDate)))
        //     ->where("transaction_at", "<=", date("Y-m-d 23:59:59", strtotime($toDate)))
        //     ->get();

        // foreach ($userCardLogWithdrawRow as $row) {
        //     $withdrawalDate = date("Y-m-d", strtotime($row->transaction_at));
        //     $dateRange[$withdrawalDate]['pos']['withdrawal'] = DecimalTrait::setDecimal($dateRange[$withdrawalDate]['pos']['withdrawal'] + ($row->operation_qty ?? 0));
        //     $tableTotal['pos']['withdrawal'] = DecimalTrait::setDecimal($tableTotal['pos']['withdrawal'] + ($row->operation_qty ?? 0));
        // }
        // $totalUserCardLogWithdraw = $summaryUserCardLogWithdraw->where("transaction_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDateFilter)))
        //     ->where("transaction_at", "<=", date("Y-m-d 23:59:59", strtotime($toDateFilter)))
        //     ->sum('operation_qty');
        // $summary['pos']['withdrawal'] = DecimalTrait::setDecimal($totalUserCardLogWithdraw);

        $data['list'] = array_values($dateRange);
        if (!isset($seeAll) || $seeAll == 0) {
            $data['table_total'] = $tableTotal;
            $data['summary'] = $summary;
            $data['pagination'] = array(
                "current_page" => $page,
                "from" => $fromRow,
                "last_page" => $lastPage,
                "per_page" => (int) $limit,
                "to" => $toRow,
                "total" => $totalRow
            );
        }
        $data['meta'] = null;

        return (object) $data;
    }

    public static function getMachineSummary($params = [])
    {
        if (!isset($params['from_date']) || empty($params['from_date'])) {
            $firstUser = User::whereIn('user_type', [User::$userType['user-account']])->first();
            $fromDate = date("Y-m-d", strtotime($firstUser->created_at));
        } else {
            $fromDate = $params['from_date'];
        }

        if (!isset($params['to_date']) || empty($params['to_date'])) {
            $toDate = date("Y-m-d");
        } else {
            $toDate = $params['to_date'];
        }

        $username = $params['username'] ?? null;
        if (isset($params['export']) && ($params['export'] == 1)) {
            $params["model"] = get_class() ?? null;
            $params["function"] = __FUNCTION__;
            ExportReport::exportReport($params);
        }

        $fromDateFilter = $fromDate;
        $toDateFilter = $toDate;

        $seeAll = $params['see_all'] ?? null;
        $startDate = $toDate;
        $dateRange = [];
        $tableTotal = [];
        $leaderDownlines = null;
        $storeId = $params['store_id'] ?? null;

        if (isset($params['leader_username']) && !empty($params['leader_username'])) {
            $leaderUser = User::where('username', $params['leader_username'])->first();
            if (isset($leaderUser) && !empty($leaderUser)) {
                $leaderTree = $leaderUser->treeSponsor;
                $donwlines = TreeSponsor::where("trace_key", 'LIKE', $leaderTree->trace_key . "%")->pluck('user_id', 'id');
                if (!empty($donwlines)) {
                    $leaderDownlines = $donwlines->toArray();
                }
            }

            if (empty($donwlines)) {
                $data['list'] = $dateRange;
                if (!isset($seeAll) || $seeAll == 0) {
                    $data['pagination'] = array(
                        "current_page" => 1,
                        "from" => null,
                        "last_page" => 1,
                        "per_page" => (int) ($params['limit'] ?? config('app.pagination_rows')),
                        "to" => null,
                        "total" => 0
                    );
                }
                $data['meta'] = null;

                return (object) $data;
            }
        }

        $memberID = $params['member_id'] ?? null;
        $username = $params['phone_no'] ?? null;

        // Manual pagination
        $page = 1;
        $limit = 0;
        $fromRow = 0;
        $toRow = INF;
        $lastPage = 1;
        if (!isset($seeAll) || $seeAll == 0) {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? config('app.pagination_rows');
            $fromDateTS = strtotime(date("Y-m-d 00:00:00", strtotime($fromDate)));
            $toDateTS = strtotime(date("Y-m-d 00:00:00", strtotime($toDate)));
            $totalRow = ($toDateTS - $fromDateTS) / (24 * 60 * 60) + 1;
            $fromRow = (($page - 1) * $limit) + 1;
            $toRow = $page * $limit;
            $lastPage = ceil(($totalRow / $limit));
            if ($page == $lastPage) {
                $toRow = $totalRow;
            }
        }

        $totalCount = 0;
        $initTable = [
            "transfer_in" => DecimalTrait::setDecimal(0),
            "transfer_out" => DecimalTrait::setDecimal(0),
        ];

        $summary = $tableTotal = $initTable;
        while (strtotime($startDate) >= strtotime($fromDate)) {
            if (strtotime($startDate) > time()) {
                $startDate = date("Y-m-d", strtotime($startDate . " -1 day"));
                $toDate = $startDate;
                continue;
            }

            $totalCount++;
            if ($totalCount < $fromRow) {
                $startDate = date("Y-m-d", strtotime($startDate . " -1 day"));
                $toDate = $startDate;
                continue;
            }

            $dateRange[$startDate]['date'] = Traits\DateTrait::dateFormat($startDate, false);
            $dateRange[$startDate] = $dateRange[$startDate] + $initTable;
            if ($totalCount >= $toRow) {
                $fromDate = $startDate;
                break;
            }
            $startDate = date("Y-m-d", strtotime($startDate . " -1 day"));
        }

        // Transfer  In
        $exTranferInRow = ExTransfer::with('user')->where('status', ExTransfer::$status['confirmed'])
            ->where('type', ExTransfer::$type['out'])
            ->whereNotNull('machine_id')
            ->whereRelation('user.store', 'id', '!=', 1)
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('user.store', 'id', $storeId);
            })
            ->when(isset($memberID), function ($query) use ($memberID) {
                $query->whereRelation('user', 'member_id', '=', $memberID);
            })
            ->when(isset($username), function ($query) use ($username) {
                $query->whereRelation('user', 'username', '=', $username);
            })
            ->when($leaderDownlines, function ($query) use ($leaderDownlines) {
                $query->whereIn('user_id', $leaderDownlines);
            });
        $summaryExTransferIn = clone $exTranferInRow;
        $exTranferInRow = $exTranferInRow->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDate)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDate)))
            ->get();

        foreach ($exTranferInRow as $row) {
            $withdrawalDate = date("Y-m-d", strtotime($row->created_at));
            $dateRange[$withdrawalDate]['transfer_in'] = DecimalTrait::setDecimal($dateRange[$withdrawalDate]['transfer_in'] + ($row->amount ?? 0));
            $dateRange[$withdrawalDate]['amount'] = DecimalTrait::setDecimal($dateRange[$withdrawalDate]['transfer_in'] + ($row->amount ?? 0));
            $tableTotal['transfer_in'] = DecimalTrait::setDecimal($tableTotal['transfer_in'] + ($row->amount ?? 0));
        }
        $totalExTransferIn = $summaryExTransferIn
            ->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDateFilter)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDateFilter)))
            ->sum('amount');
        $summary['transfer_in'] = DecimalTrait::setDecimal($totalExTransferIn);

        // Transfer Out
        $exTranferOutRow = ExTransfer::with('user')->where('status', ExTransfer::$status['confirmed'])
            ->where('type', ExTransfer::$type['in'])
            ->whereNotNull('machine_id')
            ->whereRelation('user.store', 'id', '!=', 1)
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('user.store', 'id', $storeId);
            })
            ->when(isset($memberID), function ($query) use ($memberID) {
                $query->whereRelation('user', 'member_id', '=', $memberID);
            })
            ->when(isset($username), function ($query) use ($username) {
                $query->whereRelation('user', 'username', '=', $username);
            })
            ->when($leaderDownlines, function ($query) use ($leaderDownlines) {
                $query->whereIn('user_id', $leaderDownlines);
            })
            ->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDate)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDate)))
            ->get();

        foreach ($exTranferOutRow as $row) {
            $withdrawalDate = date("Y-m-d", strtotime($row->created_at));
            $dateRange[$withdrawalDate]['transfer_out'] = DecimalTrait::setDecimal($dateRange[$withdrawalDate]['transfer_out'] + ($row->amount ?? 0));
            $tableTotal['transfer_out'] = DecimalTrait::setDecimal($tableTotal['transfer_out'] + ($row->amount ?? 0));
        }

        $data['list'] = array_values($dateRange);
        if (!isset($seeAll) || $seeAll == 0) {
            $data['table_total'] = $tableTotal;
            $data['pagination'] = array(
                "current_page" => $page,
                "from" => $fromRow,
                "last_page" => $lastPage,
                "per_page" => (int) $limit,
                "to" => $toRow,
                "total" => $totalRow
            );
        }
        $data['meta'] = null;

        return (object) $data;
    }

    public static function getMachineSummaryGroupByMachineId($params = [])
    {
        if (!isset($params['from_date']) || empty($params['from_date'])) {
            $firstUser = User::whereIn('user_type', [User::$userType['user-account']])->first();
            $fromDate = date("Y-m-d", strtotime($firstUser->created_at));
        } else {
            $fromDate = $params['from_date'];
        }

        if (!isset($params['to_date']) || empty($params['to_date'])) {
            $toDate = date("Y-m-d");
        } else {
            $toDate = $params['to_date'];
        }

        $username = $params['username'] ?? null;
        if (isset($params['export']) && ($params['export'] == 1)) {
            $params["model"] = get_class() ?? null;
            $params["function"] = __FUNCTION__;
            ExportReport::exportReport($params);
        }

        $seeAll = $params['see_all'] ?? null;
        $data = [];
        $machineRange = [];
        $leaderDownlines = null;
        $storeId = $params['store_id'] ?? null;

        if (isset($params['leader_username']) && !empty($params['leader_username'])) {
            $leaderUser = User::where('username', $params['leader_username'])->first();
            if (isset($leaderUser) && !empty($leaderUser)) {
                $leaderTree = $leaderUser->treeSponsor;
                $donwlines = TreeSponsor::where("trace_key", 'LIKE', $leaderTree->trace_key . "%")->pluck('user_id', 'id');
                if (!empty($donwlines)) {
                    $leaderDownlines = $donwlines->toArray();
                }
            }

            if (empty($donwlines)) {
                $data['list'] = $machineRange;
                if (!isset($seeAll) || $seeAll == 0) {
                    $data['pagination'] = array(
                        "current_page" => 1,
                        "from" => null,
                        "last_page" => 1,
                        "per_page" => (int) ($params['limit'] ?? config('app.pagination_rows')),
                        "to" => null,
                        "total" => 0
                    );
                }
                $data['meta'] = null;

                return (object) $data;
            }
        }

        $memberID = $params['member_id'] ?? null;
        $username = $params['phone_no'] ?? null;
        $machineId = $params['machine_id'] ?? null;
        $machines = Models\Machine::where('store_id', $storeId)
            ->when($machineId, function ($query, $id) {
                $query->where('id', $id);
            })->get();

        // Manual pagination
        $totalRow = sizeof($machines);
        $page = 1;
        $limit = 0;
        $fromRow = 1;
        $toRow = $totalRow;
        $lastPage = 1;

        if (!isset($seeAll) || $seeAll == 0) {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? config('app.pagination_rows');
            $fromRow = (($page - 1) * $limit) + 1;
            $toRow = $page * $limit;
            $lastPage = ceil(($totalRow / $limit));
            if ($page == $lastPage) {
                $toRow = $totalRow;
            }
        }

        if ($totalRow > 0) {
            for ($i = $fromRow - 1; $i < $toRow; $i++) {
                $machineRange[] = [
                    "id" => $machines[$i]->id,
                    "name" => $machines[$i]->name,
                    "transfer_in" => DecimalTrait::setDecimal(0),
                    "transfer_out" => DecimalTrait::setDecimal(0),
                ];
            }
        }

        $machineIds = array_map(function ($e) {
            return $e["id"];
        }, $machineRange);
        $machineRange = array_combine($machineIds, $machineRange);

        $transactions = ExTransfer::with('user')
            ->where('status', ExTransfer::$status['confirmed'])
            ->whereIn('machine_id', $machineIds)
            ->where("created_at", ">=", date("Y-m-d 00:00:00", strtotime($fromDate)))
            ->where("created_at", "<=", date("Y-m-d 23:59:59", strtotime($toDate)))
            ->groupBy('machine_id', 'type')
            ->selectRaw('machine_id, type, sum(amount) as sum')
            ->get();

        $tableTotal = ['transfer_in' => 0, 'transfer_out' => 0];
        foreach ($transactions as $transaction) {
            if ($transaction['type'] == ExTransfer::$type['out']) {
                $machineRange[$transaction['machine_id']]['transfer_in'] = $transaction['sum'];
                $tableTotal['transfer_in'] = $tableTotal['transfer_in'] + $transaction['sum'];
            }

            if ($transaction['type'] == ExTransfer::$type['in']) {
                $machineRange[$transaction['machine_id']]['transfer_out'] = $transaction['sum'];
                $tableTotal['transfer_in'] = $tableTotal['transfer_out'] + $transaction['sum'];
            }
        }

        $data['list'] = array_values($machineRange);
        if (!isset($seeAll) || $seeAll == 0) {
            $data['table_total'] = $tableTotal;
            $data['pagination'] = array(
                "current_page" => $page,
                "from" => $fromRow,
                "last_page" => $lastPage,
                "per_page" => (int) $limit,
                "to" => $toRow,
                "total" => $totalRow
            );
        }
        $data['meta'] = null;

        return (object) $data;
    }

    public static function getSalesExclusionList(array $params = [])
    {

        $type = $params['type'] ?? null;
        $fromDate = $params['from_date'] ?? null;
        $toDate = $params['to_date'] ?? null;
        $reference_no = $params['reference_no'] ?? null;
        $username = $params['username'] ?? null;
        $dateTime = date('Y-m-d H:i:s');
        $date = date('Y-m-d', strtotime($dateTime));
        $sortableColumns = ['name', 'email', 'status'];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        $tableName = ['member' => User::class, 'package' => UserPortfolio::class, 'insurance' => Insurance::class];

        $items = $tableName[$type]::query()
            ->when($type == 'member', function ($query) use ($fromDate, $toDate, $username, $reference_no) {
                $query->select(['created_at', 'username', 'user_type', 'email', 'country_id', 'activated', 'disabled', 'suspended']);
                $query->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                    $q->where(DB::raw('DATE(created_at)'), ">=", $fromDate);
                    return $q->where(DB::raw('DATE(created_at)'), "<=", $toDate);
                });
                $query->when(isset($username), function ($q) use ($username) {
                    return $q->where('username', 'LIKE', "%" . $username . "%");
                });
                $query->when(isset($reference_no), function ($q) use ($reference_no) {
                    return $q->where('id', '');
                });
                $query->where('user_type', '!=', 0);
            })
            ->when(($type == 'insurance' || $type == 'package'), function ($query) use ($fromDate, $toDate, $username, $reference_no) {
                $query->select(['reference_no', 'activated_at', 'user_id']);
                $query->with(['userDetail']);
                $query->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                    $q->where(DB::raw('DATE(activated_at)'), ">=", $fromDate);
                    return $q->where(DB::raw('DATE(activated_at)'), "<=", $toDate);
                });
                $query->when(isset($username), function ($q) use ($username) {
                    return $q->whereRelation('userDetail', 'username', 'LIKE', "%" . $username . "%");
                });
                $query->when(isset($reference_no), function ($q) use ($reference_no) {
                    return $q->where('reference_no', $reference_no);
                });
            })
            ->where('is_exclude', '1')
            ->orderBy($order_by, $order_sort)
            ->paginate($limit);

        $items->getCollection()->transform(function ($q) use ($type) {

            switch ($type) {
                case 'member':

                    $res = [
                        "date" => $q->created_at ? Traits\DateTrait::dateFormat($q->created_at) : null,
                        "username" => $q->username,
                        "type" => ucfirst($type),
                        "reference_no" => '-',
                    ];

                    break;
                case 'package':
                case 'insurance':

                    $res = [
                        "date" => $q->activated_at ? Traits\DateTrait::dateFormat($q->activated_at) : null,
                        "username" => $q->userDetail->username,
                        "type" => ucfirst($type),
                        "reference_no" => $q->reference_no,
                    ];

                    break;
                default:
                    break;
            }

            return (object) $res;
        });

        return (new ItemsCollection($items))->toArray();
    }

    public static function getPackagePurchaseReport(array $params = [])
    {
        $leaderUsername = $params['leader_username'] ?? null;
        $productFilter = $params['product_id'] ?? null;

        $fromDate = $params['from_date'] ?? null;
        $toDate = $params['to_date'] ?? null;

        $dateTime = date('Y-m-d H:i:s');
        $date = date('Y-m-d', strtotime($dateTime));
        $sortableColumns = ['created_at'];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        // see all
        $seeAll = $params['see_all'] ?? null;

        //leaderusername flter
        $leaderDownlines = [];
        if (isset($leaderUsername) && !empty($leaderUsername)) {
            $leaderUser = User::where('username', $leaderUsername)->first();
            if (isset($leaderUser) && !empty($leaderUser)) {
                $leaderTree = $leaderUser->treeSponsor;
                $donwlines = TreeSponsor::where("trace_key", 'LIKE', $leaderTree->trace_key . "%")->pluck('user_id', 'id');
                if (!empty($donwlines)) {
                    $leaderDownlines = $donwlines->toArray();
                }
            }
        }

        $product = ProductDetail::selectRaw('product_name,id');

        $productSummary = clone $product;
        $product = $product->when(isset($productFilter), function ($q) use ($productFilter) {
            return $q->where('id', $productFilter);
        })->get()->keyBy('id')->toArray();
        $productSummary = $productSummary->get()->keyBy('id')->toArray();
        $depositType = UserPortfolio::$depositType;

        $productIDAry = [];
        foreach ($product as $productID => $productValue) {
            $productIDAry[] = $productID;
            $productAry[$productID] = $productValue['product_name'];

            //display product
            $temp['id'] = $productID;
            $temp['name'] = $productValue['product_name'];
            $temp['display'] = Lang::get('lang.' . $productValue['product_name']);

            $productDisplayAry[$productValue['product_name']] = $temp;
            unset($temp);
        }

        foreach ($depositType as $depositName => $depositValue) {
            $depositAry[$depositValue] = $depositName;
        }

        $items = UserPortfolio::query()
            ->selectRaw("DATE(created_at) AS purchase_date, product_id")
            ->whereIn('product_id', $productIDAry)
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('DATE(created_at)'), ">=", $fromDate);
                return $q->where(DB::raw('DATE(created_at)'), "<=", $toDate);
            })
            ->when(isset($leaderUsername), function ($query) use ($leaderDownlines) {
                $query->whereIn('user_id', $leaderDownlines);
            });

        $itemDetail = clone $items;
        $items = $items->groupBy('purchase_date', 'product_id')->orderBy('purchase_date', $order_sort)
            ->when((!isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $itemDetail = $itemDetail->groupBy('purchase_date', 'product_id')->get();
        $dateRange = [];
        $tableTotal = [];

        //sumary initial
        $summary = [];
        $summary["total_package_total"] = 0;
        foreach ($productSummary as $productName => $productValue) {
            $productName = $productValue['product_name'];
            $summary[$productName . "_total"] = 0;
        }
        foreach ($depositType as $depositName => $depositValue) {
            $summary[$depositName . "_total"] = 0;
        }

        foreach ($itemDetail as $itemRow) {
            foreach ($product as $productName => $productValue) {

                $date = $itemRow['purchase_date'];
                $productName = $productValue['product_name'];

                $dateRange[$date][$productName]['total_package_purchase']['quantity'] = 0;
                $dateRange[$date][$productName]['total_package_purchase']['amount'] = 0;
                $tableTotal['total_package_purchase_quantity'] = 0;
                $tableTotal['total_package_purchase_amount'] = 0;

                $summary["total_package_total"] = 0;
                $summary[$productName . "_total"] = 0;

                foreach ($depositType as $depositName => $depositValue) {
                    $dateRange[$date][$productName][$depositName]['quantity'] = 0;
                    $dateRange[$date][$productName][$depositName]['amount'] = 0;
                    $tableTotal[$depositName . '_quantity'] = 0;
                    $tableTotal[$depositName . '_amount'] = 0;

                    $summary[$depositName . "_total"] = 0;
                }
            }
        }

        $itemDetails = UserPortfolio::whereIn('product_id', $productIDAry)
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('DATE(created_at)'), ">=", $fromDate);
                return $q->where(DB::raw('DATE(created_at)'), "<=", $toDate);
            })
            ->when(isset($leaderUsername), function ($query) use ($leaderDownlines) {
                $query->whereIn('user_id', $leaderDownlines);
            })
            ->where('product_price', '>', 0);

        $sumQuery = clone $itemDetails;
        $itemDetails = $itemDetails->groupBy('deposit_type', 'purchase_date', 'product_id')
            ->selectRaw("count(id) AS count, deposit_type,SUM(product_price) AS amount, DATE(created_at) as purchase_date, product_id")
            ->get();

        if (!empty($itemDetails)) {
            $itemCategorise = $itemDetails->toArray();
        }

        foreach ($itemCategorise as $categorise) {
            //quantity
            $dateRange[$categorise['purchase_date']][$productAry[$categorise['product_id']]]['total_package_purchase']['quantity'] += $categorise['count'];
            $dateRange[$categorise['purchase_date']][$productAry[$categorise['product_id']]][$depositAry[$categorise['deposit_type']]]['quantity'] += $categorise['count'];

            $tableTotal['total_package_purchase_quantity'] += $categorise['count'];
            $tableTotal[$depositAry[$categorise['deposit_type']] . '_quantity'] += $categorise['count'];

            //amount
            $dateRange[$categorise['purchase_date']][$productAry[$categorise['product_id']]]['total_package_purchase']['amount'] += $categorise['amount'];
            $dateRange[$categorise['purchase_date']][$productAry[$categorise['product_id']]][$depositAry[$categorise['deposit_type']]]['amount'] += $categorise['amount'];

            $tableTotal['total_package_purchase_amount'] += $categorise['amount'];
            $tableTotal[$depositAry[$categorise['deposit_type']] . '_amount'] += $categorise['amount'];
        }

        $mapFunc = function ($q) use ($depositType, $productAry, $dateRange) {
            $date = $q->purchase_date;

            $q->product_name = $productAry[$q->product_id];
            $q->product_display = Lang::has('lang.' . $q->product_name) ? __('lang.' . $q->product_name) : $q->product_name;

            $q->total_package_purchase_quantity = $dateRange[$date][$productAry[$q->product_id]]['total_package_purchase']['quantity'];
            $q->total_package_purchase_amount = DecimalTrait::setDecimal($dateRange[$date][$productAry[$q->product_id]]['total_package_purchase']['amount']);

            foreach ($depositType as $depositName => $depositValue) {

                $q->{$depositName . '_quantity'} = $dateRange[$date][$productAry[$q->product_id]][$depositName]['quantity'];
                $q->{$depositName . '_amount'} = DecimalTrait::setDecimal($dateRange[$date][$productAry[$q->product_id]][$depositName]['amount']);
            }

            unset($q->product_id);

            $res = $q;
            return (object) $res;
        };

        $sumQuery = $sumQuery->groupBy('deposit_type', 'product_id')->selectRaw("SUM(product_price) AS amount, deposit_type, product_id")->get();

        if (!empty($sumQuery)) {
            $summarise = $sumQuery->toArray();
        }

        foreach ($summarise as $summarised) {
            $summary["total_package_total"] += $summarised['amount'];

            $summary[$productAry[$summarised['product_id']] . "_total"] += $summarised['amount'];
            $summary[$depositAry[$summarised['deposit_type']] . "_total"] += $summarised['amount'];
        }

        if ($seeAll == 1) {
            $final_list = ['list' => $items->get()->map($mapFunc)->toArray()];
        } else {
            $items->getCollection()->transform($mapFunc);
            $final_list = (new ItemsCollection($items))->toArray() + ["table_total" => $tableTotal] + ["summary" => $summary];
        }
        return $final_list;
    }

    public static function getStoreSummaryReport(array $params = [])
    {
        if (isset($params['from_date']) && isset($params['to_date'])) {
            $fromDate = $params['from_date'];
            $toDate = $params['to_date'];
        } else {
            $fromDate = now()->format('Y-m-d');
            $toDate = now()->format('Y-m-d');
        }

        if (isset($params['export']) && ($params['export'] == 1)) {
            $jobData = $params;
            $jobData['model'] = get_class() ?? null;
            $jobData['function'] = __FUNCTION__;
            $jobData['module'] = MODULE;
            $jobData['creator_type'] = MODULE;
            $jobData['creator_id'] = auth()->user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, 'Export Successfully');
        }

        // Pagination
        $page = 1;
        $limit = 0;
        $fromRow = 0;
        $toRow = INF;
        $lastPage = 1;
        $tableTotal = 0;

        if (! isset($seeAll) || $seeAll == 0) {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? config('app.pagination_rows');
            $fromDateTS = strtotime(date('Y-m-d 00:00:00', strtotime($fromDate)));
            $toDateTS = strtotime(date('Y-m-d 23:59:59', strtotime($toDate)));
            $totalRow = ($toDateTS - $fromDateTS) / (24 * 60 * 60) + 1;
            $fromRow = (($page - 1) * $limit) + 1;
            $toRow = $page * $limit;
            $lastPage = ceil(($totalRow / $limit));
            if ($page == $lastPage) {
                $toRow = $totalRow;
            }
        }

        $store_ids = isset($params['store_ids']) ? json_encode($params['store_ids']) : AdminDetail::where('admin_id', auth()->user()->id)->first()->value;
        $store_id = isset($params['store_id']) ? $params['store_id'] : null;

        $total_register = User::join('store', 'store.store_id', '=', 'users.store_id')
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                DB::raw('COUNT(users.id) as total_register'),
                DB::raw('DATE(users.created_at) AS transaction_date'),
            )
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('users.created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)))
                    ->where('users.created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(!isset($store_id), function ($q) use ($store_ids) {
                $q->whereIn('store.id', json_decode($store_ids));
            })
            ->when(isset($store_id), function ($q) use ($store_id) {
                return $q->where('users.store_id', $store_id);
            })
            ->groupBy(DB::raw('DATE(users.created_at)'), 'store.id', 'store.name')
            ->orderBy('store.id', 'asc')
            ->get()->toArray();

        $total_deposit = Deposit::join('users', 'users.id', '=', 'deposit.user_id')
            ->join('store', 'store.store_id', '=', 'users.store_id')
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                DB::raw('COUNT(deposit.user_id) as total_deposit_count'),
                DB::raw('SUM(deposit.amount) as total_deposit_amount'),
                DB::raw('DATE(deposit.created_at) AS transaction_date'),
            )
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('deposit.created_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)))
                    ->where('deposit.created_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(!isset($store_id), function ($q) use ($store_ids) {
                $q->whereIn('store.id', json_decode($store_ids));
            })
            ->when(isset($store_id), function ($q) use ($store_id) {
                return $q->where('users.store_id', $store_id);
            })
            // ->whereNull('card_id')
            ->where('deposit.status', Deposit::$status['approved'])
            ->groupBy(DB::raw('DATE(deposit.created_at)'), 'store.id', 'store.name')
            ->orderBy('store.id', 'asc')
            ->get()->toArray();

        $total_withdrawal = Withdrawal::join('users', 'users.id', '=', 'withdrawal.user_id')
            ->join('store', 'store.store_id', '=', 'users.store_id')
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                DB::raw('COUNT(withdrawal.user_id) as total_withdrawal_count'),
                DB::raw('SUM(withdrawal.amount) as total_withdrawal_amount'),
                DB::raw('DATE(withdrawal.updated_at) AS transaction_date'),
            )
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('withdrawal.updated_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where('withdrawal.updated_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(!isset($store_id), function ($q) use ($store_ids) {
                $q->whereIn('store.id', json_decode($store_ids));
            })
            ->when(isset($store_id), function ($q) use ($store_id) {
                return $q->where('users.store_id', $store_id);
            })
            ->where('withdrawal.status', Withdrawal::$status['approved'])
            ->groupBy(DB::raw('DATE(withdrawal.updated_at)'), 'store.id', 'store.name')
            ->orderBy('store.id', 'asc')
            ->get()->toArray();

        $total_turnover = UserTurnoverSummary::join('users', 'users.id', '=', 'user_turnover_summaries.user_id')
            ->join('store', 'store.store_id', '=', 'users.store_id')
            ->select(
                'store.id as store_id',
                'store.name as store_name',
                DB::raw('SUM(user_turnover_summaries.bet_count) as total_bet_count'),
                DB::raw('SUM(user_turnover_summaries.turnover) as total_turnover'),
                DB::raw('DATE(user_turnover_summaries.transaction_at) AS transaction_date'),
            )
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('user_turnover_summaries.transaction_at', '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where('user_turnover_summaries.transaction_at', '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when(!isset($store_id), function ($q) use ($store_ids) {
                $q->whereIn('store.id', json_decode($store_ids));
            })
            ->when(isset($store_id), function ($q) use ($store_id) {
                return $q->where('users.store_id', $store_id);
            })
            ->groupBy(DB::raw('DATE(user_turnover_summaries.transaction_at)'), 'store.id', 'store.name')
            ->orderBy('store.id', 'asc')
            ->get()->toArray();

        $list = array_merge($total_register, $total_deposit, $total_withdrawal, $total_turnover);
        $mergedOnline = [];

        foreach ($list as $entry) {
            $key = $entry['store_id'] . '|' . $entry['store_name'] . '|' . $entry['transaction_date'];

            if (! isset($mergedOnline[$key])) {
                $mergedOnline[$key] = [
                    'store_id' => $entry['store_id'],
                    'store_name' => $entry['store_name'],
                    'transaction_date' => $entry['transaction_date'],
                    'total_register' => 0,
                    'total_deposit_amount' => 0,
                    'total_deposit_count' => 0,
                    'total_withdrawal_amount' => 0,
                    'total_withdrawal_count' => 0,
                    'total_turnover' => 0,
                    'total_bet_count' => 0,
                ];
            }

            if (isset($entry['total_register'])) {
                $mergedOnline[$key]['total_register'] += (float) $entry['total_register'];
            }

            if (isset($entry['total_deposit_amount'])) {
                $mergedOnline[$key]['total_deposit_amount'] += (float) $entry['total_deposit_amount'];
                $mergedOnline[$key]['total_deposit_count'] += (float) $entry['total_deposit_count'];
            }

            if (isset($entry['total_withdrawal_amount'])) {
                $mergedOnline[$key]['total_withdrawal_amount'] += (float) $entry['total_withdrawal_amount'];
                $mergedOnline[$key]['total_withdrawal_count'] += (float) $entry['total_withdrawal_count'];
            }

            if (isset($entry['total_turnover'])) {
                $mergedOnline[$key]['total_turnover'] += (float) $entry['total_turnover'];
                $mergedOnline[$key]['total_bet_count'] += (float) $entry['total_bet_count'];
            }
        }
        $results = array_values($mergedOnline);

        $data['list'] = $results;
        if (! isset($seeAll) || $seeAll == 0) {
            $data['table_total'] = $tableTotal;
            $data['pagination'] = [
                'current_page' => $page,
                'from' => $fromRow,
                'last_page' => $lastPage,
                'per_page' => (int) $limit,
                'to' => $toRow,
                'total' => $totalRow,
            ];
        }
        $data['meta'] = null;

        return (object) $data;
    }
}
