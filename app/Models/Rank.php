<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Rank extends Model{
    protected $table = 'rank';

    protected $hidden = [
    ];

    protected $fillable = [
        'name',
        'priority',
        'type',
    ];

    public static function getRankSetting($rankType = "",$rankID = ""){
        $rankID = isset($rankID) ? $rankID : null;
        $rankType = isset($rankType) ? $rankType : null;

        $rankData = self::with("rankSetting")
                    ->select("id","name","priority","type")
                    ->when($rankType, function ($query) use ($rankType) {
                        $query->where("type",$rankType);
                    })
                    ->when($rankID, function ($query) use ($rankID) {
                        $query->where('id',$rankID);
                    })
                    ->orderBy("priority","desc")
                    ->get()
                    ->map(function($rank){
                        // $rankSetting = $rank->rankSetting->keyBy("name")->toArray();
                        foreach($rank->rankSetting as $rankSetRow){
                            switch ($rankSetRow->type) {
                                case 'level':
                                case 'percentage':
                                    $rankPercentage[$rankSetRow->name] = $rankSetRow->value;
                                    break;

                                case 'rank':    
                                    $rankSetting[$rankSetRow->name]["count"] = $rankSetRow->value;
                                    $rankSetting[$rankSetRow->name]["rank"] = $rankSetRow->reference;
                                    break;
                                
                                case 'purchase':  
                                    $rankSetting[$rankSetRow->name] = $rankSetRow->value;
                                    break;

                                case 'own':  
                                case 'group':  
                                    $rankSetting[$rankSetRow->name][$rankSetRow->type] = $rankSetRow->value;
                                    break;

                                case 'add-req':    
                                    if(isset($rankSetRow->reference)){
                                        $rankSetting[$rankSetRow->type][$rankSetRow->name][$rankSetRow->reference] = $rankSetRow->value;    
                                    } else {
                                        $rankSetting[$rankSetRow->type][$rankSetRow->name] = $rankSetRow->value;    
                                    }
                                    break;

                                case 'exp-payout':    
                                    if(isset($rankSetRow->reference)){
                                        $rankSetting[$rankSetRow->type][$rankSetRow->name] = json_decode($rankSetRow->reference, true);
                                    } else {
                                        $rankSetting[$rankSetRow->type][$rankSetRow->name] = $rankSetRow->value;    
                                    }
                                    break;

                                default:  
                                    $rankSetting[$rankSetRow->name] = $rankSetRow->value;
                                    break;
                            }
                        }
                        $res = [
                                    "id" => $rank->id,
                                    "name" => $rank->name,
                                    "priority" => $rank->priority,
                                    "type" => $rank->type,
                                    "rank_setting" => $rankSetting,
                                    "percentage" => $rankPercentage,
                                ];
                        return $res;
                    })
                    ->toArray();
        return $rankData;

    }

    public function rankSetting(){
        return $this->hasMany(RankSetting::class,'rank_id','id')->select("rank_id","name","value","type","reference");
    }
}
