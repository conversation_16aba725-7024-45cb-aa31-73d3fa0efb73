<?php

namespace App\Models;

use App\Models\CreditTransaction as ModelsCreditTransaction;
use App\Traits\DecimalTrait;
use App\Traits\GenerateNumberTrait;
use App\Http\Resources\ItemsCollection;
use App\Traits\DateTrait;
use App\Traits;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;

class CreditTransaction extends Model
{
    use GenerateNumberTrait;
    use DecimalTrait;
    use DateTrait;

    const UPDATED_AT = null;

    protected $table = 'credit_transaction';

    protected $hidden = [];

    protected $fillable = [
        'subject_type',
        'credit_id',
        'from_id',
        'to_id',
        'user_id',
        'amount',
        'data',
        'remark',
        'belong_id',
        'batch_id',
        'reference_id',
        'creator_id',
        'creator_type',
        'portfolio_id',
        'group_id',
        'coin_rate',
        'created_at',
        'deleted_at',
    ];

    public static function adjustment($data = [])
    {
        return DB::transaction(function () use ($data) {
            $internalID = User::select('id')->where('username', 'creditSales')->where('user_type', User::$userType['internal-account'])->first()->id ?? Null;
            if (empty($internalID)) {
                abort(400, 'Invalid Internal Account.');
            }

            switch ($data['type']) {
                case 'in':
                    $fromId = $internalID;
                    $toId = $data['user_id'];
                    $subject = "adjustment-in";
                    break;

                case 'out':
                    $fromId = $data['user_id'];
                    $toId = $internalID;
                    $subject = "adjustment-out";
                    break;

                default:
                    abort(400, 'Invalid Type.');
                    break;
            }

            $creditId = $data['credit_id'];
            $credit = Credit::with('creditSetting')->find($creditId);
            $userId   = $data['user_id'];
            $belongId = GenerateNumberTrait::GenerateNewId();
            $dateTime = date('Y-m-d H:i:s');
            $creditType = $credit->name;
            $amount = $data['amount'];
            $remark = $data['remark'] ?? Null;

            CreditTransaction::insertTransaction($fromId, $toId, $userId, $creditType, $amount, $subject, $belongId, NULL, $remark, $dateTime);
        });
    }

    public static function advancedCredit($data = [])
    {
        return DB::transaction(function () use ($data) {
            $internalID = User::select('id')->where('username', 'creditSales')->where('user_type', User::$userType['internal-account'])->first()->id ?? Null;
            if (empty($internalID)) {
                abort(400, 'Invalid Internal Account.');
            }

            $fromId = $internalID;
            $toId = $data['user_id'];
            $subject = "advanced-credit";

            $creditId = $data['credit_id'];
            $credit = Credit::with('creditSetting')->find($creditId);
            $userId   = $data['user_id'];
            $belongId = GenerateNumberTrait::GenerateNewId();
            $dateTime = date('Y-m-d H:i:s');
            $creditType = $credit->name;
            $amount = $data['amount'];
            $remark = $data['remark'] ?? Null;

            CreditTransaction::insertTransaction($fromId, $toId, $userId, $creditType, $amount, $subject, $belongId, NULL, $remark, $dateTime);
        });
    }

    public static function promotionAdjustment($data = [])
    {
        return DB::transaction(function () use ($data) {
            $internalID = User::select('id')->where('username', 'creditSales')->where('user_type', User::$userType['internal-account'])->first()->id ?? Null;
            if (empty($internalID)) {
                abort(400, 'Invalid Internal Account.');
            }

            $fromId = $internalID;
            $toId = $data['user_id'];
            $subject = "promotion-adjustment";

            $creditId = $data['credit_id'];
            $credit = Credit::with('creditSetting')->find($creditId);
            $userId   = $data['user_id'];
            $belongId = GenerateNumberTrait::GenerateNewId();
            $dateTime = date('Y-m-d H:i:s');
            $creditType = $credit->name;
            $amount = $data['amount'];
            $remark = $data['remark'] ?? Null;

            CreditTransaction::insertTransaction($fromId, $toId, $userId, $creditType, $amount, $subject, $belongId, NULL, $remark, $dateTime);
        });
    }

    public static function insertTransaction($fromId, $toId, $userId, $creditType, $amount = 0, $subject, $belongId, $batchId = null, $remark = null, $dateTime = null, $data = null, $portfolioId = null, $groupId = null, $referenceId = null, $coinRate = null, $thirdParty = true)
    {
        $creatorId = Auth::user()->id ?? null;
        $creatorType = isset($creatorId) ? MODULE : 'system';
        if ($coinRate == null) $coinRate = 1;

        if ($amount <= 0) {
            // throw new Exception("Invalid Amount");
            return true;
        }

        if (empty(config('subject')[$subject])) {
            throw new Exception("Invalid Subject");
        }

        // handle sub wallet
        $creditArray = Credit::with('creditSetting')->where(function ($q) use ($creditType) {
            $q->where('name', $creditType);
            return $q->orWhere('type', $creditType);
        })->orderBy('priority', 'ASC')->get()->keyBy('id')->map(function ($q) {
            $res['name'] = $q->name;
            foreach ($q->creditSetting as $set) {
                $res[$set['name']] = $set['value'];
            }
            return $res;
        })->toArray();
        if (empty($creditArray)) {
            abort(400, 'Invalid Wallet');
        }

        // Check Decimal
        $amount = DecimalTrait::setDecimal($amount);

        $fromIdData = User::find($fromId);
        if (!isset($groupId)) $groupId = GenerateNumberTrait::GenerateGroupId();

        foreach ($creditArray as $creditId => $creditDetails) {
            $calculated_amount = $amount;
            $creditName = $creditDetails['name'];

            //Check Balance
            if (array_search($fromIdData->user_type, User::$userType) == 'internal-account') {
                //Handle check Internal type
                $transactionType = "in";
            } else {
                //Handle check Balance
                $balance = Credit::getBalance($fromId, $creditName);
                $transactionType = "out";

                // if($amount > $balance){
                //     $calculated_amount = $balance;
                // }
            }

            if ($calculated_amount <= 0) continue;

            $insertData = [
                'subject_type' => config('subject')[$subject],
                'credit_id' => $creditId,
                'from_id' => $fromId,
                'to_id' => $toId,
                'user_id' => $userId,
                'amount' => $calculated_amount,
                'data' => $data,
                'remark' => $remark,
                'belong_id' => $belongId,
                'batch_id' => $batchId ?? $belongId,
                'reference_id' => $referenceId,
                'creator_id' => isset($creatorId) ? $creatorId : 0,
                'creator_type' => $creatorType,
                'portfolio_id' => $portfolioId,
                'group_id' => $groupId,
                'coin_rate' => $coinRate,
            ];

            if (isset($dateTime)) $insertData['created_at'] = $dateTime;
            self::create($insertData);

            $amount = DecimalTrait::setDecimal(($amount - $calculated_amount));
            if ($amount <= 0) break;
        }

        return true;
    }

    public static function getExpTransactionList(array $params = [])
    {
        $user_id = (MODULE == 'admin') ? ($params['user_id'] ?? null) : (Auth::user()->id ?? Null);
        $creditId = $params['credit_id'] ?? null;
        $fromDate = $params['from_date'] ?? Null;
        $toDate = $params['to_date'] ?? Null;
        $seeAll = $params['see_all'] ?? null;

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');
        $items = self::query()
            ->with(['userDetail', 'telexTransfer.user'])
            ->select([
                'group_id',
                DB::raw('ANY_VALUE(id) AS id'),
                DB::raw('ANY_VALUE(user_id) AS user_id'),
                DB::raw('ANY_VALUE(credit_id) AS credit_id'),
                DB::raw('ANY_VALUE(from_id) AS from_id'),
                DB::raw('ANY_VALUE(to_id) AS to_id'),
                DB::raw('ANY_VALUE(belong_id) AS belong_id'),
                DB::raw('ANY_VALUE(batch_id) AS batch_id'),
                DB::raw('ANY_VALUE(remark) AS remark'),
                DB::raw('ANY_VALUE(created_at) AS created_at'),
                DB::raw('ANY_VALUE(reference_id) AS reference_id'),
                DB::raw('SUM(amount) AS amount')
            ])
            ->when($user_id, function ($query) use ($user_id) {
                $query->where('user_id', $user_id);
            })
            ->when($creditId, function ($query) use ($creditId) {
                $query->where('credit_id', $creditId);
            })
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('DATE(created_at)'), ">=", $fromDate);
                return $q->where(DB::raw('DATE(created_at)'), "<=", $toDate);
            })
            ->orderBy($order_by, $order_sort)
            ->orderBy('id', $order_sort)
            ->groupBy('group_id')
            ->when((!isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $mapFunc = function ($q) {
            $type = 'personal';
            $trxnType = 'out';
            $fromUsername = null;
            $fromPhone = null;
            $source = null;
            if ($q->from_id < 1000) {
                $trxnType = 'in';
                if (isset($q->telexTransfer)) {
                    $fromuserID = optional($q->telexTransfer)->user->id ?? null;
                    $fromUsername = optional($q->telexTransfer)->user->name ?? null;
                    $fromPhone = optional($q->telexTransfer)->user->username ?? null;
                    $fromAmount = optional($q->telexTransfer)->receivable_amount ?? 0;
                    $source = 'tt-transfer';
                }
            }

            if (isset($fromuserID) && $fromuserID != $q->user_id) {
                $type = 'group';
            }

            $res = [
                'from_username' => $fromUsername ?? null,
                'from_phone' => $fromPhone ?? null,
                'source' => $source ?? null,
                'source_display' => isset($source) ? (Lang::has("lang." . $source) ? Lang::get("lang." . $source) : $source) : null,
                'type' => $type ?? null,
                'type_display' => isset($type) ? (Lang::has("lang." . $type) ? Lang::get("lang." . $type) : $type) : null,
                'created_at' => DateTrait::dateFormat($q->created_at),
                'point' => DecimalTrait::setDecimal($q->amount),
                'amount' => DecimalTrait::setDecimal(($fromAmount ?? 0)),
                'trxn_type' => $trxnType,
            ];

            return (object) $res;
        };

        if (($seeAll == 1)) {
            $listing = $items->get()->map($mapFunc)->toArray();
            return ['list' => $listing, 'table_total' => $tableTotal];
        } else {
            $items->getCollection()->transform($mapFunc);
            $data = new ItemsCollection($items);
            return $data;
        }
    }

    public static function getTransactionList(array $params = [])
    {
        $user_id = (MODULE == 'admin') ? ($params['user_id'] ?? null) : (Auth::user()->id ?? Null);
        $creditId = $params['credit_id'] ?? null;
        $memberId = $params['member_id'] ?? null;
        $username = $params['username'] ?? null;
        $referenceName = $params['reference_name'] ?? null;
        $creditType = $params['credit_type'] ?? null;
        $fromDate = $params['from_date'] ?? Null;
        $toDate = $params['to_date'] ?? Null;
        $subjectId = isset($params['subject_id']) && $params['subject_id'] > 0 ? $params['subject_id'] : Null;
        $subjectIdAry = $params['subject_id_ary'] ?? [];
        $storeId = $params['store_id'] ?? null;
        $exStoreId = $params['extra_store_id'] ?? null;
        $groupType = isset($creditId) ? "separate" : "group";
        $seeAll = $params['see_all'] ?? null;
        $status = $params['status'] ?? null;
        $cardType = $params['card_type'] ?? null;

        if (empty($user_id) && (MODULE != 'admin')) {
            abort(400, "Invalid User");
        }

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        if (isset($params['export']) && ($params['export'] == 1)) {
            $jobData = $params;
            $jobData["model"] = get_class() ?? null;
            $jobData["function"] = __FUNCTION__;
            $jobData["module"] = MODULE;
            $jobData["creator_type"] = MODULE;
            $jobData["creator_id"] = Auth::user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, "Export Successfully");
        }

        $items = self::query()
            ->with([
                'fromUser',
                'toUser',
                'userDetail',
                'userDetail.store',
                'referenceDetail',
                'telexTransfer',
                'telexTransferRefund',
                'convertInfo' => function ($q) {
                    $q->select('belong_id', 'from_credit_id', 'to_credit_id');
                    return;
                },
                'convertInfo.from_credit' => function ($q) {
                    $q->select('id', 'name', 'code');
                    return;
                },
                'convertInfo.to_credit' => function ($q) {
                    $q->select('id', 'name', 'code');
                    return;
                },
                'exTransfer' => function ($q) {
                    $q->select('belong_id', 'card_id', 'product_id');
                    return;
                },
                'exTransfer.userCard' => function ($q) {
                    $q->select('id', 'store_id', 'card_serial_no', 'user_id');
                    return;
                },
                // 'exTransfer.userCard.store' => function($q){
                //     $q->select('id', 'store_id', 'name');
                //     return;
                // }
            ])
            ->select([
                'group_id',
                DB::raw('ANY_VALUE(id) AS id'),
                DB::raw('ANY_VALUE(user_id) AS user_id'),
                DB::raw('ANY_VALUE(credit_id) AS credit_id'),
                DB::raw('ANY_VALUE(from_id) AS from_id'),
                DB::raw('ANY_VALUE(to_id) AS to_id'),
                DB::raw('ANY_VALUE(subject_type) AS subject_type'),
                DB::raw('ANY_VALUE(belong_id) AS belong_id'),
                DB::raw('ANY_VALUE(batch_id) AS batch_id'),
                DB::raw('ANY_VALUE(remark) AS remark'),
                DB::raw('ANY_VALUE(created_at) AS created_at'),
                DB::raw('ANY_VALUE(reference_id) AS reference_id'),
                DB::raw('SUM(amount) AS amount')
            ])
            ->when($user_id, function ($query) use ($user_id) {
                $query->where('user_id', $user_id);
            })
            ->when($username, function ($query) use ($username) {
                $query->whereRelation('userDetail', 'username', 'LIKE', "%$username%");
            })
            ->when(isset($referenceName), function ($query) use ($referenceName) {
                $query->whereRelation('referenceDetail', function ($q) use ($referenceName) {
                    $q->where('name', $referenceName);
                });
            })
            ->when($creditId, function ($query) use ($creditId) {
                $query->where('credit_id', $creditId);
            })
            ->when($creditType, function ($query) use ($creditType) {
                $query->whereRelation('creditInfo', 'type', $creditType);
            })
            ->when($memberId, function ($query) use ($memberId) {
                $query->whereRelation('userDetail', 'member_id', $memberId);
            })
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('userDetail.store', 'id', $storeId);
            })
            ->when(isset($exStoreId), function ($q) use ($exStoreId) {
                return $q->whereHas('userDetail.store', function ($q) use ($exStoreId) {
                    $q->whereIn('id', $exStoreId);
                });
            })
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('DATE(created_at)'), ">=", $fromDate);
                return $q->where(DB::raw('DATE(created_at)'), "<=", $toDate);
            })
            ->when($subjectIdAry, function ($query) use ($subjectIdAry) {
                $query->whereIn('subject_type', $subjectIdAry);
            })
            ->when($subjectId, function ($query) use ($subjectId) {
                $query->where('subject_type', $subjectId);
            })
            ->when($cardType, function ($query) use ($cardType) {
                if ($cardType == 1) {
                    return $query->whereHas('exTransfer', function ($q) {
                        $q->whereNotNull('card_id')->whereNull('product_id');
                    });
                }

                if ($cardType == 0) {
                    return $query->whereHas('exTransfer', function ($q) {
                        $q->whereNotNull('product_id')->whereNull('card_id');
                    });
                }
            })
            ->whereHas('toUser', function ($q) {
                $q->where('is_dummy', false);
            })
            ->whereHas('fromUser', function ($q) {
                $q->where('is_dummy', false);
            })
            ->orderBy($order_by, $order_sort)
            ->orderBy('id', $order_sort)
            ->groupBy('group_id')
            ->when((!isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $balance = [];
        $depBelongIDAry = [];
        $withdrawBelongIDAry = [];
        $withdrawThirdPartyBelongIDAry = [];
        $initTime = null;
        $tableTotal = ["amount" => DecimalTrait::setDecimal(0)];
        $mapFunc = function ($q) use (&$balance, &$initTime, $groupType, &$depBelongIDAry, &$withdrawBelongIDAry, $params, &$tableTotal) {
            $actionType = 'personal';
            $fromUsername = null;
            $fromPhone = null;
            $source = null;

            $subject = array_search($q->subject_type, config('subject'));
            $type = (array_search($q->fromUser->user_type, User::$userType) != 'internal-account') ? "out" : "in";

            if ($q->from_id < 1000) {
                if (isset($q->telexTransfer)) {
                    $fromuserID = optional($q->telexTransfer)->user->id ?? null;
                    $fromUsername = optional($q->telexTransfer)->user->name ?? null;
                    $fromPhone = optional($q->telexTransfer)->user->username ?? null;
                    $fromAmount = optional($q->telexTransfer)->receivable_amount ?? 0;
                    $source = 'tt-transfer';
                }
            }

            if (isset($fromuserID) && $fromuserID != $q->user_id) {
                $actionType = 'group';
            }

            if ($subject == 'reset-exp') {
                $actionType = 'system';
            }

            $res = [
                'store' => $q->userDetail->store->name ?? null,
                'created_at' => DateTrait::dateFormat($q->created_at),
                'username' => $q->userDetail->username,
                'member_id' => $q->userDetail->member_id,
                'amount' => DecimalTrait::setDecimal($q->amount),
                'subject' => Lang::has('lang.' . $subject) ? Lang::get('lang.' . $subject) : $subject,
                'belong_id' => $q->belong_id,
                'batch_id' => $q->batch_id,
                'remark' => $q->remark,
                'reference' => null,
                'type' => $type,
                'is-transfer' => in_array($subject, ['transfer-in', 'transfer-out']) ? 1 : 0,
                'action_type' => $actionType ?? null,
                'action_type_display' => isset($actionType) ? (Lang::has("lang." . $actionType) ? Lang::get("lang." . $actionType) : $actionType) : null,
                'gain_from' => $source ?? null,
                'gain_from_display' => isset($source) ? (Lang::has("lang." . $source) ? Lang::get("lang." . $source) : $source) : null,
                'card_serial_no' => $q->exTransfer->userCard->card_serial_no ?? null,
                'store_name' => $q->userDetail->store->name ?? null,
                'status' => $q->status
            ];

            $tableTotal['amount'] = ($type == 'in') ? (DecimalTrait::setDecimal(($tableTotal['amount'] + $q->amount))) : (DecimalTrait::setDecimal(($tableTotal['amount'] - $q->amount)));

            switch ($subject) {
                case 'ex-transfer-in':
                case 'ex-transfer-out':
                    if (isset($q->exTransfer->product_id)) {
                        $res['subject'] = Lang::has('lang.' . $subject . '-game') ? Lang::get('lang.' . $subject . '-game') : $res['subject'];
                        $res['toFrom_name'] = $q->exTransfer->product->display_name ?? 'Game ' . $q->exTransfer->product_id;
                        $res['toFrom'] = $q->exTransfer->product->display_name ?? 'Game ' . $q->exTransfer->product_id;
                    }

                    if (isset($q->exTransfer->card_id)) {
                        $detail = $q->exTransfer->userCard?->load('user');

                        if ($subject == 'ex-transfer-out') {
                            $res['toFrom_name'] = 'Transfer to ' . $detail->user->name ?? null;
                            $res['toFrom'] = $detail->user->name ?? null;
                        }
                    }
                    break;

                case 'transfer-out':
                    $res['transaction_type'] = Lang::has('lang.transfer-to') ? Lang::get('lang.transfer-to') : "Transfer to";
                    $res['toFrom_name'] = $q->referenceDetail->name ?? null;
                    $res['toFrom'] = $q->referenceDetail->username ?? null;
                    break;

                case 'transfer-in':
                    $res['transaction_type'] = Lang::has('lang.received-from') ? Lang::get('lang.received-from') : "Receive from";
                    $res['toFrom_name'] = $q->referenceDetail->name ?? null;
                    $res['toFrom'] = $q->referenceDetail->username ?? null;
                    break;

                case 'deposit':
                case 'deposit-charge':
                    $depBelongIDAry[$q->belong_id] = $q->belong_id;
                    break;

                case 'withdrawal-out':
                case 'withdrawal-charge':
                    $withdrawBelongIDAry[$q->belong_id] = $q->belong_id;
                    break;

                case 'withdrawal-out-third-party':
                case 'withdrawal-charge-third-party':
                    $withdrawThirdPartyBelongIDAry[$q->belong_id] = $q->belong_id;
                    break;

                case 'withdrawal-refund':
                    $withdrawBelongIDAry[$q->batch_id] = $q->batch_id;
                    break;

                case 'tt-normal-transfer':
                case 'tt-instant-transfer':
                    // case 'tt-normal-transfer-charges':
                    // case 'tt-instant-transfer-charges':
                    $res['transaction_id'] = $q->telexTransfer->serial_number ?? null;
                    break;

                case 'tt-normal-transfer-refund':
                case 'tt-instant-transfer-refund':
                    $res['transaction_id'] = $q->telexTransferRefund->serial_number ?? null;
                    break;

                case 'convert-in':
                    if (MODULE == 'app') $res['subject'] = $res['subject'] . " (" . $q->convertInfo->from_credit->code . "-" . $q->convertInfo->to_credit->code . ")";
                    break;
                case 'convert-out':
                    if (MODULE == 'app') $res['subject'] = $res['subject'] . " (" . $q->convertInfo->from_credit->code . "-" . $q->convertInfo->to_credit->code . ")";
                    break;
            }

            if (!isset($params['show_all'])) {

                if (!isset($initTime)) $initTime = $q->created_at;
                if (!isset($balance[$q->user_id])) {
                    $balCredit = ($groupType == 'separate') ? $q->creditInfo->name : $q->creditInfo->type;
                    $balance[$q->user_id] = Credit::getBalance($q->user_id, $balCredit, $initTime);
                }

                $res['balance'] = DecimalTrait::setDecimal($balance[$q->user_id]);
                $res['type'] = $type;
                $balance[$q->user_id] = ($type == 'in') ? (DecimalTrait::setDecimal(($balance[$q->user_id] ?? 0) - $q->amount)) : DecimalTrait::setDecimal(($balance[$q->user_id] ?? 0) + $q->amount);
            }

            return (object) $res;
        };

        if (($seeAll == 1)) {
            $listing = $items->get()->map($mapFunc)->toArray();
            if (!empty($depBelongIDAry)) {
                $depositRes = Deposit::selectRaw('code,belong_id,status')->whereIn('belong_id', $depBelongIDAry)->get();
                $depositRef = $depositRes->pluck('code', 'belong_id');
                $depositStatus = $depositRes->pluck('status', 'belong_id');
            }
            if (!empty($withdrawBelongIDAry)) {
                $withdrawRes = Withdrawal::selectRaw('serial_number,belong_id,status')->whereIn('belong_id', $withdrawBelongIDAry)->get();
                $withdrawRef = $withdrawRes->pluck('serial_number', 'belong_id');
                $withdrawStatus = $withdrawRes->pluck('status', 'belong_id');
            }
            if (!empty($withdrawThirdPartyBelongIDAry)) {
                $withdrawThirdPartyRes = WithdrawalThirdParty::selectRaw('serial_number,belong_id,status')->whereIn('belong_id', $withdrawThirdPartyBelongIDAry)->get();
                $withdrawThirdPartyRef = $withdrawThirdPartyRes->pluck('serial_number', 'belong_id');
                $withdrawThirdPartyStatus = $withdrawThirdPartyRes->pluck('status', 'belong_id');
            }
            foreach ($listing as &$value) {
                switch (true) {
                    case (in_array($value->belong_id, $depBelongIDAry)):
                        $value->reference = $depositRef[$value->belong_id] ?? null;
                        $value->status = $depositStatus[$value->belong_id] ?? null;
                        break;

                    case (in_array($value->belong_id, $withdrawBelongIDAry)):
                        $value->reference = $withdrawRef[$value->belong_id] ?? null;
                        $value->status = $withdrawStatus[$value->belong_id] ?? null;
                        break;

                    case (in_array($value->batch_id, $withdrawBelongIDAry)):
                        $value->reference = $withdrawRef[$value->batch_id] ?? null;
                        $value->status = $withdrawStatus[$value->batch_id] ?? null;
                        break;

                    case (in_array($value->belong_id, $withdrawThirdPartyBelongIDAry)):
                        $value->reference = $withdrawThirdPartyRef[$value->belong_id] ?? null;
                        $value->status = $withdrawThirdPartyStatus[$value->belong_id] ?? null;
                        break;

                    case (in_array($value->batch_id, $withdrawThirdPartyBelongIDAry)):
                        $value->reference = $withdrawThirdPartyRef[$value->batch_id] ?? null;
                        $value->status = $withdrawThirdPartyStatus[$value->batch_id] ?? null;
                        break;
                }
                unset($value->belong_id, $value->batch_id);
            }

            return ['list' => $listing, 'table_total' => $tableTotal];
        } else {
            $items->getCollection()->transform($mapFunc);
            if (!empty($depBelongIDAry)) {
                $depositRes = Deposit::selectRaw('code,belong_id,status')->whereIn('belong_id', $depBelongIDAry)->get();
                $depositRef = $depositRes->pluck('code', 'belong_id');
                $depositStatus = $depositRes->pluck('status', 'belong_id');
            }
            if (!empty($withdrawBelongIDAry)) {
                $withdrawRes = Withdrawal::selectRaw('serial_number,belong_id,status')->whereIn('belong_id', $withdrawBelongIDAry)->get();
                $withdrawRef = $withdrawRes->pluck('serial_number', 'belong_id');
                $withdrawStatus = $withdrawRes->pluck('status', 'belong_id');
            }
            if (!empty($withdrawThirdPartyBelongIDAry)) {
                $withdrawThirdPartyRes = WithdrawalThirdParty::selectRaw('serial_number,belong_id,status')->whereIn('belong_id', $withdrawThirdPartyBelongIDAry)->get();
                $withdrawThirdPartyRef = $withdrawThirdPartyRes->pluck('serial_number', 'belong_id');
                $withdrawThirdPartyStatus = $withdrawThirdPartyRes->pluck('status', 'belong_id');
            }

            foreach ($items as &$value) {
                switch (true) {
                    case (in_array($value->belong_id, $depBelongIDAry)):
                        $value->reference = $depositRef[$value->belong_id] ?? null;
                        $value->status = $depositStatus[$value->belong_id] ?? null;
                        break;

                    case (in_array($value->belong_id, $withdrawBelongIDAry)):
                        $value->reference = $withdrawRef[$value->belong_id] ?? null;
                        $value->status = $withdrawStatus[$value->belong_id] ?? null;
                        break;

                    case (in_array($value->batch_id, $withdrawBelongIDAry)):
                        $value->reference = $withdrawRef[$value->batch_id] ?? null;
                        $value->status = $withdrawStatus[$value->batch_id] ?? null;
                        break;

                    case (in_array($value->belong_id, $withdrawThirdPartyBelongIDAry)):
                        $value->reference = $withdrawThirdPartyRef[$value->belong_id] ?? null;
                        $value->status = $withdrawThirdPartyStatus[$value->belong_id] ?? null;
                        break;

                    case (in_array($value->batch_id, $withdrawThirdPartyBelongIDAry)):
                        $value->reference = $withdrawThirdPartyRef[$value->batch_id] ?? null;
                        $value->status = $withdrawThirdPartyStatus[$value->batch_id] ?? null;
                        break;
                }
                unset($value->belong_id, $value->batch_id);
            }

            $filteredItems = [];
            if (isset($status)) {
                $filteredItems = $items->getCollection()->filter(function ($item) use ($status) {
                    return $item->status === $status;
                })->values();
            } else {
                $filteredItems = $items;
            }

            $data = new ItemsCollection($filteredItems);
            $data->additional['table_total'] = $tableTotal;
            return $data;
        }
    }

    public function fromUser()
    {
        return $this->belongsTo(User::class, "from_id", "id");
    }

    public function toUser()
    {
        return $this->belongsTo(User::class, "to_id", "id");
    }

    public function userDetail()
    {
        return $this->belongsTo(User::class, "user_id", "id");
    }

    public function creditInfo()
    {
        return $this->belongsTo(Credit::class, "credit_id", "id");
    }

    public function referenceDetail()
    {
        return $this->belongsTo(User::class, "reference_id", "id");
    }

    public function telexTransfer()
    {
        return $this->belongsTo(TelexTransfer::class, "belong_id", "belong_id");
    }

    public function exTransfer()
    {
        return $this->belongsTo(ExTransfer::class, "belong_id", "belong_id");
    }

    public function telexTransferRefund()
    {
        return $this->belongsTo(TelexTransfer::class, 'batch_id', 'belong_id');
    }

    public function convertInfo()
    {
        return $this->belongsTo(Convert::class, 'belong_id', 'belong_id');
    }
}
