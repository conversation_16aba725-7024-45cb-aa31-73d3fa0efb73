<?php

namespace App\Models\Providers;

use App\Models\Services;
use App\Models\Store;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class MtMachine extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'uuid',
        'description',
        'status',
        'store_id',
        'service_id',
        'transfer_in',
        'transfer_out',
        'profit_loss',
    ];

    /* -------------------------------------------------------------------------- */
    /*                                Relationships                               */
    /* -------------------------------------------------------------------------- */
    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'store_id', 'id');
    }

    public function service(): BelongsTo
    {
        return $this->belongsTo(Services::class, 'service_id', 'id');
    }

    protected static function booted()
    {
        static::creating(function ($mtMachine) {
            $mtMachine->uuid = (string) Str::uuid();
        });
    }
}
