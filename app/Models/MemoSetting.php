<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Http\Resources\ItemsCollection;
use Illuminate\Support\Facades\Auth;

class MemoSetting extends Model
{
    const UPDATED_AT = null;

    const CREATED_AT = null;
    
    protected $table = 'memo_setting';

    protected $hidden = [
    ];

    protected $fillable = [
        'memo_id',
        'name',
        'value',
        'deleted_at',
    ];
}
