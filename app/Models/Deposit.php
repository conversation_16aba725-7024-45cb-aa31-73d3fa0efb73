<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use App\Services\GameProvider\JILI;
use App\Services\GameProvider\JK;
use App\Services\PaymentGateway\FPay;
use App\Services\PaymentGateway\FPayEWallet;
use App\Services\PaymentGateway\FPayTelco;
use App\Services\PaymentGateway\FPayThai;
use App\Services\PaymentGateway\OnePay;
use App\Services\PaymentGateway\RapidPay;
use App\Services\PaymentGateway\WePay;
use App\Traits;
use App\Traits\DateTrait;
use App\Traits\DecimalTrait;
use App\Traits\S3Trait;
use App\Traits\ThirdPartyTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class Deposit extends Model
{
    use DecimalTrait;

    protected $table = 'deposit';

    protected $hidden = [];

    protected $fillable = [
        'code',
        'user_id',
        'credit_type',
        'reference_id',
        'type',
        'status',
        'amount',
        'currency',
        'rate',
        'converted_amount',
        'charges',
        'receivable_amount',
        'belong_id',
        'batch_id',
        'user_remark',
        'admin_remark',
        'party',
        'creator_id',
        'creator_type',
        'updater_id',
        'approved_at',
        'created_at',
        'deleted_at',
        'card_id',
    ];

    public static $type = [
        'manual-bank' => 1,
        'online-bank' => 2,
        'crypto' => 3,
        'ewallet' => 4,
        'online-bank-thai' => 5,
        'thb-manual-bank' => 6,
        'online-bank-tk8' => 7,
        'online-bank-tk8th' => 8,
        'online-bank-telco' => 9,
        'onepay-online-bank' => 10,
        'onepay-ewallet' => 11,
        'rapidpay-online-bank' => 12,
        'wepay-online-bank' => 13,
    ];

    public static $status = [
        'pending' => 0,
        'approved' => 1,
        'rejected' => 2,
        'cancelled' => 3,
    ];

    public function userDetail()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function adminDetail()
    {
        return $this->belongsTo(Admin::class, 'updater_id', 'id');
    }

    public function depositDetail()
    {
        return $this->hasMany(DepositDetail::class, 'deposit_id', 'id');
    }

    public function transactionLogs()
    {
        return $this->belongsTo(TransactionLogs::class, 'reference_id', 'id');
    }

    public function tpWebHook()
    {
        return $this->hasMany(TpWebHook::class, 'table_id', 'id')->where('table_type', TpWebHook::$tableType['deposit']);
    }

    public function lastFMTPromo()
    {
        return $this->hasMany(TpWebHook::class, 'table_id', 'id')->where('table_type', TpWebHook::$tableType['deposit'])
            ->where('type', TpWebHook::$type['balance-promoid'])
            ->where('platform', 'fmt');
    }

    public function lastDeposit()
    {
        return $this->hasMany(TpWebHook::class, 'table_id', 'id')->where('table_type', TpWebHook::$tableType['deposit'])
            ->where('type', TpWebHook::$type['callback/deposit']);
    }

    public function lastExTransfer()
    {
        return $this->hasMany(ExTransfer::class, 'reference', 'code');
    }

    public function tk8Acc()
    {
        return $this->belongsTo(UserProduct::class, 'user_id', 'user_id');
    }

    public function tk8THAcc()
    {
        return $this->belongsTo(UserProduct::class, 'user_id', 'user_id');
    }

    public function fmtAcc()
    {
        return $this->belongsTo(UserProduct::class, 'user_id', 'user_id');
    }

    public function users()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /* -------------------------------------------------------------------------- */
    /*                                   Scopes */
    /* -------------------------------------------------------------------------- */

    public function scopeApproved($query)
    {
        return $query->where('status', self::$status['approved']);
    }

    public static function getDepositData(array $params = [])
    {
        $userID = $params['user_id'] ?? null;
        $creditID = $params['credit_id'] ?? null;
        $credit = Credit::with(['creditSetting', 'currency'])
            ->when(isset($creditID) && ! empty($creditID), function ($q) use ($creditID) {
                return $q->where('id', $creditID);
            })->first();
        if (! isset($credit) || empty($credit)) {
            throw new \Exception('Credit Not Found', 400);
        }

        $creditSettingRow = $credit->creditSetting ?? [];
        $creditSetting = [];
        foreach ($creditSettingRow as $cs) {
            $creditSetting[$cs->name] = $cs->toArray();
        }

        if (! isset($creditSetting['is-fundinable']) || empty($creditSetting['is-fundinable'])) {
            throw new \Exception('Deposit Currently Not Available...', 400);
        } else {
            if ($creditSetting['is-fundinable']['value'] != 1 || $creditSetting['is-fundinable']['member'] != 1) {
                throw new \Exception('Deposit Currently Not Available...', 400);
            }
        }

        $data = [];
        $data['min-fundin'] = 0.00;
        if (isset($creditSetting['min-fundin']) && ! empty($creditSetting['min-fundin'])) {
            $data['min-fundin'] = isset($creditSetting['min-fundin']['value']) ? DecimalTrait::setDecimal($creditSetting['min-fundin']['value']) : 0.00;
        }

        $data['fundin-charge'] = 0.00;
        if (isset($creditSetting['fundin-charge']) && ! empty($creditSetting['fundin-charge'])) {
            $data['fundin-charge'] = isset($creditSetting['fundin-charge']['value']) ? DecimalTrait::setDecimal($creditSetting['fundin-charge']['value']) : 0.00;
        }

        $creditType = $credit->type ?? null;
        $data['balance'] = Credit::getBalance($userID, $creditType);
        if (isset($creditSetting['fundin-charge']) && ! empty($creditSetting['fundin-charge'])) {
            $data['fundin-charge'] = isset($creditSetting['fundin-charge']['value']) ? DecimalTrait::setDecimal($creditSetting['fundin-charge']['value']) : 0.00;
        }
        $depositOption = [];
        if (isset($creditSetting['deposit-option']['value'])) {
            $depositOptionAry = json_decode($creditSetting['deposit-option']['value']);
            foreach ($depositOptionAry as $value) {

                $double = Traits\DecimalTrait::setDecimal($value, 2);
                $display = (string) $value;

                $depositOption[] = [
                    'value' => $double,
                    'display' => $display,
                ];
            }
        }
        $data['deposit_option'] = $depositOption ?? [];

        // all cur list
        $currencySymbolList = Currency::select('iso', 'symbol')->get()->pluck('symbol', 'iso');

        $depositType = CreditSetting::getDepositMethodSetting(['setting_date' => date('Y-m-d')]);
        if (isset($creditSetting['fundin-type']) && ! empty($creditSetting['fundin-type'])) {
            $creditFundInTypeRow = $creditSetting['fundin-type']['reference'];
            $creditFundInType = explode(',', $creditFundInTypeRow);
            foreach ($creditFundInType as $typeId) {
                foreach ($depositType as $dType) {
                    // if(!in_array(self::$type[$dType['name']], $creditFundInType)) continue;
                    if (self::$type[$dType['name']] != $typeId) {
                        continue;
                    }
                    if ($dType['status_value'] == SystemSettingsAdmin::$depositMethodStatus['off']) {
                        continue;
                    }
                    $type = $dType['name'];

                    switch ($type) {
                        case 'manual-bank':
                            $bankInDetailes = $dType['bank_detail'] ?? config('bank')[env('APP_ENV')];
                            $data['option'][$type] = $bankInDetailes;
                            $data['option'][$type]['option_display'] = Lang::has('lang.' . $type) ? Lang::get('lang.' . $type) : $type;

                            $currencyList = null;
                            $currencyList['currency'] = $credit->currency->iso;
                            $currencyList['symbol'] = $credit->currency->symbol;
                            $data['option'][$type]['currency_list'][] = $currencyList;
                            break;

                        case 'online-bank':
                            try {
                                $fpay = resolve(FPay::class);
                                $currencyList = $fpay->getCurrencyList();
                                unset($currencyTemp);
                                foreach ($currencyList as &$currencyTemp) {
                                    $currencyTemp['symbol'] = $currencySymbolList[$currencyTemp['currency']] ?? $currencyTemp['currency'];
                                }
                                if (isset($currencyList) && ! empty($currencyList)) {
                                    $data['option'][$type]['currency_list'] = $currencyList;
                                    $data['option'][$type]['option_display'] = Lang::has('lang.' . $type) ? Lang::get('lang.' . $type) : $type;
                                    $data['option'][$type]['type'] = self::$type['online-bank'];
                                }
                            } catch (\Exception) {
                                break;
                            }

                            break;

                        // case 'online-bank-onepay':
                        //     try {
                        //         $fpay = resolve(OnePay::class);
                        //         $currencyList = $fpay->getCurrencyList();
                        //         unset($currencyTemp);
                        //         foreach ($currencyList as &$currencyTemp) {
                        //             $currencyTemp['symbol'] = $currencySymbolList[$currencyTemp['currency']] ?? $currencyTemp['currency'];
                        //         }
                        //         if (isset($currencyList) && !empty($currencyList)) {
                        //             $data['option'][$type]['currency_list'] = $currencyList;
                        //             $data['option'][$type]['option_display'] = Lang::has('lang.' . $type) ? Lang::get('lang.' . $type) : $type;
                        //             $data['option'][$type]['type'] = self::$type['online-bank'];
                        //         }
                        //     } catch (\Exception) {
                        //         break;
                        //     }

                        //     break;

                        case 'online-bank-thai':
                            try {
                                $fpayThai = resolve(FPayThai::class);
                                $currencyList = $fpayThai->getCurrencyList();
                                unset($currencyTemp);
                                foreach ($currencyList as &$currencyTemp) {
                                    $currencyTemp['symbol'] = $currencySymbolList[$currencyTemp['currency']] ?? $currencyTemp['currency'];
                                }
                                if (isset($currencyList) && ! empty($currencyList)) {
                                    $data['option'][$type]['currency_list'] = $currencyList;
                                    $data['option'][$type]['option_display'] = Lang::has('lang.' . $type) ? Lang::get('lang.' . $type) : $type;
                                    $data['option'][$type]['type'] = self::$type['online-bank-thai'];
                                }
                            } catch (\Exception) {
                                break;
                            }

                            break;

                        case 'online-bank-telco':
                            try {
                                $fpayTelco = resolve(FPayTelco::class);
                                $currencyList = $fpayTelco->getCurrencyList();
                                unset($currencyTemp);
                                foreach ($currencyList as &$currencyTemp) {
                                    $currencyTemp['symbol'] = $currencySymbolList[$currencyTemp['currency']] ?? $currencyTemp['currency'];
                                }
                                if (isset($currencyList) && ! empty($currencyList)) {
                                    $data['option'][$type]['currency_list'] = $currencyList;
                                    $data['option'][$type]['option_display'] = Lang::has('lang.' . $type) ? Lang::get('lang.' . $type) : $type;
                                    $data['option'][$type]['type'] = self::$type['online-bank-telco'];
                                }
                            } catch (\Exception $e) {
                                break;
                            }

                            break;

                        case 'crypto':
                            $walletAddress = UserWalletAddress::getUserWalletAddress(['user_id' => $userID, 'credit_id' => $creditID]);
                            $data['option'][$type]['wallet_address'] = $walletAddress;
                            $data['option'][$type]['option_display'] = 'USDT TRC20';
                            $currencyList = null;
                            $currencyList['currency'] = $credit->currency->iso;
                            $currencyList['symbol'] = $credit->currency->symbol;
                            $data['option'][$type]['currency_list'][] = $currencyList;
                            break;

                        case 'ewallet':
                            try {
                                $fpay_ewallet = resolve(FPayEWallet::class);
                                $currencyList = $fpay_ewallet->getCurrencyList();
                                unset($currencyTemp);
                                foreach ($currencyList as &$currencyTemp) {
                                    $currencyTemp['symbol'] = $currencySymbolList[$currencyTemp['currency']] ?? $currencyTemp['currency'];
                                }
                                if (isset($currencyList) && ! empty($currencyList)) {
                                    $data['option'][$type]['currency_list'] = $currencyList;
                                    $data['option'][$type]['option_display'] = Lang::has('lang.' . $type) ? Lang::get('lang.' . $type) : $type;
                                }
                            } catch (\Exception) {
                                break;
                            }
                            break;

                        default:
                            break;
                    }
                }
            }
        }

        $data['payment_methods'] = PaymentMethod::where('status', true)
            ->where('type', PaymentMethod::$type['deposit'])
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'value' => $item->value,
                    'type' => $item->type,
                    'image' => $item->image,
                    'bank_list' => ! empty($item->bank_list) ? json_decode($item->bank_list) : null,
                    'reference' => ! empty($item->reference) ? json_decode($item->reference) : null,
                ];
            }) ?? [];

        $data['payment_options'] = [
            ['name' => 'Main Wallet', 'type' => PaymentMethod::$paymentOption['main_wallet']],
            //   ["name" => "Card", "type" => PaymentMethod::$paymentOption['card']],
        ];

        $data['main_wallet'] = [
            'type' => PaymentMethod::$paymentOption['main_wallet'],
            'balance' => DecimalTrait::setDecimal(Credit::getBalance($userID, Credit::first()->type)),
        ];

        $data['card'] = [
            'type' => PaymentMethod::$paymentOption['card'],
            'list' => UserCard::getCardList(['see_all' => 1, 'user_id' => $userID])['list'],
        ];

        $data['promotions'] = array_merge(
            [['name' => 'No Promotion']],
            Promotion::getPromotionListForDeposit($userID) ?? []
        );

        $data['is_active_promotion'] = ! empty(UserPromotion::getUserPromotionByUserId($userID));

        return ['data' => $data];
    }

    public static function uploadBankReceipt($data = [])
    {
        $params = $data;
        $params['deposit_status'] = 'pending';
        $params['creator_id'] = Auth::user()->id;
        $params['creator_type'] = MODULE;
        $params['amount'] = $data['amount'];
        switch ($data['deposit_type']) {
            case 'thb-manual-bank':
                break;

            default:
                $params['currency'] = config('users.default_currency');
                break;
        }

        try {
            $returnData = self::addDeposit($params);
        } catch (\Exception $e) {
            $errorCode = $e->getCode();
            $errorMsg = $e->getMessage();
            abort($errorCode, $errorMsg);
        }

        $sendTelegram = Telegram::depositOrder($returnData);
        $returnData['send'] = $sendTelegram ?? null;

        return $returnData;
    }

    public static function createDeposit($params = [])
    {
        // $params['deposit_type'] = array_search($params['type'], self::$type);
        $params['deposit_status'] = 'pending';
        // $params['creator_id'] = Auth::user()->id;
        $params['creator_id'] = $params['user_id'];
        $params['creator_type'] = MODULE;
        $params['credit_id'] = $params['credit_id'] ?? null;
        $params['card_id'] = $params['card_id'] ?? null;

        try {
            $returnData = self::newAddDeposit($params);
        } catch (\Exception $e) {
            $errorCode = $e->getCode();
            $errorMsg = $e->getMessage();
            abort($errorCode, $errorMsg);
        }

        return $returnData;
    }

    public static function addOnlineBankDeposit($params = [])
    {
        $params['deposit_type'] = array_search($params['type'], self::$type);
        $params['deposit_status'] = 'pending';
        $params['creator_id'] = Auth::user()->id;
        $params['creator_type'] = MODULE;
        $params['credit_id'] = $params['credit_id'] ?? null;

        try {
            $returnData = self::addDeposit($params);
        } catch (\Exception $e) {
            $errorCode = $e->getCode();
            $errorMsg = $e->getMessage();
            abort($errorCode, $errorMsg);
        }

        return $returnData;
    }

    public static function addEWalletDeposit($params = [])
    {
        $params['deposit_type'] = 'ewallet';
        $params['deposit_status'] = 'pending';
        $params['creator_id'] = Auth::user()->id;
        $params['creator_type'] = MODULE;
        $params['currency'] = config('users.default_currency');

        try {
            $returnData = self::addDeposit($params);
        } catch (\Exception $e) {
            $errorCode = $e->getCode();
            $errorMsg = $e->getMessage();
            abort($errorCode, $errorMsg);
        }

        return $returnData;
    }

    public static function addThirdPartyDeposit($params, $depositType)
    {
        $params['party'] = $depositType['party'];
        $params['deposit_type'] = $depositType['deposit_type'];
        $params['deposit_status'] = 'pending';
        // $params['creator_id'] = Auth::user()->id;
        // $params['creator_type'] = MODULE;
        // $params['currency'] = config('users.default_currency');
        $params['currency'] = $params['currency'];
        $params['credit_id'] = $params['credit_id'] ?? null;

        try {
            $returnData = self::addDeposit($params);
        } catch (\Exception $e) {
            $errorCode = $e->getCode();
            $errorMsg = $e->getMessage();
            abort($errorCode, $errorMsg);
        }

        return $returnData;
    }

    public static function addDeposit(array $params = [])
    {
        if (! isset($params['deposit_type']) || ! in_array($params['deposit_type'], array_keys(self::$type))) {
            throw new \Exception('Invalid Deposit Type', 400);
        }

        if (! isset($params['deposit_status']) || ! in_array($params['deposit_status'], array_keys(self::$status))) {
            throw new \Exception('Invalid Deposit Status', 400);
        }

        $creatorId = isset($params['creator_id']) ? $params['creator_id'] : 0;
        $creatorType = isset($params['creator_type']) ? $params['creator_type'] : 'System';
        $depositStatus = $params['deposit_status'] ?? null;
        $userData = User::find($params['user_id']);
        $creditId = $params['credit_id'] ?? null;

        $depositCredit = CreditSetting::with(['credit.currency'])
            ->when(isset($creditId), function ($q) use ($creditId) {
                return $q->where('credit_id', $creditId);
            })
            ->where([
                'name' => ['is-fundinable'],
                'value' => 1,
            ])->first();
        if (empty($depositCredit)) {
            throw new \Exception('Deposit Credit Not Found', 400);
        }

        $creditType = $depositCredit->credit->type;
        $currency = $params['currency'] ?? ($depositCredit->credit->currency->iso ?? null);

        $dateTime = $params['datetime'] ?? date('Y-m-d H:i:s');
        $amount = $params['amount'] ?? 0;
        $remark = $params['remark'] ?? null;
        $party = $params['party'] ?? null;

        $charge = 0;

        // IMPORTANT : if rate change from 1 and charge change, need tune fpay callback for online-bank-telco
        $depositCode = Traits\GenerateNumberTrait::GenerateRandomAlphanumeric(self::query(), 'code') ?? 0;
        switch ($params['deposit_type']) {
            case 'manual-bank':
            case 'thb-manual-bank':
                $rate = CurrencyRate::getCurrencyRate($currency, $currency);
                if (! isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
                    throw new \Exception('Deposit Rete Not Found', 400);
                }

                $convertedAmount = $amount / $rate['deposit_rate'];
                $charge = 0; // temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            case 'online-bank':
                $userPhoneAry = explode('-', $userData->phone_no);
                $fpay = resolve(FPay::class);
                $fpayOrder = $fpay->generateOrder($amount, $currency, null, $userPhoneAry[0] . $userPhoneAry[1], $mode = '', $depositCode);
                // case 'online-bank-onepay':
                //     $userPhoneAry = explode('-', $userData->phone_no);
                //     $fpay = resolve(OnePay::class);
                //     $fpayOrder = $fpay->generateOrder($amount, $currency, null, $userPhoneAry[0] . $userPhoneAry[1], $mode = '', $depositCode);
            case 'online-bank-tk8':
                $params['FPay-order-id'] = $fpayOrder['orderId'] ?? $params['orderId'] ?? null;
                $params['FPay-url'] = $fpayOrder['payUrl'] ?? $params['payUrl'] ?? null;
                $depositCode = $params['FPay-order-id'];

                $rate = CurrencyRate::getCurrencyRate($currency, $currency);
                if (! isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
                    throw new \Exception('Deposit Rete Not Found', 400);
                }

                $convertedAmount = $amount / $rate['deposit_rate'];
                $charge = 0; // temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            case 'crypto':
                $rate = CurrencyRate::getCurrencyRate('USDT', 'USDT');

                $convertedAmount = $amount / $rate['deposit_rate'];

                $charge = 0; // temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            case 'ewallet':
                $userPhoneAry = explode('-', $userData->phone_no);
                $fpay_ewallet = resolve(FPayEWallet::class);
                $fpayOrder = $fpay_ewallet->generateOrder($amount, $currency, null, $userPhoneAry[0] . $userPhoneAry[1], $mode = '', $depositCode);

                $params['FPay-order-id'] = $fpayOrder['orderId'];
                $params['FPay-url'] = $fpayOrder['payUrl'];

                $rate = CurrencyRate::getCurrencyRate($currency, $currency);
                if (! isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
                    throw new \Exception('Deposit Rete Not Found', 400);
                }

                $convertedAmount = ($amount / $rate['deposit_rate']);
                $charge = 0; // Temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            case 'online-bank-telco':
                $userPhoneAry = explode('-', $userData->phone_no);
                $fpayTelco = resolve(FPayTelco::class);
                $fpayOrder = $fpayTelco->generateOrder($amount, $currency, null, $userPhoneAry[0] . $userPhoneAry[1], $mode = '', $depositCode);
                $params['FPay-order-id'] = $fpayOrder['orderId'] ?? $params['orderId'] ?? null;
                $params['FPay-url'] = $fpayOrder['payUrl'] ?? $params['payUrl'] ?? null;
                $depositCode = $params['FPay-order-id'];

                $rate = CurrencyRate::getCurrencyRate($currency, $currency);
                if (! isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
                    throw new \Exception('Deposit Rete Not Found', 400);
                }

                // IMPORTANT : if rate change from 1 and charge change, need tune fpay callback for online-bank-telco
                $convertedAmount = ($amount / $rate['deposit_rate']);
                $charge = 0; // Temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            case 'online-bank-thai':
                $userPhoneAry = explode('-', $userData->phone_no);
                $fpayThai = resolve(FPayThai::class);
                $fpayOrder = $fpayThai->generateOrder($amount, $currency, null, $userPhoneAry[0] . $userPhoneAry[1], $mode = '', $depositCode);
            case 'online-bank-tk8th':
                $params['FPay-order-id'] = $fpayOrder['orderId'] ?? $params['orderId'] ?? null;
                $params['FPay-url'] = $fpayOrder['payUrl'] ?? $params['payUrl'] ?? null;
                $depositCode = $params['FPay-order-id'];

                $rate = CurrencyRate::getCurrencyRate($currency, $currency);
                if (! isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
                    throw new \Exception('Deposit Rete Not Found', 400);
                }

                $convertedAmount = ($amount / $rate['deposit_rate']);
                $charge = 0; // Temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            default:

                break;
        }

        $depositId = null;

        $receivableAmount = Traits\DecimalTrait::setDecimal(($convertedAmount - $charge));
        if (! isset($receivableAmount) || $receivableAmount < 0) {
            $receivableAmount = 0;
        }

        $insert = [
            'code' => $depositCode,
            'user_id' => $params['user_id'] ?? 0,
            'type' => self::$type[$params['deposit_type']],
            'reference_id' => $params['reference_id'] ?? 0,
            'credit_type' => $creditType,
            'currency' => $currency,
            'amount' => $amount,
            'rate' => $rate['deposit_rate'],
            'converted_amount' => $convertedAmount,
            'charges' => $charge,
            'receivable_amount' => $receivableAmount,
            'status' => self::$status['pending'],
            'user' => self::$status['pending'],
            'creator_id' => $creatorId,
            'creator_type' => $creatorType,
            'created_at' => $dateTime,
            'user_remark' => $remark ?? null,
            'party' => $party ?? null,
        ];

        $depositDetail = $params;
        unset($depositDetail['user_id']);
        unset($depositDetail['deposit_type']);
        unset($depositDetail['deposit_status']);
        unset($depositDetail['reference_id']);
        unset($depositDetail['currency']);
        unset($depositDetail['remark']);
        unset($depositDetail['creator_type']);
        unset($depositDetail['creator_id']);

        DB::transaction(function () use (&$insert, $depositStatus, &$depositId, $dateTime, $depositDetail) {
            $deposit = self::create($insert);
            if (! $deposit) {
                throw new \Exception('Failed add deposit', 400);
            }

            $depositId = $deposit->id ?? null;

            if ($depositStatus == 'approved' && ! empty($depositId)) {
                $approveParams = [
                    'id' => $depositId,
                    'status' => $depositStatus,
                    'datetime' => $dateTime,
                ];

                $updateRes = self::updateDeposit($approveParams);
            }

            if (isset($depositDetail) && ! empty($depositDetail)) {
                foreach ($depositDetail as $detailName => $detailValue) {
                    DepositDetail::create(['deposit_id' => $depositId,  'name' => $detailName, 'value' => $detailValue]);
                }
            }
        });

        return [
            'transaction_id' => $insert['code'] ?? null,
            'reference_no' => $depositDetail['reference_no'] ?? null,
            'remark' => $insert['user_remark'] ?? null,
            'created_at' => $dateTime ? DateTrait::dateFormat($dateTime) : '-',
            'deposit_type' => Lang::has('lang.' . $params['deposit_type']) ? Lang::get('lang.' . $params['deposit_type']) : $params['deposit_type'],
            'amount' => $insert['amount'] ? DecimalTrait::setDecimal($insert['amount']) : 0.00,
            'fpay_url' => $params['FPay-url'] ?? null,
            'fpay_redirect_url' => env('FPAY_REDIRECT_URL') . '?order_id=' . $depositCode,
            'currency' => $currency,
        ];
    }

    public static function newAddDeposit(array $params = [])
    {
        // if (!isset($params['deposit_type']) || !in_array($params['deposit_type'], array_keys(self::$type))) {
        //     throw new \Exception("Invalid Deposit Type", 400);
        // }

        if (! isset($params['deposit_status']) || ! in_array($params['deposit_status'], array_keys(self::$status))) {
            throw new \Exception('Invalid Deposit Status', 400);
        }

        $paymentMethod = PaymentMethod::getActiveDepositPaymentMethodById($params['payment_method_id']);

        $userId = $params['user_id'];
        $bankId = $params['bank_id'] ?? null;
        $creatorId = isset($params['creator_id']) ? $params['creator_id'] : 0;
        $creatorType = isset($params['creator_type']) ? $params['creator_type'] : 'System';
        $depositStatus = $params['deposit_status'] ?? null;
        $userData = User::find($params['user_id']);
        $creditId = $params['credit_id'] ?? null;
        $remark = $params['remark'] ?? null;
        $promotionId = $params['promotion_id'] ?? null;
        $promotion = $promotionId ? Promotion::find($promotionId) : null;
        $userPromotion = UserPromotion::getUserPromotionByUserId($userId);

        $depositCredit = CreditSetting::with(['credit.currency'])
            ->when(isset($creditId), function ($q) use ($creditId) {
                return $q->where('credit_id', $creditId);
            })
            ->where([
                'name' => ['is-fundinable'],
                'value' => 1,
            ])->first();

        if (empty($depositCredit)) {
            throw new \Exception('Deposit Credit Not Found', 400);
        }

        if ($userPromotion && $promotionId) {
            throw new \Exception('Promotion is active', 400);
        }

        $creditType = $depositCredit->credit->type;
        $currency = $params['currency'] ?? ($depositCredit->credit->currency->iso ?? null);

        $dateTime = $params['datetime'] ?? date('Y-m-d H:i:s');
        $amount = $params['amount'] ?? 0;
        $remark = $params['remark'] ?? null;
        $party = $params['party'] ?? null;

        $charge = 0;

        // IMPORTANT : if rate change from 1 and charge change, need tune fpay callback for online-bank-telco
        $depositCode = Traits\GenerateNumberTrait::GenerateRandomAlphanumeric(self::query(), 'code') ?? 0;
        switch ($paymentMethod->value) {
            case 'manual-bank':
            case 'thb-manual-bank':
                $deposit_type = self::$type['manual-bank'];
                $rate = CurrencyRate::getCurrencyRate($currency, $currency);
                if (! isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
                    throw new \Exception('Deposit Rete Not Found', 400);
                }

                $convertedAmount = $amount / $rate['deposit_rate'];
                $charge = 0; // temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            case 'fpay-online-bank':
                $deposit_type = self::$type['online-bank'];
                $userPhoneAry = explode('-', $userData->phone_no);
                $fpay = resolve(FPay::class);
                $fpayOrder = $fpay->generateOrder($amount, $currency, null, $userPhoneAry[0] . $userPhoneAry[1], $mode = '', $depositCode);

                $params['FPay-order-id'] = $fpayOrder['orderId'] ?? $params['orderId'] ?? null;
                $params['FPay-url'] = $fpayOrder['payUrl'] ?? $params['payUrl'] ?? null;
                $depositCode = $params['FPay-order-id'];

                $rate = CurrencyRate::getCurrencyRate($currency, $currency);
                if (! isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
                    throw new \Exception('Deposit Rete Not Found', 400);
                }

                $convertedAmount = $amount / $rate['deposit_rate'];
                $charge = 0; // temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            case 'onepay-online-bank':
                $deposit_type = self::$type['onepay-online-bank'];
                $method = 'online_banking';
                $userPhoneAry = explode('-', $userData->phone_no);
                $fpay = resolve(OnePay::class);
                $fpayOrder = $fpay->generateOrder($amount, $currency, null, $userPhoneAry[0] . $userPhoneAry[1], $method, $depositCode, $bankId);

                $params['FPay-order-id'] = $fpayOrder['orderId'] ?? $params['orderId'] ?? null;
                $params['FPay-url'] = $fpayOrder['payUrl'] ?? $params['payUrl'] ?? null;
                $depositCode = $params['FPay-order-id'];

                $rate = CurrencyRate::getCurrencyRate($currency, $currency);
                if (! isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
                    throw new \Exception('Deposit Rete Not Found', 400);
                }

                $convertedAmount = $amount / $rate['deposit_rate'];
                $charge = 0; // temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            // case 'crypto':
            //     $rate = CurrencyRate::getCurrencyRate('USDT', 'USDT');
            //
            //     $convertedAmount = $amount / $rate['deposit_rate'];
            //
            //     $charge = 0; // temporary
            //     $minCharge = 0;
            //
            //     if ($charge < $minCharge) {
            //         $charge = $minCharge;
            //     }
            //
            //     if ($charge > $amount) {
            //         $charge = $amount;
            //     }
            //     break;

            case 'fpay-ewallet':
            case 'ewallet':
                $deposit_type = self::$type['ewallet'];
                $userPhoneAry = explode('-', $userData->phone_no);
                $fpay_ewallet = resolve(FPayEWallet::class);
                $fpayOrder = $fpay_ewallet->generateOrder($amount, $currency, null, $userPhoneAry[0] . $userPhoneAry[1], $mode = '', $depositCode);

                $params['FPay-order-id'] = $fpayOrder['orderId'];
                $params['FPay-url'] = $fpayOrder['payUrl'];

                $rate = CurrencyRate::getCurrencyRate($currency, $currency);
                if (! isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
                    throw new \Exception('Deposit Rete Not Found', 400);
                }

                $convertedAmount = ($amount / $rate['deposit_rate']);
                $charge = 0; // Temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            case 'onepay-ewallet':
                $deposit_type = self::$type['onepay-ewallet'];
                $method = 'duitnowqr';
                $userPhoneAry = explode('-', $userData->phone_no);
                $fpay_ewallet = resolve(OnePay::class);
                $fpayOrder = $fpay_ewallet->generateOrder($amount, $currency, null, $userPhoneAry[0] . $userPhoneAry[1], $method, $depositCode);

                $params['FPay-order-id'] = $fpayOrder['orderId'];
                $params['FPay-url'] = $fpayOrder['payUrl'];

                $rate = CurrencyRate::getCurrencyRate($currency, $currency);
                if (! isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
                    throw new \Exception('Deposit Rete Not Found', 400);
                }

                $convertedAmount = ($amount / $rate['deposit_rate']);
                $charge = 0; // Temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            case 'rapidpay-online-bank':
                $deposit_type = self::$type['rapidpay-online-bank'];
                $userPhoneAry = explode('-', $userData->phone_no);
                $rapidpay = resolve(RapidPay::class);
                $fpayOrder = $rapidpay->generateOrder($amount, $depositCode, $remark);

                $params['FPay-order-id'] = $fpayOrder['orderId'];
                $params['FPay-url'] = $fpayOrder['payUrl'];

                $rate = CurrencyRate::getCurrencyRate($currency, $currency);
                if (! isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
                    throw new \Exception('Deposit Rate Not Found', 400);
                }

                $convertedAmount = ($amount / $rate['deposit_rate']);
                $charge = 0; // Temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            case 'wepay-online-bank':
                $deposit_type = self::$type['wepay-online-bank'];
                $userPhoneAry = explode('-', $userData->phone_no);
                $rapidpay = resolve(WePay::class);
                $fpayOrder = $rapidpay->generateOrder($amount, $depositCode, $remark);

                $params['FPay-order-id'] = $fpayOrder['orderId'];
                $params['FPay-url'] = $fpayOrder['payUrl'];

                $rate = CurrencyRate::getCurrencyRate($currency, $currency);
                if (! isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
                    throw new \Exception('Deposit Rate Not Found', 400);
                }

                $convertedAmount = ($amount / $rate['deposit_rate']);
                $charge = 0; // Temporary
                $minCharge = 0;

                if ($charge < $minCharge) {
                    $charge = $minCharge;
                }

                if ($charge > $amount) {
                    $charge = $amount;
                }
                break;

            // case 'online-bank-telco':
            //     $userPhoneAry = explode('-', $userData->phone_no);
            //     $fpayTelco = resolve(FPayTelco::class);
            //     $fpayOrder = $fpayTelco->generateOrder($amount, $currency, null, $userPhoneAry[0] . $userPhoneAry[1], $mode = '', $depositCode);
            //     $params['FPay-order-id'] = $fpayOrder['orderId'] ?? $params['orderId'] ?? null;
            //     $params['FPay-url'] = $fpayOrder['payUrl'] ?? $params['payUrl'] ?? null;
            //     $depositCode = $params['FPay-order-id'];
            //
            //     $rate = CurrencyRate::getCurrencyRate($currency, $currency);
            //     if (!isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
            //         throw new \Exception('Deposit Rete Not Found', 400);
            //     }
            //
            //     // IMPORTANT : if rate change from 1 and charge change, need tune fpay callback for online-bank-telco
            //     $convertedAmount = ($amount / $rate['deposit_rate']);
            //     $charge = 0; // Temporary
            //     $minCharge = 0;
            //
            //     if ($charge < $minCharge) {
            //         $charge = $minCharge;
            //     }
            //
            //     if ($charge > $amount) {
            //         $charge = $amount;
            //     }
            //     break;

            // case 'online-bank-thai':
            //     $userPhoneAry = explode('-', $userData->phone_no);
            //     $fpayThai = resolve(FPayThai::class);
            //     $fpayOrder = $fpayThai->generateOrder($amount, $currency, null, $userPhoneAry[0] . $userPhoneAry[1], $mode = '', $depositCode);
            // case 'online-bank-tk8th':
            //     $params['FPay-order-id'] = $fpayOrder['orderId'] ?? $params['orderId'] ?? null;
            //     $params['FPay-url'] = $fpayOrder['payUrl'] ?? $params['payUrl'] ?? null;
            //     $depositCode = $params['FPay-order-id'];
            //
            //     $rate = CurrencyRate::getCurrencyRate($currency, $currency);
            //     if (!isset($rate['deposit_rate']) || empty($rate['deposit_rate'])) {
            //         throw new \Exception('Deposit Rete Not Found', 400);
            //     }
            //
            //     $convertedAmount = ($amount / $rate['deposit_rate']);
            //     $charge = 0; // Temporary
            //     $minCharge = 0;
            //
            //     if ($charge < $minCharge) {
            //         $charge = $minCharge;
            //     }
            //
            //     if ($charge > $amount) {
            //         $charge = $amount;
            //     }
            //     break;

            default:
                break;
        }

        $depositId = null;

        $receivableAmount = Traits\DecimalTrait::setDecimal(($convertedAmount - $charge));
        if (! isset($receivableAmount) || $receivableAmount < 0) {
            $receivableAmount = 0;
        }

        $insert = [
            'code' => $depositCode,
            'user_id' => $params['user_id'] ?? 0,
            'type' => $deposit_type,
            'reference_id' => $params['reference_id'] ?? 0,
            'credit_type' => $creditType,
            'currency' => $currency,
            'amount' => $amount,
            'rate' => $rate['deposit_rate'],
            'converted_amount' => $convertedAmount,
            'charges' => $charge,
            'receivable_amount' => $receivableAmount,
            'status' => self::$status['pending'],
            'user' => self::$status['pending'],
            'creator_id' => $creatorId,
            'creator_type' => $creatorType,
            'created_at' => $dateTime,
            'user_remark' => $remark ?? null,
            'party' => $party ?? null,
            'card_id' => $params['card_id'],
        ];

        $depositDetail = $params;
        unset($depositDetail['user_id']);
        unset($depositDetail['deposit_type']);
        unset($depositDetail['deposit_status']);
        unset($depositDetail['reference_id']);
        unset($depositDetail['currency']);
        unset($depositDetail['remark']);
        unset($depositDetail['creator_type']);
        unset($depositDetail['creator_id']);

        DB::transaction(function () use (&$insert, $depositStatus, &$depositId, $dateTime, $depositDetail, $promotionId) {
            $deposit = self::create($insert);
            if (! $deposit) {
                throw new \Exception('Failed add deposit', 400);
            }

            $depositId = $deposit->id ?? null;

            if ($depositStatus == 'approved' && ! empty($depositId)) {
                $approveParams = [
                    'id' => $depositId,
                    'status' => $depositStatus,
                    'datetime' => $dateTime,
                ];

                $updateRes = self::updateDeposit($approveParams);
            }

            if (isset($depositDetail) && ! empty($depositDetail)) {
                foreach ($depositDetail as $detailName => $detailValue) {
                    DepositDetail::create(['deposit_id' => $depositId,  'name' => $detailName, 'value' => $detailValue]);
                }

                if ($promotionId) {
                    DepositDetail::create(['deposit_id' => $depositId,  'name' => 'promotion_id', 'value' => $promotionId]);
                }
            }
        });

        // if ($promotion && ! $userPromotion) {
        //     $bonusAmount = UserPromotion::applyPromotionForDeposit($promotionId, $amount)['bonus_amount'];
        //     $userProduct = UserProduct::where('user_id', $userId)->where('product_id', $promotion->product_id)->first();
        //
        //     DB::transaction(function () use ($userProduct, $promotion, $amount, $bonusAmount) {
        //         $totalAmount = $amount + $bonusAmount;
        //         $belongId = Traits\GenerateNumberTrait::GenerateNewId() ?? 0;
        //         $batchId = $belongId;
        //         $creditName = Credit::where('type', 'myr-credit')->orderBy('priority', 'ASC')->first()->name;
        //         $product = $userProduct->product;
        //         $internalID = User::select('id')->where('username', 'bonusPayout')->where('user_type', User::$userType['internal-account'])->first()?->id ?? null;
        //
        //         if (empty($internalID)) {
        //             throw new \Exception('Invalid Internal Account.', 400);
        //         }
        //
        //         ExTransfer::transferInWithBonus([
        //             'amount' => $amount,
        //             'bonus_amount' => $bonusAmount,
        //             'credit_id' => 1000,
        //             'product_name' => $product->name,
        //             'product_id' => $userProduct->product_id,
        //             'user_id' => $userProduct->user_id,
        //             'credit_type' => Credit::first()->type,
        //             'curl_type' => 'refund',
        //             'product_data' => $product,
        //             'wallet_data' => [],
        //             'exMemberId' => $userProduct->member_id,
        //             'wallet_type' => 'Main',
        //         ]);
        //
        //         // if (! $res) {
        //         //     abort(400, json_encode(['msg' => ['Transfer Failed']]));
        //         // }
        //
        //         // $userPromotion = UserPromotion::getUserPromotionByUserId($userId);
        //         UserPromotion::updateUserPromotion(['achieved_turnover' => 0, 'game_return_amount' => 0]);
        //         // CreditTransaction::insertTransaction($userId, $internalID, $userId, $creditName, $bonusAmount, 'promotion-bonus-payout', $belongId, $batchId, null, date('Y-m-d H:i:s'), null, null, null, $userPromotion->id);
        //
        //         switch ($promotion->product_id) {
        //             case Product::$id['TK8']:
        //                 // code...
        //                 break;
        //             case Product::$id['MT']:
        //                 // code...
        //                 break;
        //             case Product::$id['CQ9']:
        //                 // code...
        //                 break;
        //             case Product::$id['JK']:
        //                 resolve(JK::class)->getLoginUrl($userProduct->member_id, $totalAmount, $userProduct->user->store->jk_agent_id, false);
        //                 break;
        //             case Product::$id['JILI']:
        //                 resolve(JILI::class)->deposit($userProduct->member_id, $totalAmount);
        //                 break;
        //             case Product::$id['ATLASPLAY']:
        //                 // code...
        //                 break;
        //         }
        //     });
        // }

        return [
            'transaction_id' => $insert['code'] ?? null,
            'reference_no' => $depositDetail['reference_no'] ?? null,
            'remark' => $insert['user_remark'] ?? null,
            'created_at' => $dateTime ? DateTrait::dateFormat($dateTime) : '-',
            'deposit_type' => Lang::has('lang.' . $deposit_type) ? Lang::get('lang.' . $deposit_type) : $deposit_type,
            'amount' => $insert['amount'] ? DecimalTrait::setDecimal($insert['amount']) : 0.00,
            'fpay_url' => $params['FPay-url'] ?? null,
            'fpay_redirect_url' => env('FPAY_REDIRECT_URL') . '?order_id=' . $depositCode,
            'currency' => $currency,
        ];
    }

    public static function getList(array $params = [])
    {
        $username = $params['username'] ?? null;
        $memberID = $params['member_id'] ?? null;
        $phoneNo = $params['phone_no'] ?? null;
        $storeId = $params['store_id'] ?? null;
        $exStoreId = $params['extra_store_id'] ?? null;
        $transactionID = $params['transaction_id'] ?? null;
        $referenceNo = $params['reference_no'] ?? null;
        $type = $params['type'] ?? null;
        $fromDate = $params['from_date'] ?? null;
        $toDate = $params['to_date'] ?? null;
        $updatedFromDate = $params['updated_from_date'] ?? null;
        $updatedToDate = $params['updated_to_date'] ?? null;
        $status = $params['status'] ?? null;
        $country_id = $params['country'] ?? null;
        $creditType = $params['credit_type'] ?? null;
        $txId = $params['tx_id'] ?? null;
        $seeAll = $params['see_all'] ?? null;

        $statusFilter = self::$status[$status] ?? null;
        if (is_array($type)) {
            $typeFilter = $type;
        } else {
            $typeFilter = self::$type[$type] ?? null;
        }

        $sortableColumns = [];
        if (isset($params['export']) && ($params['export'] == 1)) {
            $params['model'] = get_class() ?? null;
            $params['function'] = __FUNCTION__;
            ExportReport::exportReport($params);
        }

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        $items = self::query()
            ->with(['userDetail', 'adminDetail', 'depositDetail', 'userDetail.store'])
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('created_at'), '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where(DB::raw('created_at'), '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when((isset($updatedFromDate) && isset($updatedToDate)), function ($q) use ($updatedFromDate, $updatedToDate) {
                $q->where('approved_at', '>=', date('Y-m-d 00:00:00', strtotime($updatedFromDate)));

                return $q->where('approved_at', '<=', date('Y-m-d 23:59:59', strtotime($updatedToDate)));
            })
            ->when(isset($creditType), function ($q) use ($creditType) {
                return $q->where('credit_type', $creditType);
            })
            ->when(isset($username), function ($q) use ($username) {
                return $q->whereRelation('userDetail', 'username', 'like', '%' . $username . '%');
            })
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('userDetail.store', 'store_id', $storeId);
            })
            ->when(isset($exStoreId), function ($q) use ($exStoreId) {
                return $q->whereHas('userDetail.store', function ($q) use ($exStoreId) {
                    $q->whereIn('id', $exStoreId);
                });
            })
            ->when(isset($memberID), function ($q) use ($memberID) {
                return $q->whereRelation('userDetail', 'member_id', 'like', '%' . $memberID . '%');
            })
            ->when(isset($phoneNo), function ($q) use ($phoneNo) {
                return $q->whereRelation('userDetail', function ($q) use ($phoneNo) {
                    $phoneNo = preg_replace('/[^0-9]/', '', $phoneNo);
                    $q->where('phone_no', 'LIKE', "%$phoneNo%");
                });
            })
            ->when(isset($transactionID), function ($q) use ($transactionID) {
                return $q->where('code', 'like', '%' . $transactionID . '%');
            })
            ->when(isset($referenceNo), function ($q) use ($referenceNo) {
                $q->whereRelation('depositDetail', 'name', 'reference_no');

                return $q->whereRelation('depositDetail', 'value', $referenceNo);
            })
            ->when(isset($type), function ($q) use ($typeFilter) {
                if (is_array($typeFilter)) {
                    return $q->whereIn('type', $typeFilter);
                } else {
                    return $q->where('type', $typeFilter);
                }
            })
            ->when(isset($params['user_id']), function ($q) use ($params) {
                return $q->where('user_id', $params['user_id']);
            })
            ->when(isset($status), function ($q) use ($statusFilter) {
                return $q->where('status', $statusFilter);
            })
            ->when(isset($txId), function ($q) use ($txId) {
                return $q->whereHas('transactionLogs', function ($query) use ($txId) {
                    return $query->where('tx_id', 'like', '%' . $txId . '%');
                });
            })
            ->when(isset($country_id), function ($query) use ($country_id) {
                $query->whereRelation('userDetail', 'country_id', $country_id);
            });

        $summaryQuery = clone $items;
        $items = $items->orderBy($order_by, $order_sort)
            ->when((! isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $tableTotal = ['total_amount' => '0'];
        if (MODULE == 'admin') {
            $summary = ['total_amount' => 0];
            foreach (self::$status as $statusKey => $statusValue) {
                $summary['total_' . $statusKey . '_amount'] = 0;
            }

            $summaryQuery = $summaryQuery->groupBy('status')->selectRaw('SUM(amount) as amount, status')->get()->map(function ($q) use (&$summary) {
                $summary['total_amount'] = DecimalTrait::setDecimal(($summary['total_amount'] + $q->amount));
                $depositStatus = array_search($q->status, self::$status);
                $summary['total_' . $depositStatus . '_amount'] = DecimalTrait::setDecimal(($summary['total_' . $depositStatus . '_amount'] + $q->amount));
            });
        }

        $mapFunc = function ($q) use (&$summary, &$tableTotal) {
            $status = array_search($q->status, self::$status);
            $type = array_search($q->type, self::$type);
            $details = [];
            switch ($type) {
                case 'manual-bank':
                case 'thb-manual-bank':
                    if (isset($q->depositDetail)) {
                        foreach ($q->depositDetail as $detail) {
                            $details[$detail->name] = $detail->value ?? null;
                        }
                    }
                    break;

                case 'crypto':
                    if (isset($q->transactionLogs->result)) {
                        $result = json_decode($q->transactionLogs->result, true);
                    }
                    $fromAddress = $result['fromAddress'] ?? null;
                    $txId = $q->transactionLogs->tx_id ?? null;
                    break;

                default:
                    break;
            }
            if (MODULE == 'admin') {
                $adminRes = [
                    'id' => $q->id,
                    'username' => $q->userDetail->username,
                    'member_id' => $q->userDetail->member_id,
                    'phone_no' => $q->userDetail->phone_no ?? null,
                    'payment_type' => $type,
                    'payment_type_display' => Lang::has('lang.' . $type) ? Lang::get('lang.' . $type) : $type,
                    'payment_id' => $q->reference_id,
                    'store_name' => $q->userDetail->store->name ?? null,
                    'code' => $q->code,
                    'credit_type' => $q->credit_type,
                    'credit_type_display' => Lang::has('lang.' . $q->credit_type) ? Lang::get('lang.' . $q->credit_type) : $q->credit_type,
                    'currency' => $q->currency,
                    'country' => $q->userDetail->country->name,
                    'country_display' => Lang::has('lang.' . $q->userDetail->country->name) ? Lang::get('lang.' . $q->userDetail->country->name) : $q->userDetail->country->name,
                    'amount' => DecimalTrait::setDecimal($q->amount),
                    'rate' => DecimalTrait::setDecimal($q->rate),
                    'converted_amount' => DecimalTrait::setDecimal($q->converted_amount),
                    'charges' => DecimalTrait::setDecimal($q->charges),
                    'attachment' => $details['attachment'] ?? null,
                    'reference_no' => $details['reference_no'] ?? null,
                    'user_remark' => $q->user_remark ?? null,
                    'admin_remark' => $q->admin_remark ?? null,
                    'approved_at' => DateTrait::dateFormat($q->approved_at),
                    'updater' => $q->adminDetail->username ?? null,
                    'status' => $status,
                    'status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                ];
            }

            $res = [
                'id' => $q->id,
                'created_at' => DateTrait::dateFormat($q->created_at),
                'updated_at' => DateTrait::dateFormat($q->updated_at),
                'transaction_id' => $q->code,
                'payment_type' => $type,
                'payment_type_display' => Lang::has('lang.' . $type) ? Lang::get('lang.' . $type) : $type,
                'from_address' => $fromAddress ?? '-',
                'receivable_amount' => DecimalTrait::setDecimal($q->receivable_amount),
                'currency' => $q->currency,
                'tx_id' => $txId ?? '-',
                'tx_id_hyperlink' => isset($txId) ? env('TRON_TRXN_HYPERLINK') . $txId : '-',
                'status' => $status,
                'attachment' => $details['attachment'] ?? null,
                'reference_no' => $details['reference_no'] ?? null,
                'user_remark' => $q->user_remark ?? null,
                'admin_remark' => $q->admin_remark ?? null,
                'status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
            ];

            $tableTotal['total_amount'] = DecimalTrait::setDecimal(($tableTotal['total_amount'] + ($q->amount ?? 0)));

            return (object) array_merge($res, ($adminRes ?? []));
        };

        if (($seeAll == 1)) {
            return ['list' => $items->get()->map($mapFunc)->toArray()];
        } else {
            $items->getCollection()->transform($mapFunc);
            if (MODULE == 'admin') {
                return (new ItemsCollection($items))->toArray() + ['summary' => $summary, 'table_total' => $tableTotal];
            }

            return (new ItemsCollection($items))->toArray();
        }
    }

    public static function getDepositDet(array $params = [])
    {
        $s3Client = null;
        $items = self::query()
            ->with(['userDetail', 'adminDetail', 'depositDetail'])
            ->where('id', $params['id'])
            ->when(MODULE != 'admin', function ($q) {
                return $q->where('user_id', Auth::user()->id);
            })
            ->get()
            ->map(function ($q) use (&$s3Client) {
                $details = [];
                $imgRes = [];
                $status = array_search($q->status, self::$status);
                $type = array_search($q->type, self::$type);
                switch ($type) {
                    case 'manual-bank':
                    case 'thb-manual-bank':
                        if (isset($q->depositDetail)) {
                            foreach ($q->depositDetail as $detail) {
                                $details[$detail->name] = $detail->value ?? null;
                            }
                        }
                        if (! $s3Client) {
                            $s3Client = S3Trait::getS3Client();
                        }
                        $downloadImage = [];
                        if (isset($details['attachment'])) {
                            array_push($downloadImage, $details['attachment']);
                        }
                        if (! empty($downloadImage)) {
                            $imgRes = S3Trait::awsDownload($s3Client, $downloadImage);
                        }
                        break;

                    case 'crypto':
                        $paymentNumber = $q->transactionLogs->payment_number ?? null;
                        break;
                    default:
                        $paymentNumber = null;
                        break;
                }
                $res = [
                    'id' => $q->id,
                    'name' => $q->userDetail->name,
                    'username' => $q->userDetail->username,
                    'member_id' => $q->userDetail->member_id,
                    'phone_no' => $q->userDetail->phone_no,
                    'payment_type' => $type,
                    'payment_type_display' => Lang::has('lang.' . $type) ? Lang::get('lang.' . $type) : $type,
                    'payment_id' => $q->reference_id,
                    'payment_number' => $paymentNumber ?? null,
                    'code' => $q->code,
                    'type' => $q->type,
                    'type_display' => Lang::has('lang.' . $q->type) ? Lang::get('lang.' . $q->type) : $q->type,
                    'credit_type' => $q->credit_type,
                    'credit_type_display' => Lang::has('lang.' . $q->credit_type) ? Lang::get('lang.' . $q->credit_type) : $q->credit_type,
                    'currency' => $q->currency,
                    'status' => $status,
                    'status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                    'amount' => DecimalTrait::setDecimal($q->amount),
                    'rate' => DecimalTrait::setDecimal($q->rate),
                    'converted_amount' => DecimalTrait::setDecimal($q->converted_amount),
                    'charges' => DecimalTrait::setDecimal($q->charges),
                    'receivable_amount' => DecimalTrait::setDecimal($q->receivable_amount),
                    'approved_at' => isset($q->approved_at) ? DateTrait::dateFormat($q->approved_at) : null,
                    'created_at' => DateTrait::dateFormat($q->created_at),
                    'updater' => $q->adminDetail->username ?? null,
                    'attachment' => isset($details['attachment']) ? (isset($imgRes[$details['attachment']]) ? $imgRes[$details['attachment']] : null) : null,
                    'reference_no' => $details['reference_no'] ?? null,
                    'user_remark' => $q->user_remark ?? null,
                    'admin_remark' => $q->admin_remark ?? null,
                ];

                switch ($q->type) {
                    case self::$type['crypto']:
                        $crytoDataObj = TransactionLogs::where('id', $q->reference_id)->first(['tx_id', DB::raw('JSON_UNQUOTE(JSON_EXTRACT(JSON_UNQUOTE(result), \'$.toAddress\')) AS to_address'), DB::raw('JSON_UNQUOTE(JSON_EXTRACT(JSON_UNQUOTE(result), \'$.fromAddress\')) AS from_address')]);
                        if (! empty($crytoDataObj)) {
                            $res = $res + $crytoDataObj->toArray();
                        }
                        break;

                    default:
                        // code...
                        break;
                }

                return $res;
            })->first();

        return $items;
    }

    public static function updateDeposit(array $params = [])
    {
        $depositId = $params['id'];
        $dateTime = $params['datetime'] ?? date('Y-m-d H:i:s');
        $updateStatus = $params['status'];

        if (! isset($params['status']) || ! in_array($updateStatus, ['approved', 'rejected', 'cancelled'])) {
            throw new \Exception('Invalid Update Status', 400);
        }

        DB::transaction(function () use ($params, $dateTime, $updateStatus, &$depositData) {
            $depositData = self::where('id', $params['id'])->lockForUpdate()->first();
            $depositId = $depositData->id;

            $belongId = $batchId = null;
            // add rejected for rapidpay
            if (! in_array($depositData->status, [self::$status['pending'], self::$status['rejected']])) {
                throw new \Exception('Record Not In Pending Status', 400);
            }

            $userId = $depositData->user_id ?? 0;
            $receivableAmount = $depositData->receivable_amount ?? 0;
            $charges = $depositData->charges ?? 0;
            $creditType = $depositData->credit_type ?? 0;
            $remark = $params['remark'] ?? null;

            if ($updateStatus == 'approved' && ! empty($userId) && $receivableAmount > 0) {
                $internalID = User::select('id')->where('username', 'creditSales')->where('user_type', User::$userType['internal-account'])->first()->id ?? null;
                if (empty($internalID)) {
                    throw new \Exception('Invalid Internal Account.', 400);
                }
                $belongId = Traits\GenerateNumberTrait::GenerateNewId() ?? 0;
                $batchId = $belongId;
                $creditName = Credit::where('type', $creditType)->orderBy('priority', 'ASC')->first()->name;

                CreditTransaction::insertTransaction($internalID, $userId, $userId, $creditName, $receivableAmount, 'deposit', $belongId, $batchId, $remark, $dateTime, null, null, null);
            }

            $res = self::find($depositId)->update([
                'status' => self::$status[$updateStatus],
                'admin_remark' => $remark,
                'belong_id' => $belongId,
                'batch_id' => $batchId,
                'approved_at' => $dateTime,
                'updater_id' => auth()->user()->id ?? 0,
            ]);

            try {
                $credit = Credit::first();
                if ($updateStatus == 'approved' && ! empty($depositData->card_id && $receivableAmount > 0)) {
                    $activeCard = UserCard::with('user')
                        ->where('status', UserCard::$status['active'])
                        ->where('id', $depositData->card_id)->first() ?? null;
                    if (isset($activeCard)) {
                        $walletData = [
                            'credit_id' => $credit->id,
                            'credit_name' => $credit->name,
                            'credit_type' => $credit->type,
                            'card_serial_no' => $activeCard->card_serial_no,
                            'card_name' => $activeCard->card_name,
                            'name' => $activeCard->user->name,
                            'phone_number' => $activeCard->phone_no,
                        ];

                        ExTransfer::add([
                            'user_id' => $activeCard->user->id,
                            'wallet_data' => $walletData,
                            'credit_id' => $credit->id,
                            'card_id' => $depositData->card_id,
                            'amount' => $receivableAmount,
                            'type' => 1,
                        ]);
                    }
                }
            } catch (\Throwable $th) {
                // throw $th;
            }

            // FEAT: User Reward for Daily Checkin
            if ($updateStatus == 'approved' && ! empty($userId) && $receivableAmount > 0) {
                UserReward::depositUserReward($userId, $depositId, $receivableAmount);
                UserTicketReward::add([
                    'user_id' => $userId,
                    'amount' => $receivableAmount,
                    'ref_id' => $depositId,
                    'is_online' => true,
                ]);

                UserReward::giveFirstTopUpFreeCredit($userId, true);
            }

            $promotionId = DepositDetail::where('deposit_id', $depositId)->where('name', 'promotion_id')->first()?->value ?? null;
            $promotion = $promotionId ? Promotion::find($promotionId) : null;

            $depositData = self::find($params['id']);
            $amount = $depositData?->receivable_amount ?? 0;
            $userPromotion = UserPromotion::getUserPromotionByUserId($depositData->user_id);

            if ($updateStatus == 'approved' && empty($userPromotion) && $promotion) {
                $bonusAmount = UserPromotion::applyPromotionForDeposit($promotionId, $amount, $depositData->user_id)['bonus_amount'];
                $userProduct = UserProduct::with('product')->where('user_id', $userId)->where('product_id', $promotion->product_id)->first();

                // DB::transaction(function () use ($userProduct, $promotion, $amount, $bonusAmount) {
                $totalAmount = $amount + $bonusAmount;
                $belongId = Traits\GenerateNumberTrait::GenerateNewId() ?? 0;
                $batchId = $belongId;
                $creditName = Credit::where('type', 'myr-credit')->orderBy('priority', 'ASC')->first()->name;
                $product = $userProduct->product;
                $internalID = User::select('id')->where('username', 'bonusPayout')->where('user_type', User::$userType['internal-account'])->first()?->id ?? null;

                if (empty($internalID)) {
                    throw new \Exception('Invalid Internal Account.', 400);
                }

                ExTransfer::transferInWithBonus([
                    'amount' => $amount,
                    'bonus_amount' => $bonusAmount,
                    'credit_id' => 1000,
                    'product_name' => $product->name,
                    'product_id' => $userProduct->product_id,
                    'user_id' => $userProduct->user_id,
                    'credit_type' => Credit::first()->type,
                    'curl_type' => 'refund',
                    'product_data' => $product,
                    'wallet_data' => [],
                    'exMemberId' => $userProduct->member_id,
                    'wallet_type' => 'Main',
                ]);
            }
        });

        if (in_array($updateStatus, ['approved', 'rejected'])) {
            $userID = $depositData->user_id;
            $userDeviceToken = UserDevice::where('user_id', $userID)->where('status', UserDevice::$status['active'])->first();
            if (isset($userDeviceToken)) {
                $users = User::find($userID);
                $lang = $users->lang ?? 'en';
                $templateType = null;
                $template = null;
                switch ($updateStatus) {
                    case 'approved':
                        switch (array_search($depositData->type, Deposit::$type)) {
                            case 'thb-manual-bank':
                                $templateType = 'thbManualDepositApproved';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'online-bank':
                            case 'online-bank-onepay':
                                $templateType = 'onlineBankingDepositApproved';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'manual-bank':
                                $templateType = 'manualDepositApproved';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'crypto':
                                $templateType = 'cryptoDepositApproved';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'ewallet':
                                $templateType = 'ewalletDepositApproved';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'online-bank-thai':
                                $templateType = 'onlineBankingThaiDepositApproved';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                        }
                        break;

                    case 'rejected':
                        switch (array_search($depositData->type, Deposit::$type)) {
                            case 'thb-manual-bank':
                                $templateType = 'thbManualDepositRejected';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'online-bank':
                                $templateType = 'onlineBankingDepositRejected';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'manual-bank':
                                $templateType = 'manualDepositRejected';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'crypto':
                                $templateType = 'cryptoDepositRejected';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'ewallet':
                                $templateType = 'ewalletDepositRejected';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                            case 'online-bank-thai':
                                $templateType = 'onlineBankingThaiDepositRejected';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                        }
                        break;
                }

                try {
                    if (isset($templateType) && isset($template)) {
                        $template['body'] = str_replace('{{amount}}', DecimalTrait::setDecimal($depositData->receivable_amount), $template['body']);
                        $credit = Credit::where('type', $depositData->credit_type)->first();
                        Traits\FirebaseTrait::sendNotification($userDeviceToken->token, $template, ['deposit' => $depositData->id, 'type' => $templateType, 'credit_id' => (int) ($credit->id ?? null)], $userDeviceToken->user_id, ['amount' => DecimalTrait::setDecimal($depositData->receivable_amount)]);
                    }
                } catch (\Throwable $th) {
                }
            }
        }

        return true;
    }

    public static function fpayDepositCallBack($params = [], $productData = null)
    {
        $data = $params ?? [];
        $dateTime = date('Y-m-d H:i:s');

        // Prefix variable
        $referenceNumb = $data['order_id'] ?? null;
        $orderStatus = $data['order_status'] ?? null;
        $token = $data['token'] ?? null;
        $amount = $data['amount'] ?? null;

        // region 3rd party insert deposit record
        $currency = $data['currency'] ?? null;
        $userId = null;
        $credit = null;
        $partyProductId = true;
        $partyMemberId = null;

        $depositData = self::where('code', $referenceNumb)
            ->first();

        if (empty($depositData)) {
            throw new \Exception('Invalid Order.');
        }

        $firstTime = false;

        // Check Token
        if ((md5(env('FPAY_SECRET_KEY') . $referenceNumb) != $token)
            && (md5(env('FPAY_SECRET_KEY_EWALLET') . $referenceNumb) != $token)
            && (md5(env('FPAY_SECRET_KEY_THAI') . $referenceNumb) != $token)
            && (md5(env('FPAY_SECRET_KEY_TELCO') . $referenceNumb) != $token)
        ) {
            throw new \Exception('Invalid Token.');
        }

        if (empty($depositData)) {
            $productData = ThirdPartyTrait::getDataFromCurrency($currency);
            // get data from 3rd party
            switch ($productData['party']) {
                case 'TK8':
                    $res = Traits\OCTK8Trait::postOC(['data' => $data], 'verify_transaction');
                    break;
            }

            if (
                isset($res) && isset($res['status']) && $res['status'] && isset($res['data'])
                && isset($res['data']['account'])
                && isset($res['data']['order_id'])
                // && isset($res['data']['pay_url'])
                && isset($res['data']['amount'])
            ) {
                $partyMemberId = $res['data']['account'];
                $partyProductId = Product::where('code', $productData['party'])->get()->first()->id;
                $userId = UserProduct::where('member_id', $partyMemberId)
                    ->where('product_id', $partyProductId)->first()->user_id ?? null;
                $credit = Credit::where('code', $currency)->get()->first() ?? null;
                $depositParams = [
                    'user_id' => $userId,
                    'orderId' => $res['data']['order_id'],
                    // 'payUrl' => $res['data']['pay_url'],
                    'amount' => $res['data']['amount'],
                    'currency' => $currency,
                    'credit_id' => $credit->id ?? null,
                ];
                $res = self::addThirdPartyDeposit($depositParams, $productData);
                $firstTime = true;
            }

            // Get Order Detail
            $depositData = self::where('code', $referenceNumb)
                ->first();

            if (empty($depositData)) {
                throw new \Exception('Invalid Order.');
            }
        }

        DB::transaction(function () use ($data, $dateTime, $depositData, $referenceNumb, $orderStatus, $amount) {
            DepositDetail::create([
                'deposit_id' => $depositData->id ?? 0,
                'name' => 'FPay-callback',
                'value' => $referenceNumb,
                'reference' => $orderStatus,
                'description' => json_encode($data),
            ]);

            if ($depositData->status != self::$status['pending']) {
                throw new \Exception('Expired Order.');
            }

            // handling for online-bank-telco
            if ($depositData->type == Deposit::$type['online-bank-telco']) {
                $res = self::find($depositData->id)->update([
                    'amount' => $amount ?? $depositData->amount,
                    'converted_amount' => $amount ?? $depositData->converted_amount,
                    'receivable_amount' => $amount ?? $depositData->receivable_amount,
                ]);
            }

            if (! isset($depositData->party)) {
                switch (strtolower($orderStatus)) {
                    case 'completed':
                        $depositStatus = 'approved';
                        break;

                    case 'fail':
                        $depositStatus = 'rejected';
                        break;

                    default:
                        break;
                }

                if (isset($depositStatus)) {
                    $updateParams = [
                        'id' => $depositData->id ?? 0,
                        'status' => $depositStatus,
                        'datetime' => $dateTime,
                    ];
                }

                try {
                    $updateRes = self::updateDeposit($updateParams);
                } catch (\Exception $e) {
                    return false;
                }
            }

            // // region call FMT
            // if ($firstTime && $depositData->party && $orderStatus == 'completed' && $depositData->status == self::$status['pending']) {
            //     // inform FMT
            //     $productId = Product::where('code', 'FMT')->get()->first()->id ?? null;
            //     $fmtMemberId = UserProduct::where('user_id', $userId)
            //     ->where('product_id', $productId)->first()->member_id ?? null;

            //     $jobData = [
            //         'deposit_id' => $depositData->id,
            //         'order_id' => $referenceNumb,
            //         'partyAccount' => $partyMemberId,
            //         'account' => $fmtMemberId,
            //         'amount' => $depositData->receivable_amount,
            //         'company' => $productData['party'],
            //         'product_id' => $productId ?? null,
            //         'party_product_id' => $partyProductId ?? null,
            //         'credit_id' => $credit->id ?? null,
            //         'user_id' => $userId ?? null,
            //         'curl_type' => 'balance-promoid',
            //     ];

            //     $job = new \App\Jobs\ProcessMerchant(json_encode($jobData));
            //     dispatch($job)->onQueue('merchant');
            // }
        });

        return true;
    }

    public static function onepayDepositCallBack($params = [], $productData = null)
    {
        $data = $params ?? [];
        $dateTime = date('Y-m-d H:i:s');

        // Prefix variable
        $referenceNumb = $data['order_id'] ?? null;
        $orderStatus = $data['order_status'] ?? null;
        $token = $data['token'] ?? null;
        $amount = $data['amount'] ?? null;

        // region 3rd party insert deposit record
        $currency = $data['currency'] ?? null;
        $userId = null;
        $credit = null;
        $partyProductId = true;
        $partyMemberId = null;

        if (isset($data) && $data['type'] == 'withdrawal') {
            return true;
        }

        $depositData = self::where('code', $referenceNumb)
            ->first();

        if (empty($depositData)) {
            throw new \Exception('Invalid Order.');
        }

        $firstTime = false;

        // Check Token
        if ((md5(env('ONEPAY_SECRET_KEY') . $referenceNumb) != $token)
            && (md5(env('ONEPAY_SECRET_KEY_EWALLET') . $referenceNumb) != $token)
            && (md5(env('ONEPAY_SECRET_KEY_THAI') . $referenceNumb) != $token)
            && (md5(env('ONEPAY_SECRET_KEY_TELCO') . $referenceNumb) != $token)
        ) {
            throw new \Exception('Invalid Token.');
        }

        if (empty($depositData)) {
            $productData = ThirdPartyTrait::getDataFromCurrency($currency);
            // get data from 3rd party
            switch ($productData['party']) {
                case 'TK8':
                    $res = Traits\OCTK8Trait::postOC(['data' => $data], 'verify_transaction');
                    break;
            }

            if (
                isset($res) && isset($res['status']) && $res['status'] && isset($res['data'])
                && isset($res['data']['account'])
                && isset($res['data']['order_id'])
                // && isset($res['data']['pay_url'])
                && isset($res['data']['amount'])
            ) {
                $partyMemberId = $res['data']['account'];
                $partyProductId = Product::where('code', $productData['party'])->get()->first()->id;
                $userId = UserProduct::where('member_id', $partyMemberId)
                    ->where('product_id', $partyProductId)->first()->user_id ?? null;
                $credit = Credit::where('code', $currency)->get()->first() ?? null;
                $depositParams = [
                    'user_id' => $userId,
                    'orderId' => $res['data']['order_id'],
                    // 'payUrl' => $res['data']['pay_url'],
                    'amount' => $res['data']['amount'],
                    'currency' => $currency,
                    'credit_id' => $credit->id ?? null,
                ];
                $res = self::addThirdPartyDeposit($depositParams, $productData);
                $firstTime = true;
            }

            // Get Order Detail
            $depositData = self::where('code', $referenceNumb)
                ->first();

            if (empty($depositData)) {
                throw new \Exception('Invalid Order.');
            }
        }

        DB::transaction(function () use ($data, $dateTime, $depositData, $referenceNumb, $orderStatus, $amount) {
            DepositDetail::create([
                'deposit_id' => $depositData->id ?? 0,
                'name' => 'FPay-callback',
                'value' => $referenceNumb,
                'reference' => $orderStatus,
                'description' => json_encode($data),
            ]);

            if ($depositData->status != self::$status['pending']) {
                throw new \Exception('Expired Order.');
            }

            // handling for online-bank-telco
            if ($depositData->type == Deposit::$type['online-bank-telco']) {
                $res = self::find($depositData->id)->update([
                    'amount' => $amount ?? $depositData->amount,
                    'converted_amount' => $amount ?? $depositData->converted_amount,
                    'receivable_amount' => $amount ?? $depositData->receivable_amount,
                ]);
            }

            if (! isset($depositData->party)) {
                switch (strtolower($orderStatus)) {
                    case 'completed':
                        $depositStatus = 'approved';
                        break;

                    case 'fail':
                        $depositStatus = 'rejected';
                        break;

                    default:
                        break;
                }

                if (isset($depositStatus)) {
                    $updateParams = [
                        'id' => $depositData->id ?? 0,
                        'status' => $depositStatus,
                        'datetime' => $dateTime,
                    ];
                }

                try {
                    $updateRes = self::updateDeposit($updateParams);
                } catch (\Exception $e) {
                    abort(500, $e);

                    return false;
                }
            }

            // // region call FMT
            // if ($firstTime && $depositData->party && $orderStatus == 'completed' && $depositData->status == self::$status['pending']) {
            //     // inform FMT
            //     $productId = Product::where('code', 'FMT')->get()->first()->id ?? null;
            //     $fmtMemberId = UserProduct::where('user_id', $userId)
            //     ->where('product_id', $productId)->first()->member_id ?? null;

            //     $jobData = [
            //         'deposit_id' => $depositData->id,
            //         'order_id' => $referenceNumb,
            //         'partyAccount' => $partyMemberId,
            //         'account' => $fmtMemberId,
            //         'amount' => $depositData->receivable_amount,
            //         'company' => $productData['party'],
            //         'product_id' => $productId ?? null,
            //         'party_product_id' => $partyProductId ?? null,
            //         'credit_id' => $credit->id ?? null,
            //         'user_id' => $userId ?? null,
            //         'curl_type' => 'balance-promoid',
            //     ];

            //     $job = new \App\Jobs\ProcessMerchant(json_encode($jobData));
            //     dispatch($job)->onQueue('merchant');
            // }
        });

        return true;
    }

    // TODO: WIP
    public static function rapidpayDepositCallback($params = [])
    {
        $data = $params ?? [];
        $dateTime = date('Y-m-d H:i:s');

        // Prefix variable
        $referenceNumb = $data['ID'] ?? null;
        $orderStatus = $data['Status'] ?? null;
        $customerCode = $data['CustomerCode'] ?? null;
        $hash = $data['Hash'] ?? null;
        $amount = $data['Amount'] ?? null;

        $userId = null;
        $credit = null;
        $partyProductId = true;
        $partyMemberId = null;

        $depositData = self::where('code', $customerCode)->first();

        if (empty($depositData)) {
            throw new \Exception('Invalid Order.');
        }

        // TODO: Hash check
        // if (sha1($referenceNumb.$customerCode.$amount.$orderStatus.env('RAPIDPAY_API_KEY')) != $hash) {
        //     throw new \Exception('Invalid Hash.');
        // }

        // TODO:
        if (empty($depositData)) {
            // $productData = ThirdPartyTrait::getDataFromCurrency($currency);

            // if (
            //     isset($res) && isset($res['status']) && $res['status'] && isset($res['data'])
            //     && isset($res['data']['account'])
            //     && isset($res['data']['order_id'])
            //     // && isset($res['data']['pay_url'])
            //     && isset($res['data']['amount'])
            // ) {
            //     $partyMemberId = $res['data']['account'];
            //     $partyProductId = Product::where('code', $productData['party'])->get()->first()->id;
            //     $userId = UserProduct::where('member_id', $partyMemberId)
            //         ->where('product_id', $partyProductId)->first()->user_id ?? null;
            //     $credit = Credit::first() ?? null;
            //     $depositParams = [
            //         'user_id' => $userId,
            //         'orderId' => $res['data']['order_id'],
            //         // 'payUrl' => $res['data']['pay_url'],
            //         'amount' => $res['data']['amount'],
            //         // 'currency' => $currency,
            //         'credit_id' => $credit->id ?? null,
            //     ];
            //     $res = self::addThirdPartyDeposit($depositParams, $productData);
            // }

            // Get Order Detail
            $depositData = self::where('code', $customerCode)->first();

            if (empty($depositData)) {
                throw new \Exception('Invalid Order.');
            }
        }

        DB::transaction(function () use ($data, $dateTime, $depositData, $referenceNumb, $orderStatus) {
            DepositDetail::create([
                'deposit_id' => $depositData->id ?? 0,
                'name' => 'Rapidpay-callback',
                'value' => $orderStatus,
                'reference' => $referenceNumb,
                'description' => json_encode($data),
            ]);

            if (! in_array($depositData->status, [self::$status['pending'], self::$status['rejected']])) {
                throw new \Exception('Expired Order.');
            }

            // handling for online-bank-telco
            // if ($depositData->type == Deposit::$type['online-bank-telco']) {
            //     $res = self::find($depositData->id)->update([
            //         'amount' => $amount ?? $depositData->amount,
            //         'converted_amount' => $amount ?? $depositData->converted_amount,
            //         'receivable_amount' => $amount ?? $depositData->receivable_amount,
            //     ]);
            // }

            if (! isset($depositData->party)) {
                switch (strtolower($orderStatus)) {
                    case 'success':
                        $depositStatus = 'approved';
                        break;

                    case 'fail':
                        $depositStatus = 'rejected';
                        break;

                    default:
                        break;
                }

                if (isset($depositStatus)) {
                    $updateParams = [
                        'id' => $depositData->id ?? 0,
                        'status' => $depositStatus,
                        'datetime' => $dateTime,
                    ];
                }

                try {
                    $updateRes = self::updateDeposit($updateParams);
                } catch (\Exception $e) {
                    abort(500, $e);

                    return false;
                }
            }
        });

        return true;
    }

    public static function wepayDepositCallback($params = [])
    {
        $data = $params ?? [];
        $dateTime = date('Y-m-d H:i:s');

        // Prefix variable
        $referenceNumb = $data['transaction_id'] ?? null;
        $orderStatus = $data['status'] ?? null;
        $customerCode = $data['ref_id'] ?? null;
        $hash = $data['hash'] ?? null;
        $amount = $data['amount'] ?? null;

        $depositData = self::where('code', $customerCode)->first();

        if (empty($depositData)) {
            throw new \Exception('Invalid Order.');
        }

        // TODO: Hash check
        // if (hash('sha256', $referenceNumb . $customerCode . $amount . $orderStatus . env('WEPAY_API_KEY')) != $hash) {
        //     throw new \Exception('Invalid Hash.');
        // }

        // TODO:
        if (empty($depositData)) {
            // Get Order Detail
            $depositData = self::where('code', $customerCode)->first();

            if (empty($depositData)) {
                throw new \Exception('Invalid Order.');
            }
        }

        DB::transaction(function () use ($data, $dateTime, $depositData, $referenceNumb, $orderStatus) {
            DepositDetail::create([
                'deposit_id' => $depositData->id ?? 0,
                'name' => 'wepay-callback',
                'value' => $orderStatus,
                'reference' => $referenceNumb,
                'description' => json_encode($data),
            ]);

            if (! in_array($depositData->status, [self::$status['pending'], self::$status['rejected']])) {
                throw new \Exception('Expired Order.');
            }

            if (! isset($depositData->party)) {
                switch (strtolower($orderStatus)) {
                    case 'success':
                        $depositStatus = 'approved';
                        break;

                    case 'fail':
                        $depositStatus = 'rejected';
                        break;

                    default:
                        break;
                }

                if (isset($depositStatus)) {
                    $updateParams = [
                        'id' => $depositData->id ?? 0,
                        'status' => $depositStatus,
                        'datetime' => $dateTime,
                    ];
                }

                try {
                    $updateRes = self::updateDeposit($updateParams);
                } catch (\Exception $e) {
                    abort(500, $e);

                    return false;
                }
            }
        });

        return true;
    }

    // combine with tp_webhook
    public static function apiList(array $params = [])
    {
        $username = $params['username'] ?? null;
        $memberID = $params['member_id'] ?? null;
        $phoneNo = $params['phone_no'] ?? null;
        $transactionID = $params['transaction_id'] ?? null;
        $referenceNo = $params['reference_no'] ?? null;
        $type = $params['type'] ?? null;
        $fromDate = $params['from_date'] ?? null;
        $toDate = $params['to_date'] ?? null;
        $updatedFromDate = $params['updated_from_date'] ?? null;
        $updatedToDate = $params['updated_to_date'] ?? null;
        $status = $params['status'] ?? null;
        $country_id = $params['country'] ?? null;
        $creditType = $params['credit_type'] ?? null;
        $txId = $params['tx_id'] ?? null;
        $seeAll = $params['see_all'] ?? null;

        $statusFilter = self::$status[$status] ?? null;
        if (is_array($type)) {
            $typeFilter = $type;
        } else {
            $typeFilter = self::$type[$type] ?? null;
        }

        $sortableColumns = [];
        if (isset($params['export']) && ($params['export'] == 1)) {
            $params['model'] = get_class() ?? null;
            $params['function'] = __FUNCTION__;
            ExportReport::exportReport($params);
        }

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        $items = self::query()
            ->with(['tpWebHook'])
            // ->with(['tpWebHook', 'userDetail', 'adminDetail','depositDetail'])
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('created_at'), '>=', date('Y-m-d 00:00:00', strtotime($fromDate)));

                return $q->where(DB::raw('created_at'), '<=', date('Y-m-d 23:59:59', strtotime($toDate)));
            })
            ->when((isset($updatedFromDate) && isset($updatedToDate)), function ($q) use ($updatedFromDate, $updatedToDate) {
                $q->where('approved_at', '>=', date('Y-m-d 00:00:00', strtotime($updatedFromDate)));

                return $q->where('approved_at', '<=', date('Y-m-d 23:59:59', strtotime($updatedToDate)));
            })
            ->when(isset($creditType), function ($q) use ($creditType) {
                return $q->where('credit_type', $creditType);
            })
            ->when(isset($username), function ($q) use ($username) {
                return $q->whereRelation('userDetail', 'username', 'like', '%' . $username . '%');
            })
            ->when(isset($memberID), function ($q) use ($memberID) {
                return $q->whereRelation('userDetail', 'member_id', 'like', '%' . $memberID . '%');
            })
            ->when(isset($phoneNo), function ($q) use ($phoneNo) {
                return $q->whereRelation('userDetail', function ($q) use ($phoneNo) {
                    $phoneNo = preg_replace('/[^0-9]/', '', $phoneNo);
                    $q->where('phone_no', 'LIKE', "%$phoneNo%");
                });
            })
            ->when(isset($transactionID), function ($q) use ($transactionID) {
                return $q->where('code', 'like', '%' . $transactionID . '%');
            })
            ->when(isset($referenceNo), function ($q) use ($referenceNo) {
                $q->whereRelation('depositDetail', 'name', 'reference_no');

                return $q->whereRelation('depositDetail', 'value', $referenceNo);
            })
            ->when(isset($type), function ($q) use ($typeFilter) {
                if (is_array($typeFilter)) {
                    return $q->whereIn('type', $typeFilter);
                } else {
                    return $q->where('type', $typeFilter);
                }
            })
            ->when(isset($params['user_id']), function ($q) use ($params) {
                return $q->where('user_id', $params['user_id']);
            })
            ->when(isset($status), function ($q) use ($statusFilter) {
                return $q->where('status', $statusFilter);
            })
            ->when(isset($txId), function ($q) use ($txId) {
                return $q->whereHas('transactionLogs', function ($query) use ($txId) {
                    return $query->where('tx_id', 'like', '%' . $txId . '%');
                });
            })
            ->when(isset($country_id), function ($query) use ($country_id) {
                $query->whereRelation('userDetail', 'country_id', $country_id);
            })
            ->whereNotNull('party');

        $items = $items->orderBy($order_by, $order_sort)
            ->when((! isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $productCode = Product::select('code')->get();

        $mapFunc = function ($q) use ($productCode) {
            $status = array_search($q->status, self::$status);

            if (MODULE == 'admin') {
                $adminRes = [
                    'id' => $q->id ?? null,
                    'username' => $q->userDetail->username ?? null,
                    'transaction_id' => $q->code ?? null,
                    'status' => $status,
                    'status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                ];

                foreach ($productCode as $value) {
                    $adminRes[strtolower($value->code)] = [];
                }

                if (! empty($q->tpWebHook)) {
                    foreach ($q->tpWebHook as $hook) {
                        $data = [
                            'type' => (array_search($hook->type, TpWebHook::$type) ?? null),
                            'req_data' => $hook->req_data,
                            'res_data' => $hook->res_data,
                            'created_at' => DateTrait::dateFormat($hook->created_at),
                        ];
                        $adminRes[$hook->platform][] = $data;
                    }
                }
            }

            $res = [
                'id' => $q->id,
            ];

            return (object) array_merge($res, ($adminRes ?? []));
        };

        if (($seeAll == 1)) {
            return ['list' => $items->get()->map($mapFunc)->toArray()];
        } else {
            $items->getCollection()->transform($mapFunc);

            if (MODULE == 'admin') {
                return (new ItemsCollection($items))->toArray();
            }

            return (new ItemsCollection($items))->toArray();
        }
    }

    public static function getDepositSumByUserId($userId)
    {
        $sum = self::where('user_id', $userId)
            ->where('status', 1)
            ->sum('receivable_amount');

        return $sum;
    }

    public static function getLatestDepositTimestampByUserId($userId)
    {
        $timestamp = self::where('user_id', $userId)
            ->where('status', 1)
            ->orderBy('created_at', 'DESC')
            ->first()
            ?->created_at;

        return $timestamp ?? null;
    }
}
