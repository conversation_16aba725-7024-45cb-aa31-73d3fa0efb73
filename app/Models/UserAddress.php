<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use App\Traits\DateTrait;
use DateTime;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Auth;

class UserAddress extends Model
{
    use DateTrait;

    protected $table = 'user_address';

    protected $hidden = [];

    protected $fillable = [
        'user_id',
        'label',
        'name',
        'phone_no',
        'country_id',
        'state_id',
        'zone_id',
        'address_1',
        'address_2',
        'city',
        'post_code',
        'is_default',
        'status',
        'created_at',
        'deleted_at',
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function country()
    {
        return $this->hasOne(Country::class, 'id', 'country_id');
    }

    public function state()
    {
        return $this->hasOne(State::class, 'id', 'state_id');
    }

    public function zone()
    {
        return $this->hasOne(Zone::class, 'id', 'zone_id');
    }

    public static function get(array $params = [], $isInventory = false)
    {
        $uid = $params['uid'] ?? null;
        $username = $params['username'] ?? null;
        $receiverName = $params['receiver_name'] ?? null;
        $receiverContact = $params['receiver_contact'] ?? null;
        $countryID = $params['country_id'] ?? null;
        $stateID = $params['state_id'] ?? null;
        $isListing = $params['isListing'] ?? null;

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');
        
        $address = self::query()
            ->select(['id', 'user_id', 'label', 'name', 'phone_no', 'country_id', 'state_id', 'zone_id', 'address_1', 'address_2', 'city', 'post_code', 'is_default','created_at'])
            ->with([
                'country' => function ($query) {
                    $query->selectRaw('id, name');
                },
                'state' => function ($query) {
                    $query->selectRaw('id, name');
                },
                'zone' => function ($query) {
                    $query->selectRaw('id, name');
                },
            ])
            ->when((MODULE == 'admin'),function ($query){
                $query->with([
                    'user' => function ($query) {
                        $query->selectRaw('id, username');
                    },
                ]);
            })
            ->when(isset($uid), function ($query) use ($uid) {
                $query->where('user_id', $uid);
            })
            ->when(isset($username), function ($query) use ($username) {
                $query->whereRelation('user','username', 'LIKE', "%".$username."%");
            })
            ->when(isset($receiverName), function ($query) use ($receiverName) {
                $query->where('name',$receiverName);
            })
            ->when(isset($receiverContact), function ($query) use ($receiverContact) {
                $query->where('phone_no',$receiverContact);
            })
            ->when(isset($countryID), function ($query) use ($countryID) {
                $query->where('country_id',$countryID);
            })
            ->when(isset($stateID), function ($query) use ($stateID) {
                $query->where('state_id',$stateID);
            })
            ->where('status',self::$status['active'])
            ->whereNull('deleted_at')
            ->orderBy('is_default','DESC');

        $list = clone $address;

        if((isset($isListing) && $isListing == 1) && MODULE == 'admin'){
            $list = $list->paginate($limit);

            $list->getCollection()->transform(function($q){
                $res = [
                    'created_at' => self::dateFormat($q->created_at),
                    'username' => $q->user->username,
                    'address_label' => $q->label,
                    'receiver_name' => $q->name,
                    'receiver_contact' => $q->phone_no,
                    'address_1' => $q->address_1,
                    'address_2' => $q->address_2,
                    'country' => (Lang::has('lang.'.$q->country->name) ? Lang::get('lang.'.$q->country->name) : $q->country->name),
                    'state_id' => $q->state_id,
                    'state' => (Lang::has('lang.'.$q->state->name) ? Lang::get('lang.'.$q->state->name) : $q->state->name),
                    'zone_id' => $q->zone_id,
                    'zone' => (Lang::has('lang.'.$q->zone->name) ? Lang::get('lang.'.$q->zone->name) : $q->zone->name),
                    'city' => $q->city,
                    'post_code' => $q->post_code,
                ];
                return (object) $res;
            })->toArray();
            return (new ItemsCollection($list));

        }else{
            $address = $address->get()
                ->map(function($q) use ($isInventory) {

                    $address = $q->address_1.', '.$q->address_2.', '.$q->post_code.', '.$q->city.', '.(Lang::has('lang.'.$q->state->name) ? Lang::get('lang.'.$q->state->name) : $q->state->name).', '.(Lang::has('lang.'.$q->zone->name) ? Lang::get('lang.'.$q->zone->name) : $q->zone->name).', '.(Lang::has('lang.'.$q->country->name) ? Lang::get('lang.'.$q->country->name) : $q->country->name);

                    if ($isInventory == true) {
                        return [
                            'id' => $q->id,
                            'label' => $q->label,
                            'name' => $q->name,
                            'phone_no' => $q->phone_no,
                            'address_1' => $q->address_1,
                            'address_2' => $q->address_2,
                            'country_id' => $q->country_id,
                            'country' => (Lang::has('lang.'.$q->country->name) ? Lang::get('lang.'.$q->country->name) : $q->country->name),
                            'state_id' => $q->state_id,
                            'state' => (Lang::has('lang.'.$q->state->name) ? Lang::get('lang.'.$q->state->name) : $q->state->name),
                            'zone_id' => $q->zone_id,
                            'zone' => (Lang::has('lang.'.$q->zone->name) ? Lang::get('lang.'.$q->zone->name) : $q->zone->name),
                            'city' => $q->city,
                            'post_code' => $q->post_code,
                        ];
                    }

                    if(MODULE == 'admin'){
                        return [
                            'created_at' => self::dateFormat($q->created_at),
                            'username' => $q->user->username,
                            'address_label' => $q->label,
                            'receiver_name' => $q->name,
                            'receiver_contact' => $q->phone_no,
                            'address_1' => $q->address_1,
                            'address_2' => $q->address_2,
                            'country' => (Lang::has('lang.'.$q->country->name) ? Lang::get('lang.'.$q->country->name) : $q->country->name),
                            'state_id' => $q->state_id,
                            'state' => (Lang::has('lang.'.$q->state->name) ? Lang::get('lang.'.$q->state->name) : $q->state->name),
                            'zone_id' => $q->zone_id,
                            'zone' => (Lang::has('lang.'.$q->zone->name) ? Lang::get('lang.'.$q->zone->name) : $q->zone->name),
                            'city' => $q->city,
                            'post_code' => $q->post_code,
                        ];
                    }

                    return [
                        'id' => $q->id,
                        'user_id' => $q->user_id,
                        'label' => $q->label,
                        'name' => $q->name,
                        'phone_no' => $q->phone_no,
                        'address' => $address,
                        'is_default' => $q->is_default,
                    ];
                });

            return $address;
        }
    }

    public static function addAddress(array $params = [])
    {
        $stateRes = State::find($params['state_id']);
        $countryID = $stateRes->country_id ?? null;
        $params['zone_id'] = $stateRes->zone_id ?? null;

        if (($countryID != $params['country_id']) || (!$params['zone_id'])) {
            return false;
        }

        if ((MODULE == 'admin') && (!$params['user_id'])) {
            return false;
        }

        if (MODULE == 'user') {
            $params['user_id'] = Auth::user()->id ?? null;

            if (!$params['user_id']) {
                return false;
            }
        }

        $addressCount = self::where('user_id',$params['user_id'])->where('status',self::$status['active'])->whereNull('deleted_at');
        $isDefault = clone $addressCount;
        $addressCount = $addressCount->get()->count() ?? 0;

        if($addressCount + 1 > 10) abort(400, json_encode("Maximum address allowed is 10"));

        if($params['is_default'] == 1){
            $isDefault = $isDefault->update(['is_default' => 0]);
        }

        $insertData = [
            'user_id' => $params['user_id'],
            'label' => $params['label'],
            'name' => $params['name'],
            'phone_no' => $params['phone_no'],
            'country_id' => $params['country_id'],
            'state_id' => $params['state_id'],
            'zone_id' => $params['zone_id'],
            'address_1' => $params['address_1'],
            'address_2' => $params['address_2'],
            'city' => $params['city'],
            'post_code' => $params['post_code'],
            'is_default' => $params['is_default'],
            'status' => self::$status['active'],
        ];

        $insertAddress = self::create($insertData);

        if (!$insertAddress) {
            return false;
        }

        return $insertAddress;
    }

    public static function getDetails(array $params = [])
    {
        $filter = isset($params['get_default']) ? ['user_id' => $params['user_id'], 'is_default' => 1, 'status' => self::$status['active']] : ['id' => $params['id']];

        $addressData = self::with(['country', 'state', 'zone'])->where($filter)->first();
        if (empty($addressData)) {
            throw new \Exception('Invalid address');
        }

        if(MODULE == 'user'){
            if($addressData->user_id != Auth::user()->id )  throw new \Exception('Invalid access');
        }

        $userCurrency = User::find($addressData->user_id) ?? null;
        $country_name = $addressData->country->name ?? null;
        $state_name = $addressData->state->name ?? null;
        $zone_name = $addressData->zone->name ?? null;
        $data = [
            'id' => $addressData->id,
            'user_id' => $addressData->user_id,
            'label' => $addressData->label,
            'name' => $addressData->name,
            'phone_no' => $addressData->phone_no,
            'country_id' => $addressData->country_id,
            'country' => isset($country_name) ? Lang::get('lang.' . $country_name) : $country_name,
            'state_id' => $addressData->state_id,
            'state' => isset($state_name) ? Lang::get('lang.' . $state_name) : $state_name,
            'zone_id' => $addressData->zone_id,
            'zone' => isset($zone_name) ? Lang::get('lang.' . $zone_name) : $zone_name,
            'address_1' => $addressData->address_1,
            'address_2' => $addressData->address_2,
            'city' => $addressData->city,
            'post_code' => $addressData->post_code,
            'is_default' => $addressData->is_default,
            'status' => $addressData->status,
            'currency_id' => isset($userCurrency) ? $userCurrency->currency_id : null,
        ];

        return $data;
    }

    public static function updateDeliveryAddress(array $params = [])
    {
        $stateRes = State::find($params['state_id']);
        $countryID = $stateRes->country_id ?? null;
        $params['zone_id'] = $stateRes->zone_id ?? null;

        if (($countryID != $params['country_id']) || (!$params['zone_id'])) {
            return false;
        }

        if ((MODULE == 'admin') && (!$params['user_id'])) {
            return false;
        }

        if (MODULE == 'user') {
            $params['user_id'] = Auth::user()->id ?? null;

            if (!$params['user_id']) {
                return false;
            }
        }

        $address = self::find($params['id']);
        if (empty($address)) {
            throw new \Exception('Address does not exist!');
        }

        DB::transaction(function () use ($params, $address) {
            $insertData = [
                'user_id' => $params['user_id'],
                'label' => $params['label'],
                'name' => $params['name'],
                'phone_no' => $params['phone_no'],
                'country_id' => $params['country_id'],
                'state_id' => $params['state_id'],
                'zone_id' => $params['zone_id'],
                'address_1' => $params['address_1'],
                'address_2' => $params['address_2'],
                'city' => $params['city'],
                'post_code' => $params['post_code'],
                'is_default' => $params['is_default'],
                'status' => UserAddress::$status['active'],
            ];

            $updateData = [
                'status' => UserAddress::$status['inactive'],
            ];

            if(isset($params['status']) && ($params['status'] == UserAddress::$status['inactive'])) {
                $address->update($updateData);
            } else {
                if($params['is_default'] == 1){
                    UserAddress::where('user_id',$params['user_id'])->where('status',UserAddress::$status['active'])->whereNull('deleted_at')->update(['is_default' => 0]);
                }

                $address->update($updateData);
                UserAddress::create($insertData);
            }
        });

        if ($address->wasChanged()) {
            return 'Successfully updated address!';
        }

        return 'No changes detected';
    }
}