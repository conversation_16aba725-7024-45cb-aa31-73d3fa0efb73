<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class AccountBalance extends Model
{

    protected $table = 'account_balance';

    protected $hidden = [];

    protected $fillable = [
        'user_id',
        'credit_id',
        'balance',
        'created_at',
        'updated_at',
    ];

    public function getCreditData()
    {
        return $this->belongsTo(Credit::class, "credit_id", "id");
    }

    public static function updateBalance($userId, $amount)
    {
        AccountBalance::updateOrCreate([
            'user_id' => $userId,
            'credit_id' => 1000, // WARNING: Hardcode: credit_id
        ], [
            'balance' => $amount
        ]);
    }

    public static function deductBalance($userId, $amount)
    {
        $balance = AccountBalance::where('user_id', $userId)->where('credit_id', 1000)->first();
        if (empty($balance)) {
            AccountBalance::updateOrCreate([
                'user_id' => $userId,
                'credit_id' => 1000, // WARNING: Hardcode: credit_id
            ], [
                'balance' => $amount
            ]);
        } else {
            $balance->balance = $balance->balance - $amount;
            $balance->save();
        }
    }

    public static function addBalance($userId, $amount)
    {
        $balance = AccountBalance::where('user_id', $userId)->where('credit_id', 1000)->first();
        if (empty($balance)) {
            AccountBalance::updateOrCreate([
                'user_id' => $userId,
                'credit_id' => 1000, // WARNING: Hardcode: credit_id
            ], [
                'balance' => $amount
            ]);
        } else {
            $balance->balance = $balance->balance + $amount;
            $balance->save();
        }
    }
}
