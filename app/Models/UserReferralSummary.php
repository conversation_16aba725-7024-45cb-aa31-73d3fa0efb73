<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserReferralSummary extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'downline_user_id',
        'total_turnover',
        'batch',
    ];

    public static function getTotalDownlineTurnoverByUserId($userId)
    {
        $total = self::where('user_id', $userId)
            ->latest('batch')
            ->groupBy('batch')
            ->sum('total_turnover');

        return $total;
    }

    public static function getTotalTurnoverByUserIdAndBatch($userId, $batch)
    {
        $total = self::where('user_id', $userId)
            ->where('batch', $batch)
            ->sum('total_turnover');

        return $total;
    }

    public static function getLatestBatchByUserIdAndDownlineUserId($userId, $downlineUserId)
    {
        $batch = self::where('user_id', $userId)
            ->where('downline_user_id', $downlineUserId)
            ->latest('batch')
            ->first()
            ?->batch ?? 0;

        return $batch;
    }

    private static function getLatestTotalDownlineTurnoverByUserId($userId)
    {
        $sum = self::whereRaw('(user_id, downline_user_id, batch) IN
    (SELECT user_id, downline_user_id, MAX(batch) AS batch
    FROM user_referral_summaries
    GROUP BY user_id, downline_user_id)')
            ->where('user_id', $userId)
            ->sum('total_turnover');

        return $sum;
    }

    private static function getPreviousTotalDownlineTurnoverByUserId($userId)
    {
        $sum = self::whereRaw('(user_id, downline_user_id, batch) IN
    (SELECT user_id, downline_user_id, MAX(batch) -1 AS batch
    FROM user_referral_summaries
    GROUP BY user_id, downline_user_id)')
            ->where('user_id', $userId)
            ->sum('total_turnover');

        return $sum;
    }

    public static function getLatestTotalDownlineTurnoverGroupByUserId()
    {
        $sum = self::whereRaw('(user_id, downline_user_id, batch) IN
    (SELECT user_id, downline_user_id, MAX(batch) AS batch
    FROM user_referral_summaries
    GROUP BY user_id, downline_user_id)')
            ->select('user_id')
            ->selectRaw('SUM(total_turnover) AS total_downline_turnover')
            ->groupBy('user_id')
            ->get();

        return $sum;
    }

    // INFO: For Jimmy <3
    public static function getLatestTotalTurnover()
    {
        $sum = self::whereRaw('(user_id, downline_user_id, batch) IN
    (SELECT user_id, downline_user_id, MAX(batch) AS batch
    FROM user_referral_summaries
    GROUP BY user_id, downline_user_id)')
      // INFO: Add filter & sorting accordingly
            ->orderBy('user_id')
            ->orderBy('downline_user_id')
            ->get();

        return $sum;
    }

    public static function getClaimableAmountByUserId($userId)
    {
        $latestTurnover = self::getLatestTotalDownlineTurnoverByUserId($userId);
        $previousTurnover = self::getPreviousTotalDownlineTurnoverByUserId($userId);

        return $latestTurnover - $previousTurnover;
    }
}
