<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Http\Resources\ItemsCollection;
use Illuminate\Support\Facades\Auth;

class AnnouncementBarSetting extends Model
{
    const UPDATED_AT = null;

    const CREATED_AT = null;
    
    protected $table = 'announcement_bar_setting';

    protected $hidden = [
    ];

    protected $fillable = [
        'bar_id',
        'name',
        'value',
        'deleted_at',
    ];
}
