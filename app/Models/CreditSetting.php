<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Auth;
use App\Http\Resources\ItemsCollection;
use App\Traits;

class CreditSetting extends Model
{
    const UPDATED_AT = null;

    const CREATED_AT = null;

    protected $table = 'credit_setting';

    protected $hidden = [
        'deleted_at',
    ];

    protected $fillable = [
        'credit_id',
        'name',
        'value',
        'admin',
        'member',
        'type',
        'reference',
        'description',
        'deleted_at'
    ];

    public static $status = [
        "inactive" => 0,
        "active" => 1,
    ];

    public function credit()
    {
        return $this->belongsTo(Credit::class, "credit_id", "id");
    }

    public function codeCredit()
    {
        return $this->belongsTo(Credit::class, "value", "name");
    }

    public function convertToCredit()
    {
        return $this->belongsTo(Credit::class, "value", "name");
    }

    public static function getDepositMethodSetting($params = [])
    {
        $settingDate = isset($params['setting_date']) ? date("Y-m-d 00:00:00", strtotime($params['setting_date'])) : date("Y-m-d 00:00:00");
        $data = [];
        $depositMethodRaw = SystemSettingsAdmin::where('type', 'deposit')->where('active_at', '<=', $settingDate)->groupBy('name')->selectRaw('MAX(id) AS id')->get()->pluck('id')->toArray();
        $depositMethod = SystemSettingsAdmin::whereIn('id', $depositMethodRaw)->get()->keyBy('name');

        foreach ($depositMethod as $method) {
            switch ($method->name) {
                case 'manual-bank':
                    $bankDetails = [];
                    if (isset($method->reference) && !empty($method->reference)) {
                        // temp
                        $bankDetails = json_decode($method->reference, true);
                    } else {
                        $bankInDetailes = config('bank');
                        $bankDetails = $bankInDetailes[env('APP_ENV')];
                    }

                    $data[$method->name]['name'] = $method->name;
                    $data[$method->name]['display'] = Lang::has("lang." . $method->name) ? Lang::get("lang." . $method->name) : $method->name;
                    $data[$method->name]['status'] = array_search($method->value, SystemSettingsAdmin::$depositMethodStatus);
                    $data[$method->name]['status_value'] = $method->value;
                    $data[$method->name]['status_display'] = Lang::has("lang." . array_search($method->value, SystemSettingsAdmin::$depositMethodStatus)) ? Lang::get("lang." . array_search($method->value, SystemSettingsAdmin::$depositMethodStatus)) : array_search($method->value, SystemSettingsAdmin::$depositMethodStatus);
                    $data[$method->name]['bank_detail'] = $bankDetails;
                    break;

                case 'online-bank':
                case 'online-bank-onepay':
                case 'online-bank-thai':
                case 'online-bank-telco':
                case 'ewallet':
                case 'crypto':
                    $data[$method->name]['name'] = $method->name;
                    $data[$method->name]['display'] = Lang::has("lang." . $method->name) ? Lang::get("lang." . $method->name) : $method->name;
                    $data[$method->name]['status'] = array_search($method->value, SystemSettingsAdmin::$depositMethodStatus);
                    $data[$method->name]['status_value'] = $method->value;
                    $data[$method->name]['status_display'] = Lang::has("lang." . array_search($method->value, SystemSettingsAdmin::$depositMethodStatus)) ? Lang::get("lang." . array_search($method->value, SystemSettingsAdmin::$depositMethodStatus)) : array_search($method->value, SystemSettingsAdmin::$depositMethodStatus);
                    break;

                default:
                    break;
            }
        }

        return $data;
    }

    public static function setDepositMethodSetting($params = [])
    {
        $activeDate = isset($params['active_date']) ? date("Y-m-d 00:00:00", strtotime($params['active_date'])) : date("Y-m-d 00:00:00");
        $bankDetails = $params['bank_details'] ?? [];

        unset($params['active_date'], $params['bank_details']);
        return DB::transaction(function () use ($activeDate, $params, $bankDetails) {
            $dateTime = date("Y-m-d H:i:s");
            foreach ($params as $depositMethod => $depositMethodStatus) {
                SystemSettingsAdmin::create([
                    "name" => $depositMethod,
                    "type" => 'deposit',
                    "value" => $depositMethodStatus,
                    "reference" => $depositMethod == 'manual-bank' ? json_encode($bankDetails) : null,
                    "ref_id" => strtotime($dateTime),
                    "status" => 'active',
                    "active_at" => $activeDate,
                    "created_at" => $dateTime,
                    "creator_id" => Auth::user()->id ?? 0,
                ]);
            }
        });
    }

    public static function depositMethodHistory(array $params = [])
    {
        $fromDate = $params['from_date'] ?? Null;
        $toDate = $params['to_date'] ?? Null;
        $seeAll = $params['see_all'] ?? Null;

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'activated_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'activated_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        $items = SystemSettingsAdmin::query()
            ->with('admin')
            ->selectRaw('system_settings_admin.ref_id as ref_id, ANY_VALUE(a.value) as manual_bank_status, ANY_VALUE(a.reference) as bank_details, ANY_VALUE(b.value) as online_bank_status, ANY_VALUE(c.value) as ewallet_status, ANY_VALUE(d.value) as telco_status, ANY_VALUE(e.value) as online_bank_onepay_status, ANY_VALUE(system_settings_admin.active_at) as active_at, ANY_VALUE(system_settings_admin.created_at) as created_at, ANY_VALUE(system_settings_admin.creator_id) as creator_id')
            ->leftJoin('system_settings_admin as a', function ($q) {
                $q->on('system_settings_admin.ref_id', '=', 'a.ref_id');
                $q->where('a.name', '=', 'manual-bank');
            })
            ->leftJoin('system_settings_admin as b', function ($q) {
                $q->on('system_settings_admin.ref_id', '=', 'b.ref_id');
                $q->where('b.name', '=', 'online-bank');
            })
            ->leftJoin('system_settings_admin as c', function ($q) {
                $q->on('system_settings_admin.ref_id', '=', 'c.ref_id');
                $q->where('c.name', '=', 'ewallet');
            })
            ->leftJoin('system_settings_admin as d', function ($q) {
                $q->on('system_settings_admin.ref_id', '=', 'd.ref_id');
                $q->where('d.name', '=', 'online-bank-telco');
            })
            ->leftJoin('system_settings_admin as e', function ($q) {
                $q->on('system_settings_admin.ref_id', '=', 'e.ref_id');
                $q->where('e.name', '=', 'online-bank-onepay');
            })
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where('system_settings_admin.active_at', ">=", date("Y-m-d 00:00:00", strtotime($fromDate)));
                $q->where('system_settings_admin.active_at', "<=", date("Y-m-d 23:59:59", strtotime($toDate)));
            })
            ->groupBy('system_settings_admin.ref_id')
            ->orderBy('active_at', 'DESC')
            ->orderBy('ref_id', 'DESC')
            ->when((!isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $mapFunc = function ($q) {
            $bankDetails = isset($q->bank_details) && !empty($q->bank_details) ? json_decode($q->bank_details, true) : null;

            $res = [
                'ref_id' => $q->ref_id,
                "manual_bank_status" => array_search($q->manual_bank_status, SystemSettingsAdmin::$depositMethodStatus),
                "manual_bank_status_display" => Lang::has("lang." . array_search($q->manual_bank_status, SystemSettingsAdmin::$depositMethodStatus)) ? Lang::get("lang." . array_search($q->manual_bank_status, SystemSettingsAdmin::$depositMethodStatus)) : array_search($q->manual_bank_status, SystemSettingsAdmin::$depositMethodStatus),
                "bank_details" => isset($bankDetails) && !empty($bankDetails) ? $bankDetails : null,
                "online_bank_status" => array_search($q->online_bank_status, SystemSettingsAdmin::$depositMethodStatus),
                "online_bank_status_display" => Lang::has("lang." . array_search($q->online_bank_status, SystemSettingsAdmin::$depositMethodStatus)) ? Lang::get("lang." . array_search($q->online_bank_status, SystemSettingsAdmin::$depositMethodStatus)) : array_search($q->online_bank_status, SystemSettingsAdmin::$depositMethodStatus),
                "online_bank_onepay_status" => array_search($q->online_bank_onepay_status, SystemSettingsAdmin::$depositMethodStatus),
                "online_bank_onepay_status_display" => Lang::has("lang." . array_search($q->online_bank_onepay_status, SystemSettingsAdmin::$depositMethodStatus)) ? Lang::get("lang." . array_search($q->online_bank_onepay_status, SystemSettingsAdmin::$depositMethodStatus)) : array_search($q->online_bank_onepay_status, SystemSettingsAdmin::$depositMethodStatus),
                "ewallet_status" => array_search($q->ewallet_status, SystemSettingsAdmin::$depositMethodStatus),
                "ewallet_status_display" => Lang::has("lang." . array_search($q->ewallet_status, SystemSettingsAdmin::$depositMethodStatus)) ? Lang::get("lang." . array_search($q->ewallet_status, SystemSettingsAdmin::$depositMethodStatus)) : array_search($q->ewallet_status, SystemSettingsAdmin::$depositMethodStatus),
                "telco_status" => array_search($q->telco_status, SystemSettingsAdmin::$depositMethodStatus),
                "telco_status_display" => Lang::has("lang." . array_search($q->telco_status, SystemSettingsAdmin::$depositMethodStatus)) ? Lang::get("lang." . array_search($q->telco_status, SystemSettingsAdmin::$depositMethodStatus)) : array_search($q->telco_status, SystemSettingsAdmin::$depositMethodStatus),
                "active_at" => Traits\DateTrait::dateFormat($q->active_at, false),
                "created_at" => Traits\DateTrait::dateFormat($q->created_at),
                'created_by' => optional($q->admin)->username ?? "System",
            ];

            return (object) $res;
        };

        if ($seeAll == 1) {
            $data = $items->get()->map($mapFunc);
        } else {
            $items->getCollection()->transform($mapFunc);
            $data =  (new ItemsCollection($items));
        }

        return $data;
    }

    public static function checkAndUpdateIsConvertible()
    {

        $activeCreditID = [];
        $inActiveCreditID = [];

        $credit = Credit::with([
            'creditSetting' => function ($q) {
                $q->whereIn('name', ['convert-to']);
            },
            'creditSetting.codeCredit'
        ])
            ->where("code", "MYR")
            ->get()
            ->map(function ($q) use (&$activeCreditID, &$inActiveCreditID) {
                foreach ($q->creditSetting as $key => $value) {
                    if ($value->member == 1) {
                        // active
                        $activeCreditID[] = $value->codeCredit->id;
                    } else {
                        // inactive
                        $inActiveCreditID[] = $value->codeCredit->id;
                    }
                }
                return $q;
            })->first();

        if (count($activeCreditID) == 0) {
            // update myr is convertible to inactive
            self::where('credit_id', $credit->id)
                ->where('name', 'is-convertible')
                ->update(['member' => '0']);
        } else {
            // update myr is convertible to active
            self::where('credit_id', $credit->id)
                ->where('name', 'is-convertible')
                ->update(['member' => '1']);
        }
        if (count($activeCreditID) > 0) {
            self::whereIn('credit_id', $activeCreditID)
                ->where('name', 'is-convertible')
                ->update(['member' => '1']);
        }
        if (count($inActiveCreditID) > 0) {
            self::whereIn('credit_id', $inActiveCreditID)
                ->where('name', 'is-convertible')
                ->update(['member' => '0']);
        }
    }
}
