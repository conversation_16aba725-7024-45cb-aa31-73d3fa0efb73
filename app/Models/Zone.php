<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;

class Zone extends Model
{
    protected $table = 'zone';

    protected $hidden = [
    ];

    protected $fillable = [
        'name',
        'country_id',
        'status',
        'priority',
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
    ];

    public function country(){
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    public function shippingFee(){
        return $this->hasMany(ShippingFee::class, 'zone_id', 'id');
    }
}
