<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Http\Resources\ItemsCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\SoftDeletes;

class BannerSetting extends Model
{
    use SoftDeletes;

    const UPDATED_AT = null;

    const CREATED_AT = null;
    
    protected $table = 'banner_setting';

    protected $hidden = [
    ];

    protected $fillable = [
        'banner_id',
        'name',
        'value',
        'deleted_at',
    ];
}
