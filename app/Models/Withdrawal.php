<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use App\Services\PaymentGateway\FPayPayout;
use App\Services\PaymentGateway\OnePay;
use App\Services\PaymentGateway\RapidPay;
use App\Services\PaymentGateway\WePay;
use App\Traits;
use App\Traits\DateTrait;
use App\Traits\DecimalTrait;
use App\Traits\GenerateNumberTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Lang;

class Withdrawal extends Model
{
    use DecimalTrait;
    use GenerateNumberTrait;

    protected $table = 'withdrawal';

    protected $hidden = [];

    protected $fillable = [
        'serial_number',
        'user_id',
        'credit_type',
        'amount',
        'charges',
        'receivable_amount',
        'currency_id',
        'currency_rate',
        'converted_amount',
        'status',
        'remark',
        'user_bank_id',
        'user_withdrawal_address_id',
        'withdrawal_type',
        'belong_id',
        'batch_id',
        'approved_at',
        'approved_by',
        'updater_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $status = [
        'waiting-approval' => 0,
        'approved' => 1,
        'pending' => 2,
        'rejected' => 3,
        'cancel' => 9,
    ];

    public static $withdrawalType = [
        'usdt' => 1,
        'bank' => 2,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function admin()
    {
        return $this->belongsTo(Admin::class, 'approved_by', 'id');
    }

    public function updateUser()
    {
        return $this->belongsTo(User::class, 'updater_id', 'id');
    }

    public function updateAdmin()
    {
        return $this->belongsTo(Admin::class, 'updater_id', 'id');
    }

    public function withdrawalDetail()
    {
        return $this->hasMany(WithdrawalDetail::class);
    }

    public function userBank()
    {
        return $this->belongsTo(UserBank::class, 'user_bank_id', 'id');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_id', 'id');
    }

    public function credit()
    {
        return $this->belongsTo(Credit::class, 'credit_type', 'type');
    }

    /* -------------------------------------------------------------------------- */
    /*                                   Scopes */
    /* -------------------------------------------------------------------------- */

    public function scopeApproved($query)
    {
        return $query->where('status', self::$status['approved']);
    }

    public static function add($data = [])
    {
        $returnData = DB::transaction(function () use ($data) {
            $internalID = User::select('id')->where('username', 'withdrawal')->where('user_type', User::$userType['internal-account'])->first()->id ?? null;
            if (empty($internalID)) {
                abort(400, 'Invalid Internal Account.');
            }

            $dateTime = date('Y-m-d H:i:s');
            $userID = $data['uid'] ?? 0;
            $dateTime = $data['datetime'] ?? date('Y-m-d H:i:s');
            $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;
            $refNo = GenerateNumberTrait::generateReferenceNo(self::query(), 'serial_number', null, 'bank-withdrawal');
            $amount = $data['amount'];
            $creditId = $data['credit_id'];
            $walletData = $data['wallet_data'];

            $bankDetaill = UserBank::with(['bank'])->where('id', $data['channel_id'])->first();

            // Transaction
            $creditType = $walletData['credit_type'];
            $charge = (empty($walletData['processing_fee']) || ! isset($walletData['processing_fee'])) ? 0 : $walletData['processing_fee'];

            $withdrawal = Withdrawal::where('user_id', $userID)->where('status', self::$status['pending'])
                ->where('created_at', '>=', now()->subMinutes(3))
                ->where('created_at', '<=', now())
                ->first();
            if (isset($withdrawal)) {
                abort(400, 'You have a pending withdrawal request.');
            }

            // Check Balanace
            $balance = Credit::getBalance($userID, $creditType);
            if ($amount > $balance) {
                abort(400, 'Insufficient Balance.');
            }

            if (isset($charge) && $charge > 0) {
                $charge = DecimalTrait::setDecimal($charge);
                CreditTransaction::insertTransaction($userID, $internalID, $userID, $creditType, $charge, 'withdrawal-charge', $belongId, $belongId, null, $dateTime, null, null, null, null, null, false);
            }
            $currencyRate = $walletData['rate'];

            $convertAmount = $amount;
            $amountAfterCharge = $amount; // Charge separate from amount logic
            if (isset($currencyRate)) {
                $convertAmount = $amountAfterCharge * $currencyRate;
            } else {
                abort(400, json_encode(Lang::get('lang.withdrawal-invalid-currency-rate')));
            }

            if ($amountAfterCharge > 0) {
                CreditTransaction::insertTransaction($userID, $internalID, $userID, $creditType, $amountAfterCharge, 'withdrawal-out', $belongId, $belongId, null, $dateTime, null, null, null, null, null, true);
            }

            Withdrawal::create([
                'serial_number' => $refNo,
                'user_id' => $userID,
                'status' => self::$status['waiting-approval'],
                'remark' => null,
                'user_bank_id' => $data['channel_id'] ?? null,
                'approved_at' => null,
                'approved_by' => null,
                'updater_id' => $userID,
                'credit_type' => $creditType,
                'amount' => $amount,
                'charges' => $charge,
                'receivable_amount' => $amountAfterCharge,
                'currency_id' => $walletData['currency_id'],
                'currency_rate' => $currencyRate,
                'converted_amount' => $convertAmount,
                'withdrawal_type' => self::$withdrawalType['bank'],
                'belong_id' => $belongId,
                'batch_id' => $belongId,
            ]);

            return array_merge([
                'transaction_id' => $refNo,
                'bank_name' => Lang::has('lang.' . $bankDetaill->bank->translation_code) ? Lang::get('lang.' . $bankDetaill->bank->translation_code) : $bankDetaill->bank->translation_code,
                'account_holder_name' => $bankDetaill->account_no,
                'account_holder_number' => $bankDetaill->account_holder,
                'amount' => $amount,
                'charges' => $charge,
                'receivable_amount' => DecimalTrait::setDecimal($amountAfterCharge + $charge), // payable_amount
                'balance' => DecimalTrait::setDecimal($balance - $amountAfterCharge - $charge), // total balance amouint
                'date_time' => DateTrait::dateFormat($dateTime),
            ], ($extraParams ?? []));
        });

        $sendTelegram = Telegram::withdrawalOrder($returnData);
        $returnData['send'] = $sendTelegram ?? null;

        return $returnData;
    }

    public static function updateStatus($data = [])
    {
        $notification = [];
        DB::transaction(function () use ($data, &$notification) {
            $withdrawalIDAry = $data['withdrawal_id_ary'];
            $action = $data['action'];
            $remark = $data['remark'] ?? null;
            $dateTime = $data['datetime'] ?? date('Y-m-d H:i:s');
            $updaterId = auth()->user()->id ?? 0;

            self::with('credit', 'userBank')->whereIn('id', $withdrawalIDAry)->get()->map(function ($withdrawalData) use ($data, $action, $remark, $dateTime, $updaterId, &$notification) {
                $withdrawalStatus = isset($withdrawalData->status) ? array_search($withdrawalData->status, self::$status) : null;

                switch ($action) {
                    case 'approved':
                        // TODO: payout services
                        // $payoutSwitch = SystemSetting::where('name', 'withdrawal.payout.switch')->pluck('value', 'name')->toArray() ?? null;
                        // if (!isset($payoutSwitch['withdrawal.payout.switch'])) {
                        //     abort(400, 'Payout service is not enabled');
                        // }
                        if (($withdrawalData?->status ?? -1) != self::$status['pending']) {
                            return;
                        }

                        // if ($payoutSwitch['withdrawal.payout.switch'] == 1) {
                        if ($data['is_payout'] == true) {
                            // Check Payment Method Status
                            $payment_method = PaymentMethod::where('status', true)->where('type', PaymentMethod::$type['payout'])->first();

                            switch ($payment_method->value) {
                                case 'onepay-payout':
                                    $onepay_payout = resolve(OnePay::class);
                                    $onepay_payout->generateWithdrawOrder(
                                        $withdrawalData->receivable_amount,
                                        $withdrawalData->credit->code,
                                        $withdrawalData->userBank->bank->ref_onepay_wd_bank_id,
                                        $withdrawalData->userBank->account_holder,
                                        $withdrawalData->userBank->account_no,
                                        $withdrawalData->serial_number
                                    );
                                    break;
                                case 'fpay-payout':
                                    $fpay_payout = resolve(FPayPayout::class);
                                    $fpay_payout->generateWithdrawOrder(
                                        $withdrawalData->receivable_amount,
                                        $withdrawalData->credit->code,
                                        $withdrawalData->userBank->bank->ref_bank_id,
                                        $withdrawalData->userBank->account_holder,
                                        $withdrawalData->userBank->account_no,
                                        $withdrawalData->serial_number
                                    );
                                    break;
                                case 'wepay-payout':
                                    $fpay_payout = resolve(WePay::class);
                                    $fpay_payout->generateWithdrawOrder(
                                        $withdrawalData->receivable_amount,
                                        $withdrawalData->userBank->bank->ref_wepay_wd_bank_id,
                                        $withdrawalData->userBank->account_holder,
                                        $withdrawalData->userBank->account_no,
                                        $withdrawalData->serial_number
                                    );
                                    break;
                                // case 'rapidpay-payout':
                                //     $fpay_payout = resolve(RapidPay::class);
                                //     $fpay_payout->generateWithdrawOrder(
                                //         $withdrawalData->receivable_amount,
                                //         $withdrawalData->userBank->bank->ref_rapidpay_bank_id,
                                //         $withdrawalData->userBank->account_holder,
                                //         $withdrawalData->userBank->account_no,
                                //         $withdrawalData->serial_number
                                //     );
                                //     break;
                                default:
                                    abort(400, 'Payout service is not enabled');
                                    break;
                            }
                        }

                        $withdrawalData->update([
                            'remark' => $remark,
                            'status' => Withdrawal::$status[$data['action']],
                            'approved_at' => $dateTime,
                            'approved_by' => $updaterId,
                            'updater_id' => $updaterId,
                        ]);
                        break;

                    case 'pending':
                    case 'waiting-approval':
                        $processingWithdrawal = [
                            'remark' => $remark,
                            'status' => self::$status[$action],
                            'updater_id' => $updaterId,
                        ];

                        $withdrawalData->update($processingWithdrawal);
                        break;

                    case 'rejected':
                    case 'cancel':
                        $internalID = User::select('id')->where('username', 'telexTransfer')->where('user_type', User::$userType['internal-account'])->first()->id ?? null;
                        if (empty($internalID)) {
                            abort(400, 'Invalid Internal Account.');
                        }

                        $belongId = GenerateNumberTrait::GenerateNewId() ?? 0;
                        $creditType = $withdrawalData->credit_type;

                        $refundRes = CreditTransaction::with(['creditInfo'])->selectRaw('SUM(amount) AS refundAmt, ANY_VALUE(credit_id) AS credit_id, ANY_VALUE(user_id) AS user_id')
                            ->whereIn('subject_type', Arr::only(config('subject'), ['withdrawal-out', 'withdrawal-charge']))
                            ->where('belong_id', $withdrawalData->belong_id)->groupBy('credit_id')->get()->map(function ($q) {
                                return [
                                    'refundAmt' => $q->refundAmt,
                                    'credit_type' => $q->creditInfo->name,
                                    'user_id' => $q->user_id,
                                ];
                            });

                        foreach ($refundRes as $refundRow) {
                            $creditType = $refundRow['credit_type'];
                            $refundAmt = $refundRow['refundAmt'];
                            $userID = $refundRow['user_id'];

                            if (empty($creditType) || empty($refundAmt) || empty($userID)) {
                                abort(400, 'Failed to update status.');
                            }
                            if ($refundAmt > 0) {
                                CreditTransaction::insertTransaction($internalID, $userID, $userID, $creditType, $refundAmt, 'withdrawal-refund', $belongId, $withdrawalData->belong_id, null, $dateTime, null, null, null, null, null);
                            }
                        }

                        $cancelWithdrawal = [
                            'remark' => $remark,
                            'status' => self::$status[$action],
                            'updater_id' => $updaterId,
                        ];

                        $withdrawalData->update($cancelWithdrawal);
                        break;
                }

                if (in_array($action, ['pending', 'approved', 'rejected'])) {
                    $userID = $withdrawalData->user_id;
                    $userDeviceToken = UserDevice::where('user_id', $userID)->where('status', UserDevice::$status['active'])->first();
                    if (isset($userDeviceToken)) {
                        $users = User::find($userID);
                        $lang = $users->lang ?? 'en';
                        $templateType = null;
                        $template = null;
                        switch ($action) {
                            case 'pending':
                                $templateType = 'withdrawalProcessing';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;

                            case 'approved':
                                $templateType = 'withdrawalApproved';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;

                            case 'rejected':
                                $templateType = 'withdrawalRejected';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                        }
                        $template['body'] = str_replace('{{serial_no}}', $withdrawalData->serial_number, $template['body']);
                        $notification[] = [
                            'user_id' => $userDeviceToken->user_id,
                            'token' => $userDeviceToken->token,
                            'template' => $template,
                            'data' => ['withdrawal' => $withdrawalData->id, 'type' => $templateType, 'credit_id' => $withdrawalData->credit->id ?? ''],
                            'reference_data' => ['serial_no' => $withdrawalData->serial_number],
                        ];
                    }
                }
            });
        });

        foreach ($notification as $noti) {
            try {
                Traits\FirebaseTrait::sendNotification($noti['token'], $noti['template'], $noti['data'], $noti['user_id'], $noti['reference_data']);
            } catch (\Throwable $th) {
                // throw $th;
            }
        }

        return true;
    }

    public static function generateWithdrawalOrder($params = [])
    {
        $bank = Bank::where('id', $params['bank_id'])->first();

        $payment_method = PaymentMethod::where('status', true)->where('type', PaymentMethod::$type['payout'])->first();
        switch ($payment_method->value) {
            case 'onepay-payout':
                $onepay_payout = resolve(OnePay::class);
                $onepay_payout->generateWithdrawOrder(
                    $params['amount'],
                    'MYR',
                    $bank->ref_onepay_wd_bank_id,
                    $params['account_holder'],
                    $params['account_no']
                );
                break;
            case 'fpay-payout':
                $fpay_payout = resolve(FPayPayout::class);
                $fpay_payout->generateWithdrawOrder(
                    $params['amount'],
                    'MYR',
                    $bank->ref_bank_id,
                    $params['account_holder'],
                    $params['account_no']
                );
                break;
            // case 'rapidpay-payout':
            //     $fpay_payout = resolve(RapidPay::class);
            //     $fpay_payout->generateWithdrawOrder(
            //         $params['amount'],
            //         $bank->ref_rapidpay_bank_id,
            //         $params['account_holder'],
            //         $params['account_no']
            //     );
            //     break;
            default:
                abort(400, 'Payout service is not enabled');
                break;
        }

        return true;
    }

    public static function list(array $params = [])
    {
        $fromDate = $params['from_date'] ?? null;
        $toDate = $params['to_date'] ?? null;
        $updatedFrom = $params['updated_from'] ?? null;
        $updatedTo = $params['updated_to'] ?? null;
        $approvalFromDate = $params['approval_from_date'] ?? null;
        $approvalToDate = $params['approval_to_date'] ?? null;
        $storeId = $params['store_id'] ?? null;
        $username = $params['username'] ?? null;
        $memberId = $params['member_id'] ?? null;
        $phoneNo = $params['phone_no'] ?? null;
        $exStoreId = $params['extra_store_id'] ?? null;
        $status = $params['status'] ?? null;
        $type = $params['type'] ?? null;
        $userFrom = $params['user_from'] ?? null;
        $withdrawal_reference = $params['withdrawal_reference'] ?? null;
        $lang = config('app.locale') ?? 'en';
        $seeAll = $params['see_all'] ?? null;

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        if (isset($params['export']) && ($params['export'] == 1)) {
            $jobData = $params;
            $jobData['model'] = get_class() ?? null;
            $jobData['function'] = __FUNCTION__;
            $jobData['module'] = MODULE;
            $jobData['creator_type'] = MODULE;
            $jobData['creator_id'] = Auth::user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, 'Export Successfully');
        }

        $items = self::query()
            ->with(['user', 'admin', 'userBank', 'userBank.bank', 'currency', 'updateUser', 'updateAdmin'])
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('DATE(created_at)'), '>=', $fromDate);

                return $q->where(DB::raw('DATE(created_at)'), '<=', $toDate);
            })
            ->when((isset($updatedFrom) && isset($updatedTo)), function ($q) use ($updatedFrom, $updatedTo) {
                $q->where(DB::raw('DATE(updated_at)'), '>=', $updatedFrom);

                return $q->where(DB::raw('DATE(updated_at)'), '<=', $updatedTo);
            })
            ->when(isset($status), function ($q) use ($status) {
                return $q->where('status', Withdrawal::$status[$status]);
            })
            ->when(isset($exStoreId), function ($q) use ($exStoreId) {
                return $q->whereHas('user.store', function ($q) use ($exStoreId) {
                    $q->whereIn('id', $exStoreId);
                });
            })
            ->when(MODULE == 'admin', function ($q) use ($approvalFromDate, $approvalToDate, $username, $memberId, $phoneNo, $type, $withdrawal_reference, $userFrom, $storeId) {
                $q->when((isset($approvalFromDate) && isset($approvalToDate)), function ($q) use ($approvalFromDate, $approvalToDate) {
                    $q->where(DB::raw('DATE(approved_at)'), '>=', $approvalFromDate);

                    return $q->where(DB::raw('DATE(approved_at)'), '<=', $approvalToDate);
                });
                $q->when(isset($username), function ($q) use ($username) {
                    return $q->whereRelation('user', 'username', 'LIKE', '%' . $username . '%');
                });
                $q->when(isset($memberId), function ($q) use ($memberId) {
                    return $q->whereRelation('user', 'member_id', $memberId);
                });
                $q->when($storeId, function ($query) use ($storeId) {
                    $query->whereRelation('user.store', 'id', $storeId);
                });
                $q->when(isset($phoneNo), function ($q) use ($phoneNo) {
                    return $q->whereRelation('user', function ($q) use ($phoneNo) {
                        $phoneNo = preg_replace('/[^0-9]/', '', $phoneNo);
                        $q->where('phone_no', 'LIKE', "%$phoneNo%");
                    });
                });
                $q->when(isset($userFrom), function ($q) use ($userFrom) {
                    if ($userFrom == 'FW') {
                        // $query->whereNull('party');
                        return $q->whereRelation('user', function ($q) {
                            return $q->whereNull('party');
                        },);
                    } else {
                        // $query->where('party',$userFrom);
                        return $q->whereRelation('user', function ($q) use ($userFrom) {
                            return $q->where('party', $userFrom);
                        },);
                    }
                });
                $q->when(isset($type), function ($q) use ($type) {
                    return $q->where('withdrawal_type', Withdrawal::$withdrawalType[$type]);
                });
                $q->when(isset($withdrawal_reference), function ($q) use ($withdrawal_reference) {
                    return $q->where('serial_number', $withdrawal_reference);
                });

                return $q;
            })
            ->when(in_array(MODULE, ['user', 'app']), function ($q) {
                return $q->where('user_id', auth()->user()->id);
            });
        $sumQuery = clone $items;
        $items = $items->select(['id', 'user_id', 'amount', 'currency_id', 'status', 'remark', 'user_bank_id', 'user_withdrawal_address_id', 'approved_at', 'approved_by', 'updater_id', 'credit_type', 'receivable_amount', 'charges', 'currency_rate', 'converted_amount', 'withdrawal_type', 'serial_number', 'created_at', 'updated_at', 'deleted_at'])
            ->orderBy($order_by, $order_sort)
            ->when((! isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $summary = [
            'total_withdrawal' => 0,
            'total_processing_fee' => 0,
            'total_receivable_amount' => 0,
            'total_net_withdrawal_amount' => 0,

            'total_amount_approved' => 0,
            'total_amount_pending' => 0,
            'total_amount_rejected' => 0,
            'total_amount_cancel' => 0,
        ];
        $totalTable = [
            'total_amount' => 0,
            'total_processing_fee' => 0,
            'total_receivable_amount' => 0,
            'total_net_withdrawal_amount' => 0,

            'total_amount_approved' => 0,
            'total_amount_pending' => 0,
            'total_amount_rejected' => 0,
            'total_amount_cancel' => 0,
        ];

        $sumQuery->selectRaw('SUM(amount) AS amount, SUM(charges) AS charges, SUM(receivable_amount) AS receivable_amount, ANY_VALUE(status) AS status')->get()
            ->map(function ($q) use (&$summary) {
                $summary['total_withdrawal'] += $q->amount;
                $summary['total_processing_fee'] += $q->charges;
                $summary['total_receivable_amount'] += $q->receivable_amount;
                $summary['total_net_withdrawal_amount'] += $q->amount + $q->charges;

                $status = array_search($q->status, self::$status);
                if (isset($totalTable['total_amount_' . $status])) {
                    $summary['total_amount_' . $status] += $q->amount;
                }
            });

        $mapFunc = function ($q) use (&$totalTable) {

            $status = array_search($q->status, self::$status);
            if (in_array(MODULE, ['user', 'app'])) {
                $isEditable = in_array($q->status, [self::$status['waiting-approval']]) ? 1 : 0;
                switch ($status) {
                    case 'pending':
                        $status = 'processing';
                        break;
                }
            } elseif (MODULE == 'admin') {
                $isEditable = in_array($q->status, [self::$status['pending'], self::$status['waiting-approval']]) ? 1 : 0;
            }
            $toCurrency = $q->currency->iso;

            switch (MODULE) {
                case 'admin':
                    if ($q->updater_id >= 1000000) {
                        $updater = $q->updateUser ? $q->updateUser->username : null;
                    } elseif ($q->updater_id == 0) {
                        $updater = 'system';
                    } else {
                        $updater = $q->updateAdmin ? $q->updateAdmin->username : null;
                    }

                    $res = [
                        'id' => $q->id,
                        'withdrawal_reference' => $q->serial_number,
                        'created_at' => DateTrait::dateFormat($q->created_at),
                        'username' => $q->user->username ?? null,
                        'member_id' => $q->user->member_id ?? null,
                        'phone_no' => $q->user->phone_no ?? null,
                        'withdrawal_amount' => DecimalTrait::setDecimal($q->amount ?? 0),
                        'net_withdrawal_amount' => DecimalTrait::setDecimal(($q->amount + $q->charges)),
                        'processing_fee' => DecimalTrait::setDecimal($q->charges ?? 0),
                        'receivable_amount' => DecimalTrait::setDecimal($q->converted_amount ?? 0),
                        'updated_at' => DateTrait::dateFormat($q->updated_at),
                        'updated_by' => $updater,
                        'status' => $status,
                        'status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                        'is_editable' => $isEditable,
                        'bank_display' => Lang::has('lang.' . $q->userBank->bank->translation_code) ? Lang::get('lang.' . $q->userBank->bank->translation_code) : $q->userBank->bank->translation_code,
                        'account_holder' => $q->userBank->account_holder,
                        'account_number' => $q->userBank->account_no,
                        'user_from' => $q->user->party ?? 'FW',
                        'store_name' => $q->user->store->name ?? null,
                    ];
                    $totalTable['total_amount'] += $q->amount;
                    $totalTable['total_processing_fee'] += $q->charges;
                    $totalTable['total_receivable_amount'] += $q->converted_amount;
                    $totalTable['total_net_withdrawal_amount'] += ($q->amount + $q->charges);
                    $status = array_search($q->status, self::$status);
                    if (isset($totalTable['total_amount_' . $status])) {
                        $totalTable['total_amount_' . $status] += $q->amount;
                    }
                    break;

                case 'app':
                    $res = [
                        'id' => $q->id,
                        'reference' => $q->serial_number,
                        'withdrawal_amount' => DecimalTrait::setDecimal($q->amount ?? 0),
                        'net_withdrawal_amount' => DecimalTrait::setDecimal(($q->amount + $q->charges)),
                        'currency_iso' => config('users.default_currency'),
                        'bank_display' => Lang::has('lang.' . $q->userBank->bank->translation_code) ? Lang::get('lang.' . $q->userBank->bank->translation_code) : $q->userBank->bank->translation_code,
                        'account_holder' => $q->userBank->account_holder,
                        'account_number' => $q->userBank->account_no,
                        'created_at' => DateTrait::dateFormat($q->created_at),
                        'status' => $status,
                        'status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                        'is_editable' => $isEditable,
                    ];
                    break;

                case 'user':
                    $res = [];
                    break;
            }

            return (object) $res;
        };

        if (($seeAll == 1)) {
            $data = $items->get()->map($mapFunc);
        } else {
            $items->getCollection()->transform($mapFunc);
            $data = (new ItemsCollection($items));
        }

        if (MODULE == 'admin') {
            if (($seeAll == 1)) {
                $data = ['list' => $data->toArray()] + ['summary' => $summary] + ['totalTable' => $totalTable];
            } else {
                $data->additional['summary'] = $summary;
                $data->additional['totalTable'] = $totalTable;
            }
        }

        return $data;
    }

    public static function getWithdrawalDet(array $params = [])
    {
        if (MODULE == 'user') {
            $uid = Auth::user()->id;
        } else {
            $uid = [];
        }

        $s3Client = null;
        $items = self::query()
            ->with('user', 'admin', 'userBank', 'userBank.bank', 'currency', 'updateUser', 'updateAdmin')
            ->where('id', $params['id'])
            ->when(MODULE == 'user', function ($q) use ($uid) {
                return $q->where('user_id', $uid);
            })
            ->get()
            ->map(function ($q) use (&$s3Client) {
                $status = array_search($q->status, self::$status);
                $toCurrency = $q->currency->iso;
                $userBank = $q->userBank->bank->translation_code ?? null;
                $userBankHolder = $q->userBank->account_holder ?? null;
                $userBankNumber = $q->userBank->account_no ?? null;

                if (in_array(MODULE, ['user', 'app'])) {
                    $isEditable = in_array($q->status, [self::$status['waiting-approval']]) ? 1 : 0;
                    switch ($status) {
                        case 'pending':
                            $status = 'processing';
                            break;
                    }
                } elseif (MODULE == 'admin') {
                    $isEditable = in_array($q->status, [self::$status['pending'], self::$status['waiting-approval']]) ? 1 : 0;
                }

                switch (MODULE) {
                    case 'admin':
                        if ($q->updater_id >= 1000000) {
                            $updater = $q->updateUser ? $q->updateUser->username : null;
                        } elseif ($q->updater_id == 0) {
                            $updater = 'system';
                        } else {
                            $updater = $q->updateAdmin ? $q->updateAdmin->username : null;
                        }

                        $res = [
                            'id' => $q->id,
                            'withdrawal_reference' => $q->serial_number,
                            'created_at' => DateTrait::dateFormat($q->created_at),
                            'username' => $q->user->username ?? null,
                            'member_id' => $q->user->member_id ?? null,
                            'phone_no' => $q->user->phone_no ?? null,
                            'withdrawal_amount' => DecimalTrait::setDecimal($q->amount),
                            'net_withdrawal_amount' => DecimalTrait::setDecimal(($q->amount + $q->charges)),
                            'processing_fee' => DecimalTrait::setDecimal($q->charges),
                            'receivable_amount' => DecimalTrait::setDecimal($q->converted_amount),
                            'bank_display' => Lang::has('lang.' . $userBank) ? Lang::get('lang.' . $userBank) : $userBank,
                            'account_holder' => $userBankHolder,
                            'account_number' => $userBankNumber,
                            'status' => $status,
                            'status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                            'remark' => $q->remark ?: '-',
                            'updated_at' => DateTrait::dateFormat($q->updated_at),
                            'updated_by' => $updater,
                            'is_editable' => $isEditable,
                        ];
                        break;

                    case 'app':
                        $res = [
                            'id' => $q->id,
                            'withdrawal_amount' => DecimalTrait::setDecimal($q->amount ?? 0),
                            'net_withdrawal_amount' => DecimalTrait::setDecimal(($q->amount + $q->charges)),
                            'currency_iso' => config('users.default_currency'),
                            'processing_fee' => DecimalTrait::setDecimal($q->charges),
                            'receivable_amount' => DecimalTrait::setDecimal($q->converted_amount),
                            'bank_display' => Lang::has('lang.' . $userBank) ? Lang::get('lang.' . $userBank) : $userBank,
                            'account_holder' => $userBankHolder,
                            'account_number' => $userBankNumber,
                            'transaction_id' => $q->serial_number,
                            'status' => $status,
                            'status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                            'remark' => $q->remark ?: '-',
                            'created_at' => DateTrait::dateFormat($q->created_at),
                            'updated_at' => DateTrait::dateFormat($q->updated_at),
                            'is_editable' => $isEditable,
                        ];
                        break;

                    case 'user':
                        $res = [];
                        break;
                }

                return $res;
            })
            ->first();

        return $items;
    }
}
