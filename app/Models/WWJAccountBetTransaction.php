<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WWJAccountBetTransaction extends Model
{
    use HasFactory;

    protected $table = 'wwj_account_bet_transaction';

    protected $fillable = [
        'id',
        'user_id',
        'account',
        'total_turn_over',
        'total_bet',
        'total_return',
        'total_win_loss',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
