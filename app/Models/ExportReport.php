<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use App\Traits;
use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\SoftDeletes;

class ExportReport extends Model
{
    use SoftDeletes;
    protected $table = "export_report";

    protected $hidden = [];

    protected $fillable = [
        'name',
        'file_name',
        'completed',
        'list_key',
        'export_data',
        'model',
        'function',
        'failed_message',
        'creator_type',
        'creator_id',
        'deleted',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $completed = [
        'processing' => 0,
        'completed' => 1,
        'failed' => 2
    ];

    public function platform()
    {
        return $this->belongsTo(Platform::class, 'platform_id', 'id');
    }

    public static function get(array $params = [])
    {
        $from = $params['from'] ?? null;
        $to = $params['to'] ?? null;
        $reportName = $params['name'] ?? null;
        $seeAll = $params['see_all'] ?? null;

        $sortableColumns = ['created_at'];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        $user = auth()->user();
        $userID = $user->id;
        $isMaster = $user->is_master ?? 0;
        $role = null;
        if ($isMaster != 1) $role = auth()->user()->getRoles->permissions_id;
        $permissionsId = Permissions::where('name', 'perm-download-export-report')->first()->id ?? null;

        $list = self::select('id', 'name', 'file_name', 'completed', 'deleted', 'created_at')
            ->when(MODULE == 'admin' && isset($userID), function ($query) use ($userID) {
                $query->where('creator_id', $userID);
            })
            ->when(isset($reportName), function ($query) use ($reportName) {
                $query->where('name', $reportName);
            })
            ->when(isset($from), function ($query) use ($from) {
                $query->where(DB::raw('date(created_at)'), '>=', $from);
            })
            ->when(isset($to), function ($query) use ($to) {
                $query->where(DB::raw('date(created_at)'), '<=', $to);
            })
            ->orderBy($order_by, $order_sort)
            ->when((!isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $mapFunc = function ($q) use ($isMaster, $role, $permissionsId) {
            $completed = array_search($q->completed, self::$completed);
            $res = [
                'id' => $q->id,
                'created_at' => Traits\DateTrait::dateFormat($q->created_at),
                'report_name' => Lang::has('lang.' . $q->name) ? Lang::get('lang.' . $q->name) : $q->name,
                'file_name' => $q->file_name,
                'completed' => Lang::has('lang.' . $completed) ? Lang::get('lang.' . $completed) : $completed,
                'deleted' => ($q->deleted > 0) ? 'Yes' : 'No',
                'is_downloadable' => ($isMaster || in_array($permissionsId, $role)) && ($completed == 'completed') ? 1 : 0,
            ];
            return $res;
        };

        if (($seeAll == 1)) {
            return ['list' => $list->get()->map($mapFunc)->toArray()];
        } else {
            $list->getCollection()->transform($mapFunc);
            $data = new ItemsCollection($list);
            return $data;
        }
    }

    public static function download(array $params = [])
    {
        $id = $params['id'] ?? null;
        $data = [];

        $fileRes = self::find($id);
        $pathToFile = storage_path('app') . '/public/' . $fileRes->file_name . '.xlsx';
        if (!file_exists($pathToFile)) abort(400, json_decode('file-not-exists'));
        $data['base64'] = 'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,' . base64_encode(file_get_contents($pathToFile));
        $data['file_name'] = $fileRes->file_name ?? null;
        $data['file_extension'] = 'xlsx';

        return $data;
    }

    public static function exportReport(array $jobData = [])
    {
        $jobData["module"] = MODULE;
        $jobData["creator_type"] = MODULE ?? null;
        $jobData["creator_id"] = Auth::user()->id ?? null;
        $job = new \App\Jobs\ExportExcel($jobData);
        dispatch($job)->onQueue('export');
        abort(200, "Export Successfully");
    }
}
