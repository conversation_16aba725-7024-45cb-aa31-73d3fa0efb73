<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserTicketReward extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'store_id',
        'name',
        'total_token',
        'total_deposit',
        'ref_event_id',
        'is_selected',
        'is_joined',
        'is_dummy',
        'is_read',
        'status',
    ];

    protected $casts = [
        'is_dummy' => 'boolean',
        'is_selected' => 'boolean',
        'is_joined' => 'boolean',
        'is_read' => 'boolean',
        'status' => 'boolean',
    ];

    // WARNING: Hardcoded value
    public static $minDepositAmount = 50;

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class, 'store_id', 'store_id');
    }

    public static function add($params = [])
    {
        if ($params['amount'] < self::$minDepositAmount) {
            return false;
        }

        if (! isset($params['user_id']) || $params['user_id'] == null) {
            return false;
        }

        $isLogExist = UserTicketRewardLog::where('user_id', $params['user_id'])->where('ref_id', $params['ref_id'])->first();
        if (isset($isLogExist) && $isLogExist != null) {
            return false;
        }

        $user = User::find($params['user_id']);
        $angpauEvent = AngpauEvent::getCurrentAngpauEvent();
        if ($angpauEvent == null) {
            return false;
        }

        // $date = Store::getDrawDateByStoreId($user->store_id);
        // if (! isset($date) || $date == null) {
        //     return false;
        // }

        // Amount 50 = 1 ticket
        // $inStores = [29, 30, 31, 6, 7, 8, 12, 20001, 20002, 20003, 20004, 20005, 20006, 20007, 20008, 20009, 20010, 20011];
        // if (in_array($user->store_id, $inStores) && $params['is_online'] == true) {
        //     $params['token'] = (int) ($params['amount'] / self::$minDepositAmount);
        // } else {
        //     return false;
        // }

        if ($params['is_online'] == true) {
            $params['token'] = (int) ($params['amount'] / self::$minDepositAmount);
        } else {
            return false;
        }

        $userTicket = UserTicketReward::where('user_id', $params['user_id'])
            ->where('ref_event_id', $angpauEvent['id'])
            ->first();
        if (isset($userTicket) && $userTicket != null) {
            $userTicket->update([
                'total_token' => $userTicket->total_token + $params['token'],
                'total_deposit' => $userTicket->total_deposit + $params['amount'],
            ]);

            UserAngpau::where('angpau_event_id', $angpauEvent['id'])
                ->where('user_id', $params['user_id'])
                ->update([
                    'ticket' => $userTicket->total_token + $params['token'],
                ]);
        } else {
            UserTicketReward::create([
                'ref_event_id' => $angpauEvent['id'],
                'user_id' => $params['user_id'],
                'store_id' => $user->store_id,
                'name' => $user->username,
                'total_token' => $params['token'],
                'total_deposit' => $params['amount'],
                'is_dummy' => false,
                'status' => true,
            ]);
        }

        UserTicketRewardLog::create([
            'user_id' => $params['user_id'],
            'name' => $user->username,
            'store_id' => $user->store_id,
            'token' => (int) $params['token'],
            'deposit_amount' => $params['amount'],
            'ref_id' => $params['ref_id'],
            'is_dummy' => false,
            'status' => true,
        ]);

        $user_reward = UserReward::where('user_id', $params['user_id'])->first();
        if (! isset($user_reward) && $user_reward == null) {
            UserReward::create([
                'user_id' => $params['user_id'],
                'total_points' => 0,
                'total_token' => $params['token'],
                'total_claimed' => 0,
                'last_deposit_at' => now(),
                'ref_deposit_id' => $params['ref_id'] ?? null,
            ]);
        } else {
            $user_reward->update([
                'total_token' => $user_reward->total_token + $params['token'],
            ]);
        }

        return true;
    }
}
