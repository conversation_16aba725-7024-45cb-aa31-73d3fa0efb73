<?php

namespace App\Models;

use App\Traits\DecimalTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\DB;

class UserAngpau extends Model
{
    use HasFactory;

    protected $fillable = [
        'angpau_event_id',
        'user_id',
        'name',
        'ticket',
        'amount',
        'is_selected',
        'is_dummy',
        'status',
    ];

    protected $casts = [
        'is_selected' => 'boolean',
        'is_dummy' => 'boolean',
    ];

    public function angpauEvent(): BelongsTo
    {
        return $this->belongsTo(AngpauEvent::class, 'angpau_event_id', 'id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public static function hasAngpauFullyClaimed($angpauEventId)
    {
        $angpauEvent = AngpauEvent::find($angpauEventId);
        $userAngpauCount = UserAngpau::where('angpau_event_id', $angpauEventId)->count();

        if (! $angpauEvent | $userAngpauCount >= $angpauEvent->no_of_winner) {
            return true;
        }

        return false;
    }

    public static function isUserEntitle($userId, $minTicket)
    {
        $userTicketReward = UserTicketReward::where('user_id', $userId)->first();

        return ($userTicketReward?->total_token ?? 0) >= $minTicket;
    }

    public static function claimAngpauByUserId($userId, $angpauEventId)
    {
        $user = User::with('userAngpau')->find($userId);
        $angpauEvent = AngpauEvent::find($angpauEventId);
        $userAngpaus = $user?->userAngpau;

        if (! $user) {
            abort(400, json_encode('User not found'));
        }

        if (! $angpauEvent) {
            abort(400, json_encode('Angpau event not found'));
        }

        if (UserPromotion::getUserPromotionByUserId($userId)) {
            abort(400, json_encode('Promotion is active'));
        }

        if (! self::isUserEntitle($userId, $angpauEvent->min_ticket)) {
            abort(400, json_encode('Insufficient ticket'));
        }

        foreach ($userAngpaus as $angpau) {
            if ($angpau->angpau_event_id == $angpauEventId) {
                abort(400, json_encode('Angpau already claimed'));
            }
        }

        if ($angpauEvent->current_pool_amount < 1) {
            abort(400, json_encode('Angpau is fully claimed'));
        }

        $amount = rand($angpauEvent->min_amount, $angpauEvent->max_amount);

        if ($amount > $angpauEvent->current_pool_amount) {
            $amount = $angpauEvent->current_pool_amount;
        }

        DB::transaction(function () use ($userId, $user, $angpauEvent, $angpauEventId, $amount) {
            $angpauEvent->decrement('current_pool_amount', $amount);

            UserAngpau::create([
                'angpau_event_id' => $angpauEventId,
                'user_id' => $userId,
                'name' => $user->name,
                'amount' => $amount,
            ]);

            $dummyUserAngpausCount = UserAngpau::where('angpau_event_id', $angpauEventId)
                ->where('is_dummy', true)
                ->count();
            $totalDummyAmount = UserAngpau::where('angpau_event_id', $angpauEventId)
                ->where('is_dummy', true)
                ->sum('amount');
            $dummyPool = $angpauEvent->pool_amount_display - $angpauEvent->pool_amount - $totalDummyAmount;

            if ($dummyUserAngpausCount < 50 && $dummyPool > 0) {
                $randString = str_shuffle('abcdefghijklmnopqrstuvwxyz');
                $randString2 = str_shuffle('abcdefghijklmnopqrstuvwxyz');

                $dummyAmount = rand($angpauEvent->min_amount, $angpauEvent->max_amount) * 2;
                $dummyAmount2 = rand($angpauEvent->min_amount, $angpauEvent->max_amount) * 2;

                if ($dummyAmount > $dummyPool) {
                    $dummyAmount = $dummyPool;
                }

                if ($dummyAmount2 > $dummyPool) {
                    $dummyAmount2 = $dummyPool;
                }

                UserAngpau::create([
                    'angpau_event_id' => $angpauEventId,
                    'name' => $randString,
                    'amount' => $dummyAmount,
                    'is_dummy' => true,
                ]);

                UserAngpau::create([
                    'angpau_event_id' => $angpauEventId,
                    'name' => $randString2,
                    'amount' => $dummyAmount2,
                    'is_dummy' => true,
                ]);
            }

            UserPromotion::applyPromotion($angpauEvent->promotion_id, $amount);
        });

        return $amount;
    }

    public static function getAngpauRankByUserId($userId, $angpauEventId)
    {
        $userAngpaus = UserAngpau::where('angpau_event_id', $angpauEventId)
            ->orderBy('amount', 'DESC')
            ->get();

        $index = $userAngpaus?->search(function ($angpau) use ($userId) {
            return $angpau->user_id == $userId;
        });

        return is_numeric($index ?? null) ? $index + 1 : null;
    }

    public static function getTopUserAngpauList($angpauEvent, $userId, $limit)
    {
        $currentRank = self::getAngpauRankByUserId($userId, $angpauEvent['id']);
        $userAngpaus = UserAngpau::where('angpau_event_id', $angpauEvent['id'])
            ->orderBy('amount', 'DESC')
            ->limit($limit)
            // ->whereDate('created_at', $angpauEvent['end_date'])
            ->get()
            ?->map(function ($e, $i) use ($currentRank) {
                if ($currentRank && $currentRank == $i + 1) {
                    return [
                        'rank' => $i + 1,
                        'name' => $e->name,
                        'ticket' => $e->ticket,
                        'amount' => DecimalTrait::setDecimal($e->amount),
                    ];
                }

                return [
                    'rank' => $i + 1,
                    'name' => substr($e->name, 0, 1) . '***' . substr($e->name, -1),
                    'ticket' => $e->ticket,
                    'amount' => DecimalTrait::setDecimal($e->amount),
                ];
            })
            ->toArray();

        $userAngpaus[0] = ($userAngpaus[0] ?? null) ? array_merge($userAngpaus[0], [
            'image' => 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/angpau/angpau_no_1.png',
        ]) : null;

        $userAngpaus[1] = ($userAngpaus[1] ?? null) ? array_merge($userAngpaus[1], [
            'image' => 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/angpau/angpau_no_2.png',
        ]) : null;

        $userAngpaus[2] = ($userAngpaus[2] ?? null) ? array_merge($userAngpaus[2], [
            'image' => 'https://funw-pub.s3.ap-southeast-1.amazonaws.com/production/angpau/angpau_no_3.png',
        ]) : null;

        if ($currentRank > $limit) {
            $userAngpau = UserAngpau::where('angpau_event_id', $angpauEvent['id'])
                ->where('user_id', $userId)
                ->first();
            $userAngpaus[] = [
                'rank' => $currentRank,
                'name' => $userAngpau->name,
                'ticket' => $userAngpau->ticket,
                'amount' => DecimalTrait::setDecimal($userAngpau->amount),
            ];
        }

        return array_filter($userAngpaus, fn($value) => ! is_null($value) && $value !== '');
    }

    public static function getRankAngpauList($angpauEventId, $userId, $limit)
    {
        $currentRank = self::getAngpauRankByUserId($userId, $angpauEventId);
        $userAngpaus = UserAngpau::where('angpau_event_id', $angpauEventId)
            ->orderBy('amount', 'DESC')
            ->limit($limit)
            ->get()
            ?->map(function ($e, $i) use ($currentRank) {
                if ($currentRank && $currentRank == $i + 1) {
                    return [
                        'rank' => $i + 1,
                        'name' => $e->name,
                        'amount' => DecimalTrait::setDecimal($e->amount),
                        'claim_date' => date_format($e->created_at, 'Y-m-d'),
                    ];
                }

                return [
                    'rank' => $i + 1,
                    'name' => substr($e->name, 0, 1) . '***' . substr($e->name, -1),
                    'amount' => DecimalTrait::setDecimal($e->amount),
                    'claim_date' => date_format($e->created_at, 'Y-m-d'),
                ];
            })
            ->toArray();

        return array_filter($userAngpaus, fn($value) => ! is_null($value) && $value !== '');
    }
}
