<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Traits;

class ApiStatus extends Model
{
    protected $table = 'api_status';

    protected $hidden = [
    ];

    protected $fillable = [
        'user_id',
        'api_path',
        'status',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $status = [ // this is api status only
        'pending' => 0,
        'success' => 1,
        'failed' => 2,
    ];
}
