<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Arr;
use App\Traits;

class TelexTransfer extends Model
{
    protected $table = 'telex_transfer';

    protected $hidden = [
    ];

    protected $fillable = [
        'serial_number',
        'user_id',
        'type',
        'credit_type',
        'amount',
        'charges',
        'receivable_amount',
        'from_currency_id',
        'currency_id',
        'currency_rate',
        'converted_amount',
        'bank_id',
        'is_daily_limit',
        'belong_id',
        'status',
        'remark',
        'approved_at',
        'approved_by',
        'updater_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $status = [
        'pending' => 0,
        'in-progress' => 2,
        'rejected' => 3,
        'approved' => 1,
        'cancel' => 4,
    ];

    public static $type = [
        'normal-transfer' => 1,
        'instant-transfer' => 2,
    ];

    public static $reqBranchCode = [
        'INR',
    ];

    public function user(){
        return $this->belongsTo(User::class,"user_id","id");
    }

    public function currency(){
        return $this->belongsTo(Currency::class,"currency_id","id");
    }

    public function fromCurrency(){
        return $this->belongsTo(Currency::class,"from_currency_id","id");
    }

    public function bank(){
        return $this->belongsTo(Bank::class,"bank_id","id");
    }

    public function telexTransferDetail(){
        return $this->hasMany(TelexTransferDetail::class,"telex_transfer_id","id");
    }

    public function admin(){
        return $this->belongsTo(Admin::class,"updater_id","id");
    }

    public static function addTelexTransfer(array $params = [])
    {
        $dateTime = date('Y-m-d H:i:s');
        $date = date('Y-m-d',strtotime($dateTime));
        $step = $params['step'] ?? null;
        $userId = $params['user_id'] ?? null;
        $creditIdd = $params['credit_id'] ?? null;
        $type = $params['type'] ?? null;
        $currencyId = $params['currency_id'] ?? null;
        $amount = $params['amount'] ?? null;
        $bankId = $params['bank_id'] ?? null;
        $isFavourite = $params['is_favourite'] ?? null;
        $transferDetail = Arr::only($params,['account_holder','account_number', 'branch_code']);

        $walletData = $params['wallet_data'];
        $telexTransferData = $params['telex_transfer_data'];
        $bankList = $params['bank_list'];

        $transferType = array_search($type,self::$type) ?? null;
        $rate = $telexTransferData['rate'] ?? 0;
        $dailyLimit = $telexTransferData['daily_limit'] ?? 0;
        $isDailyLimit = $telexTransferData['is_daily_limit'] ?? 0;
        $minOrder = $telexTransferData['min_order'] ?? 0;

        /*$totalTransferAmt = self::selectRaw('SUM(converted_amount) AS totalTransferAmt')
                            ->where('user_id', $userId)
                            ->when(isset($currencyId), function ($q) use ($currencyId) {
                                $q->where('currency_id', $currencyId);
                            })
                            ->whereIn('status', Arr::except(self::$status, ['rejected', 'cancel']))
                            ->whereDATE('created_at', $date)
                            ->first()->totalTransferAmt ?? 0;

        $dailyLimitBalance = $dailyLimit - $totalTransferAmt;
        if ((($totalTransferAmt + $amount) <= $dailyLimit) && (!empty($telexTransferData['daily_rate'])) && $dailyLimitBalance >= $minOrder) {
            $rate = $telexTransferData['daily_rate'];
        }*/

        $convertAmt = $amount;
        $amount = Traits\DecimalTrait::setDecimal($amount / $rate, null, true);
        $processFee = $telexTransferData['processing_fee'] ?? 0;
        $receivedAmt = Traits\DecimalTrait::setDecimal(($amount));

        $summaryData = array_merge([
                "received_amount" => $receivedAmt,
                "converted_received_amount" => $convertAmt,
                "currency" => $telexTransferData['currency_iso'],
                "country_iso" => $telexTransferData['country_iso_code'],
                "rate" => $rate,
                "amount" => Traits\DecimalTrait::setDecimal($amount),
                "from_currency" => $telexTransferData['from_currency_iso'],
                "from_country_iso" => $telexTransferData['from_country_iso_code'],
                "processing_fee" =>  Traits\DecimalTrait::setDecimal($telexTransferData['processing_fee']),
                "total_deducted" => Traits\DecimalTrait::setDecimal($amount + $processFee),
                "bank_display" => $bankList['display'],
                "transfer_method" => Lang::has('lang.tt-transfer') ? Lang::get('lang.tt-transfer') : 'tt-transfer',
                "transfer_type" => Lang::has('lang.'.$transferType) ? Lang::get('lang.'.$transferType) : $transferType,
                "created_at" => Traits\DateTrait::dateFormat($dateTime)
            ],$transferDetail);

        if($step == 4){
            return ["summary_data" => $summaryData];
        }

        $belongId =Traits\GenerateNumberTrait::GenerateNewId() ?? 0;
        $refNo = Traits\GenerateNumberTrait::generateReferenceNo(self::query(),'serial_number',NULL,"tt-".array_search($type,self::$type));

        $insertData = [
            "serial_number" => $refNo,
            "user_id" => $userId,
            "credit_type" => $walletData['credit_type'],
            "type" => $type,
            "amount" => $amount,
            "charges" => $processFee,
            "receivable_amount" => $receivedAmt,
            "from_currency_id" => $telexTransferData['from_currency_id'],
            "currency_id" => $currencyId,
            "currency_rate" => $rate,
            "converted_amount" => $convertAmt,
            "bank_id" => $bankId,
            "is_daily_limit" => $isDailyLimit,
            "belong_id" => $belongId,
            "status" => self::$status['pending'],
        ];

        $summaryData = array_merge($summaryData, [
            'transaction_id' => $refNo
        ]);

        $result = DB::transaction(function () use ($insertData, $dateTime, $transferDetail, $summaryData, $isFavourite) {
            $internalID = User::select('id')->where('username','telexTransfer')->where('user_type',User::$userType['internal-account'])->first()->id ?? Null;
            if(empty($internalID)){
                abort(400,'Invalid Internal Account.');
            }

            CreditTransaction::insertTransaction($insertData['user_id'], $internalID, $insertData['user_id'], $insertData['credit_type'], $insertData['amount'], "tt-".array_search($insertData['type'],self::$type), $insertData['belong_id'], $insertData['belong_id'], null, $dateTime, null, null, null, null, null, true);
            if($insertData['charges'] > 0){
                CreditTransaction::insertTransaction($insertData['user_id'], $internalID, $insertData['user_id'], $insertData['credit_type'], $insertData['charges'], "tt-".array_search($insertData['type'],self::$type)."-charges", $insertData['belong_id'], $insertData['belong_id'], null, $dateTime, null, null, null, null, null, true);
            }

            $telexTransferRes = self::create($insertData);
            
            foreach ($transferDetail as $detailName => $detailValue) {
                if (!isset($detailValue) || empty($detailValue)) continue;
                if(is_array($detailValue)){
                    $extraParam = ["type" => 'json', "reference"=>$detailValue];
                }else{
                    $extraParam = ["type" => 'string', "value"=>$detailValue];
                }

                TelexTransferDetail::create(array_merge([
                    "telex_transfer_id" => $telexTransferRes->id,
                    "name" => $detailName,
                ],$extraParam));
            }

            if(isset($isFavourite) && ($isFavourite > 0) && (empty($params['favourite_id']))){
                FavouriteBank::create([
                    "user_id" => $insertData['user_id'],
                    "bank_id" => $insertData['bank_id'],
                    "account_no" => ($transferDetail['account_number'] ?? null),
                    "account_holder" => ($transferDetail['account_holder'] ?? null),
                    "branch_code" => ($transferDetail['branch_code'] ?? null),
                    "status" => FavouriteBank::$status['active']
                ]);
            }

            return [
                "summary_data" => $summaryData
            ];
        });

        $telegramParams = [
            'serial_number' => $refNo,
            'from' => $telexTransferData['from_currency_iso'],
            'to' => $telexTransferData['currency_iso'],
            'datetime' => $dateTime
        ];

        $sendTelegram = Telegram::telexTransferOrder($telegramParams);
        $result['send'] = $sendTelegram ?? null;
        
        return $result;
    }

    public static function getTelexTransferList($data = []){
        $isLeader = $data['isLeader'] ?? null;
        $username = $data['username'] ?? null;
        $memberId = $data['member_id'] ?? null;
        $phoneNo = $data['phone_no'] ?? null;
        $name = $data['name'] ?? null;
        $transactionId = $data['transaction_id'] ?? null;
        $fromDate = $data['from_date'] ?? Null;
        $toDate = $data['to_date'] ?? null;
        $updatedFrom = $data['updated_from'] ?? Null;
        $updatedTo = $data['updated_to'] ?? null;
        $status = $data['status'] ?? null;
        $seeAll = $data['see_all'] ?? null;
        $currency = $data['currency'] ?? null;

        $sortableColumns = [];

        $tableTotal['amount'] = 0;

        // order
        $order_by = isset($data['order_by']) ? $data['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($data['order_sort']) ? $data['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        if ((isset($data['export']) && ($data['export'] == 1)) || (isset($data['bank_export']) && ($data['bank_export'] == 1))) {
            $jobData = $data;
            $jobData['file_name'] = isset($data['bank_export']) ? $data['bank_export_file_name'] : $data['file_name'];
            $jobData['export_data'] = isset($data['bank_export']) ? [] : $data['export_data'];
            $jobData["model"] = get_class() ?? null;
            $jobData["function"] = __FUNCTION__;
            $jobData["module"] = MODULE;
            $jobData["creator_type"] = MODULE;
            $jobData["creator_id"] = auth()->user()->id ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, "Export Successfully");
        }

        // limit
        $limit = $data['limit'] ?? config('app.pagination_rows');
        $items = self::query()
            ->with(['user','currency','fromCurrency','admin','telexTransferDetail', 'bank'])
            ->when(in_array(MODULE,['app','user']),function ($q) use($isLeader){
                if(isset($isLeader) && $isLeader == 1){
                    $downlines = TreeSponsor::where('trace_key', 'LIKE', "%".auth()->user()->id."/%")->get('user_id')->pluck('user_id')->toArray();
                    $q->whereIn('user_id',$downlines);    
                } else {
                    $q->where('user_id',auth()->user()->id);    
                }
            })
            ->when((isset($fromDate) && isset($toDate)),function ($q) use($fromDate,$toDate){
                $q->whereDate('created_at',">=",$fromDate);
                return $q->whereDate('created_at',"<=",$toDate);
            })
            ->when((isset($updatedFrom) && isset($updatedTo)),function ($q) use($updatedFrom,$updatedTo){
                $q->whereDate('updated_at',">=",$updatedFrom);
                return $q->whereDate('updated_at',"<=",$updatedTo);
            })
            ->when(isset($memberId), function ($query) use ($memberId) {
                return $query->whereRelation('user','member_id', $memberId);
            })
            ->when(isset($phoneNo), function ($query) use ($phoneNo) {
                $query->whereRelation('user', function($q) use ($phoneNo) {
                    $phoneNo = preg_replace('/[^0-9]/', '', $phoneNo);
                    $q->where('phone_no', 'LIKE', "%$phoneNo%");
                });
            })
            ->when(isset($name), function ($query) use ($name) {
                $query->whereRelation('user', 'name', $name);
            })
            ->when(isset($transactionId), function ($query) use ($transactionId) {
                return $query->where('serial_number', $transactionId);
            })
            ->when(isset($status), function ($query) use ($status) {
                return $query->where('status', $status);
            })
            ->when(isset($currency), function ($query) use ($currency) {
                return $query->whereHas('currency', function ($q) use ($currency) {
                    return $q->where('id', $currency);
                });
            });

        $summaryQuery = clone $items;
        $items = $items->orderBy($order_by, $order_sort)
            ->when((!isset($seeAll) || $seeAll == 0),function ($q) use ($limit){
                return $q->paginate($limit);
            });

        if (MODULE == 'admin'){
            $tableTotal['charges'] = 0;
            $tableTotal['received_amount'] = 0;
            $tableTotal['transfer_amount'] = 0;

            
            $summaryQueryRes = $summaryQuery->selectRaw('SUM(amount + charges) AS total_transfer, SUM(charges) AS charges')->first();
            $summary = [
                "total_transfer_requested" => Traits\DecimalTrait::setDecimal((self::selectRaw('SUM(amount) AS total_amount')->first()->total_amount ?? 0)),
                "total_transfer" => isset($summaryQueryRes->total_transfer) ? Traits\DecimalTrait::setDecimal($summaryQueryRes->total_transfer) : Traits\DecimalTrait::setDecimal(0),
                "total_charge" => isset($summaryQueryRes->charges) ? Traits\DecimalTrait::setDecimal($summaryQueryRes->charges) : Traits\DecimalTrait::setDecimal(0),
            ];
        }

        $mapFunc = function($q) use(&$tableTotal) {
            $status = array_search($q->status, self::$status);

            $telexTransferDetailAry = [];
            foreach ($q->telexTransferDetail->keyBy('name') as $name => $value) {
                $telexTransferDetailAry[$name] = ($value->type == 'string') ? $value->value : $value->reference;
            }

            switch (MODULE) {
                case 'admin':
                    $res = [
                        'id' => $q->id,
                        'transaction_id' => $q->serial_number,
                        'created_at' => Traits\DateTrait::dateFormat($q->created_at),
                        'member_id' => $q->user->member_id ?? null,
                        'phone_no' => $q->user->phone_no ?? null,
                        'amount' => Traits\DecimalTrait::setDecimal($q->amount + $q->charges),
                        'charges' => Traits\DecimalTrait::setDecimal($q->charges),
                        'transfer_amount' => Traits\DecimalTrait::setDecimal($q->receivable_amount),
                        "currency_iso" => $q->currency->iso,
                        "currency_rate" => Traits\DecimalTrait::setDecimal($q->currency_rate, 3),
                        'received_amount' => Traits\DecimalTrait::setDecimal($q->converted_amount),
                        'bank_name' => isset($q->bank->translation_code) ? Lang::get('lang.'.$q->bank->translation_code) : null,
                        'bank_account_name' => $telexTransferDetailAry['account_holder'] ?? null,
                        'bank_account_number' => $telexTransferDetailAry['account_number'] ?? null,
                        'branch_code' => $telexTransferDetailAry['branch_code'] ?? null,
                        "status" => $status,
                        "status_display" => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                        'updated_by' => $q->admin->username ?? null,
                        'updated_at' => Traits\DateTrait::dateFormat($q->updated_at),
                    ];
                    $tableTotal['amount'] = Traits\DecimalTrait::setDecimal($tableTotal['amount']) + $q->amount + $q->charges;
                    $tableTotal['charges'] = Traits\DecimalTrait::setDecimal($tableTotal['charges']) + $q->charges;
                    $tableTotal['transfer_amount'] = Traits\DecimalTrait::setDecimal($tableTotal['transfer_amount']) + $q->receivable_amount;
                    $tableTotal['received_amount'] = Traits\DecimalTrait::setDecimal($tableTotal['received_amount']) + $q->converted_amount;
                    break;

                case 'app':
                case 'user':
                    foreach ($q->telexTransferDetail->keyBy('name') as $name => $value) {
                        $telexTransferDetailAry[$name] = ($value->type == 'string') ? $value->value : $value->reference;
                    }

                    $res = [
                        'id' => $q->id,
                        'name' => $q->user->name ?? null,
                        'phone_no' => $q->user->phone_no ?? null,
                        'transaction_id' => $q->serial_number,
                        'transfer_amount' => Traits\DecimalTrait::setDecimal($q->receivable_amount),
                        "from_currency_iso" => $q->fromCurrency->iso,
                        "currency_iso" => $q->currency->iso,
                        "status" => $status,
                        "status_display" => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                        'bank_account_name' => $telexTransferDetailAry['account_holder'] ?? null,
                        'created_at' => Traits\DateTrait::dateFormat($q->created_at),
                        'updated_at' => Traits\DateTrait::dateFormat($q->updated_at),
                    ];
                    $tableTotal['amount'] = Traits\DecimalTrait::setDecimal($tableTotal['amount']) + $q->amount;
                    break;
            }

            return (object) $res;
        };

        if(($seeAll == 1)){
            if(MODULE == 'admin') return ['list' => $items->get()->map($mapFunc)->toArray(), 'table_total' => $tableTotal,'summary' => $summary];
            return ['list' => $items->get()->map($mapFunc)->toArray(), 'table_total' => $tableTotal];
        }else{
            
            $items->getCollection()->transform($mapFunc);
            foreach ($tableTotal as $key => &$value) {
                $value = Traits\DecimalTrait::setDecimal($value);
            }
            $data = new ItemsCollection($items);

            $data->additional['table_total'] = $tableTotal;
            if(MODULE == 'admin') $data->additional['summary'] = $summary;
            return $data;
        }
    }

    public static function getTelexTransferDetail(array $params = [])
    {
        $data = self::with(['user','bank','telexTransferDetail','fromCurrency','currency'])
            ->where('id',$params['id'])->get()->map(function($q){
                $telexTransferDetailAry = [];
                foreach ($q->telexTransferDetail->keyBy('name') as $name => $value) {
                    $telexTransferDetailAry[$name] = ($value->type == 'string') ? $value->value : $value->reference;
                }

                $status = array_search($q->status,  self::$status);
                $type = array_search($q->type,  self::$type);

                switch (MODULE) {
                    case 'admin':
                        $res = [
                            'id' => $q->id,
                            "transaction_id" => $q->serial_number,
                            "created_at" => Traits\DateTrait::dateFormat(($q->created_at)),
                            'member_id' => $q->user->member_id ?? null,
                            'name' => $q->user->name ?? null,
                            'phone_no' => $q->user->phone_no ?? null,
                            'bank_name' => isset($q->bank->translation_code) ? Lang::get('lang.'.$q->bank->translation_code) : null,
                            'bank_account_name' => $telexTransferDetailAry['account_holder'] ?? null,
                            'bank_account_number' => $telexTransferDetailAry['account_number'] ?? null,
                            'branch_code' => $telexTransferDetailAry['branch_code'] ?? null,
                            'amount' => Traits\DecimalTrait::setDecimal($q->amount + $q->charges),
                            'processing_fee' => Traits\DecimalTrait::setDecimal(($q->charges)),
                            'transfer_amount' => Traits\DecimalTrait::setDecimal($q->receivable_amount),
                            "currency_iso" => $q->currency->iso,
                            "currency_rate" => $q->currency_rate,
                            'received_amount' => Traits\DecimalTrait::setDecimal($q->converted_amount),
                            "status" => $status,
                            "status_display" => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                            "type" => $type,
                            "type_display" => Lang::has('lang.'.$type) ? Lang::get('lang.'.$type) : $type,
                            "remark" => $q->remark ?? null,
                            'updated_by' => $q->admin->username ?? null,
                            'updated_at' => Traits\DateTrait::dateFormat($q->updated_at),
                        ];
                        break;
                    
                    case 'user':
                    case 'app':
                        $res = [
                            'id' => $q->id,
                            'received_amount' => Traits\DecimalTrait::setDecimal($q->converted_amount),
                            'transfer_amount' => Traits\DecimalTrait::setDecimal($q->receivable_amount),
                            'processing_fee' => Traits\DecimalTrait::setDecimal(($q->charges)),
                            'total_deduct' => Traits\DecimalTrait::setDecimal(($q->receivable_amount + $q->charges)),
                            'bank_name' => isset($q->bank->translation_code) ? Lang::get('lang.'.$q->bank->translation_code) : null,
                            'bank_account_name' => $telexTransferDetailAry['account_holder'] ?? null,
                            'bank_account_number' => $telexTransferDetailAry['account_number'] ?? null,
                            'branch_code' => $telexTransferDetailAry['branch_code'] ?? null,
                            "currency_iso" => $q->currency->iso,
                            "from_currency_iso" => $q->fromCurrency->iso,
                            "currency_rate" => $q->currency_rate,
                            "transaction_id" => $q->serial_number,
                            "status" => $status,
                            "status_display" => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                            "type" => $type,
                            "type_display" => Lang::has('lang.'.$type) ? Lang::get('lang.'.$type) : $type,
                            "remark" => $q->remark ?? null,
                            "created_at" => Traits\DateTrait::dateFormat(($q->created_at)),
                            'updated_at' => Traits\DateTrait::dateFormat($q->updated_at),
                            'is_cancellable' => (($q->status == self::$status['pending']) ? 1 : 0),
                        ];
                        break;
                }
                return $res;
            })->first();

        if (empty($data)) {
            throw new \Exception(Lang::get('lang.telex-transfer-not-exist'));
        }

        return $data;
    }

    public static function editTelexTransfer($data = []){
        $notification = [];
        DB::transaction(function () use ($data, &$notification) {

            $telexTransferId = $data['id'];
            $statusId = $data['status'];
            $status = array_search($statusId,self::$status);
            $remark = $data['remark'] ?? null;
            $dateTime = $data['datetime'] ?? date('Y-m-d H:i:s');
            $updaterId = auth()->user()->id;

            $creditTypeToId = Credit::select(DB::raw('min(id) as id'), 'type')->groupBy('type')->get()->pluck('id', 'type');
            $telexTransferRes = self::where('id',$telexTransferId)->get();
            foreach($telexTransferRes as $telexTransferRow){
                $insertBounsIn = false;
                switch($status){
                    case 'approved':
                        $approveWithdrawal = [
                            'remark' => $remark,
                            'status' => $statusId,
                            'approved_at' => $dateTime,
                            'approved_by' => $updaterId,
                            'updater_id' => $updaterId,
                        ];
                        $telexTransferRow->update($approveWithdrawal);
                        $insertBounsIn = true;
                        break;

                    case 'pending' :
                    case 'in-progress' :
                        $pendingWithdrawal = [
                            'remark' => $remark,
                            'status' => $statusId,
                            'updater_id' => $updaterId,
                        ];
                        $telexTransferRow->update($pendingWithdrawal);
                        break;
                        

                    case 'rejected':
                    case 'cancel':
                        $internalID = User::select('id')->where('username','telexTransfer')->where('user_type',User::$userType['internal-account'])->first()->id ?? Null;
                        if(empty($internalID)){
                            abort(400,'Invalid Internal Account.');
                        }
                        
                        $belongId = Traits\GenerateNumberTrait::GenerateNewId() ?? 0;
                        $creditType   = $telexTransferRow->credit_type;
                        $type = array_search($telexTransferRow->type,self::$type);

                        $refundRes = CreditTransaction::with(['creditInfo'])->selectRaw('SUM(amount) AS refundAmt, ANY_VALUE(credit_id) AS credit_id, ANY_VALUE(user_id) AS user_id')
                                    ->whereIn('subject_type',[config('subject')['tt-'.$type],config('subject')['tt-'.$type."-charges"]])
                                    ->where('belong_id',$telexTransferRow->belong_id)->groupBy('credit_id')->get()->map(function($q){
                                        return  [
                                            "refundAmt" => $q->refundAmt,
                                            "credit_type" => $q->creditInfo->name,
                                            "user_id" => $q->user_id,
                                        ];
                                    });
                        foreach ($refundRes as $refundRow) {
                            $creditType = $refundRow['credit_type'];
                            $refundAmt = $refundRow['refundAmt'];
                            $userID = $refundRow['user_id'];

                            if(empty($creditType) || empty($refundAmt) || empty($userID)){
                                abort(400,'Failed to update status.');
                            }
                            if($refundAmt > 0){
                                CreditTransaction::insertTransaction($internalID, $userID, $userID, $creditType, $refundAmt, ('tt-'.$type."-refund"), $belongId, $telexTransferRow->belong_id, null, $dateTime, null, null, null, null, null);
                            }
                        }

                        $cancelWithdrawal = [
                            'remark' => $remark,
                            'status' => $statusId,
                            'updater_id' => $updaterId,
                        ];

                        $telexTransferRow->update($cancelWithdrawal);
                        break;
                }

                if (in_array($status, ['in-progress', 'approved', 'rejected'])) {
                    $userID = $telexTransferRow->user_id;
                    $creditType   = $telexTransferRow->credit_type;
                    $userDeviceToken = UserDevice::where('user_id', $userID)->where('status', UserDevice::$status['active'])->first();
                    if (isset($userDeviceToken)) {
                        $users = User::find($userID);
                        $lang = $users->lang ?? 'en';
                        $templateType = null;
                        $template = null;
                        switch($status) {
                            case 'in-progress':
                                $templateType = 'ttransferProcessing';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;

                            case 'approved':
                                $templateType = 'ttransferApproved';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;

                            case 'rejected':
                                $templateType = 'ttransferRejected';
                                $template = Traits\FirebaseTrait::template($templateType, $lang);
                                break;
                        }
                        $template['body'] = str_replace('{{serial_no}}', $telexTransferRow->serial_number, $template['body']);
                        $notification[] = [
                            'user_id' => $userDeviceToken->user_id,
                            'token' => $userDeviceToken->token,
                            'template' => $template,
                            'data' => ['ttransfer' => $telexTransferRow->id, 'type' => $templateType, 'credit_id' => $creditTypeToId[$creditType] ?? ''],
                            'reference_data' => ["serial_no" => $telexTransferRow->serial_number]
                        ];
                    }
                }
            }
        });
        foreach($notification as $noti) {
            Traits\FirebaseTrait::sendNotification($noti['token'], $noti['template'], $noti['data'], $noti['user_id'], $noti['reference_data']);
        }
        return lang::get('lang.update-successfully');
    }
}
