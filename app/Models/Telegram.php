<?php

namespace App\Models;

use App\Traits;
use App\Models;
use App\Services\TelegramService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Traits\TelegramTrait;
use App\Traits\DecimalTrait;
use Illuminate\Support\Facades\Cache;

class Telegram extends Model
{
    public static $sendEnv = ['staging', 'production'];

    public static function telexTransferOrder($params = [])
    {
        if (!in_array(env('APP_ENV'), self::$sendEnv)) return false;

        $refNo = $params['serial_number'] ?? null;
        $from = $params['from'] ?? null;
        $to = $params['to'] ?? null;
        $dateTime = $params['datetime'] ?? date("Y-m-d H:i:s");

        try {
            $eventDescription = "Order\n";
            $eventDescription .= $refNo . " (" . $to . ")\n";
            $eventDescription .= $dateTime;

            TelegramTrait::sendTelegram(null, $eventDescription);
        } catch (\Exception $e) {
            return false;
        }
        return true;
    }

    public static function depositOrder($params = [])
    {
        if (!in_array(env('APP_ENV'), self::$sendEnv)) return false;

        $txID = $params['transaction_id'] ?? null;
        $dateTime = $params['datetime'] ?? date("Y-m-d H:i:s");

        try {
            $eventDescription = "Deposit(" . ($params['currency'] ?? null) . ")\n";
            $eventDescription .= $txID . "\n";
            $eventDescription .= $dateTime;
            TelegramTrait::sendTelegram(null, $eventDescription);
        } catch (\Exception $e) {
            return false;
        }
        return true;
    }

    public static function withdrawalOrder($params = [])
    {
        if (!in_array(env('APP_ENV'), self::$sendEnv)) return false;

        $txID = $params['transaction_id'] ?? null;
        $dateTime = $params['datetime'] ?? date("Y-m-d H:i:s");

        try {
            $eventDescription = "Withdrawal\n";
            $eventDescription .= $txID . "\n";
            $eventDescription .= $dateTime;

            // TelegramTrait::sendTelegram(null,$eventDescription);
            (new TelegramService)->sendMessage($eventDescription);
        } catch (\Exception $e) {
            return false;
        }
        return true;
    }

    public static function cardRechargeOrder($params = [])
    {
        if (!in_array(env('APP_ENV'), self::$sendEnv)) return false;

        $dateTime = $params['datetime'] ?? date("Y-m-d H:i:s");

        try {
            $eventDescription = "❗️❗️" . ($params['store'] ?? null) . "❗️❗️\n\n";
            $eventDescription .= "Date & Time: " . $dateTime . "\n";
            $eventDescription .= "Phone: " . $params['phone_no'] . "\n";
            $eventDescription .= "Card No: " . $params['card_no'] . "\n";
            $eventDescription .= "Amount: " . $params['amount'];

            TelegramTrait::send('-4676214828', $eventDescription);
        } catch (\Exception $e) {
            return false;
        }
        return true;
    }

    public static function sendStoreStatus($params = [])
    {
        if (!in_array(env('APP_ENV'), self::$sendEnv)) return false;

        $dateTime = date("Y-m-d H:i:s A");

        try {
            $eventDescription = $params['title'] . "\n";
            $eventDescription .= $dateTime . "\n\n";
            $eventDescription .= $params['content'];

            TelegramTrait::send('-4715111384', $eventDescription);
        } catch (\Exception $e) {
            return false;
        }
        return true;
    }
}
