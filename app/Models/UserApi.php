<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;

class UserApi extends Model
{
    protected $table = 'user_api';

    protected $hidden = [
    ];

    protected $fillable = [
        'parent_id',
        'name',
        'priority',
        'status',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
    ];

    public static function getScheduleAPI()
    {
        return self::where('status',self::$status['active'])->orderBy('parent_id','ASC')->orderBy('priority','ASC')->get()->map(function($q){
            $status = array_search($q->status,  self::$status);
            $res = [
                'id' => $q->id,
                'parent_id' => $q->parent_id,
                'name' => $q->name,
                'name_display' => Lang::has('lang.'.$q->name) ? Lang::get('lang.'.$q->name) : $q->name,
                'status' => $q->status,
                'status_display' => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status
            ];
            return $res;
        });
    }
}
