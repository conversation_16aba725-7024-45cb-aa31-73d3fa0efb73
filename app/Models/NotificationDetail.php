<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class NotificationDetail extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'notification_id',
        'language_type',
        'title',
        'content',
    ];

    public function notification()
    {
        return $this->belongsTo(Notification::class);
    }
}
