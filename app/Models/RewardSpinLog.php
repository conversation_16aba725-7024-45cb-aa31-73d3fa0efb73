<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class RewardSpinLog extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'reward_spin_id',
        'reward_type',
        'is_free',
        'image',
        'value',
        'status',
        'is_dummy',
        'name',
    ];

    protected $appends = ['won'];

    protected $casts = [
        'status' => 'boolean',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function rewardSpin()
    {
        return $this->belongsTo(RewardSpin::class, 'reward_spin_id');
    }

    public function won(): Attribute
    {
        // $this->reward_type == RewardSpin::$reward_type['point'] ? $this->value . ' Points' : $this->value
        $return = null;
        switch ($this->reward_type) {
            case RewardSpin::$reward_type['point']:
                $return = $this->value.' Points';
                break;
            case RewardSpin::$reward_type['promotion']:
                $return = $this->value.' Bonus Promotions';
                break;
            case RewardSpin::$reward_type['prize']:
                $return = $this->value;
                break;
            default:
                $return = $this->value.' Coins';
                break;
        }

        return Attribute::make(
            get: fn () => $return
        );
    }

    public static function getSpunList(array $params = [])
    {
        $dateTime = date('Y-m-d H:i:s');
        $date = date('Y-m-d', strtotime($dateTime));
        $sortableColumns = ['created_at'];
        $status = $params['status'] ?? null;
        $createdFrom = $params['created_from'] ?? null;
        $createdTo = $params['created_to'] ?? null;

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : 'null';
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        // see all
        $seeAll = $params['see_all'] ?? null;

        $report = self::query()->with([
            'user',
            'rewardSpin',
        ])
            ->when(isset($status), function ($q) use ($status) {
                return $q->where('status', $status);
            })
            ->when($createdFrom, function ($q) use ($createdFrom) {
                return $q->whereDATE('created_at', '>=', $createdFrom);
            })
            ->when($createdTo, function ($q) use ($createdTo) {
                return $q->whereDATE('created_at', '<=', $createdTo);
            })
            ->orderBy($order_by, $order_sort)
            ->orderBy('id', $order_sort)
            ->when((! isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $mapFunc = function ($item) {

            switch (MODULE) {
                case 'admin':
                    $res = [
                        'name' => $item->user->name,
                        'spin_type' => $item->rewardSpin->title,
                        'spin_reward' => $item->value,
                        'spin_date' => $item->created_at->format('Y-m-d H:i:s'),
                    ];
                    break;

                case 'user':
                    $res = [];
                    break;
            }

            return $res;
        };

        if (($seeAll == 1)) {
            return ['list' => $report->get()->map($mapFunc)->toArray()];
        } else {
            $report->getCollection()->transform($mapFunc);

            return (new ItemsCollection($report))->toArray();
        }
    }

    public static function isFreeClaimedByUserId($userId)
    {
        $count = self::where('user_id', $userId)
            ->where('is_free', true)
            ->whereDate('created_at', today())
            ->count();

        return $count > 0;
    }

    /**
     * Get lucky spin list with export functionality
     *
     * @return array
     */
    public static function getLuckySpinList(array $params = [])
    {
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 10;
        $search = $params['search'] ?? '';
        $orderBy = $params['order_by'] ?? 'created_at';
        $orderSort = $params['order_sort'] ?? 'desc';
        $isFree = $params['is_free'] ?? 'all';
        $dateFrom = $params['date_from'] ?? null;
        $dateTo = $params['date_to'] ?? null;
        $startDate = $params['start_date'] ?? null;
        $endDate = $params['end_date'] ?? null;
        $seeAll = $params['see_all'] ?? 0;
        $isExport = isset($params['is_export']) && $params['is_export'] === true;

        if (! $isExport && isset($params['export']) && ($params['export'] == 1)) {
            if (! isset($params['export_data']) || ! isset($params['export_data']['data'])) {
                $params['export_data'] = [
                    'data' => [
                        'id' => 'ID',
                        'user_id' => 'User ID',
                        'user_name' => 'User Name',
                        'title' => 'Title',
                        'is_free' => 'Is Free',
                        'value' => 'Value',
                        'created_at' => 'Created At',
                    ],
                ];
            }

            $params['see_all'] = 1;

            $jobData = $params;
            $jobData['model'] = get_class();
            $jobData['function'] = __FUNCTION__;
            $jobData['module'] = MODULE;
            $jobData['creator_type'] = MODULE;
            $jobData['creator_id'] = $params['user_id'] ?? null;
            $job = new \App\Jobs\ExportExcel($jobData);
            dispatch($job)->onQueue('export');
            abort(200, 'Export Successfully');
        }

        $params['is_export'] = true;

        $query = self::with(['user', 'rewardSpin'])
            ->where('status', 1)
            ->where('is_dummy', false);

        if ($startDate && $endDate) {
            $query->whereDate('created_at', '>=', $startDate)
                ->whereDate('created_at', '<=', $endDate);
        }

        if ($dateFrom && $dateTo) {
            $query->whereDate('created_at', '>=', $dateFrom)
                ->whereDate('created_at', '<=', $dateTo);
        }

        if ($isFree === 'true') {
            $query->where('is_free', true);
        } elseif ($isFree === 'false') {
            $query->where('is_free', false);
        }

        if (! empty($search)) {
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            })->orWhereHas('rewardSpin', function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%");
            });
        }

        $total = $query->count();

        if ($orderBy === 'user_name') {
            $query->join('users', 'reward_spin_logs.user_id', '=', 'users.id')
                ->orderBy('users.name', $orderSort);
        } elseif ($orderBy === 'title') {
            $query->join('reward_spins', 'reward_spin_logs.reward_spin_id', '=', 'reward_spins.id')
                ->orderBy('reward_spins.title', $orderSort);
        } else {
            $query->orderBy($orderBy, $orderSort);
        }

        if ($seeAll != 1) {
            $logsQuery = $query->skip(($page - 1) * $limit)
                ->take($limit);
        } else {
            $logsQuery = $query;
        }

        $logs = $logsQuery->get();

        $list = $logs->map(function ($log) use ($isExport) {
            $isFreeValue = $isExport ? ($log->is_free ? 'Yes' : 'No') : ($log->is_free ? 'Yes' : 'No');

            return [
                'id' => $log->id,
                'user_id' => $log->user_id,
                'user_name' => $log->user->name ?? '',
                'title' => $log->rewardSpin->title ?? '',
                'is_free' => $isFreeValue,
                'value' => $log->value,
                'created_at' => $log->created_at ? $log->created_at->format('Y-m-d H:i:s') : '',
            ];
        });

        $pagination = [
            'current_page' => (int) $page,
            'from' => ($page - 1) * $limit + 1,
            'last_page' => ceil($total / $limit),
            'per_page' => (int) $limit,
            'to' => min($page * $limit, $total),
            'total' => $total,
        ];

        return [
            'list' => $list,
            'pagination' => $pagination,
            'meta' => null,
        ];
    }
}
