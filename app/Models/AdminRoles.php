<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Http\Resources\ItemsCollection;
use App\Traits;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;

class AdminRoles extends Model
{
    protected $table = 'admin_roles';

    protected $hidden = [];

    protected $fillable = [
        "name",
        "permissions_id",
        "status",
        "created_at",
        "updated_at",
        "deleted_at",
    ];

    public static $status = [
        "inactive" => 0,
        "active" => 1,
    ];

    protected $casts = [
        'permissions_id' => 'array'
    ];

    public static function getRoleList($params = [])
    {
        $status = $params['status'] ?? null;
        $fromDate = $params['date_from'] ?? null;
        $toDate = $params['date_to'] ?? null;
        $seeAll = $params['see_all'] ?? null;
        $name = $params['name'] ?? null;
        $updateFromDate = $params['updated_date_from'] ?? null;
        $updateToDate = $params['updated_date_to'] ?? null;

        $sortableColumns = ['id'];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        $items = self::query()
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->whereDate('created_at', ">=", date("Y-m-d 00:00:00", strtotime($fromDate)));
                return $q->whereDate('created_at', "<=", date("Y-m-d 23:59:59", strtotime($toDate)));
            })
            ->when((isset($updateFromDate) && isset($updateToDate)), function ($q) use ($updateFromDate, $updateToDate) {
                $q->whereDate('updated_at', ">=", date("Y-m-d 00:00:00", strtotime($updateFromDate)));
                return $q->whereDate('updated_at', "<=", date("Y-m-d 23:59:59", strtotime($updateToDate)));
            })
            ->when((isset($status)), function ($q) use ($status) {
                return $q->where('status', AdminRoles::$status[$status]);
            })
            ->when((isset($name)), function ($q) use ($name) {
                return $q->where('name', 'like', '%'.$name.'%');
            })
            ->orderBy($order_by, $order_sort)
            ->when((!isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $mapFunc = function ($q) {
            $statusDisplay = array_search($q->status, AdminRoles::$status);
            $res = [
                'id' => $q->id,
                'name' => $q->name,
                'created_at' => Traits\DateTrait::dateFormat($q->created_at),
                'updated_at' => $q->updated_at ? Traits\DateTrait::dateFormat($q->updated_at) : Traits\DateTrait::dateFormat($q->created_at),
                'status' => $q->status,
                'status_display' => Lang::has('lang.' . $statusDisplay) ? Lang::get('lang.' . $statusDisplay) : $statusDisplay,
            ];

            return (object) $res;
        };

        if (!isset($seeAll) || $seeAll == 0) {
            $data = $items->getCollection()->transform($mapFunc);
            $data = (new ItemsCollection($items))->toArray();
            return ["data" => $data];
        } else {
            return ["data" => $items->get()->map($mapFunc)];
        }
    }

    public static function getRoleDetail($params = [])
    {
        $allPermissions = Permissions::getList();
        $activePermissions = isset($allPermissions) ? $allPermissions->pluck('id')->toArray() : [];
        if (isset($params['role_id'])) {
            $data = AdminRoles::where('id', $params['role_id'])->first();
            if (!isset($data) || empty($data)) {
                abort(400, Lang::get("lang.role-not-exists"));
            }

            $permissionsIds = array_values(array_intersect($activePermissions, $data['permissions_id'] ?? []));
            $status = array_search($data['status'], AdminRoles::$status);
            $detail = [
                "name" => $data['name'],
                "permissions_id" => $permissionsIds,
                "status" => $status,
                "status_display" => Lang::has("lang." . $status) ? Lang::get('lang.' . $status) : $status,
                "created_at" => Traits\DateTrait::dateFormat($data['created_at']),
                "updated_at" => Traits\DateTrait::dateFormat($data['updated_at']),
                "is_editable" => (!auth()->user()->is_master && in_array($params['role_id'], [1, 2])) ? 0 : 1,
            ];
        }
        return ["details" => $detail ?? null, "permissions" => $allPermissions];
    }

    public static function addRole($params = [])
    {
        DB::transaction(function ($q) use ($params) {
            $params['status'] = AdminRoles::$status[$params['status']];
            AdminRoles::create($params);
        });
        return true;
    }

    public static function editRole($params = [])
    {
        DB::transaction(function () use ($params) {
            $roles = AdminRoles::find($params['role_id']);
            unset($params['role_id']);
            $permissionsId = $params['permissions_id'];

            if (auth()->user()->is_master && $roles->id == 1) {
                Permissions::where('master_disabled', 0)->update(['disabled' => 1]);
                Permissions::whereIn('id', $permissionsId)->where('master_disabled', 0)->update(['disabled' => 0]);
            }
            $params['status'] = AdminRoles::$status[$params['status']];
            $roles->update($params);
        });
        return true;
    }
}
