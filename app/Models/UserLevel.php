<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class UserLevel extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'vip_level_id',
        'current_point',
        'rebate',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function vip_level()
    {
        return $this->belongsTo(VipLevel::class);
    }

    public static function getVipByUserIdOrCreateIfNull($userId)
    {
        $userLevel = self::with('vip_level')
            ->where('user_id', $userId)
            ->first();
        $vipLevel = VipLevel::where('status', true)
            ->orderBy('level')
            ->first();

        if (! $vipLevel) {
            return null;
        }

        if (! $userLevel) {
            self::create([
                'user_id' => $userId,
                'vip_level_id' => $vipLevel->id,
                'rebate' => $vipLevel->rebate,
            ]);
            $userLevel = self::with('vip_level')
                ->where('user_id', $userId)
                ->first();
        }

        $nextLevel = VipLevel::where('status', true)
            ->where('level', $userLevel->vip_level->level + 1)
            ->first();

        $designMeta = json_decode($userLevel->vip_level->design_meta);
        $nextLevelDesignMeta = $nextLevel ? json_decode($nextLevel->design_meta) : null;

        return [
            'current_point' => $userLevel->current_point,
            'current_rank' => [
                'name' => $userLevel->vip_level->name,
                'level' => $userLevel->vip_level->level,
                'target_point' => $userLevel->vip_level->target_point,
                'badge_image' => $userLevel->vip_level->badge_image,
                'card_logo' => $designMeta->card_logo,
                'bg_gradient_color' => $designMeta->bg_gradient_color,
                'balance_theme' => $designMeta->balance_theme,
                'vip_card_theme' => $designMeta->vip_card_theme,
            ],
            'next_rank' => $nextLevel ? [
                'name' => $nextLevel->name,
                'level' => $nextLevel->level,
                'target_point' => $nextLevel->target_point,
                'badge_image' => $nextLevel->badge_image,
                'card_logo' => $nextLevelDesignMeta->card_logo,
                'bg_gradient_color' => $nextLevelDesignMeta->bg_gradient_color,
                'balance_theme' => $nextLevelDesignMeta->balance_theme,
                'vip_card_theme' => $nextLevelDesignMeta->vip_card_theme,
            ] : null,
        ];
    }

    public static function claimDailyVipPoint($userId)
    {
        // WARNING: Hardcode
        $minDeposit = 200;
        $totalDeposit = Deposit::where('user_id', $userId)
            ->where('status', Deposit::$status['approved'])
            ->sum('receivable_amount');

        $dailyTransactionCount = UserLevelTransaction::where('user_id', $userId)
            ->where('type', UserLevelTransaction::$type['daily'])
            ->whereDate('created_at', today())
            ->count();

        if ($totalDeposit < $minDeposit) {
            return null;
        }

        if ($dailyTransactionCount > 0) {
            return null;
        }

        $userLevel = self::with('vip_level')
            ->where('user_id', $userId)
            ->first();

        if (! $userLevel || ! $userLevel->vip_level) {
            return null;
        }

        $point = $userLevel->vip_level->daily_point;
        // $userLevel->increment('current_point', $point);
        self::addPointByUserId($userId, $point);

        UserLevelTransaction::create([
            'user_id' => $userId,
            'user_level_id' => $userLevel->id,
            'type' => UserLevelTransaction::$type['daily'],
            'point' => $point,
            'status' => UserLevelTransaction::$status['completed'],
        ]);

        return [
            'message' => 'Point claimed',
            'point' => $point,
        ];
    }

    public static function addPointByUserId($userId, $point)
    {
        $userLevel = self::with('vip_level')
            ->where('user_id', $userId)
            ->first();

        $userLevel->increment('current_point', $point);

        $userLevel = self::with('vip_level')
            ->where('user_id', $userId)
            ->first();

        $nextLevel = VipLevel::where('status', true)
            ->where('level', $userLevel->vip_level->level + 1)
            ->first();

        if ($nextLevel && $userLevel->current_point >= $nextLevel->target_point) {
            $userLevel->update(['vip_level_id' => $nextLevel->id]);
        }
    }

    public static function getByUserId($userId)
    {
        $vipLevel = VipLevel::where('status', true)
            ->orderBy('level')
            ->first();

        $userLevel = self::firstOrCreate(['user_id' => $userId], [
            'user_id' => $userId,
            'vip_level_id' => $vipLevel->id,
            'rebate' => $vipLevel->rebate,
        ]);

        return $userLevel;
    }

    public static function updateVIPLevelAndRebateByUserId($adminId, $userId, $vipLevelId)
    {
        $userLevel = self::where('user_id', $userId)->first();
        $vipLevel = VipLevel::find($vipLevelId);

        if (! $userLevel) {
            abort(400, json_encode('User level not exists'));
        }

        if (! $vipLevel) {
            abort(400, json_encode('Vip level not exists'));
        }

        if ($userLevel->vip_level_id == $vipLevelId) {
            return;
        }

        DB::transaction(function () use ($userLevel, $vipLevelId, $vipLevel, $userId, $adminId) {
            $currentPoint = $userLevel->current_point;
            $adjustment = $vipLevel->target_point - $currentPoint;

            $userLevel->update([
                'vip_level_id' => $vipLevelId,
                'rebate' => $vipLevel->rebate,
                'current_point' => $vipLevel->target_point,
            ]);

            if ($adjustment > 0) {
                UserLevelTransaction::create([
                    'user_id' => $userId,
                    'user_level_id' => $userLevel->id,
                    'type' => UserLevelTransaction::$type['adjustment'],
                    'point' => $adjustment,
                    'rebate_amount' => 0,
                    'remark' => $adminId,
                    'status' => UserLevelTransaction::$status['pending'],
                ]);
            }
        });
    }
}
