<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;

class Fingerprint extends Model
{
    const UPDATED_AT = null;

    protected $table = 'fingerprint';

    protected $hidden = [
    ];

    protected $fillable = [
        'fingerprint',
        'data',
        'created_at',
    ];

    protected $casts = [
        'data' => 'array',
    ];

    public static function getList(array $params = []){
        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');
        $items = self::query()
            ->select([
                    'data',
                    'created_at',
            ])
            ->orderBy($order_by, $order_sort)
            ->paginate($limit);
        $items->getCollection()->transform(function($q) {
            $res = [
                'ip_address' => $q->data['ip']??Null,
                'browser' => $q->data['browser']??Null,
                'browser_version' => $q->data['browser-version']??Null,
                'os_platform' => $q->data['os-platform']??Null,
                'user_agent' => $q->data['user-agent']??Null,
                'device' => $q->data['device']??Null,
                'created_at' => $q->created_at,
            ];
            return (object) $res;
        });
        return (new ItemsCollection($items))->toArray();
    }
}

