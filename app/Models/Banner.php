<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Http\Resources\ItemsCollection;
use App\Traits\DateTrait;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;

class Banner extends Model
{
   
    protected $table = 'banner';

    protected $hidden = [
    ];

    protected $fillable = [
        'status',
        'start_date',
        'end_date',
        'creator_id',
        'creator_type',
        'priority',
        'created_at',
        'updater_id',
        'updated_at',
        'deleted_at',
        'link',
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
        'deleted' => 2,
    ];

    public function bannerDetail(){
        return $this->hasMany(BannerDetail::class);
    }

    public function bannerSetting(){
        return $this->hasMany(BannerSetting::class);
    }

    public function admin(){
        return $this->belongsTo(Admin::class,'creator_id','id');
    }

    public function updator()
    {
        return $this->belongsTo(Admin::class, 'updater_id', 'id');
    }

    public static function getBanner(array $params = []) {
        $directorie = File::files(resource_path('banner'));
        $res = File::get($directorie[0]);
        $arr = json_decode($res, true)['data'];
        return $arr;
    }

    public static function addBanner(array $params = []){
        return DB::transaction(function () use ($params){
            $creatorId = Auth::user()->id ?? null;
            $creatorType = isset($creatorId) ? MODULE : 'system';
            $status = $params['status'] ?? 'active';

            $insertBanner = [
                'status' => self::$status[$status],
                'creator_id' => $creatorId,
                'creator_type' => $creatorType,
                'link' => $params['link'] ?? null,
            ];

            if (isset($params['start_date']) && isset($params['end_date'])){
                $insertBanner['start_date'] = $params['start_date'];
                $insertBanner['end_date'] = $params['end_date'];
            }

            $maxBanner = self::max('priority') + 1 ?? 1;

            if (isset($params['priority'])) {
                $insertBanner['priority'] = $params['priority'] > $maxBanner ? $maxBanner : $params['priority'];
                self::where('priority', '>=', $params['priority'])->increment("priority");
            } else {
                $lastPrio = self::orderBy('priority', 'desc')->first();
                $priority = $lastPrio ? ($lastPrio->priority + 1) : 0;
                $insertBanner['priority'] = $priority;
            }

            $bannerId = self::create($insertBanner)->id;

            // Insert Banner Data
            foreach ($params['banner_data'] as $bannerData) {
                $bannerDetail = [
                    "banner_id" => $bannerId,
                    "title" => $bannerData['title'],
                    "language_type" => config('language')[$bannerData['language_type']],
                    "description" => $bannerData['description'],
                    'terms' => $bannerData['terms'],
                ];

                foreach($bannerData['upload'] as $type => $display) {
                    $insertColumn = $type.'_image_data';
                    $bannerDetail[$insertColumn] = ($display ?? []);
                }

                BannerDetail::create($bannerDetail);
            }

            foreach($params['banner_setting'] ?? [] as $key => $value) {
                if ($value == '') continue;

                $record = [
                    "banner_id" => $bannerId,
                    "name" => $key,
                    "value" => $value,
                ];

                BannerSetting::create($record);
            }

            return true;
        });
    }

    public static function getBannerList(array $params = []){
        $fromDate = $params['from_date'] ?? Null;
        $toDate = $params['to_date'] ?? Null;
        $startFromDate = $params['start_from_date'] ?? Null;
        $startToDate = $params['start_to_date'] ?? Null;
        $title = $params['title'] ?? Null;
        $status = $params['status'] ?? Null;
        $lang = config('app.locale') ?? 'en';
        $dateTime = date('Y-m-d H:i:s');
        $date = date('Y-m-d',strtotime($dateTime));

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');
        $items = self::query()
            ->with(['bannerDetail' => function($q){
                $q->orderBy('language_type','DESC');
                return $q;
            }, 'bannerSetting','admin'])
            ->when((in_array(MODULE, ['user', 'app'])), function ($q) use ($date){
                $q->where(function($q) use ($date){
                    $q->where(function($q) use ($date){
                        $q->where(DB::raw('DATE(start_date)'),'<=',$date);
                        $q->where(DB::raw('DATE(end_date)'),'>=',$date);
                    });
                    $q->orWhere(function($q) use ($date){
                        $q->where(DB::raw('DATE(start_date)'),'<=',$date);
                        $q->where('end_date',null);
                    });
                    $q->orWhere(function($q) use ($date){
                        $q->where('start_date',null)->where('end_date',null);
                    });
                });
                $q->where('status', self::$status['active']);
                return $q;
            })
            ->when((isset($fromDate) && isset($toDate)),function ($q) use($fromDate,$toDate){
                $q->where(DB::raw('DATE(created_at)'),">=",$fromDate);
                return $q->where(DB::raw('DATE(created_at)'),"<=",$toDate);
            })
            ->when((isset($startFromDate) && isset($startToDate)), function ($q) use ($startFromDate, $startToDate) {
                $q->where(DB::raw('DATE(start_date)'), ">=", $startFromDate);
                return $q->where(DB::raw('DATE(start_date)'), "<=", $startToDate);
            })
            ->when($title, function ($q) use ($title, $lang) {
                return $q->whereRelation('bannerDetail', function($query) use ($title, $lang) {
                    return $query->where('language_type', config('language')[$lang])->where('title', 'like', '%' . $title . '%');
                });
            })
            ->when(isset($status), function ($q) use ($status) {
                return $q->where('status', self::$status[$status]);
            })
            ->whereNull('deleted_at')
            ->orderBy($order_by, $order_sort)
            ->paginate($limit);

        $items->getCollection()->transform(function($q) use($lang){
            $img = [];
            $bannerSetting = $q->bannerSetting->pluck('value', 'name');
            foreach ($q->bannerDetail as $key => $value) {
                $defaultSetting = $value;
                if (array_search($value['language_type'], config('language')) == $lang) {
                    $defaultSetting = $value;
                    break;
                }
            }
            $mobileImage = $defaultSetting['mobile_image_data'];
            if (!empty($mobileImage)) {
                $img['mobile'] = $mobileImage;
                $img['mobile']['image_display'] = env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $mobileImage['upload_name'];
            }
            
            $appRedirect = isset($bannerSetting['app_redirect']) ? str_replace("_", "-", $bannerSetting['app_redirect']) : null;

            $res = [
                "id" => $q->id,
                "title" => $defaultSetting->title ?? null,
                "priority" => $q->priority,
                "start_date" => DateTrait::dateformat($q->start_date, false),
                "end_date" => DateTrait::dateFormat($q->end_date, false),
                "image_display" => $img,
                "app_redirect" => $bannerSetting['app_redirect'] ?? null,
                "app_redirect_display" => Lang::has('lang.'.$appRedirect) ? Lang::get('lang.'.$appRedirect) : ucfirst(str_replace("-", " ", $appRedirect)),
                "link" => $q->link,
            ];

            if (MODULE == 'admin') {
                $status = array_search($q->status, self::$status);

                $res = array_merge($res,[
                    "created_at" => DateTrait::dateFormat($q->created_at),
                    "creator" => $q->admin->username ?? null,
                    "created_at" => DateTrait::dateFormat($q->created_at),
                    "updated_at" => DateTrait::dateFormat($q->updated_at),
                    "status" => $status,
                    "status_display" => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                    "is_deletable" => $status == 'inactive' ? 1 : 0,
                    "updated_by" => $q->updater_id ? $q->updator->name : null,
                ]);
            }
            
            return (object) $res;
        });
        return (new ItemsCollection($items))->toArray();
    }

    public static function getBannerDetail(array $params = []){
        $items = self::query()
            ->where('id',$params['id'])
            ->with(['bannerDetail','bannerSetting'])
            ->get()
            ->map(function ($q){
                $status = array_search($q->status,self::$status);
                $bannerData = $q->bannerDetail->toArray();
                $bannerSetting = $q->bannerSetting->toArray();

                foreach ($bannerData as &$bannerDetail) {
                    unset($bannerDetail['banner_id']);
                    $bannerDetail['language_type'] = array_search($bannerDetail['language_type'],config('language'));
                    $bannerDetail['language_type_display'] = Lang::has('lang.'.$bannerDetail['language_type']) ? Lang::get('lang.'.$bannerDetail['language_type']) : $bannerDetail['language_type'];
                    $mobileImage = $bannerDetail['mobile_image_data'];
                    unset($bannerDetail['mobile_image_data']);
                    if(!empty($mobileImage)) {
                        $bannerDetail['upload']['mobile'] = $mobileImage;
                        $bannerDetail['upload']['mobile']['image_display'] = env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN').$mobileImage['upload_name'];
                    }
                }

                foreach ($bannerSetting as $setting) {
                    unset($setting['banner_id']);
                    $returnDisplay[str_replace("-", "_", $setting['name'])] = $setting['value'];
                }

                $res = [
                    "id" => $q->id,
                    "start_date" => isset($q->start_date) ? date("Y-m-d", strtotime($q->start_date)) : null,
                    "end_date" => isset($q->end_date) ? date("Y-m-d", strtotime($q->end_date)) : null,
                    "status" => $status,
                    "priority" => $q->priority,
                    "status_display" => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                    "banner_data" => $bannerData ?? null,
                    "banner_setting" => $returnDisplay ?? null,
                    "link" => $q->link,
                ];
                return $res;
            })->first();

        $return ['data']  = $items ?? [];
        return $return;
    }

    public static function editBanner(array $params = []){
        return DB::transaction(function () use ($params){
            $creatorId = Auth::user()->id ?? null;
            $creatorType = isset($creatorId) ? MODULE : 'system';

            if ($params['status'] == 'deleted') {
                self::find($params['id'])->update(['creator_id' => $creatorId, 'status' => self::$status[$params['status']], 'deleted_at' => now()]);
                BannerDetail::where(["banner_id" => $params['id']])->delete();
                BannerSetting::where(["banner_id" => $params['id']])->delete();
                return true;
            }

            $insertBanner = [
                'status' => self::$status[$params['status']],
                'updater_id' => $creatorId,
                'link' => $params['link'] ?? null,
            ];

            if (isset($params['start_date']) && isset($params['end_date'])){
                $insertBanner['start_date'] = $params['start_date'];
                $insertBanner['end_date'] = $params['end_date'];
            }

            $maxBanner = self::max('priority') + 1 ?? 1;

            if (isset($params['priority'])) {
                $insertBanner['priority'] = $params['priority'] > $maxBanner ? $maxBanner : $params['priority'];
                $checkUpdate = self::where('priority', $params['priority'])->whereNot('id', $params['id'])->first();
                if (!empty($checkUpdate)) {
                    self::where('priority', '>=', $params['priority'])->increment("priority");
                }            
            } else {
                $lastPrio = self::orderBy('priority', 'desc')->first();
                $priority = $lastPrio ? ($lastPrio->priority + 1) : 0;
                $insertBanner['priority'] = $priority;
            }

            $banner = self::find($params['id']);
            $banner->update($insertBanner);

            // Update Banner Data
            foreach ($params['banner_data'] as $bannerData) {
                $validLanguage[] = config('language')[$bannerData['language_type']];
                $imageDetail = [
                    "web_image_data" => null,
                    "mobile_image_data" => null
                ];

                foreach($bannerData['upload'] as $key => $display) {
                    $insertColumn = $key.'_image_data';
                    $imageDetail[$insertColumn] = ($display) ?? [];
                }

                BannerDetail::updateOrCreate([
                        'banner_id' => $params['id'], 
                        'language_type' => config('language')[$bannerData['language_type']]
                    ], 
                    array_merge($imageDetail, [
                        "title" => $bannerData['title'],
                        "description" => $bannerData['description'],
                        "terms" => $bannerData['terms'],
                    ])
                );
            }

            BannerDetail::where('banner_id', $params['id'])
                ->whereNotIn('language_type', ($validLanguage ?? []))
                ->delete();

            foreach ($params['banner_setting'] ?? [] as $key => $value) {
                if ($value == '') continue;
                $record[] = $key;

                BannerSetting::updateOrCreate([
                    "banner_id" => $params['id'], 
                    "name" => $key
                ], [
                    "value" => $value
                ]);
            }

            BannerSetting::where('banner_id', $params['id'])
                ->whereNotIn('name', ($record ?? []))
                ->delete(); 

            return true;
        });
    }

    public static function getDashboardBanner(){
        $dateTime = date('Y-m-d H:i:s');
        $date = date('Y-m-d',strtotime($dateTime));
        $lang = config('app.locale') ?? 'en';
        $country_id = $userData['country_id'] ?? null;

        $items = self::query()
            ->whereNull('deleted_at')
            ->where('status',self::$status['active'])
            ->where(function($q) use ($date){
                $q->where(function($q) use ($date){
                    $q->where(DB::raw('DATE(start_date)'),'<=',$date);
                    $q->where(DB::raw('DATE(end_date)'),'>=',$date);
                    return $q;
                })
                ->orWhere(function($q) use ($date){
                    $q->whereNull('start_date')->whereNull('end_date');
                    return $q;
                });
                return $q;
            })
            ->with(['bannerDetail', 'bannerSetting'])
            ->whereRelation('bannerDetail', function($q) {
                $q->whereNull('deleted_at');
                $q->orderBy('language_type','DESC');
                return $q;
            })
            ->when(isset($country_id) && !empty($country_id), function($sq) use ($country_id){
                return $sq->whereRelation('bannerSetting', function ($q) use ($country_id) {
                    $q->whereNull('deleted_at');
                    if (!(is_null($country_id) || $country_id == 0)) $q->whereRaw("(name = 'valid-country' and find_in_set(".$country_id.",value))");
                    return $q;
                });
            })
            ->orderBy('priority', 'asc')
            ->get()
            ->take(5)
            ->map(function ($q) use ($lang){
                $webImageData = null;
                $mobileImageData = null;
                $title = null;
                foreach ($q->bannerDetail as $key => $value) {
                    $webImageData = $value['web_image_data'] ?? null;
                    $mobileImageData = $value['mobile_image_data'] ?? null;
                    if(isset($webImageData)) $webImageData = env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN').$webImageData['upload_name'];
                    if(isset($mobileImageData)) $mobileImageData = env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN').$mobileImageData['upload_name'];
                    $title = $value['title'];
                    if(array_search($value['language_type'],config('language')) == $lang){
                        break;
                    }
                }

                $overallStg = [];
                foreach($q->bannerSetting as $setting) {
                    switch($setting['name']) {
                        case 'banner-url':
                            $overallStg[$setting['name']] = json_decode($setting['value'], true);
                            break;
                        
                        case 'valid-country':
                            $overallStg[$setting['name']] = explode(",", $setting['value']);
                            break;
                        
                        default:
                            $overallStg[$setting['name']] = $setting['value'];
                            break;
                    }
                }

                $res = [
                    "title" => $title,
                    "web_image" => $webImageData,
                    "mobile_image" => $mobileImageData,
                    "url" => $overallStg['banner-url'] ?? null
                ];
                
                return (object) $res;
            });
        return $items ?? null;
    }
}
