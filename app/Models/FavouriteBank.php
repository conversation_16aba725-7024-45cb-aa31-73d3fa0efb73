<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;

class FavouriteBank extends Model
{
    protected $table = 'favourite_bank';

    protected $hidden = [
    ];

    protected $fillable = [
        'user_id',
        'bank_id',
        'account_no',
        'account_holder',
        'branch_code',
        'status',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
    ];

    public function bank(){
        return $this->belongsTo(Bank::class,"bank_id","id");
    }

    public static function getDetail(array $params = []){
        $countryId = null;
        $items = self::query()
            ->with(['bank'])
            ->where('id',$params['id'])
            ->select(["id","bank_id","account_no","account_holder","branch_code","status"])
            ->get()
            ->map(function ($q) use (&$countryId){
                $status = array_search($q->status,self::$status);
                $countryId = $q->bank->country_id;
                $res = [
                    "id" => $q->id,
                    "bank_id" => $q->bank_id,
                    "bank_name_display" => Lang::has('lang.'.$q->bank->translation_code) ? Lang::get('lang.'.$q->bank->translation_code) : $q->bank->name,
                    "account_number" => $q->account_no,
                    "account_holder" => $q->account_holder,
                    "branch_code" => $q->branch_code,
                    "status" => $status,
                    "status_display" => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                ];
                return $res;
            })->first();

        $bankList = Bank::where('status',Bank::$status['active'])
                    ->when(in_array(MODULE,['user','app']),function ($q) use($countryId) {
                        return $q->where('country_id',$countryId);
                    })->get()->map(function ($q){
                        return [
                            "id" => $q->id,
                            "name" => $q->name,
                            "display" => Lang::has('lang.'.$q->translation_code) ? Lang::get('lang.'.$q->translation_code) : $q->name,
                            "priority" => $q->priority
                        ];
                    })->toArray();

        $return = [
            "detail" => $items ?? [],
            "bank_list" => $bankList ?? []
        ];
        return $return;
    }

    public static function edit(array $params = [])
    {
        $favBank = self::find($params['id']);

        $updateCol = [
            "bank_id" => array_key_exists("bank_id",$params) ? $params['bank_id'] : $favBank->bank_id,
            "account_no" => array_key_exists("account_number",$params) ? $params['account_number'] : $favBank->account_no,
            "account_holder" => array_key_exists("account_holder",$params) ? $params['account_holder'] : $favBank->account_holder,
            "branch_code" => array_key_exists("branch_code",$params) ? $params['branch_code'] : $favBank->branch_code,
            "status" => array_key_exists("is_favourite",$params) ? $params['is_favourite'] : $favBank->is_favourite,
        ];

        DB::transaction(function () use ($favBank,$updateCol) {
            $favBank->update($updateCol);
        });

        return true;
    }
}
