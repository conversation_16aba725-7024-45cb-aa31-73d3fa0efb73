<?php

namespace App\Models;

use App\Traits;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class GameCategory extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'icon',
        'description',
        'priority',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    public function game_category_providers()
    {
        return $this->hasMany(GameCategoryProvider::class, 'game_category_id', 'id');
    }

    public static function getListByCategory()
    {
        return self::with('game_category_providers', 'game_category_providers.game_provider', 'game_category_providers.services', 'game_category_providers.service')
            ->where('status', true)
            ->orderBy('priority', 'asc')
            ->get()
            ->map(function ($q) {
                return [
                    'id' => $q->id,
                    'name' => $q->name,
                    'disply' => $q->name,
                    'is_game_list' => false,
                    'list' => $q->game_category_providers->map(function ($q) {
                        if ($q->is_game) {
                            $status = array_search($q->status, Services::$status) ?? null;
                            return [
                                "service_id" => $q->service?->id,
                                "icon" => isset($q->icon) ? env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $q->icon : $q->game_provider->icon,
                                'name' => $q->game_provider->name,
                                "display" => Lang::has('langCustom.service-' . $q->game_provider->name) ? Lang::get('langCustom.service-' . $q->game_provider->name) : $q->game_provider->name,
                                "url" => $q->service->url ?? null,
                                'status' => $status,
                                'isTK8' => 0,
                                'showCashInOutWarning' => 0,
                                'product_id' => (int)$q->service?->product_id,
                                'product_name' => null,
                                'filter_type' => $q->service?->filter_type,
                                'is_game' => 1,
                                'is_deeplink' => $q->service?->is_deeplink
                            ];
                        } else {
                            return [
                                'id' => $q->id,
                                // 'icon' => $q->game_provider->icon,
                                "icon" => isset($q->icon) ? env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $q->icon : $q->game_provider->icon,
                                'name' => $q->game_provider->name,
                                'is_game' => $q->is_game,
                            ];
                        }
                    })
                ];
            });
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->pirority = self::max('priority') + 1;
        });
    }
}
