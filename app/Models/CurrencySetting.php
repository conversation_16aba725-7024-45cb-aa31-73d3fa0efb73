<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use App\Traits;
use App\Traits\DecimalTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Arr;

class CurrencySetting extends Model
{
    use DecimalTrait;

    protected $table = 'currency_setting';

    protected $hidden = [
    ];

    protected $fillable = [
        'from_currency_id',
        'to_currency_id',
        'type',
        'sell_rate',
        'withdrawal_rate',
        'withdrawal_fee',
        'withdrawal_min_amount',
        'withdrawal_max_amount',
        'withdrawal_max_request_daily',
        'convert_out_rate',
        'convert_in_rate',
        'convert_in_daily_limit', 
        'convert_out_daily_limit', 
        'conversion_min_amount',
        'conversion_max_amount',
        'fee',
        'min_amount',
        'instant_rate',
        'normal_rate',
        'daily_rate',
        'daily_limit',
        'disabled',
        'instant_disabled',
        'normal_disabled',
        'creator_id',
        'updater_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $type = [
        'fiat' => 1,
        'crypto' => 2,
        'system_wallet' => 3,
    ];

    public static function edit(array $params = [], $isProcessExchangeRate = false, $isConvert = false)
    {
        $iso = $params['iso'] ?? null;
        $type = $params['type'] ?? null;

        $currency_id = Currency::where(['iso' => $iso])->whereNull('deleted_at')->first()->id ?? null;
        $from_currency_id = Currency::where(['iso' => 'MYR'])->first()->id ?? null;
        if (!(isset($currency_id) && isset($from_currency_id) && isset($type))) abort(400, Lang::get('lang.failed'));

        $currency = Currency::find($currency_id);
        $currencyUpdateCol = ['disabled' => $params['status'] ?? $currency->disabled];

        $settingUniqueIndex = ['from_currency_id' => $from_currency_id, 'to_currency_id' => $currency_id, 'type' => $type];
        $settingUpdateCol = Arr::only($params, ['convert_in_daily_limit', 'convert_out_daily_limit', 'convert_in_rate', 'conversion_max_amount', 'convert_out_rate','conversion_min_amount','sell_rate','withdrawal_rate','withdrawal_fee','withdrawal_min_amount','fee','min_amount','instant_rate','instant_disabled','normal_rate','normal_disabled','daily_rate','daily_limit','disabled']);

        //Handle for update setting history
        $currencySetting = self::whereNull('deleted_at')->where($settingUniqueIndex)->first()->id ?? null;
        $currencySetting = self::find($currencySetting);

        if($currencySetting) {
            foreach($settingUpdateCol as $col => $val) {
                if(is_numeric($val)) {
                    $val = floatval($val);
                    $currencySetting->$col = floatval($currencySetting->$col);
                }

                if(($currencySetting->$col == $val) && !$isProcessExchangeRate) unset($settingUpdateCol[$col]);
            }
        }

        $toCreditName = Credit::where('currency_id', $currency_id)->get()->pluck("name")->first();

        $creditSettingIDList = [];
        $creditSettingUpdateCol = [];
        if($isConvert)
        {
            $creditSettingList = Credit::with([
                'creditSetting' => function ($q) {
                    $q->whereIn('name', ['convert-to']);
                },
                'creditSetting.codeCredit',
                'creditSetting.codeCredit.creditSetting' => function ($q) {
                    $q->whereIn('name', ['convert-to']);
                },
            ])
            ->where('currency_id', $from_currency_id)
            ->get()
            ->pluck('creditSetting')
            ->map(function ($q) {
                $return = [];
                foreach ($q as $key => $value) {
                    $return[$value->value][] = $value->id;
                    $return[$value->value][] = $value->codeCredit->creditSetting->first()->id;
                }
                return $return;
            })->first();

            $creditSettingIDList = $creditSettingList[$toCreditName];
            $creditSettingUpdateCol = ['member' => $params['status']];
        }

        DB::transaction(function () use ($currency, $currencyUpdateCol, $currencySetting, $settingUniqueIndex, $settingUpdateCol, $creditSettingIDList, $creditSettingUpdateCol, $isConvert) {
            $updater_id = Auth::user()->id ?? null;

            // convert dont affect main currency table
            if(!$isConvert){
                $currency->update($currencyUpdateCol);
            }

            if(count($settingUpdateCol) > 0) {
                $settingUpdateCol = array_merge($settingUpdateCol, [
                    'creator_id' => isset($currencySetting) ? $currencySetting->creator_id : $updater_id,
                    'updater_id' => $updater_id,
                ]);

                $settingUpdate = self::updateOrCreate($settingUniqueIndex, $settingUpdateCol);
                if(!$settingUpdate) {
                    throw new \Exception(Lang::get('lang.currency-setting-update-fail'));
                }

                if(isset($currencySetting)) {
                    $currencySetting = array_merge($currencySetting->toArray(), ['setting_id' => $currencySetting->id]);
                    $settingHistory = CurrencySettingHistory::insert($currencySetting);
                }
            }

            if($isConvert){
                CreditSetting::whereIn('id', $creditSettingIDList)->update($creditSettingUpdateCol);
                CreditSetting::checkAndUpdateIsConvertible();
            }
        });

        return true;
    }

    public static function batchEdit(array $params = [], $isConvert = false)
    {
        $isoAry = $params['iso'] ?? null;
        $type = $params['type'] ?? null;

        $currencyIDAry = Currency::whereIN('iso', $isoAry)->whereNull('deleted_at')->get()->pluck('disabled','id')->toArray() ?? null;
        $from_currency_id = Currency::where(['iso' => 'MYR'])->first()->id ?? null;
        if (!(isset($currencyIDAry) && !empty($currencyIDAry) && isset($from_currency_id) && isset($type))) abort(400, Lang::get('lang.failed'));
        
        $settingUpdateAry = Arr::only($params, ['instant_disabled','normal_disabled','daily_limit','fee','min_amount','withdrawal_fee','withdrawal_min_amount', 'conversion_min_amount', 'conversion_max_amount']);
        $settingUpdateCol = [];
        $currencyUpdateCol = [];
        $settingUniqueIndex = [];

        $currencySettingAry = self::where('from_currency_id',$from_currency_id)->whereIN('to_currency_id',array_keys($currencyIDAry))->whereNull('deleted_at')->get()->keyBy('to_currency_id') ?? null;

        $creditSettingList = Credit::with([
            'creditSetting' => function ($q) {
                $q->whereIn('name', ['convert-to']);
            },
            'creditSetting.codeCredit',
            'creditSetting.codeCredit.creditSetting' => function ($q) {
                $q->whereIn('name', ['convert-to']);
            },
        ])
        ->where('currency_id', $from_currency_id)
        ->get()
        ->pluck('creditSetting')
        ->map(function ($q) {
            $return = [];
            foreach ($q as $key => $value) {
                $return[$value->codeCredit->currency_id][] = $value->id;
                $return[$value->codeCredit->currency_id][] = $value->codeCredit->creditSetting->first()->id;
            }
            return $return;
        })->first();

        foreach($currencyIDAry as $currency_id => $disabled) {
            $settingUniqueIndex[$currency_id] = ['from_currency_id' => $from_currency_id, 'to_currency_id' => $currency_id, 'type' => $type];
            $settingUpdateCol[$currency_id] = $settingUpdateAry;
            $currencyUpdateCol[$currency_id] = ['disabled' => $params['status'] ?? $disabled];

            //Handle for update setting history
            $currencySetting = $currencySettingAry[$currency_id] ?? null;

            if($currencySetting) {
                foreach($settingUpdateCol[$currency_id] as $col => $val) {
                    if(is_numeric($val)) {
                        $val = floatval($val);
                        $currencySetting->$col = floatval($currencySetting->$col);
                    }

                    if($currencySetting->$col == $val) unset($settingUpdateCol[$currency_id][$col]);
                }
            } else {
                $settingCreateCol = [
                    'sell_rate' => isset($currencyRate) ? $currencyRate->rate : null,
                    'instant_rate' => isset($currencyRate) ? $currencyRate->rate : null,
                    'normal_rate' => isset($currencyRate) ? $currencyRate->rate : null,
                    'daily_rate' => isset($currencyRate) ? $currencyRate->rate : null,
                ];

                $settingUpdateCol[$currency_id] = array_merge($settingUpdateCol[$currency_id], $settingCreateCol);
            }

            $creditSettingUpdateCol[$currency_id] = ['member' => ($params['status'] == "1"?"0":"1")];
        }

        DB::transaction(function () use ($currencyIDAry, $currencyUpdateCol, $currencySettingAry, $settingUniqueIndex, $settingUpdateCol, $creditSettingList, $creditSettingUpdateCol, $isConvert) {
            $updater_id = Auth::user()->id ?? null;

            foreach($currencyIDAry as $currency_id => $disabled) {
                $currencySettingUpdate = $currencySettingAry[$currency_id] ?? null;

                // convert dont affect main currency table
                if(!$isConvert){
                    $currency = Currency::where('id',$currency_id)->update($currencyUpdateCol[$currency_id]);
                }

                if(isset($settingUpdateCol[$currency_id]) && count($settingUpdateCol[$currency_id]) > 0) {
                    $settingUpdateCol[$currency_id] = array_merge($settingUpdateCol[$currency_id], [
                        'creator_id' => isset($currencySettingUpdate) ? $currencySettingUpdate->creator_id : $updater_id,
                        'updater_id' => $updater_id,
                    ]);

                    $settingUpdate = self::updateOrCreate($settingUniqueIndex[$currency_id], $settingUpdateCol[$currency_id]);
                    if(!$settingUpdate) {
                        throw new \Exception(Lang::get('lang.currency-setting-update-fail'));
                    }

                    if(isset($currencySettingUpdate)) {
                        $currencySettingUpdate = array_merge($currencySettingUpdate->toArray(), ['setting_id' => $currencySettingUpdate->id]);
                        $settingHistory = CurrencySettingHistory::insert($currencySettingUpdate);
                    }
                }

                if(!$isConvert){
                    CreditSetting::whereIn('id', $creditSettingList[$currency_id])->update($creditSettingUpdateCol[$currency_id]);
                }
            }
            CreditSetting::checkAndUpdateIsConvertible();
        });

        return true;
    }

    public function fromCurrency(){
        return $this->belongsTo(Currency::class,"from_currency_id","id");
    }

    public function toCurrency(){
        return $this->belongsTo(Currency::class,"to_currency_id","id");
    }

    public function toCredit(){
        return $this->belongsTo(Credit::class,"to_currency_id","currency_id");
    }
    
    public function fromCredit(){
        return $this->belongsTo(Credit::class,"from_currency_id","currency_id");
    }

    public function updater()
    {
        return $this->belongsTo(Admin::class, 'updater_id', 'id');
    }
}

