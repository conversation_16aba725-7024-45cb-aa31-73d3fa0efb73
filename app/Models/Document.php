<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use App\Traits\DateTrait;
use App\Traits\DecimalTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class Document extends Model
{
    use DecimalTrait, SoftDeletes;

    protected $table = 'document';

    protected $hidden = [
    ];

    protected $fillable = [
        'start_date',
        'end_date',
        'type',
        'status',
        'creator_id',
        'creator_type',
        'created_at',
        'updater_id',
        'updated_at',
        'deleted_at',
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
    ];

    public static $type = [
        'document' => 0,
        'image' => 1,
    ];

    public function documentDetail()
    {
        return $this->hasMany(DocumentDetail::class);
    }

    public function documentSetting()
    {
        return $this->hasMany(DocumentSetting::class);
    }

    public function admin()
    {
        return $this->belongsTo(Admin::class, 'creator_id', 'id');
    }

    public function updatedAdmin()
    {
        return $this->belongsTo(Admin::class, 'updater_id', 'id');
    }

    public static function addDocument(array $params = [])
    {
        return DB::transaction(function () use ($params) {
            $creatorId = Auth::user()->id ?? null;
            $creatorType = isset($creatorId) ? MODULE : 'system';

            $insertDocument = [
                'creator_id' => $creatorId,
                'creator_type' => $creatorType,
                'status' => self::$status[$params['status']],
                'type' => self::$type[$params['type']],
            ];

            if (isset($params['start_date']) && isset($params['end_date'])) {
                $insertDocument['start_date'] = $params['start_date'];
                $insertDocument['end_date'] = $params['end_date'];
            }

            $documentId = Document::create($insertDocument)->id;

            // Insert document Data
            foreach ($params['document'] as $documentData) {
                DocumentDetail::create([
                    "document_id" => $documentId,
                    "title" => $documentData['title'],
                    "attachment_data" => $documentData['attachment'],
                    "language_type" => config('language')[$documentData['language_type']],
                ]);
            }

            return true;
        });
    }

    public static function editDocument(array $params = [])
    {
        return DB::transaction(function () use ($params) {
            $updater_id = Auth::user()->id ?? null;

            $document = self::find($params['id']);

            $updatedocument = [
                'status' => self::$status[$params['status']],
                'type' => self::$type[$params['type']],
                'updater_id' => $updater_id,
            ];

            if (isset($params['start_date']) && isset($params['end_date'])) {
                $updatedocument['start_date'] = $params['start_date'];
                $updatedocument['end_date'] = $params['end_date'];
            }

            $document->update($updatedocument);

            // Insert document Data
            foreach ($params['document'] as $documentData) {
                $updated_id[] = DocumentDetail::updateOrCreate([
                    "document_id" => $params['id'],
                    "language_type" => config('language')[$documentData['language_type']],
                ], [
                    "title" => $documentData['title'],
                    "attachment_data" => $documentData['attachment'],
                ])->id;

            }

            DocumentDetail::whereNotIn('id', $updated_id)->where('document_id', $params['id'])->delete();
            return true;
        });
    }

    public static function getdocumentList(array $params = [])
    {
        $fromDate = $params['from_date'] ?? Null;
        $toDate = $params['to_date'] ?? Null;
        $title = $params['title'] ?? Null;
        $status = $params['status'] ?? Null;
        $type = $params['type'] ?? Null;
        $lang = config('app.locale') ?? 'en';

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        $items = self::query()
            ->with(['documentDetail' => function ($q) {
                $q->orderBy('language_type', 'DESC');
                return $q;
            }, 'updatedAdmin'])
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('DATE(created_at)'), ">=", $fromDate);
                return $q->where(DB::raw('DATE(created_at)'), "<=", $toDate);
            })
            ->when(isset($title), function ($q) use ($title, $lang) {
                return $q->whereHas("documentDetail", function($query) use ($title, $lang) {
                    $query->where('language_type', config('language')[$lang]);
                    $query->where("title", "LIKE", "%" . $title . "%");
                    return $query;
                });
            })
            ->when(isset($status), function ($q) use ($status) {
                return $q->where("status", self::$status[$status]);
            })
            ->when(isset($type), function ($q) use ($type) {
                return $q->where("type", self::$type[$type]);
            })
            ->when(MODULE == 'user', function($q) use ($lang) {
                $q->where(DB::raw("DATE(start_date)"), '<=', date('Y-m-d'));
                $q->where(DB::raw("DATE(end_date)"), '>=', date('Y-m-d'));
                $q->where("status", self::$status['active']);
                return $q;
            })
            ->orderBy($order_by, $order_sort)
            ->paginate($limit);

        $items->getCollection()->transform(function ($q) use ($lang) {
            foreach ($q->documentDetail as $key => $value) {
                $defaultSetting = $value;
                if (array_search($value['language_type'], config('language')) == $lang) {
                    $defaultSetting = $value;
                    break;
                }
            }
            $status = array_search($q->status, self::$status);
            $type = array_search($q->type, self::$type);
            $res = [
                "id" => $q->id,
                "created_at" => isset($q->created_at) ? DateTrait::dateFormat($q->created_at) : null,
                "title" => $defaultSetting->title,
                "type" => $type,
                "type_display" => Lang::has('lang.' . $type) ? Lang::get('lang.' . $type) : $type,
            ];

            if (MODULE == 'admin') {
                $res['status'] = $status;
                $res['status_display'] = Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status;
                $res['start_date'] = isset($q->start_date) ? DateTrait::dateFormat($q->start_date, false) : null;
                $res["end_date"] = isset($q->end_date) ? DateTrait::dateFormat($q->end_date, false) : null;
                $res['updated_by'] = $q->updatedAdmin->username ?? null;
                $res['updated_at'] = isset($q->updated_at) ? DateTrait::dateFormat($q->updated_at) : null;
            }
            if (MODULE == 'user') $res['attachment'] = env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $defaultSetting->attachment_data;
            return (object) $res;
        })->toArray();

        if (MODULE == 'user') {
            $getLatest = self::with(['documentDetail'])
                ->where(DB::raw("DATE(start_date)"), '<=', date('Y-m-d'))
                ->where(DB::raw("DATE(end_date)"), '>=', date('Y-m-d'))
                ->where("status", self::$status['active'])
                ->orderBy($order_by, $order_sort)
                ->take(4)->get()->map(function ($q) use ($lang) {
                foreach ($q->documentDetail as $key => $value) {
                    $defaultSetting = $value;
                    if (array_search($value['language_type'], config('language')) == $lang) {
                        $defaultSetting = $value;
                        break;
                    }
                }
                $type = array_search($q->type, self::$type);
                $res = [
                        "id" => $q->id,
                        "created_at" => isset($q->created_at) ? DateTrait::dateFormat($q->created_at) : null,
                        "title" => $defaultSetting->title,
                        "type" => $type,
                        "type_display" => Lang::has('lang.' . $type) ? Lang::get('lang.' . $type) : $type,
                        "attachment" => env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $defaultSetting->attachment_data,
                    ];
                return (object) $res;
            });
            return array_merge((new ItemsCollection($items))->toArray(), ['latest' => $getLatest]);
        }
        return (new ItemsCollection($items));
    }

    public static function getdocumentDetail(array $params = [])
    {
        $items = self::query()
            ->where('id', $params['id'])
            ->with(['documentDetail', "documentSetting"])
            ->get()
            ->map(function ($q) {
                $documentData = $q->documentDetail->toArray();

                foreach ($documentData as $documentDetail) {
                    $document[] = [
                        "title" => $documentDetail['title'],
                        "attachment" => env('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN') . $documentDetail['attachment_data'],
                        "attachment_name" => $documentDetail['attachment_data'],
                        "language_type" => array_search($documentDetail['language_type'], config("language"))
                    ];
                }
                $status = array_search($q->status, self::$status);
                $type = array_search($q->type, self::$type);
                $res = [
                    "id" => $q->id,
                    "created_at" => isset($q->created_at) ? DateTrait::dateFormat($q->created_at) : null,
                    "type" => $type,
                    "type_display" => Lang::has('lang.' . $type) ? Lang::get('lang.' . $type) : $type,
                    "start_date" => isset($q->start_date) ? DateTrait::dateFormat($q->start_date, false) : null,
                    "end_date" => isset($q->end_date) ? DateTrait::dateFormat($q->end_date, false) : null,
                    "status" => $status,
                    "status_display" => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                    "updated_by" => $q->updatedAdmin->username ?? null,
                    "updated_at" => isset($q->updated_at) ? DateTrait::dateFormat($q->updated_at) : null,
                    "document" => $document,
                ];
                return $res;
            })->first();

        $return['data']  = $items ?? [];
        return $return;
    }
}

