<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CurrencyDetail extends Model
{
    use HasFactory;

    public $fillable = [
        'user_id',
        'user_card_id',
        'store_id',
        'operation_qty',
        'terminal_serial',
        'machine_name',
        'machine_serial',
        'member_card_id',
        'member_card_no',
        'member_phone',
        'operation_type',
        'operation_type_id',
        'date_time',
        'member_balance',
        'latest_member_balance',
        'transaction_at',
        'pnl_ref_id',
        'pnl_amount',
        'user_level_transaction_id'
    ];

    public static $type = [
        'token_in' => 5,
        'token_out' => 6
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function user_card()
    {
        return $this->belongsTo(UserCard::class, 'user_card_id', 'id');
    }

    public function store()
    {
        return $this->belongsTo(Store::class, 'store_id', 'id');
    }
}
