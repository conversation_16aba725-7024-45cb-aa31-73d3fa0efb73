<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserReferral extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'referral_tier_id',
        'type',
        'amount',
        'is_claimed',
        'claimed_at',
    ];

    public static $turnoverMultiplier = 0.001;

    public static $agentMultiplier = 0.003;

    public static $minClaimAmount = '1';

    public static $type = [
        'turnover' => 0,
        'tier' => 1,
    ];

    public static function getUserReferralsByUserId($userId)
    {
        $referrals = self::where('user_id', $userId)->get();

        return $referrals ?? [];
    }

    public static function getTotalBonusClaimedByUserId($userId)
    {
        $total = self::where('user_id', $userId)
            ->where('is_claimed', true)
            ->where('type', self::$type['turnover'])
            ->sum('amount');

        return $total;
    }

    public static function getPendingClaimAmountByUserId($userId)
    {
        $total = self::where('user_id', $userId)
            ->where('is_claimed', false)
            ->where('type', self::$type['turnover'])
            ->sum('amount');

        return $total >= 1 ? $total : 0;
    }

    public static function getPendingClaimUserReferralsByUserId($userId)
    {
        $referrals = self::where('user_id', $userId)
            ->where('is_claimed', false)
            ->where('type', self::$type['turnover'])
            ->get();

        return $referrals ?? [];
    }

    public static function getPendingClaimTierBonusAmountByUserId($userId)
    {
        $total = self::where('user_id', $userId)
            ->whereNotNull('referral_tier_id')
            ->where('is_claimed', false)
            ->where('type', self::$type['tier'])
            ->sum('amount');

        return $total >= 1 ? $total : 0;
    }

    public static function getPendingClaimTierBonusByUserId($userId)
    {
        $referrals = self::where('user_id', $userId)
            ->whereNotNull('referral_tier_id')
            ->where('is_claimed', false)
            ->where('type', self::$type['tier'])
            ->get();

        return $referrals ?? [];
    }

    public static function getClaimedTiersByUserId($userId)
    {
        $tiers = self::where('user_id', $userId)
            ->whereNotNull('referral_tier_id')
            ->where('is_claimed', true)
            ->where('type', self::$type['tier'])
            ->get()
            ->map(function ($e) {
                return $e->referral_tier_id;
            })
            ->toArray();

        return $tiers;
    }

    public static function getUserReferralByUserIdAndReferralTierId($userId, $referralTierId)
    {
        $referral = self::where('user_id', $userId)
            ->where('referral_tier_id', $referralTierId)
            ->first();

        return $referral;
    }
}
