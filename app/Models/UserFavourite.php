<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;

class UserFavourite extends Model
{
    protected $table = 'user_favourite';

    protected $hidden = [
    ];

    protected $fillable = [
        'type',
        'user_id',
        'favourite_id',
        'status',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $type = [
        'transfer' => 1,
        'currency' => 2,
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function userDevice()
    {
        return $this->belongsTo(UserDevice::class,'user_id','user_id')->where('status',UserDevice::$status['active']);
    }

    public static function setFavourite($data = []) {
        DB::transaction(function () use ($data) {
            if(isset($data['is_clear_all']) && ($data['is_clear_all'] == 1)){
                self::where('user_id',$data['user_id'])->where("type", self::$type[$data['type']])->update(["status" => self::$status['inactive']]);
            }else{
                self::updateOrCreate(
                    [
                        "type" => self::$type[$data['type']],
                        "user_id" => $data['user_id'],
                        "favourite_id" => $data['favourite_id'],
                    ],
                    [
                        "status" => $data['status']
                    ]
                );
            }
        });

        return true;
    }
}
