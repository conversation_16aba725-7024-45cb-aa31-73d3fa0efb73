<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;

class State extends Model
{
    protected $table = 'state';

    protected $hidden = [
    ];

    protected $fillable = [
        'name',
        'country_id',
        'zone_id',
        'status',
        'priority',
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
    ];

    public function country(){
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    public function zone(){
        return $this->belongsTo(Zone::class, 'zone_id', 'id');
    }
}