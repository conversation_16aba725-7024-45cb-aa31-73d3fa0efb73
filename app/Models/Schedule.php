<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;
use App\Traits;
use Exception;

class Schedule extends Model
{
    protected $table = 'schedule';

    protected $hidden = [
    ];

    protected $fillable = [
        'api_id',
        'start_date',
        'end_date',
        'start_time',
        'end_time',
        'status',
        'remark',
        'updater_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
    ];

    public function userApi(){
        return $this->belongsTo(UserApi::class, 'api_id', 'id');
    }

    public function admin(){
        return $this->belongsTo(Admin::class, 'updater_id', 'id');
    }

    public static function addSchedule(array $params = [])
    {
        $insertData = [
                        'start_date' => $params["start_date"] ?? null,
                        'end_date' => $params["end_date"] ?? null,
                        'start_time' => $params["start_time"] ?? null,
                        'end_time' => $params["end_time"] ?? null,
                        'status' => $params["status"] ?? null,
                        'remark' => $params["remark"] ?? null,
                    ];

        $apiAry = $params['api_id'];

        DB::transaction(function () use ($insertData,$apiAry) {
            foreach ($apiAry as $apiId) {
                $schedule = self::create(array_merge(['api_id'=>$apiId],$insertData));
                if (!$schedule) {
                    throw new \Exception(Lang::get('lang.schedule-create-fail'));
                }
            }
        });

        return lang::get('lang.successful');
    }

    public static function getScheduleList(array $params = []){
        $dateTime = date('Y-m-d H:i:s');
        $date = date('Y-m-d',strtotime($dateTime));
        $sortableColumns = ['created_at'];
        $status = $params['status'] ?? null;
        $apiId = $params['api_id'] ?? null;
        $startDate = $params['start_date'] ?? null;
        $endDate = $params['end_date'] ?? null;
        $createdFrom = $params['created_from'] ?? null;
        $createdTo = $params['created_to'] ?? null;
        $updatedFrom = $params['updated_from'] ?? null;
        $updatedTo = $params['updated_to'] ?? null;

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : 'null';
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        // see all
        $seeAll = $params['see_all'] ?? null;

        $report = self::query()->with(['userApi'])
                ->when(isset($status), function($q) use ($status){
                    return $q->where('status', $status);
                })
                ->when(isset($apiId), function($q) use ($apiId){
                    return $q->where('api_id', $apiId);
                })
                ->when($startDate, function($q) use ($startDate){
                    return $q->where('start_date', '>=', $startDate);
                })
                ->when($endDate, function($q) use ($endDate) {
                    return $q->where('end_date', '<=', $endDate);
                })
                ->when($createdFrom, function($q) use ($createdFrom){
                    return $q->whereDATE('created_at', '>=', $createdFrom);
                })
                ->when($createdTo, function($q) use ($createdTo) {
                    return $q->whereDATE('created_at', '<=', $createdTo);
                })
                ->when($updatedFrom, function($q) use ($updatedFrom){
                    return $q->whereDATE('updated_at', '>=', $updatedFrom);
                })
                ->when($updatedTo, function($q) use ($updatedTo) {
                    return $q->whereDATE('updated_at', '<=', $updatedTo);
                })
                ->orderBy($order_by, $order_sort)
                ->orderBy('id', $order_sort)
                ->when((!isset($seeAll) || $seeAll == 0),function ($q) use ($limit){
                    return $q->paginate($limit);
                });

        $mapFunc = function ($q){
            $status = array_search($q->status,self::$status) ?? null; 

            switch (MODULE) {
                case 'admin':
                    $res = [
                        'id' => $q->id,
                        'start_date' => Traits\DateTrait::dateFormat($q->start_date,false),
                        'end_date' => Traits\DateTrait::dateFormat($q->end_date,false),
                        'start_time' => $q->start_time,
                        'end_time' => $q->end_time,
                        'function' => $q->userApi->name,
                        'function_display' => Lang::has('lang.'.$q->userApi->name) ? Lang::get('lang.'.$q->userApi->name) : $q->userApi->name,
                        'status' => $status,
                        'status_display' => Lang::has('lang.'.$status) ? Lang::get('lang.'.$status) : $status,
                        'remark' => $q->remark,
                        'updated_by' => $q->admin->username ?? null,
                        'updated_at' => Traits\DateTrait::dateFormat($q->updated_at),
                    ];
                    break;
                
                case 'user':
                    $res = [
                    ];
                    break;
            }

            return $res;
        };

        if(($seeAll == 1)){
            return ['list' => $report->get()->map($mapFunc)->toArray()];
        }else{
            $report->getCollection()->transform($mapFunc);
            return (new ItemsCollection($report))->toArray();
        }
    }

    public static function editSchedule(array $params = [])
    {
        $schedule = self::find($params['id']);
        if (empty($schedule)) {
            throw new \Exception(Lang::get('lang.schedule-not-exist'));
        }

        $updateData = [
            'status' => array_key_exists("status",$params) ? $params["status"] : $schedule->status,
            'updater_id' => auth()->user()->id,
        ];

        DB::transaction(function () use ($updateData, $schedule) {
            $schedule->update($updateData);
        });

        if ($schedule->wasChanged()) {
            return Lang::get('lang.update-successfully');
        }

        return Lang::get('lang.no-changes-detected');
    }

    public static function checkSchedule(array $params = [])
    {   
        $dateTime = date('Y-m-d H:i:s');
        $date = date('Y-m-d',strtotime($dateTime));
        $apiDisplay = [];
        $maxIDAry = [];

        self::with(['userApi'])->selectRaw('MAX(id) AS maxID, ANY_VALUE(api_id) AS api_id')
                        ->whereRelation('userApi',function ($q) use ($params){
                            return $q->whereIn('name',$params);
                        })
                        ->where('status',self::$status['active'])
                        ->groupBy('api_id')
                        ->get()->map(function ($q) use (&$apiDisplay, &$maxIDAry){
                            $apiDisplay[$q->api_id] = Lang::has('lang.'.$q->userApi->name) ? Lang::get('lang.'.$q->userApi->name) : $q->userApi->name;
                            $maxIDAry[$q->maxID] = $q->maxID;
                        });

        if($maxIDAry){
            self::with(['userApi'])->whereIn('id',$maxIDAry)
                ->where('start_date','<=',$date)->where('end_date','>=',$date)
                ->where('start_time','<=',$dateTime)->where('end_time','>=',$dateTime)
                ->get()->map(function ($q) use ($apiDisplay){
                    $errorMsg = "Module ".$apiDisplay[$q->api_id]." close on ".$q->start_time." - ".$q->end_time;
                    abort(400,json_encode(["schedule" => $errorMsg]));
                });
        }

        return true;
    }
}
