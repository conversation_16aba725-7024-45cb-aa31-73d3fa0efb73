<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use App\Traits;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Arr;
use App\Traits\DateTrait;
use App\Traits\DecimalTrait;
use Illuminate\Database\Eloquent\SoftDeletes;
use <PERSON>bauman\Location\Facades\Location;

class CurrencyRateHourly extends Model
{
    use SoftDeletes;

    protected $table = 'currency_rate_hourly';

    protected $hidden = [
    ];

    protected $fillable = [
        'from_currency_id',
        'to_currency_id',
        'rate',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public function toCurrency()
    {
        return $this->belongsTo(Currencies::class, 'to_currency_id', 'id')->whereNull('deleted_at');
    }

    public function fromCurrency()
    {
        return $this->belongsTo(Currencies::class, 'from_currency_id', 'id')->whereNull('deleted_at');
    }

    public static function getMyrCurrencyRate($params = null)
    {   
        $userId = $params['user_id'] ?? null;

        $country = Country::pluck('iso_code2', 'currency_code');
        // special handle for some country;
        $country['ANG'] = 'CW'; // curacao
        $country['AUD'] = 'AU'; // australia
        $country['CHF'] = 'CH'; // switzerland
        $country['DKK'] = 'DK'; // denmark
        $country['EUR'] = 'ES'; // spain
        $country['FKP'] = 'FK'; // falkland-islands-malvinas
        $country['GBP'] = 'GB'; // united-kingdom
        $country['ILS'] = 'IL'; // israel
        $country['MAD'] = 'MA'; // morocco
        $country['NOK'] = 'NO'; // norway
        $country['NZD'] = 'NZ'; // new-zealand
        $country['USD'] = 'US'; // united-states
        $country['XAF'] = 'CF'; // central-african-republic
        $country['XCD'] = 'AG'; // antigua-and-barbuda
        $country['XOF'] = 'NE'; // niger
        $country['XPF'] = 'PF'; // french-polynesia

        $userFav =  null;
        if(isset($userId)){
            $userFav = UserFavourite::where('user_id',$userId)->where('type',UserFavourite::$type['currency'])->where('status',UserFavourite::$status['active'])->get()->pluck('favourite_id')->toArray() ?? [];
        }

        $maxIDAry = self::selectRaw('MAX(id) AS id')->groupBy('from_currency_id')->groupBy('to_currency_id')->get()->pluck('id')->toArray();
        $items = [];
        $lastUpdated = null;
        self::with(['toCurrency', 'fromCurrency'])
            ->whereIn('id', $maxIDAry)
            // ->whereRelation('toCurrency', 'disabled', 0)
            // ->whereRelation('fromCurrency', 'disabled', 0)
            ->get()->map(function ($q) use ($country, &$items, &$lastUpdated, $userFav) {
                if (!isset($lastUpdated) || $q->updated_at > $lastUpdated) $lastUpdated = $q->updated_at;

                if(in_array($q->toCurrency->id,($userFav ?? []))) $isFav = 1;
                
                $items[] = [
                    "to_id" => $q->toCurrency->id,
                    "from" => $q->fromCurrency->iso,
                    "to" => $q->toCurrency->iso,
                    "to_iso" => $country[$q->toCurrency->iso] ?? null,
                    "rate" => $q->rate,
                    "is_favourite" => (is_array($userFav)) ? ($isFav ?? 0) : null
                ];
            });

        return ['list' => $items, 'updated_at' => date('d.m.Y h:iA', strtotime($lastUpdated))];

    }

    public static function getCurrency($params = null) {

        $maxIDAry = self::selectRaw('MAX(id) AS id')->groupBy('from_currency_id')->groupBy('to_currency_id')->get()->pluck('id')->toArray();

        $currency = null;
        $country = null;
        $validCountry = 0;

        $country = Country::query()
            ->select(['id','name','country_code','iso_code2', 'currency_code'])
            ->with(['getCurrencies'])
            // ->whereIn('name',Config('general.valid_exchange_country'))
            // ->whereRelation('getCurrencies','disabled',0)
            ->orderBy('name', 'ASC')
            ->get()
            ->map(function ($q) {
                return [
                    'name' => $q->name,
                    'country_display' => Lang::has('lang.'.$q->name) ? Lang::get('lang.'.$q->name) : $q->name,
                    'iso_code' => $q->iso_code2,
                    'currency_code' => $q->currency_code,
                ];
            });

        $countryAry = $country->pluck('iso_code', 'currency_code');
        // special handle for some country;
        $countryAry['ANG'] = 'CW'; // curacao
        $countryAry['AUD'] = 'AU'; // australia
        $countryAry['CHF'] = 'CH'; // switzerland
        $countryAry['DKK'] = 'DK'; // denmark
        $countryAry['EUR'] = 'ES'; // spain
        $countryAry['FKP'] = 'FK'; // falkland-islands-malvinas
        $countryAry['GBP'] = 'GB'; // united-kingdom
        $countryAry['ILS'] = 'IL'; // israel
        $countryAry['MAD'] = 'MA'; // morocco
        $countryAry['NOK'] = 'NO'; // norway
        $countryAry['NZD'] = 'NZ'; // new-zealand
        $countryAry['USD'] = 'US'; // united-states
        $countryAry['XAF'] = 'CF'; // central-african-republic
        $countryAry['XCD'] = 'AG'; // antigua-and-barbuda
        $countryAry['XOF'] = 'NE'; // niger
        $countryAry['XPF'] = 'PF'; // french-polynesia

        if(!empty($maxIDAry)){
            $currency = self::with(['toCurrency','fromCurrency'])
                    ->whereIn('id',$maxIDAry)
                    // ->whereRelation('toCurrency','disabled',0)
                    // ->whereRelation('fromCurrency','disabled',0)
                    ->get()->map(function ($q) use ($countryAry){
                        return [
                            "from" => $q->fromCurrency->iso,
                            "from_iso" => $countryAry[$q->fromCurrency->iso] ?? null,
                            "to" => $q->toCurrency->iso,
                            "to_iso" => $countryAry[$q->toCurrency->iso] ?? null,
                            "rate" => $q->rate,
                        ];
                    });
        }

        if(isset($params['ip_address']) && (filter_var($params['ip_address'], FILTER_VALIDATE_IP, FILTER_FLAG_IPV4))){
            $location = Location::get($params['ip_address']);
            if(!empty($location) && ($location->countryCode == 'MY')){
                $validCountry = 1;
            }
        }

        // Open for All Countries
        $validCountry = 1;

        return [
            "currency" => $currency,
            "country" => $country,
            "validCountry" => $validCountry
        ];
    }
}