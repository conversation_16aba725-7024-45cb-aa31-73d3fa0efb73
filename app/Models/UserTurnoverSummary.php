<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserTurnoverSummary extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'product_id',
        'bet_count',
        'turnover',
        'return',
        'win_loss',
        'transaction_at',
        'status',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public static function getTotalTurnoverByUserIds($userIds)
    {
        $total = self::whereIn('user_id', $userIds)
            ->sum('turnover');

        return $total;
    }

    public static function getSummariesGroupByUserId()
    {
        $summaries = self::groupBy('user_id')
            ->select('user_id')
            ->selectRaw('SUM(turnover) as total_turnover')
            ->get();

        return $summaries;
    }
}
