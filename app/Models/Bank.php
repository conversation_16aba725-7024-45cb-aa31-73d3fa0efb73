<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Traits;

class Bank extends Model
{
    protected $table = 'bank';

    protected $hidden = [];

    protected $fillable = [
        'country_id',
        'name',
        'translation_code',
        'ref_bank_id',
        'ref_onepay_wd_bank_id',
        'status',
        'transfer_status',
        'priority',
        'updater_id'
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
    ];

    public static $transferStatus = [
        'inactive' => 0,
        'active' => 1,
    ];

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    public function admin()
    {
        return $this->belongsTo(Admin::class, 'updater_id', 'id');
    }

    public static function addBank(array $params = [])
    {
        $currency = Currency::with(['country' => function ($q) {
            return $q->whereIn('name', config('general.valid_exchange_country'));
        }])->find($params['currency_id']);

        //Language
        foreach (config('language') as $lang => $value) {
            $langKey = array_search($lang, array_column($params['language'], 'type'));
            if (($langKey === false)) $langKey = array_search('en', array_column($params['language'], 'type'));
            $insertLanguage[$lang] = $params['language'][$langKey]['name'];
        }

        $insertData = [
            'country_id' => $currency->country->id,
            'name' => $params['name'] ?? null,
            'translation_code' => $insertLanguage["slug"] ?? null,
            'status' => $params['status'],
            'transfer_status' => $params['transfer_status'],
            'priority' => self::max('priority') + 1,
            'updater_id' => Auth::user()->id ?? null,
        ];

        DB::transaction(function () use ($insertData, $insertLanguage, $params) {
            $bank = self::create($insertData);
            if (!$bank) {
                throw new \Exception(Lang::get('lang.bank-create-fail'));
            }

            $bank->update(["translation_code" => $params["translation_code"] . "-" . $bank->id]);

            $insertLanguage['slug'] = $params["translation_code"] . "-" . $bank->id ?? null;
            $lang = LangTable::create($insertLanguage);
            if (!$lang) {
                throw new \Exception(Lang::get('lang.language-create-fail'));
            }

            Traits\CacheTrait::clearLangCache('lang-');
        });

        return true;
    }

    public static function getList(array $params = [])
    {
        $name = $params['name'] ?? null;
        $currency = $params['currency'] ?? null;
        $status = $params['status'] ?? null;
        $transferStatus = $params['transfer_status'] ?? null;
        $seeAll = $params['see_all'] ?? null;

        $sortableColumns = ['name', 'priority'];

        // Order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'priority';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'priority';

        // Sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'asc';

        // limit
        $limit = ($seeAll == 1) ? null : ($params['limit'] ?? config('app.pagination_rows'));

        if (isset($params['export']) && ($params['export'] == 1)) {
            $params["model"] = get_class() ?? null;
            $params["function"] = __FUNCTION__;
            ExportReport::exportReport($params);
        }

        $items = self::query()
            ->with(['country', 'admin'])
            ->select(['id', 'country_id', 'name', 'translation_code', 'status', 'transfer_status', 'priority', 'updater_id', 'updated_at'])
            ->when(isset($name), function ($q) use ($name) {
                return $q->where('name', 'LIKE', '%' . $name . '%');
            })
            ->when(isset($currency), function ($q) use ($currency) {
                return $q->whereRelation('country', 'currency_code', $currency);
            })
            ->when(isset($status), function ($q) use ($status) {
                return $q->where('status', $status);
            })
            ->when(isset($transferStatus), function ($q) use ($transferStatus) {
                return $q->where('transfer_status', $transferStatus);
            })
            ->orderBy($order_by, $order_sort)
            ->when((!isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $mapFunc = function ($q) {
            $status = array_search($q->status, self::$status) ?? null;
            $transferStatus = array_search($q->transfer_status, self::$transferStatus) ?? null;

            return [
                'id' => $q->id,
                'country_id' => $q->country_id,
                'currency_iso' => $q->country->currency_code,
                'name' => $q->name,
                'bank_display' => Lang::has('lang.' . $q->translation_code) ? Lang::get('lang.' . $q->translation_code) : $q->name,
                'status' => $status,
                'status_display' =>  Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                'transfer_status' => $transferStatus,
                'transfer_status_display' =>  Lang::has('lang.' . $transferStatus) ? Lang::get('lang.' . $transferStatus) : $transferStatus,
                'updated_by' => $q->admin->username ?? null,
                'updated_at' => isset($q->updated_at) ? Traits\DateTrait::dateFormat($q->updated_at) : null
            ];
        };

        if (($seeAll == 1)) {
            return ['list' => $items->get()->map($mapFunc)->toArray()];
        } else {
            $items->getCollection()->transform($mapFunc);
            return (new ItemsCollection($items))->toArray();
        }
    }

    public static function getBankDetail(array $params = [])
    {
        $data = self::with(['country', 'country.getCurrency'])
            ->where('id', $params['id'])->get()->map(function ($q) {
                $langAry = [];
                foreach (config('language') as $lang => $row) {
                    $langDisplay = Lang::has('lang.' . $q->translation_code) ? Lang::get('lang.' . $q->translation_code, [], $lang) : null;
                    if (empty($langDisplay)) continue;
                    $temp['type'] = $lang;
                    $temp['name'] = $langDisplay;
                    $langAry[] = $temp;
                }

                $status = array_search($q->status,  self::$status);
                $transferStatus = array_search($q->transfer_status,  self::$transferStatus);
                $res = [
                    'id' => $q->id,
                    'language' => $langAry ?? [],
                    'currency_id' => $q->country->getCurrency->id,
                    'currency' => $q->country->currency_code,
                    'status' => $status,
                    'status_display' => Lang::has('lang.' . $status) ? Lang::get('lang.' . $status) : $status,
                    'transfer_status' => $transferStatus,
                    'transfer_status_display' => Lang::has('lang.' . $transferStatus) ? Lang::get('lang.' . $transferStatus) : $transferStatus
                ];
                return $res;
            })->first();

        if (empty($data)) {
            throw new \Exception(Lang::get('lang.service-not-exist'));
        }

        return $data;
    }

    public static function editBank(array $params = [])
    {
        $bank = self::find($params['id']);
        if (empty($bank)) {
            throw new \Exception(Lang::get('lang.bank-not-exist'));
        }

        $langID = LangTable::where('slug', $bank->translation_code)->first()['id'];
        if (empty($langID)) {
            throw new \Exception(Lang::get('lang.lang-not-exist'));
        }

        if (!empty($params['currency_id'])) {
            $currency = Currency::with(['country' => function ($q) {
                return $q->whereIn('name', config('general.valid_exchange_country'));
            }])->find($params['currency_id']);
        }

        $lang = LangTable::find($langID);

        //Language
        $updateLang = [
            'slug' => isset($params["translation_code"]) ? ($params["translation_code"] . "-" . $bank->id) : $lang->slug,
        ];
        $languageAry = [];
        if (isset($params['language'])) $languageAry = $params['language'];
        foreach ($languageAry as $languageData) {
            $updateLang[$languageData['type']] = $languageData['name'];
        }

        $updateData = [
            'country_id' => array_key_exists("currency_id", $params) ? $currency->country->id : $bank->country_id,
            'name' => array_key_exists("name", $params) ? $params["name"] : $bank->name,
            'translation_code' => array_key_exists("translation_code", $params) ? $params["translation_code"] . "-" . $bank->id : $bank->translation_code,
            'status' => array_key_exists("status", $params) ? $params["status"] : $bank->status,
            'transfer_status' => array_key_exists("transfer_status", $params) ? $params["transfer_status"] : $bank->transfer_status,
        ];

        DB::transaction(function () use ($updateData, $updateLang, $bank, $lang) {
            $bank->update($updateData);
            $lang->update($updateLang);
        });

        if ($lang->wasChanged() || $bank->wasChanged()) {
            Traits\CacheTrait::clearLangCache('lang-');
            $bank->update(['updater_id' => auth()->user()->id]);
            return Lang::get('lang.update-successfully');
        }

        return Lang::get('lang.no-changes-detected');
    }
}
