<?php

namespace App\Models;

use App\Http\Resources\ItemsCollection;
use App\Models;
use App\Models\Deposit;
use App\Models\Providers\MtUser;
use App\Services;
use App\Traits;
use App\Traits\GenerateNumberTrait;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Lang;
use Laravel\Sanctum\HasApiTokens;
use Tymon\JWTAuth\Contracts\JWTSubject;

class User extends Authenticatable implements JWTSubject
{
    use GenerateNumberTrait;
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $table = 'users';

    protected $fillable = [
        'uuid',
        'member_id',
        'referral_code',
        'username',
        'name',
        'password',
        'ic_no',
        'email',
        'phone_no',
        'sponsor_id',
        'country_id',
        'currency_id',
        'gender_id',
        'race_id',
        'dob',
        'lang',
        'party',
        'deeplink',
        'store_id',
        'user_type',
        'activated',
        'disabled',
        'suspended',
        'restricted',
        'fail_login',
        'last_active_at',
        'created_at',
        'updated_at',
        'deleted_at',
        'is_dummy',
        'is_agent',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'email_verify_code',
        'email_verified_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'activated' => 'boolean',
        'disabled' => 'boolean',
        'suspended' => 'boolean',
        'is_dummy' => 'boolean',
    ];

    public static $userType = [
        'internal-account' => 0,
        'user-account' => 1,
        'shop-account' => 2,
    ];

    public static $activatedDisplay = [
        'inactive' => 0,
        'active' => 1,
    ];

    public static $memberStatus = [
        'suspended' => ['suspended' => 1],
        'disabled' => ['disabled' => 1],
        'inactive' => ['suspended' => 0, 'disabled' => 0, 'activated' => 0],
        'active' => ['suspended' => 0, 'disabled' => 0, 'activated' => 1],
    ];

    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [];
    }

    protected $with = ['balance', 'store'];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->uuid = str()->random(12);
        });
    }

    /* -------------------------------------------------------------------------- */
    /*                                   Scopes */
    /* -------------------------------------------------------------------------- */
    public function scopeInternalUser($query)
    {
        return $query->where('user_type', self::$userType['internal-account']);
    }

    public function scopeUser($query)
    {
        return $query->where('user_type', self::$userType['user-account']);
    }

    public function scopeShop($query)
    {
        return $query->where('user_type', self::$userType['shop-account']);
    }

    public function scopeActive($query)
    {
        return $query->where('activated', true);
    }

    /* -------------------------------------------------------------------------- */
    /*                                Relationships */
    /* -------------------------------------------------------------------------- */
    public function apiKeys()
    {
        return $this->hasMany(ApiKey::class);
    }

    public function balance()
    {
        return $this->hasMany(AccountBalance::class);
    }

    public function userDetail()
    {
        return $this->hasMany(UserDetail::class);
    }

    public function userAngpau()
    {
        return $this->hasMany(UserAngpau::class);
    }

    public function userSales()
    {
        return $this->belongsTo(UserSales::class, 'id', 'user_id');
    }

    public function treeSponsor()
    {
        return $this->belongsTo(TreeSponsor::class, 'id', 'user_id');
    }

    public function treeSponsorCache()
    {
        return $this->belongsTo(TreeSponsorCache::class, 'id', 'user_id');
    }

    public function sponsor()
    {
        return $this->belongsTo(User::class, 'sponsor_id', 'id');
    }

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_id', 'id');
    }

    public function userFavourite()
    {
        return $this->hasMany(UserFavourite::class, 'favourite_id', 'id');
    }

    public function user_level()
    {
        return $this->hasOne(UserLevel::class, 'user_id', 'id');
    }

    public function balanceLastUpdate()
    {
        return $this->hasOne(CreditTransaction::class, 'user_id', 'id')->latest();
    }

    public function userTelexTransfer()
    {
        return $this->hasMany(TelexTransfer::class);
    }

    public function directDownlineSales()
    {
        return $this->hasMany(UserSales::class, 'sponsor_id', 'id');
    }

    public function nextRankSetting()
    {
        return $this->hasMany(RankSetting::class, 'rank_id', 'next_rank_id');
    }

    public function deviceToken()
    {
        return $this->belongsTo(UserDevice::class, 'id', 'user_id')
            ->where('status', UserDevice::$status['active'])
            ->where('updated_at', '>=', date('Y-m-d H:i:s', strtotime('-'.((env('USER_JWT_TTL') * 60) ?? 0).' seconds')));
    }

    public function device()
    {
        return $this->hasOne(UserDevice::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class, 'store_id', 'store_id');
    }

    public function userBank()
    {
        return $this->hasMany(UserBank::class, 'user_id', 'id');
    }

    public function allAccount()
    {
        return $this->hasMany(User::class, 'phone_no', 'phone_no');
    }

    public function mtUser()
    {
        return $this->hasOne(MtUser::class);
    }

    public function deposits()
    {
        return $this->hasMany(Deposit::class);
    }

    public function lastApprovedDeposit()
    {
        return $this->hasOne(Deposit::class)
            ->where('status', Deposit::$status['approved'])
            ->orderBy('approved_at', 'desc');
    }

    public function userCardLogs()
    {
        return $this->hasMany(UserCardLog::class);
    }

    public function userLevelTransactions()
    {
        return $this->hasMany(UserLevelTransaction::class);
    }

    public static function getProfile($userId, $login = false)
    {
        $dateTime = date('Y-m-d H:i:s');
        $date = date('Y-m-d', strtotime($dateTime));

        $user = self::with([
            'userDetail',
            'country',
            'sponsor',
            'deviceToken',
            'store' => function ($q) {
                $q->select('id', 'store_id', 'name');
            },
        ])->find($userId);

        if (empty($user)) {
            abort(400, Lang::get('lang.user-not-exist'));
        }

        $kyc = Kyc::where('user_id', $userId)->whereNOT('status', Kyc::$kycStatus['declined'])->first();

        if ($user->suspended == 1) {
            $status = 'suspended';
            $statusDisplay = Lang::get('lang.suspended');
        } elseif ($user->disabled == 1) {
            $status = 'disabled';
            $statusDisplay = Lang::get('lang.disabled');
        } elseif ($user->activated == 0) {
            $status = 'inactive';
            $statusDisplay = Lang::get('lang.inactive');
        } else {
            $status = 'active';
            $statusDisplay = Lang::get('lang.active');
        }

        $userType = array_search($user->user_type, self::$userType) ?? null;

        if (! empty($userType)) {
            $userTypeDisplay = (Lang::has('lang.'.$userType) ? Lang::get('lang.'.$userType) : $userType);
        }

        $userDetail = isset($user->userDetail) ? $user->userDetail->keyBy('name')->toArray() : null;

        $profilePic = isset($userDetail['profile_picture']) ? getenv('AWS_S3_BUCKET_NAME_PUBLIC_DOMAIN').$userDetail['profile_picture']['value'] : null;

        $gender = $user->gender_id ? (config('users.gender')[$user->gender_id]) : null;
        $race = $user->race_id ? (config('users.race')[$user->race_id]) : null;
        $country = $user->country->name ?? null;

        $data = [
            'id' => $user->id,
            'member_id' => $user->member_id,
            'name' => $user->name,
            'username' => $user->username,
            'ic_no' => $user->ic_no,
            'dob' => isset($user->dob) ? $user->dob : null,
            'gender' => isset($gender) ? Lang::get('lang.'.$gender) : null,
            'gender_id' => $user->gender_id ?? null,
            'race' => isset($race) ? Lang::get('lang.'.$race) : null,
            'race_id' => $user->race_id ?? null,
            'email' => $user->email,
            'phone_no' => $user->phone_no,
            'user_type' => $userType,
            'user_type_display' => $userTypeDisplay ?? null,
            'country' => $country,
            'country_display' => Lang::has('lang.'.$country) ? Lang::get('lang.'.$country) : $country,
            'referral_member_id' => $user->sponsor->member_id ?? null,
            'referral_name' => $user->sponsor->name ?? null,
            'referral_phone_no' => $user->sponsor->phone_no ?? null,
            'status' => $status,
            'status_display' => $statusDisplay ?? $status,
            'created_at' => Traits\DateTrait::dateFormat($user->created_at),
            'activated_at' => Traits\DateTrait::dateFormat($user->email_verified_at),
            'currency_code' => (! isset($user->country->name) || in_array($user->country->name, config('users')['default_country_currency'])) ? config('users')['default_currency'] : $user->country->currency_code,
            'profile_picture' => $profilePic,
            'profile_picture_path' => isset($userDetail['profile_picture']) ? $userDetail['profile_picture']['value'] : null,
            'd_token' => $user->deviceToken->token ?? null,
            'store' => [
                'id' => $user->store->id ?? null,
                'store_id' => $user->store->store_id ?? null,
                'store_name' => $user->store->name ?? null,
                'is_wallet_transferable' => $user->store->is_wallet_transferable ?? 0,
            ],
        ];

        if (! $login) {
            $data['kyc_type'] = null;
            $data['kyc_type_display'] = null;
            $data['image_1'] = null;
            $data['image_1_path'] = null;
            $data['image_2'] = null;
            $data['image_2_path'] = null;
            if (isset($kyc) && ! empty($kyc)) {
                $data['kyc_type'] = array_search($kyc->type, Kyc::$kycType) ?? null;
                $data['kyc_type_display'] = Lang::has('lang.'.$data['kyc_type']) ? Lang::get('lang.'.$data['kyc_type']) : ucfirst($data['kyc_type']);
                $image = $kycImg = [];
                if (isset($kyc->image_1)) {
                    array_push($image, $kyc->image_1);
                }
                if (isset($kyc->image_2)) {
                    array_push($image, $kyc->image_2);
                }
                if (! empty($image)) {
                    $s3Client = Traits\S3Trait::getS3Client();
                    $kycImg = Traits\S3Trait::awsDownload($s3Client, $image);
                }
                $data['image_1'] = $kycImg[$kyc->image_1] ?? null;
                $data['image_1_path'] = $kyc->image_1 ?? null;
                $data['image_2'] = $kycImg[$kyc->image_2] ?? null;
                $data['image_2_path'] = $kyc->image_2 ?? null;
            }

            $kycStatus = isset($kyc->status) ? array_search($kyc->status, Kyc::$kycStatus) : 'declined';
            $kycStatusDisplay = Lang::has('lang.kyc-app-'.$kycStatus) ? Lang::get('lang.kyc-app-'.$kycStatus) : $kycStatus;
            $data['kyc_status'] = $kycStatus;
            $data['kyc_status_display'] = $kycStatusDisplay;
            $data['updated_at'] = Traits\DateTrait::dateFormat($user->updated_at);
        }

        $data['settings'] = [
            'term' => self::termsDetail(),
        ];

        return $data;
    }

    public static function getList(array $params = [])
    {
        $status = $params['status'] ?? null;
        $name = $params['name'] ?? null;
        $username = $params['username'] ?? null;
        $account = $params['account'] ?? null;
        $memberId = $params['member_id'] ?? null;
        $phoneNo = $params['phone_no'] ?? null;
        $fromDate = $params['from_date'] ?? null;
        $toDate = $params['to_date'] ?? null;
        $country_id = $params['country'] ?? null;
        $leaderUsername = $params['leader_username'] ?? null;
        $leaderPhone = $params['leader_phone'] ?? null;
        $leaderMemberId = $params['leader_member_id'] ?? null;
        $agentRankId = $params['agent_rank_id'] ?? null;
        $sponsorUsername = $params['sponsor_username'] ?? null;
        $sponsorPhone = $params['sponsor_phone'] ?? null;
        $sponsorMemberId = $params['sponsor_member_id'] ?? null;
        $storeId = $params['store_id'] ?? null;
        $lastUpdateFromDate = $params['last_update_from_date'] ?? null;
        $lastUpdateToDate = $params['last_update_to_date'] ?? null;
        $lastActiveFromDate = $params['last_active_from_date'] ?? null;
        $lastActiveToDate = $params['last_active_to_date'] ?? null;
        $fromDate = $params['from_date'] ?? null;
        $toDate = $params['to_date'] ?? null;
        $productName = $params['platform'] ?? null;
        $exStoreId = $params['extra_store_id'] ?? null;
        $seeAll = $params['see_all'] ?? null;
        $dateTime = date('Y-m-d H:i:s');
        $date = date('Y-m-d', strtotime($dateTime));
        $sortableColumns = ['name', 'email', 'status'];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = ($seeAll == 1) ? null : ($params['limit'] ?? config('app.pagination_rows'));

        if (isset($params['export']) && ($params['export'] == 1)) {
            $params['model'] = get_class() ?? null;
            $params['function'] = __FUNCTION__;
            ExportReport::exportReport($params);
        }

        // leaderusername flter
        $leaderDownlines = [];
        if (! empty($leaderUsername) || ! empty($leaderPhone) || ! empty($leaderMemberId)) {
            $leaderUser = User::when($leaderUsername, function ($query) use ($leaderUsername) {
                $query->where('username', $leaderUsername);
            })
                ->when($leaderMemberId, function ($query) use ($leaderMemberId) {
                    $query->where('member_id', $leaderMemberId);
                })
                ->when($leaderPhone, function ($query) use ($leaderPhone) {
                    $query->where('phone_no', $leaderPhone);
                })->first();
            if (isset($leaderUser) && ! empty($leaderUser)) {
                $leaderTree = $leaderUser->treeSponsor;
                $donwlines = TreeSponsor::where('trace_key', 'LIKE', $leaderTree->trace_key.'%')->pluck('user_id', 'id');
                if (! empty($donwlines)) {
                    $leaderDownlines = $donwlines->toArray();
                }
            }
        }

        $items = self::query()
            ->select(['id', 'username', 'uuid', 'member_id', 'name', 'phone_no', 'email', 'sponsor_id', 'ic_no', 'country_id', 'store_id', 'activated', 'disabled', 'suspended', 'created_at', 'last_active_at', 'updated_at', 'deleted_at', 'party'])
            ->with(['country', 'sponsor', 'store', 'lastApprovedDeposit'])
            ->whereNotIn('user_type', [self::$userType['internal-account']])
            ->where('is_dummy', false)
            ->when($name, function ($query) use ($name) {
                $query->where('name', 'like', "%$name%");
            })
            ->when($username, function ($query) use ($username) {
                $query->where('username', 'like', "%$username%");
            })
            ->when($account, function ($query) use ($account) {
                $query->where('uuid', 'like', "%$account%");
            })
            ->when($memberId, function ($query) use ($memberId) {
                $query->where('member_id', $memberId);
            })
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('store', 'id', $storeId);
            })
            ->when(isset($phoneNo), function ($query) use ($phoneNo) {
                $phoneNo = preg_replace('/[^0-9]/', '', $phoneNo);
                $query->where('phone_no', 'like', "%$phoneNo%");
            })
            ->when((isset($fromDate) && isset($toDate)), function ($q) use ($fromDate, $toDate) {
                $q->where(DB::raw('DATE(created_at)'), '>=', $fromDate);

                return $q->where(DB::raw('DATE(created_at)'), '<=', $toDate);
            })
            ->when((isset($lastUpdateFromDate) && isset($lastUpdateToDate)), function ($q) use ($lastUpdateFromDate, $lastUpdateToDate) {
                $q->where(DB::raw('DATE(updated_at)'), '>=', $lastUpdateFromDate);

                return $q->where(DB::raw('DATE(updated_at)'), '<=', $lastUpdateToDate);
            })
            ->when(isset($country_id), function ($query) use ($country_id) {
                $query->where('country_id', $country_id);
            })
            ->when(! empty($leaderUsername) || ! empty($leaderPhone) || ! empty($leaderMemberId), function ($query) use ($leaderDownlines) {
                $query->whereIn('id', $leaderDownlines);
            })
            ->when(isset($sponsorUsername), function ($query) use ($sponsorUsername) {
                $query->whereRelation('sponsor', 'username', 'LIKE', '%'.$sponsorUsername.'%');
            })
            ->when(isset($sponsorPhone), function ($query) use ($sponsorPhone) {
                $query->whereRelation('sponsor', 'phone_no', $sponsorPhone);
            })
            ->when(isset($sponsorMemberId), function ($query) use ($sponsorMemberId) {
                $query->whereRelation('sponsor', 'member_id', $sponsorMemberId);
            })
            ->when(isset($productName), function ($query) use ($productName) {
                if ($productName == 'FW') {
                    $query->whereNull('party');
                } else {
                    $query->where('party', $productName);
                }
            })
            ->when(isset($exStoreId), function ($q) use ($exStoreId) {
                return $q->whereHas('store', function ($q) use ($exStoreId) {
                    $q->whereIn('id', $exStoreId);
                });
            })
            ->when(isset($status), function ($query) use ($status) {
                $query->where(self::$memberStatus[$status]);
            })
            ->orderBy($order_by, $order_sort)
            ->when((! isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $userIds = [];
        $mapFunc = function ($q) use (&$userIds) {
            unset($status, $statusDisplay);
            if ($q->suspended == 1) {
                $status = 'suspended';
            } elseif ($q->disabled == 1) {
                $status = 'disabled';
            } else {
                $status = array_search(['suspended' => $q->suspended, 'disabled' => $q->disabled, 'activated' => $q->activated], self::$memberStatus);
            }

            // Deleted Account Status
            if (! is_null($q->deleted_at)) {
                $status = 'deleted';
            }
            $statusDisplay = Lang::get('lang.'.$status);

            if (isset($q->id)) {
                $userIds[$q->id] = $q->id;
            }
            if (isset($q->sponsor->id)) {
                $userIds[$q->sponsor->id] = $q->sponsor->id;
            }

            $county = $q->country->name;
            $res = [
                'id' => $q->id,
                'member_id' => $q->member_id,
                'name' => $q->name,
                'username' => $q->username,
                'account' => $q->uuid,
                'phone_no' => $q->phone_no ?? null,
                'country_display' => Lang::has('lang.'.$county) ? Lang::get('lang.'.$county) : $county,
                'referral_user_id' => $q->sponsor->id ?? null,
                'referral_id' => $q->sponsor->member_id ?? null,
                'referral_name' => $q->sponsor->name ?? null,
                'referral_phone_no' => $q->sponsor->phone_no ?? null,
                'platform' => $q->party ?? 'FW',
                'status' => $status,
                'status_display' => $statusDisplay,
                'last_active_at' => Traits\DateTrait::dateFormat($q->last_active_at),
                'last_deposit_at' => Traits\DateTrait::dateFormat($q->lastApprovedDeposit->approved_at ?? null),
                'created_at' => Traits\DateTrait::dateFormat($q->created_at),
                'updated_at' => Traits\DateTrait::dateFormat($q->updated_at),
                'store' => $q->store->name ?? null,
            ];

            return (object) $res;
        };

        if (($seeAll == 1)) {
            $listing = $items->get()->map($mapFunc)->toArray();

            return ['list' => $listing];
        } else {
            $items->getCollection()->transform($mapFunc);
            $data = new ItemsCollection($items);

            return $data;
        }
    }

    public static function addUser(array $params = [], $useDefaultReferral = false)
    {
        $isAuth = Auth::check() ?? false;
        $isDummy = isset($params['is_dummy']) ? $params['is_dummy'] : false;

        // Restricted User
        $restrictedPhoneNumbers = [1131851121, 1123392630, 1416451073, 1135120801, 1172689127];
        if (in_array(substr($params['phone_no'], -10), $restrictedPhoneNumbers)) {
            abort(400, Lang::get('lang.restricted-user-error'));
        }

        // Checking for sponsor id if not exists
        if ($useDefaultReferral) {
            // Get Default Referral
            $defaultReferral = SystemSetting::where('name', 'defaultReferralUsername')->pluck('value', 'name')->toArray() ?? null;

            if (! isset($defaultReferral['defaultReferralUsername']) || empty($defaultReferral['defaultReferralUsername'])) {
                abort(400, Lang::get('lang.invalid-referral-username-error'));
            }

            $sponsorId = self::where('username', $defaultReferral['defaultReferralUsername'])->first()->id ?? null;
            $commPerc = 0;
        } elseif (isset($params['store_id'])) {
            if (isset($params['referral_code'])) {
                if (preg_match('([a-zA-Z].*[0-9]|[0-9].*[a-zA-Z])', $params['referral_code'])) {
                    $userRec = User::where('referral_code', $params['referral_code'])->first();
                    $sponsorId = $userRec?->id ?? null;
                    $commPerc = 0;
                } else {
                    $phoneNumber = substr($params['referral_code'], -9);
                    $userRec = User::where('phone_no', 'LIKE', "%{$phoneNumber}%")->first();
                    $sponsorId = $userRec?->id ?? null;
                    $commPerc = 0;
                }
            } else {
                $storeRes = Store::where('store_id', $params['store_id'])->first();
                $sponsorId = $storeRes?->user_id;
                $commPerc = 0;
            }
        } else {
            // Get Default Referral
            $defaultReferral = SystemSetting::where('name', 'defaultReferralUsername')->pluck('value', 'name')->toArray() ?? null;

            if (! isset($defaultReferral['defaultReferralUsername']) || empty($defaultReferral['defaultReferralUsername'])) {
                abort(400, Lang::get('lang.invalid-referral-username-error'));
            }

            $sponsorId = self::where('username', $defaultReferral['defaultReferralUsername'])->first()->id ?? null;
            $commPerc = 0;
        }
        if (! $sponsorId) {
            abort(400, json_encode(Lang::get('lang.invalid-referral-username-error')));
        }

        $memberID = Traits\GenerateNumberTrait::GenerateMemberID(self::query());

        // Get Fix Phone No
        $v2AuthorizedUser = json_decode((SystemSetting::where('name', 'v2AuthorizedUser')->first()->value ?? null), true) ?? [];

        if (! isset($params['store_id'])) {
            abort(400, Lang::get('lang.store-not-exist'));
        }

        $insertUser = [
            'member_id' => $memberID,
            // "username" => str_replace("-","",$params['phone_no']),
            'username' => $params['name'],
            'name' => $params['name'],
            'password' => Hash::make($params['password']),
            'phone_no' => $params['phone_no'],
            'country_id' => $params['country_id'] ?? null,
            'sponsor_id' => $sponsorId,
            'lang' => $params['preferred_language'],
            'store_id' => $params['store_id'] ?? null,
            'activated' => 1,
            'is_dummy' => $isDummy,
            'referral_code' => GenerateNumberTrait::generateReferralCode(self::query(), 'referral_code'),
        ];

        return DB::transaction(function () use ($insertUser, $params, $sponsorId, $v2AuthorizedUser) {
            $checkUser = User::where('phone_no', $params['phone_no'])->first();
            if (isset($checkUser) && in_array($checkUser->username, $v2AuthorizedUser)) {
                if (isset($checkUser)) {
                    $checkUser->update(['name' => $insertUser['name'], 'activated' => 1, 'disabled' => 0, 'suspended' => 0, 'fail_login' => 0, 'deleted_at' => null]);
                }
                if (MODULE == 'app') {
                    $loginData = Services\AuthService::login(['phone_no' => $checkUser->phone_no, 'password' => $params['password']], 'users');

                    return $loginData;
                }

                return true;
            }

            // Insert User
            $user = self::create($insertUser);
            if (! $user) {
                abort(400, Lang::get('lang.user-create-error'));
            }

            $insertSponsorTree = Traits\TreeTrait::insertSponsorTree($user->id, $sponsorId);
            if (! $insertSponsorTree) {
                abort(400, Lang::get('lang.user-create-error'));
            }

            // Insert Sales
            $sales = [
                'user_id' => $user->id,
                'sponsor_id' => $sponsorId,
                'module_type' => 'register',
            ];
            $updateSales = UserSales::updateSales($sales, 'package');
            if (! $updateSales) {
                abort(400, Lang::get('lang.user-create-error'));
            }

            // Send SMS if above all success
            /*
                $smsParams = [
                    'phone' => $params['phone_no'] ?? null,
                    'lang' => 'en',
                    'type' => 'register-success',
                    'user_id' => $user->id ?? null,
                ];
                $smsParams = json_encode($smsParams);
                $job = new \App\Jobs\SendSMS($smsParams);
                dispatch($job);
            */

            $referralOcMemberId = UserProduct::where('user_id', $sponsorId)->first()->member_id ?? null;
            $phoneAry = explode('-', $params['phone_no']);
            $ocAccountNo = str_replace('6', '', $phoneAry[0]).$phoneAry[1];
            if ($ocAccountNo[0] != 0) {
                $ocAccountNo = '0'.$ocAccountNo;
            }

            // if (env('APP_ENV') != 'production'){
            //     $ocAccountNo = $ocAccountNo.$user->member_id;
            // }
            // UserProduct::subscribe([
            //     'user_id' => $user->id,
            //     // "account" => $ocAccountNo,
            //     'account' => $user->uuid,
            //     'referral' => $referralOcMemberId,
            //     // "password" => $user->id . $insertUser['member_id'],
            //     'password' => $user->uuid,
            //     'dial_code' => $params['dial_code'],
            // ]);

            if (MODULE == 'app') {
                // $checkParams = [
                //     'user_id' => $params['user_id'] ?? null,
                //     'uuid' => $params['uuid'] ?? null,
                // ];
                // // $valid = Models\UserDeviceInfo::checkValidDeviceToLogin($checkParams);
                // $valid = true;
                // if ($valid) {
                //     $loginData = Services\AuthService::login(['phone_no' => $user->phone_no, 'password' => $params['password']], 'users');
                //     $id = $loginData['data']['user']['id'] ?? null;
                //     if (isset($id)) {
                //         $params['user_id'] = $loginData['data']['user']['id'] ?? null;
                //         UserDeviceInfo::insertUserDInfo($params);
                //     }
                //
                //     $loginData['access_token'] = $loginData['data']['access_token'] ?? null;
                //
                //     return $loginData;
                // }
            }

            return true;
        });
    }

    public static function verifyUserEmail(array $params = [])
    {
        $user = self::query()
            ->where(DB::raw('md5(concat(id,email_verify_code))'), $params['code'])
            ->whereNull('email_verified_at')
            ->first();

        if (empty($user)) {
            $userDetailRow = UserDetail::where('name', 'change-email')->where(DB::raw('md5(concat(user_id,reference))'), $params['code'])->where('type', 'pending')->first();
            if (empty($userDetailRow)) {
                $returnData['message'] = Lang::get('lang.invalid-verify-email-link');
                $returnData['data'] = ['isActivated' => 1];
                abort(400, json_encode($returnData));
            }

            $userId = $userDetailRow->user_id;
            $user = self::find($userId);

            DB::transaction(function () use ($user, $userDetailRow) {
                request()->merge(['act-log' => 'user-verify-email']);
                $user->email = $userDetailRow->value;
                $user->email_verify_code = $userDetailRow->reference;
                $user->email_verified_at = date('Y-m-d H:i:s');
                $user->activated = 1;
                $user->save();

                $userDetailRow->type = 'completed';
                $userDetailRow->save();
            });
        } else {
            DB::transaction(function () use ($user) {
                request()->merge(['act-log' => 'user-verify-email']);
                $user->email_verified_at = date('Y-m-d H:i:s');
                $user->activated = 1;
                $user->save();
            });
        }

        return true;
    }

    public static function updateProfile(array $params, int $userId)
    {
        $user = self::find($userId);

        if (empty($user)) {
            abort(400, Lang::get('lang.user-not-exist'));
        }

        if (MODULE == 'admin') {
            $status = $params['status'] ?? null;
            switch ($status) {
                case 'active':
                    $params['activated'] = 1;
                    $params['disabled'] = 0;
                    $params['suspended'] = 0;
                    $params['fail_login'] = 0;
                    break;

                case 'suspended':
                    $params['suspended'] = 1;
                    break;

                case 'disabled':
                    $params['disabled'] = 1;
                    break;

                default:
                    break;
            }
        }

        DB::transaction(function () use ($params, $user) {
            /* UserDetail::uploadProfilePicture(['user_id' => $params['id'], 'image' => $params['profile_pic'] ?? null]);
            if (isset($params['ic_no']) && isset($params['ic_front']) && isset($params['ic_back']) && isset($params['ic_type'])) {
                Kyc::updateOrCreate([
                    'user_id' => $params['id'],
                ],[
                    'status' => Kyc::$kycStatus['successful'],
                    'type' => Kyc::$kycType[$params['ic_type']],
                    'image_1' => $params['ic_front'],
                    'image_2' => ($params['ic_type'] == 'ic') ? ($params['ic_back'] ?? null) : null,
                ]);
            } else {
                Kyc::where('user_id', $params['id'])->update(['status' => Kyc::$kycStatus['declined']]);
            } */
            $user->update($params);

            if ($params['suspended'] == 1) {
                $jwt = DB::table('jwt_token')->where('uid', $user->id)->first();
                Auth::guard('users')->setToken($jwt->token)->invalidate(true);
            }
        });

        return true;
    }

    public static function firstLoginSetup(array $params = [])
    {
        DB::transaction(function () use ($params) {
            $res = self::where('id', $params['user_id'])
                ->update([
                    'password' => Hash::make($params['password']),
                    'phone_verified_at' => now(),
                ]);

            if (! $res) {
                abort(400, Lang::get('lang.user-not-exist'));
            }
        });

        return true;
    }

    public static function changePassword($data = [])
    {
        request()->merge(['act-log' => 'change-password']);
        $res = self::find($data['user_id'])
            ->update([
                'password' => Hash::Make($data['password']),
            ]);

        if (! $res) {
            abort(400, Lang::get('lang.user-not-exist'));
        }
        UserDetail::where(['user_id' => $data['user_id'], 'name' => 'reset_password'])->whereNull('deleted_at')->delete();

        return true;
    }

    public static function resetPassword($data = [])
    {
        $user = self::where(['username' => $data['username']])->first();
        // $alreadyReset = UserDetail::where(['name' => $data['type'], 'user_id' => $user->id])->whereNull('deleted_at')->first();
        // if ($alreadyReset) {
        //     abort(400, Lang::get('lang.reset-request-exists'));
        // }
        switch ($data['type']) {
            case 'reset_password':
                break;

            case 'reset_transaction_password':
                $current = UserDetail::where(['user_id' => $user->id, 'name' => 'transaction_password'])->whereNull('deleted_at')->first();
                if (! $current) {
                    abort(400, Lang::get('lang.transfer-setup-pin-remind'));
                }
                break;
        }

        UserDetail::insert(['name' => $data['type'], 'value' => 1, 'user_id' => $user->id, 'reference' => now(), 'type' => MODULE]);
        $hashPwd = Hash::Make($data['password']);

        try {
            switch ($data['type']) {
                case 'reset_password':
                    request()->merge(['act-log' => 'reset-password']);
                    self::find($user->id)->update(['password' => $hashPwd]);
                    break;

                case 'reset_transaction_password':
                    request()->merge(['act-log' => 'reset-transaction-password']);
                    $updateTrxPassword = UserDetail::where(['user_id' => $user->id, 'name' => 'transaction_password'])->first() ?? null;
                    $updateTrxPassword->update(['value' => $hashPwd]);
                    break;
            }
        } catch (Exception $e) {
            return false;
        }

        return true;
    }

    public static function loginToMember($data = [])
    {
        $validMinutes = '5 minutes';
        $returnData['userId'] = self::find($data['user_id'])->id;

        $cacheName = md5(time().str_pad((rand(1, 999999)), 6, 0, STR_PAD_LEFT));

        $cacheExpiry = now()->add($validMinutes);

        Traits\CacheTrait::setCache($cacheName, (object) $returnData, $cacheExpiry);

        return [
            'data' => [
                'login_token' => $cacheName,
                'login_url' => env('APP_URL_USER'),
            ],
        ];
    }

    public static function walletBalanceList(array $params = [])
    {
        $credit = Credit::where(['id' => $params['credit_id']])->first();
        $creditType = $credit->name ?? null;
        $creditCode = $credit->code ?? null;

        $memberId = $params['member_id'] ?? null;
        $username = $params['username'] ?? null;
        $phoneNo = $params['phone_no'] ?? null;
        $storeId = $params['store_id'] ?? null;
        $exStoreId = $params['extra_store_id'] ?? null;
        $updateFromDate = $params['updated_from_date'] ?? null;
        $updateToDate = $params['updated_to_date'] ?? null;
        $creditID = $params['credit_id'] ?? null;
        $seeAll = $params['see_all'] ?? null;
        $allIds = $pageIds = [];
        $balanceSummary = $grandTotal = 0;

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'asc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        if (isset($params['export']) && ($params['export'] == 1)) {
            $params['model'] = get_class() ?? null;
            $params['function'] = __FUNCTION__;
            ExportReport::exportReport($params);
        }

        $items = self::query()
            ->whereIn('user_type', [self::$userType['user-account']])
            ->when($username, function ($query) use ($username) {
                $query->whereRelation('userDetail', 'username', 'LIKE', "%$username%");
            })
            ->when(isset($phoneNo), function ($query) use ($phoneNo) {
                $phoneNo = preg_replace('/[^0-9]/', '', $phoneNo);
                $query->where('phone_no', 'like', "%$phoneNo%");
            })
            ->when($storeId, function ($query) use ($storeId) {
                $query->whereRelation('store', 'store_id', $storeId);
            })
            ->when(isset($exStoreId), function ($q) use ($exStoreId) {
                return $q->whereHas('store', function ($q) use ($exStoreId) {
                    $q->whereIn('id', $exStoreId);
                });
            })
            ->when($memberId, function ($query) use ($memberId) {
                $query->where('member_id', '=', $memberId);
            });
        $totalUsed = clone $items;
        // 2024-03-13  >> display all user, no matter user's have transaction or not
        // $items
        // ->with(['balanceLastUpdate' => function($q) use ($creditID, $updateFromDate, $updateToDate){
        //     $q->where('credit_id', $creditID);
        //     $q->when((isset($updateFromDate) && isset($updateToDate)), function ($q) use ($updateFromDate, $updateToDate) {
        //         $q->whereBetween('created_at', [date("Y-m-d 00:00:00", strtotime($updateFromDate)), date("Y-m-d 23:59:59", strtotime($updateToDate))]);
        //     });
        // }])
        // ->whereHas('balanceLastUpdate', function ($q) use ($creditID, $updateFromDate, $updateToDate) {
        //     $q->where('credit_id', $creditID);
        //     $q->when((isset($updateFromDate) && isset($updateToDate)), function ($q) use ($updateFromDate, $updateToDate) {
        //         $q->whereBetween('created_at', [date("Y-m-d 00:00:00", strtotime($updateFromDate)), date("Y-m-d 23:59:59", strtotime($updateToDate))]);
        //     });
        // });

        $items = $items->orderBy($order_by, $order_sort)
            ->when((! isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $totalUsed->get()->map(function ($q) use (&$allIds) {
            $allIds[$q->id] = $q->id;

            return $q;
        });
        if ($seeAll != 1) {
            $items->getCollection()->transform(function ($q) use (&$pageIds) {
                $pageIds[$q->id] = $q->id;

                return $q;
            });
        } else {
            $items->get()->map(function ($q) use (&$pageIds) {
                $pageIds[$q->id] = $q->id;

                return $q;
            });
        }

        $balance = Credit::getMultiBalance($allIds, $creditType);
        foreach ($balance as $userId => $b) {
            if (in_array($userId, $pageIds)) {
                $grandTotal += $b;
            }
            $balanceSummary += $b;
        }
        $mapFunc = function ($q) use ($balance) {
            $res = [
                'user_id' => $q->id,
                'member_id' => $q->member_id,
                'username' => $q->username,
                'phone_no' => $q->phone_no,
                'name' => $q->name,
                'store_name' => $q->store->name ?? null,
                'balance' => Traits\DecimalTrait::setDecimal($balance[$q->id] ?? 0),
                'last_update' => isset($q->balanceLastUpdate) ? Traits\DateTrait::dateFormat($q->balanceLastUpdate->created_at) : null,
            ];

            return (object) $res;
        };

        if (($seeAll == 1)) {
            return ['credit_code' => $creditCode, 'list' => $items->get()->map($mapFunc)->toArray()];
        } else {
            $items->getCollection()->transform($mapFunc);

            return (new ItemsCollection($items))->toArray() + ['grand_total' => Traits\DecimalTrait::setDecimal($grandTotal), 'summary' => ['balance' => Traits\DecimalTrait::setDecimal($balanceSummary)], 'credit_code' => $creditCode];
        }
    }

    public static function walletBalanceReport(array $params = [])
    {
        $memberId = $params['member_id'] ?? null;
        $username = $params['username'] ?? null;
        $date = isset($params['date']) ? $params['date'].' 23:59:59' : null;
        // $creditID = $params['credit_id'] ?? null;
        $seeAll = $params['see_all'] ?? null;
        $allIds = $pageIds = [];
        $balanceSummary = $grandTotal = 0;

        $sortableColumns = [];

        // order
        $order_by = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $order_by = in_array($order_by, $sortableColumns) ? $order_by : 'created_at';

        // sort
        $order_sort = isset($params['order_sort']) ? $params['order_sort'] : null;
        $order_sort = in_array($order_sort, ['asc', 'desc']) ? $order_sort : 'desc';

        // limit
        $limit = $params['limit'] ?? config('app.pagination_rows');

        $creditArray = Credit::select('type')->groupBy('type')->get();

        if (isset($params['export']) && ($params['export'] == 1)) {
            $params['model'] = get_class() ?? null;
            $params['function'] = __FUNCTION__;
            ExportReport::exportReport($params);
        }

        $items = self::query()
            ->whereIn('user_type', [self::$userType['user-account']])
            ->when($username, function ($query) use ($username) {
                // $username = preg_replace('/[^0-9]/', '', $username);
                $query->where('username', 'like', "%$username%");
            })
            ->when($memberId, function ($query) use ($memberId) {
                $query->where('member_id', '=', $memberId);
            });

        $totalUsed = clone $items;

        $items = $items->orderBy($order_by, $order_sort)
            ->when((! isset($seeAll) || $seeAll == 0), function ($q) use ($limit) {
                return $q->paginate($limit);
            });

        $totalUsed->get()->map(function ($q) use (&$allIds) {
            $allIds[$q->id] = $q->id;

            return $q;
        });
        if ($seeAll != 1) {
            $items->getCollection()->transform(function ($q) use (&$pageIds) {
                $pageIds[$q->id] = $q->id;

                return $q;
            });
        } else {
            $items->get()->map(function ($q) use (&$pageIds) {
                $pageIds[$q->id] = $q->id;

                return $q;
            });
        }

        $balance = null;
        $grandTotal = null;
        $balanceSummary = null;
        foreach ($creditArray as $credit) {
            $grandTotal[$credit->type] ??= 0;
            $balanceSummary[$credit->type] ??= 0;
        }
        foreach ($creditArray as $credit) {
            $balance[$credit->type] = Credit::getMultiBalance($allIds, $credit->type, $date);
            foreach ($balance[$credit->type] as $userId => $b) {
                if (in_array($userId, $pageIds)) {
                    $grandTotal[$credit->type] ??= 0;
                    $grandTotal[$credit->type] += $b;
                }
                $balanceSummary[$credit->type] ??= 0;
                $balanceSummary[$credit->type] += $b;
            }
        }
        foreach ($creditArray as $credit) {
            $grandTotal[$credit->type] = Traits\DecimalTrait::setDecimal($grandTotal[$credit->type]);
            $balanceSummary[$credit->type] = Traits\DecimalTrait::setDecimal($balanceSummary[$credit->type]);
        }

        $mapFunc = function ($q) use ($balance, $creditArray) {
            $res = [
                'user_id' => $q->id,
                'member_id' => $q->member_id,
                'username' => $q->username,
                'name' => $q->name,
            ];

            foreach ($creditArray as $credit) {
                $res['balance'][$credit->type] ??= 0;
                $res['balance'][$credit->type] = Traits\DecimalTrait::setDecimal($balance[$credit->type][$q->id] ?? 0);
            }

            return (object) $res;
        };

        if (($seeAll == 1)) {
            return ['list' => $items->get()->map($mapFunc)->toArray()];
        } else {
            $items->getCollection()->transform($mapFunc);

            return (new ItemsCollection($items))->toArray() + [
                'grand_total' => $grandTotal,
                'summary' => [
                    'balance' => $balanceSummary,
                ],
            ];
        }
    }

    public static function deleteAccount(array $params = [])
    {
        DB::transaction(function () use ($params) {
            $res = self::where('id', $params['user_id'])
                ->update([
                    'activated' => 0,
                    'deleted_at' => now(),
                ]);

            if (! $res) {
                abort(400, Lang::get('lang.user-not-exist'));
            }

            $newOTP = GenerateNumberTrait::GenerateOTPCode();
            SystemSetting::where('name', 'fixOTP')->update(['value' => $newOTP]);

            auth()->logout();
            $es = auth('users')->logout();
        });

        return true;
    }

    public static function getName(array $params = [])
    {
        $username = $params['username'] ?? null;
        $user = null;
        if (isset($username)) {
            $user = self::where('username', $username)->first();
        }

        $data['name'] = $user->name ?? null;

        return $data;
    }

    public static function getDeeplink(array $params = [])
    {
        $userId = $params['user_id'] ?? null;
        $user = null;
        if (isset($userId)) {
            $user = self::where('id', $userId)->first();
        }

        $data['deeplink'] = $user->deeplink ?? null;

        return $data;
    }

    public static function addThirdPartyUser(array $params = [], $party = null)
    {
        $isAuth = Auth::check() ?? false;

        // Checking for sponsor id if not exists
        if (! isset($params['referral_code'])) {
            // Get Default Referral
            $defaultReferral = SystemSetting::where('name', 'defaultReferralUsername')->pluck('value', 'name')->toArray() ?? null;

            if (! isset($defaultReferral['defaultReferralUsername']) || empty($defaultReferral['defaultReferralUsername'])) {
                abort(400, Lang::get('lang.invalid-referral-username-error'));
            }

            $sponsorId = self::where('username', $defaultReferral['defaultReferralUsername'])->first()->id ?? null;
        } else {
            $sponsorId = self::where('username', $params['referral_code'])->first()->id ?? null;
        }
        if (! $sponsorId) {
            abort(400, json_encode(Lang::get('lang.invalid-referral-username-error')));
        }

        $memberID = Traits\GenerateNumberTrait::GenerateMemberID(self::query());

        // Get Fix Phone No
        // $v2AuthorizedUser = json_decode((SystemSetting::where('name','v2AuthorizedUser')->first()->value ?? null),true) ?? [];

        $insertUser = [
            'member_id' => $memberID,
            'username' => str_replace('-', '', $params['phone_no']),
            'name' => $params['name'],
            'phone_no' => $params['phone_no'],
            'country_id' => $params['country_id'] ?? null,
            'sponsor_id' => $sponsorId,
            'lang' => $params['preferred_language'],
            'activated' => 1,
            'party' => $party,
            'deeplink' => $params['deeplink'] ?? null,
            'referral_code' => GenerateNumberTrait::generateReferralCode(self::query(), 'referral_code'),
        ];

        return DB::transaction(function () use ($insertUser, $params, $sponsorId, $party) {
            $checkUser = User::where('phone_no', $params['phone_no'])->first();
            // if(isset($checkUser) && in_array($checkUser->username,$v2AuthorizedUser)){
            //     if(isset($checkUser)){
            //         $checkUser->update(['name'=> $insertUser['name'],'activated'=>1,'disabled'=>0,'suspended'=>0,'fail_login'=>0,'deleted_at'=>null]);
            //     }
            //     if(MODULE == 'app'){
            //         $loginData = Services\AuthService::login(["phone_no"=>$checkUser->phone_no], 'users');
            //         return $loginData;
            //     }
            //     return true;
            // }

            // Insert User
            $user = self::create($insertUser);
            if (! $user) {
                abort(400, Lang::get('lang.user-create-error'));
            }

            $insertSponsorTree = Traits\TreeTrait::insertSponsorTree($user->id, $sponsorId);
            if (! $insertSponsorTree) {
                abort(400, Lang::get('lang.user-create-error'));
            }

            // Insert Sales
            $sales = [
                'user_id' => $user->id,
                'sponsor_id' => $sponsorId,
                'module_type' => 'register',
            ];
            $updateSales = UserSales::updateSales($sales, 'package');
            if (! $updateSales) {
                abort(400, Lang::get('lang.user-create-error'));
            }

            // Send SMS if above all success
            /*
                $smsParams = [
                    'phone' => $params['phone_no'] ?? null,
                    'lang' => 'en',
                    'type' => 'register-success',
                    'user_id' => $user->id ?? null,
                ];
                $smsParams = json_encode($smsParams);
                $job = new \App\Jobs\SendSMS($smsParams);
                dispatch($job);
            */

            $referralOcMemberId = UserProduct::where('user_id', $sponsorId)->first()->member_id ?? null;
            $phoneAry = explode('-', $params['phone_no']);
            $ocAccountNo = str_replace('6', '', $phoneAry[0]).$phoneAry[1];
            if ($ocAccountNo[0] != 0) {
                $ocAccountNo = '0'.$ocAccountNo;
            }

            // if (env('APP_ENV') != 'production'){
            //     $ocAccountNo = $ocAccountNo.$user->member_id;
            // }
            UserProduct::subscribe([
                'user_id' => $user->id,
                // "account" => $ocAccountNo,
                'account' => $user->uuid,
                'referral' => $referralOcMemberId,
                // "password" => $user->id . $insertUser['member_id'],
                'password' => $user->uuid,
                'dial_code' => $params['dial_code'],
            ], $party);

            self::getFreeCredit($user->id);

            return true;
        });
    }

    public static function getFreeCredit($userID, $amount = 0)
    {
        // $freeCreditSS = SystemSetting::where('name', 'freeCredit')->first()->value ?? null;
        // if (!isset($freeCreditSS)) {
        //     return;
        // }

        $now = date('Y-m-d H:i:s');
        $internalID = User::where('username', 'freeCredit')->where('user_type', User::$userType['internal-account'])->first()->id;
        $belongID = GenerateNumberTrait::GenerateNewId() ?? 0;

        // $freeJson = json_decode($freeCreditSS, true);
        // foreach ($freeJson as $creditName => $amount) {

        // }

        $credit = Credit::where('name', 'myr-credit')->first();
        CreditTransaction::insertTransaction($internalID, $userID, $userID, $credit->type, $amount, 'free-credit', $belongID, $belongID, null, $now);
    }

    public static function getAllAccount($userId)
    {
        $user = self::with([
            'allAccount' => function ($q) {
                $q->select('id', 'store_id', 'username', 'phone_no');
            },
            'allAccount.store' => function ($q) {
                $q->select('id', 'store_id', 'name');
            },
            'store' => function ($q) {
                $q->select('id', 'store_id', 'name');
            },
        ])->find($userId);

        foreach ($user->allAccount as &$eachAccount) {
            $eachAccount['selected'] = false;
            if ($user->id == $eachAccount->id) {
                $eachAccount['selected'] = true;
            }
        }

        $data = [
            'data' => [
                'all_account' => $user->allAccount ?? null,
            ],
        ];

        return $data;
    }

    private static function termsDetail()
    {
        return '<div style="font-family:system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif; font-size:14px; line-height:1.5; max-width:100%; word-wrap:break-word; overflow-wrap:break-word; padding:10px;">
    <!-- English Terms -->
    <div style="margin-bottom:20px;">
        <div style="font-weight:bold; font-size:16px; margin-bottom:10px;">Promotion subject to availability</div>
        <ul style="list-style-type:disc; padding-left:20px; margin:0;">
            <li style="margin-bottom:8px; text-align:left;">Deposit requires minimum of 30MYR.</li>
            <li style="margin-bottom:8px; text-align:left;">Withdrawal requires with a minimum of 50MYR.</li>
            <li style="margin-bottom:8px; text-align:left;">Each deposit requires x1 turnover to process a withdrawal.</li>
            <li style="margin-bottom:8px; text-align:left;">This promotion is open to all UWIN members, aged 18 years old and above with a valid Malaysia phone number and a valid Malaysian banking account.</li>
            <li style="margin-bottom:8px; text-align:left;">This promotion is not permissible for multiple or fake accounts.</li>
            <li style="margin-bottom:8px; text-align:left;">If there is any collusion or cheating, usage of multiple accounts, all bonus and winning amounts will be confiscated.</li>
            <li style="margin-bottom:8px; text-align:left;">Members participate in the activity must accept and comply with the above rules and terms as well as all relevant rules and terms of the website implement by UWIN.</li>
            <li style="margin-bottom:8px; text-align:left;">The maximum daily withdrawal from Uwin to card, and Uwin to bank, is both RM30,000.00, with a total of RM60,000.00.</li>
            <li style="margin-bottom:8px; text-align:left;">Uwin reserve the right to amend these terms and conditions at its discretion.</li>
        </ul>
    </div>

    <!-- Divider -->
    <hr style="border:0; height:1px; margin:20px 0;">

    <!-- Chinese Terms -->
    <div style="margin-bottom:20px;">
        <div style="font-weight:bold; font-size:16px; margin-bottom:10px;">促销活动视供应情况而定</div>
        <ul style="list-style-type:disc; padding-left:20px; margin:0;">
            <li style="margin-bottom:8px; text-align:left;">存款需要最少30MYR。</li>
            <li style="margin-bottom:8px; text-align:left;">提现最少50MYR。</li>
            <li style="margin-bottom:8px; text-align:left;">每笔存款需要x1流水才能处理提款。</li>
            <li style="margin-bottom:8px; text-align:left;">此促销活动开放给所有年满18岁或以上并拥有有效马来西亚手机号码和有效马来西亚银行账户的UWIN会员。</li>
            <li style="margin-bottom:8px; text-align:left;">此促销活动不允许使用多个或虚假帐户。</li>
            <li style="margin-bottom:8px; text-align:left;">如果存在串通或使虚假作弊，多个账户的情况，所有奖金和中奖金额将被没收。</li>
            <li style="margin-bottom:8px; text-align:left;">会员参加活动必须接受并遵守上述规则和条款以及UWIN网站实施的所有相关规则和条款。</li>
            <li style="margin-bottom:8px; text-align:left;">从 Uwin 提款到卡和从 Uwin 提款到银行的每日最高限额均为 RM30,000.00，总计 RM60,000.00。</li>
            <li style="margin-bottom:8px; text-align:left;">Uwin保留自行修改这些条款和条件的权利。</li>
        </ul>
    </div>

    <!-- Divider -->
    <hr style="border:0; height:1px; margin:20px 0;">

    <!-- Malay Terms -->
    <div style="margin-bottom:20px;">
        <div style="font-weight:bold; font-size:16px; margin-bottom:10px;">Promosi tertakluk kepada ketersediaan</div>
        <ul style="list-style-type:disc; padding-left:20px; margin:0;">
            <li style="margin-bottom:8px; text-align:left;">Deposit memerlukan dengan minimum 10MYR.</li>
            <li style="margin-bottom:8px; text-align:left;">Pengeluaran memerlukan dengan minimum 20MYR.</li>
            <li style="margin-bottom:8px; text-align:left;">Setiap deposit memerlukan x1 pusing ganti untuk memproses pengeluaran.</li>
            <li style="margin-bottom:8px; text-align:left;">Promosi ini terbuka kepada semua ahli UWIN, berumur 18 tahun dan ke atas dengan nombor telefon bimbit Malaysia yang sah dan akaun perbankan Malaysia yang sah.</li>
            <li style="margin-bottom:8px; text-align:left;">Promosi ini tidak dibenarkan untuk berbilang atau akaun palsu.</li>
            <li style="margin-bottom:8px; text-align:left;">Jika terdapat sebarang pakatan sulit atau penipuan, penggunaan berbilang akaun, semua bonus dan jumlah kemenangan akan dirampas.</li>
            <li style="margin-bottom:8px; text-align:left;">Ahli yang menyertai aktiviti mesti menerima dan mematuhi peraturan dan terma di atas serta semua peraturan dan terma berkaitan laman web yang dilaksanakan oleh UWIN.</li>
            <li style="margin-bottom:8px; text-align:left;">Pengeluaran harian maksimum dari Uwin ke kad, dan Uwin ke bank, kedua-duanya adalah RM30,000.00, dengan jumlah RM60,000.00.</li>
            <li style="margin-bottom:8px; text-align:left;">Uwin berhak untuk meminda terma dan syarat ini mengikut budi bicaranya.</li>
        </ul>
    </div>
</div>';
    }

    public static function isAgent($userId)
    {
        $count = self::where('id', $userId)
            ->where('is_agent', true)
            ->count();

        return $count ? true : false;
    }
}
