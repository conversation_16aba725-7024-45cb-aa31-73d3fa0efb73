<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ApiKey extends Model
{
    use SoftDeletes;

    protected $hidden = [
        'api_key_iv',
        'user_id',
    ];

    public $timestamps = false; // Required because this table dont have updated_at column

    protected $fillable = [
        'api_key',
        'api_key_iv',
        'user_id',
        'created_at',
        'deleted_at',
    ];

    /**
     * Get the user that owns the api key.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
