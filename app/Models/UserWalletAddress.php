<?php

namespace App\Models;

use App\Traits\TronUMXTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class UserWalletAddress extends Model
{
    const UPDATED_AT = null;

    use TronUMXTrait;

    protected $table = 'user_wallet_address';

    protected $hidden = [
    ];

    protected $fillable = [
        'user_id',
        'credit_id',
        'coin_type',
        'address',
        'status',
        'created_at',
        'deleted_at',
    ];

    public static $status = [
        'inactive' => 0,
        'active' => 1,
    ];

    public static function generateWalletAddress($userId,$coinType,$creditId = null){
        $memberId = User::find($userId)->member_id;
        $walletAddress = TronUMXTrait::generateWalletAddress($memberId);

        if(isset($walletAddress)){
            self::create([
                "user_id" => $userId,
                "credit_id" => $creditId,
                "coin_type" => $coinType,
                "address" => $walletAddress,
                "status" => self::$status['active'],
            ]);
        }
        return $walletAddress ?? NULL;
    }

    public static function getUserWalletAddress($params = []){
        if(!isset($params['user_id'])) return false;
        $userId = $params['user_id'];
        $coinType = $params['coin_type'] ?? "USDT";
        $creditId = $params['credit_id'] ?? 0;

        $walletAddress = null;
        $res = self::where(['user_id' => $userId, 'status' => self::$status['active'], 'coin_type' => $coinType])->first();
        if(isset($res->address)){
            $walletAddress = $res->address ?? null;
        } else {
            $walletAddress = self::generateWalletAddress($userId, $coinType, $creditId);
        }
        return $walletAddress ?? NULL;
    }
}
