<?php

namespace App\Notifications;

use App\Models;
use DB;
use NotificationChannels\Telegram\TelegramChannel;
use NotificationChannels\Telegram\TelegramMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Lang;

class Telegram extends Notification
{
    // protected static $chatID = '-1001819883415'; // Live
    // protected static $chatID = '-1001820813725'; // Test
    protected static $lang = 'en';
    protected static $decimal = 0;
    protected $date = null;

    public function __construct($date)
    {
        $this->date = $date;
    }

    public function via($notifiable)
    {
        return [TelegramChannel::class];
    }

    public function toTelegram()
    {
        $eventDescription = '';
        
        $message = TelegramMessage::create()
            ->to(self::$chatID)
            ->content($eventDescription);

        return $message;
    }

    public static function setDecimal($amount){
        if (!$amount) {
            $amount = 0;
        }

        $decimal = self::$decimal;

        $amount = explode('.',$amount);
        $amount = $amount[0];

        return number_format($amount, $decimal, '.', ',');
    }
}
