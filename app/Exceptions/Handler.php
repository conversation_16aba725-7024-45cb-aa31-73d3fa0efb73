<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Sentry\Laravel\Integration;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            Integration::captureUnhandledException($e);
        });
    }

    /**
     * @param  Request  $request
     * @return JsonResponse|Response|\Symfony\Component\HttpFoundation\Response
     *
     * @throws Throwable
     */
    public function render($request, Throwable $e)
    {
        $msg = $e->getMessage();
        if (method_exists($e, 'getStatusCode')) {
            $code = $e->getStatusCode();
        } else {
            $code = $e->getCode();
        }
        if (! $code) {
            $code = 500;
        }
        if (gettype($code) !== 'integer') {
            // handle unexpected code which is not integer, especially come from mysql error
            $code = 500;
        }

        if ($code == 500) {
            if ($msg == 'Route [login] not defined.') {
                $msg = __('lang.unauthenticated');
                // $msg = [
                //     'msg' => array(__('lang.unauthenticated')),
                // ];
                // $msg = json_encode($msg);
                $code = 400;
                $extraParams = ['expired' => true];
            } elseif ($msg == 'Unauthenticated.') {
                $code = 400;
            }
        }
        if ($code == 404) {
            $checkAgent = array_key_exists('user-agent', $request->header()) ? explode('/', $request->header()['user-agent'][0]) : [];
            if (array_intersect($checkAgent, ['Zabbix'])) {
                return response()->json([
                    'message' => 'Granted',
                ], 200);
            }

            return response()->json([
                'message' => '#@CAN@#$STUPID&*8999',
            ], $code);
        }

        $http_status_code = ['100', '101', '200', '201', '202', '203', '204', '205', '206', '300', '301', '302', '303', '304', '305', '306', '307', '400', '401', '402', '403', '404', '405', '406', '407', '408', '409', '410', '411', '412', '413', '414', '415', '416', '417', '500', '501', '502', '503', '504', '505'];
        if (! in_array($code, $http_status_code)) {
            $code = 417;
        }

        // expected error using abort command
        if ($this->isJson($msg)) {
            // msg is json

            $msg = json_decode($msg, true);
            if ($code == 400) {
                $tempErr = $msg;
                unset($msg);
                if (isset($tempErr['message'])) {
                    $msg['error'] = $tempErr['message'];
                    $msg['data'] = $tempErr['data'];
                } else {
                    $msg['error'] = $tempErr;
                }
            }
            $msg['status'] = true;

            return response()->json($msg, $code);
        } else {
            if ($code == 500) { // Unplanned error
                return response()->json([
                    'status' => false,
                    'message' => $msg,
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                ], $code);
            } elseif ($code == 200) {
                return response()->json([
                    'status' => true,
                    'message' => $msg == '' ? __('lang.granted') : $msg,
                ], $code);
            }
            // else if ($code == 400) {
            //     return response()->json(array_merge([
            //         'status' => true,
            //         'error' => $msg == '' ? __('lang.failed') : $msg,
            //     ], ($extraParams ?? [])), $code);
            // }
            else {
                // planned error
                return response()->json(array_merge([
                    'status' => true,
                    'message' => $msg == '' ? __('lang.failed') : $msg,
                ], ($extraParams ?? [])), $code);
            }
        }

        // unexpected response
        return response()->json([
            'message' => $msg,
            'file' => $e->getFile(),
            'line' => $e->getLine(),
        ], $code);
    }

    protected function isJson($string)
    {
        json_decode($string);

        return json_last_error() == JSON_ERROR_NONE;
    }
}
