<?php

namespace App\Exports;

use App\Models\UserTicketReward;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Excel;

class UserTicketRewardExport implements FromQuery, WithHeadings
{
    use Exportable;

    /**
     * Optional Writer Type
     */
    private $writerType = Excel::CSV;

    private $headers = [
        'Content-Type' => 'text/csv',
    ];

    public $store_id;

    public function __construct(int $store_id)
    {
        $this->store_id = $store_id;
    }

    public function query()
    {
        /**
         * name	
         * user_id	
         * total_token
         */
        return UserTicketReward::query()
            ->where('store_id', $this->store_id)
            ->select('name', 'user_id', 'total_token');
    }

    public function headings(): array
    {
        return [
            'name',
            'user_id',
            'total_token',
        ];
    }
}
