<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MtForceRefresh implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $connection = 'database';

    public $queue = 'default';

    public $uuid;

    public $status;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($uuid, $status = true)
    {
        $this->uuid = $uuid;
        $this->status = $status;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new Channel('mt-machine.' . $this->uuid);
    }

    public function broadcastAs()
    {
        return 'force-refresh';
    }

    public function broadcastWith(): array
    {
        return [
            'uuid' => $this->uuid,
            'status' => $this->status
        ];
    }
}
