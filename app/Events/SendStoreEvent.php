<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SendStoreEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $event;

    public $identity;

    public $data = [];

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($event, $identity, $data)
    {
        $this->event = $event;
        $this->identity = $identity;
        $this->data = $data;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new Channel('listen-store.' . $this->identity);
    }

    public function broadcastAs()
    {
        return 'send-store';
    }

    public function broadcastWith(): array
    {
        return [
            'event' => $this->event,
            'identity' => $this->identity,
            'data' => $this->data
        ];
    }
}
