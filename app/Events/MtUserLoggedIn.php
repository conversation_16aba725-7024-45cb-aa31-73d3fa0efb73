<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class MtUserLoggedIn implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $connection = 'database';

    public $queue = 'default';

    public $uuid;

    public $user_id;

    public $url;

    public $token;

    public $game;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($uuid, $user_id, $url, $token, $game)
    {
        $this->uuid =  $uuid;
        $this->user_id =  $user_id;
        $this->url =  $url;
        $this->token = $token;
        $this->game = $game;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        // return new PrivateChannel('mt-machine.' . $this->uuid);

        return new Channel('mt-machine.' . $this->uuid);
    }

    public function broadcastAs()
    {
        return 'user-logged-in';
    }

    public function broadcastWith(): array
    {
        return [
            'url' => $this->url,
            'user_id' => $this->user_id,
            'token' => $this->token,
            'game' => $this->game
        ];
    }
}
