<?php

namespace App\Imports;

use App\Models\User;
use App\Models\UserTicketReward;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Row;

class UserTicketRewardImport implements OnEachRow, WithStartRow
{
    use Importable;

    public function onRow(Row $row)
    {
        $rowIndex = $row->getIndex();
        $row      = $row->toArray();

        $name = trim($row[1]);
        $ticket = trim($row[2]);

        $userTicketReward = UserTicketReward::where('name', $name)->first();
        if (isset($userTicketReward)) {
            $userTicketReward->update([
                'total_token' => $userTicketReward->total_token + (int)$ticket,
                'total_deposit' => $userTicketReward->total_deposit + (int)$ticket * 100
            ]);
        } else {

            $user = User::where('username', $name)->first();
            if (isset($user)) {
                UserTicketReward::create([
                    'user_id' => $user->id,
                    'store_id' => $user->store_id,
                    'name' => $user->username,
                    'total_token' => (int)$ticket,
                    'total_deposit' => (int)$ticket * 100,
                    'is_dummy' => 0,
                    'status' => 1,
                ]);
            }
        }
    }

    public function startRow(): int
    {
        return 2;
    }
}
