<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithStartRow;
use App\Traits\CacheTrait;

class ImportLang implements ToCollection, WithBatchInserts, WithChunkReading, WithHeadingRow, WithStartRow
{
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) 
        {
            $updated = [];

            $slug = $row['code'] ?? null;
            foreach (config('language') as $lang => $langId) {
                isset($row[$lang]) ? $updated[$lang] = $row[$lang] : null;
            }

            if(!empty($slug)){
                $updated['deleted_at'] = null;

                DB::table('lang')->updateOrInsert(
                    [
                        'slug' => $slug,
                    ],
                    $updated,
                );
            }
        }
        CacheTrait::clearLangCache('lang-');
    }

    public function headingRow(): int
    {
        return 1;
    }

    public function batchSize(): int
    {
        return 100;
    }

    public function chunkSize(): int
    {
        return 1000;
    }

    public function startRow(): int
    {
        return 2;
    }
}
