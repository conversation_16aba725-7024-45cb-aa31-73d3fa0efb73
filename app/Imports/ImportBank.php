<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use App\Models;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Illuminate\Contracts\Queue\ShouldQueue;

class ImportBank implements ShouldQueue, ToCollection, WithBatchInserts, WithChunkReading, WithHeadingRow, WithStartRow
{

    public $timeout = 0;

    public function collection(Collection $rows)
    {   
        $countryList = Models\Country::get()->pluck('id','name');
        foreach ($rows as $row) 
        {
            if ($row['name'] == '') continue;
            switch($row['country']) {
                case 'Russian Federation':
                case 'Hong Kong':
                case 'Sri Lanka':
                    $row['country'] = str_replace(" ", "-", $row['country']);
                    break;
                case 'Viet Nam':
                    $row['country'] = str_replace(" ", "", $row['country']);
                    break;
                case 'Bangladesh_1':
                    $row['country'] = "Bangladesh";
                    break;
            }
            $slug = strtolower(str_replace(" ", "-", $row['name']));
            Models\Bank::create([
                'country_id' => $countryList[strtolower($row['country'])],
                'name' => $row['name'],
                "translation_code" => $slug,
                "status" => Models\Bank::$status['active'],
                "transfer_status" => Models\Bank::$status['active'],
                "priority" => DB::raw("id"),
            ]);

            Models\LangTable::updateOrCreate([
                "slug" => $slug,
                "type" => 'bank',
            ], [
                "en" => $row['name'],
                "vn" => $row['name'],
                "th" => $row['name'],
                "indo" => $row['name'],
                "cn" => $row['name'],
                "my" => $row['name'],
                "ben" => $row['name'],
            ]);
        }
    }

    public function headingRow(): int
    {
        return 1;
    }

    public function batchSize(): int
    {
        return 100;
    }

    public function chunkSize(): int
    {
        return 1000;
    }

    public function startRow(): int
    {
        return 2;
    }
}
