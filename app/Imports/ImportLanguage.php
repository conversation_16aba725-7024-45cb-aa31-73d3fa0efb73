<?php

namespace App\Imports;

use Illuminate\Contracts\Queue\ShouldQueue;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class ImportLanguage implements ShouldQueue, WithChunkReading, WithMultipleSheets
{
    public function sheets(): array
    {
        return [
            'BE Code' => new ImportLang(),
            'BE Validation' => new ImportLangValidation(),
        ];
    }

    public function chunkSize(): int
    {
        return 1000;
    }
}
