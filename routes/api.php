<?php

use App\Http\Controllers;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::middleware('whitelist')->group(function () {
    Route::get('/api-docs', [Controllers\ApiDocController::class, 'apiDocs'])->name('api-docs');
    Route::get('/api-docs-postman', [Controllers\ApiDocController::class, 'downloadPostmanJson']);
    // Route::post('/update-t', [Controllers\DeveloperController::class, 'updateDB']);
    // Route::post('/send-noti', [Controllers\DeveloperController::class, 'sendNotification']);

    Route::controller(Controllers\ThirdPartyController::class)->group(function () {});

    Route::controller(Controllers\UserController::class)->group(function () {
        Route::post('/sureceive-callback', 'sureceiveCallback');
    });

    Route::controller(Controllers\LangController::class)->group(function () {
        Route::post('/upload-language', 'uploadLanguage')->withoutMiddleware(['whitelist']);
        Route::post('/clear-language-cache', 'clearLangCache');
    });

    Route::middleware('dropdown.list')->group(function () {
        Route::controller(Controllers\ExchangeController::class)->prefix('rate')->group(function () {
            Route::post('/get-latest', 'getLatestRate');
        });

        Route::controller(Controllers\CountryController::class)->prefix('country')->group(function () {
            Route::post('/get', 'getCountry');
        });
    });

    Route::controller(Controllers\DeveloperController::class)->group(function () {
        Route::post('/upload-bank', 'uploadBank');
        Route::post('/run-script', 'runScript');
        Route::post('/get-key', 'getKey');
        Route::post('/insert-merchant-process', 'insertMerchantProcess');
        Route::post('/recall-fmt-promo', 'recallFMTpromo');
        Route::post('/recall-callback-withdrawal', 'recallCallbackWithdrawal');
        Route::post('/recall-callback-deposit', 'recallCallbackDeposit');
    });
});
