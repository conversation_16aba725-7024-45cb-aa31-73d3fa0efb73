<?php

use App\Http\Controllers;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('blacklist')->group(function () {
    Route::get('/api-docs', [Controllers\ApiDocController::class, ((defined('MODULE') && MODULE == 'app') ? 'appApiDocs' : 'userApiDocs')])->name('api-docs');
    Route::get('/api-docs-postman', [Controllers\ApiDocController::class, 'downloadUserPostmanJson']);

    Route::post('/check-time', [Controllers\DeveloperController::class, 'checkMaintenance']);
    Route::get('/demo-wwj', [Controllers\DemoController::class, 'getDemoWWJ']);
    Route::get('/admin-wwj', [Controllers\DemoController::class, 'getAdminWWJ']);
    Route::get('/admin-wwj-logout', [Controllers\DemoController::class, 'randomLogoutWWJ']);

    Route::middleware('maintenance')->group(function () {
        Route::post('/login', [Controllers\AuthController::class, ((defined('MODULE') && MODULE == 'app') ? 'memberAppLogin' : 'memberLogin')]);
        Route::post('/register', [Controllers\UserController::class, 'register']);
        Route::post('/verify-user-email', [Controllers\UserController::class, 'verifyUserEmail']);
        Route::post('/request-otp', [Controllers\UserController::class, 'requestOTP']);
        Route::post('/reset-password', [Controllers\UserController::class, 'resetPassword']);
        Route::post('/forgot-username', [Controllers\UserController::class, 'forgotUsername']);
    });

    Route::middleware('whitelistfpay')->group(function () {
        Route::post('/fpay-callback', [Controllers\CreditController::class, 'fpayCallBack']);
        Route::post('/onepay-callback', [Controllers\CreditController::class, 'onepayCallBack']);
        Route::post('/rapidpay-callback', [Controllers\CreditController::class, 'rapidpayCallback']);
        Route::post('/wepay-callback', [Controllers\CreditController::class, 'wepayCallback']);
        Route::post('/wepay-payout-callback', [Controllers\CreditController::class, 'wepayPayoutCallback']);
    });

    Route::post('/crypto-ev', [Controllers\CreditController::class, 'tronUMXCallback']);

    Route::controller(Controllers\CountryController::class)->prefix('country')->group(function () {
        Route::post('/get', 'getCountry')->middleware(('dropdown.list'));
    });

    Route::controller(Controllers\CurrencyController::class)->prefix('currency')->group(function () {
        Route::post('/myr-currency-rate', 'getMyrCurrencyRate');
        Route::post('/get', 'getCurrency');
        Route::post('/get-hourly', 'getCurrencyHourly');
        Route::post('/get-exchange-data', 'getExchangeData')->middleware('auth:users', 'auth.status', 'jwt.reset', 'dropdown.list');
    });

    // Announcement
    Route::controller(Controllers\AnnouncementController::class)->prefix('announcement')->group(function () {
        Route::post('/get-news', 'getNews');
    });

    Route::post('/home-page', [Controllers\DashboardController::class, 'getDashboard']);

    Route::controller(Controllers\GameController::class)->prefix('game')->group(function () {
        Route::post('/get-game-dashboard', 'getDashboardGameList');
    });

    Route::middleware('auth:users', 'auth.status', 'jwt.reset', 'dropdown.list', 'maintenance')->group(function () {
        // Auth
        Route::post('/change-pwd', [Controllers\AuthController::class, 'memberChangePassword']);
        Route::post('/logout', [Controllers\AuthController::class, 'memberLogout'])->withoutMiddleware(['jwt.reset', 'force.reset', 'dropdown.list']);

        // File
        Route::controller(Controllers\FileController::class)->group(function () {
            Route::post('/upload-s3', 'uploadS3');
            Route::post('/upload-s3-multi', 'uploadS3Multi');
        });

        // Dashboard
        Route::controller(Controllers\DashboardController::class)->group(function () {
            Route::post('/get-dashboard', 'dashboardSwitcher');
        });

        // Game
        Route::controller(Controllers\GameController::class)->prefix('game')->group(function () {
            Route::post('/get-game-list', 'getGameListbyCategoryId');
        });

        // User
        Route::controller(Controllers\UserController::class)->prefix('user')->group(function () {
            Route::post('/my-profile', 'myProfile');
            Route::post('/first-login-setup', 'firstLoginSetup');
            Route::post('/change-pwd', 'changePassword');
            Route::post('/change-transaction-pwd', 'changeTransactionPassword');
            Route::post('/reset-transaction-pwd', 'resetTransactionPassword');
            Route::post('/edit', 'edit');
            Route::post('/change-phone-number', 'changePhoneNumber');
            Route::post('/set-favourite', 'setFavourite');
            Route::post('/get-user-name', 'getUserName');
            Route::post('/get-linked-device', 'getLinkedDevice');

            // Third Party
            Route::post('/third-party-login', 'thirdPartyLogin');
            Route::post('/update-device-token', 'updateDeviceToken');

            // Delete User
            Route::post('/delete-account', 'deleteAccount')->withoutMiddleware(['jwt.reset']);

            // Notification
            Route::post('/notification-list', 'getNotificationList');
            Route::post('/read-notification', 'readNotification');

            // Currency
            Route::post('/set-favourite-currency', 'setFavouriteCurrency');

            Route::post('/switch-account', 'switchAccount');
            Route::post('/get-all-account', 'getAllAccount');
        });

        // Tree
        Route::controller(Controllers\TreeController::class)->prefix('tree')->group(function () {
            Route::post('/get-tree-sponsor', 'getTreeSponsor');
            Route::post('/get-tree-sponsor-vertical', 'getTreeSponsorVertical');
        });

        // KYC
        Route::controller(Controllers\KycController::class)->prefix('kyc')->group(function () {
            Route::post('/register-kyc', 'registerKyc');
            Route::post('/get-own-kyc', 'getOwnKyc');
        });

        // Credit
        Route::controller(Controllers\CreditController::class)->prefix('credit')->group(function () {
            Route::post('/wallet-list', 'getWalletList');
            Route::post('/transaction-list', 'getTransactionList');
            Route::post('/transfer-transaction-list', 'allTransferTransactionList');

            // Transfer
            Route::post('/transfer', 'transferConfirmation');
            Route::post('/transfer-list', 'getTransferList');

            // Deposit
            Route::post('/get-deposit-data', 'getDepositData');
            Route::post('/add-deposit', 'addDeposit');

            Route::post('/manual-bank-deposit', 'manualBankDeposit');
            Route::post('/help-centre-deposit', 'manualBankDeposit');
            Route::post('/get-deposit-list', 'getDepositList');
            Route::post('/get-deposit-det', 'getDepositDet');
            Route::post('/online-bank-deposit', 'onlineBankDeposit');
            Route::post('/ewallet-deposit', 'eWalletDeposit');

            // Withdrawal
            Route::post('/add-withdrawal-channel', 'addWithdrawalChannel');
            Route::post('/withdrawal-channel-list', 'getWithdrawalChannelList');
            Route::post('/update-withdrawal-channel', 'updateWithdrawalChannel');
            Route::post('/add-withdrawal', 'withdrawConfirmation');
            Route::post('/withdraw-list', 'getWithdrawalList');
            Route::post('/withdraw-det', 'getWithdrawalDet');
            Route::post('/withdraw-update', 'withdrawUpdate');

            // Exp Transaction
            Route::post('/exp-transaction-list', 'getExpTransactionList');

            // Convert
            Route::post('/convert', 'convertConfirmation');
            Route::post('/convert-list', 'getConversionList');
        });

        // Telex Transfer
        Route::controller(Controllers\TTransferController::class)->prefix('tt-transfer')->group(function () {
            Route::post('/add-telex-transfer', 'addTelexTransfer');
            Route::post('/get-telex-transfer-list', 'getTelexTransferList');
            Route::post('/get-telex-transfer-detail', 'getTelexTransferDetail');
            Route::post('/edit-telex-transfer', 'editTelexTransfer');

            // Favourite Bank
            Route::post('/get-favourite-bank-detail', 'getFavouriteBankDetail');
            Route::post('/edit-favourite-bank', 'editFavouriteBank');
        });

        // Announcement
        Route::controller(Controllers\AnnouncementController::class)->prefix('announcement')->group(function () {
            Route::post('/announcement-list', 'getAnnouncementList');
            Route::post('/announcement-detail', 'getAnnouncementDetail');
        });

        // Document
        Route::controller(Controllers\DocumentController::class)->prefix('document')->group(function () {
            Route::post('/document-list', 'getDocumentList');
        });

        Route::controller(Controllers\CardController::class)->prefix('card')->group(function () {
            Route::post('/new-card-list', 'getNewCardList');
            Route::post('/add-new-card', 'addNewCard');

            Route::post('/card-list', 'getCardList');
            Route::post('/card-detail', 'getCardDetail');

            Route::post('/ex-transfer-list', 'getExTransferList');
            Route::post('/add-ex-transfer', 'addExTransfer');
            Route::post('/get-ex-transfer-data', 'getExTransferData');
            Route::post('/transfer-pos', 'addExTransferPos');
        });

        // Product
        Route::controller(Controllers\ProductController::class)->prefix('product')->group(function () {
            Route::post('/get-user-product', 'getUserProduct');
            Route::post('/get-product-balance', 'getProductBalance');
            Route::post('/get-restore-balance', 'restoreProductBalance');
            Route::post('/get-all-balance', 'getAllProductBalance');
            Route::post('/add-ex-transfer', 'addExTransfer');
            Route::post('/ex-transfer-list', 'getExTransferList');
            Route::post('/access-game', 'getEnterGame')->middleware('throttle:1,0.03');
            Route::post('/transfer-in', 'getTransferIn');
        });

        // MT Machine
        Route::prefix('mt')
            ->name('mt.')
            ->controller(Controllers\MtController::class)->group(function () {
                Route::post('/login', 'login')->name('login');
                Route::post('/claim-promotion', 'claimPromotion');
            });

        // Reward
        Route::controller(Controllers\UserRewardController::class)->prefix('user-reward')->group(function () {
            Route::post('/get-user-reward', 'getUserReward');
            Route::post('/claim-user-reward', 'claimUserReward');
        });

        Route::controller(Controllers\RewardSpinController::class)->prefix('reward-spin')->group(function () {
            Route::post('/get-reward-spin', 'getRewardSpin');
            Route::post('/spin-reward', 'spinReward');
        });

        // Buaya Tournament
        Route::controller(Controllers\BuayaTournamentController::class)->prefix('buaya-tournament')->group(function () {
            Route::post('/get-top-turnover-and-deposit', 'getTopTurnoverAndDeposit');
        });

        // Promotion
        Route::prefix('promotion')
            ->name('promotion.')
            ->controller(Controllers\PromotionController::class)
            ->group(function () {
                Route::post('/get-promotion-list', 'getPromotionList');
                Route::post('/get-promotion-detail', 'getPromotionDetail');
                Route::post('/claim-buaya-promotion', 'claimBuayaPromotion');
            });

        // VIP Levels
        Route::controller(Controllers\VipLevelController::class)->prefix('vip-level')->group(function () {
            Route::post('/get-vip-levels', 'getVipLevels');
        });

        Route::controller(Controllers\UserRebateController::class)->prefix('vip-level')->group(function () {
            Route::post('/get-user-rebates', 'getUserRebates');
            Route::post('/request-rebate', 'requestRebate');
        });

        // Angpau
        Route::controller(Controllers\UserAngpauController::class)->prefix('angpau')->group(function () {
            Route::post('/claim-angpau', 'claimAngpau');
            Route::post('/get-leaderboard', 'getLeaderboard');
            Route::post('/get-fortune-angpao', 'getFuDaiAngPao');
            Route::post('/join-fortune-angpao', 'joinFuDaiAngPao');
            Route::post('/get-fortune-leaderboard', 'getAngpauLeaderboard');
        });

        // User Referral
        Route::controller(Controllers\UserReferralController::class)->prefix('user-referral')->group(function () {
            Route::post('/get-user-referral', 'getUserReferral');
            Route::post('/get-user-referral-bonus', 'getUserReferralBonus');
            Route::post('/claim-referral-bonus', 'claimReferralBonus');
            Route::post('/claim-referral-tier-bonus', 'claimReferralTierBonus');
        });
    });

    Route::controller(Controllers\UserTicketRewardController::class)->prefix('reward-ticket')->group(function () {
        Route::post('/get-user-ticket', 'getUserTicketReward');
        Route::post('/get-prize-list', 'getPrizeList');
    });

    Route::prefix('mt')
        ->name('mt.')
        ->controller(Controllers\MtController::class)->group(function () {
            Route::post('/logout', 'logout')->name('logout');
            Route::post('/get-machine-detail', 'getMachineDetail');
            Route::post('/get-force-status', 'getForceLogOutStatus');
            Route::post('/demo-link/{id}/{game?}', 'getDemoGameUrl');
            Route::post('/get-game-list', 'getGameList');
            Route::post('/get-game-category', 'getGameListByCategoryId');
        });

    Route::prefix('game')
        ->name('game.')
        ->controller(Controllers\GameController::class)
        ->group(function () {
            Route::post('/get-game-category', 'getGameListByCategory');
            Route::post('/get-game-category-id', 'getGameListbyCategoryId');
        });

    Route::controller(Controllers\BannerController::class)->prefix('banner')->group(function () {
        Route::post('/get-banners', 'getBannerList');
    });
});
